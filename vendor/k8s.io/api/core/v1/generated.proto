/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/


// This file was autogenerated by go-to-protobuf. Do not edit it manually!

syntax = "proto2";

package k8s.io.api.core.v1;

import "k8s.io/apimachinery/pkg/api/resource/generated.proto";
import "k8s.io/apimachinery/pkg/apis/meta/v1/generated.proto";
import "k8s.io/apimachinery/pkg/runtime/generated.proto";
import "k8s.io/apimachinery/pkg/runtime/schema/generated.proto";
import "k8s.io/apimachinery/pkg/util/intstr/generated.proto";

// Package-wide variables from generator "generated".
option go_package = "k8s.io/api/core/v1";

// Represents a Persistent Disk resource in AWS.
//
// An AWS EBS disk must exist before mounting to a container. The disk
// must also be in the same AWS zone as the kubelet. An AWS EBS disk
// can only be mounted as read/write once. AWS EBS volumes support
// ownership management and SELinux relabeling.
message AWSElasticBlockStoreVolumeSource {
  // volumeID is unique ID of the persistent disk resource in AWS (Amazon EBS volume).
  // More info: https://kubernetes.io/docs/concepts/storage/volumes#awselasticblockstore
  optional string volumeID = 1;

  // fsType is the filesystem type of the volume that you want to mount.
  // Tip: Ensure that the filesystem type is supported by the host operating system.
  // Examples: "ext4", "xfs", "ntfs". Implicitly inferred to be "ext4" if unspecified.
  // More info: https://kubernetes.io/docs/concepts/storage/volumes#awselasticblockstore
  // TODO: how do we prevent errors in the filesystem from compromising the machine
  // +optional
  optional string fsType = 2;

  // partition is the partition in the volume that you want to mount.
  // If omitted, the default is to mount by volume name.
  // Examples: For volume /dev/sda1, you specify the partition as "1".
  // Similarly, the volume partition for /dev/sda is "0" (or you can leave the property empty).
  // +optional
  optional int32 partition = 3;

  // readOnly value true will force the readOnly setting in VolumeMounts.
  // More info: https://kubernetes.io/docs/concepts/storage/volumes#awselasticblockstore
  // +optional
  optional bool readOnly = 4;
}

// Affinity is a group of affinity scheduling rules.
message Affinity {
  // Describes node affinity scheduling rules for the pod.
  // +optional
  optional NodeAffinity nodeAffinity = 1;

  // Describes pod affinity scheduling rules (e.g. co-locate this pod in the same node, zone, etc. as some other pod(s)).
  // +optional
  optional PodAffinity podAffinity = 2;

  // Describes pod anti-affinity scheduling rules (e.g. avoid putting this pod in the same node, zone, etc. as some other pod(s)).
  // +optional
  optional PodAntiAffinity podAntiAffinity = 3;
}

// AttachedVolume describes a volume attached to a node
message AttachedVolume {
  // Name of the attached volume
  optional string name = 1;

  // DevicePath represents the device path where the volume should be available
  optional string devicePath = 2;
}

// AvoidPods describes pods that should avoid this node. This is the value for a
// Node annotation with key scheduler.alpha.kubernetes.io/preferAvoidPods and
// will eventually become a field of NodeStatus.
message AvoidPods {
  // Bounded-sized list of signatures of pods that should avoid this node, sorted
  // in timestamp order from oldest to newest. Size of the slice is unspecified.
  // +optional
  repeated PreferAvoidPodsEntry preferAvoidPods = 1;
}

// AzureDisk represents an Azure Data Disk mount on the host and bind mount to the pod.
message AzureDiskVolumeSource {
  // diskName is the Name of the data disk in the blob storage
  optional string diskName = 1;

  // diskURI is the URI of data disk in the blob storage
  optional string diskURI = 2;

  // cachingMode is the Host Caching mode: None, Read Only, Read Write.
  // +optional
  optional string cachingMode = 3;

  // fsType is Filesystem type to mount.
  // Must be a filesystem type supported by the host operating system.
  // Ex. "ext4", "xfs", "ntfs". Implicitly inferred to be "ext4" if unspecified.
  // +optional
  optional string fsType = 4;

  // readOnly Defaults to false (read/write). ReadOnly here will force
  // the ReadOnly setting in VolumeMounts.
  // +optional
  optional bool readOnly = 5;

  // kind expected values are Shared: multiple blob disks per storage account  Dedicated: single blob disk per storage account  Managed: azure managed data disk (only in managed availability set). defaults to shared
  optional string kind = 6;
}

// AzureFile represents an Azure File Service mount on the host and bind mount to the pod.
message AzureFilePersistentVolumeSource {
  // secretName is the name of secret that contains Azure Storage Account Name and Key
  optional string secretName = 1;

  // shareName is the azure Share Name
  optional string shareName = 2;

  // readOnly defaults to false (read/write). ReadOnly here will force
  // the ReadOnly setting in VolumeMounts.
  // +optional
  optional bool readOnly = 3;

  // secretNamespace is the namespace of the secret that contains Azure Storage Account Name and Key
  // default is the same as the Pod
  // +optional
  optional string secretNamespace = 4;
}

// AzureFile represents an Azure File Service mount on the host and bind mount to the pod.
message AzureFileVolumeSource {
  // secretName is the  name of secret that contains Azure Storage Account Name and Key
  optional string secretName = 1;

  // shareName is the azure share Name
  optional string shareName = 2;

  // readOnly defaults to false (read/write). ReadOnly here will force
  // the ReadOnly setting in VolumeMounts.
  // +optional
  optional bool readOnly = 3;
}

// Binding ties one object to another; for example, a pod is bound to a node by a scheduler.
// Deprecated in 1.7, please use the bindings subresource of pods instead.
message Binding {
  // Standard object's metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // The target object that you want to bind to the standard object.
  optional ObjectReference target = 2;
}

// Represents storage that is managed by an external CSI volume driver (Beta feature)
message CSIPersistentVolumeSource {
  // driver is the name of the driver to use for this volume.
  // Required.
  optional string driver = 1;

  // volumeHandle is the unique volume name returned by the CSI volume
  // plugin’s CreateVolume to refer to the volume on all subsequent calls.
  // Required.
  optional string volumeHandle = 2;

  // readOnly value to pass to ControllerPublishVolumeRequest.
  // Defaults to false (read/write).
  // +optional
  optional bool readOnly = 3;

  // fsType to mount. Must be a filesystem type supported by the host operating system.
  // Ex. "ext4", "xfs", "ntfs".
  // +optional
  optional string fsType = 4;

  // volumeAttributes of the volume to publish.
  // +optional
  map<string, string> volumeAttributes = 5;

  // controllerPublishSecretRef is a reference to the secret object containing
  // sensitive information to pass to the CSI driver to complete the CSI
  // ControllerPublishVolume and ControllerUnpublishVolume calls.
  // This field is optional, and may be empty if no secret is required. If the
  // secret object contains more than one secret, all secrets are passed.
  // +optional
  optional SecretReference controllerPublishSecretRef = 6;

  // nodeStageSecretRef is a reference to the secret object containing sensitive
  // information to pass to the CSI driver to complete the CSI NodeStageVolume
  // and NodeStageVolume and NodeUnstageVolume calls.
  // This field is optional, and may be empty if no secret is required. If the
  // secret object contains more than one secret, all secrets are passed.
  // +optional
  optional SecretReference nodeStageSecretRef = 7;

  // nodePublishSecretRef is a reference to the secret object containing
  // sensitive information to pass to the CSI driver to complete the CSI
  // NodePublishVolume and NodeUnpublishVolume calls.
  // This field is optional, and may be empty if no secret is required. If the
  // secret object contains more than one secret, all secrets are passed.
  // +optional
  optional SecretReference nodePublishSecretRef = 8;

  // controllerExpandSecretRef is a reference to the secret object containing
  // sensitive information to pass to the CSI driver to complete the CSI
  // ControllerExpandVolume call.
  // This field is optional, and may be empty if no secret is required. If the
  // secret object contains more than one secret, all secrets are passed.
  // +optional
  optional SecretReference controllerExpandSecretRef = 9;

  // nodeExpandSecretRef is a reference to the secret object containing
  // sensitive information to pass to the CSI driver to complete the CSI
  // NodeExpandVolume call.
  // This is a beta field which is enabled default by CSINodeExpandSecret feature gate.
  // This field is optional, may be omitted if no secret is required. If the
  // secret object contains more than one secret, all secrets are passed.
  // +featureGate=CSINodeExpandSecret
  // +optional
  optional SecretReference nodeExpandSecretRef = 10;
}

// Represents a source location of a volume to mount, managed by an external CSI driver
message CSIVolumeSource {
  // driver is the name of the CSI driver that handles this volume.
  // Consult with your admin for the correct name as registered in the cluster.
  optional string driver = 1;

  // readOnly specifies a read-only configuration for the volume.
  // Defaults to false (read/write).
  // +optional
  optional bool readOnly = 2;

  // fsType to mount. Ex. "ext4", "xfs", "ntfs".
  // If not provided, the empty value is passed to the associated CSI driver
  // which will determine the default filesystem to apply.
  // +optional
  optional string fsType = 3;

  // volumeAttributes stores driver-specific properties that are passed to the CSI
  // driver. Consult your driver's documentation for supported values.
  // +optional
  map<string, string> volumeAttributes = 4;

  // nodePublishSecretRef is a reference to the secret object containing
  // sensitive information to pass to the CSI driver to complete the CSI
  // NodePublishVolume and NodeUnpublishVolume calls.
  // This field is optional, and  may be empty if no secret is required. If the
  // secret object contains more than one secret, all secret references are passed.
  // +optional
  optional LocalObjectReference nodePublishSecretRef = 5;
}

// Adds and removes POSIX capabilities from running containers.
message Capabilities {
  // Added capabilities
  // +optional
  repeated string add = 1;

  // Removed capabilities
  // +optional
  repeated string drop = 2;
}

// Represents a Ceph Filesystem mount that lasts the lifetime of a pod
// Cephfs volumes do not support ownership management or SELinux relabeling.
message CephFSPersistentVolumeSource {
  // monitors is Required: Monitors is a collection of Ceph monitors
  // More info: https://examples.k8s.io/volumes/cephfs/README.md#how-to-use-it
  repeated string monitors = 1;

  // path is Optional: Used as the mounted root, rather than the full Ceph tree, default is /
  // +optional
  optional string path = 2;

  // user is Optional: User is the rados user name, default is admin
  // More info: https://examples.k8s.io/volumes/cephfs/README.md#how-to-use-it
  // +optional
  optional string user = 3;

  // secretFile is Optional: SecretFile is the path to key ring for User, default is /etc/ceph/user.secret
  // More info: https://examples.k8s.io/volumes/cephfs/README.md#how-to-use-it
  // +optional
  optional string secretFile = 4;

  // secretRef is Optional: SecretRef is reference to the authentication secret for User, default is empty.
  // More info: https://examples.k8s.io/volumes/cephfs/README.md#how-to-use-it
  // +optional
  optional SecretReference secretRef = 5;

  // readOnly is Optional: Defaults to false (read/write). ReadOnly here will force
  // the ReadOnly setting in VolumeMounts.
  // More info: https://examples.k8s.io/volumes/cephfs/README.md#how-to-use-it
  // +optional
  optional bool readOnly = 6;
}

// Represents a Ceph Filesystem mount that lasts the lifetime of a pod
// Cephfs volumes do not support ownership management or SELinux relabeling.
message CephFSVolumeSource {
  // monitors is Required: Monitors is a collection of Ceph monitors
  // More info: https://examples.k8s.io/volumes/cephfs/README.md#how-to-use-it
  repeated string monitors = 1;

  // path is Optional: Used as the mounted root, rather than the full Ceph tree, default is /
  // +optional
  optional string path = 2;

  // user is optional: User is the rados user name, default is admin
  // More info: https://examples.k8s.io/volumes/cephfs/README.md#how-to-use-it
  // +optional
  optional string user = 3;

  // secretFile is Optional: SecretFile is the path to key ring for User, default is /etc/ceph/user.secret
  // More info: https://examples.k8s.io/volumes/cephfs/README.md#how-to-use-it
  // +optional
  optional string secretFile = 4;

  // secretRef is Optional: SecretRef is reference to the authentication secret for User, default is empty.
  // More info: https://examples.k8s.io/volumes/cephfs/README.md#how-to-use-it
  // +optional
  optional LocalObjectReference secretRef = 5;

  // readOnly is Optional: Defaults to false (read/write). ReadOnly here will force
  // the ReadOnly setting in VolumeMounts.
  // More info: https://examples.k8s.io/volumes/cephfs/README.md#how-to-use-it
  // +optional
  optional bool readOnly = 6;
}

// Represents a cinder volume resource in Openstack.
// A Cinder volume must exist before mounting to a container.
// The volume must also be in the same region as the kubelet.
// Cinder volumes support ownership management and SELinux relabeling.
message CinderPersistentVolumeSource {
  // volumeID used to identify the volume in cinder.
  // More info: https://examples.k8s.io/mysql-cinder-pd/README.md
  optional string volumeID = 1;

  // fsType Filesystem type to mount.
  // Must be a filesystem type supported by the host operating system.
  // Examples: "ext4", "xfs", "ntfs". Implicitly inferred to be "ext4" if unspecified.
  // More info: https://examples.k8s.io/mysql-cinder-pd/README.md
  // +optional
  optional string fsType = 2;

  // readOnly is Optional: Defaults to false (read/write). ReadOnly here will force
  // the ReadOnly setting in VolumeMounts.
  // More info: https://examples.k8s.io/mysql-cinder-pd/README.md
  // +optional
  optional bool readOnly = 3;

  // secretRef is Optional: points to a secret object containing parameters used to connect
  // to OpenStack.
  // +optional
  optional SecretReference secretRef = 4;
}

// Represents a cinder volume resource in Openstack.
// A Cinder volume must exist before mounting to a container.
// The volume must also be in the same region as the kubelet.
// Cinder volumes support ownership management and SELinux relabeling.
message CinderVolumeSource {
  // volumeID used to identify the volume in cinder.
  // More info: https://examples.k8s.io/mysql-cinder-pd/README.md
  optional string volumeID = 1;

  // fsType is the filesystem type to mount.
  // Must be a filesystem type supported by the host operating system.
  // Examples: "ext4", "xfs", "ntfs". Implicitly inferred to be "ext4" if unspecified.
  // More info: https://examples.k8s.io/mysql-cinder-pd/README.md
  // +optional
  optional string fsType = 2;

  // readOnly defaults to false (read/write). ReadOnly here will force
  // the ReadOnly setting in VolumeMounts.
  // More info: https://examples.k8s.io/mysql-cinder-pd/README.md
  // +optional
  optional bool readOnly = 3;

  // secretRef is optional: points to a secret object containing parameters used to connect
  // to OpenStack.
  // +optional
  optional LocalObjectReference secretRef = 4;
}

// ClaimSource describes a reference to a ResourceClaim.
//
// Exactly one of these fields should be set.  Consumers of this type must
// treat an empty object as if it has an unknown value.
message ClaimSource {
  // ResourceClaimName is the name of a ResourceClaim object in the same
  // namespace as this pod.
  optional string resourceClaimName = 1;

  // ResourceClaimTemplateName is the name of a ResourceClaimTemplate
  // object in the same namespace as this pod.
  //
  // The template will be used to create a new ResourceClaim, which will
  // be bound to this pod. When this pod is deleted, the ResourceClaim
  // will also be deleted. The pod name and resource name, along with a
  // generated component, will be used to form a unique name for the
  // ResourceClaim, which will be recorded in pod.status.resourceClaimStatuses.
  //
  // This field is immutable and no changes will be made to the
  // corresponding ResourceClaim by the control plane after creating the
  // ResourceClaim.
  optional string resourceClaimTemplateName = 2;
}

// ClientIPConfig represents the configurations of Client IP based session affinity.
message ClientIPConfig {
  // timeoutSeconds specifies the seconds of ClientIP type session sticky time.
  // The value must be >0 && <=86400(for 1 day) if ServiceAffinity == "ClientIP".
  // Default value is 10800(for 3 hours).
  // +optional
  optional int32 timeoutSeconds = 1;
}

// Information about the condition of a component.
message ComponentCondition {
  // Type of condition for a component.
  // Valid value: "Healthy"
  optional string type = 1;

  // Status of the condition for a component.
  // Valid values for "Healthy": "True", "False", or "Unknown".
  optional string status = 2;

  // Message about the condition for a component.
  // For example, information about a health check.
  // +optional
  optional string message = 3;

  // Condition error code for a component.
  // For example, a health check error code.
  // +optional
  optional string error = 4;
}

// ComponentStatus (and ComponentStatusList) holds the cluster validation info.
// Deprecated: This API is deprecated in v1.19+
message ComponentStatus {
  // Standard object's metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // List of component conditions observed
  // +optional
  // +patchMergeKey=type
  // +patchStrategy=merge
  repeated ComponentCondition conditions = 2;
}

// Status of all the conditions for the component as a list of ComponentStatus objects.
// Deprecated: This API is deprecated in v1.19+
message ComponentStatusList {
  // Standard list metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // List of ComponentStatus objects.
  repeated ComponentStatus items = 2;
}

// ConfigMap holds configuration data for pods to consume.
message ConfigMap {
  // Standard object's metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Immutable, if set to true, ensures that data stored in the ConfigMap cannot
  // be updated (only object metadata can be modified).
  // If not set to true, the field can be modified at any time.
  // Defaulted to nil.
  // +optional
  optional bool immutable = 4;

  // Data contains the configuration data.
  // Each key must consist of alphanumeric characters, '-', '_' or '.'.
  // Values with non-UTF-8 byte sequences must use the BinaryData field.
  // The keys stored in Data must not overlap with the keys in
  // the BinaryData field, this is enforced during validation process.
  // +optional
  map<string, string> data = 2;

  // BinaryData contains the binary data.
  // Each key must consist of alphanumeric characters, '-', '_' or '.'.
  // BinaryData can contain byte sequences that are not in the UTF-8 range.
  // The keys stored in BinaryData must not overlap with the ones in
  // the Data field, this is enforced during validation process.
  // Using this field will require 1.10+ apiserver and
  // kubelet.
  // +optional
  map<string, bytes> binaryData = 3;
}

// ConfigMapEnvSource selects a ConfigMap to populate the environment
// variables with.
//
// The contents of the target ConfigMap's Data field will represent the
// key-value pairs as environment variables.
message ConfigMapEnvSource {
  // The ConfigMap to select from.
  optional LocalObjectReference localObjectReference = 1;

  // Specify whether the ConfigMap must be defined
  // +optional
  optional bool optional = 2;
}

// Selects a key from a ConfigMap.
// +structType=atomic
message ConfigMapKeySelector {
  // The ConfigMap to select from.
  optional LocalObjectReference localObjectReference = 1;

  // The key to select.
  optional string key = 2;

  // Specify whether the ConfigMap or its key must be defined
  // +optional
  optional bool optional = 3;
}

// ConfigMapList is a resource containing a list of ConfigMap objects.
message ConfigMapList {
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // Items is the list of ConfigMaps.
  repeated ConfigMap items = 2;
}

// ConfigMapNodeConfigSource contains the information to reference a ConfigMap as a config source for the Node.
// This API is deprecated since 1.22: https://git.k8s.io/enhancements/keps/sig-node/281-dynamic-kubelet-configuration
message ConfigMapNodeConfigSource {
  // Namespace is the metadata.namespace of the referenced ConfigMap.
  // This field is required in all cases.
  optional string namespace = 1;

  // Name is the metadata.name of the referenced ConfigMap.
  // This field is required in all cases.
  optional string name = 2;

  // UID is the metadata.UID of the referenced ConfigMap.
  // This field is forbidden in Node.Spec, and required in Node.Status.
  // +optional
  optional string uid = 3;

  // ResourceVersion is the metadata.ResourceVersion of the referenced ConfigMap.
  // This field is forbidden in Node.Spec, and required in Node.Status.
  // +optional
  optional string resourceVersion = 4;

  // KubeletConfigKey declares which key of the referenced ConfigMap corresponds to the KubeletConfiguration structure
  // This field is required in all cases.
  optional string kubeletConfigKey = 5;
}

// Adapts a ConfigMap into a projected volume.
//
// The contents of the target ConfigMap's Data field will be presented in a
// projected volume as files using the keys in the Data field as the file names,
// unless the items element is populated with specific mappings of keys to paths.
// Note that this is identical to a configmap volume source without the default
// mode.
message ConfigMapProjection {
  optional LocalObjectReference localObjectReference = 1;

  // items if unspecified, each key-value pair in the Data field of the referenced
  // ConfigMap will be projected into the volume as a file whose name is the
  // key and content is the value. If specified, the listed keys will be
  // projected into the specified paths, and unlisted keys will not be
  // present. If a key is specified which is not present in the ConfigMap,
  // the volume setup will error unless it is marked optional. Paths must be
  // relative and may not contain the '..' path or start with '..'.
  // +optional
  repeated KeyToPath items = 2;

  // optional specify whether the ConfigMap or its keys must be defined
  // +optional
  optional bool optional = 4;
}

// Adapts a ConfigMap into a volume.
//
// The contents of the target ConfigMap's Data field will be presented in a
// volume as files using the keys in the Data field as the file names, unless
// the items element is populated with specific mappings of keys to paths.
// ConfigMap volumes support ownership management and SELinux relabeling.
message ConfigMapVolumeSource {
  optional LocalObjectReference localObjectReference = 1;

  // items if unspecified, each key-value pair in the Data field of the referenced
  // ConfigMap will be projected into the volume as a file whose name is the
  // key and content is the value. If specified, the listed keys will be
  // projected into the specified paths, and unlisted keys will not be
  // present. If a key is specified which is not present in the ConfigMap,
  // the volume setup will error unless it is marked optional. Paths must be
  // relative and may not contain the '..' path or start with '..'.
  // +optional
  repeated KeyToPath items = 2;

  // defaultMode is optional: mode bits used to set permissions on created files by default.
  // Must be an octal value between 0000 and 0777 or a decimal value between 0 and 511.
  // YAML accepts both octal and decimal values, JSON requires decimal values for mode bits.
  // Defaults to 0644.
  // Directories within the path are not affected by this setting.
  // This might be in conflict with other options that affect the file
  // mode, like fsGroup, and the result can be other mode bits set.
  // +optional
  optional int32 defaultMode = 3;

  // optional specify whether the ConfigMap or its keys must be defined
  // +optional
  optional bool optional = 4;
}

// A single application container that you want to run within a pod.
message Container {
  // Name of the container specified as a DNS_LABEL.
  // Each container in a pod must have a unique name (DNS_LABEL).
  // Cannot be updated.
  optional string name = 1;

  // Container image name.
  // More info: https://kubernetes.io/docs/concepts/containers/images
  // This field is optional to allow higher level config management to default or override
  // container images in workload controllers like Deployments and StatefulSets.
  // +optional
  optional string image = 2;

  // Entrypoint array. Not executed within a shell.
  // The container image's ENTRYPOINT is used if this is not provided.
  // Variable references $(VAR_NAME) are expanded using the container's environment. If a variable
  // cannot be resolved, the reference in the input string will be unchanged. Double $$ are reduced
  // to a single $, which allows for escaping the $(VAR_NAME) syntax: i.e. "$$(VAR_NAME)" will
  // produce the string literal "$(VAR_NAME)". Escaped references will never be expanded, regardless
  // of whether the variable exists or not. Cannot be updated.
  // More info: https://kubernetes.io/docs/tasks/inject-data-application/define-command-argument-container/#running-a-command-in-a-shell
  // +optional
  repeated string command = 3;

  // Arguments to the entrypoint.
  // The container image's CMD is used if this is not provided.
  // Variable references $(VAR_NAME) are expanded using the container's environment. If a variable
  // cannot be resolved, the reference in the input string will be unchanged. Double $$ are reduced
  // to a single $, which allows for escaping the $(VAR_NAME) syntax: i.e. "$$(VAR_NAME)" will
  // produce the string literal "$(VAR_NAME)". Escaped references will never be expanded, regardless
  // of whether the variable exists or not. Cannot be updated.
  // More info: https://kubernetes.io/docs/tasks/inject-data-application/define-command-argument-container/#running-a-command-in-a-shell
  // +optional
  repeated string args = 4;

  // Container's working directory.
  // If not specified, the container runtime's default will be used, which
  // might be configured in the container image.
  // Cannot be updated.
  // +optional
  optional string workingDir = 5;

  // List of ports to expose from the container. Not specifying a port here
  // DOES NOT prevent that port from being exposed. Any port which is
  // listening on the default "0.0.0.0" address inside a container will be
  // accessible from the network.
  // Modifying this array with strategic merge patch may corrupt the data.
  // For more information See https://github.com/kubernetes/kubernetes/issues/108255.
  // Cannot be updated.
  // +optional
  // +patchMergeKey=containerPort
  // +patchStrategy=merge
  // +listType=map
  // +listMapKey=containerPort
  // +listMapKey=protocol
  repeated ContainerPort ports = 6;

  // List of sources to populate environment variables in the container.
  // The keys defined within a source must be a C_IDENTIFIER. All invalid keys
  // will be reported as an event when the container is starting. When a key exists in multiple
  // sources, the value associated with the last source will take precedence.
  // Values defined by an Env with a duplicate key will take precedence.
  // Cannot be updated.
  // +optional
  repeated EnvFromSource envFrom = 19;

  // List of environment variables to set in the container.
  // Cannot be updated.
  // +optional
  // +patchMergeKey=name
  // +patchStrategy=merge
  repeated EnvVar env = 7;

  // Compute Resources required by this container.
  // Cannot be updated.
  // More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/
  // +optional
  optional ResourceRequirements resources = 8;

  // Resources resize policy for the container.
  // +featureGate=InPlacePodVerticalScaling
  // +optional
  // +listType=atomic
  repeated ContainerResizePolicy resizePolicy = 23;

  // RestartPolicy defines the restart behavior of individual containers in a pod.
  // This field may only be set for init containers, and the only allowed value is "Always".
  // For non-init containers or when this field is not specified,
  // the restart behavior is defined by the Pod's restart policy and the container type.
  // Setting the RestartPolicy as "Always" for the init container will have the following effect:
  // this init container will be continually restarted on
  // exit until all regular containers have terminated. Once all regular
  // containers have completed, all init containers with restartPolicy "Always"
  // will be shut down. This lifecycle differs from normal init containers and
  // is often referred to as a "sidecar" container. Although this init
  // container still starts in the init container sequence, it does not wait
  // for the container to complete before proceeding to the next init
  // container. Instead, the next init container starts immediately after this
  // init container is started, or after any startupProbe has successfully
  // completed.
  // +featureGate=SidecarContainers
  // +optional
  optional string restartPolicy = 24;

  // Pod volumes to mount into the container's filesystem.
  // Cannot be updated.
  // +optional
  // +patchMergeKey=mountPath
  // +patchStrategy=merge
  repeated VolumeMount volumeMounts = 9;

  // volumeDevices is the list of block devices to be used by the container.
  // +patchMergeKey=devicePath
  // +patchStrategy=merge
  // +optional
  repeated VolumeDevice volumeDevices = 21;

  // Periodic probe of container liveness.
  // Container will be restarted if the probe fails.
  // Cannot be updated.
  // More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes
  // +optional
  optional Probe livenessProbe = 10;

  // Periodic probe of container service readiness.
  // Container will be removed from service endpoints if the probe fails.
  // Cannot be updated.
  // More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes
  // +optional
  optional Probe readinessProbe = 11;

  // StartupProbe indicates that the Pod has successfully initialized.
  // If specified, no other probes are executed until this completes successfully.
  // If this probe fails, the Pod will be restarted, just as if the livenessProbe failed.
  // This can be used to provide different probe parameters at the beginning of a Pod's lifecycle,
  // when it might take a long time to load data or warm a cache, than during steady-state operation.
  // This cannot be updated.
  // More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes
  // +optional
  optional Probe startupProbe = 22;

  // Actions that the management system should take in response to container lifecycle events.
  // Cannot be updated.
  // +optional
  optional Lifecycle lifecycle = 12;

  // Optional: Path at which the file to which the container's termination message
  // will be written is mounted into the container's filesystem.
  // Message written is intended to be brief final status, such as an assertion failure message.
  // Will be truncated by the node if greater than 4096 bytes. The total message length across
  // all containers will be limited to 12kb.
  // Defaults to /dev/termination-log.
  // Cannot be updated.
  // +optional
  optional string terminationMessagePath = 13;

  // Indicate how the termination message should be populated. File will use the contents of
  // terminationMessagePath to populate the container status message on both success and failure.
  // FallbackToLogsOnError will use the last chunk of container log output if the termination
  // message file is empty and the container exited with an error.
  // The log output is limited to 2048 bytes or 80 lines, whichever is smaller.
  // Defaults to File.
  // Cannot be updated.
  // +optional
  optional string terminationMessagePolicy = 20;

  // Image pull policy.
  // One of Always, Never, IfNotPresent.
  // Defaults to Always if :latest tag is specified, or IfNotPresent otherwise.
  // Cannot be updated.
  // More info: https://kubernetes.io/docs/concepts/containers/images#updating-images
  // +optional
  optional string imagePullPolicy = 14;

  // SecurityContext defines the security options the container should be run with.
  // If set, the fields of SecurityContext override the equivalent fields of PodSecurityContext.
  // More info: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/
  // +optional
  optional SecurityContext securityContext = 15;

  // Whether this container should allocate a buffer for stdin in the container runtime. If this
  // is not set, reads from stdin in the container will always result in EOF.
  // Default is false.
  // +optional
  optional bool stdin = 16;

  // Whether the container runtime should close the stdin channel after it has been opened by
  // a single attach. When stdin is true the stdin stream will remain open across multiple attach
  // sessions. If stdinOnce is set to true, stdin is opened on container start, is empty until the
  // first client attaches to stdin, and then remains open and accepts data until the client disconnects,
  // at which time stdin is closed and remains closed until the container is restarted. If this
  // flag is false, a container processes that reads from stdin will never receive an EOF.
  // Default is false
  // +optional
  optional bool stdinOnce = 17;

  // Whether this container should allocate a TTY for itself, also requires 'stdin' to be true.
  // Default is false.
  // +optional
  optional bool tty = 18;
}

// Describe a container image
message ContainerImage {
  // Names by which this image is known.
  // e.g. ["kubernetes.example/hyperkube:v1.0.7", "cloud-vendor.registry.example/cloud-vendor/hyperkube:v1.0.7"]
  // +optional
  repeated string names = 1;

  // The size of the image in bytes.
  // +optional
  optional int64 sizeBytes = 2;
}

// ContainerPort represents a network port in a single container.
message ContainerPort {
  // If specified, this must be an IANA_SVC_NAME and unique within the pod. Each
  // named port in a pod must have a unique name. Name for the port that can be
  // referred to by services.
  // +optional
  optional string name = 1;

  // Number of port to expose on the host.
  // If specified, this must be a valid port number, 0 < x < 65536.
  // If HostNetwork is specified, this must match ContainerPort.
  // Most containers do not need this.
  // +optional
  optional int32 hostPort = 2;

  // Number of port to expose on the pod's IP address.
  // This must be a valid port number, 0 < x < 65536.
  optional int32 containerPort = 3;

  // Protocol for port. Must be UDP, TCP, or SCTP.
  // Defaults to "TCP".
  // +optional
  // +default="TCP"
  optional string protocol = 4;

  // What host IP to bind the external port to.
  // +optional
  optional string hostIP = 5;
}

// ContainerResizePolicy represents resource resize policy for the container.
message ContainerResizePolicy {
  // Name of the resource to which this resource resize policy applies.
  // Supported values: cpu, memory.
  optional string resourceName = 1;

  // Restart policy to apply when specified resource is resized.
  // If not specified, it defaults to NotRequired.
  optional string restartPolicy = 2;
}

// ContainerState holds a possible state of container.
// Only one of its members may be specified.
// If none of them is specified, the default one is ContainerStateWaiting.
message ContainerState {
  // Details about a waiting container
  // +optional
  optional ContainerStateWaiting waiting = 1;

  // Details about a running container
  // +optional
  optional ContainerStateRunning running = 2;

  // Details about a terminated container
  // +optional
  optional ContainerStateTerminated terminated = 3;
}

// ContainerStateRunning is a running state of a container.
message ContainerStateRunning {
  // Time at which the container was last (re-)started
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time startedAt = 1;
}

// ContainerStateTerminated is a terminated state of a container.
message ContainerStateTerminated {
  // Exit status from the last termination of the container
  optional int32 exitCode = 1;

  // Signal from the last termination of the container
  // +optional
  optional int32 signal = 2;

  // (brief) reason from the last termination of the container
  // +optional
  optional string reason = 3;

  // Message regarding the last termination of the container
  // +optional
  optional string message = 4;

  // Time at which previous execution of the container started
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time startedAt = 5;

  // Time at which the container last terminated
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time finishedAt = 6;

  // Container's ID in the format '<type>://<container_id>'
  // +optional
  optional string containerID = 7;
}

// ContainerStateWaiting is a waiting state of a container.
message ContainerStateWaiting {
  // (brief) reason the container is not yet running.
  // +optional
  optional string reason = 1;

  // Message regarding why the container is not yet running.
  // +optional
  optional string message = 2;
}

// ContainerStatus contains details for the current status of this container.
message ContainerStatus {
  // Name is a DNS_LABEL representing the unique name of the container.
  // Each container in a pod must have a unique name across all container types.
  // Cannot be updated.
  optional string name = 1;

  // State holds details about the container's current condition.
  // +optional
  optional ContainerState state = 2;

  // LastTerminationState holds the last termination state of the container to
  // help debug container crashes and restarts. This field is not
  // populated if the container is still running and RestartCount is 0.
  // +optional
  optional ContainerState lastState = 3;

  // Ready specifies whether the container is currently passing its readiness check.
  // The value will change as readiness probes keep executing. If no readiness
  // probes are specified, this field defaults to true once the container is
  // fully started (see Started field).
  //
  // The value is typically used to determine whether a container is ready to
  // accept traffic.
  optional bool ready = 4;

  // RestartCount holds the number of times the container has been restarted.
  // Kubelet makes an effort to always increment the value, but there
  // are cases when the state may be lost due to node restarts and then the value
  // may be reset to 0. The value is never negative.
  optional int32 restartCount = 5;

  // Image is the name of container image that the container is running.
  // The container image may not match the image used in the PodSpec,
  // as it may have been resolved by the runtime.
  // More info: https://kubernetes.io/docs/concepts/containers/images.
  optional string image = 6;

  // ImageID is the image ID of the container's image. The image ID may not
  // match the image ID of the image used in the PodSpec, as it may have been
  // resolved by the runtime.
  optional string imageID = 7;

  // ContainerID is the ID of the container in the format '<type>://<container_id>'.
  // Where type is a container runtime identifier, returned from Version call of CRI API
  // (for example "containerd").
  // +optional
  optional string containerID = 8;

  // Started indicates whether the container has finished its postStart lifecycle hook
  // and passed its startup probe.
  // Initialized as false, becomes true after startupProbe is considered
  // successful. Resets to false when the container is restarted, or if kubelet
  // loses state temporarily. In both cases, startup probes will run again.
  // Is always true when no startupProbe is defined and container is running and
  // has passed the postStart lifecycle hook. The null value must be treated the
  // same as false.
  // +optional
  optional bool started = 9;

  // AllocatedResources represents the compute resources allocated for this container by the
  // node. Kubelet sets this value to Container.Resources.Requests upon successful pod admission
  // and after successfully admitting desired pod resize.
  // +featureGate=InPlacePodVerticalScaling
  // +optional
  map<string, k8s.io.apimachinery.pkg.api.resource.Quantity> allocatedResources = 10;

  // Resources represents the compute resource requests and limits that have been successfully
  // enacted on the running container after it has been started or has been successfully resized.
  // +featureGate=InPlacePodVerticalScaling
  // +optional
  optional ResourceRequirements resources = 11;
}

// DaemonEndpoint contains information about a single Daemon endpoint.
message DaemonEndpoint {
  // Port number of the given endpoint.
  optional int32 Port = 1;
}

// Represents downward API info for projecting into a projected volume.
// Note that this is identical to a downwardAPI volume source without the default
// mode.
message DownwardAPIProjection {
  // Items is a list of DownwardAPIVolume file
  // +optional
  repeated DownwardAPIVolumeFile items = 1;
}

// DownwardAPIVolumeFile represents information to create the file containing the pod field
message DownwardAPIVolumeFile {
  // Required: Path is  the relative path name of the file to be created. Must not be absolute or contain the '..' path. Must be utf-8 encoded. The first item of the relative path must not start with '..'
  optional string path = 1;

  // Required: Selects a field of the pod: only annotations, labels, name and namespace are supported.
  // +optional
  optional ObjectFieldSelector fieldRef = 2;

  // Selects a resource of the container: only resources limits and requests
  // (limits.cpu, limits.memory, requests.cpu and requests.memory) are currently supported.
  // +optional
  optional ResourceFieldSelector resourceFieldRef = 3;

  // Optional: mode bits used to set permissions on this file, must be an octal value
  // between 0000 and 0777 or a decimal value between 0 and 511.
  // YAML accepts both octal and decimal values, JSON requires decimal values for mode bits.
  // If not specified, the volume defaultMode will be used.
  // This might be in conflict with other options that affect the file
  // mode, like fsGroup, and the result can be other mode bits set.
  // +optional
  optional int32 mode = 4;
}

// DownwardAPIVolumeSource represents a volume containing downward API info.
// Downward API volumes support ownership management and SELinux relabeling.
message DownwardAPIVolumeSource {
  // Items is a list of downward API volume file
  // +optional
  repeated DownwardAPIVolumeFile items = 1;

  // Optional: mode bits to use on created files by default. Must be a
  // Optional: mode bits used to set permissions on created files by default.
  // Must be an octal value between 0000 and 0777 or a decimal value between 0 and 511.
  // YAML accepts both octal and decimal values, JSON requires decimal values for mode bits.
  // Defaults to 0644.
  // Directories within the path are not affected by this setting.
  // This might be in conflict with other options that affect the file
  // mode, like fsGroup, and the result can be other mode bits set.
  // +optional
  optional int32 defaultMode = 2;
}

// Represents an empty directory for a pod.
// Empty directory volumes support ownership management and SELinux relabeling.
message EmptyDirVolumeSource {
  // medium represents what type of storage medium should back this directory.
  // The default is "" which means to use the node's default medium.
  // Must be an empty string (default) or Memory.
  // More info: https://kubernetes.io/docs/concepts/storage/volumes#emptydir
  // +optional
  optional string medium = 1;

  // sizeLimit is the total amount of local storage required for this EmptyDir volume.
  // The size limit is also applicable for memory medium.
  // The maximum usage on memory medium EmptyDir would be the minimum value between
  // the SizeLimit specified here and the sum of memory limits of all containers in a pod.
  // The default is nil which means that the limit is undefined.
  // More info: https://kubernetes.io/docs/concepts/storage/volumes#emptydir
  // +optional
  optional k8s.io.apimachinery.pkg.api.resource.Quantity sizeLimit = 2;
}

// EndpointAddress is a tuple that describes single IP address.
// +structType=atomic
message EndpointAddress {
  // The IP of this endpoint.
  // May not be loopback (*********/8 or ::1), link-local (***********/16 or fe80::/10),
  // or link-local multicast (*********/24 or ff02::/16).
  optional string ip = 1;

  // The Hostname of this endpoint
  // +optional
  optional string hostname = 3;

  // Optional: Node hosting this endpoint. This can be used to determine endpoints local to a node.
  // +optional
  optional string nodeName = 4;

  // Reference to object providing the endpoint.
  // +optional
  optional ObjectReference targetRef = 2;
}

// EndpointPort is a tuple that describes a single port.
// +structType=atomic
message EndpointPort {
  // The name of this port.  This must match the 'name' field in the
  // corresponding ServicePort.
  // Must be a DNS_LABEL.
  // Optional only if one port is defined.
  // +optional
  optional string name = 1;

  // The port number of the endpoint.
  optional int32 port = 2;

  // The IP protocol for this port.
  // Must be UDP, TCP, or SCTP.
  // Default is TCP.
  // +optional
  optional string protocol = 3;

  // The application protocol for this port.
  // This is used as a hint for implementations to offer richer behavior for protocols that they understand.
  // This field follows standard Kubernetes label syntax.
  // Valid values are either:
  //
  // * Un-prefixed protocol names - reserved for IANA standard service names (as per
  // RFC-6335 and https://www.iana.org/assignments/service-names).
  //
  // * Kubernetes-defined prefixed names:
  //   * 'kubernetes.io/h2c' - HTTP/2 over cleartext as described in https://www.rfc-editor.org/rfc/rfc7540
  //   * 'kubernetes.io/ws'  - WebSocket over cleartext as described in https://www.rfc-editor.org/rfc/rfc6455
  //   * 'kubernetes.io/wss' - WebSocket over TLS as described in https://www.rfc-editor.org/rfc/rfc6455
  //
  // * Other protocols should use implementation-defined prefixed names such as
  // mycompany.com/my-custom-protocol.
  // +optional
  optional string appProtocol = 4;
}

// EndpointSubset is a group of addresses with a common set of ports. The
// expanded set of endpoints is the Cartesian product of Addresses x Ports.
// For example, given:
//
// 	{
// 	  Addresses: [{"ip": "*********"}, {"ip": "*********"}],
// 	  Ports:     [{"name": "a", "port": 8675}, {"name": "b", "port": 309}]
// 	}
//
// The resulting set of endpoints can be viewed as:
//
// 	a: [ *********:8675, *********:8675 ],
// 	b: [ *********:309, *********:309 ]
message EndpointSubset {
  // IP addresses which offer the related ports that are marked as ready. These endpoints
  // should be considered safe for load balancers and clients to utilize.
  // +optional
  repeated EndpointAddress addresses = 1;

  // IP addresses which offer the related ports but are not currently marked as ready
  // because they have not yet finished starting, have recently failed a readiness check,
  // or have recently failed a liveness check.
  // +optional
  repeated EndpointAddress notReadyAddresses = 2;

  // Port numbers available on the related IP addresses.
  // +optional
  repeated EndpointPort ports = 3;
}

// Endpoints is a collection of endpoints that implement the actual service. Example:
//
// 	 Name: "mysvc",
// 	 Subsets: [
// 	   {
// 	     Addresses: [{"ip": "*********"}, {"ip": "*********"}],
// 	     Ports: [{"name": "a", "port": 8675}, {"name": "b", "port": 309}]
// 	   },
// 	   {
// 	     Addresses: [{"ip": "*********"}],
// 	     Ports: [{"name": "a", "port": 93}, {"name": "b", "port": 76}]
// 	   },
// 	]
message Endpoints {
  // Standard object's metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // The set of all endpoints is the union of all subsets. Addresses are placed into
  // subsets according to the IPs they share. A single address with multiple ports,
  // some of which are ready and some of which are not (because they come from
  // different containers) will result in the address being displayed in different
  // subsets for the different ports. No address will appear in both Addresses and
  // NotReadyAddresses in the same subset.
  // Sets of addresses and ports that comprise a service.
  // +optional
  repeated EndpointSubset subsets = 2;
}

// EndpointsList is a list of endpoints.
message EndpointsList {
  // Standard list metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // List of endpoints.
  repeated Endpoints items = 2;
}

// EnvFromSource represents the source of a set of ConfigMaps
message EnvFromSource {
  // An optional identifier to prepend to each key in the ConfigMap. Must be a C_IDENTIFIER.
  // +optional
  optional string prefix = 1;

  // The ConfigMap to select from
  // +optional
  optional ConfigMapEnvSource configMapRef = 2;

  // The Secret to select from
  // +optional
  optional SecretEnvSource secretRef = 3;
}

// EnvVar represents an environment variable present in a Container.
message EnvVar {
  // Name of the environment variable. Must be a C_IDENTIFIER.
  optional string name = 1;

  // Variable references $(VAR_NAME) are expanded
  // using the previously defined environment variables in the container and
  // any service environment variables. If a variable cannot be resolved,
  // the reference in the input string will be unchanged. Double $$ are reduced
  // to a single $, which allows for escaping the $(VAR_NAME) syntax: i.e.
  // "$$(VAR_NAME)" will produce the string literal "$(VAR_NAME)".
  // Escaped references will never be expanded, regardless of whether the variable
  // exists or not.
  // Defaults to "".
  // +optional
  optional string value = 2;

  // Source for the environment variable's value. Cannot be used if value is not empty.
  // +optional
  optional EnvVarSource valueFrom = 3;
}

// EnvVarSource represents a source for the value of an EnvVar.
message EnvVarSource {
  // Selects a field of the pod: supports metadata.name, metadata.namespace, `metadata.labels['<KEY>']`, `metadata.annotations['<KEY>']`,
  // spec.nodeName, spec.serviceAccountName, status.hostIP, status.podIP, status.podIPs.
  // +optional
  optional ObjectFieldSelector fieldRef = 1;

  // Selects a resource of the container: only resources limits and requests
  // (limits.cpu, limits.memory, limits.ephemeral-storage, requests.cpu, requests.memory and requests.ephemeral-storage) are currently supported.
  // +optional
  optional ResourceFieldSelector resourceFieldRef = 2;

  // Selects a key of a ConfigMap.
  // +optional
  optional ConfigMapKeySelector configMapKeyRef = 3;

  // Selects a key of a secret in the pod's namespace
  // +optional
  optional SecretKeySelector secretKeyRef = 4;
}

// An EphemeralContainer is a temporary container that you may add to an existing Pod for
// user-initiated activities such as debugging. Ephemeral containers have no resource or
// scheduling guarantees, and they will not be restarted when they exit or when a Pod is
// removed or restarted. The kubelet may evict a Pod if an ephemeral container causes the
// Pod to exceed its resource allocation.
//
// To add an ephemeral container, use the ephemeralcontainers subresource of an existing
// Pod. Ephemeral containers may not be removed or restarted.
message EphemeralContainer {
  // Ephemeral containers have all of the fields of Container, plus additional fields
  // specific to ephemeral containers. Fields in common with Container are in the
  // following inlined struct so than an EphemeralContainer may easily be converted
  // to a Container.
  optional EphemeralContainerCommon ephemeralContainerCommon = 1;

  // If set, the name of the container from PodSpec that this ephemeral container targets.
  // The ephemeral container will be run in the namespaces (IPC, PID, etc) of this container.
  // If not set then the ephemeral container uses the namespaces configured in the Pod spec.
  //
  // The container runtime must implement support for this feature. If the runtime does not
  // support namespace targeting then the result of setting this field is undefined.
  // +optional
  optional string targetContainerName = 2;
}

// EphemeralContainerCommon is a copy of all fields in Container to be inlined in
// EphemeralContainer. This separate type allows easy conversion from EphemeralContainer
// to Container and allows separate documentation for the fields of EphemeralContainer.
// When a new field is added to Container it must be added here as well.
message EphemeralContainerCommon {
  // Name of the ephemeral container specified as a DNS_LABEL.
  // This name must be unique among all containers, init containers and ephemeral containers.
  optional string name = 1;

  // Container image name.
  // More info: https://kubernetes.io/docs/concepts/containers/images
  optional string image = 2;

  // Entrypoint array. Not executed within a shell.
  // The image's ENTRYPOINT is used if this is not provided.
  // Variable references $(VAR_NAME) are expanded using the container's environment. If a variable
  // cannot be resolved, the reference in the input string will be unchanged. Double $$ are reduced
  // to a single $, which allows for escaping the $(VAR_NAME) syntax: i.e. "$$(VAR_NAME)" will
  // produce the string literal "$(VAR_NAME)". Escaped references will never be expanded, regardless
  // of whether the variable exists or not. Cannot be updated.
  // More info: https://kubernetes.io/docs/tasks/inject-data-application/define-command-argument-container/#running-a-command-in-a-shell
  // +optional
  repeated string command = 3;

  // Arguments to the entrypoint.
  // The image's CMD is used if this is not provided.
  // Variable references $(VAR_NAME) are expanded using the container's environment. If a variable
  // cannot be resolved, the reference in the input string will be unchanged. Double $$ are reduced
  // to a single $, which allows for escaping the $(VAR_NAME) syntax: i.e. "$$(VAR_NAME)" will
  // produce the string literal "$(VAR_NAME)". Escaped references will never be expanded, regardless
  // of whether the variable exists or not. Cannot be updated.
  // More info: https://kubernetes.io/docs/tasks/inject-data-application/define-command-argument-container/#running-a-command-in-a-shell
  // +optional
  repeated string args = 4;

  // Container's working directory.
  // If not specified, the container runtime's default will be used, which
  // might be configured in the container image.
  // Cannot be updated.
  // +optional
  optional string workingDir = 5;

  // Ports are not allowed for ephemeral containers.
  // +optional
  // +patchMergeKey=containerPort
  // +patchStrategy=merge
  // +listType=map
  // +listMapKey=containerPort
  // +listMapKey=protocol
  repeated ContainerPort ports = 6;

  // List of sources to populate environment variables in the container.
  // The keys defined within a source must be a C_IDENTIFIER. All invalid keys
  // will be reported as an event when the container is starting. When a key exists in multiple
  // sources, the value associated with the last source will take precedence.
  // Values defined by an Env with a duplicate key will take precedence.
  // Cannot be updated.
  // +optional
  repeated EnvFromSource envFrom = 19;

  // List of environment variables to set in the container.
  // Cannot be updated.
  // +optional
  // +patchMergeKey=name
  // +patchStrategy=merge
  repeated EnvVar env = 7;

  // Resources are not allowed for ephemeral containers. Ephemeral containers use spare resources
  // already allocated to the pod.
  // +optional
  optional ResourceRequirements resources = 8;

  // Resources resize policy for the container.
  // +featureGate=InPlacePodVerticalScaling
  // +optional
  // +listType=atomic
  repeated ContainerResizePolicy resizePolicy = 23;

  // Restart policy for the container to manage the restart behavior of each
  // container within a pod.
  // This may only be set for init containers. You cannot set this field on
  // ephemeral containers.
  // +featureGate=SidecarContainers
  // +optional
  optional string restartPolicy = 24;

  // Pod volumes to mount into the container's filesystem. Subpath mounts are not allowed for ephemeral containers.
  // Cannot be updated.
  // +optional
  // +patchMergeKey=mountPath
  // +patchStrategy=merge
  repeated VolumeMount volumeMounts = 9;

  // volumeDevices is the list of block devices to be used by the container.
  // +patchMergeKey=devicePath
  // +patchStrategy=merge
  // +optional
  repeated VolumeDevice volumeDevices = 21;

  // Probes are not allowed for ephemeral containers.
  // +optional
  optional Probe livenessProbe = 10;

  // Probes are not allowed for ephemeral containers.
  // +optional
  optional Probe readinessProbe = 11;

  // Probes are not allowed for ephemeral containers.
  // +optional
  optional Probe startupProbe = 22;

  // Lifecycle is not allowed for ephemeral containers.
  // +optional
  optional Lifecycle lifecycle = 12;

  // Optional: Path at which the file to which the container's termination message
  // will be written is mounted into the container's filesystem.
  // Message written is intended to be brief final status, such as an assertion failure message.
  // Will be truncated by the node if greater than 4096 bytes. The total message length across
  // all containers will be limited to 12kb.
  // Defaults to /dev/termination-log.
  // Cannot be updated.
  // +optional
  optional string terminationMessagePath = 13;

  // Indicate how the termination message should be populated. File will use the contents of
  // terminationMessagePath to populate the container status message on both success and failure.
  // FallbackToLogsOnError will use the last chunk of container log output if the termination
  // message file is empty and the container exited with an error.
  // The log output is limited to 2048 bytes or 80 lines, whichever is smaller.
  // Defaults to File.
  // Cannot be updated.
  // +optional
  optional string terminationMessagePolicy = 20;

  // Image pull policy.
  // One of Always, Never, IfNotPresent.
  // Defaults to Always if :latest tag is specified, or IfNotPresent otherwise.
  // Cannot be updated.
  // More info: https://kubernetes.io/docs/concepts/containers/images#updating-images
  // +optional
  optional string imagePullPolicy = 14;

  // Optional: SecurityContext defines the security options the ephemeral container should be run with.
  // If set, the fields of SecurityContext override the equivalent fields of PodSecurityContext.
  // +optional
  optional SecurityContext securityContext = 15;

  // Whether this container should allocate a buffer for stdin in the container runtime. If this
  // is not set, reads from stdin in the container will always result in EOF.
  // Default is false.
  // +optional
  optional bool stdin = 16;

  // Whether the container runtime should close the stdin channel after it has been opened by
  // a single attach. When stdin is true the stdin stream will remain open across multiple attach
  // sessions. If stdinOnce is set to true, stdin is opened on container start, is empty until the
  // first client attaches to stdin, and then remains open and accepts data until the client disconnects,
  // at which time stdin is closed and remains closed until the container is restarted. If this
  // flag is false, a container processes that reads from stdin will never receive an EOF.
  // Default is false
  // +optional
  optional bool stdinOnce = 17;

  // Whether this container should allocate a TTY for itself, also requires 'stdin' to be true.
  // Default is false.
  // +optional
  optional bool tty = 18;
}

// Represents an ephemeral volume that is handled by a normal storage driver.
message EphemeralVolumeSource {
  // Will be used to create a stand-alone PVC to provision the volume.
  // The pod in which this EphemeralVolumeSource is embedded will be the
  // owner of the PVC, i.e. the PVC will be deleted together with the
  // pod.  The name of the PVC will be `<pod name>-<volume name>` where
  // `<volume name>` is the name from the `PodSpec.Volumes` array
  // entry. Pod validation will reject the pod if the concatenated name
  // is not valid for a PVC (for example, too long).
  //
  // An existing PVC with that name that is not owned by the pod
  // will *not* be used for the pod to avoid using an unrelated
  // volume by mistake. Starting the pod is then blocked until
  // the unrelated PVC is removed. If such a pre-created PVC is
  // meant to be used by the pod, the PVC has to updated with an
  // owner reference to the pod once the pod exists. Normally
  // this should not be necessary, but it may be useful when
  // manually reconstructing a broken cluster.
  //
  // This field is read-only and no changes will be made by Kubernetes
  // to the PVC after it has been created.
  //
  // Required, must not be nil.
  optional PersistentVolumeClaimTemplate volumeClaimTemplate = 1;
}

// Event is a report of an event somewhere in the cluster.  Events
// have a limited retention time and triggers and messages may evolve
// with time.  Event consumers should not rely on the timing of an event
// with a given Reason reflecting a consistent underlying trigger, or the
// continued existence of events with that Reason.  Events should be
// treated as informative, best-effort, supplemental data.
message Event {
  // Standard object's metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // The object that this event is about.
  optional ObjectReference involvedObject = 2;

  // This should be a short, machine understandable string that gives the reason
  // for the transition into the object's current status.
  // TODO: provide exact specification for format.
  // +optional
  optional string reason = 3;

  // A human-readable description of the status of this operation.
  // TODO: decide on maximum length.
  // +optional
  optional string message = 4;

  // The component reporting this event. Should be a short machine understandable string.
  // +optional
  optional EventSource source = 5;

  // The time at which the event was first recorded. (Time of server receipt is in TypeMeta.)
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time firstTimestamp = 6;

  // The time at which the most recent occurrence of this event was recorded.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time lastTimestamp = 7;

  // The number of times this event has occurred.
  // +optional
  optional int32 count = 8;

  // Type of this event (Normal, Warning), new types could be added in the future
  // +optional
  optional string type = 9;

  // Time when this Event was first observed.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.MicroTime eventTime = 10;

  // Data about the Event series this event represents or nil if it's a singleton Event.
  // +optional
  optional EventSeries series = 11;

  // What action was taken/failed regarding to the Regarding object.
  // +optional
  optional string action = 12;

  // Optional secondary object for more complex actions.
  // +optional
  optional ObjectReference related = 13;

  // Name of the controller that emitted this Event, e.g. `kubernetes.io/kubelet`.
  // +optional
  optional string reportingComponent = 14;

  // ID of the controller instance, e.g. `kubelet-xyzf`.
  // +optional
  optional string reportingInstance = 15;
}

// EventList is a list of events.
message EventList {
  // Standard list metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // List of events
  repeated Event items = 2;
}

// EventSeries contain information on series of events, i.e. thing that was/is happening
// continuously for some time.
message EventSeries {
  // Number of occurrences in this series up to the last heartbeat time
  optional int32 count = 1;

  // Time of the last occurrence observed
  optional k8s.io.apimachinery.pkg.apis.meta.v1.MicroTime lastObservedTime = 2;
}

// EventSource contains information for an event.
message EventSource {
  // Component from which the event is generated.
  // +optional
  optional string component = 1;

  // Node name on which the event is generated.
  // +optional
  optional string host = 2;
}

// ExecAction describes a "run in container" action.
message ExecAction {
  // Command is the command line to execute inside the container, the working directory for the
  // command  is root ('/') in the container's filesystem. The command is simply exec'd, it is
  // not run inside a shell, so traditional shell instructions ('|', etc) won't work. To use
  // a shell, you need to explicitly call out to that shell.
  // Exit status of 0 is treated as live/healthy and non-zero is unhealthy.
  // +optional
  repeated string command = 1;
}

// Represents a Fibre Channel volume.
// Fibre Channel volumes can only be mounted as read/write once.
// Fibre Channel volumes support ownership management and SELinux relabeling.
message FCVolumeSource {
  // targetWWNs is Optional: FC target worldwide names (WWNs)
  // +optional
  repeated string targetWWNs = 1;

  // lun is Optional: FC target lun number
  // +optional
  optional int32 lun = 2;

  // fsType is the filesystem type to mount.
  // Must be a filesystem type supported by the host operating system.
  // Ex. "ext4", "xfs", "ntfs". Implicitly inferred to be "ext4" if unspecified.
  // TODO: how do we prevent errors in the filesystem from compromising the machine
  // +optional
  optional string fsType = 3;

  // readOnly is Optional: Defaults to false (read/write). ReadOnly here will force
  // the ReadOnly setting in VolumeMounts.
  // +optional
  optional bool readOnly = 4;

  // wwids Optional: FC volume world wide identifiers (wwids)
  // Either wwids or combination of targetWWNs and lun must be set, but not both simultaneously.
  // +optional
  repeated string wwids = 5;
}

// FlexPersistentVolumeSource represents a generic persistent volume resource that is
// provisioned/attached using an exec based plugin.
message FlexPersistentVolumeSource {
  // driver is the name of the driver to use for this volume.
  optional string driver = 1;

  // fsType is the Filesystem type to mount.
  // Must be a filesystem type supported by the host operating system.
  // Ex. "ext4", "xfs", "ntfs". The default filesystem depends on FlexVolume script.
  // +optional
  optional string fsType = 2;

  // secretRef is Optional: SecretRef is reference to the secret object containing
  // sensitive information to pass to the plugin scripts. This may be
  // empty if no secret object is specified. If the secret object
  // contains more than one secret, all secrets are passed to the plugin
  // scripts.
  // +optional
  optional SecretReference secretRef = 3;

  // readOnly is Optional: defaults to false (read/write). ReadOnly here will force
  // the ReadOnly setting in VolumeMounts.
  // +optional
  optional bool readOnly = 4;

  // options is Optional: this field holds extra command options if any.
  // +optional
  map<string, string> options = 5;
}

// FlexVolume represents a generic volume resource that is
// provisioned/attached using an exec based plugin.
message FlexVolumeSource {
  // driver is the name of the driver to use for this volume.
  optional string driver = 1;

  // fsType is the filesystem type to mount.
  // Must be a filesystem type supported by the host operating system.
  // Ex. "ext4", "xfs", "ntfs". The default filesystem depends on FlexVolume script.
  // +optional
  optional string fsType = 2;

  // secretRef is Optional: secretRef is reference to the secret object containing
  // sensitive information to pass to the plugin scripts. This may be
  // empty if no secret object is specified. If the secret object
  // contains more than one secret, all secrets are passed to the plugin
  // scripts.
  // +optional
  optional LocalObjectReference secretRef = 3;

  // readOnly is Optional: defaults to false (read/write). ReadOnly here will force
  // the ReadOnly setting in VolumeMounts.
  // +optional
  optional bool readOnly = 4;

  // options is Optional: this field holds extra command options if any.
  // +optional
  map<string, string> options = 5;
}

// Represents a Flocker volume mounted by the Flocker agent.
// One and only one of datasetName and datasetUUID should be set.
// Flocker volumes do not support ownership management or SELinux relabeling.
message FlockerVolumeSource {
  // datasetName is Name of the dataset stored as metadata -> name on the dataset for Flocker
  // should be considered as deprecated
  // +optional
  optional string datasetName = 1;

  // datasetUUID is the UUID of the dataset. This is unique identifier of a Flocker dataset
  // +optional
  optional string datasetUUID = 2;
}

// Represents a Persistent Disk resource in Google Compute Engine.
//
// A GCE PD must exist before mounting to a container. The disk must
// also be in the same GCE project and zone as the kubelet. A GCE PD
// can only be mounted as read/write once or read-only many times. GCE
// PDs support ownership management and SELinux relabeling.
message GCEPersistentDiskVolumeSource {
  // pdName is unique name of the PD resource in GCE. Used to identify the disk in GCE.
  // More info: https://kubernetes.io/docs/concepts/storage/volumes#gcepersistentdisk
  optional string pdName = 1;

  // fsType is filesystem type of the volume that you want to mount.
  // Tip: Ensure that the filesystem type is supported by the host operating system.
  // Examples: "ext4", "xfs", "ntfs". Implicitly inferred to be "ext4" if unspecified.
  // More info: https://kubernetes.io/docs/concepts/storage/volumes#gcepersistentdisk
  // TODO: how do we prevent errors in the filesystem from compromising the machine
  // +optional
  optional string fsType = 2;

  // partition is the partition in the volume that you want to mount.
  // If omitted, the default is to mount by volume name.
  // Examples: For volume /dev/sda1, you specify the partition as "1".
  // Similarly, the volume partition for /dev/sda is "0" (or you can leave the property empty).
  // More info: https://kubernetes.io/docs/concepts/storage/volumes#gcepersistentdisk
  // +optional
  optional int32 partition = 3;

  // readOnly here will force the ReadOnly setting in VolumeMounts.
  // Defaults to false.
  // More info: https://kubernetes.io/docs/concepts/storage/volumes#gcepersistentdisk
  // +optional
  optional bool readOnly = 4;
}

message GRPCAction {
  // Port number of the gRPC service. Number must be in the range 1 to 65535.
  optional int32 port = 1;

  // Service is the name of the service to place in the gRPC HealthCheckRequest
  // (see https://github.com/grpc/grpc/blob/master/doc/health-checking.md).
  //
  // If this is not specified, the default behavior is defined by gRPC.
  // +optional
  // +default=""
  optional string service = 2;
}

// Represents a volume that is populated with the contents of a git repository.
// Git repo volumes do not support ownership management.
// Git repo volumes support SELinux relabeling.
//
// DEPRECATED: GitRepo is deprecated. To provision a container with a git repo, mount an
// EmptyDir into an InitContainer that clones the repo using git, then mount the EmptyDir
// into the Pod's container.
message GitRepoVolumeSource {
  // repository is the URL
  optional string repository = 1;

  // revision is the commit hash for the specified revision.
  // +optional
  optional string revision = 2;

  // directory is the target directory name.
  // Must not contain or start with '..'.  If '.' is supplied, the volume directory will be the
  // git repository.  Otherwise, if specified, the volume will contain the git repository in
  // the subdirectory with the given name.
  // +optional
  optional string directory = 3;
}

// Represents a Glusterfs mount that lasts the lifetime of a pod.
// Glusterfs volumes do not support ownership management or SELinux relabeling.
message GlusterfsPersistentVolumeSource {
  // endpoints is the endpoint name that details Glusterfs topology.
  // More info: https://examples.k8s.io/volumes/glusterfs/README.md#create-a-pod
  optional string endpoints = 1;

  // path is the Glusterfs volume path.
  // More info: https://examples.k8s.io/volumes/glusterfs/README.md#create-a-pod
  optional string path = 2;

  // readOnly here will force the Glusterfs volume to be mounted with read-only permissions.
  // Defaults to false.
  // More info: https://examples.k8s.io/volumes/glusterfs/README.md#create-a-pod
  // +optional
  optional bool readOnly = 3;

  // endpointsNamespace is the namespace that contains Glusterfs endpoint.
  // If this field is empty, the EndpointNamespace defaults to the same namespace as the bound PVC.
  // More info: https://examples.k8s.io/volumes/glusterfs/README.md#create-a-pod
  // +optional
  optional string endpointsNamespace = 4;
}

// Represents a Glusterfs mount that lasts the lifetime of a pod.
// Glusterfs volumes do not support ownership management or SELinux relabeling.
message GlusterfsVolumeSource {
  // endpoints is the endpoint name that details Glusterfs topology.
  // More info: https://examples.k8s.io/volumes/glusterfs/README.md#create-a-pod
  optional string endpoints = 1;

  // path is the Glusterfs volume path.
  // More info: https://examples.k8s.io/volumes/glusterfs/README.md#create-a-pod
  optional string path = 2;

  // readOnly here will force the Glusterfs volume to be mounted with read-only permissions.
  // Defaults to false.
  // More info: https://examples.k8s.io/volumes/glusterfs/README.md#create-a-pod
  // +optional
  optional bool readOnly = 3;
}

// HTTPGetAction describes an action based on HTTP Get requests.
message HTTPGetAction {
  // Path to access on the HTTP server.
  // +optional
  optional string path = 1;

  // Name or number of the port to access on the container.
  // Number must be in the range 1 to 65535.
  // Name must be an IANA_SVC_NAME.
  optional k8s.io.apimachinery.pkg.util.intstr.IntOrString port = 2;

  // Host name to connect to, defaults to the pod IP. You probably want to set
  // "Host" in httpHeaders instead.
  // +optional
  optional string host = 3;

  // Scheme to use for connecting to the host.
  // Defaults to HTTP.
  // +optional
  optional string scheme = 4;

  // Custom headers to set in the request. HTTP allows repeated headers.
  // +optional
  repeated HTTPHeader httpHeaders = 5;
}

// HTTPHeader describes a custom header to be used in HTTP probes
message HTTPHeader {
  // The header field name.
  // This will be canonicalized upon output, so case-variant names will be understood as the same header.
  optional string name = 1;

  // The header field value
  optional string value = 2;
}

// HostAlias holds the mapping between IP and hostnames that will be injected as an entry in the
// pod's hosts file.
message HostAlias {
  // IP address of the host file entry.
  optional string ip = 1;

  // Hostnames for the above IP address.
  repeated string hostnames = 2;
}

// HostIP represents a single IP address allocated to the host.
message HostIP {
  // IP is the IP address assigned to the host
  optional string ip = 1;
}

// Represents a host path mapped into a pod.
// Host path volumes do not support ownership management or SELinux relabeling.
message HostPathVolumeSource {
  // path of the directory on the host.
  // If the path is a symlink, it will follow the link to the real path.
  // More info: https://kubernetes.io/docs/concepts/storage/volumes#hostpath
  optional string path = 1;

  // type for HostPath Volume
  // Defaults to ""
  // More info: https://kubernetes.io/docs/concepts/storage/volumes#hostpath
  // +optional
  optional string type = 2;
}

// ISCSIPersistentVolumeSource represents an ISCSI disk.
// ISCSI volumes can only be mounted as read/write once.
// ISCSI volumes support ownership management and SELinux relabeling.
message ISCSIPersistentVolumeSource {
  // targetPortal is iSCSI Target Portal. The Portal is either an IP or ip_addr:port if the port
  // is other than default (typically TCP ports 860 and 3260).
  optional string targetPortal = 1;

  // iqn is Target iSCSI Qualified Name.
  optional string iqn = 2;

  // lun is iSCSI Target Lun number.
  optional int32 lun = 3;

  // iscsiInterface is the interface Name that uses an iSCSI transport.
  // Defaults to 'default' (tcp).
  // +optional
  optional string iscsiInterface = 4;

  // fsType is the filesystem type of the volume that you want to mount.
  // Tip: Ensure that the filesystem type is supported by the host operating system.
  // Examples: "ext4", "xfs", "ntfs". Implicitly inferred to be "ext4" if unspecified.
  // More info: https://kubernetes.io/docs/concepts/storage/volumes#iscsi
  // TODO: how do we prevent errors in the filesystem from compromising the machine
  // +optional
  optional string fsType = 5;

  // readOnly here will force the ReadOnly setting in VolumeMounts.
  // Defaults to false.
  // +optional
  optional bool readOnly = 6;

  // portals is the iSCSI Target Portal List. The Portal is either an IP or ip_addr:port if the port
  // is other than default (typically TCP ports 860 and 3260).
  // +optional
  repeated string portals = 7;

  // chapAuthDiscovery defines whether support iSCSI Discovery CHAP authentication
  // +optional
  optional bool chapAuthDiscovery = 8;

  // chapAuthSession defines whether support iSCSI Session CHAP authentication
  // +optional
  optional bool chapAuthSession = 11;

  // secretRef is the CHAP Secret for iSCSI target and initiator authentication
  // +optional
  optional SecretReference secretRef = 10;

  // initiatorName is the custom iSCSI Initiator Name.
  // If initiatorName is specified with iscsiInterface simultaneously, new iSCSI interface
  // <target portal>:<volume name> will be created for the connection.
  // +optional
  optional string initiatorName = 12;
}

// Represents an ISCSI disk.
// ISCSI volumes can only be mounted as read/write once.
// ISCSI volumes support ownership management and SELinux relabeling.
message ISCSIVolumeSource {
  // targetPortal is iSCSI Target Portal. The Portal is either an IP or ip_addr:port if the port
  // is other than default (typically TCP ports 860 and 3260).
  optional string targetPortal = 1;

  // iqn is the target iSCSI Qualified Name.
  optional string iqn = 2;

  // lun represents iSCSI Target Lun number.
  optional int32 lun = 3;

  // iscsiInterface is the interface Name that uses an iSCSI transport.
  // Defaults to 'default' (tcp).
  // +optional
  optional string iscsiInterface = 4;

  // fsType is the filesystem type of the volume that you want to mount.
  // Tip: Ensure that the filesystem type is supported by the host operating system.
  // Examples: "ext4", "xfs", "ntfs". Implicitly inferred to be "ext4" if unspecified.
  // More info: https://kubernetes.io/docs/concepts/storage/volumes#iscsi
  // TODO: how do we prevent errors in the filesystem from compromising the machine
  // +optional
  optional string fsType = 5;

  // readOnly here will force the ReadOnly setting in VolumeMounts.
  // Defaults to false.
  // +optional
  optional bool readOnly = 6;

  // portals is the iSCSI Target Portal List. The portal is either an IP or ip_addr:port if the port
  // is other than default (typically TCP ports 860 and 3260).
  // +optional
  repeated string portals = 7;

  // chapAuthDiscovery defines whether support iSCSI Discovery CHAP authentication
  // +optional
  optional bool chapAuthDiscovery = 8;

  // chapAuthSession defines whether support iSCSI Session CHAP authentication
  // +optional
  optional bool chapAuthSession = 11;

  // secretRef is the CHAP Secret for iSCSI target and initiator authentication
  // +optional
  optional LocalObjectReference secretRef = 10;

  // initiatorName is the custom iSCSI Initiator Name.
  // If initiatorName is specified with iscsiInterface simultaneously, new iSCSI interface
  // <target portal>:<volume name> will be created for the connection.
  // +optional
  optional string initiatorName = 12;
}

// Maps a string key to a path within a volume.
message KeyToPath {
  // key is the key to project.
  optional string key = 1;

  // path is the relative path of the file to map the key to.
  // May not be an absolute path.
  // May not contain the path element '..'.
  // May not start with the string '..'.
  optional string path = 2;

  // mode is Optional: mode bits used to set permissions on this file.
  // Must be an octal value between 0000 and 0777 or a decimal value between 0 and 511.
  // YAML accepts both octal and decimal values, JSON requires decimal values for mode bits.
  // If not specified, the volume defaultMode will be used.
  // This might be in conflict with other options that affect the file
  // mode, like fsGroup, and the result can be other mode bits set.
  // +optional
  optional int32 mode = 3;
}

// Lifecycle describes actions that the management system should take in response to container lifecycle
// events. For the PostStart and PreStop lifecycle handlers, management of the container blocks
// until the action is complete, unless the container process fails, in which case the handler is aborted.
message Lifecycle {
  // PostStart is called immediately after a container is created. If the handler fails,
  // the container is terminated and restarted according to its restart policy.
  // Other management of the container blocks until the hook completes.
  // More info: https://kubernetes.io/docs/concepts/containers/container-lifecycle-hooks/#container-hooks
  // +optional
  optional LifecycleHandler postStart = 1;

  // PreStop is called immediately before a container is terminated due to an
  // API request or management event such as liveness/startup probe failure,
  // preemption, resource contention, etc. The handler is not called if the
  // container crashes or exits. The Pod's termination grace period countdown begins before the
  // PreStop hook is executed. Regardless of the outcome of the handler, the
  // container will eventually terminate within the Pod's termination grace
  // period (unless delayed by finalizers). Other management of the container blocks until the hook completes
  // or until the termination grace period is reached.
  // More info: https://kubernetes.io/docs/concepts/containers/container-lifecycle-hooks/#container-hooks
  // +optional
  optional LifecycleHandler preStop = 2;
}

// LifecycleHandler defines a specific action that should be taken in a lifecycle
// hook. One and only one of the fields, except TCPSocket must be specified.
message LifecycleHandler {
  // Exec specifies the action to take.
  // +optional
  optional ExecAction exec = 1;

  // HTTPGet specifies the http request to perform.
  // +optional
  optional HTTPGetAction httpGet = 2;

  // Deprecated. TCPSocket is NOT supported as a LifecycleHandler and kept
  // for the backward compatibility. There are no validation of this field and
  // lifecycle hooks will fail in runtime when tcp handler is specified.
  // +optional
  optional TCPSocketAction tcpSocket = 3;
}

// LimitRange sets resource usage limits for each kind of resource in a Namespace.
message LimitRange {
  // Standard object's metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Spec defines the limits enforced.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status
  // +optional
  optional LimitRangeSpec spec = 2;
}

// LimitRangeItem defines a min/max usage limit for any resource that matches on kind.
message LimitRangeItem {
  // Type of resource that this limit applies to.
  optional string type = 1;

  // Max usage constraints on this kind by resource name.
  // +optional
  map<string, k8s.io.apimachinery.pkg.api.resource.Quantity> max = 2;

  // Min usage constraints on this kind by resource name.
  // +optional
  map<string, k8s.io.apimachinery.pkg.api.resource.Quantity> min = 3;

  // Default resource requirement limit value by resource name if resource limit is omitted.
  // +optional
  map<string, k8s.io.apimachinery.pkg.api.resource.Quantity> default = 4;

  // DefaultRequest is the default resource requirement request value by resource name if resource request is omitted.
  // +optional
  map<string, k8s.io.apimachinery.pkg.api.resource.Quantity> defaultRequest = 5;

  // MaxLimitRequestRatio if specified, the named resource must have a request and limit that are both non-zero where limit divided by request is less than or equal to the enumerated value; this represents the max burst for the named resource.
  // +optional
  map<string, k8s.io.apimachinery.pkg.api.resource.Quantity> maxLimitRequestRatio = 6;
}

// LimitRangeList is a list of LimitRange items.
message LimitRangeList {
  // Standard list metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // Items is a list of LimitRange objects.
  // More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/
  repeated LimitRange items = 2;
}

// LimitRangeSpec defines a min/max usage limit for resources that match on kind.
message LimitRangeSpec {
  // Limits is the list of LimitRangeItem objects that are enforced.
  repeated LimitRangeItem limits = 1;
}

// List holds a list of objects, which may not be known by the server.
message List {
  // Standard list metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // List of objects
  repeated k8s.io.apimachinery.pkg.runtime.RawExtension items = 2;
}

// LoadBalancerIngress represents the status of a load-balancer ingress point:
// traffic intended for the service should be sent to an ingress point.
message LoadBalancerIngress {
  // IP is set for load-balancer ingress points that are IP based
  // (typically GCE or OpenStack load-balancers)
  // +optional
  optional string ip = 1;

  // Hostname is set for load-balancer ingress points that are DNS based
  // (typically AWS load-balancers)
  // +optional
  optional string hostname = 2;

  // Ports is a list of records of service ports
  // If used, every port defined in the service should have an entry in it
  // +listType=atomic
  // +optional
  repeated PortStatus ports = 4;
}

// LoadBalancerStatus represents the status of a load-balancer.
message LoadBalancerStatus {
  // Ingress is a list containing ingress points for the load-balancer.
  // Traffic intended for the service should be sent to these ingress points.
  // +optional
  repeated LoadBalancerIngress ingress = 1;
}

// LocalObjectReference contains enough information to let you locate the
// referenced object inside the same namespace.
// +structType=atomic
message LocalObjectReference {
  // Name of the referent.
  // More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
  // TODO: Add other useful fields. apiVersion, kind, uid?
  // +optional
  optional string name = 1;
}

// Local represents directly-attached storage with node affinity (Beta feature)
message LocalVolumeSource {
  // path of the full path to the volume on the node.
  // It can be either a directory or block device (disk, partition, ...).
  optional string path = 1;

  // fsType is the filesystem type to mount.
  // It applies only when the Path is a block device.
  // Must be a filesystem type supported by the host operating system.
  // Ex. "ext4", "xfs", "ntfs". The default value is to auto-select a filesystem if unspecified.
  // +optional
  optional string fsType = 2;
}

// Represents an NFS mount that lasts the lifetime of a pod.
// NFS volumes do not support ownership management or SELinux relabeling.
message NFSVolumeSource {
  // server is the hostname or IP address of the NFS server.
  // More info: https://kubernetes.io/docs/concepts/storage/volumes#nfs
  optional string server = 1;

  // path that is exported by the NFS server.
  // More info: https://kubernetes.io/docs/concepts/storage/volumes#nfs
  optional string path = 2;

  // readOnly here will force the NFS export to be mounted with read-only permissions.
  // Defaults to false.
  // More info: https://kubernetes.io/docs/concepts/storage/volumes#nfs
  // +optional
  optional bool readOnly = 3;
}

// Namespace provides a scope for Names.
// Use of multiple namespaces is optional.
message Namespace {
  // Standard object's metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Spec defines the behavior of the Namespace.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status
  // +optional
  optional NamespaceSpec spec = 2;

  // Status describes the current status of a Namespace.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status
  // +optional
  optional NamespaceStatus status = 3;
}

// NamespaceCondition contains details about state of namespace.
message NamespaceCondition {
  // Type of namespace controller condition.
  optional string type = 1;

  // Status of the condition, one of True, False, Unknown.
  optional string status = 2;

  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time lastTransitionTime = 4;

  // +optional
  optional string reason = 5;

  // +optional
  optional string message = 6;
}

// NamespaceList is a list of Namespaces.
message NamespaceList {
  // Standard list metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // Items is the list of Namespace objects in the list.
  // More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/namespaces/
  repeated Namespace items = 2;
}

// NamespaceSpec describes the attributes on a Namespace.
message NamespaceSpec {
  // Finalizers is an opaque list of values that must be empty to permanently remove object from storage.
  // More info: https://kubernetes.io/docs/tasks/administer-cluster/namespaces/
  // +optional
  repeated string finalizers = 1;
}

// NamespaceStatus is information about the current status of a Namespace.
message NamespaceStatus {
  // Phase is the current lifecycle phase of the namespace.
  // More info: https://kubernetes.io/docs/tasks/administer-cluster/namespaces/
  // +optional
  optional string phase = 1;

  // Represents the latest available observations of a namespace's current state.
  // +optional
  // +patchMergeKey=type
  // +patchStrategy=merge
  repeated NamespaceCondition conditions = 2;
}

// Node is a worker node in Kubernetes.
// Each node will have a unique identifier in the cache (i.e. in etcd).
message Node {
  // Standard object's metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Spec defines the behavior of a node.
  // https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status
  // +optional
  optional NodeSpec spec = 2;

  // Most recently observed status of the node.
  // Populated by the system.
  // Read-only.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status
  // +optional
  optional NodeStatus status = 3;
}

// NodeAddress contains information for the node's address.
message NodeAddress {
  // Node address type, one of Hostname, ExternalIP or InternalIP.
  optional string type = 1;

  // The node address.
  optional string address = 2;
}

// Node affinity is a group of node affinity scheduling rules.
message NodeAffinity {
  // If the affinity requirements specified by this field are not met at
  // scheduling time, the pod will not be scheduled onto the node.
  // If the affinity requirements specified by this field cease to be met
  // at some point during pod execution (e.g. due to an update), the system
  // may or may not try to eventually evict the pod from its node.
  // +optional
  optional NodeSelector requiredDuringSchedulingIgnoredDuringExecution = 1;

  // The scheduler will prefer to schedule pods to nodes that satisfy
  // the affinity expressions specified by this field, but it may choose
  // a node that violates one or more of the expressions. The node that is
  // most preferred is the one with the greatest sum of weights, i.e.
  // for each node that meets all of the scheduling requirements (resource
  // request, requiredDuringScheduling affinity expressions, etc.),
  // compute a sum by iterating through the elements of this field and adding
  // "weight" to the sum if the node matches the corresponding matchExpressions; the
  // node(s) with the highest sum are the most preferred.
  // +optional
  repeated PreferredSchedulingTerm preferredDuringSchedulingIgnoredDuringExecution = 2;
}

// NodeCondition contains condition information for a node.
message NodeCondition {
  // Type of node condition.
  optional string type = 1;

  // Status of the condition, one of True, False, Unknown.
  optional string status = 2;

  // Last time we got an update on a given condition.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time lastHeartbeatTime = 3;

  // Last time the condition transit from one status to another.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time lastTransitionTime = 4;

  // (brief) reason for the condition's last transition.
  // +optional
  optional string reason = 5;

  // Human readable message indicating details about last transition.
  // +optional
  optional string message = 6;
}

// NodeConfigSource specifies a source of node configuration. Exactly one subfield (excluding metadata) must be non-nil.
// This API is deprecated since 1.22
message NodeConfigSource {
  // ConfigMap is a reference to a Node's ConfigMap
  optional ConfigMapNodeConfigSource configMap = 2;
}

// NodeConfigStatus describes the status of the config assigned by Node.Spec.ConfigSource.
message NodeConfigStatus {
  // Assigned reports the checkpointed config the node will try to use.
  // When Node.Spec.ConfigSource is updated, the node checkpoints the associated
  // config payload to local disk, along with a record indicating intended
  // config. The node refers to this record to choose its config checkpoint, and
  // reports this record in Assigned. Assigned only updates in the status after
  // the record has been checkpointed to disk. When the Kubelet is restarted,
  // it tries to make the Assigned config the Active config by loading and
  // validating the checkpointed payload identified by Assigned.
  // +optional
  optional NodeConfigSource assigned = 1;

  // Active reports the checkpointed config the node is actively using.
  // Active will represent either the current version of the Assigned config,
  // or the current LastKnownGood config, depending on whether attempting to use the
  // Assigned config results in an error.
  // +optional
  optional NodeConfigSource active = 2;

  // LastKnownGood reports the checkpointed config the node will fall back to
  // when it encounters an error attempting to use the Assigned config.
  // The Assigned config becomes the LastKnownGood config when the node determines
  // that the Assigned config is stable and correct.
  // This is currently implemented as a 10-minute soak period starting when the local
  // record of Assigned config is updated. If the Assigned config is Active at the end
  // of this period, it becomes the LastKnownGood. Note that if Spec.ConfigSource is
  // reset to nil (use local defaults), the LastKnownGood is also immediately reset to nil,
  // because the local default config is always assumed good.
  // You should not make assumptions about the node's method of determining config stability
  // and correctness, as this may change or become configurable in the future.
  // +optional
  optional NodeConfigSource lastKnownGood = 3;

  // Error describes any problems reconciling the Spec.ConfigSource to the Active config.
  // Errors may occur, for example, attempting to checkpoint Spec.ConfigSource to the local Assigned
  // record, attempting to checkpoint the payload associated with Spec.ConfigSource, attempting
  // to load or validate the Assigned config, etc.
  // Errors may occur at different points while syncing config. Earlier errors (e.g. download or
  // checkpointing errors) will not result in a rollback to LastKnownGood, and may resolve across
  // Kubelet retries. Later errors (e.g. loading or validating a checkpointed config) will result in
  // a rollback to LastKnownGood. In the latter case, it is usually possible to resolve the error
  // by fixing the config assigned in Spec.ConfigSource.
  // You can find additional information for debugging by searching the error message in the Kubelet log.
  // Error is a human-readable description of the error state; machines can check whether or not Error
  // is empty, but should not rely on the stability of the Error text across Kubelet versions.
  // +optional
  optional string error = 4;
}

// NodeDaemonEndpoints lists ports opened by daemons running on the Node.
message NodeDaemonEndpoints {
  // Endpoint on which Kubelet is listening.
  // +optional
  optional DaemonEndpoint kubeletEndpoint = 1;
}

// NodeList is the whole list of all Nodes which have been registered with master.
message NodeList {
  // Standard list metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // List of nodes
  repeated Node items = 2;
}

// NodeProxyOptions is the query options to a Node's proxy call.
message NodeProxyOptions {
  // Path is the URL path to use for the current proxy request to node.
  // +optional
  optional string path = 1;
}

// NodeResources is an object for conveying resource information about a node.
// see https://kubernetes.io/docs/concepts/architecture/nodes/#capacity for more details.
message NodeResources {
  // Capacity represents the available resources of a node
  map<string, k8s.io.apimachinery.pkg.api.resource.Quantity> capacity = 1;
}

// A node selector represents the union of the results of one or more label queries
// over a set of nodes; that is, it represents the OR of the selectors represented
// by the node selector terms.
// +structType=atomic
message NodeSelector {
  // Required. A list of node selector terms. The terms are ORed.
  repeated NodeSelectorTerm nodeSelectorTerms = 1;
}

// A node selector requirement is a selector that contains values, a key, and an operator
// that relates the key and values.
message NodeSelectorRequirement {
  // The label key that the selector applies to.
  optional string key = 1;

  // Represents a key's relationship to a set of values.
  // Valid operators are In, NotIn, Exists, DoesNotExist. Gt, and Lt.
  optional string operator = 2;

  // An array of string values. If the operator is In or NotIn,
  // the values array must be non-empty. If the operator is Exists or DoesNotExist,
  // the values array must be empty. If the operator is Gt or Lt, the values
  // array must have a single element, which will be interpreted as an integer.
  // This array is replaced during a strategic merge patch.
  // +optional
  repeated string values = 3;
}

// A null or empty node selector term matches no objects. The requirements of
// them are ANDed.
// The TopologySelectorTerm type implements a subset of the NodeSelectorTerm.
// +structType=atomic
message NodeSelectorTerm {
  // A list of node selector requirements by node's labels.
  // +optional
  repeated NodeSelectorRequirement matchExpressions = 1;

  // A list of node selector requirements by node's fields.
  // +optional
  repeated NodeSelectorRequirement matchFields = 2;
}

// NodeSpec describes the attributes that a node is created with.
message NodeSpec {
  // PodCIDR represents the pod IP range assigned to the node.
  // +optional
  optional string podCIDR = 1;

  // podCIDRs represents the IP ranges assigned to the node for usage by Pods on that node. If this
  // field is specified, the 0th entry must match the podCIDR field. It may contain at most 1 value for
  // each of IPv4 and IPv6.
  // +optional
  // +patchStrategy=merge
  repeated string podCIDRs = 7;

  // ID of the node assigned by the cloud provider in the format: <ProviderName>://<ProviderSpecificNodeID>
  // +optional
  optional string providerID = 3;

  // Unschedulable controls node schedulability of new pods. By default, node is schedulable.
  // More info: https://kubernetes.io/docs/concepts/nodes/node/#manual-node-administration
  // +optional
  optional bool unschedulable = 4;

  // If specified, the node's taints.
  // +optional
  repeated Taint taints = 5;

  // Deprecated: Previously used to specify the source of the node's configuration for the DynamicKubeletConfig feature. This feature is removed.
  // +optional
  optional NodeConfigSource configSource = 6;

  // Deprecated. Not all kubelets will set this field. Remove field after 1.13.
  // see: https://issues.k8s.io/61966
  // +optional
  optional string externalID = 2;
}

// NodeStatus is information about the current status of a node.
message NodeStatus {
  // Capacity represents the total resources of a node.
  // More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#capacity
  // +optional
  map<string, k8s.io.apimachinery.pkg.api.resource.Quantity> capacity = 1;

  // Allocatable represents the resources of a node that are available for scheduling.
  // Defaults to Capacity.
  // +optional
  map<string, k8s.io.apimachinery.pkg.api.resource.Quantity> allocatable = 2;

  // NodePhase is the recently observed lifecycle phase of the node.
  // More info: https://kubernetes.io/docs/concepts/nodes/node/#phase
  // The field is never populated, and now is deprecated.
  // +optional
  optional string phase = 3;

  // Conditions is an array of current observed node conditions.
  // More info: https://kubernetes.io/docs/concepts/nodes/node/#condition
  // +optional
  // +patchMergeKey=type
  // +patchStrategy=merge
  repeated NodeCondition conditions = 4;

  // List of addresses reachable to the node.
  // Queried from cloud provider, if available.
  // More info: https://kubernetes.io/docs/concepts/nodes/node/#addresses
  // Note: This field is declared as mergeable, but the merge key is not sufficiently
  // unique, which can cause data corruption when it is merged. Callers should instead
  // use a full-replacement patch. See https://pr.k8s.io/79391 for an example.
  // Consumers should assume that addresses can change during the
  // lifetime of a Node. However, there are some exceptions where this may not
  // be possible, such as Pods that inherit a Node's address in its own status or
  // consumers of the downward API (status.hostIP).
  // +optional
  // +patchMergeKey=type
  // +patchStrategy=merge
  repeated NodeAddress addresses = 5;

  // Endpoints of daemons running on the Node.
  // +optional
  optional NodeDaemonEndpoints daemonEndpoints = 6;

  // Set of ids/uuids to uniquely identify the node.
  // More info: https://kubernetes.io/docs/concepts/nodes/node/#info
  // +optional
  optional NodeSystemInfo nodeInfo = 7;

  // List of container images on this node
  // +optional
  repeated ContainerImage images = 8;

  // List of attachable volumes in use (mounted) by the node.
  // +optional
  repeated string volumesInUse = 9;

  // List of volumes that are attached to the node.
  // +optional
  repeated AttachedVolume volumesAttached = 10;

  // Status of the config assigned to the node via the dynamic Kubelet config feature.
  // +optional
  optional NodeConfigStatus config = 11;
}

// NodeSystemInfo is a set of ids/uuids to uniquely identify the node.
message NodeSystemInfo {
  // MachineID reported by the node. For unique machine identification
  // in the cluster this field is preferred. Learn more from man(5)
  // machine-id: http://man7.org/linux/man-pages/man5/machine-id.5.html
  optional string machineID = 1;

  // SystemUUID reported by the node. For unique machine identification
  // MachineID is preferred. This field is specific to Red Hat hosts
  // https://access.redhat.com/documentation/en-us/red_hat_subscription_management/1/html/rhsm/uuid
  optional string systemUUID = 2;

  // Boot ID reported by the node.
  optional string bootID = 3;

  // Kernel Version reported by the node from 'uname -r' (e.g. 3.16.0-0.bpo.4-amd64).
  optional string kernelVersion = 4;

  // OS Image reported by the node from /etc/os-release (e.g. Debian GNU/Linux 7 (wheezy)).
  optional string osImage = 5;

  // ContainerRuntime Version reported by the node through runtime remote API (e.g. containerd://1.4.2).
  optional string containerRuntimeVersion = 6;

  // Kubelet Version reported by the node.
  optional string kubeletVersion = 7;

  // KubeProxy Version reported by the node.
  optional string kubeProxyVersion = 8;

  // The Operating System reported by the node
  optional string operatingSystem = 9;

  // The Architecture reported by the node
  optional string architecture = 10;
}

// ObjectFieldSelector selects an APIVersioned field of an object.
// +structType=atomic
message ObjectFieldSelector {
  // Version of the schema the FieldPath is written in terms of, defaults to "v1".
  // +optional
  optional string apiVersion = 1;

  // Path of the field to select in the specified API version.
  optional string fieldPath = 2;
}

// ObjectReference contains enough information to let you inspect or modify the referred object.
// ---
// New uses of this type are discouraged because of difficulty describing its usage when embedded in APIs.
//  1. Ignored fields.  It includes many fields which are not generally honored.  For instance, ResourceVersion and FieldPath are both very rarely valid in actual usage.
//  2. Invalid usage help.  It is impossible to add specific help for individual usage.  In most embedded usages, there are particular
//     restrictions like, "must refer only to types A and B" or "UID not honored" or "name must be restricted".
//     Those cannot be well described when embedded.
//  3. Inconsistent validation.  Because the usages are different, the validation rules are different by usage, which makes it hard for users to predict what will happen.
//  4. The fields are both imprecise and overly precise.  Kind is not a precise mapping to a URL. This can produce ambiguity
//     during interpretation and require a REST mapping.  In most cases, the dependency is on the group,resource tuple
//     and the version of the actual struct is irrelevant.
//  5. We cannot easily change it.  Because this type is embedded in many locations, updates to this type
//     will affect numerous schemas.  Don't make new APIs embed an underspecified API type they do not control.
//
// Instead of using this type, create a locally provided and used type that is well-focused on your reference.
// For example, ServiceReferences for admission registration: https://github.com/kubernetes/api/blob/release-1.17/admissionregistration/v1/types.go#L533 .
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object
// +structType=atomic
message ObjectReference {
  // Kind of the referent.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
  // +optional
  optional string kind = 1;

  // Namespace of the referent.
  // More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/namespaces/
  // +optional
  optional string namespace = 2;

  // Name of the referent.
  // More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
  // +optional
  optional string name = 3;

  // UID of the referent.
  // More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#uids
  // +optional
  optional string uid = 4;

  // API version of the referent.
  // +optional
  optional string apiVersion = 5;

  // Specific resourceVersion to which this reference is made, if any.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#concurrency-control-and-consistency
  // +optional
  optional string resourceVersion = 6;

  // If referring to a piece of an object instead of an entire object, this string
  // should contain a valid JSON/Go field access statement, such as desiredState.manifest.containers[2].
  // For example, if the object reference is to a container within a pod, this would take on a value like:
  // "spec.containers{name}" (where "name" refers to the name of the container that triggered
  // the event) or if no container name is specified "spec.containers[2]" (container with
  // index 2 in this pod). This syntax is chosen only to have some well-defined way of
  // referencing a part of an object.
  // TODO: this design is not final and this field is subject to change in the future.
  // +optional
  optional string fieldPath = 7;
}

// PersistentVolume (PV) is a storage resource provisioned by an administrator.
// It is analogous to a node.
// More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes
message PersistentVolume {
  // Standard object's metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // spec defines a specification of a persistent volume owned by the cluster.
  // Provisioned by an administrator.
  // More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#persistent-volumes
  // +optional
  optional PersistentVolumeSpec spec = 2;

  // status represents the current information/status for the persistent volume.
  // Populated by the system.
  // Read-only.
  // More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#persistent-volumes
  // +optional
  optional PersistentVolumeStatus status = 3;
}

// PersistentVolumeClaim is a user's request for and claim to a persistent volume
message PersistentVolumeClaim {
  // Standard object's metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // spec defines the desired characteristics of a volume requested by a pod author.
  // More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#persistentvolumeclaims
  // +optional
  optional PersistentVolumeClaimSpec spec = 2;

  // status represents the current information/status of a persistent volume claim.
  // Read-only.
  // More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#persistentvolumeclaims
  // +optional
  optional PersistentVolumeClaimStatus status = 3;
}

// PersistentVolumeClaimCondition contains details about state of pvc
message PersistentVolumeClaimCondition {
  optional string type = 1;

  optional string status = 2;

  // lastProbeTime is the time we probed the condition.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time lastProbeTime = 3;

  // lastTransitionTime is the time the condition transitioned from one status to another.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time lastTransitionTime = 4;

  // reason is a unique, this should be a short, machine understandable string that gives the reason
  // for condition's last transition. If it reports "ResizeStarted" that means the underlying
  // persistent volume is being resized.
  // +optional
  optional string reason = 5;

  // message is the human-readable message indicating details about last transition.
  // +optional
  optional string message = 6;
}

// PersistentVolumeClaimList is a list of PersistentVolumeClaim items.
message PersistentVolumeClaimList {
  // Standard list metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // items is a list of persistent volume claims.
  // More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#persistentvolumeclaims
  repeated PersistentVolumeClaim items = 2;
}

// PersistentVolumeClaimSpec describes the common attributes of storage devices
// and allows a Source for provider-specific attributes
message PersistentVolumeClaimSpec {
  // accessModes contains the desired access modes the volume should have.
  // More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#access-modes-1
  // +optional
  repeated string accessModes = 1;

  // selector is a label query over volumes to consider for binding.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.LabelSelector selector = 4;

  // resources represents the minimum resources the volume should have.
  // If RecoverVolumeExpansionFailure feature is enabled users are allowed to specify resource requirements
  // that are lower than previous value but must still be higher than capacity recorded in the
  // status field of the claim.
  // More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#resources
  // +optional
  optional ResourceRequirements resources = 2;

  // volumeName is the binding reference to the PersistentVolume backing this claim.
  // +optional
  optional string volumeName = 3;

  // storageClassName is the name of the StorageClass required by the claim.
  // More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#class-1
  // +optional
  optional string storageClassName = 5;

  // volumeMode defines what type of volume is required by the claim.
  // Value of Filesystem is implied when not included in claim spec.
  // +optional
  optional string volumeMode = 6;

  // dataSource field can be used to specify either:
  // * An existing VolumeSnapshot object (snapshot.storage.k8s.io/VolumeSnapshot)
  // * An existing PVC (PersistentVolumeClaim)
  // If the provisioner or an external controller can support the specified data source,
  // it will create a new volume based on the contents of the specified data source.
  // When the AnyVolumeDataSource feature gate is enabled, dataSource contents will be copied to dataSourceRef,
  // and dataSourceRef contents will be copied to dataSource when dataSourceRef.namespace is not specified.
  // If the namespace is specified, then dataSourceRef will not be copied to dataSource.
  // +optional
  optional TypedLocalObjectReference dataSource = 7;

  // dataSourceRef specifies the object from which to populate the volume with data, if a non-empty
  // volume is desired. This may be any object from a non-empty API group (non
  // core object) or a PersistentVolumeClaim object.
  // When this field is specified, volume binding will only succeed if the type of
  // the specified object matches some installed volume populator or dynamic
  // provisioner.
  // This field will replace the functionality of the dataSource field and as such
  // if both fields are non-empty, they must have the same value. For backwards
  // compatibility, when namespace isn't specified in dataSourceRef,
  // both fields (dataSource and dataSourceRef) will be set to the same
  // value automatically if one of them is empty and the other is non-empty.
  // When namespace is specified in dataSourceRef,
  // dataSource isn't set to the same value and must be empty.
  // There are three important differences between dataSource and dataSourceRef:
  // * While dataSource only allows two specific types of objects, dataSourceRef
  //   allows any non-core object, as well as PersistentVolumeClaim objects.
  // * While dataSource ignores disallowed values (dropping them), dataSourceRef
  //   preserves all values, and generates an error if a disallowed value is
  //   specified.
  // * While dataSource only allows local objects, dataSourceRef allows objects
  //   in any namespaces.
  // (Beta) Using this field requires the AnyVolumeDataSource feature gate to be enabled.
  // (Alpha) Using the namespace field of dataSourceRef requires the CrossNamespaceVolumeDataSource feature gate to be enabled.
  // +optional
  optional TypedObjectReference dataSourceRef = 8;
}

// PersistentVolumeClaimStatus is the current status of a persistent volume claim.
message PersistentVolumeClaimStatus {
  // phase represents the current phase of PersistentVolumeClaim.
  // +optional
  optional string phase = 1;

  // accessModes contains the actual access modes the volume backing the PVC has.
  // More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#access-modes-1
  // +optional
  repeated string accessModes = 2;

  // capacity represents the actual resources of the underlying volume.
  // +optional
  map<string, k8s.io.apimachinery.pkg.api.resource.Quantity> capacity = 3;

  // conditions is the current Condition of persistent volume claim. If underlying persistent volume is being
  // resized then the Condition will be set to 'ResizeStarted'.
  // +optional
  // +patchMergeKey=type
  // +patchStrategy=merge
  repeated PersistentVolumeClaimCondition conditions = 4;

  // allocatedResources tracks the resources allocated to a PVC including its capacity.
  // Key names follow standard Kubernetes label syntax. Valid values are either:
  // 	* Un-prefixed keys:
  // 		- storage - the capacity of the volume.
  // 	* Custom resources must use implementation-defined prefixed names such as "example.com/my-custom-resource"
  // Apart from above values - keys that are unprefixed or have kubernetes.io prefix are considered
  // reserved and hence may not be used.
  //
  // Capacity reported here may be larger than the actual capacity when a volume expansion operation
  // is requested.
  // For storage quota, the larger value from allocatedResources and PVC.spec.resources is used.
  // If allocatedResources is not set, PVC.spec.resources alone is used for quota calculation.
  // If a volume expansion capacity request is lowered, allocatedResources is only
  // lowered if there are no expansion operations in progress and if the actual volume capacity
  // is equal or lower than the requested capacity.
  //
  // A controller that receives PVC update with previously unknown resourceName
  // should ignore the update for the purpose it was designed. For example - a controller that
  // only is responsible for resizing capacity of the volume, should ignore PVC updates that change other valid
  // resources associated with PVC.
  //
  // This is an alpha field and requires enabling RecoverVolumeExpansionFailure feature.
  // +featureGate=RecoverVolumeExpansionFailure
  // +optional
  map<string, k8s.io.apimachinery.pkg.api.resource.Quantity> allocatedResources = 5;

  // allocatedResourceStatuses stores status of resource being resized for the given PVC.
  // Key names follow standard Kubernetes label syntax. Valid values are either:
  // 	* Un-prefixed keys:
  // 		- storage - the capacity of the volume.
  // 	* Custom resources must use implementation-defined prefixed names such as "example.com/my-custom-resource"
  // Apart from above values - keys that are unprefixed or have kubernetes.io prefix are considered
  // reserved and hence may not be used.
  //
  // ClaimResourceStatus can be in any of following states:
  // 	- ControllerResizeInProgress:
  // 		State set when resize controller starts resizing the volume in control-plane.
  // 	- ControllerResizeFailed:
  // 		State set when resize has failed in resize controller with a terminal error.
  // 	- NodeResizePending:
  // 		State set when resize controller has finished resizing the volume but further resizing of
  // 		volume is needed on the node.
  // 	- NodeResizeInProgress:
  // 		State set when kubelet starts resizing the volume.
  // 	- NodeResizeFailed:
  // 		State set when resizing has failed in kubelet with a terminal error. Transient errors don't set
  // 		NodeResizeFailed.
  // For example: if expanding a PVC for more capacity - this field can be one of the following states:
  // 	- pvc.status.allocatedResourceStatus['storage'] = "ControllerResizeInProgress"
  //      - pvc.status.allocatedResourceStatus['storage'] = "ControllerResizeFailed"
  //      - pvc.status.allocatedResourceStatus['storage'] = "NodeResizePending"
  //      - pvc.status.allocatedResourceStatus['storage'] = "NodeResizeInProgress"
  //      - pvc.status.allocatedResourceStatus['storage'] = "NodeResizeFailed"
  // When this field is not set, it means that no resize operation is in progress for the given PVC.
  //
  // A controller that receives PVC update with previously unknown resourceName or ClaimResourceStatus
  // should ignore the update for the purpose it was designed. For example - a controller that
  // only is responsible for resizing capacity of the volume, should ignore PVC updates that change other valid
  // resources associated with PVC.
  //
  // This is an alpha field and requires enabling RecoverVolumeExpansionFailure feature.
  // +featureGate=RecoverVolumeExpansionFailure
  // +mapType=granular
  // +optional
  map<string, string> allocatedResourceStatuses = 7;
}

// PersistentVolumeClaimTemplate is used to produce
// PersistentVolumeClaim objects as part of an EphemeralVolumeSource.
message PersistentVolumeClaimTemplate {
  // May contain labels and annotations that will be copied into the PVC
  // when creating it. No other fields are allowed and will be rejected during
  // validation.
  //
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // The specification for the PersistentVolumeClaim. The entire content is
  // copied unchanged into the PVC that gets created from this
  // template. The same fields as in a PersistentVolumeClaim
  // are also valid here.
  optional PersistentVolumeClaimSpec spec = 2;
}

// PersistentVolumeClaimVolumeSource references the user's PVC in the same namespace.
// This volume finds the bound PV and mounts that volume for the pod. A
// PersistentVolumeClaimVolumeSource is, essentially, a wrapper around another
// type of volume that is owned by someone else (the system).
message PersistentVolumeClaimVolumeSource {
  // claimName is the name of a PersistentVolumeClaim in the same namespace as the pod using this volume.
  // More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#persistentvolumeclaims
  optional string claimName = 1;

  // readOnly Will force the ReadOnly setting in VolumeMounts.
  // Default false.
  // +optional
  optional bool readOnly = 2;
}

// PersistentVolumeList is a list of PersistentVolume items.
message PersistentVolumeList {
  // Standard list metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // items is a list of persistent volumes.
  // More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes
  repeated PersistentVolume items = 2;
}

// PersistentVolumeSource is similar to VolumeSource but meant for the
// administrator who creates PVs. Exactly one of its members must be set.
message PersistentVolumeSource {
  // gcePersistentDisk represents a GCE Disk resource that is attached to a
  // kubelet's host machine and then exposed to the pod. Provisioned by an admin.
  // More info: https://kubernetes.io/docs/concepts/storage/volumes#gcepersistentdisk
  // +optional
  optional GCEPersistentDiskVolumeSource gcePersistentDisk = 1;

  // awsElasticBlockStore represents an AWS Disk resource that is attached to a
  // kubelet's host machine and then exposed to the pod.
  // More info: https://kubernetes.io/docs/concepts/storage/volumes#awselasticblockstore
  // +optional
  optional AWSElasticBlockStoreVolumeSource awsElasticBlockStore = 2;

  // hostPath represents a directory on the host.
  // Provisioned by a developer or tester.
  // This is useful for single-node development and testing only!
  // On-host storage is not supported in any way and WILL NOT WORK in a multi-node cluster.
  // More info: https://kubernetes.io/docs/concepts/storage/volumes#hostpath
  // +optional
  optional HostPathVolumeSource hostPath = 3;

  // glusterfs represents a Glusterfs volume that is attached to a host and
  // exposed to the pod. Provisioned by an admin.
  // More info: https://examples.k8s.io/volumes/glusterfs/README.md
  // +optional
  optional GlusterfsPersistentVolumeSource glusterfs = 4;

  // nfs represents an NFS mount on the host. Provisioned by an admin.
  // More info: https://kubernetes.io/docs/concepts/storage/volumes#nfs
  // +optional
  optional NFSVolumeSource nfs = 5;

  // rbd represents a Rados Block Device mount on the host that shares a pod's lifetime.
  // More info: https://examples.k8s.io/volumes/rbd/README.md
  // +optional
  optional RBDPersistentVolumeSource rbd = 6;

  // iscsi represents an ISCSI Disk resource that is attached to a
  // kubelet's host machine and then exposed to the pod. Provisioned by an admin.
  // +optional
  optional ISCSIPersistentVolumeSource iscsi = 7;

  // cinder represents a cinder volume attached and mounted on kubelets host machine.
  // More info: https://examples.k8s.io/mysql-cinder-pd/README.md
  // +optional
  optional CinderPersistentVolumeSource cinder = 8;

  // cephFS represents a Ceph FS mount on the host that shares a pod's lifetime
  // +optional
  optional CephFSPersistentVolumeSource cephfs = 9;

  // fc represents a Fibre Channel resource that is attached to a kubelet's host machine and then exposed to the pod.
  // +optional
  optional FCVolumeSource fc = 10;

  // flocker represents a Flocker volume attached to a kubelet's host machine and exposed to the pod for its usage. This depends on the Flocker control service being running
  // +optional
  optional FlockerVolumeSource flocker = 11;

  // flexVolume represents a generic volume resource that is
  // provisioned/attached using an exec based plugin.
  // +optional
  optional FlexPersistentVolumeSource flexVolume = 12;

  // azureFile represents an Azure File Service mount on the host and bind mount to the pod.
  // +optional
  optional AzureFilePersistentVolumeSource azureFile = 13;

  // vsphereVolume represents a vSphere volume attached and mounted on kubelets host machine
  // +optional
  optional VsphereVirtualDiskVolumeSource vsphereVolume = 14;

  // quobyte represents a Quobyte mount on the host that shares a pod's lifetime
  // +optional
  optional QuobyteVolumeSource quobyte = 15;

  // azureDisk represents an Azure Data Disk mount on the host and bind mount to the pod.
  // +optional
  optional AzureDiskVolumeSource azureDisk = 16;

  // photonPersistentDisk represents a PhotonController persistent disk attached and mounted on kubelets host machine
  optional PhotonPersistentDiskVolumeSource photonPersistentDisk = 17;

  // portworxVolume represents a portworx volume attached and mounted on kubelets host machine
  // +optional
  optional PortworxVolumeSource portworxVolume = 18;

  // scaleIO represents a ScaleIO persistent volume attached and mounted on Kubernetes nodes.
  // +optional
  optional ScaleIOPersistentVolumeSource scaleIO = 19;

  // local represents directly-attached storage with node affinity
  // +optional
  optional LocalVolumeSource local = 20;

  // storageOS represents a StorageOS volume that is attached to the kubelet's host machine and mounted into the pod
  // More info: https://examples.k8s.io/volumes/storageos/README.md
  // +optional
  optional StorageOSPersistentVolumeSource storageos = 21;

  // csi represents storage that is handled by an external CSI driver (Beta feature).
  // +optional
  optional CSIPersistentVolumeSource csi = 22;
}

// PersistentVolumeSpec is the specification of a persistent volume.
message PersistentVolumeSpec {
  // capacity is the description of the persistent volume's resources and capacity.
  // More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#capacity
  // +optional
  map<string, k8s.io.apimachinery.pkg.api.resource.Quantity> capacity = 1;

  // persistentVolumeSource is the actual volume backing the persistent volume.
  optional PersistentVolumeSource persistentVolumeSource = 2;

  // accessModes contains all ways the volume can be mounted.
  // More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#access-modes
  // +optional
  repeated string accessModes = 3;

  // claimRef is part of a bi-directional binding between PersistentVolume and PersistentVolumeClaim.
  // Expected to be non-nil when bound.
  // claim.VolumeName is the authoritative bind between PV and PVC.
  // More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#binding
  // +optional
  // +structType=granular
  optional ObjectReference claimRef = 4;

  // persistentVolumeReclaimPolicy defines what happens to a persistent volume when released from its claim.
  // Valid options are Retain (default for manually created PersistentVolumes), Delete (default
  // for dynamically provisioned PersistentVolumes), and Recycle (deprecated).
  // Recycle must be supported by the volume plugin underlying this PersistentVolume.
  // More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#reclaiming
  // +optional
  optional string persistentVolumeReclaimPolicy = 5;

  // storageClassName is the name of StorageClass to which this persistent volume belongs. Empty value
  // means that this volume does not belong to any StorageClass.
  // +optional
  optional string storageClassName = 6;

  // mountOptions is the list of mount options, e.g. ["ro", "soft"]. Not validated - mount will
  // simply fail if one is invalid.
  // More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes/#mount-options
  // +optional
  repeated string mountOptions = 7;

  // volumeMode defines if a volume is intended to be used with a formatted filesystem
  // or to remain in raw block state. Value of Filesystem is implied when not included in spec.
  // +optional
  optional string volumeMode = 8;

  // nodeAffinity defines constraints that limit what nodes this volume can be accessed from.
  // This field influences the scheduling of pods that use this volume.
  // +optional
  optional VolumeNodeAffinity nodeAffinity = 9;
}

// PersistentVolumeStatus is the current status of a persistent volume.
message PersistentVolumeStatus {
  // phase indicates if a volume is available, bound to a claim, or released by a claim.
  // More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#phase
  // +optional
  optional string phase = 1;

  // message is a human-readable message indicating details about why the volume is in this state.
  // +optional
  optional string message = 2;

  // reason is a brief CamelCase string that describes any failure and is meant
  // for machine parsing and tidy display in the CLI.
  // +optional
  optional string reason = 3;

  // lastPhaseTransitionTime is the time the phase transitioned from one to another
  // and automatically resets to current time everytime a volume phase transitions.
  // This is an alpha field and requires enabling PersistentVolumeLastPhaseTransitionTime feature.
  // +featureGate=PersistentVolumeLastPhaseTransitionTime
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time lastPhaseTransitionTime = 4;
}

// Represents a Photon Controller persistent disk resource.
message PhotonPersistentDiskVolumeSource {
  // pdID is the ID that identifies Photon Controller persistent disk
  optional string pdID = 1;

  // fsType is the filesystem type to mount.
  // Must be a filesystem type supported by the host operating system.
  // Ex. "ext4", "xfs", "ntfs". Implicitly inferred to be "ext4" if unspecified.
  optional string fsType = 2;
}

// Pod is a collection of containers that can run on a host. This resource is created
// by clients and scheduled onto hosts.
message Pod {
  // Standard object's metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Specification of the desired behavior of the pod.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status
  // +optional
  optional PodSpec spec = 2;

  // Most recently observed status of the pod.
  // This data may not be up to date.
  // Populated by the system.
  // Read-only.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status
  // +optional
  optional PodStatus status = 3;
}

// Pod affinity is a group of inter pod affinity scheduling rules.
message PodAffinity {
  // If the affinity requirements specified by this field are not met at
  // scheduling time, the pod will not be scheduled onto the node.
  // If the affinity requirements specified by this field cease to be met
  // at some point during pod execution (e.g. due to a pod label update), the
  // system may or may not try to eventually evict the pod from its node.
  // When there are multiple elements, the lists of nodes corresponding to each
  // podAffinityTerm are intersected, i.e. all terms must be satisfied.
  // +optional
  repeated PodAffinityTerm requiredDuringSchedulingIgnoredDuringExecution = 1;

  // The scheduler will prefer to schedule pods to nodes that satisfy
  // the affinity expressions specified by this field, but it may choose
  // a node that violates one or more of the expressions. The node that is
  // most preferred is the one with the greatest sum of weights, i.e.
  // for each node that meets all of the scheduling requirements (resource
  // request, requiredDuringScheduling affinity expressions, etc.),
  // compute a sum by iterating through the elements of this field and adding
  // "weight" to the sum if the node has pods which matches the corresponding podAffinityTerm; the
  // node(s) with the highest sum are the most preferred.
  // +optional
  repeated WeightedPodAffinityTerm preferredDuringSchedulingIgnoredDuringExecution = 2;
}

// Defines a set of pods (namely those matching the labelSelector
// relative to the given namespace(s)) that this pod should be
// co-located (affinity) or not co-located (anti-affinity) with,
// where co-located is defined as running on a node whose value of
// the label with key <topologyKey> matches that of any node on which
// a pod of the set of pods is running
message PodAffinityTerm {
  // A label query over a set of resources, in this case pods.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.LabelSelector labelSelector = 1;

  // namespaces specifies a static list of namespace names that the term applies to.
  // The term is applied to the union of the namespaces listed in this field
  // and the ones selected by namespaceSelector.
  // null or empty namespaces list and null namespaceSelector means "this pod's namespace".
  // +optional
  repeated string namespaces = 2;

  // This pod should be co-located (affinity) or not co-located (anti-affinity) with the pods matching
  // the labelSelector in the specified namespaces, where co-located is defined as running on a node
  // whose value of the label with key topologyKey matches that of any node on which any of the
  // selected pods is running.
  // Empty topologyKey is not allowed.
  optional string topologyKey = 3;

  // A label query over the set of namespaces that the term applies to.
  // The term is applied to the union of the namespaces selected by this field
  // and the ones listed in the namespaces field.
  // null selector and null or empty namespaces list means "this pod's namespace".
  // An empty selector ({}) matches all namespaces.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.LabelSelector namespaceSelector = 4;
}

// Pod anti affinity is a group of inter pod anti affinity scheduling rules.
message PodAntiAffinity {
  // If the anti-affinity requirements specified by this field are not met at
  // scheduling time, the pod will not be scheduled onto the node.
  // If the anti-affinity requirements specified by this field cease to be met
  // at some point during pod execution (e.g. due to a pod label update), the
  // system may or may not try to eventually evict the pod from its node.
  // When there are multiple elements, the lists of nodes corresponding to each
  // podAffinityTerm are intersected, i.e. all terms must be satisfied.
  // +optional
  repeated PodAffinityTerm requiredDuringSchedulingIgnoredDuringExecution = 1;

  // The scheduler will prefer to schedule pods to nodes that satisfy
  // the anti-affinity expressions specified by this field, but it may choose
  // a node that violates one or more of the expressions. The node that is
  // most preferred is the one with the greatest sum of weights, i.e.
  // for each node that meets all of the scheduling requirements (resource
  // request, requiredDuringScheduling anti-affinity expressions, etc.),
  // compute a sum by iterating through the elements of this field and adding
  // "weight" to the sum if the node has pods which matches the corresponding podAffinityTerm; the
  // node(s) with the highest sum are the most preferred.
  // +optional
  repeated WeightedPodAffinityTerm preferredDuringSchedulingIgnoredDuringExecution = 2;
}

// PodAttachOptions is the query options to a Pod's remote attach call.
// ---
// TODO: merge w/ PodExecOptions below for stdin, stdout, etc
// and also when we cut V2, we should export a "StreamOptions" or somesuch that contains Stdin, Stdout, Stder and TTY
message PodAttachOptions {
  // Stdin if true, redirects the standard input stream of the pod for this call.
  // Defaults to false.
  // +optional
  optional bool stdin = 1;

  // Stdout if true indicates that stdout is to be redirected for the attach call.
  // Defaults to true.
  // +optional
  optional bool stdout = 2;

  // Stderr if true indicates that stderr is to be redirected for the attach call.
  // Defaults to true.
  // +optional
  optional bool stderr = 3;

  // TTY if true indicates that a tty will be allocated for the attach call.
  // This is passed through the container runtime so the tty
  // is allocated on the worker node by the container runtime.
  // Defaults to false.
  // +optional
  optional bool tty = 4;

  // The container in which to execute the command.
  // Defaults to only container if there is only one container in the pod.
  // +optional
  optional string container = 5;
}

// PodCondition contains details for the current condition of this pod.
message PodCondition {
  // Type is the type of the condition.
  // More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#pod-conditions
  optional string type = 1;

  // Status is the status of the condition.
  // Can be True, False, Unknown.
  // More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#pod-conditions
  optional string status = 2;

  // Last time we probed the condition.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time lastProbeTime = 3;

  // Last time the condition transitioned from one status to another.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time lastTransitionTime = 4;

  // Unique, one-word, CamelCase reason for the condition's last transition.
  // +optional
  optional string reason = 5;

  // Human-readable message indicating details about last transition.
  // +optional
  optional string message = 6;
}

// PodDNSConfig defines the DNS parameters of a pod in addition to
// those generated from DNSPolicy.
message PodDNSConfig {
  // A list of DNS name server IP addresses.
  // This will be appended to the base nameservers generated from DNSPolicy.
  // Duplicated nameservers will be removed.
  // +optional
  repeated string nameservers = 1;

  // A list of DNS search domains for host-name lookup.
  // This will be appended to the base search paths generated from DNSPolicy.
  // Duplicated search paths will be removed.
  // +optional
  repeated string searches = 2;

  // A list of DNS resolver options.
  // This will be merged with the base options generated from DNSPolicy.
  // Duplicated entries will be removed. Resolution options given in Options
  // will override those that appear in the base DNSPolicy.
  // +optional
  repeated PodDNSConfigOption options = 3;
}

// PodDNSConfigOption defines DNS resolver options of a pod.
message PodDNSConfigOption {
  // Required.
  optional string name = 1;

  // +optional
  optional string value = 2;
}

// PodExecOptions is the query options to a Pod's remote exec call.
// ---
// TODO: This is largely identical to PodAttachOptions above, make sure they stay in sync and see about merging
// and also when we cut V2, we should export a "StreamOptions" or somesuch that contains Stdin, Stdout, Stder and TTY
message PodExecOptions {
  // Redirect the standard input stream of the pod for this call.
  // Defaults to false.
  // +optional
  optional bool stdin = 1;

  // Redirect the standard output stream of the pod for this call.
  // +optional
  optional bool stdout = 2;

  // Redirect the standard error stream of the pod for this call.
  // +optional
  optional bool stderr = 3;

  // TTY if true indicates that a tty will be allocated for the exec call.
  // Defaults to false.
  // +optional
  optional bool tty = 4;

  // Container in which to execute the command.
  // Defaults to only container if there is only one container in the pod.
  // +optional
  optional string container = 5;

  // Command is the remote command to execute. argv array. Not executed within a shell.
  repeated string command = 6;
}

// PodIP represents a single IP address allocated to the pod.
message PodIP {
  // IP is the IP address assigned to the pod
  optional string ip = 1;
}

// PodList is a list of Pods.
message PodList {
  // Standard list metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // List of pods.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md
  repeated Pod items = 2;
}

// PodLogOptions is the query options for a Pod's logs REST call.
message PodLogOptions {
  // The container for which to stream logs. Defaults to only container if there is one container in the pod.
  // +optional
  optional string container = 1;

  // Follow the log stream of the pod. Defaults to false.
  // +optional
  optional bool follow = 2;

  // Return previous terminated container logs. Defaults to false.
  // +optional
  optional bool previous = 3;

  // A relative time in seconds before the current time from which to show logs. If this value
  // precedes the time a pod was started, only logs since the pod start will be returned.
  // If this value is in the future, no logs will be returned.
  // Only one of sinceSeconds or sinceTime may be specified.
  // +optional
  optional int64 sinceSeconds = 4;

  // An RFC3339 timestamp from which to show logs. If this value
  // precedes the time a pod was started, only logs since the pod start will be returned.
  // If this value is in the future, no logs will be returned.
  // Only one of sinceSeconds or sinceTime may be specified.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time sinceTime = 5;

  // If true, add an RFC3339 or RFC3339Nano timestamp at the beginning of every line
  // of log output. Defaults to false.
  // +optional
  optional bool timestamps = 6;

  // If set, the number of lines from the end of the logs to show. If not specified,
  // logs are shown from the creation of the container or sinceSeconds or sinceTime
  // +optional
  optional int64 tailLines = 7;

  // If set, the number of bytes to read from the server before terminating the
  // log output. This may not display a complete final line of logging, and may return
  // slightly more or slightly less than the specified limit.
  // +optional
  optional int64 limitBytes = 8;

  // insecureSkipTLSVerifyBackend indicates that the apiserver should not confirm the validity of the
  // serving certificate of the backend it is connecting to.  This will make the HTTPS connection between the apiserver
  // and the backend insecure. This means the apiserver cannot verify the log data it is receiving came from the real
  // kubelet.  If the kubelet is configured to verify the apiserver's TLS credentials, it does not mean the
  // connection to the real kubelet is vulnerable to a man in the middle attack (e.g. an attacker could not intercept
  // the actual log data coming from the real kubelet).
  // +optional
  optional bool insecureSkipTLSVerifyBackend = 9;
}

// PodOS defines the OS parameters of a pod.
message PodOS {
  // Name is the name of the operating system. The currently supported values are linux and windows.
  // Additional value may be defined in future and can be one of:
  // https://github.com/opencontainers/runtime-spec/blob/master/config.md#platform-specific-configuration
  // Clients should expect to handle additional values and treat unrecognized values in this field as os: null
  optional string name = 1;
}

// PodPortForwardOptions is the query options to a Pod's port forward call
// when using WebSockets.
// The `port` query parameter must specify the port or
// ports (comma separated) to forward over.
// Port forwarding over SPDY does not use these options. It requires the port
// to be passed in the `port` header as part of request.
message PodPortForwardOptions {
  // List of ports to forward
  // Required when using WebSockets
  // +optional
  repeated int32 ports = 1;
}

// PodProxyOptions is the query options to a Pod's proxy call.
message PodProxyOptions {
  // Path is the URL path to use for the current proxy request to pod.
  // +optional
  optional string path = 1;
}

// PodReadinessGate contains the reference to a pod condition
message PodReadinessGate {
  // ConditionType refers to a condition in the pod's condition list with matching type.
  optional string conditionType = 1;
}

// PodResourceClaim references exactly one ResourceClaim through a ClaimSource.
// It adds a name to it that uniquely identifies the ResourceClaim inside the Pod.
// Containers that need access to the ResourceClaim reference it with this name.
message PodResourceClaim {
  // Name uniquely identifies this resource claim inside the pod.
  // This must be a DNS_LABEL.
  optional string name = 1;

  // Source describes where to find the ResourceClaim.
  optional ClaimSource source = 2;
}

// PodResourceClaimStatus is stored in the PodStatus for each PodResourceClaim
// which references a ResourceClaimTemplate. It stores the generated name for
// the corresponding ResourceClaim.
message PodResourceClaimStatus {
  // Name uniquely identifies this resource claim inside the pod.
  // This must match the name of an entry in pod.spec.resourceClaims,
  // which implies that the string must be a DNS_LABEL.
  optional string name = 1;

  // ResourceClaimName is the name of the ResourceClaim that was
  // generated for the Pod in the namespace of the Pod. It this is
  // unset, then generating a ResourceClaim was not necessary. The
  // pod.spec.resourceClaims entry can be ignored in this case.
  //
  // +optional
  optional string resourceClaimName = 2;
}

// PodSchedulingGate is associated to a Pod to guard its scheduling.
message PodSchedulingGate {
  // Name of the scheduling gate.
  // Each scheduling gate must have a unique name field.
  optional string name = 1;
}

// PodSecurityContext holds pod-level security attributes and common container settings.
// Some fields are also present in container.securityContext.  Field values of
// container.securityContext take precedence over field values of PodSecurityContext.
message PodSecurityContext {
  // The SELinux context to be applied to all containers.
  // If unspecified, the container runtime will allocate a random SELinux context for each
  // container.  May also be set in SecurityContext.  If set in
  // both SecurityContext and PodSecurityContext, the value specified in SecurityContext
  // takes precedence for that container.
  // Note that this field cannot be set when spec.os.name is windows.
  // +optional
  optional SELinuxOptions seLinuxOptions = 1;

  // The Windows specific settings applied to all containers.
  // If unspecified, the options within a container's SecurityContext will be used.
  // If set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence.
  // Note that this field cannot be set when spec.os.name is linux.
  // +optional
  optional WindowsSecurityContextOptions windowsOptions = 8;

  // The UID to run the entrypoint of the container process.
  // Defaults to user specified in image metadata if unspecified.
  // May also be set in SecurityContext.  If set in both SecurityContext and
  // PodSecurityContext, the value specified in SecurityContext takes precedence
  // for that container.
  // Note that this field cannot be set when spec.os.name is windows.
  // +optional
  optional int64 runAsUser = 2;

  // The GID to run the entrypoint of the container process.
  // Uses runtime default if unset.
  // May also be set in SecurityContext.  If set in both SecurityContext and
  // PodSecurityContext, the value specified in SecurityContext takes precedence
  // for that container.
  // Note that this field cannot be set when spec.os.name is windows.
  // +optional
  optional int64 runAsGroup = 6;

  // Indicates that the container must run as a non-root user.
  // If true, the Kubelet will validate the image at runtime to ensure that it
  // does not run as UID 0 (root) and fail to start the container if it does.
  // If unset or false, no such validation will be performed.
  // May also be set in SecurityContext.  If set in both SecurityContext and
  // PodSecurityContext, the value specified in SecurityContext takes precedence.
  // +optional
  optional bool runAsNonRoot = 3;

  // A list of groups applied to the first process run in each container, in addition
  // to the container's primary GID, the fsGroup (if specified), and group memberships
  // defined in the container image for the uid of the container process. If unspecified,
  // no additional groups are added to any container. Note that group memberships
  // defined in the container image for the uid of the container process are still effective,
  // even if they are not included in this list.
  // Note that this field cannot be set when spec.os.name is windows.
  // +optional
  repeated int64 supplementalGroups = 4;

  // A special supplemental group that applies to all containers in a pod.
  // Some volume types allow the Kubelet to change the ownership of that volume
  // to be owned by the pod:
  //
  // 1. The owning GID will be the FSGroup
  // 2. The setgid bit is set (new files created in the volume will be owned by FSGroup)
  // 3. The permission bits are OR'd with rw-rw----
  //
  // If unset, the Kubelet will not modify the ownership and permissions of any volume.
  // Note that this field cannot be set when spec.os.name is windows.
  // +optional
  optional int64 fsGroup = 5;

  // Sysctls hold a list of namespaced sysctls used for the pod. Pods with unsupported
  // sysctls (by the container runtime) might fail to launch.
  // Note that this field cannot be set when spec.os.name is windows.
  // +optional
  repeated Sysctl sysctls = 7;

  // fsGroupChangePolicy defines behavior of changing ownership and permission of the volume
  // before being exposed inside Pod. This field will only apply to
  // volume types which support fsGroup based ownership(and permissions).
  // It will have no effect on ephemeral volume types such as: secret, configmaps
  // and emptydir.
  // Valid values are "OnRootMismatch" and "Always". If not specified, "Always" is used.
  // Note that this field cannot be set when spec.os.name is windows.
  // +optional
  optional string fsGroupChangePolicy = 9;

  // The seccomp options to use by the containers in this pod.
  // Note that this field cannot be set when spec.os.name is windows.
  // +optional
  optional SeccompProfile seccompProfile = 10;
}

// Describes the class of pods that should avoid this node.
// Exactly one field should be set.
message PodSignature {
  // Reference to controller whose pods should avoid this node.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.OwnerReference podController = 1;
}

// PodSpec is a description of a pod.
message PodSpec {
  // List of volumes that can be mounted by containers belonging to the pod.
  // More info: https://kubernetes.io/docs/concepts/storage/volumes
  // +optional
  // +patchMergeKey=name
  // +patchStrategy=merge,retainKeys
  repeated Volume volumes = 1;

  // List of initialization containers belonging to the pod.
  // Init containers are executed in order prior to containers being started. If any
  // init container fails, the pod is considered to have failed and is handled according
  // to its restartPolicy. The name for an init container or normal container must be
  // unique among all containers.
  // Init containers may not have Lifecycle actions, Readiness probes, Liveness probes, or Startup probes.
  // The resourceRequirements of an init container are taken into account during scheduling
  // by finding the highest request/limit for each resource type, and then using the max of
  // of that value or the sum of the normal containers. Limits are applied to init containers
  // in a similar fashion.
  // Init containers cannot currently be added or removed.
  // Cannot be updated.
  // More info: https://kubernetes.io/docs/concepts/workloads/pods/init-containers/
  // +patchMergeKey=name
  // +patchStrategy=merge
  repeated Container initContainers = 20;

  // List of containers belonging to the pod.
  // Containers cannot currently be added or removed.
  // There must be at least one container in a Pod.
  // Cannot be updated.
  // +patchMergeKey=name
  // +patchStrategy=merge
  repeated Container containers = 2;

  // List of ephemeral containers run in this pod. Ephemeral containers may be run in an existing
  // pod to perform user-initiated actions such as debugging. This list cannot be specified when
  // creating a pod, and it cannot be modified by updating the pod spec. In order to add an
  // ephemeral container to an existing pod, use the pod's ephemeralcontainers subresource.
  // +optional
  // +patchMergeKey=name
  // +patchStrategy=merge
  repeated EphemeralContainer ephemeralContainers = 34;

  // Restart policy for all containers within the pod.
  // One of Always, OnFailure, Never. In some contexts, only a subset of those values may be permitted.
  // Default to Always.
  // More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle/#restart-policy
  // +optional
  optional string restartPolicy = 3;

  // Optional duration in seconds the pod needs to terminate gracefully. May be decreased in delete request.
  // Value must be non-negative integer. The value zero indicates stop immediately via
  // the kill signal (no opportunity to shut down).
  // If this value is nil, the default grace period will be used instead.
  // The grace period is the duration in seconds after the processes running in the pod are sent
  // a termination signal and the time when the processes are forcibly halted with a kill signal.
  // Set this value longer than the expected cleanup time for your process.
  // Defaults to 30 seconds.
  // +optional
  optional int64 terminationGracePeriodSeconds = 4;

  // Optional duration in seconds the pod may be active on the node relative to
  // StartTime before the system will actively try to mark it failed and kill associated containers.
  // Value must be a positive integer.
  // +optional
  optional int64 activeDeadlineSeconds = 5;

  // Set DNS policy for the pod.
  // Defaults to "ClusterFirst".
  // Valid values are 'ClusterFirstWithHostNet', 'ClusterFirst', 'Default' or 'None'.
  // DNS parameters given in DNSConfig will be merged with the policy selected with DNSPolicy.
  // To have DNS options set along with hostNetwork, you have to specify DNS policy
  // explicitly to 'ClusterFirstWithHostNet'.
  // +optional
  optional string dnsPolicy = 6;

  // NodeSelector is a selector which must be true for the pod to fit on a node.
  // Selector which must match a node's labels for the pod to be scheduled on that node.
  // More info: https://kubernetes.io/docs/concepts/configuration/assign-pod-node/
  // +optional
  // +mapType=atomic
  map<string, string> nodeSelector = 7;

  // ServiceAccountName is the name of the ServiceAccount to use to run this pod.
  // More info: https://kubernetes.io/docs/tasks/configure-pod-container/configure-service-account/
  // +optional
  optional string serviceAccountName = 8;

  // DeprecatedServiceAccount is a depreciated alias for ServiceAccountName.
  // Deprecated: Use serviceAccountName instead.
  // +k8s:conversion-gen=false
  // +optional
  optional string serviceAccount = 9;

  // AutomountServiceAccountToken indicates whether a service account token should be automatically mounted.
  // +optional
  optional bool automountServiceAccountToken = 21;

  // NodeName is a request to schedule this pod onto a specific node. If it is non-empty,
  // the scheduler simply schedules this pod onto that node, assuming that it fits resource
  // requirements.
  // +optional
  optional string nodeName = 10;

  // Host networking requested for this pod. Use the host's network namespace.
  // If this option is set, the ports that will be used must be specified.
  // Default to false.
  // +k8s:conversion-gen=false
  // +optional
  optional bool hostNetwork = 11;

  // Use the host's pid namespace.
  // Optional: Default to false.
  // +k8s:conversion-gen=false
  // +optional
  optional bool hostPID = 12;

  // Use the host's ipc namespace.
  // Optional: Default to false.
  // +k8s:conversion-gen=false
  // +optional
  optional bool hostIPC = 13;

  // Share a single process namespace between all of the containers in a pod.
  // When this is set containers will be able to view and signal processes from other containers
  // in the same pod, and the first process in each container will not be assigned PID 1.
  // HostPID and ShareProcessNamespace cannot both be set.
  // Optional: Default to false.
  // +k8s:conversion-gen=false
  // +optional
  optional bool shareProcessNamespace = 27;

  // SecurityContext holds pod-level security attributes and common container settings.
  // Optional: Defaults to empty.  See type description for default values of each field.
  // +optional
  optional PodSecurityContext securityContext = 14;

  // ImagePullSecrets is an optional list of references to secrets in the same namespace to use for pulling any of the images used by this PodSpec.
  // If specified, these secrets will be passed to individual puller implementations for them to use.
  // More info: https://kubernetes.io/docs/concepts/containers/images#specifying-imagepullsecrets-on-a-pod
  // +optional
  // +patchMergeKey=name
  // +patchStrategy=merge
  repeated LocalObjectReference imagePullSecrets = 15;

  // Specifies the hostname of the Pod
  // If not specified, the pod's hostname will be set to a system-defined value.
  // +optional
  optional string hostname = 16;

  // If specified, the fully qualified Pod hostname will be "<hostname>.<subdomain>.<pod namespace>.svc.<cluster domain>".
  // If not specified, the pod will not have a domainname at all.
  // +optional
  optional string subdomain = 17;

  // If specified, the pod's scheduling constraints
  // +optional
  optional Affinity affinity = 18;

  // If specified, the pod will be dispatched by specified scheduler.
  // If not specified, the pod will be dispatched by default scheduler.
  // +optional
  optional string schedulerName = 19;

  // If specified, the pod's tolerations.
  // +optional
  repeated Toleration tolerations = 22;

  // HostAliases is an optional list of hosts and IPs that will be injected into the pod's hosts
  // file if specified. This is only valid for non-hostNetwork pods.
  // +optional
  // +patchMergeKey=ip
  // +patchStrategy=merge
  repeated HostAlias hostAliases = 23;

  // If specified, indicates the pod's priority. "system-node-critical" and
  // "system-cluster-critical" are two special keywords which indicate the
  // highest priorities with the former being the highest priority. Any other
  // name must be defined by creating a PriorityClass object with that name.
  // If not specified, the pod priority will be default or zero if there is no
  // default.
  // +optional
  optional string priorityClassName = 24;

  // The priority value. Various system components use this field to find the
  // priority of the pod. When Priority Admission Controller is enabled, it
  // prevents users from setting this field. The admission controller populates
  // this field from PriorityClassName.
  // The higher the value, the higher the priority.
  // +optional
  optional int32 priority = 25;

  // Specifies the DNS parameters of a pod.
  // Parameters specified here will be merged to the generated DNS
  // configuration based on DNSPolicy.
  // +optional
  optional PodDNSConfig dnsConfig = 26;

  // If specified, all readiness gates will be evaluated for pod readiness.
  // A pod is ready when all its containers are ready AND
  // all conditions specified in the readiness gates have status equal to "True"
  // More info: https://git.k8s.io/enhancements/keps/sig-network/580-pod-readiness-gates
  // +optional
  repeated PodReadinessGate readinessGates = 28;

  // RuntimeClassName refers to a RuntimeClass object in the node.k8s.io group, which should be used
  // to run this pod.  If no RuntimeClass resource matches the named class, the pod will not be run.
  // If unset or empty, the "legacy" RuntimeClass will be used, which is an implicit class with an
  // empty definition that uses the default runtime handler.
  // More info: https://git.k8s.io/enhancements/keps/sig-node/585-runtime-class
  // +optional
  optional string runtimeClassName = 29;

  // EnableServiceLinks indicates whether information about services should be injected into pod's
  // environment variables, matching the syntax of Docker links.
  // Optional: Defaults to true.
  // +optional
  optional bool enableServiceLinks = 30;

  // PreemptionPolicy is the Policy for preempting pods with lower priority.
  // One of Never, PreemptLowerPriority.
  // Defaults to PreemptLowerPriority if unset.
  // +optional
  optional string preemptionPolicy = 31;

  // Overhead represents the resource overhead associated with running a pod for a given RuntimeClass.
  // This field will be autopopulated at admission time by the RuntimeClass admission controller. If
  // the RuntimeClass admission controller is enabled, overhead must not be set in Pod create requests.
  // The RuntimeClass admission controller will reject Pod create requests which have the overhead already
  // set. If RuntimeClass is configured and selected in the PodSpec, Overhead will be set to the value
  // defined in the corresponding RuntimeClass, otherwise it will remain unset and treated as zero.
  // More info: https://git.k8s.io/enhancements/keps/sig-node/688-pod-overhead/README.md
  // +optional
  map<string, k8s.io.apimachinery.pkg.api.resource.Quantity> overhead = 32;

  // TopologySpreadConstraints describes how a group of pods ought to spread across topology
  // domains. Scheduler will schedule pods in a way which abides by the constraints.
  // All topologySpreadConstraints are ANDed.
  // +optional
  // +patchMergeKey=topologyKey
  // +patchStrategy=merge
  // +listType=map
  // +listMapKey=topologyKey
  // +listMapKey=whenUnsatisfiable
  repeated TopologySpreadConstraint topologySpreadConstraints = 33;

  // If true the pod's hostname will be configured as the pod's FQDN, rather than the leaf name (the default).
  // In Linux containers, this means setting the FQDN in the hostname field of the kernel (the nodename field of struct utsname).
  // In Windows containers, this means setting the registry value of hostname for the registry key HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Services\\Tcpip\\Parameters to FQDN.
  // If a pod does not have FQDN, this has no effect.
  // Default to false.
  // +optional
  optional bool setHostnameAsFQDN = 35;

  // Specifies the OS of the containers in the pod.
  // Some pod and container fields are restricted if this is set.
  //
  // If the OS field is set to linux, the following fields must be unset:
  // -securityContext.windowsOptions
  //
  // If the OS field is set to windows, following fields must be unset:
  // - spec.hostPID
  // - spec.hostIPC
  // - spec.hostUsers
  // - spec.securityContext.seLinuxOptions
  // - spec.securityContext.seccompProfile
  // - spec.securityContext.fsGroup
  // - spec.securityContext.fsGroupChangePolicy
  // - spec.securityContext.sysctls
  // - spec.shareProcessNamespace
  // - spec.securityContext.runAsUser
  // - spec.securityContext.runAsGroup
  // - spec.securityContext.supplementalGroups
  // - spec.containers[*].securityContext.seLinuxOptions
  // - spec.containers[*].securityContext.seccompProfile
  // - spec.containers[*].securityContext.capabilities
  // - spec.containers[*].securityContext.readOnlyRootFilesystem
  // - spec.containers[*].securityContext.privileged
  // - spec.containers[*].securityContext.allowPrivilegeEscalation
  // - spec.containers[*].securityContext.procMount
  // - spec.containers[*].securityContext.runAsUser
  // - spec.containers[*].securityContext.runAsGroup
  // +optional
  optional PodOS os = 36;

  // Use the host's user namespace.
  // Optional: Default to true.
  // If set to true or not present, the pod will be run in the host user namespace, useful
  // for when the pod needs a feature only available to the host user namespace, such as
  // loading a kernel module with CAP_SYS_MODULE.
  // When set to false, a new userns is created for the pod. Setting false is useful for
  // mitigating container breakout vulnerabilities even allowing users to run their
  // containers as root without actually having root privileges on the host.
  // This field is alpha-level and is only honored by servers that enable the UserNamespacesSupport feature.
  // +k8s:conversion-gen=false
  // +optional
  optional bool hostUsers = 37;

  // SchedulingGates is an opaque list of values that if specified will block scheduling the pod.
  // If schedulingGates is not empty, the pod will stay in the SchedulingGated state and the
  // scheduler will not attempt to schedule the pod.
  //
  // SchedulingGates can only be set at pod creation time, and be removed only afterwards.
  //
  // This is a beta feature enabled by the PodSchedulingReadiness feature gate.
  //
  // +patchMergeKey=name
  // +patchStrategy=merge
  // +listType=map
  // +listMapKey=name
  // +featureGate=PodSchedulingReadiness
  // +optional
  repeated PodSchedulingGate schedulingGates = 38;

  // ResourceClaims defines which ResourceClaims must be allocated
  // and reserved before the Pod is allowed to start. The resources
  // will be made available to those containers which consume them
  // by name.
  //
  // This is an alpha field and requires enabling the
  // DynamicResourceAllocation feature gate.
  //
  // This field is immutable.
  //
  // +patchMergeKey=name
  // +patchStrategy=merge,retainKeys
  // +listType=map
  // +listMapKey=name
  // +featureGate=DynamicResourceAllocation
  // +optional
  repeated PodResourceClaim resourceClaims = 39;
}

// PodStatus represents information about the status of a pod. Status may trail the actual
// state of a system, especially if the node that hosts the pod cannot contact the control
// plane.
message PodStatus {
  // The phase of a Pod is a simple, high-level summary of where the Pod is in its lifecycle.
  // The conditions array, the reason and message fields, and the individual container status
  // arrays contain more detail about the pod's status.
  // There are five possible phase values:
  //
  // Pending: The pod has been accepted by the Kubernetes system, but one or more of the
  // container images has not been created. This includes time before being scheduled as
  // well as time spent downloading images over the network, which could take a while.
  // Running: The pod has been bound to a node, and all of the containers have been created.
  // At least one container is still running, or is in the process of starting or restarting.
  // Succeeded: All containers in the pod have terminated in success, and will not be restarted.
  // Failed: All containers in the pod have terminated, and at least one container has
  // terminated in failure. The container either exited with non-zero status or was terminated
  // by the system.
  // Unknown: For some reason the state of the pod could not be obtained, typically due to an
  // error in communicating with the host of the pod.
  //
  // More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#pod-phase
  // +optional
  optional string phase = 1;

  // Current service state of pod.
  // More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#pod-conditions
  // +optional
  // +patchMergeKey=type
  // +patchStrategy=merge
  repeated PodCondition conditions = 2;

  // A human readable message indicating details about why the pod is in this condition.
  // +optional
  optional string message = 3;

  // A brief CamelCase message indicating details about why the pod is in this state.
  // e.g. 'Evicted'
  // +optional
  optional string reason = 4;

  // nominatedNodeName is set only when this pod preempts other pods on the node, but it cannot be
  // scheduled right away as preemption victims receive their graceful termination periods.
  // This field does not guarantee that the pod will be scheduled on this node. Scheduler may decide
  // to place the pod elsewhere if other nodes become available sooner. Scheduler may also decide to
  // give the resources on this node to a higher priority pod that is created after preemption.
  // As a result, this field may be different than PodSpec.nodeName when the pod is
  // scheduled.
  // +optional
  optional string nominatedNodeName = 11;

  // hostIP holds the IP address of the host to which the pod is assigned. Empty if the pod has not started yet.
  // A pod can be assigned to a node that has a problem in kubelet which in turns mean that HostIP will
  // not be updated even if there is a node is assigned to pod
  // +optional
  optional string hostIP = 5;

  // hostIPs holds the IP addresses allocated to the host. If this field is specified, the first entry must
  // match the hostIP field. This list is empty if the pod has not started yet.
  // A pod can be assigned to a node that has a problem in kubelet which in turns means that HostIPs will
  // not be updated even if there is a node is assigned to this pod.
  // +optional
  // +patchStrategy=merge
  // +patchMergeKey=ip
  // +listType=atomic
  repeated HostIP hostIPs = 16;

  // podIP address allocated to the pod. Routable at least within the cluster.
  // Empty if not yet allocated.
  // +optional
  optional string podIP = 6;

  // podIPs holds the IP addresses allocated to the pod. If this field is specified, the 0th entry must
  // match the podIP field. Pods may be allocated at most 1 value for each of IPv4 and IPv6. This list
  // is empty if no IPs have been allocated yet.
  // +optional
  // +patchStrategy=merge
  // +patchMergeKey=ip
  repeated PodIP podIPs = 12;

  // RFC 3339 date and time at which the object was acknowledged by the Kubelet.
  // This is before the Kubelet pulled the container image(s) for the pod.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time startTime = 7;

  // The list has one entry per init container in the manifest. The most recent successful
  // init container will have ready = true, the most recently started container will have
  // startTime set.
  // More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#pod-and-container-status
  repeated ContainerStatus initContainerStatuses = 10;

  // The list has one entry per container in the manifest.
  // More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#pod-and-container-status
  // +optional
  repeated ContainerStatus containerStatuses = 8;

  // The Quality of Service (QOS) classification assigned to the pod based on resource requirements
  // See PodQOSClass type for available QOS classes
  // More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-qos/#quality-of-service-classes
  // +optional
  optional string qosClass = 9;

  // Status for any ephemeral containers that have run in this pod.
  // +optional
  repeated ContainerStatus ephemeralContainerStatuses = 13;

  // Status of resources resize desired for pod's containers.
  // It is empty if no resources resize is pending.
  // Any changes to container resources will automatically set this to "Proposed"
  // +featureGate=InPlacePodVerticalScaling
  // +optional
  optional string resize = 14;

  // Status of resource claims.
  // +patchMergeKey=name
  // +patchStrategy=merge,retainKeys
  // +listType=map
  // +listMapKey=name
  // +featureGate=DynamicResourceAllocation
  // +optional
  repeated PodResourceClaimStatus resourceClaimStatuses = 15;
}

// PodStatusResult is a wrapper for PodStatus returned by kubelet that can be encode/decoded
message PodStatusResult {
  // Standard object's metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Most recently observed status of the pod.
  // This data may not be up to date.
  // Populated by the system.
  // Read-only.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status
  // +optional
  optional PodStatus status = 2;
}

// PodTemplate describes a template for creating copies of a predefined pod.
message PodTemplate {
  // Standard object's metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Template defines the pods that will be created from this pod template.
  // https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status
  // +optional
  optional PodTemplateSpec template = 2;
}

// PodTemplateList is a list of PodTemplates.
message PodTemplateList {
  // Standard list metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // List of pod templates
  repeated PodTemplate items = 2;
}

// PodTemplateSpec describes the data a pod should have when created from a template
message PodTemplateSpec {
  // Standard object's metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Specification of the desired behavior of the pod.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status
  // +optional
  optional PodSpec spec = 2;
}

message PortStatus {
  // Port is the port number of the service port of which status is recorded here
  optional int32 port = 1;

  // Protocol is the protocol of the service port of which status is recorded here
  // The supported values are: "TCP", "UDP", "SCTP"
  optional string protocol = 2;

  // Error is to record the problem with the service port
  // The format of the error shall comply with the following rules:
  // - built-in error values shall be specified in this file and those shall use
  //   CamelCase names
  // - cloud provider specific error values must have names that comply with the
  //   format foo.example.com/CamelCase.
  // ---
  // The regex it matches is (dns1123SubdomainFmt/)?(qualifiedNameFmt)
  // +optional
  // +kubebuilder:validation:Required
  // +kubebuilder:validation:Pattern=`^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$`
  // +kubebuilder:validation:MaxLength=316
  optional string error = 3;
}

// PortworxVolumeSource represents a Portworx volume resource.
message PortworxVolumeSource {
  // volumeID uniquely identifies a Portworx volume
  optional string volumeID = 1;

  // fSType represents the filesystem type to mount
  // Must be a filesystem type supported by the host operating system.
  // Ex. "ext4", "xfs". Implicitly inferred to be "ext4" if unspecified.
  optional string fsType = 2;

  // readOnly defaults to false (read/write). ReadOnly here will force
  // the ReadOnly setting in VolumeMounts.
  // +optional
  optional bool readOnly = 3;
}

// Preconditions must be fulfilled before an operation (update, delete, etc.) is carried out.
// +k8s:openapi-gen=false
message Preconditions {
  // Specifies the target UID.
  // +optional
  optional string uid = 1;
}

// Describes a class of pods that should avoid this node.
message PreferAvoidPodsEntry {
  // The class of pods.
  optional PodSignature podSignature = 1;

  // Time at which this entry was added to the list.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time evictionTime = 2;

  // (brief) reason why this entry was added to the list.
  // +optional
  optional string reason = 3;

  // Human readable message indicating why this entry was added to the list.
  // +optional
  optional string message = 4;
}

// An empty preferred scheduling term matches all objects with implicit weight 0
// (i.e. it's a no-op). A null preferred scheduling term matches no objects (i.e. is also a no-op).
message PreferredSchedulingTerm {
  // Weight associated with matching the corresponding nodeSelectorTerm, in the range 1-100.
  optional int32 weight = 1;

  // A node selector term, associated with the corresponding weight.
  optional NodeSelectorTerm preference = 2;
}

// Probe describes a health check to be performed against a container to determine whether it is
// alive or ready to receive traffic.
message Probe {
  // The action taken to determine the health of a container
  optional ProbeHandler handler = 1;

  // Number of seconds after the container has started before liveness probes are initiated.
  // More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes
  // +optional
  optional int32 initialDelaySeconds = 2;

  // Number of seconds after which the probe times out.
  // Defaults to 1 second. Minimum value is 1.
  // More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes
  // +optional
  optional int32 timeoutSeconds = 3;

  // How often (in seconds) to perform the probe.
  // Default to 10 seconds. Minimum value is 1.
  // +optional
  optional int32 periodSeconds = 4;

  // Minimum consecutive successes for the probe to be considered successful after having failed.
  // Defaults to 1. Must be 1 for liveness and startup. Minimum value is 1.
  // +optional
  optional int32 successThreshold = 5;

  // Minimum consecutive failures for the probe to be considered failed after having succeeded.
  // Defaults to 3. Minimum value is 1.
  // +optional
  optional int32 failureThreshold = 6;

  // Optional duration in seconds the pod needs to terminate gracefully upon probe failure.
  // The grace period is the duration in seconds after the processes running in the pod are sent
  // a termination signal and the time when the processes are forcibly halted with a kill signal.
  // Set this value longer than the expected cleanup time for your process.
  // If this value is nil, the pod's terminationGracePeriodSeconds will be used. Otherwise, this
  // value overrides the value provided by the pod spec.
  // Value must be non-negative integer. The value zero indicates stop immediately via
  // the kill signal (no opportunity to shut down).
  // This is a beta field and requires enabling ProbeTerminationGracePeriod feature gate.
  // Minimum value is 1. spec.terminationGracePeriodSeconds is used if unset.
  // +optional
  optional int64 terminationGracePeriodSeconds = 7;
}

// ProbeHandler defines a specific action that should be taken in a probe.
// One and only one of the fields must be specified.
message ProbeHandler {
  // Exec specifies the action to take.
  // +optional
  optional ExecAction exec = 1;

  // HTTPGet specifies the http request to perform.
  // +optional
  optional HTTPGetAction httpGet = 2;

  // TCPSocket specifies an action involving a TCP port.
  // +optional
  optional TCPSocketAction tcpSocket = 3;

  // GRPC specifies an action involving a GRPC port.
  // +optional
  optional GRPCAction grpc = 4;
}

// Represents a projected volume source
message ProjectedVolumeSource {
  // sources is the list of volume projections
  // +optional
  repeated VolumeProjection sources = 1;

  // defaultMode are the mode bits used to set permissions on created files by default.
  // Must be an octal value between 0000 and 0777 or a decimal value between 0 and 511.
  // YAML accepts both octal and decimal values, JSON requires decimal values for mode bits.
  // Directories within the path are not affected by this setting.
  // This might be in conflict with other options that affect the file
  // mode, like fsGroup, and the result can be other mode bits set.
  // +optional
  optional int32 defaultMode = 2;
}

// Represents a Quobyte mount that lasts the lifetime of a pod.
// Quobyte volumes do not support ownership management or SELinux relabeling.
message QuobyteVolumeSource {
  // registry represents a single or multiple Quobyte Registry services
  // specified as a string as host:port pair (multiple entries are separated with commas)
  // which acts as the central registry for volumes
  optional string registry = 1;

  // volume is a string that references an already created Quobyte volume by name.
  optional string volume = 2;

  // readOnly here will force the Quobyte volume to be mounted with read-only permissions.
  // Defaults to false.
  // +optional
  optional bool readOnly = 3;

  // user to map volume access to
  // Defaults to serivceaccount user
  // +optional
  optional string user = 4;

  // group to map volume access to
  // Default is no group
  // +optional
  optional string group = 5;

  // tenant owning the given Quobyte volume in the Backend
  // Used with dynamically provisioned Quobyte volumes, value is set by the plugin
  // +optional
  optional string tenant = 6;
}

// Represents a Rados Block Device mount that lasts the lifetime of a pod.
// RBD volumes support ownership management and SELinux relabeling.
message RBDPersistentVolumeSource {
  // monitors is a collection of Ceph monitors.
  // More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it
  repeated string monitors = 1;

  // image is the rados image name.
  // More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it
  optional string image = 2;

  // fsType is the filesystem type of the volume that you want to mount.
  // Tip: Ensure that the filesystem type is supported by the host operating system.
  // Examples: "ext4", "xfs", "ntfs". Implicitly inferred to be "ext4" if unspecified.
  // More info: https://kubernetes.io/docs/concepts/storage/volumes#rbd
  // TODO: how do we prevent errors in the filesystem from compromising the machine
  // +optional
  optional string fsType = 3;

  // pool is the rados pool name.
  // Default is rbd.
  // More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it
  // +optional
  optional string pool = 4;

  // user is the rados user name.
  // Default is admin.
  // More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it
  // +optional
  optional string user = 5;

  // keyring is the path to key ring for RBDUser.
  // Default is /etc/ceph/keyring.
  // More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it
  // +optional
  optional string keyring = 6;

  // secretRef is name of the authentication secret for RBDUser. If provided
  // overrides keyring.
  // Default is nil.
  // More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it
  // +optional
  optional SecretReference secretRef = 7;

  // readOnly here will force the ReadOnly setting in VolumeMounts.
  // Defaults to false.
  // More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it
  // +optional
  optional bool readOnly = 8;
}

// Represents a Rados Block Device mount that lasts the lifetime of a pod.
// RBD volumes support ownership management and SELinux relabeling.
message RBDVolumeSource {
  // monitors is a collection of Ceph monitors.
  // More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it
  repeated string monitors = 1;

  // image is the rados image name.
  // More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it
  optional string image = 2;

  // fsType is the filesystem type of the volume that you want to mount.
  // Tip: Ensure that the filesystem type is supported by the host operating system.
  // Examples: "ext4", "xfs", "ntfs". Implicitly inferred to be "ext4" if unspecified.
  // More info: https://kubernetes.io/docs/concepts/storage/volumes#rbd
  // TODO: how do we prevent errors in the filesystem from compromising the machine
  // +optional
  optional string fsType = 3;

  // pool is the rados pool name.
  // Default is rbd.
  // More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it
  // +optional
  optional string pool = 4;

  // user is the rados user name.
  // Default is admin.
  // More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it
  // +optional
  optional string user = 5;

  // keyring is the path to key ring for RBDUser.
  // Default is /etc/ceph/keyring.
  // More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it
  // +optional
  optional string keyring = 6;

  // secretRef is name of the authentication secret for RBDUser. If provided
  // overrides keyring.
  // Default is nil.
  // More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it
  // +optional
  optional LocalObjectReference secretRef = 7;

  // readOnly here will force the ReadOnly setting in VolumeMounts.
  // Defaults to false.
  // More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it
  // +optional
  optional bool readOnly = 8;
}

// RangeAllocation is not a public type.
message RangeAllocation {
  // Standard object's metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Range is string that identifies the range represented by 'data'.
  optional string range = 2;

  // Data is a bit array containing all allocated addresses in the previous segment.
  optional bytes data = 3;
}

// ReplicationController represents the configuration of a replication controller.
message ReplicationController {
  // If the Labels of a ReplicationController are empty, they are defaulted to
  // be the same as the Pod(s) that the replication controller manages.
  // Standard object's metadata. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Spec defines the specification of the desired behavior of the replication controller.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status
  // +optional
  optional ReplicationControllerSpec spec = 2;

  // Status is the most recently observed status of the replication controller.
  // This data may be out of date by some window of time.
  // Populated by the system.
  // Read-only.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status
  // +optional
  optional ReplicationControllerStatus status = 3;
}

// ReplicationControllerCondition describes the state of a replication controller at a certain point.
message ReplicationControllerCondition {
  // Type of replication controller condition.
  optional string type = 1;

  // Status of the condition, one of True, False, Unknown.
  optional string status = 2;

  // The last time the condition transitioned from one status to another.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time lastTransitionTime = 3;

  // The reason for the condition's last transition.
  // +optional
  optional string reason = 4;

  // A human readable message indicating details about the transition.
  // +optional
  optional string message = 5;
}

// ReplicationControllerList is a collection of replication controllers.
message ReplicationControllerList {
  // Standard list metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // List of replication controllers.
  // More info: https://kubernetes.io/docs/concepts/workloads/controllers/replicationcontroller
  repeated ReplicationController items = 2;
}

// ReplicationControllerSpec is the specification of a replication controller.
message ReplicationControllerSpec {
  // Replicas is the number of desired replicas.
  // This is a pointer to distinguish between explicit zero and unspecified.
  // Defaults to 1.
  // More info: https://kubernetes.io/docs/concepts/workloads/controllers/replicationcontroller#what-is-a-replicationcontroller
  // +optional
  optional int32 replicas = 1;

  // Minimum number of seconds for which a newly created pod should be ready
  // without any of its container crashing, for it to be considered available.
  // Defaults to 0 (pod will be considered available as soon as it is ready)
  // +optional
  optional int32 minReadySeconds = 4;

  // Selector is a label query over pods that should match the Replicas count.
  // If Selector is empty, it is defaulted to the labels present on the Pod template.
  // Label keys and values that must match in order to be controlled by this replication
  // controller, if empty defaulted to labels on Pod template.
  // More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/labels/#label-selectors
  // +optional
  // +mapType=atomic
  map<string, string> selector = 2;

  // Template is the object that describes the pod that will be created if
  // insufficient replicas are detected. This takes precedence over a TemplateRef.
  // The only allowed template.spec.restartPolicy value is "Always".
  // More info: https://kubernetes.io/docs/concepts/workloads/controllers/replicationcontroller#pod-template
  // +optional
  optional PodTemplateSpec template = 3;
}

// ReplicationControllerStatus represents the current status of a replication
// controller.
message ReplicationControllerStatus {
  // Replicas is the most recently observed number of replicas.
  // More info: https://kubernetes.io/docs/concepts/workloads/controllers/replicationcontroller#what-is-a-replicationcontroller
  optional int32 replicas = 1;

  // The number of pods that have labels matching the labels of the pod template of the replication controller.
  // +optional
  optional int32 fullyLabeledReplicas = 2;

  // The number of ready replicas for this replication controller.
  // +optional
  optional int32 readyReplicas = 4;

  // The number of available replicas (ready for at least minReadySeconds) for this replication controller.
  // +optional
  optional int32 availableReplicas = 5;

  // ObservedGeneration reflects the generation of the most recently observed replication controller.
  // +optional
  optional int64 observedGeneration = 3;

  // Represents the latest available observations of a replication controller's current state.
  // +optional
  // +patchMergeKey=type
  // +patchStrategy=merge
  repeated ReplicationControllerCondition conditions = 6;
}

// ResourceClaim references one entry in PodSpec.ResourceClaims.
message ResourceClaim {
  // Name must match the name of one entry in pod.spec.resourceClaims of
  // the Pod where this field is used. It makes that resource available
  // inside a container.
  optional string name = 1;
}

// ResourceFieldSelector represents container resources (cpu, memory) and their output format
// +structType=atomic
message ResourceFieldSelector {
  // Container name: required for volumes, optional for env vars
  // +optional
  optional string containerName = 1;

  // Required: resource to select
  optional string resource = 2;

  // Specifies the output format of the exposed resources, defaults to "1"
  // +optional
  optional k8s.io.apimachinery.pkg.api.resource.Quantity divisor = 3;
}

// ResourceQuota sets aggregate quota restrictions enforced per namespace
message ResourceQuota {
  // Standard object's metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Spec defines the desired quota.
  // https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status
  // +optional
  optional ResourceQuotaSpec spec = 2;

  // Status defines the actual enforced quota and its current usage.
  // https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status
  // +optional
  optional ResourceQuotaStatus status = 3;
}

// ResourceQuotaList is a list of ResourceQuota items.
message ResourceQuotaList {
  // Standard list metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // Items is a list of ResourceQuota objects.
  // More info: https://kubernetes.io/docs/concepts/policy/resource-quotas/
  repeated ResourceQuota items = 2;
}

// ResourceQuotaSpec defines the desired hard limits to enforce for Quota.
message ResourceQuotaSpec {
  // hard is the set of desired hard limits for each named resource.
  // More info: https://kubernetes.io/docs/concepts/policy/resource-quotas/
  // +optional
  map<string, k8s.io.apimachinery.pkg.api.resource.Quantity> hard = 1;

  // A collection of filters that must match each object tracked by a quota.
  // If not specified, the quota matches all objects.
  // +optional
  repeated string scopes = 2;

  // scopeSelector is also a collection of filters like scopes that must match each object tracked by a quota
  // but expressed using ScopeSelectorOperator in combination with possible values.
  // For a resource to match, both scopes AND scopeSelector (if specified in spec), must be matched.
  // +optional
  optional ScopeSelector scopeSelector = 3;
}

// ResourceQuotaStatus defines the enforced hard limits and observed use.
message ResourceQuotaStatus {
  // Hard is the set of enforced hard limits for each named resource.
  // More info: https://kubernetes.io/docs/concepts/policy/resource-quotas/
  // +optional
  map<string, k8s.io.apimachinery.pkg.api.resource.Quantity> hard = 1;

  // Used is the current observed total usage of the resource in the namespace.
  // +optional
  map<string, k8s.io.apimachinery.pkg.api.resource.Quantity> used = 2;
}

// ResourceRequirements describes the compute resource requirements.
message ResourceRequirements {
  // Limits describes the maximum amount of compute resources allowed.
  // More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/
  // +optional
  map<string, k8s.io.apimachinery.pkg.api.resource.Quantity> limits = 1;

  // Requests describes the minimum amount of compute resources required.
  // If Requests is omitted for a container, it defaults to Limits if that is explicitly specified,
  // otherwise to an implementation-defined value. Requests cannot exceed Limits.
  // More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/
  // +optional
  map<string, k8s.io.apimachinery.pkg.api.resource.Quantity> requests = 2;

  // Claims lists the names of resources, defined in spec.resourceClaims,
  // that are used by this container.
  //
  // This is an alpha field and requires enabling the
  // DynamicResourceAllocation feature gate.
  //
  // This field is immutable. It can only be set for containers.
  //
  // +listType=map
  // +listMapKey=name
  // +featureGate=DynamicResourceAllocation
  // +optional
  repeated ResourceClaim claims = 3;
}

// SELinuxOptions are the labels to be applied to the container
message SELinuxOptions {
  // User is a SELinux user label that applies to the container.
  // +optional
  optional string user = 1;

  // Role is a SELinux role label that applies to the container.
  // +optional
  optional string role = 2;

  // Type is a SELinux type label that applies to the container.
  // +optional
  optional string type = 3;

  // Level is SELinux level label that applies to the container.
  // +optional
  optional string level = 4;
}

// ScaleIOPersistentVolumeSource represents a persistent ScaleIO volume
message ScaleIOPersistentVolumeSource {
  // gateway is the host address of the ScaleIO API Gateway.
  optional string gateway = 1;

  // system is the name of the storage system as configured in ScaleIO.
  optional string system = 2;

  // secretRef references to the secret for ScaleIO user and other
  // sensitive information. If this is not provided, Login operation will fail.
  optional SecretReference secretRef = 3;

  // sslEnabled is the flag to enable/disable SSL communication with Gateway, default false
  // +optional
  optional bool sslEnabled = 4;

  // protectionDomain is the name of the ScaleIO Protection Domain for the configured storage.
  // +optional
  optional string protectionDomain = 5;

  // storagePool is the ScaleIO Storage Pool associated with the protection domain.
  // +optional
  optional string storagePool = 6;

  // storageMode indicates whether the storage for a volume should be ThickProvisioned or ThinProvisioned.
  // Default is ThinProvisioned.
  // +optional
  optional string storageMode = 7;

  // volumeName is the name of a volume already created in the ScaleIO system
  // that is associated with this volume source.
  optional string volumeName = 8;

  // fsType is the filesystem type to mount.
  // Must be a filesystem type supported by the host operating system.
  // Ex. "ext4", "xfs", "ntfs".
  // Default is "xfs"
  // +optional
  optional string fsType = 9;

  // readOnly defaults to false (read/write). ReadOnly here will force
  // the ReadOnly setting in VolumeMounts.
  // +optional
  optional bool readOnly = 10;
}

// ScaleIOVolumeSource represents a persistent ScaleIO volume
message ScaleIOVolumeSource {
  // gateway is the host address of the ScaleIO API Gateway.
  optional string gateway = 1;

  // system is the name of the storage system as configured in ScaleIO.
  optional string system = 2;

  // secretRef references to the secret for ScaleIO user and other
  // sensitive information. If this is not provided, Login operation will fail.
  optional LocalObjectReference secretRef = 3;

  // sslEnabled Flag enable/disable SSL communication with Gateway, default false
  // +optional
  optional bool sslEnabled = 4;

  // protectionDomain is the name of the ScaleIO Protection Domain for the configured storage.
  // +optional
  optional string protectionDomain = 5;

  // storagePool is the ScaleIO Storage Pool associated with the protection domain.
  // +optional
  optional string storagePool = 6;

  // storageMode indicates whether the storage for a volume should be ThickProvisioned or ThinProvisioned.
  // Default is ThinProvisioned.
  // +optional
  optional string storageMode = 7;

  // volumeName is the name of a volume already created in the ScaleIO system
  // that is associated with this volume source.
  optional string volumeName = 8;

  // fsType is the filesystem type to mount.
  // Must be a filesystem type supported by the host operating system.
  // Ex. "ext4", "xfs", "ntfs".
  // Default is "xfs".
  // +optional
  optional string fsType = 9;

  // readOnly Defaults to false (read/write). ReadOnly here will force
  // the ReadOnly setting in VolumeMounts.
  // +optional
  optional bool readOnly = 10;
}

// A scope selector represents the AND of the selectors represented
// by the scoped-resource selector requirements.
// +structType=atomic
message ScopeSelector {
  // A list of scope selector requirements by scope of the resources.
  // +optional
  repeated ScopedResourceSelectorRequirement matchExpressions = 1;
}

// A scoped-resource selector requirement is a selector that contains values, a scope name, and an operator
// that relates the scope name and values.
message ScopedResourceSelectorRequirement {
  // The name of the scope that the selector applies to.
  optional string scopeName = 1;

  // Represents a scope's relationship to a set of values.
  // Valid operators are In, NotIn, Exists, DoesNotExist.
  optional string operator = 2;

  // An array of string values. If the operator is In or NotIn,
  // the values array must be non-empty. If the operator is Exists or DoesNotExist,
  // the values array must be empty.
  // This array is replaced during a strategic merge patch.
  // +optional
  repeated string values = 3;
}

// SeccompProfile defines a pod/container's seccomp profile settings.
// Only one profile source may be set.
// +union
message SeccompProfile {
  // type indicates which kind of seccomp profile will be applied.
  // Valid options are:
  //
  // Localhost - a profile defined in a file on the node should be used.
  // RuntimeDefault - the container runtime default profile should be used.
  // Unconfined - no profile should be applied.
  // +unionDiscriminator
  optional string type = 1;

  // localhostProfile indicates a profile defined in a file on the node should be used.
  // The profile must be preconfigured on the node to work.
  // Must be a descending path, relative to the kubelet's configured seccomp profile location.
  // Must be set if type is "Localhost". Must NOT be set for any other type.
  // +optional
  optional string localhostProfile = 2;
}

// Secret holds secret data of a certain type. The total bytes of the values in
// the Data field must be less than MaxSecretSize bytes.
message Secret {
  // Standard object's metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Immutable, if set to true, ensures that data stored in the Secret cannot
  // be updated (only object metadata can be modified).
  // If not set to true, the field can be modified at any time.
  // Defaulted to nil.
  // +optional
  optional bool immutable = 5;

  // Data contains the secret data. Each key must consist of alphanumeric
  // characters, '-', '_' or '.'. The serialized form of the secret data is a
  // base64 encoded string, representing the arbitrary (possibly non-string)
  // data value here. Described in https://tools.ietf.org/html/rfc4648#section-4
  // +optional
  map<string, bytes> data = 2;

  // stringData allows specifying non-binary secret data in string form.
  // It is provided as a write-only input field for convenience.
  // All keys and values are merged into the data field on write, overwriting any existing values.
  // The stringData field is never output when reading from the API.
  // +k8s:conversion-gen=false
  // +optional
  map<string, string> stringData = 4;

  // Used to facilitate programmatic handling of secret data.
  // More info: https://kubernetes.io/docs/concepts/configuration/secret/#secret-types
  // +optional
  optional string type = 3;
}

// SecretEnvSource selects a Secret to populate the environment
// variables with.
//
// The contents of the target Secret's Data field will represent the
// key-value pairs as environment variables.
message SecretEnvSource {
  // The Secret to select from.
  optional LocalObjectReference localObjectReference = 1;

  // Specify whether the Secret must be defined
  // +optional
  optional bool optional = 2;
}

// SecretKeySelector selects a key of a Secret.
// +structType=atomic
message SecretKeySelector {
  // The name of the secret in the pod's namespace to select from.
  optional LocalObjectReference localObjectReference = 1;

  // The key of the secret to select from.  Must be a valid secret key.
  optional string key = 2;

  // Specify whether the Secret or its key must be defined
  // +optional
  optional bool optional = 3;
}

// SecretList is a list of Secret.
message SecretList {
  // Standard list metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // Items is a list of secret objects.
  // More info: https://kubernetes.io/docs/concepts/configuration/secret
  repeated Secret items = 2;
}

// Adapts a secret into a projected volume.
//
// The contents of the target Secret's Data field will be presented in a
// projected volume as files using the keys in the Data field as the file names.
// Note that this is identical to a secret volume source without the default
// mode.
message SecretProjection {
  optional LocalObjectReference localObjectReference = 1;

  // items if unspecified, each key-value pair in the Data field of the referenced
  // Secret will be projected into the volume as a file whose name is the
  // key and content is the value. If specified, the listed keys will be
  // projected into the specified paths, and unlisted keys will not be
  // present. If a key is specified which is not present in the Secret,
  // the volume setup will error unless it is marked optional. Paths must be
  // relative and may not contain the '..' path or start with '..'.
  // +optional
  repeated KeyToPath items = 2;

  // optional field specify whether the Secret or its key must be defined
  // +optional
  optional bool optional = 4;
}

// SecretReference represents a Secret Reference. It has enough information to retrieve secret
// in any namespace
// +structType=atomic
message SecretReference {
  // name is unique within a namespace to reference a secret resource.
  // +optional
  optional string name = 1;

  // namespace defines the space within which the secret name must be unique.
  // +optional
  optional string namespace = 2;
}

// Adapts a Secret into a volume.
//
// The contents of the target Secret's Data field will be presented in a volume
// as files using the keys in the Data field as the file names.
// Secret volumes support ownership management and SELinux relabeling.
message SecretVolumeSource {
  // secretName is the name of the secret in the pod's namespace to use.
  // More info: https://kubernetes.io/docs/concepts/storage/volumes#secret
  // +optional
  optional string secretName = 1;

  // items If unspecified, each key-value pair in the Data field of the referenced
  // Secret will be projected into the volume as a file whose name is the
  // key and content is the value. If specified, the listed keys will be
  // projected into the specified paths, and unlisted keys will not be
  // present. If a key is specified which is not present in the Secret,
  // the volume setup will error unless it is marked optional. Paths must be
  // relative and may not contain the '..' path or start with '..'.
  // +optional
  repeated KeyToPath items = 2;

  // defaultMode is Optional: mode bits used to set permissions on created files by default.
  // Must be an octal value between 0000 and 0777 or a decimal value between 0 and 511.
  // YAML accepts both octal and decimal values, JSON requires decimal values
  // for mode bits. Defaults to 0644.
  // Directories within the path are not affected by this setting.
  // This might be in conflict with other options that affect the file
  // mode, like fsGroup, and the result can be other mode bits set.
  // +optional
  optional int32 defaultMode = 3;

  // optional field specify whether the Secret or its keys must be defined
  // +optional
  optional bool optional = 4;
}

// SecurityContext holds security configuration that will be applied to a container.
// Some fields are present in both SecurityContext and PodSecurityContext.  When both
// are set, the values in SecurityContext take precedence.
message SecurityContext {
  // The capabilities to add/drop when running containers.
  // Defaults to the default set of capabilities granted by the container runtime.
  // Note that this field cannot be set when spec.os.name is windows.
  // +optional
  optional Capabilities capabilities = 1;

  // Run container in privileged mode.
  // Processes in privileged containers are essentially equivalent to root on the host.
  // Defaults to false.
  // Note that this field cannot be set when spec.os.name is windows.
  // +optional
  optional bool privileged = 2;

  // The SELinux context to be applied to the container.
  // If unspecified, the container runtime will allocate a random SELinux context for each
  // container.  May also be set in PodSecurityContext.  If set in both SecurityContext and
  // PodSecurityContext, the value specified in SecurityContext takes precedence.
  // Note that this field cannot be set when spec.os.name is windows.
  // +optional
  optional SELinuxOptions seLinuxOptions = 3;

  // The Windows specific settings applied to all containers.
  // If unspecified, the options from the PodSecurityContext will be used.
  // If set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence.
  // Note that this field cannot be set when spec.os.name is linux.
  // +optional
  optional WindowsSecurityContextOptions windowsOptions = 10;

  // The UID to run the entrypoint of the container process.
  // Defaults to user specified in image metadata if unspecified.
  // May also be set in PodSecurityContext.  If set in both SecurityContext and
  // PodSecurityContext, the value specified in SecurityContext takes precedence.
  // Note that this field cannot be set when spec.os.name is windows.
  // +optional
  optional int64 runAsUser = 4;

  // The GID to run the entrypoint of the container process.
  // Uses runtime default if unset.
  // May also be set in PodSecurityContext.  If set in both SecurityContext and
  // PodSecurityContext, the value specified in SecurityContext takes precedence.
  // Note that this field cannot be set when spec.os.name is windows.
  // +optional
  optional int64 runAsGroup = 8;

  // Indicates that the container must run as a non-root user.
  // If true, the Kubelet will validate the image at runtime to ensure that it
  // does not run as UID 0 (root) and fail to start the container if it does.
  // If unset or false, no such validation will be performed.
  // May also be set in PodSecurityContext.  If set in both SecurityContext and
  // PodSecurityContext, the value specified in SecurityContext takes precedence.
  // +optional
  optional bool runAsNonRoot = 5;

  // Whether this container has a read-only root filesystem.
  // Default is false.
  // Note that this field cannot be set when spec.os.name is windows.
  // +optional
  optional bool readOnlyRootFilesystem = 6;

  // AllowPrivilegeEscalation controls whether a process can gain more
  // privileges than its parent process. This bool directly controls if
  // the no_new_privs flag will be set on the container process.
  // AllowPrivilegeEscalation is true always when the container is:
  // 1) run as Privileged
  // 2) has CAP_SYS_ADMIN
  // Note that this field cannot be set when spec.os.name is windows.
  // +optional
  optional bool allowPrivilegeEscalation = 7;

  // procMount denotes the type of proc mount to use for the containers.
  // The default is DefaultProcMount which uses the container runtime defaults for
  // readonly paths and masked paths.
  // This requires the ProcMountType feature flag to be enabled.
  // Note that this field cannot be set when spec.os.name is windows.
  // +optional
  optional string procMount = 9;

  // The seccomp options to use by this container. If seccomp options are
  // provided at both the pod & container level, the container options
  // override the pod options.
  // Note that this field cannot be set when spec.os.name is windows.
  // +optional
  optional SeccompProfile seccompProfile = 11;
}

// SerializedReference is a reference to serialized object.
message SerializedReference {
  // The reference to an object in the system.
  // +optional
  optional ObjectReference reference = 1;
}

// Service is a named abstraction of software service (for example, mysql) consisting of local port
// (for example 3306) that the proxy listens on, and the selector that determines which pods
// will answer requests sent through the proxy.
message Service {
  // Standard object's metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Spec defines the behavior of a service.
  // https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status
  // +optional
  optional ServiceSpec spec = 2;

  // Most recently observed status of the service.
  // Populated by the system.
  // Read-only.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status
  // +optional
  optional ServiceStatus status = 3;
}

// ServiceAccount binds together:
// * a name, understood by users, and perhaps by peripheral systems, for an identity
// * a principal that can be authenticated and authorized
// * a set of secrets
message ServiceAccount {
  // Standard object's metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Secrets is a list of the secrets in the same namespace that pods running using this ServiceAccount are allowed to use.
  // Pods are only limited to this list if this service account has a "kubernetes.io/enforce-mountable-secrets" annotation set to "true".
  // This field should not be used to find auto-generated service account token secrets for use outside of pods.
  // Instead, tokens can be requested directly using the TokenRequest API, or service account token secrets can be manually created.
  // More info: https://kubernetes.io/docs/concepts/configuration/secret
  // +optional
  // +patchMergeKey=name
  // +patchStrategy=merge
  repeated ObjectReference secrets = 2;

  // ImagePullSecrets is a list of references to secrets in the same namespace to use for pulling any images
  // in pods that reference this ServiceAccount. ImagePullSecrets are distinct from Secrets because Secrets
  // can be mounted in the pod, but ImagePullSecrets are only accessed by the kubelet.
  // More info: https://kubernetes.io/docs/concepts/containers/images/#specifying-imagepullsecrets-on-a-pod
  // +optional
  repeated LocalObjectReference imagePullSecrets = 3;

  // AutomountServiceAccountToken indicates whether pods running as this service account should have an API token automatically mounted.
  // Can be overridden at the pod level.
  // +optional
  optional bool automountServiceAccountToken = 4;
}

// ServiceAccountList is a list of ServiceAccount objects
message ServiceAccountList {
  // Standard list metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // List of ServiceAccounts.
  // More info: https://kubernetes.io/docs/tasks/configure-pod-container/configure-service-account/
  repeated ServiceAccount items = 2;
}

// ServiceAccountTokenProjection represents a projected service account token
// volume. This projection can be used to insert a service account token into
// the pods runtime filesystem for use against APIs (Kubernetes API Server or
// otherwise).
message ServiceAccountTokenProjection {
  // audience is the intended audience of the token. A recipient of a token
  // must identify itself with an identifier specified in the audience of the
  // token, and otherwise should reject the token. The audience defaults to the
  // identifier of the apiserver.
  // +optional
  optional string audience = 1;

  // expirationSeconds is the requested duration of validity of the service
  // account token. As the token approaches expiration, the kubelet volume
  // plugin will proactively rotate the service account token. The kubelet will
  // start trying to rotate the token if the token is older than 80 percent of
  // its time to live or if the token is older than 24 hours.Defaults to 1 hour
  // and must be at least 10 minutes.
  // +optional
  optional int64 expirationSeconds = 2;

  // path is the path relative to the mount point of the file to project the
  // token into.
  optional string path = 3;
}

// ServiceList holds a list of services.
message ServiceList {
  // Standard list metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // List of services
  repeated Service items = 2;
}

// ServicePort contains information on service's port.
message ServicePort {
  // The name of this port within the service. This must be a DNS_LABEL.
  // All ports within a ServiceSpec must have unique names. When considering
  // the endpoints for a Service, this must match the 'name' field in the
  // EndpointPort.
  // Optional if only one ServicePort is defined on this service.
  // +optional
  optional string name = 1;

  // The IP protocol for this port. Supports "TCP", "UDP", and "SCTP".
  // Default is TCP.
  // +default="TCP"
  // +optional
  optional string protocol = 2;

  // The application protocol for this port.
  // This is used as a hint for implementations to offer richer behavior for protocols that they understand.
  // This field follows standard Kubernetes label syntax.
  // Valid values are either:
  //
  // * Un-prefixed protocol names - reserved for IANA standard service names (as per
  // RFC-6335 and https://www.iana.org/assignments/service-names).
  //
  // * Kubernetes-defined prefixed names:
  //   * 'kubernetes.io/h2c' - HTTP/2 over cleartext as described in https://www.rfc-editor.org/rfc/rfc7540
  //   * 'kubernetes.io/ws'  - WebSocket over cleartext as described in https://www.rfc-editor.org/rfc/rfc6455
  //   * 'kubernetes.io/wss' - WebSocket over TLS as described in https://www.rfc-editor.org/rfc/rfc6455
  //
  // * Other protocols should use implementation-defined prefixed names such as
  // mycompany.com/my-custom-protocol.
  // +optional
  optional string appProtocol = 6;

  // The port that will be exposed by this service.
  optional int32 port = 3;

  // Number or name of the port to access on the pods targeted by the service.
  // Number must be in the range 1 to 65535. Name must be an IANA_SVC_NAME.
  // If this is a string, it will be looked up as a named port in the
  // target Pod's container ports. If this is not specified, the value
  // of the 'port' field is used (an identity map).
  // This field is ignored for services with clusterIP=None, and should be
  // omitted or set equal to the 'port' field.
  // More info: https://kubernetes.io/docs/concepts/services-networking/service/#defining-a-service
  // +optional
  optional k8s.io.apimachinery.pkg.util.intstr.IntOrString targetPort = 4;

  // The port on each node on which this service is exposed when type is
  // NodePort or LoadBalancer.  Usually assigned by the system. If a value is
  // specified, in-range, and not in use it will be used, otherwise the
  // operation will fail.  If not specified, a port will be allocated if this
  // Service requires one.  If this field is specified when creating a
  // Service which does not need it, creation will fail. This field will be
  // wiped when updating a Service to no longer need it (e.g. changing type
  // from NodePort to ClusterIP).
  // More info: https://kubernetes.io/docs/concepts/services-networking/service/#type-nodeport
  // +optional
  optional int32 nodePort = 5;
}

// ServiceProxyOptions is the query options to a Service's proxy call.
message ServiceProxyOptions {
  // Path is the part of URLs that include service endpoints, suffixes,
  // and parameters to use for the current proxy request to service.
  // For example, the whole request URL is
  // http://localhost/api/v1/namespaces/kube-system/services/elasticsearch-logging/_search?q=user:kimchy.
  // Path is _search?q=user:kimchy.
  // +optional
  optional string path = 1;
}

// ServiceSpec describes the attributes that a user creates on a service.
message ServiceSpec {
  // The list of ports that are exposed by this service.
  // More info: https://kubernetes.io/docs/concepts/services-networking/service/#virtual-ips-and-service-proxies
  // +patchMergeKey=port
  // +patchStrategy=merge
  // +listType=map
  // +listMapKey=port
  // +listMapKey=protocol
  repeated ServicePort ports = 1;

  // Route service traffic to pods with label keys and values matching this
  // selector. If empty or not present, the service is assumed to have an
  // external process managing its endpoints, which Kubernetes will not
  // modify. Only applies to types ClusterIP, NodePort, and LoadBalancer.
  // Ignored if type is ExternalName.
  // More info: https://kubernetes.io/docs/concepts/services-networking/service/
  // +optional
  // +mapType=atomic
  map<string, string> selector = 2;

  // clusterIP is the IP address of the service and is usually assigned
  // randomly. If an address is specified manually, is in-range (as per
  // system configuration), and is not in use, it will be allocated to the
  // service; otherwise creation of the service will fail. This field may not
  // be changed through updates unless the type field is also being changed
  // to ExternalName (which requires this field to be blank) or the type
  // field is being changed from ExternalName (in which case this field may
  // optionally be specified, as describe above).  Valid values are "None",
  // empty string (""), or a valid IP address. Setting this to "None" makes a
  // "headless service" (no virtual IP), which is useful when direct endpoint
  // connections are preferred and proxying is not required.  Only applies to
  // types ClusterIP, NodePort, and LoadBalancer. If this field is specified
  // when creating a Service of type ExternalName, creation will fail. This
  // field will be wiped when updating a Service to type ExternalName.
  // More info: https://kubernetes.io/docs/concepts/services-networking/service/#virtual-ips-and-service-proxies
  // +optional
  optional string clusterIP = 3;

  // ClusterIPs is a list of IP addresses assigned to this service, and are
  // usually assigned randomly.  If an address is specified manually, is
  // in-range (as per system configuration), and is not in use, it will be
  // allocated to the service; otherwise creation of the service will fail.
  // This field may not be changed through updates unless the type field is
  // also being changed to ExternalName (which requires this field to be
  // empty) or the type field is being changed from ExternalName (in which
  // case this field may optionally be specified, as describe above).  Valid
  // values are "None", empty string (""), or a valid IP address.  Setting
  // this to "None" makes a "headless service" (no virtual IP), which is
  // useful when direct endpoint connections are preferred and proxying is
  // not required.  Only applies to types ClusterIP, NodePort, and
  // LoadBalancer. If this field is specified when creating a Service of type
  // ExternalName, creation will fail. This field will be wiped when updating
  // a Service to type ExternalName.  If this field is not specified, it will
  // be initialized from the clusterIP field.  If this field is specified,
  // clients must ensure that clusterIPs[0] and clusterIP have the same
  // value.
  //
  // This field may hold a maximum of two entries (dual-stack IPs, in either order).
  // These IPs must correspond to the values of the ipFamilies field. Both
  // clusterIPs and ipFamilies are governed by the ipFamilyPolicy field.
  // More info: https://kubernetes.io/docs/concepts/services-networking/service/#virtual-ips-and-service-proxies
  // +listType=atomic
  // +optional
  repeated string clusterIPs = 18;

  // type determines how the Service is exposed. Defaults to ClusterIP. Valid
  // options are ExternalName, ClusterIP, NodePort, and LoadBalancer.
  // "ClusterIP" allocates a cluster-internal IP address for load-balancing
  // to endpoints. Endpoints are determined by the selector or if that is not
  // specified, by manual construction of an Endpoints object or
  // EndpointSlice objects. If clusterIP is "None", no virtual IP is
  // allocated and the endpoints are published as a set of endpoints rather
  // than a virtual IP.
  // "NodePort" builds on ClusterIP and allocates a port on every node which
  // routes to the same endpoints as the clusterIP.
  // "LoadBalancer" builds on NodePort and creates an external load-balancer
  // (if supported in the current cloud) which routes to the same endpoints
  // as the clusterIP.
  // "ExternalName" aliases this service to the specified externalName.
  // Several other fields do not apply to ExternalName services.
  // More info: https://kubernetes.io/docs/concepts/services-networking/service/#publishing-services-service-types
  // +optional
  optional string type = 4;

  // externalIPs is a list of IP addresses for which nodes in the cluster
  // will also accept traffic for this service.  These IPs are not managed by
  // Kubernetes.  The user is responsible for ensuring that traffic arrives
  // at a node with this IP.  A common example is external load-balancers
  // that are not part of the Kubernetes system.
  // +optional
  repeated string externalIPs = 5;

  // Supports "ClientIP" and "None". Used to maintain session affinity.
  // Enable client IP based session affinity.
  // Must be ClientIP or None.
  // Defaults to None.
  // More info: https://kubernetes.io/docs/concepts/services-networking/service/#virtual-ips-and-service-proxies
  // +optional
  optional string sessionAffinity = 7;

  // Only applies to Service Type: LoadBalancer.
  // This feature depends on whether the underlying cloud-provider supports specifying
  // the loadBalancerIP when a load balancer is created.
  // This field will be ignored if the cloud-provider does not support the feature.
  // Deprecated: This field was under-specified and its meaning varies across implementations.
  // Using it is non-portable and it may not support dual-stack.
  // Users are encouraged to use implementation-specific annotations when available.
  // +optional
  optional string loadBalancerIP = 8;

  // If specified and supported by the platform, this will restrict traffic through the cloud-provider
  // load-balancer will be restricted to the specified client IPs. This field will be ignored if the
  // cloud-provider does not support the feature."
  // More info: https://kubernetes.io/docs/tasks/access-application-cluster/create-external-load-balancer/
  // +optional
  repeated string loadBalancerSourceRanges = 9;

  // externalName is the external reference that discovery mechanisms will
  // return as an alias for this service (e.g. a DNS CNAME record). No
  // proxying will be involved.  Must be a lowercase RFC-1123 hostname
  // (https://tools.ietf.org/html/rfc1123) and requires `type` to be "ExternalName".
  // +optional
  optional string externalName = 10;

  // externalTrafficPolicy describes how nodes distribute service traffic they
  // receive on one of the Service's "externally-facing" addresses (NodePorts,
  // ExternalIPs, and LoadBalancer IPs). If set to "Local", the proxy will configure
  // the service in a way that assumes that external load balancers will take care
  // of balancing the service traffic between nodes, and so each node will deliver
  // traffic only to the node-local endpoints of the service, without masquerading
  // the client source IP. (Traffic mistakenly sent to a node with no endpoints will
  // be dropped.) The default value, "Cluster", uses the standard behavior of
  // routing to all endpoints evenly (possibly modified by topology and other
  // features). Note that traffic sent to an External IP or LoadBalancer IP from
  // within the cluster will always get "Cluster" semantics, but clients sending to
  // a NodePort from within the cluster may need to take traffic policy into account
  // when picking a node.
  // +optional
  optional string externalTrafficPolicy = 11;

  // healthCheckNodePort specifies the healthcheck nodePort for the service.
  // This only applies when type is set to LoadBalancer and
  // externalTrafficPolicy is set to Local. If a value is specified, is
  // in-range, and is not in use, it will be used.  If not specified, a value
  // will be automatically allocated.  External systems (e.g. load-balancers)
  // can use this port to determine if a given node holds endpoints for this
  // service or not.  If this field is specified when creating a Service
  // which does not need it, creation will fail. This field will be wiped
  // when updating a Service to no longer need it (e.g. changing type).
  // This field cannot be updated once set.
  // +optional
  optional int32 healthCheckNodePort = 12;

  // publishNotReadyAddresses indicates that any agent which deals with endpoints for this
  // Service should disregard any indications of ready/not-ready.
  // The primary use case for setting this field is for a StatefulSet's Headless Service to
  // propagate SRV DNS records for its Pods for the purpose of peer discovery.
  // The Kubernetes controllers that generate Endpoints and EndpointSlice resources for
  // Services interpret this to mean that all endpoints are considered "ready" even if the
  // Pods themselves are not. Agents which consume only Kubernetes generated endpoints
  // through the Endpoints or EndpointSlice resources can safely assume this behavior.
  // +optional
  optional bool publishNotReadyAddresses = 13;

  // sessionAffinityConfig contains the configurations of session affinity.
  // +optional
  optional SessionAffinityConfig sessionAffinityConfig = 14;

  // IPFamilies is a list of IP families (e.g. IPv4, IPv6) assigned to this
  // service. This field is usually assigned automatically based on cluster
  // configuration and the ipFamilyPolicy field. If this field is specified
  // manually, the requested family is available in the cluster,
  // and ipFamilyPolicy allows it, it will be used; otherwise creation of
  // the service will fail. This field is conditionally mutable: it allows
  // for adding or removing a secondary IP family, but it does not allow
  // changing the primary IP family of the Service. Valid values are "IPv4"
  // and "IPv6".  This field only applies to Services of types ClusterIP,
  // NodePort, and LoadBalancer, and does apply to "headless" services.
  // This field will be wiped when updating a Service to type ExternalName.
  //
  // This field may hold a maximum of two entries (dual-stack families, in
  // either order).  These families must correspond to the values of the
  // clusterIPs field, if specified. Both clusterIPs and ipFamilies are
  // governed by the ipFamilyPolicy field.
  // +listType=atomic
  // +optional
  repeated string ipFamilies = 19;

  // IPFamilyPolicy represents the dual-stack-ness requested or required by
  // this Service. If there is no value provided, then this field will be set
  // to SingleStack. Services can be "SingleStack" (a single IP family),
  // "PreferDualStack" (two IP families on dual-stack configured clusters or
  // a single IP family on single-stack clusters), or "RequireDualStack"
  // (two IP families on dual-stack configured clusters, otherwise fail). The
  // ipFamilies and clusterIPs fields depend on the value of this field. This
  // field will be wiped when updating a service to type ExternalName.
  // +optional
  optional string ipFamilyPolicy = 17;

  // allocateLoadBalancerNodePorts defines if NodePorts will be automatically
  // allocated for services with type LoadBalancer.  Default is "true". It
  // may be set to "false" if the cluster load-balancer does not rely on
  // NodePorts.  If the caller requests specific NodePorts (by specifying a
  // value), those requests will be respected, regardless of this field.
  // This field may only be set for services with type LoadBalancer and will
  // be cleared if the type is changed to any other type.
  // +optional
  optional bool allocateLoadBalancerNodePorts = 20;

  // loadBalancerClass is the class of the load balancer implementation this Service belongs to.
  // If specified, the value of this field must be a label-style identifier, with an optional prefix,
  // e.g. "internal-vip" or "example.com/internal-vip". Unprefixed names are reserved for end-users.
  // This field can only be set when the Service type is 'LoadBalancer'. If not set, the default load
  // balancer implementation is used, today this is typically done through the cloud provider integration,
  // but should apply for any default implementation. If set, it is assumed that a load balancer
  // implementation is watching for Services with a matching class. Any default load balancer
  // implementation (e.g. cloud providers) should ignore Services that set this field.
  // This field can only be set when creating or updating a Service to type 'LoadBalancer'.
  // Once set, it can not be changed. This field will be wiped when a service is updated to a non 'LoadBalancer' type.
  // +optional
  optional string loadBalancerClass = 21;

  // InternalTrafficPolicy describes how nodes distribute service traffic they
  // receive on the ClusterIP. If set to "Local", the proxy will assume that pods
  // only want to talk to endpoints of the service on the same node as the pod,
  // dropping the traffic if there are no local endpoints. The default value,
  // "Cluster", uses the standard behavior of routing to all endpoints evenly
  // (possibly modified by topology and other features).
  // +optional
  optional string internalTrafficPolicy = 22;
}

// ServiceStatus represents the current status of a service.
message ServiceStatus {
  // LoadBalancer contains the current status of the load-balancer,
  // if one is present.
  // +optional
  optional LoadBalancerStatus loadBalancer = 1;

  // Current service state
  // +optional
  // +patchMergeKey=type
  // +patchStrategy=merge
  // +listType=map
  // +listMapKey=type
  repeated k8s.io.apimachinery.pkg.apis.meta.v1.Condition conditions = 2;
}

// SessionAffinityConfig represents the configurations of session affinity.
message SessionAffinityConfig {
  // clientIP contains the configurations of Client IP based session affinity.
  // +optional
  optional ClientIPConfig clientIP = 1;
}

// Represents a StorageOS persistent volume resource.
message StorageOSPersistentVolumeSource {
  // volumeName is the human-readable name of the StorageOS volume.  Volume
  // names are only unique within a namespace.
  optional string volumeName = 1;

  // volumeNamespace specifies the scope of the volume within StorageOS.  If no
  // namespace is specified then the Pod's namespace will be used.  This allows the
  // Kubernetes name scoping to be mirrored within StorageOS for tighter integration.
  // Set VolumeName to any name to override the default behaviour.
  // Set to "default" if you are not using namespaces within StorageOS.
  // Namespaces that do not pre-exist within StorageOS will be created.
  // +optional
  optional string volumeNamespace = 2;

  // fsType is the filesystem type to mount.
  // Must be a filesystem type supported by the host operating system.
  // Ex. "ext4", "xfs", "ntfs". Implicitly inferred to be "ext4" if unspecified.
  // +optional
  optional string fsType = 3;

  // readOnly defaults to false (read/write). ReadOnly here will force
  // the ReadOnly setting in VolumeMounts.
  // +optional
  optional bool readOnly = 4;

  // secretRef specifies the secret to use for obtaining the StorageOS API
  // credentials.  If not specified, default values will be attempted.
  // +optional
  optional ObjectReference secretRef = 5;
}

// Represents a StorageOS persistent volume resource.
message StorageOSVolumeSource {
  // volumeName is the human-readable name of the StorageOS volume.  Volume
  // names are only unique within a namespace.
  optional string volumeName = 1;

  // volumeNamespace specifies the scope of the volume within StorageOS.  If no
  // namespace is specified then the Pod's namespace will be used.  This allows the
  // Kubernetes name scoping to be mirrored within StorageOS for tighter integration.
  // Set VolumeName to any name to override the default behaviour.
  // Set to "default" if you are not using namespaces within StorageOS.
  // Namespaces that do not pre-exist within StorageOS will be created.
  // +optional
  optional string volumeNamespace = 2;

  // fsType is the filesystem type to mount.
  // Must be a filesystem type supported by the host operating system.
  // Ex. "ext4", "xfs", "ntfs". Implicitly inferred to be "ext4" if unspecified.
  // +optional
  optional string fsType = 3;

  // readOnly defaults to false (read/write). ReadOnly here will force
  // the ReadOnly setting in VolumeMounts.
  // +optional
  optional bool readOnly = 4;

  // secretRef specifies the secret to use for obtaining the StorageOS API
  // credentials.  If not specified, default values will be attempted.
  // +optional
  optional LocalObjectReference secretRef = 5;
}

// Sysctl defines a kernel parameter to be set
message Sysctl {
  // Name of a property to set
  optional string name = 1;

  // Value of a property to set
  optional string value = 2;
}

// TCPSocketAction describes an action based on opening a socket
message TCPSocketAction {
  // Number or name of the port to access on the container.
  // Number must be in the range 1 to 65535.
  // Name must be an IANA_SVC_NAME.
  optional k8s.io.apimachinery.pkg.util.intstr.IntOrString port = 1;

  // Optional: Host name to connect to, defaults to the pod IP.
  // +optional
  optional string host = 2;
}

// The node this Taint is attached to has the "effect" on
// any pod that does not tolerate the Taint.
message Taint {
  // Required. The taint key to be applied to a node.
  optional string key = 1;

  // The taint value corresponding to the taint key.
  // +optional
  optional string value = 2;

  // Required. The effect of the taint on pods
  // that do not tolerate the taint.
  // Valid effects are NoSchedule, PreferNoSchedule and NoExecute.
  optional string effect = 3;

  // TimeAdded represents the time at which the taint was added.
  // It is only written for NoExecute taints.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time timeAdded = 4;
}

// The pod this Toleration is attached to tolerates any taint that matches
// the triple <key,value,effect> using the matching operator <operator>.
message Toleration {
  // Key is the taint key that the toleration applies to. Empty means match all taint keys.
  // If the key is empty, operator must be Exists; this combination means to match all values and all keys.
  // +optional
  optional string key = 1;

  // Operator represents a key's relationship to the value.
  // Valid operators are Exists and Equal. Defaults to Equal.
  // Exists is equivalent to wildcard for value, so that a pod can
  // tolerate all taints of a particular category.
  // +optional
  optional string operator = 2;

  // Value is the taint value the toleration matches to.
  // If the operator is Exists, the value should be empty, otherwise just a regular string.
  // +optional
  optional string value = 3;

  // Effect indicates the taint effect to match. Empty means match all taint effects.
  // When specified, allowed values are NoSchedule, PreferNoSchedule and NoExecute.
  // +optional
  optional string effect = 4;

  // TolerationSeconds represents the period of time the toleration (which must be
  // of effect NoExecute, otherwise this field is ignored) tolerates the taint. By default,
  // it is not set, which means tolerate the taint forever (do not evict). Zero and
  // negative values will be treated as 0 (evict immediately) by the system.
  // +optional
  optional int64 tolerationSeconds = 5;
}

// A topology selector requirement is a selector that matches given label.
// This is an alpha feature and may change in the future.
message TopologySelectorLabelRequirement {
  // The label key that the selector applies to.
  optional string key = 1;

  // An array of string values. One value must match the label to be selected.
  // Each entry in Values is ORed.
  repeated string values = 2;
}

// A topology selector term represents the result of label queries.
// A null or empty topology selector term matches no objects.
// The requirements of them are ANDed.
// It provides a subset of functionality as NodeSelectorTerm.
// This is an alpha feature and may change in the future.
// +structType=atomic
message TopologySelectorTerm {
  // A list of topology selector requirements by labels.
  // +optional
  repeated TopologySelectorLabelRequirement matchLabelExpressions = 1;
}

// TopologySpreadConstraint specifies how to spread matching pods among the given topology.
message TopologySpreadConstraint {
  // MaxSkew describes the degree to which pods may be unevenly distributed.
  // When `whenUnsatisfiable=DoNotSchedule`, it is the maximum permitted difference
  // between the number of matching pods in the target topology and the global minimum.
  // The global minimum is the minimum number of matching pods in an eligible domain
  // or zero if the number of eligible domains is less than MinDomains.
  // For example, in a 3-zone cluster, MaxSkew is set to 1, and pods with the same
  // labelSelector spread as 2/2/1:
  // In this case, the global minimum is 1.
  // +-------+-------+-------+
  // | zone1 | zone2 | zone3 |
  // +-------+-------+-------+
  // |  P P  |  P P  |   P   |
  // +-------+-------+-------+
  // - if MaxSkew is 1, incoming pod can only be scheduled to zone3 to become 2/2/2;
  // scheduling it onto zone1(zone2) would make the ActualSkew(3-1) on zone1(zone2)
  // violate MaxSkew(1).
  // - if MaxSkew is 2, incoming pod can be scheduled onto any zone.
  // When `whenUnsatisfiable=ScheduleAnyway`, it is used to give higher precedence
  // to topologies that satisfy it.
  // It's a required field. Default value is 1 and 0 is not allowed.
  optional int32 maxSkew = 1;

  // TopologyKey is the key of node labels. Nodes that have a label with this key
  // and identical values are considered to be in the same topology.
  // We consider each <key, value> as a "bucket", and try to put balanced number
  // of pods into each bucket.
  // We define a domain as a particular instance of a topology.
  // Also, we define an eligible domain as a domain whose nodes meet the requirements of
  // nodeAffinityPolicy and nodeTaintsPolicy.
  // e.g. If TopologyKey is "kubernetes.io/hostname", each Node is a domain of that topology.
  // And, if TopologyKey is "topology.kubernetes.io/zone", each zone is a domain of that topology.
  // It's a required field.
  optional string topologyKey = 2;

  // WhenUnsatisfiable indicates how to deal with a pod if it doesn't satisfy
  // the spread constraint.
  // - DoNotSchedule (default) tells the scheduler not to schedule it.
  // - ScheduleAnyway tells the scheduler to schedule the pod in any location,
  //   but giving higher precedence to topologies that would help reduce the
  //   skew.
  // A constraint is considered "Unsatisfiable" for an incoming pod
  // if and only if every possible node assignment for that pod would violate
  // "MaxSkew" on some topology.
  // For example, in a 3-zone cluster, MaxSkew is set to 1, and pods with the same
  // labelSelector spread as 3/1/1:
  // +-------+-------+-------+
  // | zone1 | zone2 | zone3 |
  // +-------+-------+-------+
  // | P P P |   P   |   P   |
  // +-------+-------+-------+
  // If WhenUnsatisfiable is set to DoNotSchedule, incoming pod can only be scheduled
  // to zone2(zone3) to become 3/2/1(3/1/2) as ActualSkew(2-1) on zone2(zone3) satisfies
  // MaxSkew(1). In other words, the cluster can still be imbalanced, but scheduler
  // won't make it *more* imbalanced.
  // It's a required field.
  optional string whenUnsatisfiable = 3;

  // LabelSelector is used to find matching pods.
  // Pods that match this label selector are counted to determine the number of pods
  // in their corresponding topology domain.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.LabelSelector labelSelector = 4;

  // MinDomains indicates a minimum number of eligible domains.
  // When the number of eligible domains with matching topology keys is less than minDomains,
  // Pod Topology Spread treats "global minimum" as 0, and then the calculation of Skew is performed.
  // And when the number of eligible domains with matching topology keys equals or greater than minDomains,
  // this value has no effect on scheduling.
  // As a result, when the number of eligible domains is less than minDomains,
  // scheduler won't schedule more than maxSkew Pods to those domains.
  // If value is nil, the constraint behaves as if MinDomains is equal to 1.
  // Valid values are integers greater than 0.
  // When value is not nil, WhenUnsatisfiable must be DoNotSchedule.
  //
  // For example, in a 3-zone cluster, MaxSkew is set to 2, MinDomains is set to 5 and pods with the same
  // labelSelector spread as 2/2/2:
  // +-------+-------+-------+
  // | zone1 | zone2 | zone3 |
  // +-------+-------+-------+
  // |  P P  |  P P  |  P P  |
  // +-------+-------+-------+
  // The number of domains is less than 5(MinDomains), so "global minimum" is treated as 0.
  // In this situation, new pod with the same labelSelector cannot be scheduled,
  // because computed skew will be 3(3 - 0) if new Pod is scheduled to any of the three zones,
  // it will violate MaxSkew.
  //
  // This is a beta field and requires the MinDomainsInPodTopologySpread feature gate to be enabled (enabled by default).
  // +optional
  optional int32 minDomains = 5;

  // NodeAffinityPolicy indicates how we will treat Pod's nodeAffinity/nodeSelector
  // when calculating pod topology spread skew. Options are:
  // - Honor: only nodes matching nodeAffinity/nodeSelector are included in the calculations.
  // - Ignore: nodeAffinity/nodeSelector are ignored. All nodes are included in the calculations.
  //
  // If this value is nil, the behavior is equivalent to the Honor policy.
  // This is a beta-level feature default enabled by the NodeInclusionPolicyInPodTopologySpread feature flag.
  // +optional
  optional string nodeAffinityPolicy = 6;

  // NodeTaintsPolicy indicates how we will treat node taints when calculating
  // pod topology spread skew. Options are:
  // - Honor: nodes without taints, along with tainted nodes for which the incoming pod
  // has a toleration, are included.
  // - Ignore: node taints are ignored. All nodes are included.
  //
  // If this value is nil, the behavior is equivalent to the Ignore policy.
  // This is a beta-level feature default enabled by the NodeInclusionPolicyInPodTopologySpread feature flag.
  // +optional
  optional string nodeTaintsPolicy = 7;

  // MatchLabelKeys is a set of pod label keys to select the pods over which
  // spreading will be calculated. The keys are used to lookup values from the
  // incoming pod labels, those key-value labels are ANDed with labelSelector
  // to select the group of existing pods over which spreading will be calculated
  // for the incoming pod. The same key is forbidden to exist in both MatchLabelKeys and LabelSelector.
  // MatchLabelKeys cannot be set when LabelSelector isn't set.
  // Keys that don't exist in the incoming pod labels will
  // be ignored. A null or empty list means only match against labelSelector.
  //
  // This is a beta field and requires the MatchLabelKeysInPodTopologySpread feature gate to be enabled (enabled by default).
  // +listType=atomic
  // +optional
  repeated string matchLabelKeys = 8;
}

// TypedLocalObjectReference contains enough information to let you locate the
// typed referenced object inside the same namespace.
// +structType=atomic
message TypedLocalObjectReference {
  // APIGroup is the group for the resource being referenced.
  // If APIGroup is not specified, the specified Kind must be in the core API group.
  // For any other third-party types, APIGroup is required.
  // +optional
  optional string apiGroup = 1;

  // Kind is the type of resource being referenced
  optional string kind = 2;

  // Name is the name of resource being referenced
  optional string name = 3;
}

message TypedObjectReference {
  // APIGroup is the group for the resource being referenced.
  // If APIGroup is not specified, the specified Kind must be in the core API group.
  // For any other third-party types, APIGroup is required.
  // +optional
  optional string apiGroup = 1;

  // Kind is the type of resource being referenced
  optional string kind = 2;

  // Name is the name of resource being referenced
  optional string name = 3;

  // Namespace is the namespace of resource being referenced
  // Note that when a namespace is specified, a gateway.networking.k8s.io/ReferenceGrant object is required in the referent namespace to allow that namespace's owner to accept the reference. See the ReferenceGrant documentation for details.
  // (Alpha) This field requires the CrossNamespaceVolumeDataSource feature gate to be enabled.
  // +featureGate=CrossNamespaceVolumeDataSource
  // +optional
  optional string namespace = 4;
}

// Volume represents a named volume in a pod that may be accessed by any container in the pod.
message Volume {
  // name of the volume.
  // Must be a DNS_LABEL and unique within the pod.
  // More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
  optional string name = 1;

  // volumeSource represents the location and type of the mounted volume.
  // If not specified, the Volume is implied to be an EmptyDir.
  // This implied behavior is deprecated and will be removed in a future version.
  optional VolumeSource volumeSource = 2;
}

// volumeDevice describes a mapping of a raw block device within a container.
message VolumeDevice {
  // name must match the name of a persistentVolumeClaim in the pod
  optional string name = 1;

  // devicePath is the path inside of the container that the device will be mapped to.
  optional string devicePath = 2;
}

// VolumeMount describes a mounting of a Volume within a container.
message VolumeMount {
  // This must match the Name of a Volume.
  optional string name = 1;

  // Mounted read-only if true, read-write otherwise (false or unspecified).
  // Defaults to false.
  // +optional
  optional bool readOnly = 2;

  // Path within the container at which the volume should be mounted.  Must
  // not contain ':'.
  optional string mountPath = 3;

  // Path within the volume from which the container's volume should be mounted.
  // Defaults to "" (volume's root).
  // +optional
  optional string subPath = 4;

  // mountPropagation determines how mounts are propagated from the host
  // to container and the other way around.
  // When not set, MountPropagationNone is used.
  // This field is beta in 1.10.
  // +optional
  optional string mountPropagation = 5;

  // Expanded path within the volume from which the container's volume should be mounted.
  // Behaves similarly to SubPath but environment variable references $(VAR_NAME) are expanded using the container's environment.
  // Defaults to "" (volume's root).
  // SubPathExpr and SubPath are mutually exclusive.
  // +optional
  optional string subPathExpr = 6;
}

// VolumeNodeAffinity defines constraints that limit what nodes this volume can be accessed from.
message VolumeNodeAffinity {
  // required specifies hard node constraints that must be met.
  optional NodeSelector required = 1;
}

// Projection that may be projected along with other supported volume types
message VolumeProjection {
  // secret information about the secret data to project
  // +optional
  optional SecretProjection secret = 1;

  // downwardAPI information about the downwardAPI data to project
  // +optional
  optional DownwardAPIProjection downwardAPI = 2;

  // configMap information about the configMap data to project
  // +optional
  optional ConfigMapProjection configMap = 3;

  // serviceAccountToken is information about the serviceAccountToken data to project
  // +optional
  optional ServiceAccountTokenProjection serviceAccountToken = 4;
}

// Represents the source of a volume to mount.
// Only one of its members may be specified.
message VolumeSource {
  // hostPath represents a pre-existing file or directory on the host
  // machine that is directly exposed to the container. This is generally
  // used for system agents or other privileged things that are allowed
  // to see the host machine. Most containers will NOT need this.
  // More info: https://kubernetes.io/docs/concepts/storage/volumes#hostpath
  // ---
  // TODO(jonesdl) We need to restrict who can use host directory mounts and who can/can not
  // mount host directories as read/write.
  // +optional
  optional HostPathVolumeSource hostPath = 1;

  // emptyDir represents a temporary directory that shares a pod's lifetime.
  // More info: https://kubernetes.io/docs/concepts/storage/volumes#emptydir
  // +optional
  optional EmptyDirVolumeSource emptyDir = 2;

  // gcePersistentDisk represents a GCE Disk resource that is attached to a
  // kubelet's host machine and then exposed to the pod.
  // More info: https://kubernetes.io/docs/concepts/storage/volumes#gcepersistentdisk
  // +optional
  optional GCEPersistentDiskVolumeSource gcePersistentDisk = 3;

  // awsElasticBlockStore represents an AWS Disk resource that is attached to a
  // kubelet's host machine and then exposed to the pod.
  // More info: https://kubernetes.io/docs/concepts/storage/volumes#awselasticblockstore
  // +optional
  optional AWSElasticBlockStoreVolumeSource awsElasticBlockStore = 4;

  // gitRepo represents a git repository at a particular revision.
  // DEPRECATED: GitRepo is deprecated. To provision a container with a git repo, mount an
  // EmptyDir into an InitContainer that clones the repo using git, then mount the EmptyDir
  // into the Pod's container.
  // +optional
  optional GitRepoVolumeSource gitRepo = 5;

  // secret represents a secret that should populate this volume.
  // More info: https://kubernetes.io/docs/concepts/storage/volumes#secret
  // +optional
  optional SecretVolumeSource secret = 6;

  // nfs represents an NFS mount on the host that shares a pod's lifetime
  // More info: https://kubernetes.io/docs/concepts/storage/volumes#nfs
  // +optional
  optional NFSVolumeSource nfs = 7;

  // iscsi represents an ISCSI Disk resource that is attached to a
  // kubelet's host machine and then exposed to the pod.
  // More info: https://examples.k8s.io/volumes/iscsi/README.md
  // +optional
  optional ISCSIVolumeSource iscsi = 8;

  // glusterfs represents a Glusterfs mount on the host that shares a pod's lifetime.
  // More info: https://examples.k8s.io/volumes/glusterfs/README.md
  // +optional
  optional GlusterfsVolumeSource glusterfs = 9;

  // persistentVolumeClaimVolumeSource represents a reference to a
  // PersistentVolumeClaim in the same namespace.
  // More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#persistentvolumeclaims
  // +optional
  optional PersistentVolumeClaimVolumeSource persistentVolumeClaim = 10;

  // rbd represents a Rados Block Device mount on the host that shares a pod's lifetime.
  // More info: https://examples.k8s.io/volumes/rbd/README.md
  // +optional
  optional RBDVolumeSource rbd = 11;

  // flexVolume represents a generic volume resource that is
  // provisioned/attached using an exec based plugin.
  // +optional
  optional FlexVolumeSource flexVolume = 12;

  // cinder represents a cinder volume attached and mounted on kubelets host machine.
  // More info: https://examples.k8s.io/mysql-cinder-pd/README.md
  // +optional
  optional CinderVolumeSource cinder = 13;

  // cephFS represents a Ceph FS mount on the host that shares a pod's lifetime
  // +optional
  optional CephFSVolumeSource cephfs = 14;

  // flocker represents a Flocker volume attached to a kubelet's host machine. This depends on the Flocker control service being running
  // +optional
  optional FlockerVolumeSource flocker = 15;

  // downwardAPI represents downward API about the pod that should populate this volume
  // +optional
  optional DownwardAPIVolumeSource downwardAPI = 16;

  // fc represents a Fibre Channel resource that is attached to a kubelet's host machine and then exposed to the pod.
  // +optional
  optional FCVolumeSource fc = 17;

  // azureFile represents an Azure File Service mount on the host and bind mount to the pod.
  // +optional
  optional AzureFileVolumeSource azureFile = 18;

  // configMap represents a configMap that should populate this volume
  // +optional
  optional ConfigMapVolumeSource configMap = 19;

  // vsphereVolume represents a vSphere volume attached and mounted on kubelets host machine
  // +optional
  optional VsphereVirtualDiskVolumeSource vsphereVolume = 20;

  // quobyte represents a Quobyte mount on the host that shares a pod's lifetime
  // +optional
  optional QuobyteVolumeSource quobyte = 21;

  // azureDisk represents an Azure Data Disk mount on the host and bind mount to the pod.
  // +optional
  optional AzureDiskVolumeSource azureDisk = 22;

  // photonPersistentDisk represents a PhotonController persistent disk attached and mounted on kubelets host machine
  optional PhotonPersistentDiskVolumeSource photonPersistentDisk = 23;

  // projected items for all in one resources secrets, configmaps, and downward API
  optional ProjectedVolumeSource projected = 26;

  // portworxVolume represents a portworx volume attached and mounted on kubelets host machine
  // +optional
  optional PortworxVolumeSource portworxVolume = 24;

  // scaleIO represents a ScaleIO persistent volume attached and mounted on Kubernetes nodes.
  // +optional
  optional ScaleIOVolumeSource scaleIO = 25;

  // storageOS represents a StorageOS volume attached and mounted on Kubernetes nodes.
  // +optional
  optional StorageOSVolumeSource storageos = 27;

  // csi (Container Storage Interface) represents ephemeral storage that is handled by certain external CSI drivers (Beta feature).
  // +optional
  optional CSIVolumeSource csi = 28;

  // ephemeral represents a volume that is handled by a cluster storage driver.
  // The volume's lifecycle is tied to the pod that defines it - it will be created before the pod starts,
  // and deleted when the pod is removed.
  //
  // Use this if:
  // a) the volume is only needed while the pod runs,
  // b) features of normal volumes like restoring from snapshot or capacity
  //    tracking are needed,
  // c) the storage driver is specified through a storage class, and
  // d) the storage driver supports dynamic volume provisioning through
  //    a PersistentVolumeClaim (see EphemeralVolumeSource for more
  //    information on the connection between this volume type
  //    and PersistentVolumeClaim).
  //
  // Use PersistentVolumeClaim or one of the vendor-specific
  // APIs for volumes that persist for longer than the lifecycle
  // of an individual pod.
  //
  // Use CSI for light-weight local ephemeral volumes if the CSI driver is meant to
  // be used that way - see the documentation of the driver for
  // more information.
  //
  // A pod can use both types of ephemeral volumes and
  // persistent volumes at the same time.
  //
  // +optional
  optional EphemeralVolumeSource ephemeral = 29;
}

// Represents a vSphere volume resource.
message VsphereVirtualDiskVolumeSource {
  // volumePath is the path that identifies vSphere volume vmdk
  optional string volumePath = 1;

  // fsType is filesystem type to mount.
  // Must be a filesystem type supported by the host operating system.
  // Ex. "ext4", "xfs", "ntfs". Implicitly inferred to be "ext4" if unspecified.
  // +optional
  optional string fsType = 2;

  // storagePolicyName is the storage Policy Based Management (SPBM) profile name.
  // +optional
  optional string storagePolicyName = 3;

  // storagePolicyID is the storage Policy Based Management (SPBM) profile ID associated with the StoragePolicyName.
  // +optional
  optional string storagePolicyID = 4;
}

// The weights of all of the matched WeightedPodAffinityTerm fields are added per-node to find the most preferred node(s)
message WeightedPodAffinityTerm {
  // weight associated with matching the corresponding podAffinityTerm,
  // in the range 1-100.
  optional int32 weight = 1;

  // Required. A pod affinity term, associated with the corresponding weight.
  optional PodAffinityTerm podAffinityTerm = 2;
}

// WindowsSecurityContextOptions contain Windows-specific options and credentials.
message WindowsSecurityContextOptions {
  // GMSACredentialSpecName is the name of the GMSA credential spec to use.
  // +optional
  optional string gmsaCredentialSpecName = 1;

  // GMSACredentialSpec is where the GMSA admission webhook
  // (https://github.com/kubernetes-sigs/windows-gmsa) inlines the contents of the
  // GMSA credential spec named by the GMSACredentialSpecName field.
  // +optional
  optional string gmsaCredentialSpec = 2;

  // The UserName in Windows to run the entrypoint of the container process.
  // Defaults to the user specified in image metadata if unspecified.
  // May also be set in PodSecurityContext. If set in both SecurityContext and
  // PodSecurityContext, the value specified in SecurityContext takes precedence.
  // +optional
  optional string runAsUserName = 3;

  // HostProcess determines if a container should be run as a 'Host Process' container.
  // All of a Pod's containers must have the same effective HostProcess value
  // (it is not allowed to have a mix of HostProcess containers and non-HostProcess containers).
  // In addition, if HostProcess is true then HostNetwork must also be set to true.
  // +optional
  optional bool hostProcess = 4;
}

