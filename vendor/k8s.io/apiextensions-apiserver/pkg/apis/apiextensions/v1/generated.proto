/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/


// This file was autogenerated by go-to-protobuf. Do not edit it manually!

syntax = "proto2";

package k8s.io.apiextensions_apiserver.pkg.apis.apiextensions.v1;

import "k8s.io/apimachinery/pkg/apis/meta/v1/generated.proto";
import "k8s.io/apimachinery/pkg/runtime/generated.proto";
import "k8s.io/apimachinery/pkg/runtime/schema/generated.proto";

// Package-wide variables from generator "generated".
option go_package = "k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1";

// ConversionRequest describes the conversion request parameters.
message ConversionRequest {
  // uid is an identifier for the individual request/response. It allows distinguishing instances of requests which are
  // otherwise identical (parallel requests, etc).
  // The UID is meant to track the round trip (request/response) between the Kubernetes API server and the webhook, not the user request.
  // It is suitable for correlating log entries between the webhook and apiserver, for either auditing or debugging.
  optional string uid = 1;

  // desiredAPIVersion is the version to convert given objects to. e.g. "myapi.example.com/v1"
  optional string desiredAPIVersion = 2;

  // objects is the list of custom resource objects to be converted.
  repeated k8s.io.apimachinery.pkg.runtime.RawExtension objects = 3;
}

// ConversionResponse describes a conversion response.
message ConversionResponse {
  // uid is an identifier for the individual request/response.
  // This should be copied over from the corresponding `request.uid`.
  optional string uid = 1;

  // convertedObjects is the list of converted version of `request.objects` if the `result` is successful, otherwise empty.
  // The webhook is expected to set `apiVersion` of these objects to the `request.desiredAPIVersion`. The list
  // must also have the same size as the input list with the same objects in the same order (equal kind, metadata.uid, metadata.name and metadata.namespace).
  // The webhook is allowed to mutate labels and annotations. Any other change to the metadata is silently ignored.
  repeated k8s.io.apimachinery.pkg.runtime.RawExtension convertedObjects = 2;

  // result contains the result of conversion with extra details if the conversion failed. `result.status` determines if
  // the conversion failed or succeeded. The `result.status` field is required and represents the success or failure of the
  // conversion. A successful conversion must set `result.status` to `Success`. A failed conversion must set
  // `result.status` to `Failure` and provide more details in `result.message` and return http status 200. The `result.message`
  // will be used to construct an error message for the end user.
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Status result = 3;
}

// ConversionReview describes a conversion request/response.
message ConversionReview {
  // request describes the attributes for the conversion request.
  // +optional
  optional ConversionRequest request = 1;

  // response describes the attributes for the conversion response.
  // +optional
  optional ConversionResponse response = 2;
}

// CustomResourceColumnDefinition specifies a column for server side printing.
message CustomResourceColumnDefinition {
  // name is a human readable name for the column.
  optional string name = 1;

  // type is an OpenAPI type definition for this column.
  // See https://github.com/OAI/OpenAPI-Specification/blob/master/versions/2.0.md#data-types for details.
  optional string type = 2;

  // format is an optional OpenAPI type definition for this column. The 'name' format is applied
  // to the primary identifier column to assist in clients identifying column is the resource name.
  // See https://github.com/OAI/OpenAPI-Specification/blob/master/versions/2.0.md#data-types for details.
  // +optional
  optional string format = 3;

  // description is a human readable description of this column.
  // +optional
  optional string description = 4;

  // priority is an integer defining the relative importance of this column compared to others. Lower
  // numbers are considered higher priority. Columns that may be omitted in limited space scenarios
  // should be given a priority greater than 0.
  // +optional
  optional int32 priority = 5;

  // jsonPath is a simple JSON path (i.e. with array notation) which is evaluated against
  // each custom resource to produce the value for this column.
  optional string jsonPath = 6;
}

// CustomResourceConversion describes how to convert different versions of a CR.
message CustomResourceConversion {
  // strategy specifies how custom resources are converted between versions. Allowed values are:
  // - `"None"`: The converter only change the apiVersion and would not touch any other field in the custom resource.
  // - `"Webhook"`: API Server will call to an external webhook to do the conversion. Additional information
  //   is needed for this option. This requires spec.preserveUnknownFields to be false, and spec.conversion.webhook to be set.
  optional string strategy = 1;

  // webhook describes how to call the conversion webhook. Required when `strategy` is set to `"Webhook"`.
  // +optional
  optional WebhookConversion webhook = 2;
}

// CustomResourceDefinition represents a resource that should be exposed on the API server.  Its name MUST be in the format
// <.spec.name>.<.spec.group>.
message CustomResourceDefinition {
  // Standard object's metadata
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // spec describes how the user wants the resources to appear
  optional CustomResourceDefinitionSpec spec = 2;

  // status indicates the actual state of the CustomResourceDefinition
  // +optional
  optional CustomResourceDefinitionStatus status = 3;
}

// CustomResourceDefinitionCondition contains details for the current condition of this pod.
message CustomResourceDefinitionCondition {
  // type is the type of the condition. Types include Established, NamesAccepted and Terminating.
  optional string type = 1;

  // status is the status of the condition.
  // Can be True, False, Unknown.
  optional string status = 2;

  // lastTransitionTime last time the condition transitioned from one status to another.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time lastTransitionTime = 3;

  // reason is a unique, one-word, CamelCase reason for the condition's last transition.
  // +optional
  optional string reason = 4;

  // message is a human-readable message indicating details about last transition.
  // +optional
  optional string message = 5;
}

// CustomResourceDefinitionList is a list of CustomResourceDefinition objects.
message CustomResourceDefinitionList {
  // Standard object's metadata
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // items list individual CustomResourceDefinition objects
  repeated CustomResourceDefinition items = 2;
}

// CustomResourceDefinitionNames indicates the names to serve this CustomResourceDefinition
message CustomResourceDefinitionNames {
  // plural is the plural name of the resource to serve.
  // The custom resources are served under `/apis/<group>/<version>/.../<plural>`.
  // Must match the name of the CustomResourceDefinition (in the form `<names.plural>.<group>`).
  // Must be all lowercase.
  optional string plural = 1;

  // singular is the singular name of the resource. It must be all lowercase. Defaults to lowercased `kind`.
  // +optional
  optional string singular = 2;

  // shortNames are short names for the resource, exposed in API discovery documents,
  // and used by clients to support invocations like `kubectl get <shortname>`.
  // It must be all lowercase.
  // +optional
  repeated string shortNames = 3;

  // kind is the serialized kind of the resource. It is normally CamelCase and singular.
  // Custom resource instances will use this value as the `kind` attribute in API calls.
  optional string kind = 4;

  // listKind is the serialized kind of the list for this resource. Defaults to "`kind`List".
  // +optional
  optional string listKind = 5;

  // categories is a list of grouped resources this custom resource belongs to (e.g. 'all').
  // This is published in API discovery documents, and used by clients to support invocations like
  // `kubectl get all`.
  // +optional
  repeated string categories = 6;
}

// CustomResourceDefinitionSpec describes how a user wants their resource to appear
message CustomResourceDefinitionSpec {
  // group is the API group of the defined custom resource.
  // The custom resources are served under `/apis/<group>/...`.
  // Must match the name of the CustomResourceDefinition (in the form `<names.plural>.<group>`).
  optional string group = 1;

  // names specify the resource and kind names for the custom resource.
  optional CustomResourceDefinitionNames names = 3;

  // scope indicates whether the defined custom resource is cluster- or namespace-scoped.
  // Allowed values are `Cluster` and `Namespaced`.
  optional string scope = 4;

  // versions is the list of all API versions of the defined custom resource.
  // Version names are used to compute the order in which served versions are listed in API discovery.
  // If the version string is "kube-like", it will sort above non "kube-like" version strings, which are ordered
  // lexicographically. "Kube-like" versions start with a "v", then are followed by a number (the major version),
  // then optionally the string "alpha" or "beta" and another number (the minor version). These are sorted first
  // by GA > beta > alpha (where GA is a version with no suffix such as beta or alpha), and then by comparing
  // major version, then minor version. An example sorted list of versions:
  // v10, v2, v1, v11beta2, v10beta3, v3beta1, v12alpha1, v11alpha2, foo1, foo10.
  repeated CustomResourceDefinitionVersion versions = 7;

  // conversion defines conversion settings for the CRD.
  // +optional
  optional CustomResourceConversion conversion = 9;

  // preserveUnknownFields indicates that object fields which are not specified
  // in the OpenAPI schema should be preserved when persisting to storage.
  // apiVersion, kind, metadata and known fields inside metadata are always preserved.
  // This field is deprecated in favor of setting `x-preserve-unknown-fields` to true in `spec.versions[*].schema.openAPIV3Schema`.
  // See https://kubernetes.io/docs/tasks/extend-kubernetes/custom-resources/custom-resource-definitions/#field-pruning for details.
  // +optional
  optional bool preserveUnknownFields = 10;
}

// CustomResourceDefinitionStatus indicates the state of the CustomResourceDefinition
message CustomResourceDefinitionStatus {
  // conditions indicate state for particular aspects of a CustomResourceDefinition
  // +optional
  // +listType=map
  // +listMapKey=type
  repeated CustomResourceDefinitionCondition conditions = 1;

  // acceptedNames are the names that are actually being used to serve discovery.
  // They may be different than the names in spec.
  // +optional
  optional CustomResourceDefinitionNames acceptedNames = 2;

  // storedVersions lists all versions of CustomResources that were ever persisted. Tracking these
  // versions allows a migration path for stored versions in etcd. The field is mutable
  // so a migration controller can finish a migration to another version (ensuring
  // no old objects are left in storage), and then remove the rest of the
  // versions from this list.
  // Versions may not be removed from `spec.versions` while they exist in this list.
  // +optional
  repeated string storedVersions = 3;
}

// CustomResourceDefinitionVersion describes a version for CRD.
message CustomResourceDefinitionVersion {
  // name is the version name, e.g. “v1”, “v2beta1”, etc.
  // The custom resources are served under this version at `/apis/<group>/<version>/...` if `served` is true.
  optional string name = 1;

  // served is a flag enabling/disabling this version from being served via REST APIs
  optional bool served = 2;

  // storage indicates this version should be used when persisting custom resources to storage.
  // There must be exactly one version with storage=true.
  optional bool storage = 3;

  // deprecated indicates this version of the custom resource API is deprecated.
  // When set to true, API requests to this version receive a warning header in the server response.
  // Defaults to false.
  // +optional
  optional bool deprecated = 7;

  // deprecationWarning overrides the default warning returned to API clients.
  // May only be set when `deprecated` is true.
  // The default warning indicates this version is deprecated and recommends use
  // of the newest served version of equal or greater stability, if one exists.
  // +optional
  optional string deprecationWarning = 8;

  // schema describes the schema used for validation, pruning, and defaulting of this version of the custom resource.
  // +optional
  optional CustomResourceValidation schema = 4;

  // subresources specify what subresources this version of the defined custom resource have.
  // +optional
  optional CustomResourceSubresources subresources = 5;

  // additionalPrinterColumns specifies additional columns returned in Table output.
  // See https://kubernetes.io/docs/reference/using-api/api-concepts/#receiving-resources-as-tables for details.
  // If no columns are specified, a single column displaying the age of the custom resource is used.
  // +optional
  repeated CustomResourceColumnDefinition additionalPrinterColumns = 6;
}

// CustomResourceSubresourceScale defines how to serve the scale subresource for CustomResources.
message CustomResourceSubresourceScale {
  // specReplicasPath defines the JSON path inside of a custom resource that corresponds to Scale `spec.replicas`.
  // Only JSON paths without the array notation are allowed.
  // Must be a JSON Path under `.spec`.
  // If there is no value under the given path in the custom resource, the `/scale` subresource will return an error on GET.
  optional string specReplicasPath = 1;

  // statusReplicasPath defines the JSON path inside of a custom resource that corresponds to Scale `status.replicas`.
  // Only JSON paths without the array notation are allowed.
  // Must be a JSON Path under `.status`.
  // If there is no value under the given path in the custom resource, the `status.replicas` value in the `/scale` subresource
  // will default to 0.
  optional string statusReplicasPath = 2;

  // labelSelectorPath defines the JSON path inside of a custom resource that corresponds to Scale `status.selector`.
  // Only JSON paths without the array notation are allowed.
  // Must be a JSON Path under `.status` or `.spec`.
  // Must be set to work with HorizontalPodAutoscaler.
  // The field pointed by this JSON path must be a string field (not a complex selector struct)
  // which contains a serialized label selector in string form.
  // More info: https://kubernetes.io/docs/tasks/access-kubernetes-api/custom-resources/custom-resource-definitions#scale-subresource
  // If there is no value under the given path in the custom resource, the `status.selector` value in the `/scale`
  // subresource will default to the empty string.
  // +optional
  optional string labelSelectorPath = 3;
}

// CustomResourceSubresourceStatus defines how to serve the status subresource for CustomResources.
// Status is represented by the `.status` JSON path inside of a CustomResource. When set,
// * exposes a /status subresource for the custom resource
// * PUT requests to the /status subresource take a custom resource object, and ignore changes to anything except the status stanza
// * PUT/POST/PATCH requests to the custom resource ignore changes to the status stanza
message CustomResourceSubresourceStatus {
}

// CustomResourceSubresources defines the status and scale subresources for CustomResources.
message CustomResourceSubresources {
  // status indicates the custom resource should serve a `/status` subresource.
  // When enabled:
  // 1. requests to the custom resource primary endpoint ignore changes to the `status` stanza of the object.
  // 2. requests to the custom resource `/status` subresource ignore changes to anything other than the `status` stanza of the object.
  // +optional
  optional CustomResourceSubresourceStatus status = 1;

  // scale indicates the custom resource should serve a `/scale` subresource that returns an `autoscaling/v1` Scale object.
  // +optional
  optional CustomResourceSubresourceScale scale = 2;
}

// CustomResourceValidation is a list of validation methods for CustomResources.
message CustomResourceValidation {
  // openAPIV3Schema is the OpenAPI v3 schema to use for validation and pruning.
  // +optional
  optional JSONSchemaProps openAPIV3Schema = 1;
}

// ExternalDocumentation allows referencing an external resource for extended documentation.
message ExternalDocumentation {
  optional string description = 1;

  optional string url = 2;
}

// JSON represents any valid JSON value.
// These types are supported: bool, int64, float64, string, []interface{}, map[string]interface{} and nil.
message JSON {
  optional bytes raw = 1;
}

// JSONSchemaProps is a JSON-Schema following Specification Draft 4 (http://json-schema.org/).
message JSONSchemaProps {
  optional string id = 1;

  optional string schema = 2;

  optional string ref = 3;

  optional string description = 4;

  optional string type = 5;

  // format is an OpenAPI v3 format string. Unknown formats are ignored. The following formats are validated:
  //
  // - bsonobjectid: a bson object ID, i.e. a 24 characters hex string
  // - uri: an URI as parsed by Golang net/url.ParseRequestURI
  // - email: an email address as parsed by Golang net/mail.ParseAddress
  // - hostname: a valid representation for an Internet host name, as defined by RFC 1034, section 3.1 [RFC1034].
  // - ipv4: an IPv4 IP as parsed by Golang net.ParseIP
  // - ipv6: an IPv6 IP as parsed by Golang net.ParseIP
  // - cidr: a CIDR as parsed by Golang net.ParseCIDR
  // - mac: a MAC address as parsed by Golang net.ParseMAC
  // - uuid: an UUID that allows uppercase defined by the regex (?i)^[0-9a-f]{8}-?[0-9a-f]{4}-?[0-9a-f]{4}-?[0-9a-f]{4}-?[0-9a-f]{12}$
  // - uuid3: an UUID3 that allows uppercase defined by the regex (?i)^[0-9a-f]{8}-?[0-9a-f]{4}-?3[0-9a-f]{3}-?[0-9a-f]{4}-?[0-9a-f]{12}$
  // - uuid4: an UUID4 that allows uppercase defined by the regex (?i)^[0-9a-f]{8}-?[0-9a-f]{4}-?4[0-9a-f]{3}-?[89ab][0-9a-f]{3}-?[0-9a-f]{12}$
  // - uuid5: an UUID5 that allows uppercase defined by the regex (?i)^[0-9a-f]{8}-?[0-9a-f]{4}-?5[0-9a-f]{3}-?[89ab][0-9a-f]{3}-?[0-9a-f]{12}$
  // - isbn: an ISBN10 or ISBN13 number string like "0321751043" or "978-0321751041"
  // - isbn10: an ISBN10 number string like "0321751043"
  // - isbn13: an ISBN13 number string like "978-0321751041"
  // - creditcard: a credit card number defined by the regex ^(?:4[0-9]{12}(?:[0-9]{3})?|5[1-5][0-9]{14}|6(?:011|5[0-9][0-9])[0-9]{12}|3[47][0-9]{13}|3(?:0[0-5]|[68][0-9])[0-9]{11}|(?:2131|1800|35\\d{3})\\d{11})$ with any non digit characters mixed in
  // - ssn: a U.S. social security number following the regex ^\\d{3}[- ]?\\d{2}[- ]?\\d{4}$
  // - hexcolor: an hexadecimal color code like "#FFFFFF: following the regex ^#?([0-9a-fA-F]{3}|[0-9a-fA-F]{6})$
  // - rgbcolor: an RGB color code like rgb like "rgb(255,255,2559"
  // - byte: base64 encoded binary data
  // - password: any kind of string
  // - date: a date string like "2006-01-02" as defined by full-date in RFC3339
  // - duration: a duration string like "22 ns" as parsed by Golang time.ParseDuration or compatible with Scala duration format
  // - datetime: a date time string like "2014-12-15T19:30:20.000Z" as defined by date-time in RFC3339.
  optional string format = 6;

  optional string title = 7;

  // default is a default value for undefined object fields.
  // Defaulting is a beta feature under the CustomResourceDefaulting feature gate.
  // Defaulting requires spec.preserveUnknownFields to be false.
  optional JSON default = 8;

  optional double maximum = 9;

  optional bool exclusiveMaximum = 10;

  optional double minimum = 11;

  optional bool exclusiveMinimum = 12;

  optional int64 maxLength = 13;

  optional int64 minLength = 14;

  optional string pattern = 15;

  optional int64 maxItems = 16;

  optional int64 minItems = 17;

  optional bool uniqueItems = 18;

  optional double multipleOf = 19;

  repeated JSON enum = 20;

  optional int64 maxProperties = 21;

  optional int64 minProperties = 22;

  repeated string required = 23;

  optional JSONSchemaPropsOrArray items = 24;

  repeated JSONSchemaProps allOf = 25;

  repeated JSONSchemaProps oneOf = 26;

  repeated JSONSchemaProps anyOf = 27;

  optional JSONSchemaProps not = 28;

  map<string, JSONSchemaProps> properties = 29;

  optional JSONSchemaPropsOrBool additionalProperties = 30;

  map<string, JSONSchemaProps> patternProperties = 31;

  map<string, JSONSchemaPropsOrStringArray> dependencies = 32;

  optional JSONSchemaPropsOrBool additionalItems = 33;

  map<string, JSONSchemaProps> definitions = 34;

  optional ExternalDocumentation externalDocs = 35;

  optional JSON example = 36;

  optional bool nullable = 37;

  // x-kubernetes-preserve-unknown-fields stops the API server
  // decoding step from pruning fields which are not specified
  // in the validation schema. This affects fields recursively,
  // but switches back to normal pruning behaviour if nested
  // properties or additionalProperties are specified in the schema.
  // This can either be true or undefined. False is forbidden.
  optional bool xKubernetesPreserveUnknownFields = 38;

  // x-kubernetes-embedded-resource defines that the value is an
  // embedded Kubernetes runtime.Object, with TypeMeta and
  // ObjectMeta. The type must be object. It is allowed to further
  // restrict the embedded object. kind, apiVersion and metadata
  // are validated automatically. x-kubernetes-preserve-unknown-fields
  // is allowed to be true, but does not have to be if the object
  // is fully specified (up to kind, apiVersion, metadata).
  optional bool xKubernetesEmbeddedResource = 39;

  // x-kubernetes-int-or-string specifies that this value is
  // either an integer or a string. If this is true, an empty
  // type is allowed and type as child of anyOf is permitted
  // if following one of the following patterns:
  //
  // 1) anyOf:
  //    - type: integer
  //    - type: string
  // 2) allOf:
  //    - anyOf:
  //      - type: integer
  //      - type: string
  //    - ... zero or more
  optional bool xKubernetesIntOrString = 40;

  // x-kubernetes-list-map-keys annotates an array with the x-kubernetes-list-type `map` by specifying the keys used
  // as the index of the map.
  //
  // This tag MUST only be used on lists that have the "x-kubernetes-list-type"
  // extension set to "map". Also, the values specified for this attribute must
  // be a scalar typed field of the child structure (no nesting is supported).
  //
  // The properties specified must either be required or have a default value,
  // to ensure those properties are present for all list items.
  //
  // +optional
  repeated string xKubernetesListMapKeys = 41;

  // x-kubernetes-list-type annotates an array to further describe its topology.
  // This extension must only be used on lists and may have 3 possible values:
  //
  // 1) `atomic`: the list is treated as a single entity, like a scalar.
  //      Atomic lists will be entirely replaced when updated. This extension
  //      may be used on any type of list (struct, scalar, ...).
  // 2) `set`:
  //      Sets are lists that must not have multiple items with the same value. Each
  //      value must be a scalar, an object with x-kubernetes-map-type `atomic` or an
  //      array with x-kubernetes-list-type `atomic`.
  // 3) `map`:
  //      These lists are like maps in that their elements have a non-index key
  //      used to identify them. Order is preserved upon merge. The map tag
  //      must only be used on a list with elements of type object.
  // Defaults to atomic for arrays.
  // +optional
  optional string xKubernetesListType = 42;

  // x-kubernetes-map-type annotates an object to further describe its topology.
  // This extension must only be used when type is object and may have 2 possible values:
  //
  // 1) `granular`:
  //      These maps are actual maps (key-value pairs) and each fields are independent
  //      from each other (they can each be manipulated by separate actors). This is
  //      the default behaviour for all maps.
  // 2) `atomic`: the list is treated as a single entity, like a scalar.
  //      Atomic maps will be entirely replaced when updated.
  // +optional
  optional string xKubernetesMapType = 43;

  // x-kubernetes-validations describes a list of validation rules written in the CEL expression language.
  // This field is an alpha-level. Using this field requires the feature gate `CustomResourceValidationExpressions` to be enabled.
  // +patchMergeKey=rule
  // +patchStrategy=merge
  // +listType=map
  // +listMapKey=rule
  repeated ValidationRule xKubernetesValidations = 44;
}

// JSONSchemaPropsOrArray represents a value that can either be a JSONSchemaProps
// or an array of JSONSchemaProps. Mainly here for serialization purposes.
message JSONSchemaPropsOrArray {
  optional JSONSchemaProps schema = 1;

  repeated JSONSchemaProps jSONSchemas = 2;
}

// JSONSchemaPropsOrBool represents JSONSchemaProps or a boolean value.
// Defaults to true for the boolean property.
message JSONSchemaPropsOrBool {
  optional bool allows = 1;

  optional JSONSchemaProps schema = 2;
}

// JSONSchemaPropsOrStringArray represents a JSONSchemaProps or a string array.
message JSONSchemaPropsOrStringArray {
  optional JSONSchemaProps schema = 1;

  repeated string property = 2;
}

// ServiceReference holds a reference to Service.legacy.k8s.io
message ServiceReference {
  // namespace is the namespace of the service.
  // Required
  optional string namespace = 1;

  // name is the name of the service.
  // Required
  optional string name = 2;

  // path is an optional URL path at which the webhook will be contacted.
  // +optional
  optional string path = 3;

  // port is an optional service port at which the webhook will be contacted.
  // `port` should be a valid port number (1-65535, inclusive).
  // Defaults to 443 for backward compatibility.
  // +optional
  optional int32 port = 4;
}

// ValidationRule describes a validation rule written in the CEL expression language.
message ValidationRule {
  // Rule represents the expression which will be evaluated by CEL.
  // ref: https://github.com/google/cel-spec
  // The Rule is scoped to the location of the x-kubernetes-validations extension in the schema.
  // The `self` variable in the CEL expression is bound to the scoped value.
  // Example:
  // - Rule scoped to the root of a resource with a status subresource: {"rule": "self.status.actual <= self.spec.maxDesired"}
  //
  // If the Rule is scoped to an object with properties, the accessible properties of the object are field selectable
  // via `self.field` and field presence can be checked via `has(self.field)`. Null valued fields are treated as
  // absent fields in CEL expressions.
  // If the Rule is scoped to an object with additionalProperties (i.e. a map) the value of the map
  // are accessible via `self[mapKey]`, map containment can be checked via `mapKey in self` and all entries of the map
  // are accessible via CEL macros and functions such as `self.all(...)`.
  // If the Rule is scoped to an array, the elements of the array are accessible via `self[i]` and also by macros and
  // functions.
  // If the Rule is scoped to a scalar, `self` is bound to the scalar value.
  // Examples:
  // - Rule scoped to a map of objects: {"rule": "self.components['Widget'].priority < 10"}
  // - Rule scoped to a list of integers: {"rule": "self.values.all(value, value >= 0 && value < 100)"}
  // - Rule scoped to a string value: {"rule": "self.startsWith('kube')"}
  //
  // The `apiVersion`, `kind`, `metadata.name` and `metadata.generateName` are always accessible from the root of the
  // object and from any x-kubernetes-embedded-resource annotated objects. No other metadata properties are accessible.
  //
  // Unknown data preserved in custom resources via x-kubernetes-preserve-unknown-fields is not accessible in CEL
  // expressions. This includes:
  // - Unknown field values that are preserved by object schemas with x-kubernetes-preserve-unknown-fields.
  // - Object properties where the property schema is of an "unknown type". An "unknown type" is recursively defined as:
  //   - A schema with no type and x-kubernetes-preserve-unknown-fields set to true
  //   - An array where the items schema is of an "unknown type"
  //   - An object where the additionalProperties schema is of an "unknown type"
  //
  // Only property names of the form `[a-zA-Z_.-/][a-zA-Z0-9_.-/]*` are accessible.
  // Accessible property names are escaped according to the following rules when accessed in the expression:
  // - '__' escapes to '__underscores__'
  // - '.' escapes to '__dot__'
  // - '-' escapes to '__dash__'
  // - '/' escapes to '__slash__'
  // - Property names that exactly match a CEL RESERVED keyword escape to '__{keyword}__'. The keywords are:
  // 	  "true", "false", "null", "in", "as", "break", "const", "continue", "else", "for", "function", "if",
  // 	  "import", "let", "loop", "package", "namespace", "return".
  // Examples:
  //   - Rule accessing a property named "namespace": {"rule": "self.__namespace__ > 0"}
  //   - Rule accessing a property named "x-prop": {"rule": "self.x__dash__prop > 0"}
  //   - Rule accessing a property named "redact__d": {"rule": "self.redact__underscores__d > 0"}
  //
  // Equality on arrays with x-kubernetes-list-type of 'set' or 'map' ignores element order, i.e. [1, 2] == [2, 1].
  // Concatenation on arrays with x-kubernetes-list-type use the semantics of the list type:
  //   - 'set': `X + Y` performs a union where the array positions of all elements in `X` are preserved and
  //     non-intersecting elements in `Y` are appended, retaining their partial order.
  //   - 'map': `X + Y` performs a merge where the array positions of all keys in `X` are preserved but the values
  //     are overwritten by values in `Y` when the key sets of `X` and `Y` intersect. Elements in `Y` with
  //     non-intersecting keys are appended, retaining their partial order.
  optional string rule = 1;

  // Message represents the message displayed when validation fails. The message is required if the Rule contains
  // line breaks. The message must not contain line breaks.
  // If unset, the message is "failed rule: {Rule}".
  // e.g. "must be a URL with the host matching spec.host"
  optional string message = 2;

  // MessageExpression declares a CEL expression that evaluates to the validation failure message that is returned when this rule fails.
  // Since messageExpression is used as a failure message, it must evaluate to a string.
  // If both message and messageExpression are present on a rule, then messageExpression will be used if validation
  // fails. If messageExpression results in a runtime error, the runtime error is logged, and the validation failure message is produced
  // as if the messageExpression field were unset. If messageExpression evaluates to an empty string, a string with only spaces, or a string
  // that contains line breaks, then the validation failure message will also be produced as if the messageExpression field were unset, and
  // the fact that messageExpression produced an empty string/string with only spaces/string with line breaks will be logged.
  // messageExpression has access to all the same variables as the rule; the only difference is the return type.
  // Example:
  // "x must be less than max ("+string(self.max)+")"
  // +optional
  optional string messageExpression = 3;

  // reason provides a machine-readable validation failure reason that is returned to the caller when a request fails this validation rule.
  // The HTTP status code returned to the caller will match the reason of the reason of the first failed validation rule.
  // The currently supported reasons are: "FieldValueInvalid", "FieldValueForbidden", "FieldValueRequired", "FieldValueDuplicate".
  // If not set, default to use "FieldValueInvalid".
  // All future added reasons must be accepted by clients when reading this value and unknown reasons should be treated as FieldValueInvalid.
  // +optional
  optional string reason = 4;

  // fieldPath represents the field path returned when the validation fails.
  // It must be a relative JSON path (i.e. with array notation) scoped to the location of this x-kubernetes-validations extension in the schema and refer to an existing field.
  // e.g. when validation checks if a specific attribute `foo` under a map `testMap`, the fieldPath could be set to `.testMap.foo`
  // If the validation checks two lists must have unique attributes, the fieldPath could be set to either of the list: e.g. `.testList`
  // It does not support list numeric index.
  // It supports child operation to refer to an existing field currently. Refer to [JSONPath support in Kubernetes](https://kubernetes.io/docs/reference/kubectl/jsonpath/) for more info.
  // Numeric index of array is not supported.
  // For field name which contains special characters, use `['specialName']` to refer the field name.
  // e.g. for attribute `foo.34$` appears in a list `testList`, the fieldPath could be set to `.testList['foo.34$']`
  // +optional
  optional string fieldPath = 5;
}

// WebhookClientConfig contains the information to make a TLS connection with the webhook.
message WebhookClientConfig {
  // url gives the location of the webhook, in standard URL form
  // (`scheme://host:port/path`). Exactly one of `url` or `service`
  // must be specified.
  //
  // The `host` should not refer to a service running in the cluster; use
  // the `service` field instead. The host might be resolved via external
  // DNS in some apiservers (e.g., `kube-apiserver` cannot resolve
  // in-cluster DNS as that would be a layering violation). `host` may
  // also be an IP address.
  //
  // Please note that using `localhost` or `127.0.0.1` as a `host` is
  // risky unless you take great care to run this webhook on all hosts
  // which run an apiserver which might need to make calls to this
  // webhook. Such installs are likely to be non-portable, i.e., not easy
  // to turn up in a new cluster.
  //
  // The scheme must be "https"; the URL must begin with "https://".
  //
  // A path is optional, and if present may be any string permissible in
  // a URL. You may use the path to pass an arbitrary string to the
  // webhook, for example, a cluster identifier.
  //
  // Attempting to use a user or basic auth e.g. "user:password@" is not
  // allowed. Fragments ("#...") and query parameters ("?...") are not
  // allowed, either.
  //
  // +optional
  optional string url = 3;

  // service is a reference to the service for this webhook. Either
  // service or url must be specified.
  //
  // If the webhook is running within the cluster, then you should use `service`.
  //
  // +optional
  optional ServiceReference service = 1;

  // caBundle is a PEM encoded CA bundle which will be used to validate the webhook's server certificate.
  // If unspecified, system trust roots on the apiserver are used.
  // +optional
  optional bytes caBundle = 2;
}

// WebhookConversion describes how to call a conversion webhook
message WebhookConversion {
  // clientConfig is the instructions for how to call the webhook if strategy is `Webhook`.
  // +optional
  optional WebhookClientConfig clientConfig = 2;

  // conversionReviewVersions is an ordered list of preferred `ConversionReview`
  // versions the Webhook expects. The API server will use the first version in
  // the list which it supports. If none of the versions specified in this list
  // are supported by API server, conversion will fail for the custom resource.
  // If a persisted Webhook configuration specifies allowed versions and does not
  // include any versions known to the API Server, calls to the webhook will fail.
  repeated string conversionReviewVersions = 3;
}

