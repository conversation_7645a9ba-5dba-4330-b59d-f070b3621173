/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by lister-gen. DO NOT EDIT.

package v1

// ComponentStatusListerExpansion allows custom methods to be added to
// ComponentStatusLister.
type ComponentStatusListerExpansion interface{}

// ConfigMapListerExpansion allows custom methods to be added to
// ConfigMapLister.
type ConfigMapListerExpansion interface{}

// ConfigMapNamespaceListerExpansion allows custom methods to be added to
// ConfigMapNamespaceLister.
type ConfigMapNamespaceListerExpansion interface{}

// EndpointsListerExpansion allows custom methods to be added to
// EndpointsLister.
type EndpointsListerExpansion interface{}

// EndpointsNamespaceListerExpansion allows custom methods to be added to
// EndpointsNamespaceLister.
type EndpointsNamespaceListerExpansion interface{}

// EventListerExpansion allows custom methods to be added to
// EventLister.
type EventListerExpansion interface{}

// EventNamespaceListerExpansion allows custom methods to be added to
// EventNamespaceLister.
type EventNamespaceListerExpansion interface{}

// LimitRangeListerExpansion allows custom methods to be added to
// LimitRangeLister.
type LimitRangeListerExpansion interface{}

// LimitRangeNamespaceListerExpansion allows custom methods to be added to
// LimitRangeNamespaceLister.
type LimitRangeNamespaceListerExpansion interface{}

// NamespaceListerExpansion allows custom methods to be added to
// NamespaceLister.
type NamespaceListerExpansion interface{}

// NodeListerExpansion allows custom methods to be added to
// NodeLister.
type NodeListerExpansion interface{}

// PersistentVolumeListerExpansion allows custom methods to be added to
// PersistentVolumeLister.
type PersistentVolumeListerExpansion interface{}

// PersistentVolumeClaimListerExpansion allows custom methods to be added to
// PersistentVolumeClaimLister.
type PersistentVolumeClaimListerExpansion interface{}

// PersistentVolumeClaimNamespaceListerExpansion allows custom methods to be added to
// PersistentVolumeClaimNamespaceLister.
type PersistentVolumeClaimNamespaceListerExpansion interface{}

// PodListerExpansion allows custom methods to be added to
// PodLister.
type PodListerExpansion interface{}

// PodNamespaceListerExpansion allows custom methods to be added to
// PodNamespaceLister.
type PodNamespaceListerExpansion interface{}

// PodTemplateListerExpansion allows custom methods to be added to
// PodTemplateLister.
type PodTemplateListerExpansion interface{}

// PodTemplateNamespaceListerExpansion allows custom methods to be added to
// PodTemplateNamespaceLister.
type PodTemplateNamespaceListerExpansion interface{}

// ResourceQuotaListerExpansion allows custom methods to be added to
// ResourceQuotaLister.
type ResourceQuotaListerExpansion interface{}

// ResourceQuotaNamespaceListerExpansion allows custom methods to be added to
// ResourceQuotaNamespaceLister.
type ResourceQuotaNamespaceListerExpansion interface{}

// SecretListerExpansion allows custom methods to be added to
// SecretLister.
type SecretListerExpansion interface{}

// SecretNamespaceListerExpansion allows custom methods to be added to
// SecretNamespaceLister.
type SecretNamespaceListerExpansion interface{}

// ServiceListerExpansion allows custom methods to be added to
// ServiceLister.
type ServiceListerExpansion interface{}

// ServiceNamespaceListerExpansion allows custom methods to be added to
// ServiceNamespaceLister.
type ServiceNamespaceListerExpansion interface{}

// ServiceAccountListerExpansion allows custom methods to be added to
// ServiceAccountLister.
type ServiceAccountListerExpansion interface{}

// ServiceAccountNamespaceListerExpansion allows custom methods to be added to
// ServiceAccountNamespaceLister.
type ServiceAccountNamespaceListerExpansion interface{}
