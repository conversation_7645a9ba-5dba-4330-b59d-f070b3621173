/*
 * 码云 Open API
 *
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * API version: 5.3.2
 * Generated by: Swagger Codegen (https://github.com/swagger-api/swagger-codegen.git)
 */

package gitee

import (
	"context"
	"io/ioutil"
	"net/http"
	"net/url"
	"strings"

	"github.com/antihax/optional"
)

// Linger please
var (
	_ context.Context
)

type SearchApiService service

/*
SearchApiService 搜索代码片段
搜索代码片段
 * @param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
 * @param q 搜索关键字
 * @param optional nil or *GetV5SearchGistsOpts - Optional Parameters:
     * @param "AccessToken" (optional.String) -  用户授权码
     * @param "Page" (optional.Int32) -  当前的页码
     * @param "PerPage" (optional.Int32) -  每页的数量，最大为 100
     * @param "Language" (optional.String) -  筛选指定语言的代码片段
     * @param "Owner" (optional.String) -  筛选所属用户 (username/login) 的代码片段
     * @param "Sort" (optional.String) -  排序字段，created_at(创建时间)、updated_at(更新时间)、notes_count(评论数)、stars_count(收藏数)、forks_count(Fork 数)，默认为最佳匹配
     * @param "Order" (optional.String) -  排序顺序: desc(default)、asc

@return []Code
*/

type GetV5SearchGistsOpts struct {
	AccessToken optional.String
	Page        optional.Int32
	PerPage     optional.Int32
	Language    optional.String
	Owner       optional.String
	Sort        optional.String
	Order       optional.String
}

func (a *SearchApiService) GetV5SearchGists(ctx context.Context, q string, localVarOptionals *GetV5SearchGistsOpts) ([]Code, *http.Response, error) {
	var (
		localVarHttpMethod  = strings.ToUpper("Get")
		localVarPostBody    interface{}
		localVarFileName    string
		localVarFileBytes   []byte
		localVarReturnValue []Code
	)

	// create path and map variables
	localVarPath := a.client.cfg.BasePath + "/v5/search/gists"

	localVarHeaderParams := make(map[string]string)
	localVarQueryParams := url.Values{}
	localVarFormParams := url.Values{}

	if localVarOptionals != nil && localVarOptionals.AccessToken.IsSet() {
		localVarQueryParams.Add("access_token", parameterToString(localVarOptionals.AccessToken.Value(), ""))
	}
	localVarQueryParams.Add("q", parameterToString(q, ""))
	if localVarOptionals != nil && localVarOptionals.Page.IsSet() {
		localVarQueryParams.Add("page", parameterToString(localVarOptionals.Page.Value(), ""))
	}
	if localVarOptionals != nil && localVarOptionals.PerPage.IsSet() {
		localVarQueryParams.Add("per_page", parameterToString(localVarOptionals.PerPage.Value(), ""))
	}
	if localVarOptionals != nil && localVarOptionals.Language.IsSet() {
		localVarQueryParams.Add("language", parameterToString(localVarOptionals.Language.Value(), ""))
	}
	if localVarOptionals != nil && localVarOptionals.Owner.IsSet() {
		localVarQueryParams.Add("owner", parameterToString(localVarOptionals.Owner.Value(), ""))
	}
	if localVarOptionals != nil && localVarOptionals.Sort.IsSet() {
		localVarQueryParams.Add("sort", parameterToString(localVarOptionals.Sort.Value(), ""))
	}
	if localVarOptionals != nil && localVarOptionals.Order.IsSet() {
		localVarQueryParams.Add("order", parameterToString(localVarOptionals.Order.Value(), ""))
	}
	// to determine the Content-Type header
	localVarHttpContentTypes := []string{"application/json", "multipart/form-data"}

	// set Content-Type header
	localVarHttpContentType := selectHeaderContentType(localVarHttpContentTypes)
	if localVarHttpContentType != "" {
		localVarHeaderParams["Content-Type"] = localVarHttpContentType
	}

	// to determine the Accept header
	localVarHttpHeaderAccepts := []string{"application/json"}

	// set Accept header
	localVarHttpHeaderAccept := selectHeaderAccept(localVarHttpHeaderAccepts)
	if localVarHttpHeaderAccept != "" {
		localVarHeaderParams["Accept"] = localVarHttpHeaderAccept
	}
	r, err := a.client.prepareRequest(ctx, localVarPath, localVarHttpMethod, localVarPostBody, localVarHeaderParams, localVarQueryParams, localVarFormParams, localVarFileName, localVarFileBytes)
	if err != nil {
		return localVarReturnValue, nil, err
	}

	localVarHttpResponse, err := a.client.callAPI(r)
	if err != nil || localVarHttpResponse == nil {
		return localVarReturnValue, localVarHttpResponse, err
	}

	localVarBody, err := ioutil.ReadAll(localVarHttpResponse.Body)
	localVarHttpResponse.Body.Close()
	if err != nil {
		return localVarReturnValue, localVarHttpResponse, err
	}

	if localVarHttpResponse.StatusCode < 300 {
		// If we succeed, return the data, otherwise pass on to decode error.
		err = a.client.decode(&localVarReturnValue, localVarBody, localVarHttpResponse.Header.Get("Content-Type"))
		if err == nil {
			return localVarReturnValue, localVarHttpResponse, err
		}
	}

	if localVarHttpResponse.StatusCode >= 300 {
		newErr := GenericSwaggerError{
			body:  localVarBody,
			error: localVarHttpResponse.Status,
		}

		if localVarHttpResponse.StatusCode == 200 {
			var v []Code
			err = a.client.decode(&v, localVarBody, localVarHttpResponse.Header.Get("Content-Type"))
			if err != nil {
				newErr.error = err.Error()
				return localVarReturnValue, localVarHttpResponse, newErr
			}
			newErr.model = v
			return localVarReturnValue, localVarHttpResponse, newErr
		}

		return localVarReturnValue, localVarHttpResponse, newErr
	}

	return localVarReturnValue, localVarHttpResponse, nil
}

/*
SearchApiService 搜索 Issues
搜索 Issues
 * @param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
 * @param q 搜索关键字
 * @param optional nil or *GetV5SearchIssuesOpts - Optional Parameters:
     * @param "AccessToken" (optional.String) -  用户授权码
     * @param "Page" (optional.Int32) -  当前的页码
     * @param "PerPage" (optional.Int32) -  每页的数量，最大为 100
     * @param "Repo" (optional.String) -  筛选指定仓库 (path, e.g. oschina/git-osc) 的 issues
     * @param "Language" (optional.String) -  筛选指定语言的 issues
     * @param "Label" (optional.String) -  筛选指定标签的 issues
     * @param "State" (optional.String) -  筛选指定状态的 issues, open(开启)、closed(完成)、rejected(拒绝)
     * @param "Author" (optional.String) -  筛选指定创建者 (username/login) 的 issues
     * @param "Assignee" (optional.String) -  筛选指定负责人 (username/login) 的 issues
     * @param "Sort" (optional.String) -  排序字段，created_at(创建时间)、last_push_at(更新时间)、notes_count(评论数)，默认为最佳匹配
     * @param "Order" (optional.String) -  排序顺序: desc(default)、asc

@return []Issue
*/

type GetV5SearchIssuesOpts struct {
	AccessToken optional.String
	Page        optional.Int32
	PerPage     optional.Int32
	Repo        optional.String
	Language    optional.String
	Label       optional.String
	State       optional.String
	Author      optional.String
	Assignee    optional.String
	Sort        optional.String
	Order       optional.String
}

func (a *SearchApiService) GetV5SearchIssues(ctx context.Context, q string, localVarOptionals *GetV5SearchIssuesOpts) ([]Issue, *http.Response, error) {
	var (
		localVarHttpMethod  = strings.ToUpper("Get")
		localVarPostBody    interface{}
		localVarFileName    string
		localVarFileBytes   []byte
		localVarReturnValue []Issue
	)

	// create path and map variables
	localVarPath := a.client.cfg.BasePath + "/v5/search/issues"

	localVarHeaderParams := make(map[string]string)
	localVarQueryParams := url.Values{}
	localVarFormParams := url.Values{}

	if localVarOptionals != nil && localVarOptionals.AccessToken.IsSet() {
		localVarQueryParams.Add("access_token", parameterToString(localVarOptionals.AccessToken.Value(), ""))
	}
	localVarQueryParams.Add("q", parameterToString(q, ""))
	if localVarOptionals != nil && localVarOptionals.Page.IsSet() {
		localVarQueryParams.Add("page", parameterToString(localVarOptionals.Page.Value(), ""))
	}
	if localVarOptionals != nil && localVarOptionals.PerPage.IsSet() {
		localVarQueryParams.Add("per_page", parameterToString(localVarOptionals.PerPage.Value(), ""))
	}
	if localVarOptionals != nil && localVarOptionals.Repo.IsSet() {
		localVarQueryParams.Add("repo", parameterToString(localVarOptionals.Repo.Value(), ""))
	}
	if localVarOptionals != nil && localVarOptionals.Language.IsSet() {
		localVarQueryParams.Add("language", parameterToString(localVarOptionals.Language.Value(), ""))
	}
	if localVarOptionals != nil && localVarOptionals.Label.IsSet() {
		localVarQueryParams.Add("label", parameterToString(localVarOptionals.Label.Value(), ""))
	}
	if localVarOptionals != nil && localVarOptionals.State.IsSet() {
		localVarQueryParams.Add("state", parameterToString(localVarOptionals.State.Value(), ""))
	}
	if localVarOptionals != nil && localVarOptionals.Author.IsSet() {
		localVarQueryParams.Add("author", parameterToString(localVarOptionals.Author.Value(), ""))
	}
	if localVarOptionals != nil && localVarOptionals.Assignee.IsSet() {
		localVarQueryParams.Add("assignee", parameterToString(localVarOptionals.Assignee.Value(), ""))
	}
	if localVarOptionals != nil && localVarOptionals.Sort.IsSet() {
		localVarQueryParams.Add("sort", parameterToString(localVarOptionals.Sort.Value(), ""))
	}
	if localVarOptionals != nil && localVarOptionals.Order.IsSet() {
		localVarQueryParams.Add("order", parameterToString(localVarOptionals.Order.Value(), ""))
	}
	// to determine the Content-Type header
	localVarHttpContentTypes := []string{"application/json", "multipart/form-data"}

	// set Content-Type header
	localVarHttpContentType := selectHeaderContentType(localVarHttpContentTypes)
	if localVarHttpContentType != "" {
		localVarHeaderParams["Content-Type"] = localVarHttpContentType
	}

	// to determine the Accept header
	localVarHttpHeaderAccepts := []string{"application/json"}

	// set Accept header
	localVarHttpHeaderAccept := selectHeaderAccept(localVarHttpHeaderAccepts)
	if localVarHttpHeaderAccept != "" {
		localVarHeaderParams["Accept"] = localVarHttpHeaderAccept
	}
	r, err := a.client.prepareRequest(ctx, localVarPath, localVarHttpMethod, localVarPostBody, localVarHeaderParams, localVarQueryParams, localVarFormParams, localVarFileName, localVarFileBytes)
	if err != nil {
		return localVarReturnValue, nil, err
	}

	localVarHttpResponse, err := a.client.callAPI(r)
	if err != nil || localVarHttpResponse == nil {
		return localVarReturnValue, localVarHttpResponse, err
	}

	localVarBody, err := ioutil.ReadAll(localVarHttpResponse.Body)
	localVarHttpResponse.Body.Close()
	if err != nil {
		return localVarReturnValue, localVarHttpResponse, err
	}

	if localVarHttpResponse.StatusCode < 300 {
		// If we succeed, return the data, otherwise pass on to decode error.
		err = a.client.decode(&localVarReturnValue, localVarBody, localVarHttpResponse.Header.Get("Content-Type"))
		if err == nil {
			return localVarReturnValue, localVarHttpResponse, err
		}
	}

	if localVarHttpResponse.StatusCode >= 300 {
		newErr := GenericSwaggerError{
			body:  localVarBody,
			error: localVarHttpResponse.Status,
		}

		if localVarHttpResponse.StatusCode == 200 {
			var v []Issue
			err = a.client.decode(&v, localVarBody, localVarHttpResponse.Header.Get("Content-Type"))
			if err != nil {
				newErr.error = err.Error()
				return localVarReturnValue, localVarHttpResponse, newErr
			}
			newErr.model = v
			return localVarReturnValue, localVarHttpResponse, newErr
		}

		return localVarReturnValue, localVarHttpResponse, newErr
	}

	return localVarReturnValue, localVarHttpResponse, nil
}

/*
SearchApiService 搜索仓库
搜索仓库
 * @param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
 * @param q 搜索关键字
 * @param optional nil or *GetV5SearchRepositoriesOpts - Optional Parameters:
     * @param "AccessToken" (optional.String) -  用户授权码
     * @param "Page" (optional.Int32) -  当前的页码
     * @param "PerPage" (optional.Int32) -  每页的数量，最大为 100
     * @param "Owner" (optional.String) -  筛选指定空间地址(企业、组织或个人的地址 path) 的仓库
     * @param "Fork" (optional.Bool) -  是否搜索含 fork 的仓库，默认：否
     * @param "Language" (optional.String) -  筛选指定语言的仓库
     * @param "Sort" (optional.String) -  排序字段，created_at(创建时间)、last_push_at(更新时间)、stars_count(收藏数)、forks_count(Fork 数)、watches_count(关注数)，默认为最佳匹配
     * @param "Order" (optional.String) -  排序顺序: desc(default)、asc

@return []Project
*/

type GetV5SearchRepositoriesOpts struct {
	AccessToken optional.String
	Page        optional.Int32
	PerPage     optional.Int32
	Owner       optional.String
	Fork        optional.Bool
	Language    optional.String
	Sort        optional.String
	Order       optional.String
}

func (a *SearchApiService) GetV5SearchRepositories(ctx context.Context, q string, localVarOptionals *GetV5SearchRepositoriesOpts) ([]Project, *http.Response, error) {
	var (
		localVarHttpMethod  = strings.ToUpper("Get")
		localVarPostBody    interface{}
		localVarFileName    string
		localVarFileBytes   []byte
		localVarReturnValue []Project
	)

	// create path and map variables
	localVarPath := a.client.cfg.BasePath + "/v5/search/repositories"

	localVarHeaderParams := make(map[string]string)
	localVarQueryParams := url.Values{}
	localVarFormParams := url.Values{}

	if localVarOptionals != nil && localVarOptionals.AccessToken.IsSet() {
		localVarQueryParams.Add("access_token", parameterToString(localVarOptionals.AccessToken.Value(), ""))
	}
	localVarQueryParams.Add("q", parameterToString(q, ""))
	if localVarOptionals != nil && localVarOptionals.Page.IsSet() {
		localVarQueryParams.Add("page", parameterToString(localVarOptionals.Page.Value(), ""))
	}
	if localVarOptionals != nil && localVarOptionals.PerPage.IsSet() {
		localVarQueryParams.Add("per_page", parameterToString(localVarOptionals.PerPage.Value(), ""))
	}
	if localVarOptionals != nil && localVarOptionals.Owner.IsSet() {
		localVarQueryParams.Add("owner", parameterToString(localVarOptionals.Owner.Value(), ""))
	}
	if localVarOptionals != nil && localVarOptionals.Fork.IsSet() {
		localVarQueryParams.Add("fork", parameterToString(localVarOptionals.Fork.Value(), ""))
	}
	if localVarOptionals != nil && localVarOptionals.Language.IsSet() {
		localVarQueryParams.Add("language", parameterToString(localVarOptionals.Language.Value(), ""))
	}
	if localVarOptionals != nil && localVarOptionals.Sort.IsSet() {
		localVarQueryParams.Add("sort", parameterToString(localVarOptionals.Sort.Value(), ""))
	}
	if localVarOptionals != nil && localVarOptionals.Order.IsSet() {
		localVarQueryParams.Add("order", parameterToString(localVarOptionals.Order.Value(), ""))
	}
	// to determine the Content-Type header
	localVarHttpContentTypes := []string{"application/json", "multipart/form-data"}

	// set Content-Type header
	localVarHttpContentType := selectHeaderContentType(localVarHttpContentTypes)
	if localVarHttpContentType != "" {
		localVarHeaderParams["Content-Type"] = localVarHttpContentType
	}

	// to determine the Accept header
	localVarHttpHeaderAccepts := []string{"application/json"}

	// set Accept header
	localVarHttpHeaderAccept := selectHeaderAccept(localVarHttpHeaderAccepts)
	if localVarHttpHeaderAccept != "" {
		localVarHeaderParams["Accept"] = localVarHttpHeaderAccept
	}
	r, err := a.client.prepareRequest(ctx, localVarPath, localVarHttpMethod, localVarPostBody, localVarHeaderParams, localVarQueryParams, localVarFormParams, localVarFileName, localVarFileBytes)
	if err != nil {
		return localVarReturnValue, nil, err
	}

	localVarHttpResponse, err := a.client.callAPI(r)
	if err != nil || localVarHttpResponse == nil {
		return localVarReturnValue, localVarHttpResponse, err
	}

	localVarBody, err := ioutil.ReadAll(localVarHttpResponse.Body)
	localVarHttpResponse.Body.Close()
	if err != nil {
		return localVarReturnValue, localVarHttpResponse, err
	}

	if localVarHttpResponse.StatusCode < 300 {
		// If we succeed, return the data, otherwise pass on to decode error.
		err = a.client.decode(&localVarReturnValue, localVarBody, localVarHttpResponse.Header.Get("Content-Type"))
		if err == nil {
			return localVarReturnValue, localVarHttpResponse, err
		}
	}

	if localVarHttpResponse.StatusCode >= 300 {
		newErr := GenericSwaggerError{
			body:  localVarBody,
			error: localVarHttpResponse.Status,
		}

		if localVarHttpResponse.StatusCode == 200 {
			var v []Project
			err = a.client.decode(&v, localVarBody, localVarHttpResponse.Header.Get("Content-Type"))
			if err != nil {
				newErr.error = err.Error()
				return localVarReturnValue, localVarHttpResponse, newErr
			}
			newErr.model = v
			return localVarReturnValue, localVarHttpResponse, newErr
		}

		return localVarReturnValue, localVarHttpResponse, newErr
	}

	return localVarReturnValue, localVarHttpResponse, nil
}

/*
SearchApiService 搜索用户
搜索用户
 * @param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
 * @param q 搜索关键字
 * @param optional nil or *GetV5SearchUsersOpts - Optional Parameters:
     * @param "AccessToken" (optional.String) -  用户授权码
     * @param "Page" (optional.Int32) -  当前的页码
     * @param "PerPage" (optional.Int32) -  每页的数量，最大为 100
     * @param "Sort" (optional.String) -  排序字段，joined_at(注册时间)，默认为最佳匹配
     * @param "Order" (optional.String) -  排序顺序: desc(default)、asc

@return []User
*/

type GetV5SearchUsersOpts struct {
	AccessToken optional.String
	Page        optional.Int32
	PerPage     optional.Int32
	Sort        optional.String
	Order       optional.String
}

func (a *SearchApiService) GetV5SearchUsers(ctx context.Context, q string, localVarOptionals *GetV5SearchUsersOpts) ([]User, *http.Response, error) {
	var (
		localVarHttpMethod  = strings.ToUpper("Get")
		localVarPostBody    interface{}
		localVarFileName    string
		localVarFileBytes   []byte
		localVarReturnValue []User
	)

	// create path and map variables
	localVarPath := a.client.cfg.BasePath + "/v5/search/users"

	localVarHeaderParams := make(map[string]string)
	localVarQueryParams := url.Values{}
	localVarFormParams := url.Values{}

	if localVarOptionals != nil && localVarOptionals.AccessToken.IsSet() {
		localVarQueryParams.Add("access_token", parameterToString(localVarOptionals.AccessToken.Value(), ""))
	}
	localVarQueryParams.Add("q", parameterToString(q, ""))
	if localVarOptionals != nil && localVarOptionals.Page.IsSet() {
		localVarQueryParams.Add("page", parameterToString(localVarOptionals.Page.Value(), ""))
	}
	if localVarOptionals != nil && localVarOptionals.PerPage.IsSet() {
		localVarQueryParams.Add("per_page", parameterToString(localVarOptionals.PerPage.Value(), ""))
	}
	if localVarOptionals != nil && localVarOptionals.Sort.IsSet() {
		localVarQueryParams.Add("sort", parameterToString(localVarOptionals.Sort.Value(), ""))
	}
	if localVarOptionals != nil && localVarOptionals.Order.IsSet() {
		localVarQueryParams.Add("order", parameterToString(localVarOptionals.Order.Value(), ""))
	}
	// to determine the Content-Type header
	localVarHttpContentTypes := []string{"application/json", "multipart/form-data"}

	// set Content-Type header
	localVarHttpContentType := selectHeaderContentType(localVarHttpContentTypes)
	if localVarHttpContentType != "" {
		localVarHeaderParams["Content-Type"] = localVarHttpContentType
	}

	// to determine the Accept header
	localVarHttpHeaderAccepts := []string{"application/json"}

	// set Accept header
	localVarHttpHeaderAccept := selectHeaderAccept(localVarHttpHeaderAccepts)
	if localVarHttpHeaderAccept != "" {
		localVarHeaderParams["Accept"] = localVarHttpHeaderAccept
	}
	r, err := a.client.prepareRequest(ctx, localVarPath, localVarHttpMethod, localVarPostBody, localVarHeaderParams, localVarQueryParams, localVarFormParams, localVarFileName, localVarFileBytes)
	if err != nil {
		return localVarReturnValue, nil, err
	}

	localVarHttpResponse, err := a.client.callAPI(r)
	if err != nil || localVarHttpResponse == nil {
		return localVarReturnValue, localVarHttpResponse, err
	}

	localVarBody, err := ioutil.ReadAll(localVarHttpResponse.Body)
	localVarHttpResponse.Body.Close()
	if err != nil {
		return localVarReturnValue, localVarHttpResponse, err
	}

	if localVarHttpResponse.StatusCode < 300 {
		// If we succeed, return the data, otherwise pass on to decode error.
		err = a.client.decode(&localVarReturnValue, localVarBody, localVarHttpResponse.Header.Get("Content-Type"))
		if err == nil {
			return localVarReturnValue, localVarHttpResponse, err
		}
	}

	if localVarHttpResponse.StatusCode >= 300 {
		newErr := GenericSwaggerError{
			body:  localVarBody,
			error: localVarHttpResponse.Status,
		}

		if localVarHttpResponse.StatusCode == 200 {
			var v []User
			err = a.client.decode(&v, localVarBody, localVarHttpResponse.Header.Get("Content-Type"))
			if err != nil {
				newErr.error = err.Error()
				return localVarReturnValue, localVarHttpResponse, newErr
			}
			newErr.model = v
			return localVarReturnValue, localVarHttpResponse, newErr
		}

		return localVarReturnValue, localVarHttpResponse, newErr
	}

	return localVarReturnValue, localVarHttpResponse, nil
}
