// Copyright Istio Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//    http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by client-gen. DO NOT EDIT.

package v1alpha3

import (
	"context"
	json "encoding/json"
	"fmt"
	"time"

	v1alpha3 "istio.io/client-go/pkg/apis/networking/v1alpha3"
	networkingv1alpha3 "istio.io/client-go/pkg/applyconfiguration/networking/v1alpha3"
	scheme "istio.io/client-go/pkg/clientset/versioned/scheme"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	rest "k8s.io/client-go/rest"
)

// SidecarsGetter has a method to return a SidecarInterface.
// A group's client should implement this interface.
type SidecarsGetter interface {
	Sidecars(namespace string) SidecarInterface
}

// SidecarInterface has methods to work with Sidecar resources.
type SidecarInterface interface {
	Create(ctx context.Context, sidecar *v1alpha3.Sidecar, opts v1.CreateOptions) (*v1alpha3.Sidecar, error)
	Update(ctx context.Context, sidecar *v1alpha3.Sidecar, opts v1.UpdateOptions) (*v1alpha3.Sidecar, error)
	UpdateStatus(ctx context.Context, sidecar *v1alpha3.Sidecar, opts v1.UpdateOptions) (*v1alpha3.Sidecar, error)
	Delete(ctx context.Context, name string, opts v1.DeleteOptions) error
	DeleteCollection(ctx context.Context, opts v1.DeleteOptions, listOpts v1.ListOptions) error
	Get(ctx context.Context, name string, opts v1.GetOptions) (*v1alpha3.Sidecar, error)
	List(ctx context.Context, opts v1.ListOptions) (*v1alpha3.SidecarList, error)
	Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error)
	Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *v1alpha3.Sidecar, err error)
	Apply(ctx context.Context, sidecar *networkingv1alpha3.SidecarApplyConfiguration, opts v1.ApplyOptions) (result *v1alpha3.Sidecar, err error)
	ApplyStatus(ctx context.Context, sidecar *networkingv1alpha3.SidecarApplyConfiguration, opts v1.ApplyOptions) (result *v1alpha3.Sidecar, err error)
	SidecarExpansion
}

// sidecars implements SidecarInterface
type sidecars struct {
	client rest.Interface
	ns     string
}

// newSidecars returns a Sidecars
func newSidecars(c *NetworkingV1alpha3Client, namespace string) *sidecars {
	return &sidecars{
		client: c.RESTClient(),
		ns:     namespace,
	}
}

// Get takes name of the sidecar, and returns the corresponding sidecar object, and an error if there is any.
func (c *sidecars) Get(ctx context.Context, name string, options v1.GetOptions) (result *v1alpha3.Sidecar, err error) {
	result = &v1alpha3.Sidecar{}
	err = c.client.Get().
		Namespace(c.ns).
		Resource("sidecars").
		Name(name).
		VersionedParams(&options, scheme.ParameterCodec).
		Do(ctx).
		Into(result)
	return
}

// List takes label and field selectors, and returns the list of Sidecars that match those selectors.
func (c *sidecars) List(ctx context.Context, opts v1.ListOptions) (result *v1alpha3.SidecarList, err error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	result = &v1alpha3.SidecarList{}
	err = c.client.Get().
		Namespace(c.ns).
		Resource("sidecars").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Do(ctx).
		Into(result)
	return
}

// Watch returns a watch.Interface that watches the requested sidecars.
func (c *sidecars) Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	opts.Watch = true
	return c.client.Get().
		Namespace(c.ns).
		Resource("sidecars").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Watch(ctx)
}

// Create takes the representation of a sidecar and creates it.  Returns the server's representation of the sidecar, and an error, if there is any.
func (c *sidecars) Create(ctx context.Context, sidecar *v1alpha3.Sidecar, opts v1.CreateOptions) (result *v1alpha3.Sidecar, err error) {
	result = &v1alpha3.Sidecar{}
	err = c.client.Post().
		Namespace(c.ns).
		Resource("sidecars").
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(sidecar).
		Do(ctx).
		Into(result)
	return
}

// Update takes the representation of a sidecar and updates it. Returns the server's representation of the sidecar, and an error, if there is any.
func (c *sidecars) Update(ctx context.Context, sidecar *v1alpha3.Sidecar, opts v1.UpdateOptions) (result *v1alpha3.Sidecar, err error) {
	result = &v1alpha3.Sidecar{}
	err = c.client.Put().
		Namespace(c.ns).
		Resource("sidecars").
		Name(sidecar.Name).
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(sidecar).
		Do(ctx).
		Into(result)
	return
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
func (c *sidecars) UpdateStatus(ctx context.Context, sidecar *v1alpha3.Sidecar, opts v1.UpdateOptions) (result *v1alpha3.Sidecar, err error) {
	result = &v1alpha3.Sidecar{}
	err = c.client.Put().
		Namespace(c.ns).
		Resource("sidecars").
		Name(sidecar.Name).
		SubResource("status").
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(sidecar).
		Do(ctx).
		Into(result)
	return
}

// Delete takes name of the sidecar and deletes it. Returns an error if one occurs.
func (c *sidecars) Delete(ctx context.Context, name string, opts v1.DeleteOptions) error {
	return c.client.Delete().
		Namespace(c.ns).
		Resource("sidecars").
		Name(name).
		Body(&opts).
		Do(ctx).
		Error()
}

// DeleteCollection deletes a collection of objects.
func (c *sidecars) DeleteCollection(ctx context.Context, opts v1.DeleteOptions, listOpts v1.ListOptions) error {
	var timeout time.Duration
	if listOpts.TimeoutSeconds != nil {
		timeout = time.Duration(*listOpts.TimeoutSeconds) * time.Second
	}
	return c.client.Delete().
		Namespace(c.ns).
		Resource("sidecars").
		VersionedParams(&listOpts, scheme.ParameterCodec).
		Timeout(timeout).
		Body(&opts).
		Do(ctx).
		Error()
}

// Patch applies the patch and returns the patched sidecar.
func (c *sidecars) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *v1alpha3.Sidecar, err error) {
	result = &v1alpha3.Sidecar{}
	err = c.client.Patch(pt).
		Namespace(c.ns).
		Resource("sidecars").
		Name(name).
		SubResource(subresources...).
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(data).
		Do(ctx).
		Into(result)
	return
}

// Apply takes the given apply declarative configuration, applies it and returns the applied sidecar.
func (c *sidecars) Apply(ctx context.Context, sidecar *networkingv1alpha3.SidecarApplyConfiguration, opts v1.ApplyOptions) (result *v1alpha3.Sidecar, err error) {
	if sidecar == nil {
		return nil, fmt.Errorf("sidecar provided to Apply must not be nil")
	}
	patchOpts := opts.ToPatchOptions()
	data, err := json.Marshal(sidecar)
	if err != nil {
		return nil, err
	}
	name := sidecar.Name
	if name == nil {
		return nil, fmt.Errorf("sidecar.Name must be provided to Apply")
	}
	result = &v1alpha3.Sidecar{}
	err = c.client.Patch(types.ApplyPatchType).
		Namespace(c.ns).
		Resource("sidecars").
		Name(*name).
		VersionedParams(&patchOpts, scheme.ParameterCodec).
		Body(data).
		Do(ctx).
		Into(result)
	return
}

// ApplyStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating ApplyStatus().
func (c *sidecars) ApplyStatus(ctx context.Context, sidecar *networkingv1alpha3.SidecarApplyConfiguration, opts v1.ApplyOptions) (result *v1alpha3.Sidecar, err error) {
	if sidecar == nil {
		return nil, fmt.Errorf("sidecar provided to Apply must not be nil")
	}
	patchOpts := opts.ToPatchOptions()
	data, err := json.Marshal(sidecar)
	if err != nil {
		return nil, err
	}

	name := sidecar.Name
	if name == nil {
		return nil, fmt.Errorf("sidecar.Name must be provided to Apply")
	}

	result = &v1alpha3.Sidecar{}
	err = c.client.Patch(types.ApplyPatchType).
		Namespace(c.ns).
		Resource("sidecars").
		Name(*name).
		SubResource("status").
		VersionedParams(&patchOpts, scheme.ParameterCodec).
		Body(data).
		Do(ctx).
		Into(result)
	return
}
