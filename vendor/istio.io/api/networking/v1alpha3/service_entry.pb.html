---
title: Service Entry
description: Configuration affecting service registry.
location: https://istio.io/docs/reference/config/networking/service-entry.html
layout: protoc-gen-docs
generator: protoc-gen-docs
schema: istio.networking.v1alpha3.ServiceEntry
aliases: [/docs/reference/config/networking/v1alpha3/service-entry]
number_of_entries: 3
---
<p><code>ServiceEntry</code> enables adding additional entries into Istio&rsquo;s
internal service registry, so that auto-discovered services in the
mesh can access/route to these manually specified services. A
service entry describes the properties of a service (DNS name,
VIPs, ports, protocols, endpoints). These services could be
external to the mesh (e.g., web APIs) or mesh-internal services
that are not part of the platform&rsquo;s service registry (e.g., a set
of VMs talking to services in Kubernetes). In addition, the
endpoints of a service entry can also be dynamically selected by
using the <code>workloadSelector</code> field. These endpoints can be VM
workloads declared using the <code>WorkloadEntry</code> object or Kubernetes
pods. The ability to select both pods and VMs under a single
service allows for migration of services from VMs to Kubernetes
without having to change the existing DNS names associated with the
services.</p>
<p>The following example declares a few external APIs accessed by internal
applications over HTTPS. The sidecar inspects the SNI value in the
ClientHello message to route to the appropriate external service.</p>
<p>{{<tabset category-name="example">}}
{{<tab name="v1alpha3" category-value="v1alpha3">}}</p>
<pre><code class="language-yaml">apiVersion: networking.istio.io/v1alpha3
kind: ServiceEntry
metadata:
  name: external-svc-https
spec:
  hosts:
  - api.dropboxapi.com
  - www.googleapis.com
  - api.facebook.com
  location: MESH_EXTERNAL
  ports:
  - number: 443
    name: https
    protocol: TLS
  resolution: DNS
</code></pre>
<p>{{</tab>}}</p>
<p>{{<tab name="v1beta1" category-value="v1beta1">}}</p>
<pre><code class="language-yaml">apiVersion: networking.istio.io/v1beta1
kind: ServiceEntry
metadata:
  name: external-svc-https
spec:
  hosts:
  - api.dropboxapi.com
  - www.googleapis.com
  - api.facebook.com
  location: MESH_EXTERNAL
  ports:
  - number: 443
    name: https
    protocol: TLS
  resolution: DNS
</code></pre>
<p>{{</tab>}}
{{</tabset>}}</p>
<p>The following configuration adds a set of MongoDB instances running on
unmanaged VMs to Istio&rsquo;s registry, so that these services can be treated
as any other service in the mesh. The associated DestinationRule is used
to initiate mTLS connections to the database instances.</p>
<p>{{<tabset category-name="example">}}
{{<tab name="v1alpha3" category-value="v1alpha3">}}</p>
<pre><code class="language-yaml">apiVersion: networking.istio.io/v1alpha3
kind: ServiceEntry
metadata:
  name: external-svc-mongocluster
spec:
  hosts:
  - mymongodb.somedomain # not used
  addresses:
  - ***************/24 # VIPs
  ports:
  - number: 27018
    name: mongodb
    protocol: MONGO
  location: MESH_INTERNAL
  resolution: STATIC
  endpoints:
  - address: *******
  - address: *******
</code></pre>
<p>{{</tab>}}</p>
<p>{{<tab name="v1beta1" category-value="v1beta1">}}</p>
<pre><code class="language-yaml">apiVersion: networking.istio.io/v1beta1
kind: ServiceEntry
metadata:
  name: external-svc-mongocluster
spec:
  hosts:
  - mymongodb.somedomain # not used
  addresses:
  - ***************/24 # VIPs
  ports:
  - number: 27018
    name: mongodb
    protocol: MONGO
  location: MESH_INTERNAL
  resolution: STATIC
  endpoints:
  - address: *******
  - address: *******
</code></pre>
<p>{{</tab>}}
{{</tabset>}}</p>
<p>and the associated DestinationRule</p>
<p>{{<tabset category-name="example">}}
{{<tab name="v1alpha3" category-value="v1alpha3">}}</p>
<pre><code class="language-yaml">apiVersion: networking.istio.io/v1alpha3
kind: DestinationRule
metadata:
  name: mtls-mongocluster
spec:
  host: mymongodb.somedomain
  trafficPolicy:
    tls:
      mode: MUTUAL
      clientCertificate: /etc/certs/myclientcert.pem
      privateKey: /etc/certs/client_private_key.pem
      caCertificates: /etc/certs/rootcacerts.pem
</code></pre>
<p>{{</tab>}}</p>
<p>{{<tab name="v1beta1" category-value="v1beta1">}}</p>
<pre><code class="language-yaml">apiVersion: networking.istio.io/v1beta1
kind: DestinationRule
metadata:
  name: mtls-mongocluster
spec:
  host: mymongodb.somedomain
  trafficPolicy:
    tls:
      mode: MUTUAL
      clientCertificate: /etc/certs/myclientcert.pem
      privateKey: /etc/certs/client_private_key.pem
      caCertificates: /etc/certs/rootcacerts.pem
</code></pre>
<p>{{</tab>}}
{{</tabset>}}</p>
<p>The following example uses a combination of service entry and TLS
routing in a virtual service to steer traffic based on the SNI value to
an internal egress firewall.</p>
<p>{{<tabset category-name="example">}}
{{<tab name="v1alpha3" category-value="v1alpha3">}}</p>
<pre><code class="language-yaml">apiVersion: networking.istio.io/v1alpha3
kind: ServiceEntry
metadata:
  name: external-svc-redirect
spec:
  hosts:
  - wikipedia.org
  - &quot;*.wikipedia.org&quot;
  location: MESH_EXTERNAL
  ports:
  - number: 443
    name: https
    protocol: TLS
  resolution: NONE
</code></pre>
<p>{{</tab>}}</p>
<p>{{<tab name="v1beta1" category-value="v1beta1">}}</p>
<pre><code class="language-yaml">apiVersion: networking.istio.io/v1beta1
kind: ServiceEntry
metadata:
  name: external-svc-redirect
spec:
  hosts:
  - wikipedia.org
  - &quot;*.wikipedia.org&quot;
  location: MESH_EXTERNAL
  ports:
  - number: 443
    name: https
    protocol: TLS
  resolution: NONE
</code></pre>
<p>{{</tab>}}
{{</tabset>}}</p>
<p>And the associated VirtualService to route based on the SNI value.</p>
<p>{{<tabset category-name="example">}}
{{<tab name="v1alpha3" category-value="v1alpha3">}}</p>
<pre><code class="language-yaml">apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: tls-routing
spec:
  hosts:
  - wikipedia.org
  - &quot;*.wikipedia.org&quot;
  tls:
  - match:
    - sniHosts:
      - wikipedia.org
      - &quot;*.wikipedia.org&quot;
    route:
    - destination:
        host: internal-egress-firewall.ns1.svc.cluster.local
</code></pre>
<p>{{</tab>}}</p>
<p>{{<tab name="v1beta1" category-value="v1beta1">}}</p>
<pre><code class="language-yaml">apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: tls-routing
spec:
  hosts:
  - wikipedia.org
  - &quot;*.wikipedia.org&quot;
  tls:
  - match:
    - sniHosts:
      - wikipedia.org
      - &quot;*.wikipedia.org&quot;
    route:
    - destination:
        host: internal-egress-firewall.ns1.svc.cluster.local
</code></pre>
<p>{{</tab>}}
{{</tabset>}}</p>
<p>The virtual service with TLS match serves to override the default SNI
match. In the absence of a virtual service, traffic will be forwarded to
the wikipedia domains.</p>
<p>The following example demonstrates the use of a dedicated egress gateway
through which all external service traffic is forwarded.
The &rsquo;exportTo&rsquo; field allows for control over the visibility of a service
declaration to other namespaces in the mesh. By default, a service is exported
to all namespaces. The following example restricts the visibility to the
current namespace, represented by &ldquo;.&rdquo;, so that it cannot be used by other
namespaces.</p>
<p>{{<tabset category-name="example">}}
{{<tab name="v1alpha3" category-value="v1alpha3">}}</p>
<pre><code class="language-yaml">apiVersion: networking.istio.io/v1alpha3
kind: ServiceEntry
metadata:
  name: external-svc-httpbin
  namespace : egress
spec:
  hosts:
  - example.com
  exportTo:
  - &quot;.&quot;
  location: MESH_EXTERNAL
  ports:
  - number: 80
    name: http
    protocol: HTTP
  resolution: DNS
</code></pre>
<p>{{</tab>}}</p>
<p>{{<tab name="v1beta1" category-value="v1beta1">}}</p>
<pre><code class="language-yaml">apiVersion: networking.istio.io/v1beta1
kind: ServiceEntry
metadata:
  name: external-svc-httpbin
  namespace : egress
spec:
  hosts:
  - example.com
  exportTo:
  - &quot;.&quot;
  location: MESH_EXTERNAL
  ports:
  - number: 80
    name: http
    protocol: HTTP
  resolution: DNS
</code></pre>
<p>{{</tab>}}
{{</tabset>}}</p>
<p>Define a gateway to handle all egress traffic.</p>
<p>{{<tabset category-name="example">}}
{{<tab name="v1alpha3" category-value="v1alpha3">}}</p>
<pre><code class="language-yaml">apiVersion: networking.istio.io/v1alpha3
kind: Gateway
metadata:
 name: istio-egressgateway
 namespace: istio-system
spec:
 selector:
   istio: egressgateway
 servers:
 - port:
     number: 80
     name: http
     protocol: HTTP
   hosts:
   - &quot;*&quot;
</code></pre>
<p>{{</tab>}}</p>
<p>{{<tab name="v1beta1" category-value="v1beta1">}}</p>
<pre><code class="language-yaml">apiVersion: networking.istio.io/v1beta1
kind: Gateway
metadata:
 name: istio-egressgateway
 namespace: istio-system
spec:
 selector:
   istio: egressgateway
 servers:
 - port:
     number: 80
     name: http
     protocol: HTTP
   hosts:
   - &quot;*&quot;
</code></pre>
<p>{{</tab>}}
{{</tabset>}}</p>
<p>And the associated <code>VirtualService</code> to route from the sidecar to the
gateway service (<code>istio-egressgateway.istio-system.svc.cluster.local</code>), as
well as route from the gateway to the external service. Note that the
virtual service is exported to all namespaces enabling them to route traffic
through the gateway to the external service. Forcing traffic to go through
a managed middle proxy like this is a common practice.</p>
<p>{{<tabset category-name="example">}}
{{<tab name="v1alpha3" category-value="v1alpha3">}}</p>
<pre><code class="language-yaml">apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: gateway-routing
  namespace: egress
spec:
  hosts:
  - example.com
  exportTo:
  - &quot;*&quot;
  gateways:
  - mesh
  - istio-egressgateway
  http:
  - match:
    - port: 80
      gateways:
      - mesh
    route:
    - destination:
        host: istio-egressgateway.istio-system.svc.cluster.local
  - match:
    - port: 80
      gateways:
      - istio-egressgateway
    route:
    - destination:
        host: example.com
</code></pre>
<p>{{</tab>}}</p>
<p>{{<tab name="v1beta1" category-value="v1beta1">}}</p>
<pre><code class="language-yaml">apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: gateway-routing
  namespace: egress
spec:
  hosts:
  - example.com
  exportTo:
  - &quot;*&quot;
  gateways:
  - mesh
  - istio-egressgateway
  http:
  - match:
    - port: 80
      gateways:
      - mesh
    route:
    - destination:
        host: istio-egressgateway.istio-system.svc.cluster.local
  - match:
    - port: 80
      gateways:
      - istio-egressgateway
    route:
    - destination:
        host: example.com
</code></pre>
<p>{{</tab>}}
{{</tabset>}}</p>
<p>The following example demonstrates the use of wildcards in the hosts for
external services. If the connection has to be routed to the IP address
requested by the application (i.e. application resolves DNS and attempts
to connect to a specific IP), the discovery mode must be set to <code>NONE</code>.</p>
<p>{{<tabset category-name="example">}}
{{<tab name="v1alpha3" category-value="v1alpha3">}}</p>
<pre><code class="language-yaml">apiVersion: networking.istio.io/v1alpha3
kind: ServiceEntry
metadata:
  name: external-svc-wildcard-example
spec:
  hosts:
  - &quot;*.bar.com&quot;
  location: MESH_EXTERNAL
  ports:
  - number: 80
    name: http
    protocol: HTTP
  resolution: NONE
</code></pre>
<p>{{</tab>}}</p>
<p>{{<tab name="v1beta1" category-value="v1beta1">}}</p>
<pre><code class="language-yaml">apiVersion: networking.istio.io/v1beta1
kind: ServiceEntry
metadata:
  name: external-svc-wildcard-example
spec:
  hosts:
  - &quot;*.bar.com&quot;
  location: MESH_EXTERNAL
  ports:
  - number: 80
    name: http
    protocol: HTTP
  resolution: NONE
</code></pre>
<p>{{</tab>}}
{{</tabset>}}</p>
<p>The following example demonstrates a service that is available via a
Unix Domain Socket on the host of the client. The resolution must be
set to STATIC to use Unix address endpoints.</p>
<p>{{<tabset category-name="example">}}
{{<tab name="v1alpha3" category-value="v1alpha3">}}</p>
<pre><code class="language-yaml">apiVersion: networking.istio.io/v1alpha3
kind: ServiceEntry
metadata:
  name: unix-domain-socket-example
spec:
  hosts:
  - &quot;example.unix.local&quot;
  location: MESH_EXTERNAL
  ports:
  - number: 80
    name: http
    protocol: HTTP
  resolution: STATIC
  endpoints:
  - address: unix:///var/run/example/socket
</code></pre>
<p>{{</tab>}}</p>
<p>{{<tab name="v1beta1" category-value="v1beta1">}}</p>
<pre><code class="language-yaml">apiVersion: networking.istio.io/v1beta1
kind: ServiceEntry
metadata:
  name: unix-domain-socket-example
spec:
  hosts:
  - &quot;example.unix.local&quot;
  location: MESH_EXTERNAL
  ports:
  - number: 80
    name: http
    protocol: HTTP
  resolution: STATIC
  endpoints:
  - address: unix:///var/run/example/socket
</code></pre>
<p>{{</tab>}}
{{</tabset>}}</p>
<p>For HTTP-based services, it is possible to create a <code>VirtualService</code>
backed by multiple DNS addressable endpoints. In such a scenario, the
application can use the <code>HTTP_PROXY</code> environment variable to transparently
reroute API calls for the <code>VirtualService</code> to a chosen backend. For
example, the following configuration creates a non-existent external
service called foo.bar.com backed by three domains: us.foo.bar.com:8080,
uk.foo.bar.com:9080, and in.foo.bar.com:7080</p>
<p>{{<tabset category-name="example">}}
{{<tab name="v1alpha3" category-value="v1alpha3">}}</p>
<pre><code class="language-yaml">apiVersion: networking.istio.io/v1alpha3
kind: ServiceEntry
metadata:
  name: external-svc-dns
spec:
  hosts:
  - foo.bar.com
  location: MESH_EXTERNAL
  ports:
  - number: 80
    name: http
    protocol: HTTP
  resolution: DNS
  endpoints:
  - address: us.foo.bar.com
    ports:
      http: 8080
  - address: uk.foo.bar.com
    ports:
      http: 9080
  - address: in.foo.bar.com
    ports:
      http: 7080
</code></pre>
<p>{{</tab>}}</p>
<p>{{<tab name="v1beta1" category-value="v1beta1">}}</p>
<pre><code class="language-yaml">apiVersion: networking.istio.io/v1beta1
kind: ServiceEntry
metadata:
  name: external-svc-dns
spec:
  hosts:
  - foo.bar.com
  location: MESH_EXTERNAL
  ports:
  - number: 80
    name: http
    protocol: HTTP
  resolution: DNS
  endpoints:
  - address: us.foo.bar.com
    ports:
      http: 8080
  - address: uk.foo.bar.com
    ports:
      http: 9080
  - address: in.foo.bar.com
    ports:
      http: 7080
</code></pre>
<p>{{</tab>}}
{{</tabset>}}</p>
<p>With <code>HTTP_PROXY=http://localhost/</code>, calls from the application to
<code>http://foo.bar.com</code> will be load balanced across the three domains
specified above. In other words, a call to <code>http://foo.bar.com/baz</code> would
be translated to <code>http://uk.foo.bar.com/baz</code>.</p>
<p>The following example illustrates the usage of a <code>ServiceEntry</code>
containing a subject alternate name
whose format conforms to the <a href="https://github.com/spiffe/spiffe/blob/master/standards/SPIFFE-ID.md">SPIFFE standard</a>:</p>
<p>{{<tabset category-name="example">}}
{{<tab name="v1alpha3" category-value="v1alpha3">}}</p>
<pre><code class="language-yaml">apiVersion: networking.istio.io/v1alpha3
kind: ServiceEntry
metadata:
  name: httpbin
  namespace : httpbin-ns
spec:
  hosts:
  - example.com
  location: MESH_INTERNAL
  ports:
  - number: 80
    name: http
    protocol: HTTP
  resolution: STATIC
  endpoints:
  - address: *******
  - address: *******
  subjectAltNames:
  - &quot;spiffe://cluster.local/ns/httpbin-ns/sa/httpbin-service-account&quot;
</code></pre>
<p>{{</tab>}}</p>
<p>{{<tab name="v1beta1" category-value="v1beta1">}}</p>
<pre><code class="language-yaml">apiVersion: networking.istio.io/v1beta1
kind: ServiceEntry
metadata:
  name: httpbin
  namespace : httpbin-ns
spec:
  hosts:
  - example.com
  location: MESH_INTERNAL
  ports:
  - number: 80
    name: http
    protocol: HTTP
  resolution: STATIC
  endpoints:
  - address: *******
  - address: *******
  subjectAltNames:
  - &quot;spiffe://cluster.local/ns/httpbin-ns/sa/httpbin-service-account&quot;
</code></pre>
<p>{{</tab>}}
{{</tabset>}}</p>
<p>The following example demonstrates the use of <code>ServiceEntry</code> with a
<code>workloadSelector</code> to handle the migration of a service
<code>details.bookinfo.com</code> from VMs to Kubernetes. The service has two
VM-based instances with sidecars as well as a set of Kubernetes
pods managed by a standard deployment object. Consumers of this
service in the mesh will be automatically load balanced across the
VMs and Kubernetes.  VM for the <code>details.bookinfo.com</code>
service. This VM has sidecar installed and bootstrapped using the
<code>details-legacy</code> service account. The sidecar receives HTTP traffic
on port 80 (wrapped in istio mutual TLS) and forwards it to the
application on the localhost on the same port.</p>
<p>{{<tabset category-name="example">}}
{{<tab name="v1alpha3" category-value="v1alpha3">}}</p>
<pre><code class="language-yaml">apiVersion: networking.istio.io/v1alpha3
kind: WorkloadEntry
metadata:
  name: details-vm-1
spec:
  serviceAccount: details
  address: *******
  labels:
    app: details
    instance-id: vm1
---
apiVersion: networking.istio.io/v1alpha3
kind: WorkloadEntry
metadata:
  name: details-vm-2
spec:
  serviceAccount: details
  address: *******
  labels:
    app: details
    instance-id: vm2
</code></pre>
<p>{{</tab>}}</p>
<p>{{<tab name="v1beta1" category-value="v1beta1">}}</p>
<pre><code class="language-yaml">apiVersion: networking.istio.io/v1beta1
kind: WorkloadEntry
metadata:
  name: details-vm-1
spec:
  serviceAccount: details
  address: *******
  labels:
    app: details
    instance-id: vm1
---
apiVersion: networking.istio.io/v1beta1
kind: WorkloadEntry
metadata:
  name: details-vm-2
spec:
  serviceAccount: details
  address: *******
  labels:
    app: details
    instance-id: vm2
</code></pre>
<p>{{</tab>}}
{{</tabset>}}</p>
<p>Assuming there is also a Kubernetes deployment with pod labels
<code>app: details</code> using the same service account <code>details</code>, the
following service entry declares a service spanning both VMs and
Kubernetes:</p>
<p>{{<tabset category-name="example">}}
{{<tab name="v1alpha3" category-value="v1alpha3">}}</p>
<pre><code class="language-yaml">apiVersion: networking.istio.io/v1alpha3
kind: ServiceEntry
metadata:
  name: details-svc
spec:
  hosts:
  - details.bookinfo.com
  location: MESH_INTERNAL
  ports:
  - number: 80
    name: http
    protocol: HTTP
  resolution: STATIC
  workloadSelector:
    labels:
      app: details
</code></pre>
<p>{{</tab>}}</p>
<p>{{<tab name="v1beta1" category-value="v1beta1">}}</p>
<pre><code class="language-yaml">apiVersion: networking.istio.io/v1beta1
kind: ServiceEntry
metadata:
  name: details-svc
spec:
  hosts:
  - details.bookinfo.com
  location: MESH_INTERNAL
  ports:
  - number: 80
    name: http
    protocol: HTTP
  resolution: STATIC
  workloadSelector:
    labels:
      app: details
</code></pre>
<p>{{</tab>}}
{{</tabset>}}</p>

<h2 id="ServiceEntry">ServiceEntry</h2>
<section>
<p>ServiceEntry enables adding additional entries into Istio&rsquo;s internal
service registry.</p>

<table class="message-fields">
<thead>
<tr>
<th>Field</th>
<th>Type</th>
<th>Description</th>
<th>Required</th>
</tr>
</thead>
<tbody>
<tr id="ServiceEntry-hosts">
<td><code>hosts</code></td>
<td><code>string[]</code></td>
<td>
<p>The hosts associated with the ServiceEntry. Could be a DNS
name with wildcard prefix.</p>
<ol>
<li>The hosts field is used to select matching hosts in VirtualServices and DestinationRules.</li>
<li>For HTTP traffic the HTTP Host/Authority header will be matched against the hosts field.</li>
<li>For HTTPs or TLS traffic containing Server Name Indication (SNI), the SNI value
will be matched against the hosts field.</li>
</ol>
<p><strong>NOTE 1:</strong> When resolution is set to type DNS and no endpoints
are specified, the host field will be used as the DNS name of the
endpoint to route traffic to.</p>
<p><strong>NOTE 2:</strong> If the hostname matches with the name of a service
from another service registry such as Kubernetes that also
supplies its own set of endpoints, the ServiceEntry will be
treated as a decorator of the existing Kubernetes
service. Properties in the service entry will be added to the
Kubernetes service if applicable. Currently, the only the
following additional properties will be considered by <code>istiod</code>:</p>
<ol>
<li>subjectAltNames: In addition to verifying the SANs of the
service accounts associated with the pods of the service, the
SANs specified here will also be verified.</li>
</ol>

</td>
<td>
Yes
</td>
</tr>
<tr id="ServiceEntry-addresses">
<td><code>addresses</code></td>
<td><code>string[]</code></td>
<td>
<p>The virtual IP addresses associated with the service. Could be CIDR
prefix. For HTTP traffic, generated route configurations will include http route
domains for both the <code>addresses</code> and <code>hosts</code> field values and the destination will
be identified based on the HTTP Host/Authority header.
If one or more IP addresses are specified,
the incoming traffic will be identified as belonging to this service
if the destination IP matches the IP/CIDRs specified in the addresses
field. If the Addresses field is empty, traffic will be identified
solely based on the destination port. In such scenarios, the port on
which the service is being accessed must not be shared by any other
service in the mesh. In other words, the sidecar will behave as a
simple TCP proxy, forwarding incoming traffic on a specified port to
the specified destination endpoint IP/host. Unix domain socket
addresses are not supported in this field.</p>

</td>
<td>
No
</td>
</tr>
<tr id="ServiceEntry-ports">
<td><code>ports</code></td>
<td><code><a href="https://istio.io/docs/reference/config/networking/gateway.html#Port">Port[]</a></code></td>
<td>
<p>The ports associated with the external service. If the
Endpoints are Unix domain socket addresses, there must be exactly one
port.</p>

</td>
<td>
Yes
</td>
</tr>
<tr id="ServiceEntry-location">
<td><code>location</code></td>
<td><code><a href="#ServiceEntry-Location">Location</a></code></td>
<td>
<p>Specify whether the service should be considered external to the mesh
or part of the mesh.</p>

</td>
<td>
No
</td>
</tr>
<tr id="ServiceEntry-resolution">
<td><code>resolution</code></td>
<td><code><a href="#ServiceEntry-Resolution">Resolution</a></code></td>
<td>
<p>Service discovery mode for the hosts. Care must be taken
when setting the resolution mode to NONE for a TCP port without
accompanying IP addresses. In such cases, traffic to any IP on
said port will be allowed (i.e. <code>0.0.0.0:&lt;port&gt;</code>).</p>

</td>
<td>
Yes
</td>
</tr>
<tr id="ServiceEntry-endpoints">
<td><code>endpoints</code></td>
<td><code><a href="https://istio.io/docs/reference/config/networking/workload-entry.html#WorkloadEntry">WorkloadEntry[]</a></code></td>
<td>
<p>One or more endpoints associated with the service. Only one of
<code>endpoints</code> or <code>workloadSelector</code> can be specified.</p>

</td>
<td>
No
</td>
</tr>
<tr id="ServiceEntry-workload_selector">
<td><code>workloadSelector</code></td>
<td><code><a href="https://istio.io/docs/reference/config/networking/sidecar.html#WorkloadSelector">WorkloadSelector</a></code></td>
<td>
<p>Applicable only for MESH_INTERNAL services. Only one of
<code>endpoints</code> or <code>workloadSelector</code> can be specified. Selects one
or more Kubernetes pods or VM workloads (specified using
<code>WorkloadEntry</code>) based on their labels. The <code>WorkloadEntry</code> object
representing the VMs should be defined in the same namespace as
the ServiceEntry.</p>

</td>
<td>
No
</td>
</tr>
<tr id="ServiceEntry-export_to">
<td><code>exportTo</code></td>
<td><code>string[]</code></td>
<td>
<p>A list of namespaces to which this service is exported. Exporting a service
allows it to be used by sidecars, gateways and virtual services defined in
other namespaces. This feature provides a mechanism for service owners
and mesh administrators to control the visibility of services across
namespace boundaries.</p>
<p>If no namespaces are specified then the service is exported to all
namespaces by default.</p>
<p>The value &ldquo;.&rdquo; is reserved and defines an export to the same namespace that
the service is declared in. Similarly the value &ldquo;*&rdquo; is reserved and
defines an export to all namespaces.</p>
<p>For a Kubernetes Service, the equivalent effect can be achieved by setting
the annotation &ldquo;networking.istio.io/exportTo&rdquo; to a comma-separated list
of namespace names.</p>

</td>
<td>
No
</td>
</tr>
<tr id="ServiceEntry-subject_alt_names">
<td><code>subjectAltNames</code></td>
<td><code>string[]</code></td>
<td>
<p>If specified, the proxy will verify that the server certificate&rsquo;s
subject alternate name matches one of the specified values.</p>
<p>NOTE: When using the workloadEntry with workloadSelectors, the
service account specified in the workloadEntry will also be used
to derive the additional subject alternate names that should be
verified.</p>

</td>
<td>
No
</td>
</tr>
</tbody>
</table>
</section>
<h2 id="ServiceEntry-Location">ServiceEntry.Location</h2>
<section>
<p>Location specifies whether the service is part of Istio mesh or
outside the mesh.  Location determines the behavior of several
features, such as service-to-service mTLS authentication, policy
enforcement, etc. When communicating with services outside the mesh,
Istio&rsquo;s mTLS authentication is disabled, and policy enforcement is
performed on the client-side as opposed to server-side.</p>

<table class="enum-values">
<thead>
<tr>
<th>Name</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr id="ServiceEntry-Location-MESH_EXTERNAL">
<td><code>MESH_EXTERNAL</code></td>
<td>
<p>Signifies that the service is external to the mesh. Typically used
to indicate external services consumed through APIs.</p>

</td>
</tr>
<tr id="ServiceEntry-Location-MESH_INTERNAL">
<td><code>MESH_INTERNAL</code></td>
<td>
<p>Signifies that the service is part of the mesh. Typically used to
indicate services added explicitly as part of expanding the service
mesh to include unmanaged infrastructure (e.g., VMs added to a
Kubernetes based service mesh).</p>

</td>
</tr>
</tbody>
</table>
</section>
<h2 id="ServiceEntry-Resolution">ServiceEntry.Resolution</h2>
<section>
<p>Resolution determines how the proxy will resolve the IP addresses of
the network endpoints associated with the service, so that it can
route to one of them. The resolution mode specified here has no impact
on how the application resolves the IP address associated with the
service. The application may still have to use DNS to resolve the
service to an IP so that the outbound traffic can be captured by the
Proxy. Alternatively, for HTTP services, the application could
directly communicate with the proxy (e.g., by setting HTTP_PROXY) to
talk to these services.</p>

<table class="enum-values">
<thead>
<tr>
<th>Name</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr id="ServiceEntry-Resolution-NONE">
<td><code>NONE</code></td>
<td>
<p>Assume that incoming connections have already been resolved (to a
specific destination IP address). Such connections are typically
routed via the proxy using mechanisms such as IP table REDIRECT/
eBPF. After performing any routing related transformations, the
proxy will forward the connection to the IP address to which the
connection was bound.</p>

</td>
</tr>
<tr id="ServiceEntry-Resolution-STATIC">
<td><code>STATIC</code></td>
<td>
<p>Use the static IP addresses specified in endpoints (see below) as the
backing instances associated with the service.</p>

</td>
</tr>
<tr id="ServiceEntry-Resolution-DNS">
<td><code>DNS</code></td>
<td>
<p>Attempt to resolve the IP address by querying the ambient DNS,
asynchronously. If no endpoints are specified, the proxy
will resolve the DNS address specified in the hosts field, if
wildcards are not used. If endpoints are specified, the DNS
addresses specified in the endpoints will be resolved to determine
the destination IP address.  DNS resolution cannot be used with Unix
domain socket endpoints.</p>

</td>
</tr>
<tr id="ServiceEntry-Resolution-DNS_ROUND_ROBIN">
<td><code>DNS_ROUND_ROBIN</code></td>
<td>
<p>Attempt to resolve the IP address by querying the ambient DNS,
asynchronously. Unlike <code>DNS</code>, <code>DNS_ROUND_ROBIN</code> only uses the
first IP address returned when a new connection needs to be initiated
without relying on complete results of DNS resolution, and connections
made to hosts will be retained even if DNS records change frequently
eliminating draining connection pools and connection cycling.
This is best suited for large web scale services that
must be accessed via DNS. The proxy will resolve the DNS address
specified in the hosts field, if wildcards are not used. DNS resolution
cannot be used with Unix domain socket endpoints.</p>

</td>
</tr>
</tbody>
</table>
</section>
