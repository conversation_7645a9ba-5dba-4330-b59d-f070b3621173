// Copyright 2020 Istio Authors
//
//   Licensed under the Apache License, Version 2.0 (the "License");
//   you may not use this file except in compliance with the License.
//   You may obtain a copy of the License at
//
//       http://www.apache.org/licenses/LICENSE-2.0
//
//   Unless required by applicable law or agreed to in writing, software
//   distributed under the License is distributed on an "AS IS" BASIS,
//   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//   See the License for the specific language governing permissions and
//   limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        (unknown)
// source: networking/v1beta1/destination_rule.proto

// $schema: istio.networking.v1beta1.DestinationRule
// $title: Destination Rule
// $description: Configuration affecting load balancing, outlier detection, etc.
// $location: https://istio.io/docs/reference/config/networking/destination-rule.html
// $aliases: [/docs/reference/config/networking/v1beta1/destination-rule]
// $mode: none

// `DestinationRule` defines policies that apply to traffic intended for a
// service after routing has occurred. These rules specify configuration
// for load balancing, connection pool size from the sidecar, and outlier
// detection settings to detect and evict unhealthy hosts from the load
// balancing pool. For example, a simple load balancing policy for the
// ratings service would look as follows:
//
// {{<tabset category-name="example">}}
// {{<tab name="v1alpha3" category-value="v1alpha3">}}
// ```yaml
// apiVersion: networking.istio.io/v1alpha3
// kind: DestinationRule
// metadata:
//   name: bookinfo-ratings
// spec:
//   host: ratings.prod.svc.cluster.local
//   trafficPolicy:
//     loadBalancer:
//       simple: LEAST_REQUEST
// ```
// {{</tab>}}
//
// {{<tab name="v1beta1" category-value="v1beta1">}}
// ```yaml
// apiVersion: networking.istio.io/v1beta1
// kind: DestinationRule
// metadata:
//   name: bookinfo-ratings
// spec:
//   host: ratings.prod.svc.cluster.local
//   trafficPolicy:
//     loadBalancer:
//       simple: LEAST_REQUEST
// ```
// {{</tab>}}
// {{</tabset>}}
//
// Version specific policies can be specified by defining a named
// `subset` and overriding the settings specified at the service level. The
// following rule uses a round robin load balancing policy for all traffic
// going to a subset named testversion that is composed of endpoints (e.g.,
// pods) with labels (version:v3).
//
// {{<tabset category-name="example">}}
// {{<tab name="v1alpha3" category-value="v1alpha3">}}
// ```yaml
// apiVersion: networking.istio.io/v1alpha3
// kind: DestinationRule
// metadata:
//   name: bookinfo-ratings
// spec:
//   host: ratings.prod.svc.cluster.local
//   trafficPolicy:
//     loadBalancer:
//       simple: LEAST_REQUEST
//   subsets:
//   - name: testversion
//     labels:
//       version: v3
//     trafficPolicy:
//       loadBalancer:
//         simple: ROUND_ROBIN
// ```
// {{</tab>}}
//
// {{<tab name="v1beta1" category-value="v1beta1">}}
// ```yaml
// apiVersion: networking.istio.io/v1beta1
// kind: DestinationRule
// metadata:
//   name: bookinfo-ratings
// spec:
//   host: ratings.prod.svc.cluster.local
//   trafficPolicy:
//     loadBalancer:
//       simple: LEAST_REQUEST
//   subsets:
//   - name: testversion
//     labels:
//       version: v3
//     trafficPolicy:
//       loadBalancer:
//         simple: ROUND_ROBIN
// ```
// {{</tab>}}
// {{</tabset>}}
//
// **Note:** Policies specified for subsets will not take effect until
// a route rule explicitly sends traffic to this subset.
//
// Traffic policies can be customized to specific ports as well. The
// following rule uses the least connection load balancing policy for all
// traffic to port 80, while uses a round robin load balancing setting for
// traffic to the port 9080.
//
// {{<tabset category-name="example">}}
// {{<tab name="v1alpha3" category-value="v1alpha3">}}
// ```yaml
// apiVersion: networking.istio.io/v1alpha3
// kind: DestinationRule
// metadata:
//   name: bookinfo-ratings-port
// spec:
//   host: ratings.prod.svc.cluster.local
//   trafficPolicy: # Apply to all ports
//     portLevelSettings:
//     - port:
//         number: 80
//       loadBalancer:
//         simple: LEAST_REQUEST
//     - port:
//         number: 9080
//       loadBalancer:
//         simple: ROUND_ROBIN
// ```
// {{</tab>}}
//
// {{<tab name="v1beta1" category-value="v1beta1">}}
// ```yaml
// apiVersion: networking.istio.io/v1beta1
// kind: DestinationRule
// metadata:
//   name: bookinfo-ratings-port
// spec:
//   host: ratings.prod.svc.cluster.local
//   trafficPolicy: # Apply to all ports
//     portLevelSettings:
//     - port:
//         number: 80
//       loadBalancer:
//         simple: LEAST_REQUEST
//     - port:
//         number: 9080
//       loadBalancer:
//         simple: ROUND_ROBIN
// ```
// {{</tab>}}
// {{</tabset>}}
//

package v1beta1

import (
	duration "github.com/golang/protobuf/ptypes/duration"
	wrappers "github.com/golang/protobuf/ptypes/wrappers"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	v1beta1 "istio.io/api/type/v1beta1"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Standard load balancing algorithms that require no tuning.
type LoadBalancerSettings_SimpleLB int32

const (
	// No load balancing algorithm has been specified by the user. Istio
	// will select an appropriate default.
	LoadBalancerSettings_UNSPECIFIED LoadBalancerSettings_SimpleLB = 0
	// Deprecated. Use LEAST_REQUEST instead.
	//
	// Deprecated: Do not use.
	LoadBalancerSettings_LEAST_CONN LoadBalancerSettings_SimpleLB = 1
	// The random load balancer selects a random healthy host. The random
	// load balancer generally performs better than round robin if no health
	// checking policy is configured.
	LoadBalancerSettings_RANDOM LoadBalancerSettings_SimpleLB = 2
	// This option will forward the connection to the original IP address
	// requested by the caller without doing any form of load
	// balancing. This option must be used with care. It is meant for
	// advanced use cases. Refer to Original Destination load balancer in
	// Envoy for further details.
	LoadBalancerSettings_PASSTHROUGH LoadBalancerSettings_SimpleLB = 3
	// A basic round robin load balancing policy. This is generally unsafe
	// for many scenarios (e.g. when enpoint weighting is used) as it can
	// overburden endpoints. In general, prefer to use LEAST_REQUEST as a
	// drop-in replacement for ROUND_ROBIN.
	LoadBalancerSettings_ROUND_ROBIN LoadBalancerSettings_SimpleLB = 4
	// The least request load balancer spreads load across endpoints, favoring
	// endpoints with the least outstanding requests. This is generally safer
	// and outperforms ROUND_ROBIN in nearly all cases. Prefer to use
	// LEAST_REQUEST as a drop-in replacement for ROUND_ROBIN.
	LoadBalancerSettings_LEAST_REQUEST LoadBalancerSettings_SimpleLB = 5
)

// Enum value maps for LoadBalancerSettings_SimpleLB.
var (
	LoadBalancerSettings_SimpleLB_name = map[int32]string{
		0: "UNSPECIFIED",
		1: "LEAST_CONN",
		2: "RANDOM",
		3: "PASSTHROUGH",
		4: "ROUND_ROBIN",
		5: "LEAST_REQUEST",
	}
	LoadBalancerSettings_SimpleLB_value = map[string]int32{
		"UNSPECIFIED":   0,
		"LEAST_CONN":    1,
		"RANDOM":        2,
		"PASSTHROUGH":   3,
		"ROUND_ROBIN":   4,
		"LEAST_REQUEST": 5,
	}
)

func (x LoadBalancerSettings_SimpleLB) Enum() *LoadBalancerSettings_SimpleLB {
	p := new(LoadBalancerSettings_SimpleLB)
	*p = x
	return p
}

func (x LoadBalancerSettings_SimpleLB) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoadBalancerSettings_SimpleLB) Descriptor() protoreflect.EnumDescriptor {
	return file_networking_v1beta1_destination_rule_proto_enumTypes[0].Descriptor()
}

func (LoadBalancerSettings_SimpleLB) Type() protoreflect.EnumType {
	return &file_networking_v1beta1_destination_rule_proto_enumTypes[0]
}

func (x LoadBalancerSettings_SimpleLB) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoadBalancerSettings_SimpleLB.Descriptor instead.
func (LoadBalancerSettings_SimpleLB) EnumDescriptor() ([]byte, []int) {
	return file_networking_v1beta1_destination_rule_proto_rawDescGZIP(), []int{3, 0}
}

// Policy for upgrading http1.1 connections to http2.
type ConnectionPoolSettings_HTTPSettings_H2UpgradePolicy int32

const (
	// Use the global default.
	ConnectionPoolSettings_HTTPSettings_DEFAULT ConnectionPoolSettings_HTTPSettings_H2UpgradePolicy = 0
	// Do not upgrade the connection to http2.
	// This opt-out option overrides the default.
	ConnectionPoolSettings_HTTPSettings_DO_NOT_UPGRADE ConnectionPoolSettings_HTTPSettings_H2UpgradePolicy = 1
	// Upgrade the connection to http2.
	// This opt-in option overrides the default.
	ConnectionPoolSettings_HTTPSettings_UPGRADE ConnectionPoolSettings_HTTPSettings_H2UpgradePolicy = 2
)

// Enum value maps for ConnectionPoolSettings_HTTPSettings_H2UpgradePolicy.
var (
	ConnectionPoolSettings_HTTPSettings_H2UpgradePolicy_name = map[int32]string{
		0: "DEFAULT",
		1: "DO_NOT_UPGRADE",
		2: "UPGRADE",
	}
	ConnectionPoolSettings_HTTPSettings_H2UpgradePolicy_value = map[string]int32{
		"DEFAULT":        0,
		"DO_NOT_UPGRADE": 1,
		"UPGRADE":        2,
	}
)

func (x ConnectionPoolSettings_HTTPSettings_H2UpgradePolicy) Enum() *ConnectionPoolSettings_HTTPSettings_H2UpgradePolicy {
	p := new(ConnectionPoolSettings_HTTPSettings_H2UpgradePolicy)
	*p = x
	return p
}

func (x ConnectionPoolSettings_HTTPSettings_H2UpgradePolicy) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ConnectionPoolSettings_HTTPSettings_H2UpgradePolicy) Descriptor() protoreflect.EnumDescriptor {
	return file_networking_v1beta1_destination_rule_proto_enumTypes[1].Descriptor()
}

func (ConnectionPoolSettings_HTTPSettings_H2UpgradePolicy) Type() protoreflect.EnumType {
	return &file_networking_v1beta1_destination_rule_proto_enumTypes[1]
}

func (x ConnectionPoolSettings_HTTPSettings_H2UpgradePolicy) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ConnectionPoolSettings_HTTPSettings_H2UpgradePolicy.Descriptor instead.
func (ConnectionPoolSettings_HTTPSettings_H2UpgradePolicy) EnumDescriptor() ([]byte, []int) {
	return file_networking_v1beta1_destination_rule_proto_rawDescGZIP(), []int{4, 1, 0}
}

// TLS connection mode
type ClientTLSSettings_TLSmode int32

const (
	// Do not setup a TLS connection to the upstream endpoint.
	ClientTLSSettings_DISABLE ClientTLSSettings_TLSmode = 0
	// Originate a TLS connection to the upstream endpoint.
	ClientTLSSettings_SIMPLE ClientTLSSettings_TLSmode = 1
	// Secure connections to the upstream using mutual TLS by presenting
	// client certificates for authentication.
	ClientTLSSettings_MUTUAL ClientTLSSettings_TLSmode = 2
	// Secure connections to the upstream using mutual TLS by presenting
	// client certificates for authentication.
	// Compared to Mutual mode, this mode uses certificates generated
	// automatically by Istio for mTLS authentication. When this mode is
	// used, all other fields in `ClientTLSSettings` should be empty.
	ClientTLSSettings_ISTIO_MUTUAL ClientTLSSettings_TLSmode = 3
)

// Enum value maps for ClientTLSSettings_TLSmode.
var (
	ClientTLSSettings_TLSmode_name = map[int32]string{
		0: "DISABLE",
		1: "SIMPLE",
		2: "MUTUAL",
		3: "ISTIO_MUTUAL",
	}
	ClientTLSSettings_TLSmode_value = map[string]int32{
		"DISABLE":      0,
		"SIMPLE":       1,
		"MUTUAL":       2,
		"ISTIO_MUTUAL": 3,
	}
)

func (x ClientTLSSettings_TLSmode) Enum() *ClientTLSSettings_TLSmode {
	p := new(ClientTLSSettings_TLSmode)
	*p = x
	return p
}

func (x ClientTLSSettings_TLSmode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ClientTLSSettings_TLSmode) Descriptor() protoreflect.EnumDescriptor {
	return file_networking_v1beta1_destination_rule_proto_enumTypes[2].Descriptor()
}

func (ClientTLSSettings_TLSmode) Type() protoreflect.EnumType {
	return &file_networking_v1beta1_destination_rule_proto_enumTypes[2]
}

func (x ClientTLSSettings_TLSmode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ClientTLSSettings_TLSmode.Descriptor instead.
func (ClientTLSSettings_TLSmode) EnumDescriptor() ([]byte, []int) {
	return file_networking_v1beta1_destination_rule_proto_rawDescGZIP(), []int{6, 0}
}

// DestinationRule defines policies that apply to traffic intended for a service
// after routing has occurred.
//
// <!-- crd generation tags
// +cue-gen:DestinationRule:groupName:networking.istio.io
// +cue-gen:DestinationRule:version:v1beta1
// +cue-gen:DestinationRule:annotations:helm.sh/resource-policy=keep
// +cue-gen:DestinationRule:labels:app=istio-pilot,chart=istio,heritage=Tiller,release=istio
// +cue-gen:DestinationRule:subresource:status
// +cue-gen:DestinationRule:scope:Namespaced
// +cue-gen:DestinationRule:resource:categories=istio-io,networking-istio-io,shortNames=dr
// +cue-gen:DestinationRule:printerColumn:name=Host,type=string,JSONPath=.spec.host,description="The name of a service from the service registry"
// +cue-gen:DestinationRule:printerColumn:name=Age,type=date,JSONPath=.metadata.creationTimestamp,description="CreationTimestamp is a timestamp
// representing the server time when this object was created. It is not guaranteed to be set in happens-before order across separate operations.
// Clients may not set this value. It is represented in RFC3339 form and is in UTC.
// Populated by the system. Read-only. Null for lists. More info: https://git.k8s.io/community/contributors/devel/api-conventions.md#metadata"
// +cue-gen:DestinationRule:preserveUnknownFields:false
// -->
//
// <!-- go code generation tags
// +kubetype-gen
// +kubetype-gen:groupVersion=networking.istio.io/v1beta1
// +genclient
// +k8s:deepcopy-gen=true
// -->
// <!-- istio code generation tags
// +istio.io/sync-from:networking/v1alpha3/destination_rule.proto
// -->
type DestinationRule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The name of a service from the service registry. Service
	// names are looked up from the platform's service registry (e.g.,
	// Kubernetes services, Consul services, etc.) and from the hosts
	// declared by [ServiceEntries](https://istio.io/docs/reference/config/networking/service-entry/#ServiceEntry). Rules defined for
	// services that do not exist in the service registry will be ignored.
	//
	// *Note for Kubernetes users*: When short names are used (e.g. "reviews"
	// instead of "reviews.default.svc.cluster.local"), Istio will interpret
	// the short name based on the namespace of the rule, not the service. A
	// rule in the "default" namespace containing a host "reviews" will be
	// interpreted as "reviews.default.svc.cluster.local", irrespective of
	// the actual namespace associated with the reviews service. _To avoid
	// potential misconfigurations, it is recommended to always use fully
	// qualified domain names over short names._
	//
	// Note that the host field applies to both HTTP and TCP services.
	Host string `protobuf:"bytes,1,opt,name=host,proto3" json:"host,omitempty"`
	// Traffic policies to apply (load balancing policy, connection pool
	// sizes, outlier detection).
	TrafficPolicy *TrafficPolicy `protobuf:"bytes,2,opt,name=traffic_policy,json=trafficPolicy,proto3" json:"traffic_policy,omitempty"`
	// One or more named sets that represent individual versions of a
	// service. Traffic policies can be overridden at subset level.
	Subsets []*Subset `protobuf:"bytes,3,rep,name=subsets,proto3" json:"subsets,omitempty"`
	// A list of namespaces to which this destination rule is exported.
	// The resolution of a destination rule to apply to a service occurs in the
	// context of a hierarchy of namespaces. Exporting a destination rule allows
	// it to be included in the resolution hierarchy for services in
	// other namespaces. This feature provides a mechanism for service owners
	// and mesh administrators to control the visibility of destination rules
	// across namespace boundaries.
	//
	// If no namespaces are specified then the destination rule is exported to all
	// namespaces by default.
	//
	// The value "." is reserved and defines an export to the same namespace that
	// the destination rule is declared in. Similarly, the value "*" is reserved and
	// defines an export to all namespaces.
	ExportTo []string `protobuf:"bytes,4,rep,name=export_to,json=exportTo,proto3" json:"export_to,omitempty"`
	// Criteria used to select the specific set of pods/VMs on which this
	// `DestinationRule` configuration should be applied. If specified, the `DestinationRule`
	// configuration will be applied only to the workload instances matching the workload selector
	// label in the same namespace. Workload selectors do not apply across namespace boundaries.
	// If omitted, the `DestinationRule` falls back to its default behavior.
	// For example, if specific sidecars need to have egress TLS settings for services outside
	// of the mesh, instead of every sidecar in the mesh needing to have the
	// configuration (which is the default behaviour), a workload selector can be specified.
	WorkloadSelector *v1beta1.WorkloadSelector `protobuf:"bytes,5,opt,name=workload_selector,json=workloadSelector,proto3" json:"workload_selector,omitempty"`
}

func (x *DestinationRule) Reset() {
	*x = DestinationRule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_networking_v1beta1_destination_rule_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DestinationRule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DestinationRule) ProtoMessage() {}

func (x *DestinationRule) ProtoReflect() protoreflect.Message {
	mi := &file_networking_v1beta1_destination_rule_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DestinationRule.ProtoReflect.Descriptor instead.
func (*DestinationRule) Descriptor() ([]byte, []int) {
	return file_networking_v1beta1_destination_rule_proto_rawDescGZIP(), []int{0}
}

func (x *DestinationRule) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *DestinationRule) GetTrafficPolicy() *TrafficPolicy {
	if x != nil {
		return x.TrafficPolicy
	}
	return nil
}

func (x *DestinationRule) GetSubsets() []*Subset {
	if x != nil {
		return x.Subsets
	}
	return nil
}

func (x *DestinationRule) GetExportTo() []string {
	if x != nil {
		return x.ExportTo
	}
	return nil
}

func (x *DestinationRule) GetWorkloadSelector() *v1beta1.WorkloadSelector {
	if x != nil {
		return x.WorkloadSelector
	}
	return nil
}

// Traffic policies to apply for a specific destination, across all
// destination ports. See DestinationRule for examples.
type TrafficPolicy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Settings controlling the load balancer algorithms.
	LoadBalancer *LoadBalancerSettings `protobuf:"bytes,1,opt,name=load_balancer,json=loadBalancer,proto3" json:"load_balancer,omitempty"`
	// Settings controlling the volume of connections to an upstream service
	ConnectionPool *ConnectionPoolSettings `protobuf:"bytes,2,opt,name=connection_pool,json=connectionPool,proto3" json:"connection_pool,omitempty"`
	// Settings controlling eviction of unhealthy hosts from the load balancing pool
	OutlierDetection *OutlierDetection `protobuf:"bytes,3,opt,name=outlier_detection,json=outlierDetection,proto3" json:"outlier_detection,omitempty"`
	// TLS related settings for connections to the upstream service.
	Tls *ClientTLSSettings `protobuf:"bytes,4,opt,name=tls,proto3" json:"tls,omitempty"`
	// Traffic policies specific to individual ports. Note that port level
	// settings will override the destination-level settings. Traffic
	// settings specified at the destination-level will not be inherited when
	// overridden by port-level settings, i.e. default values will be applied
	// to fields omitted in port-level traffic policies.
	PortLevelSettings []*TrafficPolicy_PortTrafficPolicy `protobuf:"bytes,5,rep,name=port_level_settings,json=portLevelSettings,proto3" json:"port_level_settings,omitempty"`
	// Configuration of tunneling TCP over other transport or application layers
	// for the host configured in the DestinationRule.
	// Tunnel settings can be applied to TCP or TLS routes and can't be applied to HTTP routes.
	Tunnel *TrafficPolicy_TunnelSettings `protobuf:"bytes,6,opt,name=tunnel,proto3" json:"tunnel,omitempty"`
}

func (x *TrafficPolicy) Reset() {
	*x = TrafficPolicy{}
	if protoimpl.UnsafeEnabled {
		mi := &file_networking_v1beta1_destination_rule_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrafficPolicy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrafficPolicy) ProtoMessage() {}

func (x *TrafficPolicy) ProtoReflect() protoreflect.Message {
	mi := &file_networking_v1beta1_destination_rule_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrafficPolicy.ProtoReflect.Descriptor instead.
func (*TrafficPolicy) Descriptor() ([]byte, []int) {
	return file_networking_v1beta1_destination_rule_proto_rawDescGZIP(), []int{1}
}

func (x *TrafficPolicy) GetLoadBalancer() *LoadBalancerSettings {
	if x != nil {
		return x.LoadBalancer
	}
	return nil
}

func (x *TrafficPolicy) GetConnectionPool() *ConnectionPoolSettings {
	if x != nil {
		return x.ConnectionPool
	}
	return nil
}

func (x *TrafficPolicy) GetOutlierDetection() *OutlierDetection {
	if x != nil {
		return x.OutlierDetection
	}
	return nil
}

func (x *TrafficPolicy) GetTls() *ClientTLSSettings {
	if x != nil {
		return x.Tls
	}
	return nil
}

func (x *TrafficPolicy) GetPortLevelSettings() []*TrafficPolicy_PortTrafficPolicy {
	if x != nil {
		return x.PortLevelSettings
	}
	return nil
}

func (x *TrafficPolicy) GetTunnel() *TrafficPolicy_TunnelSettings {
	if x != nil {
		return x.Tunnel
	}
	return nil
}

// A subset of endpoints of a service. Subsets can be used for scenarios
// like A/B testing, or routing to a specific version of a service. Refer
// to [VirtualService](https://istio.io/docs/reference/config/networking/virtual-service/#VirtualService) documentation for examples of using
// subsets in these scenarios. In addition, traffic policies defined at the
// service-level can be overridden at a subset-level. The following rule
// uses a round robin load balancing policy for all traffic going to a
// subset named testversion that is composed of endpoints (e.g., pods) with
// labels (version:v3).
//
// {{<tabset category-name="example">}}
// {{<tab name="v1alpha3" category-value="v1alpha3">}}
// ```yaml
// apiVersion: networking.istio.io/v1alpha3
// kind: DestinationRule
// metadata:
//
//	name: bookinfo-ratings
//
// spec:
//
//	host: ratings.prod.svc.cluster.local
//	trafficPolicy:
//	  loadBalancer:
//	    simple: LEAST_REQUEST
//	subsets:
//	- name: testversion
//	  labels:
//	    version: v3
//	  trafficPolicy:
//	    loadBalancer:
//	      simple: ROUND_ROBIN
//
// ```
// {{</tab>}}
//
// {{<tab name="v1beta1" category-value="v1beta1">}}
// ```yaml
// apiVersion: networking.istio.io/v1beta1
// kind: DestinationRule
// metadata:
//
//	name: bookinfo-ratings
//
// spec:
//
//	host: ratings.prod.svc.cluster.local
//	trafficPolicy:
//	  loadBalancer:
//	    simple: LEAST_REQUEST
//	subsets:
//	- name: testversion
//	  labels:
//	    version: v3
//	  trafficPolicy:
//	    loadBalancer:
//	      simple: ROUND_ROBIN
//
// ```
// {{</tab>}}
// {{</tabset>}}
//
// **Note:** Policies specified for subsets will not take effect until
// a route rule explicitly sends traffic to this subset.
//
// One or more labels are typically required to identify the subset destination,
// however, when the corresponding DestinationRule represents a host that
// supports multiple SNI hosts (e.g., an egress gateway), a subset without labels
// may be meaningful. In this case a traffic policy with [ClientTLSSettings](#ClientTLSSettings)
// can be used to identify a specific SNI host corresponding to the named subset.
type Subset struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Name of the subset. The service name and the subset name can
	// be used for traffic splitting in a route rule.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Labels apply a filter over the endpoints of a service in the
	// service registry. See route rules for examples of usage.
	Labels map[string]string `protobuf:"bytes,2,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// Traffic policies that apply to this subset. Subsets inherit the
	// traffic policies specified at the DestinationRule level. Settings
	// specified at the subset level will override the corresponding settings
	// specified at the DestinationRule level.
	TrafficPolicy *TrafficPolicy `protobuf:"bytes,3,opt,name=traffic_policy,json=trafficPolicy,proto3" json:"traffic_policy,omitempty"`
}

func (x *Subset) Reset() {
	*x = Subset{}
	if protoimpl.UnsafeEnabled {
		mi := &file_networking_v1beta1_destination_rule_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Subset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Subset) ProtoMessage() {}

func (x *Subset) ProtoReflect() protoreflect.Message {
	mi := &file_networking_v1beta1_destination_rule_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Subset.ProtoReflect.Descriptor instead.
func (*Subset) Descriptor() ([]byte, []int) {
	return file_networking_v1beta1_destination_rule_proto_rawDescGZIP(), []int{2}
}

func (x *Subset) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Subset) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *Subset) GetTrafficPolicy() *TrafficPolicy {
	if x != nil {
		return x.TrafficPolicy
	}
	return nil
}

// Load balancing policies to apply for a specific destination. See Envoy's
// load balancing
// [documentation](https://www.envoyproxy.io/docs/envoy/latest/intro/arch_overview/upstream/load_balancing/load_balancing)
// for more details.
//
// For example, the following rule uses a round robin load balancing policy
// for all traffic going to the ratings service.
//
// {{<tabset category-name="example">}}
// {{<tab name="v1alpha3" category-value="v1alpha3">}}
// ```yaml
// apiVersion: networking.istio.io/v1alpha3
// kind: DestinationRule
// metadata:
//
//	name: bookinfo-ratings
//
// spec:
//
//	host: ratings.prod.svc.cluster.local
//	trafficPolicy:
//	  loadBalancer:
//	    simple: ROUND_ROBIN
//
// ```
// {{</tab>}}
//
// {{<tab name="v1beta1" category-value="v1beta1">}}
// ```yaml
// apiVersion: networking.istio.io/v1beta1
// kind: DestinationRule
// metadata:
//
//	name: bookinfo-ratings
//
// spec:
//
//	host: ratings.prod.svc.cluster.local
//	trafficPolicy:
//	  loadBalancer:
//	    simple: ROUND_ROBIN
//
// ```
// {{</tab>}}
// {{</tabset>}}
//
// The following example sets up sticky sessions for the ratings service
// hashing-based load balancer for the same ratings service using the
// the User cookie as the hash key.
//
// {{<tabset category-name="example">}}
// {{<tab name="v1alpha3" category-value="v1alpha3">}}
// ```yaml
// apiVersion: networking.istio.io/v1alpha3
// kind: DestinationRule
// metadata:
//
//	name: bookinfo-ratings
//
// spec:
//
//	host: ratings.prod.svc.cluster.local
//	trafficPolicy:
//	  loadBalancer:
//	    consistentHash:
//	      httpCookie:
//	        name: user
//	        ttl: 0s
//
// ```
// {{</tab>}}
//
// {{<tab name="v1beta1" category-value="v1beta1">}}
// ```yaml
// apiVersion: networking.istio.io/v1beta1
// kind: DestinationRule
// metadata:
//
//	name: bookinfo-ratings
//
// spec:
//
//	host: ratings.prod.svc.cluster.local
//	trafficPolicy:
//	  loadBalancer:
//	    consistentHash:
//	      httpCookie:
//	        name: user
//	        ttl: 0s
//
// ```
// {{</tab>}}
// {{</tabset>}}
type LoadBalancerSettings struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Upstream load balancing policy.
	//
	// Types that are assignable to LbPolicy:
	//
	//	*LoadBalancerSettings_Simple
	//	*LoadBalancerSettings_ConsistentHash
	LbPolicy isLoadBalancerSettings_LbPolicy `protobuf_oneof:"lb_policy"`
	// Locality load balancer settings, this will override mesh wide settings in entirety, meaning no merging would be performed
	// between this object and the object one in MeshConfig
	LocalityLbSetting *LocalityLoadBalancerSetting `protobuf:"bytes,3,opt,name=locality_lb_setting,json=localityLbSetting,proto3" json:"locality_lb_setting,omitempty"`
	// Represents the warmup duration of Service. If set, the newly created endpoint of service
	// remains in warmup mode starting from its creation time for the duration of this window and
	// Istio progressively increases amount of traffic for that endpoint instead of sending proportional amount of traffic.
	// This should be enabled for services that require warm up time to serve full production load with reasonable latency.
	// Currently this is only supported for ROUND_ROBIN and LEAST_REQUEST load balancers.
	WarmupDurationSecs *duration.Duration `protobuf:"bytes,4,opt,name=warmup_duration_secs,json=warmupDurationSecs,proto3" json:"warmup_duration_secs,omitempty"`
}

func (x *LoadBalancerSettings) Reset() {
	*x = LoadBalancerSettings{}
	if protoimpl.UnsafeEnabled {
		mi := &file_networking_v1beta1_destination_rule_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoadBalancerSettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoadBalancerSettings) ProtoMessage() {}

func (x *LoadBalancerSettings) ProtoReflect() protoreflect.Message {
	mi := &file_networking_v1beta1_destination_rule_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoadBalancerSettings.ProtoReflect.Descriptor instead.
func (*LoadBalancerSettings) Descriptor() ([]byte, []int) {
	return file_networking_v1beta1_destination_rule_proto_rawDescGZIP(), []int{3}
}

func (m *LoadBalancerSettings) GetLbPolicy() isLoadBalancerSettings_LbPolicy {
	if m != nil {
		return m.LbPolicy
	}
	return nil
}

func (x *LoadBalancerSettings) GetSimple() LoadBalancerSettings_SimpleLB {
	if x, ok := x.GetLbPolicy().(*LoadBalancerSettings_Simple); ok {
		return x.Simple
	}
	return LoadBalancerSettings_UNSPECIFIED
}

func (x *LoadBalancerSettings) GetConsistentHash() *LoadBalancerSettings_ConsistentHashLB {
	if x, ok := x.GetLbPolicy().(*LoadBalancerSettings_ConsistentHash); ok {
		return x.ConsistentHash
	}
	return nil
}

func (x *LoadBalancerSettings) GetLocalityLbSetting() *LocalityLoadBalancerSetting {
	if x != nil {
		return x.LocalityLbSetting
	}
	return nil
}

func (x *LoadBalancerSettings) GetWarmupDurationSecs() *duration.Duration {
	if x != nil {
		return x.WarmupDurationSecs
	}
	return nil
}

type isLoadBalancerSettings_LbPolicy interface {
	isLoadBalancerSettings_LbPolicy()
}

type LoadBalancerSettings_Simple struct {
	Simple LoadBalancerSettings_SimpleLB `protobuf:"varint,1,opt,name=simple,proto3,enum=istio.networking.v1beta1.LoadBalancerSettings_SimpleLB,oneof"`
}

type LoadBalancerSettings_ConsistentHash struct {
	ConsistentHash *LoadBalancerSettings_ConsistentHashLB `protobuf:"bytes,2,opt,name=consistent_hash,json=consistentHash,proto3,oneof"`
}

func (*LoadBalancerSettings_Simple) isLoadBalancerSettings_LbPolicy() {}

func (*LoadBalancerSettings_ConsistentHash) isLoadBalancerSettings_LbPolicy() {}

// Connection pool settings for an upstream host. The settings apply to
// each individual host in the upstream service.  See Envoy's [circuit
// breaker](https://www.envoyproxy.io/docs/envoy/latest/intro/arch_overview/upstream/circuit_breaking)
// for more details. Connection pool settings can be applied at the TCP
// level as well as at HTTP level.
//
// For example, the following rule sets a limit of 100 connections to redis
// service called myredissrv with a connect timeout of 30ms
//
// {{<tabset category-name="example">}}
// {{<tab name="v1alpha3" category-value="v1alpha3">}}
// ```yaml
// apiVersion: networking.istio.io/v1alpha3
// kind: DestinationRule
// metadata:
//
//	name: bookinfo-redis
//
// spec:
//
//	host: myredissrv.prod.svc.cluster.local
//	trafficPolicy:
//	  connectionPool:
//	    tcp:
//	      maxConnections: 100
//	      connectTimeout: 30ms
//	      tcpKeepalive:
//	        time: 7200s
//	        interval: 75s
//
// ```
// {{</tab>}}
//
// {{<tab name="v1beta1" category-value="v1beta1">}}
// ```yaml
// apiVersion: networking.istio.io/v1beta1
// kind: DestinationRule
// metadata:
//
//	name: bookinfo-redis
//
// spec:
//
//	host: myredissrv.prod.svc.cluster.local
//	trafficPolicy:
//	  connectionPool:
//	    tcp:
//	      maxConnections: 100
//	      connectTimeout: 30ms
//	      tcpKeepalive:
//	        time: 7200s
//	        interval: 75s
//
// ```
// {{</tab>}}
// {{</tabset>}}
type ConnectionPoolSettings struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Settings common to both HTTP and TCP upstream connections.
	Tcp *ConnectionPoolSettings_TCPSettings `protobuf:"bytes,1,opt,name=tcp,proto3" json:"tcp,omitempty"`
	// HTTP connection pool settings.
	Http *ConnectionPoolSettings_HTTPSettings `protobuf:"bytes,2,opt,name=http,proto3" json:"http,omitempty"`
}

func (x *ConnectionPoolSettings) Reset() {
	*x = ConnectionPoolSettings{}
	if protoimpl.UnsafeEnabled {
		mi := &file_networking_v1beta1_destination_rule_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConnectionPoolSettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConnectionPoolSettings) ProtoMessage() {}

func (x *ConnectionPoolSettings) ProtoReflect() protoreflect.Message {
	mi := &file_networking_v1beta1_destination_rule_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConnectionPoolSettings.ProtoReflect.Descriptor instead.
func (*ConnectionPoolSettings) Descriptor() ([]byte, []int) {
	return file_networking_v1beta1_destination_rule_proto_rawDescGZIP(), []int{4}
}

func (x *ConnectionPoolSettings) GetTcp() *ConnectionPoolSettings_TCPSettings {
	if x != nil {
		return x.Tcp
	}
	return nil
}

func (x *ConnectionPoolSettings) GetHttp() *ConnectionPoolSettings_HTTPSettings {
	if x != nil {
		return x.Http
	}
	return nil
}

// A Circuit breaker implementation that tracks the status of each
// individual host in the upstream service.  Applicable to both HTTP and
// TCP services.  For HTTP services, hosts that continually return 5xx
// errors for API calls are ejected from the pool for a pre-defined period
// of time. For TCP services, connection timeouts or connection
// failures to a given host counts as an error when measuring the
// consecutive errors metric. See Envoy's [outlier
// detection](https://www.envoyproxy.io/docs/envoy/latest/intro/arch_overview/upstream/outlier)
// for more details.
//
// The following rule sets a connection pool size of 100 HTTP1 connections
// with no more than 10 req/connection to the "reviews" service. In addition,
// it sets a limit of 1000 concurrent HTTP2 requests and configures upstream
// hosts to be scanned every 5 mins so that any host that fails 7 consecutive
// times with a 502, 503, or 504 error code will be ejected for 15 minutes.
//
// {{<tabset category-name="example">}}
// {{<tab name="v1alpha3" category-value="v1alpha3">}}
// ```yaml
// apiVersion: networking.istio.io/v1alpha3
// kind: DestinationRule
// metadata:
//
//	name: reviews-cb-policy
//
// spec:
//
//	host: reviews.prod.svc.cluster.local
//	trafficPolicy:
//	  connectionPool:
//	    tcp:
//	      maxConnections: 100
//	    http:
//	      http2MaxRequests: 1000
//	      maxRequestsPerConnection: 10
//	  outlierDetection:
//	    consecutive5xxErrors: 7
//	    interval: 5m
//	    baseEjectionTime: 15m
//
// ```
// {{</tab>}}
//
// {{<tab name="v1beta1" category-value="v1beta1">}}
// ```yaml
// apiVersion: networking.istio.io/v1beta1
// kind: DestinationRule
// metadata:
//
//	name: reviews-cb-policy
//
// spec:
//
//	host: reviews.prod.svc.cluster.local
//	trafficPolicy:
//	  connectionPool:
//	    tcp:
//	      maxConnections: 100
//	    http:
//	      http2MaxRequests: 1000
//	      maxRequestsPerConnection: 10
//	  outlierDetection:
//	    consecutive5xxErrors: 7
//	    interval: 5m
//	    baseEjectionTime: 15m
//
// ```
// {{</tab>}}
// {{</tabset>}}
type OutlierDetection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Number of errors before a host is ejected from the connection
	// pool. Defaults to 5. When the upstream host is accessed over HTTP, a
	// 502, 503, or 504 return code qualifies as an error. When the upstream host
	// is accessed over an opaque TCP connection, connect timeouts and
	// connection error/failure events qualify as an error.
	// $hide_from_docs
	//
	// Deprecated: Do not use.
	ConsecutiveErrors int32 `protobuf:"varint,1,opt,name=consecutive_errors,json=consecutiveErrors,proto3" json:"consecutive_errors,omitempty"`
	// Determines whether to distinguish local origin failures from external errors. If set to true
	// consecutive_local_origin_failure is taken into account for outlier detection calculations.
	// This should be used when you want to derive the outlier detection status based on the errors
	// seen locally such as failure to connect, timeout while connecting etc. rather than the status code
	// retuned by upstream service. This is especially useful when the upstream service explicitly returns
	// a 5xx for some requests and you want to ignore those responses from upstream service while determining
	// the outlier detection status of a host.
	// Defaults to false.
	SplitExternalLocalOriginErrors bool `protobuf:"varint,8,opt,name=split_external_local_origin_errors,json=splitExternalLocalOriginErrors,proto3" json:"split_external_local_origin_errors,omitempty"`
	// The number of consecutive locally originated failures before ejection
	// occurs. Defaults to 5. Parameter takes effect only when split_external_local_origin_errors
	// is set to true.
	ConsecutiveLocalOriginFailures *wrappers.UInt32Value `protobuf:"bytes,9,opt,name=consecutive_local_origin_failures,json=consecutiveLocalOriginFailures,proto3" json:"consecutive_local_origin_failures,omitempty"`
	// Number of gateway errors before a host is ejected from the connection pool.
	// When the upstream host is accessed over HTTP, a 502, 503, or 504 return
	// code qualifies as a gateway error. When the upstream host is accessed over
	// an opaque TCP connection, connect timeouts and connection error/failure
	// events qualify as a gateway error.
	// This feature is disabled by default or when set to the value 0.
	//
	// Note that consecutive_gateway_errors and consecutive_5xx_errors can be
	// used separately or together. Because the errors counted by
	// consecutive_gateway_errors are also included in consecutive_5xx_errors,
	// if the value of consecutive_gateway_errors is greater than or equal to
	// the value of consecutive_5xx_errors, consecutive_gateway_errors will have
	// no effect.
	ConsecutiveGatewayErrors *wrappers.UInt32Value `protobuf:"bytes,6,opt,name=consecutive_gateway_errors,json=consecutiveGatewayErrors,proto3" json:"consecutive_gateway_errors,omitempty"`
	// Number of 5xx errors before a host is ejected from the connection pool.
	// When the upstream host is accessed over an opaque TCP connection, connect
	// timeouts, connection error/failure and request failure events qualify as a
	// 5xx error.
	// This feature defaults to 5 but can be disabled by setting the value to 0.
	//
	// Note that consecutive_gateway_errors and consecutive_5xx_errors can be
	// used separately or together. Because the errors counted by
	// consecutive_gateway_errors are also included in consecutive_5xx_errors,
	// if the value of consecutive_gateway_errors is greater than or equal to
	// the value of consecutive_5xx_errors, consecutive_gateway_errors will have
	// no effect.
	Consecutive_5XxErrors *wrappers.UInt32Value `protobuf:"bytes,7,opt,name=consecutive_5xx_errors,json=consecutive5xxErrors,proto3" json:"consecutive_5xx_errors,omitempty"`
	// Time interval between ejection sweep analysis. format:
	// 1h/1m/1s/1ms. MUST BE >=1ms. Default is 10s.
	Interval *duration.Duration `protobuf:"bytes,2,opt,name=interval,proto3" json:"interval,omitempty"`
	// Minimum ejection duration. A host will remain ejected for a period
	// equal to the product of minimum ejection duration and the number of
	// times the host has been ejected. This technique allows the system to
	// automatically increase the ejection period for unhealthy upstream
	// servers. format: 1h/1m/1s/1ms. MUST BE >=1ms. Default is 30s.
	BaseEjectionTime *duration.Duration `protobuf:"bytes,3,opt,name=base_ejection_time,json=baseEjectionTime,proto3" json:"base_ejection_time,omitempty"`
	// Maximum % of hosts in the load balancing pool for the upstream
	// service that can be ejected. Defaults to 10%.
	MaxEjectionPercent int32 `protobuf:"varint,4,opt,name=max_ejection_percent,json=maxEjectionPercent,proto3" json:"max_ejection_percent,omitempty"`
	// Outlier detection will be enabled as long as the associated load balancing
	// pool has at least min_health_percent hosts in healthy mode. When the
	// percentage of healthy hosts in the load balancing pool drops below this
	// threshold, outlier detection will be disabled and the proxy will load balance
	// across all hosts in the pool (healthy and unhealthy). The threshold can be
	// disabled by setting it to 0%. The default is 0% as it's not typically
	// applicable in k8s environments with few pods per service.
	MinHealthPercent int32 `protobuf:"varint,5,opt,name=min_health_percent,json=minHealthPercent,proto3" json:"min_health_percent,omitempty"`
}

func (x *OutlierDetection) Reset() {
	*x = OutlierDetection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_networking_v1beta1_destination_rule_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OutlierDetection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OutlierDetection) ProtoMessage() {}

func (x *OutlierDetection) ProtoReflect() protoreflect.Message {
	mi := &file_networking_v1beta1_destination_rule_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OutlierDetection.ProtoReflect.Descriptor instead.
func (*OutlierDetection) Descriptor() ([]byte, []int) {
	return file_networking_v1beta1_destination_rule_proto_rawDescGZIP(), []int{5}
}

// Deprecated: Do not use.
func (x *OutlierDetection) GetConsecutiveErrors() int32 {
	if x != nil {
		return x.ConsecutiveErrors
	}
	return 0
}

func (x *OutlierDetection) GetSplitExternalLocalOriginErrors() bool {
	if x != nil {
		return x.SplitExternalLocalOriginErrors
	}
	return false
}

func (x *OutlierDetection) GetConsecutiveLocalOriginFailures() *wrappers.UInt32Value {
	if x != nil {
		return x.ConsecutiveLocalOriginFailures
	}
	return nil
}

func (x *OutlierDetection) GetConsecutiveGatewayErrors() *wrappers.UInt32Value {
	if x != nil {
		return x.ConsecutiveGatewayErrors
	}
	return nil
}

func (x *OutlierDetection) GetConsecutive_5XxErrors() *wrappers.UInt32Value {
	if x != nil {
		return x.Consecutive_5XxErrors
	}
	return nil
}

func (x *OutlierDetection) GetInterval() *duration.Duration {
	if x != nil {
		return x.Interval
	}
	return nil
}

func (x *OutlierDetection) GetBaseEjectionTime() *duration.Duration {
	if x != nil {
		return x.BaseEjectionTime
	}
	return nil
}

func (x *OutlierDetection) GetMaxEjectionPercent() int32 {
	if x != nil {
		return x.MaxEjectionPercent
	}
	return 0
}

func (x *OutlierDetection) GetMinHealthPercent() int32 {
	if x != nil {
		return x.MinHealthPercent
	}
	return 0
}

// SSL/TLS related settings for upstream connections. See Envoy's [TLS
// context](https://www.envoyproxy.io/docs/envoy/latest/api-v3/extensions/transport_sockets/tls/v3/common.proto.html#common-tls-configuration)
// for more details. These settings are common to both HTTP and TCP upstreams.
//
// For example, the following rule configures a client to use mutual TLS
// for connections to upstream database cluster.
//
// {{<tabset category-name="example">}}
// {{<tab name="v1alpha3" category-value="v1alpha3">}}
// ```yaml
// apiVersion: networking.istio.io/v1alpha3
// kind: DestinationRule
// metadata:
//
//	name: db-mtls
//
// spec:
//
//	host: mydbserver.prod.svc.cluster.local
//	trafficPolicy:
//	  tls:
//	    mode: MUTUAL
//	    clientCertificate: /etc/certs/myclientcert.pem
//	    privateKey: /etc/certs/client_private_key.pem
//	    caCertificates: /etc/certs/rootcacerts.pem
//
// ```
// {{</tab>}}
//
// {{<tab name="v1beta1" category-value="v1beta1">}}
// ```yaml
// apiVersion: networking.istio.io/v1beta1
// kind: DestinationRule
// metadata:
//
//	name: db-mtls
//
// spec:
//
//	host: mydbserver.prod.svc.cluster.local
//	trafficPolicy:
//	  tls:
//	    mode: MUTUAL
//	    clientCertificate: /etc/certs/myclientcert.pem
//	    privateKey: /etc/certs/client_private_key.pem
//	    caCertificates: /etc/certs/rootcacerts.pem
//
// ```
// {{</tab>}}
// {{</tabset>}}
//
// The following rule configures a client to use TLS when talking to a
// foreign service whose domain matches *.foo.com.
//
// {{<tabset category-name="example">}}
// {{<tab name="v1alpha3" category-value="v1alpha3">}}
// ```yaml
// apiVersion: networking.istio.io/v1alpha3
// kind: DestinationRule
// metadata:
//
//	name: tls-foo
//
// spec:
//
//	host: "*.foo.com"
//	trafficPolicy:
//	  tls:
//	    mode: SIMPLE
//
// ```
// {{</tab>}}
//
// {{<tab name="v1beta1" category-value="v1beta1">}}
// ```yaml
// apiVersion: networking.istio.io/v1beta1
// kind: DestinationRule
// metadata:
//
//	name: tls-foo
//
// spec:
//
//	host: "*.foo.com"
//	trafficPolicy:
//	  tls:
//	    mode: SIMPLE
//
// ```
// {{</tab>}}
// {{</tabset>}}
//
// The following rule configures a client to use Istio mutual TLS when talking
// to rating services.
//
// {{<tabset category-name="example">}}
// {{<tab name="v1alpha3" category-value="v1alpha3">}}
// ```yaml
// apiVersion: networking.istio.io/v1alpha3
// kind: DestinationRule
// metadata:
//
//	name: ratings-istio-mtls
//
// spec:
//
//	host: ratings.prod.svc.cluster.local
//	trafficPolicy:
//	  tls:
//	    mode: ISTIO_MUTUAL
//
// ```
// {{</tab>}}
//
// {{<tab name="v1beta1" category-value="v1beta1">}}
// ```yaml
// apiVersion: networking.istio.io/v1beta1
// kind: DestinationRule
// metadata:
//
//	name: ratings-istio-mtls
//
// spec:
//
//	host: ratings.prod.svc.cluster.local
//	trafficPolicy:
//	  tls:
//	    mode: ISTIO_MUTUAL
//
// ```
// {{</tab>}}
// {{</tabset>}}
type ClientTLSSettings struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Indicates whether connections to this port should be secured
	// using TLS. The value of this field determines how TLS is enforced.
	Mode ClientTLSSettings_TLSmode `protobuf:"varint,1,opt,name=mode,proto3,enum=istio.networking.v1beta1.ClientTLSSettings_TLSmode" json:"mode,omitempty"`
	// REQUIRED if mode is `MUTUAL`. The path to the file holding the
	// client-side TLS certificate to use.
	// Should be empty if mode is `ISTIO_MUTUAL`.
	ClientCertificate string `protobuf:"bytes,2,opt,name=client_certificate,json=clientCertificate,proto3" json:"client_certificate,omitempty"`
	// REQUIRED if mode is `MUTUAL`. The path to the file holding the
	// client's private key.
	// Should be empty if mode is `ISTIO_MUTUAL`.
	PrivateKey string `protobuf:"bytes,3,opt,name=private_key,json=privateKey,proto3" json:"private_key,omitempty"`
	// OPTIONAL: The path to the file containing certificate authority
	// certificates to use in verifying a presented server certificate. If
	// omitted, the proxy will not verify the server's certificate.
	// Should be empty if mode is `ISTIO_MUTUAL`.
	CaCertificates string `protobuf:"bytes,4,opt,name=ca_certificates,json=caCertificates,proto3" json:"ca_certificates,omitempty"`
	// The name of the secret that holds the TLS certs for the
	// client including the CA certificates. Secret must exist in the
	// same namespace with the proxy using the certificates.
	// The secret (of type `generic`)should contain the
	// following keys and values: `key: <privateKey>`,
	// `cert: <clientCert>`, `cacert: <CACertificate>`.
	// Here CACertificate is used to verify the server certificate.
	// Secret of type tls for client certificates along with
	// ca.crt key for CA certificates is also supported.
	// Only one of client certificates and CA certificate
	// or credentialName can be specified.
	//
	// **NOTE:** This field is applicable at sidecars only if
	// `DestinationRule` has a `workloadSelector` specified.
	// Otherwise the field will be applicable only at gateways, and
	// sidecars will continue to use the certificate paths.
	CredentialName string `protobuf:"bytes,7,opt,name=credential_name,json=credentialName,proto3" json:"credential_name,omitempty"`
	// A list of alternate names to verify the subject identity in the
	// certificate. If specified, the proxy will verify that the server
	// certificate's subject alt name matches one of the specified values.
	// If specified, this list overrides the value of subject_alt_names
	// from the ServiceEntry. If unspecified, automatic validation of upstream
	// presented certificate for new upstream connections will be done based on the
	// downstream HTTP host/authority header, provided `VERIFY_CERT_AT_CLIENT`
	// and `ENABLE_AUTO_SNI` environmental variables are set to `true`.
	SubjectAltNames []string `protobuf:"bytes,5,rep,name=subject_alt_names,json=subjectAltNames,proto3" json:"subject_alt_names,omitempty"`
	// SNI string to present to the server during TLS handshake.
	// If unspecified, SNI will be automatically set based on downstream HTTP
	// host/authority header for SIMPLE and MUTUAL TLS modes, provided `ENABLE_AUTO_SNI`
	// environmental variable is set to `true`.
	Sni string `protobuf:"bytes,6,opt,name=sni,proto3" json:"sni,omitempty"`
	// InsecureSkipVerify specifies whether the proxy should skip verifying the
	// CA signature and SAN for the server certificate corresponding to the host.
	// This flag should only be set if global CA signature verifcation is
	// enabled, `VerifyCertAtClient` environmental variable is set to `true`,
	// but no verification is desired for a specific host. If enabled with or
	// without `VerifyCertAtClient` enabled, verification of the CA signature and
	// SAN will be skipped.
	//
	// `InsecureSkipVerify` is `false` by default.
	// `VerifyCertAtClient` is `false` by default in Istio version 1.9 but will
	// be `true` by default in a later version where, going forward, it will be
	// enabled by default.
	InsecureSkipVerify *wrappers.BoolValue `protobuf:"bytes,8,opt,name=insecure_skip_verify,json=insecureSkipVerify,proto3" json:"insecure_skip_verify,omitempty"`
}

func (x *ClientTLSSettings) Reset() {
	*x = ClientTLSSettings{}
	if protoimpl.UnsafeEnabled {
		mi := &file_networking_v1beta1_destination_rule_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClientTLSSettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClientTLSSettings) ProtoMessage() {}

func (x *ClientTLSSettings) ProtoReflect() protoreflect.Message {
	mi := &file_networking_v1beta1_destination_rule_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClientTLSSettings.ProtoReflect.Descriptor instead.
func (*ClientTLSSettings) Descriptor() ([]byte, []int) {
	return file_networking_v1beta1_destination_rule_proto_rawDescGZIP(), []int{6}
}

func (x *ClientTLSSettings) GetMode() ClientTLSSettings_TLSmode {
	if x != nil {
		return x.Mode
	}
	return ClientTLSSettings_DISABLE
}

func (x *ClientTLSSettings) GetClientCertificate() string {
	if x != nil {
		return x.ClientCertificate
	}
	return ""
}

func (x *ClientTLSSettings) GetPrivateKey() string {
	if x != nil {
		return x.PrivateKey
	}
	return ""
}

func (x *ClientTLSSettings) GetCaCertificates() string {
	if x != nil {
		return x.CaCertificates
	}
	return ""
}

func (x *ClientTLSSettings) GetCredentialName() string {
	if x != nil {
		return x.CredentialName
	}
	return ""
}

func (x *ClientTLSSettings) GetSubjectAltNames() []string {
	if x != nil {
		return x.SubjectAltNames
	}
	return nil
}

func (x *ClientTLSSettings) GetSni() string {
	if x != nil {
		return x.Sni
	}
	return ""
}

func (x *ClientTLSSettings) GetInsecureSkipVerify() *wrappers.BoolValue {
	if x != nil {
		return x.InsecureSkipVerify
	}
	return nil
}

// Locality-weighted load balancing allows administrators to control the
// distribution of traffic to endpoints based on the localities of where the
// traffic originates and where it will terminate. These localities are
// specified using arbitrary labels that designate a hierarchy of localities in
// {region}/{zone}/{sub-zone} form. For additional detail refer to
// [Locality Weight](https://www.envoyproxy.io/docs/envoy/latest/intro/arch_overview/upstream/load_balancing/locality_weight)
// The following example shows how to setup locality weights mesh-wide.
//
// Given a mesh with workloads and their service deployed to "us-west/zone1/*"
// and "us-west/zone2/*". This example specifies that when traffic accessing a
// service originates from workloads in "us-west/zone1/*", 80% of the traffic
// will be sent to endpoints in "us-west/zone1/*", i.e the same zone, and the
// remaining 20% will go to endpoints in "us-west/zone2/*". This setup is
// intended to favor routing traffic to endpoints in the same locality.
// A similar setting is specified for traffic originating in "us-west/zone2/*".
//
// ```yaml
//
//	distribute:
//	  - from: us-west/zone1/*
//	    to:
//	      "us-west/zone1/*": 80
//	      "us-west/zone2/*": 20
//	  - from: us-west/zone2/*
//	    to:
//	      "us-west/zone1/*": 20
//	      "us-west/zone2/*": 80
//
// ```
//
// If the goal of the operator is not to distribute load across zones and
// regions but rather to restrict the regionality of failover to meet other
// operational requirements an operator can set a 'failover' policy instead of
// a 'distribute' policy.
//
// The following example sets up a locality failover policy for regions.
// Assume a service resides in zones within us-east, us-west & eu-west
// this example specifies that when endpoints within us-east become unhealthy
// traffic should failover to endpoints in any zone or sub-zone within eu-west
// and similarly us-west should failover to us-east.
//
// ```yaml
//
//	failover:
//	  - from: us-east
//	    to: eu-west
//	  - from: us-west
//	    to: us-east
//
// ```
// Locality load balancing settings.
type LocalityLoadBalancerSetting struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Optional: only one of distribute, failover or failoverPriority can be set.
	// Explicitly specify loadbalancing weight across different zones and geographical locations.
	// Refer to [Locality weighted load balancing](https://www.envoyproxy.io/docs/envoy/latest/intro/arch_overview/upstream/load_balancing/locality_weight)
	// If empty, the locality weight is set according to the endpoints number within it.
	Distribute []*LocalityLoadBalancerSetting_Distribute `protobuf:"bytes,1,rep,name=distribute,proto3" json:"distribute,omitempty"`
	// Optional: only one of distribute, failover or failoverPriority can be set.
	// Explicitly specify the region traffic will land on when endpoints in local region becomes unhealthy.
	// Should be used together with OutlierDetection to detect unhealthy endpoints.
	// Note: if no OutlierDetection specified, this will not take effect.
	Failover []*LocalityLoadBalancerSetting_Failover `protobuf:"bytes,2,rep,name=failover,proto3" json:"failover,omitempty"`
	// failoverPriority is an ordered list of labels used to sort endpoints to do priority based load balancing.
	// This is to support traffic failover across different groups of endpoints.
	// Suppose there are total N labels specified:
	//
	// 1. Endpoints matching all N labels with the client proxy have priority P(0) i.e. the highest priority.
	// 2. Endpoints matching the first N-1 labels with the client proxy have priority P(1) i.e. second highest priority.
	// 3. By extension of this logic, endpoints matching only the first label with the client proxy has priority P(N-1) i.e. second lowest priority.
	// 4. All the other endpoints have priority P(N) i.e. lowest priority.
	//
	// Note: For a label to be considered for match, the previous labels must match, i.e. nth label would be considered matched only if first n-1 labels match.
	//
	// It can be any label specified on both client and server workloads.
	// The following labels which have special semantic meaning are also supported:
	//
	//   - `topology.istio.io/network` is used to match the network metadata of an endpoint, which can be specified by pod/namespace label `topology.istio.io/network`, sidecar env `ISTIO_META_NETWORK` or MeshNetworks.
	//   - `topology.istio.io/cluster` is used to match the clusterID of an endpoint, which can be specified by pod label `topology.istio.io/cluster` or pod env `ISTIO_META_CLUSTER_ID`.
	//   - `topology.kubernetes.io/region` is used to match the region metadata of an endpoint, which maps to Kubernetes node label `topology.kubernetes.io/region` or the deprecated label `failure-domain.beta.kubernetes.io/region`.
	//   - `topology.kubernetes.io/zone` is used to match the zone metadata of an endpoint, which maps to Kubernetes node label `topology.kubernetes.io/zone` or the deprecated label `failure-domain.beta.kubernetes.io/zone`.
	//   - `topology.istio.io/subzone` is used to match the subzone metadata of an endpoint, which maps to Istio node label `topology.istio.io/subzone`.
	//
	// The below topology config indicates the following priority levels:
	//
	// ```yaml
	// failoverPriority:
	// - "topology.istio.io/network"
	// - "topology.kubernetes.io/region"
	// - "topology.kubernetes.io/zone"
	// - "topology.istio.io/subzone"
	// ```
	//
	// 1. endpoints match same [network, region, zone, subzone] label with the client proxy have the highest priority.
	// 2. endpoints have same [network, region, zone] label but different [subzone] label with the client proxy have the second highest priority.
	// 3. endpoints have same [network, region] label but different [zone] label with the client proxy have the third highest priority.
	// 4. endpoints have same [network] but different [region] labels with the client proxy have the fourth highest priority.
	// 5. all the other endpoints have the same lowest priority.
	//
	// Optional: only one of distribute, failover or failoverPriority can be set.
	// And it should be used together with `OutlierDetection` to detect unhealthy endpoints, otherwise has no effect.
	FailoverPriority []string `protobuf:"bytes,4,rep,name=failover_priority,json=failoverPriority,proto3" json:"failover_priority,omitempty"`
	// enable locality load balancing, this is DestinationRule-level and will override mesh wide settings in entirety.
	// e.g. true means that turn on locality load balancing for this DestinationRule no matter what mesh wide settings is.
	Enabled *wrappers.BoolValue `protobuf:"bytes,3,opt,name=enabled,proto3" json:"enabled,omitempty"`
}

func (x *LocalityLoadBalancerSetting) Reset() {
	*x = LocalityLoadBalancerSetting{}
	if protoimpl.UnsafeEnabled {
		mi := &file_networking_v1beta1_destination_rule_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LocalityLoadBalancerSetting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LocalityLoadBalancerSetting) ProtoMessage() {}

func (x *LocalityLoadBalancerSetting) ProtoReflect() protoreflect.Message {
	mi := &file_networking_v1beta1_destination_rule_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LocalityLoadBalancerSetting.ProtoReflect.Descriptor instead.
func (*LocalityLoadBalancerSetting) Descriptor() ([]byte, []int) {
	return file_networking_v1beta1_destination_rule_proto_rawDescGZIP(), []int{7}
}

func (x *LocalityLoadBalancerSetting) GetDistribute() []*LocalityLoadBalancerSetting_Distribute {
	if x != nil {
		return x.Distribute
	}
	return nil
}

func (x *LocalityLoadBalancerSetting) GetFailover() []*LocalityLoadBalancerSetting_Failover {
	if x != nil {
		return x.Failover
	}
	return nil
}

func (x *LocalityLoadBalancerSetting) GetFailoverPriority() []string {
	if x != nil {
		return x.FailoverPriority
	}
	return nil
}

func (x *LocalityLoadBalancerSetting) GetEnabled() *wrappers.BoolValue {
	if x != nil {
		return x.Enabled
	}
	return nil
}

// Traffic policies that apply to specific ports of the service
type TrafficPolicy_PortTrafficPolicy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Specifies the number of a port on the destination service
	// on which this policy is being applied.
	Port *PortSelector `protobuf:"bytes,1,opt,name=port,proto3" json:"port,omitempty"`
	// Settings controlling the load balancer algorithms.
	LoadBalancer *LoadBalancerSettings `protobuf:"bytes,2,opt,name=load_balancer,json=loadBalancer,proto3" json:"load_balancer,omitempty"`
	// Settings controlling the volume of connections to an upstream service
	ConnectionPool *ConnectionPoolSettings `protobuf:"bytes,3,opt,name=connection_pool,json=connectionPool,proto3" json:"connection_pool,omitempty"`
	// Settings controlling eviction of unhealthy hosts from the load balancing pool
	OutlierDetection *OutlierDetection `protobuf:"bytes,4,opt,name=outlier_detection,json=outlierDetection,proto3" json:"outlier_detection,omitempty"`
	// TLS related settings for connections to the upstream service.
	Tls *ClientTLSSettings `protobuf:"bytes,5,opt,name=tls,proto3" json:"tls,omitempty"`
}

func (x *TrafficPolicy_PortTrafficPolicy) Reset() {
	*x = TrafficPolicy_PortTrafficPolicy{}
	if protoimpl.UnsafeEnabled {
		mi := &file_networking_v1beta1_destination_rule_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrafficPolicy_PortTrafficPolicy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrafficPolicy_PortTrafficPolicy) ProtoMessage() {}

func (x *TrafficPolicy_PortTrafficPolicy) ProtoReflect() protoreflect.Message {
	mi := &file_networking_v1beta1_destination_rule_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrafficPolicy_PortTrafficPolicy.ProtoReflect.Descriptor instead.
func (*TrafficPolicy_PortTrafficPolicy) Descriptor() ([]byte, []int) {
	return file_networking_v1beta1_destination_rule_proto_rawDescGZIP(), []int{1, 0}
}

func (x *TrafficPolicy_PortTrafficPolicy) GetPort() *PortSelector {
	if x != nil {
		return x.Port
	}
	return nil
}

func (x *TrafficPolicy_PortTrafficPolicy) GetLoadBalancer() *LoadBalancerSettings {
	if x != nil {
		return x.LoadBalancer
	}
	return nil
}

func (x *TrafficPolicy_PortTrafficPolicy) GetConnectionPool() *ConnectionPoolSettings {
	if x != nil {
		return x.ConnectionPool
	}
	return nil
}

func (x *TrafficPolicy_PortTrafficPolicy) GetOutlierDetection() *OutlierDetection {
	if x != nil {
		return x.OutlierDetection
	}
	return nil
}

func (x *TrafficPolicy_PortTrafficPolicy) GetTls() *ClientTLSSettings {
	if x != nil {
		return x.Tls
	}
	return nil
}

type TrafficPolicy_TunnelSettings struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Specifies which protocol to use for tunneling the downstream connection.
	// Supported protocols are:
	//
	//	CONNECT - uses HTTP CONNECT;
	//	POST - uses HTTP POST.
	//
	// CONNECT is used by default if not specified.
	// HTTP version for upstream requests is determined by the service protocol defined for the proxy.
	Protocol string `protobuf:"bytes,1,opt,name=protocol,proto3" json:"protocol,omitempty"`
	// Specifies a host to which the downstream connection is tunneled.
	// Target host must be an FQDN or IP address.
	TargetHost string `protobuf:"bytes,2,opt,name=target_host,json=targetHost,proto3" json:"target_host,omitempty"`
	// Specifies a port to which the downstream connection is tunneled.
	TargetPort uint32 `protobuf:"varint,3,opt,name=target_port,json=targetPort,proto3" json:"target_port,omitempty"`
}

func (x *TrafficPolicy_TunnelSettings) Reset() {
	*x = TrafficPolicy_TunnelSettings{}
	if protoimpl.UnsafeEnabled {
		mi := &file_networking_v1beta1_destination_rule_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrafficPolicy_TunnelSettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrafficPolicy_TunnelSettings) ProtoMessage() {}

func (x *TrafficPolicy_TunnelSettings) ProtoReflect() protoreflect.Message {
	mi := &file_networking_v1beta1_destination_rule_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrafficPolicy_TunnelSettings.ProtoReflect.Descriptor instead.
func (*TrafficPolicy_TunnelSettings) Descriptor() ([]byte, []int) {
	return file_networking_v1beta1_destination_rule_proto_rawDescGZIP(), []int{1, 1}
}

func (x *TrafficPolicy_TunnelSettings) GetProtocol() string {
	if x != nil {
		return x.Protocol
	}
	return ""
}

func (x *TrafficPolicy_TunnelSettings) GetTargetHost() string {
	if x != nil {
		return x.TargetHost
	}
	return ""
}

func (x *TrafficPolicy_TunnelSettings) GetTargetPort() uint32 {
	if x != nil {
		return x.TargetPort
	}
	return 0
}

// Consistent Hash-based load balancing can be used to provide soft
// session affinity based on HTTP headers, cookies or other
// properties. The affinity to a particular destination host may be
// lost when one or more hosts are added/removed from the destination
// service.
type LoadBalancerSettings_ConsistentHashLB struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The hash key to use.
	//
	// Types that are assignable to HashKey:
	//
	//	*LoadBalancerSettings_ConsistentHashLB_HttpHeaderName
	//	*LoadBalancerSettings_ConsistentHashLB_HttpCookie
	//	*LoadBalancerSettings_ConsistentHashLB_UseSourceIp
	//	*LoadBalancerSettings_ConsistentHashLB_HttpQueryParameterName
	HashKey isLoadBalancerSettings_ConsistentHashLB_HashKey `protobuf_oneof:"hash_key"`
	// The hash algorithm to use.
	// Please refer to https://www.envoyproxy.io/docs/envoy/latest/intro/arch_overview/upstream/load_balancing/load_balancers#ring-hash
	// and https://www.envoyproxy.io/docs/envoy/latest/intro/arch_overview/upstream/load_balancing/load_balancers#maglev for
	// considerations on choosing an algorithm.
	// Defaults to RingHash if not specified.
	//
	// Types that are assignable to HashAlgorithm:
	//
	//	*LoadBalancerSettings_ConsistentHashLB_RingHash_
	//	*LoadBalancerSettings_ConsistentHashLB_Maglev
	HashAlgorithm isLoadBalancerSettings_ConsistentHashLB_HashAlgorithm `protobuf_oneof:"hash_algorithm"`
	// Deprecated. Use RingHash instead.
	//
	// Deprecated: Do not use.
	MinimumRingSize uint64 `protobuf:"varint,4,opt,name=minimum_ring_size,json=minimumRingSize,proto3" json:"minimum_ring_size,omitempty"`
}

func (x *LoadBalancerSettings_ConsistentHashLB) Reset() {
	*x = LoadBalancerSettings_ConsistentHashLB{}
	if protoimpl.UnsafeEnabled {
		mi := &file_networking_v1beta1_destination_rule_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoadBalancerSettings_ConsistentHashLB) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoadBalancerSettings_ConsistentHashLB) ProtoMessage() {}

func (x *LoadBalancerSettings_ConsistentHashLB) ProtoReflect() protoreflect.Message {
	mi := &file_networking_v1beta1_destination_rule_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoadBalancerSettings_ConsistentHashLB.ProtoReflect.Descriptor instead.
func (*LoadBalancerSettings_ConsistentHashLB) Descriptor() ([]byte, []int) {
	return file_networking_v1beta1_destination_rule_proto_rawDescGZIP(), []int{3, 0}
}

func (m *LoadBalancerSettings_ConsistentHashLB) GetHashKey() isLoadBalancerSettings_ConsistentHashLB_HashKey {
	if m != nil {
		return m.HashKey
	}
	return nil
}

func (x *LoadBalancerSettings_ConsistentHashLB) GetHttpHeaderName() string {
	if x, ok := x.GetHashKey().(*LoadBalancerSettings_ConsistentHashLB_HttpHeaderName); ok {
		return x.HttpHeaderName
	}
	return ""
}

func (x *LoadBalancerSettings_ConsistentHashLB) GetHttpCookie() *LoadBalancerSettings_ConsistentHashLB_HTTPCookie {
	if x, ok := x.GetHashKey().(*LoadBalancerSettings_ConsistentHashLB_HttpCookie); ok {
		return x.HttpCookie
	}
	return nil
}

func (x *LoadBalancerSettings_ConsistentHashLB) GetUseSourceIp() bool {
	if x, ok := x.GetHashKey().(*LoadBalancerSettings_ConsistentHashLB_UseSourceIp); ok {
		return x.UseSourceIp
	}
	return false
}

func (x *LoadBalancerSettings_ConsistentHashLB) GetHttpQueryParameterName() string {
	if x, ok := x.GetHashKey().(*LoadBalancerSettings_ConsistentHashLB_HttpQueryParameterName); ok {
		return x.HttpQueryParameterName
	}
	return ""
}

func (m *LoadBalancerSettings_ConsistentHashLB) GetHashAlgorithm() isLoadBalancerSettings_ConsistentHashLB_HashAlgorithm {
	if m != nil {
		return m.HashAlgorithm
	}
	return nil
}

func (x *LoadBalancerSettings_ConsistentHashLB) GetRingHash() *LoadBalancerSettings_ConsistentHashLB_RingHash {
	if x, ok := x.GetHashAlgorithm().(*LoadBalancerSettings_ConsistentHashLB_RingHash_); ok {
		return x.RingHash
	}
	return nil
}

func (x *LoadBalancerSettings_ConsistentHashLB) GetMaglev() *LoadBalancerSettings_ConsistentHashLB_MagLev {
	if x, ok := x.GetHashAlgorithm().(*LoadBalancerSettings_ConsistentHashLB_Maglev); ok {
		return x.Maglev
	}
	return nil
}

// Deprecated: Do not use.
func (x *LoadBalancerSettings_ConsistentHashLB) GetMinimumRingSize() uint64 {
	if x != nil {
		return x.MinimumRingSize
	}
	return 0
}

type isLoadBalancerSettings_ConsistentHashLB_HashKey interface {
	isLoadBalancerSettings_ConsistentHashLB_HashKey()
}

type LoadBalancerSettings_ConsistentHashLB_HttpHeaderName struct {
	// Hash based on a specific HTTP header.
	HttpHeaderName string `protobuf:"bytes,1,opt,name=http_header_name,json=httpHeaderName,proto3,oneof"`
}

type LoadBalancerSettings_ConsistentHashLB_HttpCookie struct {
	// Hash based on HTTP cookie.
	HttpCookie *LoadBalancerSettings_ConsistentHashLB_HTTPCookie `protobuf:"bytes,2,opt,name=http_cookie,json=httpCookie,proto3,oneof"`
}

type LoadBalancerSettings_ConsistentHashLB_UseSourceIp struct {
	// Hash based on the source IP address.
	// This is applicable for both TCP and HTTP connections.
	UseSourceIp bool `protobuf:"varint,3,opt,name=use_source_ip,json=useSourceIp,proto3,oneof"`
}

type LoadBalancerSettings_ConsistentHashLB_HttpQueryParameterName struct {
	// Hash based on a specific HTTP query parameter.
	HttpQueryParameterName string `protobuf:"bytes,5,opt,name=http_query_parameter_name,json=httpQueryParameterName,proto3,oneof"`
}

func (*LoadBalancerSettings_ConsistentHashLB_HttpHeaderName) isLoadBalancerSettings_ConsistentHashLB_HashKey() {
}

func (*LoadBalancerSettings_ConsistentHashLB_HttpCookie) isLoadBalancerSettings_ConsistentHashLB_HashKey() {
}

func (*LoadBalancerSettings_ConsistentHashLB_UseSourceIp) isLoadBalancerSettings_ConsistentHashLB_HashKey() {
}

func (*LoadBalancerSettings_ConsistentHashLB_HttpQueryParameterName) isLoadBalancerSettings_ConsistentHashLB_HashKey() {
}

type isLoadBalancerSettings_ConsistentHashLB_HashAlgorithm interface {
	isLoadBalancerSettings_ConsistentHashLB_HashAlgorithm()
}

type LoadBalancerSettings_ConsistentHashLB_RingHash_ struct {
	// The ring/modulo hash load balancer implements consistent hashing to backend hosts.
	RingHash *LoadBalancerSettings_ConsistentHashLB_RingHash `protobuf:"bytes,6,opt,name=ring_hash,json=ringHash,proto3,oneof"`
}

type LoadBalancerSettings_ConsistentHashLB_Maglev struct {
	// The Maglev load balancer implements consistent hashing to backend hosts.
	Maglev *LoadBalancerSettings_ConsistentHashLB_MagLev `protobuf:"bytes,7,opt,name=maglev,proto3,oneof"`
}

func (*LoadBalancerSettings_ConsistentHashLB_RingHash_) isLoadBalancerSettings_ConsistentHashLB_HashAlgorithm() {
}

func (*LoadBalancerSettings_ConsistentHashLB_Maglev) isLoadBalancerSettings_ConsistentHashLB_HashAlgorithm() {
}

type LoadBalancerSettings_ConsistentHashLB_RingHash struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The minimum number of virtual nodes to use for the hash
	// ring. Defaults to 1024. Larger ring sizes result in more granular
	// load distributions. If the number of hosts in the load balancing
	// pool is larger than the ring size, each host will be assigned a
	// single virtual node.
	MinimumRingSize uint64 `protobuf:"varint,1,opt,name=minimum_ring_size,json=minimumRingSize,proto3" json:"minimum_ring_size,omitempty"`
}

func (x *LoadBalancerSettings_ConsistentHashLB_RingHash) Reset() {
	*x = LoadBalancerSettings_ConsistentHashLB_RingHash{}
	if protoimpl.UnsafeEnabled {
		mi := &file_networking_v1beta1_destination_rule_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoadBalancerSettings_ConsistentHashLB_RingHash) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoadBalancerSettings_ConsistentHashLB_RingHash) ProtoMessage() {}

func (x *LoadBalancerSettings_ConsistentHashLB_RingHash) ProtoReflect() protoreflect.Message {
	mi := &file_networking_v1beta1_destination_rule_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoadBalancerSettings_ConsistentHashLB_RingHash.ProtoReflect.Descriptor instead.
func (*LoadBalancerSettings_ConsistentHashLB_RingHash) Descriptor() ([]byte, []int) {
	return file_networking_v1beta1_destination_rule_proto_rawDescGZIP(), []int{3, 0, 0}
}

func (x *LoadBalancerSettings_ConsistentHashLB_RingHash) GetMinimumRingSize() uint64 {
	if x != nil {
		return x.MinimumRingSize
	}
	return 0
}

type LoadBalancerSettings_ConsistentHashLB_MagLev struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The table size for Maglev hashing. This helps in controlling the
	// disruption when the backend hosts change.
	// Increasing the table size reduces the amount of disruption.
	TableSize uint64 `protobuf:"varint,1,opt,name=table_size,json=tableSize,proto3" json:"table_size,omitempty"`
}

func (x *LoadBalancerSettings_ConsistentHashLB_MagLev) Reset() {
	*x = LoadBalancerSettings_ConsistentHashLB_MagLev{}
	if protoimpl.UnsafeEnabled {
		mi := &file_networking_v1beta1_destination_rule_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoadBalancerSettings_ConsistentHashLB_MagLev) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoadBalancerSettings_ConsistentHashLB_MagLev) ProtoMessage() {}

func (x *LoadBalancerSettings_ConsistentHashLB_MagLev) ProtoReflect() protoreflect.Message {
	mi := &file_networking_v1beta1_destination_rule_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoadBalancerSettings_ConsistentHashLB_MagLev.ProtoReflect.Descriptor instead.
func (*LoadBalancerSettings_ConsistentHashLB_MagLev) Descriptor() ([]byte, []int) {
	return file_networking_v1beta1_destination_rule_proto_rawDescGZIP(), []int{3, 0, 1}
}

func (x *LoadBalancerSettings_ConsistentHashLB_MagLev) GetTableSize() uint64 {
	if x != nil {
		return x.TableSize
	}
	return 0
}

// Describes a HTTP cookie that will be used as the hash key for the
// Consistent Hash load balancer. If the cookie is not present, it will
// be generated.
type LoadBalancerSettings_ConsistentHashLB_HTTPCookie struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Name of the cookie.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Path to set for the cookie.
	Path string `protobuf:"bytes,2,opt,name=path,proto3" json:"path,omitempty"`
	// Lifetime of the cookie.
	Ttl *duration.Duration `protobuf:"bytes,3,opt,name=ttl,proto3" json:"ttl,omitempty"`
}

func (x *LoadBalancerSettings_ConsistentHashLB_HTTPCookie) Reset() {
	*x = LoadBalancerSettings_ConsistentHashLB_HTTPCookie{}
	if protoimpl.UnsafeEnabled {
		mi := &file_networking_v1beta1_destination_rule_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoadBalancerSettings_ConsistentHashLB_HTTPCookie) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoadBalancerSettings_ConsistentHashLB_HTTPCookie) ProtoMessage() {}

func (x *LoadBalancerSettings_ConsistentHashLB_HTTPCookie) ProtoReflect() protoreflect.Message {
	mi := &file_networking_v1beta1_destination_rule_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoadBalancerSettings_ConsistentHashLB_HTTPCookie.ProtoReflect.Descriptor instead.
func (*LoadBalancerSettings_ConsistentHashLB_HTTPCookie) Descriptor() ([]byte, []int) {
	return file_networking_v1beta1_destination_rule_proto_rawDescGZIP(), []int{3, 0, 2}
}

func (x *LoadBalancerSettings_ConsistentHashLB_HTTPCookie) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *LoadBalancerSettings_ConsistentHashLB_HTTPCookie) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *LoadBalancerSettings_ConsistentHashLB_HTTPCookie) GetTtl() *duration.Duration {
	if x != nil {
		return x.Ttl
	}
	return nil
}

// Settings common to both HTTP and TCP upstream connections.
type ConnectionPoolSettings_TCPSettings struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Maximum number of HTTP1 /TCP connections to a destination host. Default 2^32-1.
	MaxConnections int32 `protobuf:"varint,1,opt,name=max_connections,json=maxConnections,proto3" json:"max_connections,omitempty"`
	// TCP connection timeout. format:
	// 1h/1m/1s/1ms. MUST BE >=1ms. Default is 10s.
	ConnectTimeout *duration.Duration `protobuf:"bytes,2,opt,name=connect_timeout,json=connectTimeout,proto3" json:"connect_timeout,omitempty"`
	// If set then set SO_KEEPALIVE on the socket to enable TCP Keepalives.
	TcpKeepalive *ConnectionPoolSettings_TCPSettings_TcpKeepalive `protobuf:"bytes,3,opt,name=tcp_keepalive,json=tcpKeepalive,proto3" json:"tcp_keepalive,omitempty"`
	// The maximum duration of a connection. The duration is defined as the period since a connection
	// was established. If not set, there is no max duration. When max_connection_duration
	// is reached the connection will be closed. Duration must be at least 1ms.
	MaxConnectionDuration *duration.Duration `protobuf:"bytes,4,opt,name=max_connection_duration,json=maxConnectionDuration,proto3" json:"max_connection_duration,omitempty"`
}

func (x *ConnectionPoolSettings_TCPSettings) Reset() {
	*x = ConnectionPoolSettings_TCPSettings{}
	if protoimpl.UnsafeEnabled {
		mi := &file_networking_v1beta1_destination_rule_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConnectionPoolSettings_TCPSettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConnectionPoolSettings_TCPSettings) ProtoMessage() {}

func (x *ConnectionPoolSettings_TCPSettings) ProtoReflect() protoreflect.Message {
	mi := &file_networking_v1beta1_destination_rule_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConnectionPoolSettings_TCPSettings.ProtoReflect.Descriptor instead.
func (*ConnectionPoolSettings_TCPSettings) Descriptor() ([]byte, []int) {
	return file_networking_v1beta1_destination_rule_proto_rawDescGZIP(), []int{4, 0}
}

func (x *ConnectionPoolSettings_TCPSettings) GetMaxConnections() int32 {
	if x != nil {
		return x.MaxConnections
	}
	return 0
}

func (x *ConnectionPoolSettings_TCPSettings) GetConnectTimeout() *duration.Duration {
	if x != nil {
		return x.ConnectTimeout
	}
	return nil
}

func (x *ConnectionPoolSettings_TCPSettings) GetTcpKeepalive() *ConnectionPoolSettings_TCPSettings_TcpKeepalive {
	if x != nil {
		return x.TcpKeepalive
	}
	return nil
}

func (x *ConnectionPoolSettings_TCPSettings) GetMaxConnectionDuration() *duration.Duration {
	if x != nil {
		return x.MaxConnectionDuration
	}
	return nil
}

// Settings applicable to HTTP1.1/HTTP2/GRPC connections.
type ConnectionPoolSettings_HTTPSettings struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Maximum number of requests that will be queued while waiting for
	// a ready connection pool connection. Default 1024.
	// Refer to https://www.envoyproxy.io/docs/envoy/latest/intro/arch_overview/upstream/circuit_breaking
	// under which conditions a new connection is created for HTTP2.
	// Please note that this is applicable to both HTTP/1.1 and HTTP2.
	Http1MaxPendingRequests int32 `protobuf:"varint,1,opt,name=http1_max_pending_requests,json=http1MaxPendingRequests,proto3" json:"http1_max_pending_requests,omitempty"`
	// Maximum number of active requests to a destination. Default 1024.
	// Please note that this is applicable to both HTTP/1.1 and HTTP2.
	Http2MaxRequests int32 `protobuf:"varint,2,opt,name=http2_max_requests,json=http2MaxRequests,proto3" json:"http2_max_requests,omitempty"`
	// Maximum number of requests per connection to a backend. Setting this
	// parameter to 1 disables keep alive. Default 0, meaning "unlimited",
	// up to 2^29.
	MaxRequestsPerConnection int32 `protobuf:"varint,3,opt,name=max_requests_per_connection,json=maxRequestsPerConnection,proto3" json:"max_requests_per_connection,omitempty"`
	// Maximum number of retries that can be outstanding to all hosts in a
	// cluster at a given time. Defaults to 2^32-1.
	MaxRetries int32 `protobuf:"varint,4,opt,name=max_retries,json=maxRetries,proto3" json:"max_retries,omitempty"`
	// The idle timeout for upstream connection pool connections. The idle timeout
	// is defined as the period in which there are no active requests.
	// If not set, the default is 1 hour. When the idle timeout is reached,
	// the connection will be closed. If the connection is an HTTP/2
	// connection a drain sequence will occur prior to closing the connection.
	// Note that request based timeouts mean that HTTP/2 PINGs will not
	// keep the connection alive. Applies to both HTTP1.1 and HTTP2 connections.
	IdleTimeout *duration.Duration `protobuf:"bytes,5,opt,name=idle_timeout,json=idleTimeout,proto3" json:"idle_timeout,omitempty"`
	// Specify if http1.1 connection should be upgraded to http2 for the associated destination.
	H2UpgradePolicy ConnectionPoolSettings_HTTPSettings_H2UpgradePolicy `protobuf:"varint,6,opt,name=h2_upgrade_policy,json=h2UpgradePolicy,proto3,enum=istio.networking.v1beta1.ConnectionPoolSettings_HTTPSettings_H2UpgradePolicy" json:"h2_upgrade_policy,omitempty"`
	// If set to true, client protocol will be preserved while initiating connection to backend.
	// Note that when this is set to true, h2_upgrade_policy will be ineffective i.e. the client
	// connections will not be upgraded to http2.
	UseClientProtocol bool `protobuf:"varint,7,opt,name=use_client_protocol,json=useClientProtocol,proto3" json:"use_client_protocol,omitempty"`
}

func (x *ConnectionPoolSettings_HTTPSettings) Reset() {
	*x = ConnectionPoolSettings_HTTPSettings{}
	if protoimpl.UnsafeEnabled {
		mi := &file_networking_v1beta1_destination_rule_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConnectionPoolSettings_HTTPSettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConnectionPoolSettings_HTTPSettings) ProtoMessage() {}

func (x *ConnectionPoolSettings_HTTPSettings) ProtoReflect() protoreflect.Message {
	mi := &file_networking_v1beta1_destination_rule_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConnectionPoolSettings_HTTPSettings.ProtoReflect.Descriptor instead.
func (*ConnectionPoolSettings_HTTPSettings) Descriptor() ([]byte, []int) {
	return file_networking_v1beta1_destination_rule_proto_rawDescGZIP(), []int{4, 1}
}

func (x *ConnectionPoolSettings_HTTPSettings) GetHttp1MaxPendingRequests() int32 {
	if x != nil {
		return x.Http1MaxPendingRequests
	}
	return 0
}

func (x *ConnectionPoolSettings_HTTPSettings) GetHttp2MaxRequests() int32 {
	if x != nil {
		return x.Http2MaxRequests
	}
	return 0
}

func (x *ConnectionPoolSettings_HTTPSettings) GetMaxRequestsPerConnection() int32 {
	if x != nil {
		return x.MaxRequestsPerConnection
	}
	return 0
}

func (x *ConnectionPoolSettings_HTTPSettings) GetMaxRetries() int32 {
	if x != nil {
		return x.MaxRetries
	}
	return 0
}

func (x *ConnectionPoolSettings_HTTPSettings) GetIdleTimeout() *duration.Duration {
	if x != nil {
		return x.IdleTimeout
	}
	return nil
}

func (x *ConnectionPoolSettings_HTTPSettings) GetH2UpgradePolicy() ConnectionPoolSettings_HTTPSettings_H2UpgradePolicy {
	if x != nil {
		return x.H2UpgradePolicy
	}
	return ConnectionPoolSettings_HTTPSettings_DEFAULT
}

func (x *ConnectionPoolSettings_HTTPSettings) GetUseClientProtocol() bool {
	if x != nil {
		return x.UseClientProtocol
	}
	return false
}

// TCP keepalive.
type ConnectionPoolSettings_TCPSettings_TcpKeepalive struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Maximum number of keepalive probes to send without response before
	// deciding the connection is dead. Default is to use the OS level configuration
	// (unless overridden, Linux defaults to 9.)
	Probes uint32 `protobuf:"varint,1,opt,name=probes,proto3" json:"probes,omitempty"`
	// The time duration a connection needs to be idle before keep-alive
	// probes start being sent. Default is to use the OS level configuration
	// (unless overridden, Linux defaults to 7200s (ie 2 hours.)
	Time *duration.Duration `protobuf:"bytes,2,opt,name=time,proto3" json:"time,omitempty"`
	// The time duration between keep-alive probes.
	// Default is to use the OS level configuration
	// (unless overridden, Linux defaults to 75s.)
	Interval *duration.Duration `protobuf:"bytes,3,opt,name=interval,proto3" json:"interval,omitempty"`
}

func (x *ConnectionPoolSettings_TCPSettings_TcpKeepalive) Reset() {
	*x = ConnectionPoolSettings_TCPSettings_TcpKeepalive{}
	if protoimpl.UnsafeEnabled {
		mi := &file_networking_v1beta1_destination_rule_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConnectionPoolSettings_TCPSettings_TcpKeepalive) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConnectionPoolSettings_TCPSettings_TcpKeepalive) ProtoMessage() {}

func (x *ConnectionPoolSettings_TCPSettings_TcpKeepalive) ProtoReflect() protoreflect.Message {
	mi := &file_networking_v1beta1_destination_rule_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConnectionPoolSettings_TCPSettings_TcpKeepalive.ProtoReflect.Descriptor instead.
func (*ConnectionPoolSettings_TCPSettings_TcpKeepalive) Descriptor() ([]byte, []int) {
	return file_networking_v1beta1_destination_rule_proto_rawDescGZIP(), []int{4, 0, 0}
}

func (x *ConnectionPoolSettings_TCPSettings_TcpKeepalive) GetProbes() uint32 {
	if x != nil {
		return x.Probes
	}
	return 0
}

func (x *ConnectionPoolSettings_TCPSettings_TcpKeepalive) GetTime() *duration.Duration {
	if x != nil {
		return x.Time
	}
	return nil
}

func (x *ConnectionPoolSettings_TCPSettings_TcpKeepalive) GetInterval() *duration.Duration {
	if x != nil {
		return x.Interval
	}
	return nil
}

// Describes how traffic originating in the 'from' zone or sub-zone is
// distributed over a set of 'to' zones. Syntax for specifying a zone is
// {region}/{zone}/{sub-zone} and terminal wildcards are allowed on any
// segment of the specification. Examples:
//
// `*` - matches all localities
//
// `us-west/*` - all zones and sub-zones within the us-west region
//
// `us-west/zone-1/*` - all sub-zones within us-west/zone-1
type LocalityLoadBalancerSetting_Distribute struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Originating locality, '/' separated, e.g. 'region/zone/sub_zone'.
	From string `protobuf:"bytes,1,opt,name=from,proto3" json:"from,omitempty"`
	// Map of upstream localities to traffic distribution weights. The sum of
	// all weights should be 100. Any locality not present will
	// receive no traffic.
	To map[string]uint32 `protobuf:"bytes,2,rep,name=to,proto3" json:"to,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
}

func (x *LocalityLoadBalancerSetting_Distribute) Reset() {
	*x = LocalityLoadBalancerSetting_Distribute{}
	if protoimpl.UnsafeEnabled {
		mi := &file_networking_v1beta1_destination_rule_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LocalityLoadBalancerSetting_Distribute) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LocalityLoadBalancerSetting_Distribute) ProtoMessage() {}

func (x *LocalityLoadBalancerSetting_Distribute) ProtoReflect() protoreflect.Message {
	mi := &file_networking_v1beta1_destination_rule_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LocalityLoadBalancerSetting_Distribute.ProtoReflect.Descriptor instead.
func (*LocalityLoadBalancerSetting_Distribute) Descriptor() ([]byte, []int) {
	return file_networking_v1beta1_destination_rule_proto_rawDescGZIP(), []int{7, 0}
}

func (x *LocalityLoadBalancerSetting_Distribute) GetFrom() string {
	if x != nil {
		return x.From
	}
	return ""
}

func (x *LocalityLoadBalancerSetting_Distribute) GetTo() map[string]uint32 {
	if x != nil {
		return x.To
	}
	return nil
}

// Specify the traffic failover policy across regions. Since zone and sub-zone
// failover is supported by default this only needs to be specified for
// regions when the operator needs to constrain traffic failover so that
// the default behavior of failing over to any endpoint globally does not
// apply. This is useful when failing over traffic across regions would not
// improve service health or may need to be restricted for other reasons
// like regulatory controls.
type LocalityLoadBalancerSetting_Failover struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Originating region.
	From string `protobuf:"bytes,1,opt,name=from,proto3" json:"from,omitempty"`
	// Destination region the traffic will fail over to when endpoints in
	// the 'from' region becomes unhealthy.
	To string `protobuf:"bytes,2,opt,name=to,proto3" json:"to,omitempty"`
}

func (x *LocalityLoadBalancerSetting_Failover) Reset() {
	*x = LocalityLoadBalancerSetting_Failover{}
	if protoimpl.UnsafeEnabled {
		mi := &file_networking_v1beta1_destination_rule_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LocalityLoadBalancerSetting_Failover) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LocalityLoadBalancerSetting_Failover) ProtoMessage() {}

func (x *LocalityLoadBalancerSetting_Failover) ProtoReflect() protoreflect.Message {
	mi := &file_networking_v1beta1_destination_rule_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LocalityLoadBalancerSetting_Failover.ProtoReflect.Descriptor instead.
func (*LocalityLoadBalancerSetting_Failover) Descriptor() ([]byte, []int) {
	return file_networking_v1beta1_destination_rule_proto_rawDescGZIP(), []int{7, 1}
}

func (x *LocalityLoadBalancerSetting_Failover) GetFrom() string {
	if x != nil {
		return x.From
	}
	return ""
}

func (x *LocalityLoadBalancerSetting_Failover) GetTo() string {
	if x != nil {
		return x.To
	}
	return ""
}

var File_networking_v1beta1_destination_rule_proto protoreflect.FileDescriptor

var file_networking_v1beta1_destination_rule_proto_rawDesc = []byte{
	0x0a, 0x29, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x62,
	0x65, 0x74, 0x61, 0x31, 0x2f, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x72, 0x75, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x18, 0x69, 0x73, 0x74,
	0x69, 0x6f, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x62, 0x65, 0x74, 0x61, 0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x62, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x69,
	0x6e, 0x67, 0x2f, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2f, 0x76, 0x69, 0x72, 0x74, 0x75,
	0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1b, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2f, 0x73,
	0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa7, 0x02,
	0x0a, 0x0f, 0x44, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x75, 0x6c,
	0x65, 0x12, 0x18, 0x0a, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x04, 0xe2, 0x41, 0x01, 0x02, 0x52, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x12, 0x4e, 0x0a, 0x0e, 0x74,
	0x72, 0x61, 0x66, 0x66, 0x69, 0x63, 0x5f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x69, 0x73, 0x74, 0x69, 0x6f, 0x2e, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x54,
	0x72, 0x61, 0x66, 0x66, 0x69, 0x63, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x0d, 0x74, 0x72,
	0x61, 0x66, 0x66, 0x69, 0x63, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x3a, 0x0a, 0x07, 0x73,
	0x75, 0x62, 0x73, 0x65, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x69,
	0x73, 0x74, 0x69, 0x6f, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x53, 0x75, 0x62, 0x73, 0x65, 0x74, 0x52, 0x07,
	0x73, 0x75, 0x62, 0x73, 0x65, 0x74, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x65, 0x78, 0x70, 0x6f, 0x72,
	0x74, 0x5f, 0x74, 0x6f, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x65, 0x78, 0x70, 0x6f,
	0x72, 0x74, 0x54, 0x6f, 0x12, 0x51, 0x0a, 0x11, 0x77, 0x6f, 0x72, 0x6b, 0x6c, 0x6f, 0x61, 0x64,
	0x5f, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x24, 0x2e, 0x69, 0x73, 0x74, 0x69, 0x6f, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x76, 0x31, 0x62,
	0x65, 0x74, 0x61, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x6c, 0x6f, 0x61, 0x64, 0x53, 0x65, 0x6c,
	0x65, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x10, 0x77, 0x6f, 0x72, 0x6b, 0x6c, 0x6f, 0x61, 0x64, 0x53,
	0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x22, 0xa8, 0x08, 0x0a, 0x0d, 0x54, 0x72, 0x61, 0x66,
	0x66, 0x69, 0x63, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x53, 0x0a, 0x0d, 0x6c, 0x6f, 0x61,
	0x64, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2e, 0x2e, 0x69, 0x73, 0x74, 0x69, 0x6f, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x4c, 0x6f, 0x61, 0x64,
	0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x72, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73,
	0x52, 0x0c, 0x6c, 0x6f, 0x61, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x72, 0x12, 0x59,
	0x0a, 0x0f, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x6f, 0x6f,
	0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x69, 0x73, 0x74, 0x69, 0x6f, 0x2e,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74,
	0x61, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6f, 0x6f,
	0x6c, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x0e, 0x63, 0x6f, 0x6e, 0x6e, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6f, 0x6f, 0x6c, 0x12, 0x57, 0x0a, 0x11, 0x6f, 0x75, 0x74,
	0x6c, 0x69, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x69, 0x73, 0x74, 0x69, 0x6f, 0x2e, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e,
	0x4f, 0x75, 0x74, 0x6c, 0x69, 0x65, 0x72, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x10, 0x6f, 0x75, 0x74, 0x6c, 0x69, 0x65, 0x72, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x3d, 0x0a, 0x03, 0x74, 0x6c, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2b, 0x2e, 0x69, 0x73, 0x74, 0x69, 0x6f, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x43, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x54, 0x4c, 0x53, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x03, 0x74, 0x6c,
	0x73, 0x12, 0x69, 0x0a, 0x13, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x5f,
	0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x39,
	0x2e, 0x69, 0x73, 0x74, 0x69, 0x6f, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x54, 0x72, 0x61, 0x66, 0x66, 0x69,
	0x63, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x2e, 0x50, 0x6f, 0x72, 0x74, 0x54, 0x72, 0x61, 0x66,
	0x66, 0x69, 0x63, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x11, 0x70, 0x6f, 0x72, 0x74, 0x4c,
	0x65, 0x76, 0x65, 0x6c, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x4e, 0x0a, 0x06,
	0x74, 0x75, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x69,
	0x73, 0x74, 0x69, 0x6f, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x54, 0x72, 0x61, 0x66, 0x66, 0x69, 0x63, 0x50,
	0x6f, 0x6c, 0x69, 0x63, 0x79, 0x2e, 0x54, 0x75, 0x6e, 0x6e, 0x65, 0x6c, 0x53, 0x65, 0x74, 0x74,
	0x69, 0x6e, 0x67, 0x73, 0x52, 0x06, 0x74, 0x75, 0x6e, 0x6e, 0x65, 0x6c, 0x1a, 0x97, 0x03, 0x0a,
	0x11, 0x50, 0x6f, 0x72, 0x74, 0x54, 0x72, 0x61, 0x66, 0x66, 0x69, 0x63, 0x50, 0x6f, 0x6c, 0x69,
	0x63, 0x79, 0x12, 0x3a, 0x0a, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x26, 0x2e, 0x69, 0x73, 0x74, 0x69, 0x6f, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x50, 0x6f, 0x72, 0x74,
	0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x53,
	0x0a, 0x0d, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x72, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x69, 0x73, 0x74, 0x69, 0x6f, 0x2e, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31,
	0x2e, 0x4c, 0x6f, 0x61, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x72, 0x53, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x0c, 0x6c, 0x6f, 0x61, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e,
	0x63, 0x65, 0x72, 0x12, 0x59, 0x0a, 0x0f, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x70, 0x6f, 0x6f, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x69,
	0x73, 0x74, 0x69, 0x6f, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x50, 0x6f, 0x6f, 0x6c, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x0e,
	0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6f, 0x6f, 0x6c, 0x12, 0x57,
	0x0a, 0x11, 0x6f, 0x75, 0x74, 0x6c, 0x69, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x69, 0x73, 0x74, 0x69,
	0x6f, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x62,
	0x65, 0x74, 0x61, 0x31, 0x2e, 0x4f, 0x75, 0x74, 0x6c, 0x69, 0x65, 0x72, 0x44, 0x65, 0x74, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x10, 0x6f, 0x75, 0x74, 0x6c, 0x69, 0x65, 0x72, 0x44, 0x65,
	0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3d, 0x0a, 0x03, 0x74, 0x6c, 0x73, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x69, 0x73, 0x74, 0x69, 0x6f, 0x2e, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e,
	0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x54, 0x4c, 0x53, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x73, 0x52, 0x03, 0x74, 0x6c, 0x73, 0x1a, 0x7a, 0x0a, 0x0e, 0x54, 0x75, 0x6e, 0x6e, 0x65, 0x6c,
	0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x25, 0x0a, 0x0b, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x68,
	0x6f, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x04, 0xe2, 0x41, 0x01, 0x02, 0x52,
	0x0a, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x48, 0x6f, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0b, 0x74,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d,
	0x42, 0x04, 0xe2, 0x41, 0x01, 0x02, 0x52, 0x0a, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x50, 0x6f,
	0x72, 0x74, 0x22, 0xf3, 0x01, 0x0a, 0x06, 0x53, 0x75, 0x62, 0x73, 0x65, 0x74, 0x12, 0x18, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x04, 0xe2, 0x41, 0x01,
	0x02, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x44, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x69, 0x73, 0x74, 0x69, 0x6f, 0x2e,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74,
	0x61, 0x31, 0x2e, 0x53, 0x75, 0x62, 0x73, 0x65, 0x74, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x4e, 0x0a,
	0x0e, 0x74, 0x72, 0x61, 0x66, 0x66, 0x69, 0x63, 0x5f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x69, 0x73, 0x74, 0x69, 0x6f, 0x2e, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31,
	0x2e, 0x54, 0x72, 0x61, 0x66, 0x66, 0x69, 0x63, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x0d,
	0x74, 0x72, 0x61, 0x66, 0x66, 0x69, 0x63, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x1a, 0x39, 0x0a,
	0x0b, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x84, 0x0a, 0x0a, 0x14, 0x4c, 0x6f, 0x61,
	0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x72, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x73, 0x12, 0x51, 0x0a, 0x06, 0x73, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x37, 0x2e, 0x69, 0x73, 0x74, 0x69, 0x6f, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x4c, 0x6f, 0x61,
	0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x72, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x73, 0x2e, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x4c, 0x42, 0x48, 0x00, 0x52, 0x06, 0x73, 0x69,
	0x6d, 0x70, 0x6c, 0x65, 0x12, 0x6a, 0x0a, 0x0f, 0x63, 0x6f, 0x6e, 0x73, 0x69, 0x73, 0x74, 0x65,
	0x6e, 0x74, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3f, 0x2e,
	0x69, 0x73, 0x74, 0x69, 0x6f, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x4c, 0x6f, 0x61, 0x64, 0x42, 0x61, 0x6c,
	0x61, 0x6e, 0x63, 0x65, 0x72, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x43, 0x6f,
	0x6e, 0x73, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x74, 0x48, 0x61, 0x73, 0x68, 0x4c, 0x42, 0x48, 0x00,
	0x52, 0x0e, 0x63, 0x6f, 0x6e, 0x73, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x74, 0x48, 0x61, 0x73, 0x68,
	0x12, 0x65, 0x0a, 0x13, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x6c, 0x62, 0x5f,
	0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e,
	0x69, 0x73, 0x74, 0x69, 0x6f, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x69, 0x74,
	0x79, 0x4c, 0x6f, 0x61, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x72, 0x53, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x52, 0x11, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x4c, 0x62,
	0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x4b, 0x0a, 0x14, 0x77, 0x61, 0x72, 0x6d, 0x75,
	0x70, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x63, 0x73, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x12, 0x77, 0x61, 0x72, 0x6d, 0x75, 0x70, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x65, 0x63, 0x73, 0x1a, 0xf9, 0x05, 0x0a, 0x10, 0x43, 0x6f, 0x6e, 0x73, 0x69, 0x73, 0x74,
	0x65, 0x6e, 0x74, 0x48, 0x61, 0x73, 0x68, 0x4c, 0x42, 0x12, 0x2a, 0x0a, 0x10, 0x68, 0x74, 0x74,
	0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0e, 0x68, 0x74, 0x74, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x6d, 0x0a, 0x0b, 0x68, 0x74, 0x74, 0x70, 0x5f, 0x63, 0x6f,
	0x6f, 0x6b, 0x69, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x4a, 0x2e, 0x69, 0x73, 0x74,
	0x69, 0x6f, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x4c, 0x6f, 0x61, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63,
	0x65, 0x72, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x69,
	0x73, 0x74, 0x65, 0x6e, 0x74, 0x48, 0x61, 0x73, 0x68, 0x4c, 0x42, 0x2e, 0x48, 0x54, 0x54, 0x50,
	0x43, 0x6f, 0x6f, 0x6b, 0x69, 0x65, 0x48, 0x00, 0x52, 0x0a, 0x68, 0x74, 0x74, 0x70, 0x43, 0x6f,
	0x6f, 0x6b, 0x69, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x75, 0x73, 0x65, 0x5f, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x5f, 0x69, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x0b, 0x75,
	0x73, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x70, 0x12, 0x3b, 0x0a, 0x19, 0x68, 0x74,
	0x74, 0x70, 0x5f, 0x71, 0x75, 0x65, 0x72, 0x79, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74,
	0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52,
	0x16, 0x68, 0x74, 0x74, 0x70, 0x51, 0x75, 0x65, 0x72, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65,
	0x74, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x67, 0x0a, 0x09, 0x72, 0x69, 0x6e, 0x67, 0x5f,
	0x68, 0x61, 0x73, 0x68, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x48, 0x2e, 0x69, 0x73, 0x74,
	0x69, 0x6f, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x4c, 0x6f, 0x61, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63,
	0x65, 0x72, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x69,
	0x73, 0x74, 0x65, 0x6e, 0x74, 0x48, 0x61, 0x73, 0x68, 0x4c, 0x42, 0x2e, 0x52, 0x69, 0x6e, 0x67,
	0x48, 0x61, 0x73, 0x68, 0x48, 0x01, 0x52, 0x08, 0x72, 0x69, 0x6e, 0x67, 0x48, 0x61, 0x73, 0x68,
	0x12, 0x60, 0x0a, 0x06, 0x6d, 0x61, 0x67, 0x6c, 0x65, 0x76, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x46, 0x2e, 0x69, 0x73, 0x74, 0x69, 0x6f, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x4c, 0x6f, 0x61, 0x64,
	0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x72, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73,
	0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x74, 0x48, 0x61, 0x73, 0x68, 0x4c,
	0x42, 0x2e, 0x4d, 0x61, 0x67, 0x4c, 0x65, 0x76, 0x48, 0x01, 0x52, 0x06, 0x6d, 0x61, 0x67, 0x6c,
	0x65, 0x76, 0x12, 0x2e, 0x0a, 0x11, 0x6d, 0x69, 0x6e, 0x69, 0x6d, 0x75, 0x6d, 0x5f, 0x72, 0x69,
	0x6e, 0x67, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x42, 0x02, 0x18,
	0x01, 0x52, 0x0f, 0x6d, 0x69, 0x6e, 0x69, 0x6d, 0x75, 0x6d, 0x52, 0x69, 0x6e, 0x67, 0x53, 0x69,
	0x7a, 0x65, 0x1a, 0x36, 0x0a, 0x08, 0x52, 0x69, 0x6e, 0x67, 0x48, 0x61, 0x73, 0x68, 0x12, 0x2a,
	0x0a, 0x11, 0x6d, 0x69, 0x6e, 0x69, 0x6d, 0x75, 0x6d, 0x5f, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x73,
	0x69, 0x7a, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0f, 0x6d, 0x69, 0x6e, 0x69, 0x6d,
	0x75, 0x6d, 0x52, 0x69, 0x6e, 0x67, 0x53, 0x69, 0x7a, 0x65, 0x1a, 0x27, 0x0a, 0x06, 0x4d, 0x61,
	0x67, 0x4c, 0x65, 0x76, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x73, 0x69,
	0x7a, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x53,
	0x69, 0x7a, 0x65, 0x1a, 0x6d, 0x0a, 0x0a, 0x48, 0x54, 0x54, 0x50, 0x43, 0x6f, 0x6f, 0x6b, 0x69,
	0x65, 0x12, 0x18, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x04, 0xe2, 0x41, 0x01, 0x02, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70,
	0x61, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12,
	0x31, 0x0a, 0x03, 0x74, 0x74, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x04, 0xe2, 0x41, 0x01, 0x02, 0x52, 0x03, 0x74,
	0x74, 0x6c, 0x42, 0x0a, 0x0a, 0x08, 0x68, 0x61, 0x73, 0x68, 0x5f, 0x6b, 0x65, 0x79, 0x42, 0x10,
	0x0a, 0x0e, 0x68, 0x61, 0x73, 0x68, 0x5f, 0x61, 0x6c, 0x67, 0x6f, 0x72, 0x69, 0x74, 0x68, 0x6d,
	0x22, 0x70, 0x0a, 0x08, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x4c, 0x42, 0x12, 0x0f, 0x0a, 0x0b,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x12, 0x0a,
	0x0a, 0x4c, 0x45, 0x41, 0x53, 0x54, 0x5f, 0x43, 0x4f, 0x4e, 0x4e, 0x10, 0x01, 0x1a, 0x02, 0x08,
	0x01, 0x12, 0x0a, 0x0a, 0x06, 0x52, 0x41, 0x4e, 0x44, 0x4f, 0x4d, 0x10, 0x02, 0x12, 0x0f, 0x0a,
	0x0b, 0x50, 0x41, 0x53, 0x53, 0x54, 0x48, 0x52, 0x4f, 0x55, 0x47, 0x48, 0x10, 0x03, 0x12, 0x0f,
	0x0a, 0x0b, 0x52, 0x4f, 0x55, 0x4e, 0x44, 0x5f, 0x52, 0x4f, 0x42, 0x49, 0x4e, 0x10, 0x04, 0x12,
	0x11, 0x0a, 0x0d, 0x4c, 0x45, 0x41, 0x53, 0x54, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54,
	0x10, 0x05, 0x42, 0x0b, 0x0a, 0x09, 0x6c, 0x62, 0x5f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x22,
	0x90, 0x09, 0x0a, 0x16, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6f,
	0x6f, 0x6c, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x4e, 0x0a, 0x03, 0x74, 0x63,
	0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x69, 0x73, 0x74, 0x69, 0x6f, 0x2e,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74,
	0x61, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6f, 0x6f,
	0x6c, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x54, 0x43, 0x50, 0x53, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x03, 0x74, 0x63, 0x70, 0x12, 0x51, 0x0a, 0x04, 0x68, 0x74,
	0x74, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x69, 0x73, 0x74, 0x69, 0x6f,
	0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x62, 0x65,
	0x74, 0x61, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6f,
	0x6f, 0x6c, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x48, 0x54, 0x54, 0x50, 0x53,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x04, 0x68, 0x74, 0x74, 0x70, 0x1a, 0xcc, 0x03,
	0x0a, 0x0b, 0x54, 0x43, 0x50, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x27, 0x0a,
	0x0f, 0x6d, 0x61, 0x78, 0x5f, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x6d, 0x61, 0x78, 0x43, 0x6f, 0x6e, 0x6e, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x42, 0x0a, 0x0f, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63,
	0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0e, 0x63, 0x6f, 0x6e, 0x6e,
	0x65, 0x63, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x12, 0x6e, 0x0a, 0x0d, 0x74, 0x63,
	0x70, 0x5f, 0x6b, 0x65, 0x65, 0x70, 0x61, 0x6c, 0x69, 0x76, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x49, 0x2e, 0x69, 0x73, 0x74, 0x69, 0x6f, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x43, 0x6f, 0x6e,
	0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6f, 0x6f, 0x6c, 0x53, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x73, 0x2e, 0x54, 0x43, 0x50, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x2e,
	0x54, 0x63, 0x70, 0x4b, 0x65, 0x65, 0x70, 0x61, 0x6c, 0x69, 0x76, 0x65, 0x52, 0x0c, 0x74, 0x63,
	0x70, 0x4b, 0x65, 0x65, 0x70, 0x61, 0x6c, 0x69, 0x76, 0x65, 0x12, 0x51, 0x0a, 0x17, 0x6d, 0x61,
	0x78, 0x5f, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x15, 0x6d, 0x61, 0x78, 0x43, 0x6f, 0x6e, 0x6e, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x8c, 0x01,
	0x0a, 0x0c, 0x54, 0x63, 0x70, 0x4b, 0x65, 0x65, 0x70, 0x61, 0x6c, 0x69, 0x76, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x70, 0x72, 0x6f, 0x62, 0x65, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06,
	0x70, 0x72, 0x6f, 0x62, 0x65, 0x73, 0x12, 0x2d, 0x0a, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x04, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x35, 0x0a, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61,
	0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x1a, 0x83, 0x04, 0x0a,
	0x0c, 0x48, 0x54, 0x54, 0x50, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x3b, 0x0a,
	0x1a, 0x68, 0x74, 0x74, 0x70, 0x31, 0x5f, 0x6d, 0x61, 0x78, 0x5f, 0x70, 0x65, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x17, 0x68, 0x74, 0x74, 0x70, 0x31, 0x4d, 0x61, 0x78, 0x50, 0x65, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x12, 0x2c, 0x0a, 0x12, 0x68, 0x74,
	0x74, 0x70, 0x32, 0x5f, 0x6d, 0x61, 0x78, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x68, 0x74, 0x74, 0x70, 0x32, 0x4d, 0x61, 0x78,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x12, 0x3d, 0x0a, 0x1b, 0x6d, 0x61, 0x78, 0x5f,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e,
	0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x18, 0x6d,
	0x61, 0x78, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x50, 0x65, 0x72, 0x43, 0x6f, 0x6e,
	0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x61, 0x78, 0x5f, 0x72,
	0x65, 0x74, 0x72, 0x69, 0x65, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x6d, 0x61,
	0x78, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x73, 0x12, 0x3c, 0x0a, 0x0c, 0x69, 0x64, 0x6c, 0x65,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x69, 0x64, 0x6c, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x12, 0x79, 0x0a, 0x11, 0x68, 0x32, 0x5f, 0x75, 0x70, 0x67,
	0x72, 0x61, 0x64, 0x65, 0x5f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x4d, 0x2e, 0x69, 0x73, 0x74, 0x69, 0x6f, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x43, 0x6f, 0x6e,
	0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6f, 0x6f, 0x6c, 0x53, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x73, 0x2e, 0x48, 0x54, 0x54, 0x50, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73,
	0x2e, 0x48, 0x32, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79,
	0x52, 0x0f, 0x68, 0x32, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x50, 0x6f, 0x6c, 0x69, 0x63,
	0x79, 0x12, 0x2e, 0x0a, 0x13, 0x75, 0x73, 0x65, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11,
	0x75, 0x73, 0x65, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f,
	0x6c, 0x22, 0x3f, 0x0a, 0x0f, 0x48, 0x32, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x50, 0x6f,
	0x6c, 0x69, 0x63, 0x79, 0x12, 0x0b, 0x0a, 0x07, 0x44, 0x45, 0x46, 0x41, 0x55, 0x4c, 0x54, 0x10,
	0x00, 0x12, 0x12, 0x0a, 0x0e, 0x44, 0x4f, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x55, 0x50, 0x47, 0x52,
	0x41, 0x44, 0x45, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x50, 0x47, 0x52, 0x41, 0x44, 0x45,
	0x10, 0x02, 0x22, 0x8a, 0x05, 0x0a, 0x10, 0x4f, 0x75, 0x74, 0x6c, 0x69, 0x65, 0x72, 0x44, 0x65,
	0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x31, 0x0a, 0x12, 0x63, 0x6f, 0x6e, 0x73, 0x65,
	0x63, 0x75, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x42, 0x02, 0x18, 0x01, 0x52, 0x11, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x63, 0x75,
	0x74, 0x69, 0x76, 0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x12, 0x4a, 0x0a, 0x22, 0x73, 0x70,
	0x6c, 0x69, 0x74, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x6c, 0x6f, 0x63,
	0x61, 0x6c, 0x5f, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x1e, 0x73, 0x70, 0x6c, 0x69, 0x74, 0x45, 0x78, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x6c, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x4f, 0x72, 0x69, 0x67, 0x69, 0x6e,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x12, 0x67, 0x0a, 0x21, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x63,
	0x75, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x5f, 0x6f, 0x72, 0x69, 0x67,
	0x69, 0x6e, 0x5f, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52,
	0x1e, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x63, 0x75, 0x74, 0x69, 0x76, 0x65, 0x4c, 0x6f, 0x63, 0x61,
	0x6c, 0x4f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x73, 0x12,
	0x5a, 0x0a, 0x1a, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x63, 0x75, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x18, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x63, 0x75, 0x74, 0x69, 0x76, 0x65, 0x47, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x12, 0x52, 0x0a, 0x16, 0x63,
	0x6f, 0x6e, 0x73, 0x65, 0x63, 0x75, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x35, 0x78, 0x78, 0x5f, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x55, 0x49,
	0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x14, 0x63, 0x6f, 0x6e, 0x73, 0x65,
	0x63, 0x75, 0x74, 0x69, 0x76, 0x65, 0x35, 0x78, 0x78, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x12,
	0x35, 0x0a, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x08, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x12, 0x47, 0x0a, 0x12, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x65,
	0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x10, 0x62,
	0x61, 0x73, 0x65, 0x45, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x30, 0x0a, 0x14, 0x6d, 0x61, 0x78, 0x5f, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x12, 0x6d,
	0x61, 0x78, 0x45, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e,
	0x74, 0x12, 0x2c, 0x0a, 0x12, 0x6d, 0x69, 0x6e, 0x5f, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x5f,
	0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x6d,
	0x69, 0x6e, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x22,
	0xd2, 0x03, 0x0a, 0x11, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x54, 0x4c, 0x53, 0x53, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x4d, 0x0a, 0x04, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x33, 0x2e, 0x69, 0x73, 0x74, 0x69, 0x6f, 0x2e, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x43,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x54, 0x4c, 0x53, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73,
	0x2e, 0x54, 0x4c, 0x53, 0x6d, 0x6f, 0x64, 0x65, 0x42, 0x04, 0xe2, 0x41, 0x01, 0x02, 0x52, 0x04,
	0x6d, 0x6f, 0x64, 0x65, 0x12, 0x2d, 0x0a, 0x12, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x63,
	0x65, 0x72, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x11, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x43, 0x65, 0x72, 0x74, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x5f, 0x6b,
	0x65, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74,
	0x65, 0x4b, 0x65, 0x79, 0x12, 0x27, 0x0a, 0x0f, 0x63, 0x61, 0x5f, 0x63, 0x65, 0x72, 0x74, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x65, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63,
	0x61, 0x43, 0x65, 0x72, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x65, 0x73, 0x12, 0x27, 0x0a,
	0x0f, 0x63, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63,
	0x74, 0x5f, 0x61, 0x6c, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x0f, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x41, 0x6c, 0x74, 0x4e, 0x61, 0x6d,
	0x65, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x6e, 0x69, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x73, 0x6e, 0x69, 0x12, 0x4c, 0x0a, 0x14, 0x69, 0x6e, 0x73, 0x65, 0x63, 0x75, 0x72, 0x65,
	0x5f, 0x73, 0x6b, 0x69, 0x70, 0x5f, 0x76, 0x65, 0x72, 0x69, 0x66, 0x79, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x12,
	0x69, 0x6e, 0x73, 0x65, 0x63, 0x75, 0x72, 0x65, 0x53, 0x6b, 0x69, 0x70, 0x56, 0x65, 0x72, 0x69,
	0x66, 0x79, 0x22, 0x40, 0x0a, 0x07, 0x54, 0x4c, 0x53, 0x6d, 0x6f, 0x64, 0x65, 0x12, 0x0b, 0x0a,
	0x07, 0x44, 0x49, 0x53, 0x41, 0x42, 0x4c, 0x45, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x53, 0x49,
	0x4d, 0x50, 0x4c, 0x45, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x4d, 0x55, 0x54, 0x55, 0x41, 0x4c,
	0x10, 0x02, 0x12, 0x10, 0x0a, 0x0c, 0x49, 0x53, 0x54, 0x49, 0x4f, 0x5f, 0x4d, 0x55, 0x54, 0x55,
	0x41, 0x4c, 0x10, 0x03, 0x22, 0xa2, 0x04, 0x0a, 0x1b, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x69, 0x74,
	0x79, 0x4c, 0x6f, 0x61, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x72, 0x53, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x12, 0x60, 0x0a, 0x0a, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75,
	0x74, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x40, 0x2e, 0x69, 0x73, 0x74, 0x69, 0x6f,
	0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x62, 0x65,
	0x74, 0x61, 0x31, 0x2e, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x4c, 0x6f, 0x61, 0x64,
	0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x72, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x2e,
	0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x52, 0x0a, 0x64, 0x69, 0x73, 0x74,
	0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x12, 0x5a, 0x0a, 0x08, 0x66, 0x61, 0x69, 0x6c, 0x6f, 0x76,
	0x65, 0x72, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3e, 0x2e, 0x69, 0x73, 0x74, 0x69, 0x6f,
	0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x62, 0x65,
	0x74, 0x61, 0x31, 0x2e, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x4c, 0x6f, 0x61, 0x64,
	0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x72, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x2e,
	0x46, 0x61, 0x69, 0x6c, 0x6f, 0x76, 0x65, 0x72, 0x52, 0x08, 0x66, 0x61, 0x69, 0x6c, 0x6f, 0x76,
	0x65, 0x72, 0x12, 0x2b, 0x0a, 0x11, 0x66, 0x61, 0x69, 0x6c, 0x6f, 0x76, 0x65, 0x72, 0x5f, 0x70,
	0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x10, 0x66,
	0x61, 0x69, 0x6c, 0x6f, 0x76, 0x65, 0x72, 0x50, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x12,
	0x34, 0x0a, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x07, 0x65, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x64, 0x1a, 0xb1, 0x01, 0x0a, 0x0a, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69,
	0x62, 0x75, 0x74, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x12, 0x58, 0x0a, 0x02, 0x74, 0x6f, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x48, 0x2e, 0x69, 0x73, 0x74, 0x69, 0x6f, 0x2e, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e,
	0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x4c, 0x6f, 0x61, 0x64, 0x42, 0x61, 0x6c, 0x61,
	0x6e, 0x63, 0x65, 0x72, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x44, 0x69, 0x73, 0x74,
	0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x2e, 0x54, 0x6f, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x02,
	0x74, 0x6f, 0x1a, 0x35, 0x0a, 0x07, 0x54, 0x6f, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x2e, 0x0a, 0x08, 0x46, 0x61, 0x69,
	0x6c, 0x6f, 0x76, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x74, 0x6f, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x74, 0x6f, 0x42, 0x21, 0x5a, 0x1f, 0x69, 0x73, 0x74,
	0x69, 0x6f, 0x2e, 0x69, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_networking_v1beta1_destination_rule_proto_rawDescOnce sync.Once
	file_networking_v1beta1_destination_rule_proto_rawDescData = file_networking_v1beta1_destination_rule_proto_rawDesc
)

func file_networking_v1beta1_destination_rule_proto_rawDescGZIP() []byte {
	file_networking_v1beta1_destination_rule_proto_rawDescOnce.Do(func() {
		file_networking_v1beta1_destination_rule_proto_rawDescData = protoimpl.X.CompressGZIP(file_networking_v1beta1_destination_rule_proto_rawDescData)
	})
	return file_networking_v1beta1_destination_rule_proto_rawDescData
}

var file_networking_v1beta1_destination_rule_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_networking_v1beta1_destination_rule_proto_msgTypes = make([]protoimpl.MessageInfo, 21)
var file_networking_v1beta1_destination_rule_proto_goTypes = []interface{}{
	(LoadBalancerSettings_SimpleLB)(0),                       // 0: istio.networking.v1beta1.LoadBalancerSettings.SimpleLB
	(ConnectionPoolSettings_HTTPSettings_H2UpgradePolicy)(0), // 1: istio.networking.v1beta1.ConnectionPoolSettings.HTTPSettings.H2UpgradePolicy
	(ClientTLSSettings_TLSmode)(0),                           // 2: istio.networking.v1beta1.ClientTLSSettings.TLSmode
	(*DestinationRule)(nil),                                  // 3: istio.networking.v1beta1.DestinationRule
	(*TrafficPolicy)(nil),                                    // 4: istio.networking.v1beta1.TrafficPolicy
	(*Subset)(nil),                                           // 5: istio.networking.v1beta1.Subset
	(*LoadBalancerSettings)(nil),                             // 6: istio.networking.v1beta1.LoadBalancerSettings
	(*ConnectionPoolSettings)(nil),                           // 7: istio.networking.v1beta1.ConnectionPoolSettings
	(*OutlierDetection)(nil),                                 // 8: istio.networking.v1beta1.OutlierDetection
	(*ClientTLSSettings)(nil),                                // 9: istio.networking.v1beta1.ClientTLSSettings
	(*LocalityLoadBalancerSetting)(nil),                      // 10: istio.networking.v1beta1.LocalityLoadBalancerSetting
	(*TrafficPolicy_PortTrafficPolicy)(nil),                  // 11: istio.networking.v1beta1.TrafficPolicy.PortTrafficPolicy
	(*TrafficPolicy_TunnelSettings)(nil),                     // 12: istio.networking.v1beta1.TrafficPolicy.TunnelSettings
	nil,                                                      // 13: istio.networking.v1beta1.Subset.LabelsEntry
	(*LoadBalancerSettings_ConsistentHashLB)(nil),            // 14: istio.networking.v1beta1.LoadBalancerSettings.ConsistentHashLB
	(*LoadBalancerSettings_ConsistentHashLB_RingHash)(nil),   // 15: istio.networking.v1beta1.LoadBalancerSettings.ConsistentHashLB.RingHash
	(*LoadBalancerSettings_ConsistentHashLB_MagLev)(nil),     // 16: istio.networking.v1beta1.LoadBalancerSettings.ConsistentHashLB.MagLev
	(*LoadBalancerSettings_ConsistentHashLB_HTTPCookie)(nil), // 17: istio.networking.v1beta1.LoadBalancerSettings.ConsistentHashLB.HTTPCookie
	(*ConnectionPoolSettings_TCPSettings)(nil),               // 18: istio.networking.v1beta1.ConnectionPoolSettings.TCPSettings
	(*ConnectionPoolSettings_HTTPSettings)(nil),              // 19: istio.networking.v1beta1.ConnectionPoolSettings.HTTPSettings
	(*ConnectionPoolSettings_TCPSettings_TcpKeepalive)(nil),  // 20: istio.networking.v1beta1.ConnectionPoolSettings.TCPSettings.TcpKeepalive
	(*LocalityLoadBalancerSetting_Distribute)(nil),           // 21: istio.networking.v1beta1.LocalityLoadBalancerSetting.Distribute
	(*LocalityLoadBalancerSetting_Failover)(nil),             // 22: istio.networking.v1beta1.LocalityLoadBalancerSetting.Failover
	nil,                              // 23: istio.networking.v1beta1.LocalityLoadBalancerSetting.Distribute.ToEntry
	(*v1beta1.WorkloadSelector)(nil), // 24: istio.type.v1beta1.WorkloadSelector
	(*duration.Duration)(nil),        // 25: google.protobuf.Duration
	(*wrappers.UInt32Value)(nil),     // 26: google.protobuf.UInt32Value
	(*wrappers.BoolValue)(nil),       // 27: google.protobuf.BoolValue
	(*PortSelector)(nil),             // 28: istio.networking.v1beta1.PortSelector
}
var file_networking_v1beta1_destination_rule_proto_depIdxs = []int32{
	4,  // 0: istio.networking.v1beta1.DestinationRule.traffic_policy:type_name -> istio.networking.v1beta1.TrafficPolicy
	5,  // 1: istio.networking.v1beta1.DestinationRule.subsets:type_name -> istio.networking.v1beta1.Subset
	24, // 2: istio.networking.v1beta1.DestinationRule.workload_selector:type_name -> istio.type.v1beta1.WorkloadSelector
	6,  // 3: istio.networking.v1beta1.TrafficPolicy.load_balancer:type_name -> istio.networking.v1beta1.LoadBalancerSettings
	7,  // 4: istio.networking.v1beta1.TrafficPolicy.connection_pool:type_name -> istio.networking.v1beta1.ConnectionPoolSettings
	8,  // 5: istio.networking.v1beta1.TrafficPolicy.outlier_detection:type_name -> istio.networking.v1beta1.OutlierDetection
	9,  // 6: istio.networking.v1beta1.TrafficPolicy.tls:type_name -> istio.networking.v1beta1.ClientTLSSettings
	11, // 7: istio.networking.v1beta1.TrafficPolicy.port_level_settings:type_name -> istio.networking.v1beta1.TrafficPolicy.PortTrafficPolicy
	12, // 8: istio.networking.v1beta1.TrafficPolicy.tunnel:type_name -> istio.networking.v1beta1.TrafficPolicy.TunnelSettings
	13, // 9: istio.networking.v1beta1.Subset.labels:type_name -> istio.networking.v1beta1.Subset.LabelsEntry
	4,  // 10: istio.networking.v1beta1.Subset.traffic_policy:type_name -> istio.networking.v1beta1.TrafficPolicy
	0,  // 11: istio.networking.v1beta1.LoadBalancerSettings.simple:type_name -> istio.networking.v1beta1.LoadBalancerSettings.SimpleLB
	14, // 12: istio.networking.v1beta1.LoadBalancerSettings.consistent_hash:type_name -> istio.networking.v1beta1.LoadBalancerSettings.ConsistentHashLB
	10, // 13: istio.networking.v1beta1.LoadBalancerSettings.locality_lb_setting:type_name -> istio.networking.v1beta1.LocalityLoadBalancerSetting
	25, // 14: istio.networking.v1beta1.LoadBalancerSettings.warmup_duration_secs:type_name -> google.protobuf.Duration
	18, // 15: istio.networking.v1beta1.ConnectionPoolSettings.tcp:type_name -> istio.networking.v1beta1.ConnectionPoolSettings.TCPSettings
	19, // 16: istio.networking.v1beta1.ConnectionPoolSettings.http:type_name -> istio.networking.v1beta1.ConnectionPoolSettings.HTTPSettings
	26, // 17: istio.networking.v1beta1.OutlierDetection.consecutive_local_origin_failures:type_name -> google.protobuf.UInt32Value
	26, // 18: istio.networking.v1beta1.OutlierDetection.consecutive_gateway_errors:type_name -> google.protobuf.UInt32Value
	26, // 19: istio.networking.v1beta1.OutlierDetection.consecutive_5xx_errors:type_name -> google.protobuf.UInt32Value
	25, // 20: istio.networking.v1beta1.OutlierDetection.interval:type_name -> google.protobuf.Duration
	25, // 21: istio.networking.v1beta1.OutlierDetection.base_ejection_time:type_name -> google.protobuf.Duration
	2,  // 22: istio.networking.v1beta1.ClientTLSSettings.mode:type_name -> istio.networking.v1beta1.ClientTLSSettings.TLSmode
	27, // 23: istio.networking.v1beta1.ClientTLSSettings.insecure_skip_verify:type_name -> google.protobuf.BoolValue
	21, // 24: istio.networking.v1beta1.LocalityLoadBalancerSetting.distribute:type_name -> istio.networking.v1beta1.LocalityLoadBalancerSetting.Distribute
	22, // 25: istio.networking.v1beta1.LocalityLoadBalancerSetting.failover:type_name -> istio.networking.v1beta1.LocalityLoadBalancerSetting.Failover
	27, // 26: istio.networking.v1beta1.LocalityLoadBalancerSetting.enabled:type_name -> google.protobuf.BoolValue
	28, // 27: istio.networking.v1beta1.TrafficPolicy.PortTrafficPolicy.port:type_name -> istio.networking.v1beta1.PortSelector
	6,  // 28: istio.networking.v1beta1.TrafficPolicy.PortTrafficPolicy.load_balancer:type_name -> istio.networking.v1beta1.LoadBalancerSettings
	7,  // 29: istio.networking.v1beta1.TrafficPolicy.PortTrafficPolicy.connection_pool:type_name -> istio.networking.v1beta1.ConnectionPoolSettings
	8,  // 30: istio.networking.v1beta1.TrafficPolicy.PortTrafficPolicy.outlier_detection:type_name -> istio.networking.v1beta1.OutlierDetection
	9,  // 31: istio.networking.v1beta1.TrafficPolicy.PortTrafficPolicy.tls:type_name -> istio.networking.v1beta1.ClientTLSSettings
	17, // 32: istio.networking.v1beta1.LoadBalancerSettings.ConsistentHashLB.http_cookie:type_name -> istio.networking.v1beta1.LoadBalancerSettings.ConsistentHashLB.HTTPCookie
	15, // 33: istio.networking.v1beta1.LoadBalancerSettings.ConsistentHashLB.ring_hash:type_name -> istio.networking.v1beta1.LoadBalancerSettings.ConsistentHashLB.RingHash
	16, // 34: istio.networking.v1beta1.LoadBalancerSettings.ConsistentHashLB.maglev:type_name -> istio.networking.v1beta1.LoadBalancerSettings.ConsistentHashLB.MagLev
	25, // 35: istio.networking.v1beta1.LoadBalancerSettings.ConsistentHashLB.HTTPCookie.ttl:type_name -> google.protobuf.Duration
	25, // 36: istio.networking.v1beta1.ConnectionPoolSettings.TCPSettings.connect_timeout:type_name -> google.protobuf.Duration
	20, // 37: istio.networking.v1beta1.ConnectionPoolSettings.TCPSettings.tcp_keepalive:type_name -> istio.networking.v1beta1.ConnectionPoolSettings.TCPSettings.TcpKeepalive
	25, // 38: istio.networking.v1beta1.ConnectionPoolSettings.TCPSettings.max_connection_duration:type_name -> google.protobuf.Duration
	25, // 39: istio.networking.v1beta1.ConnectionPoolSettings.HTTPSettings.idle_timeout:type_name -> google.protobuf.Duration
	1,  // 40: istio.networking.v1beta1.ConnectionPoolSettings.HTTPSettings.h2_upgrade_policy:type_name -> istio.networking.v1beta1.ConnectionPoolSettings.HTTPSettings.H2UpgradePolicy
	25, // 41: istio.networking.v1beta1.ConnectionPoolSettings.TCPSettings.TcpKeepalive.time:type_name -> google.protobuf.Duration
	25, // 42: istio.networking.v1beta1.ConnectionPoolSettings.TCPSettings.TcpKeepalive.interval:type_name -> google.protobuf.Duration
	23, // 43: istio.networking.v1beta1.LocalityLoadBalancerSetting.Distribute.to:type_name -> istio.networking.v1beta1.LocalityLoadBalancerSetting.Distribute.ToEntry
	44, // [44:44] is the sub-list for method output_type
	44, // [44:44] is the sub-list for method input_type
	44, // [44:44] is the sub-list for extension type_name
	44, // [44:44] is the sub-list for extension extendee
	0,  // [0:44] is the sub-list for field type_name
}

func init() { file_networking_v1beta1_destination_rule_proto_init() }
func file_networking_v1beta1_destination_rule_proto_init() {
	if File_networking_v1beta1_destination_rule_proto != nil {
		return
	}
	file_networking_v1beta1_virtual_service_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_networking_v1beta1_destination_rule_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DestinationRule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_networking_v1beta1_destination_rule_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TrafficPolicy); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_networking_v1beta1_destination_rule_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Subset); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_networking_v1beta1_destination_rule_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoadBalancerSettings); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_networking_v1beta1_destination_rule_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConnectionPoolSettings); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_networking_v1beta1_destination_rule_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OutlierDetection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_networking_v1beta1_destination_rule_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClientTLSSettings); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_networking_v1beta1_destination_rule_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LocalityLoadBalancerSetting); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_networking_v1beta1_destination_rule_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TrafficPolicy_PortTrafficPolicy); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_networking_v1beta1_destination_rule_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TrafficPolicy_TunnelSettings); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_networking_v1beta1_destination_rule_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoadBalancerSettings_ConsistentHashLB); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_networking_v1beta1_destination_rule_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoadBalancerSettings_ConsistentHashLB_RingHash); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_networking_v1beta1_destination_rule_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoadBalancerSettings_ConsistentHashLB_MagLev); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_networking_v1beta1_destination_rule_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoadBalancerSettings_ConsistentHashLB_HTTPCookie); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_networking_v1beta1_destination_rule_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConnectionPoolSettings_TCPSettings); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_networking_v1beta1_destination_rule_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConnectionPoolSettings_HTTPSettings); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_networking_v1beta1_destination_rule_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConnectionPoolSettings_TCPSettings_TcpKeepalive); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_networking_v1beta1_destination_rule_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LocalityLoadBalancerSetting_Distribute); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_networking_v1beta1_destination_rule_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LocalityLoadBalancerSetting_Failover); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_networking_v1beta1_destination_rule_proto_msgTypes[3].OneofWrappers = []interface{}{
		(*LoadBalancerSettings_Simple)(nil),
		(*LoadBalancerSettings_ConsistentHash)(nil),
	}
	file_networking_v1beta1_destination_rule_proto_msgTypes[11].OneofWrappers = []interface{}{
		(*LoadBalancerSettings_ConsistentHashLB_HttpHeaderName)(nil),
		(*LoadBalancerSettings_ConsistentHashLB_HttpCookie)(nil),
		(*LoadBalancerSettings_ConsistentHashLB_UseSourceIp)(nil),
		(*LoadBalancerSettings_ConsistentHashLB_HttpQueryParameterName)(nil),
		(*LoadBalancerSettings_ConsistentHashLB_RingHash_)(nil),
		(*LoadBalancerSettings_ConsistentHashLB_Maglev)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_networking_v1beta1_destination_rule_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   21,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_networking_v1beta1_destination_rule_proto_goTypes,
		DependencyIndexes: file_networking_v1beta1_destination_rule_proto_depIdxs,
		EnumInfos:         file_networking_v1beta1_destination_rule_proto_enumTypes,
		MessageInfos:      file_networking_v1beta1_destination_rule_proto_msgTypes,
	}.Build()
	File_networking_v1beta1_destination_rule_proto = out.File
	file_networking_v1beta1_destination_rule_proto_rawDesc = nil
	file_networking_v1beta1_destination_rule_proto_goTypes = nil
	file_networking_v1beta1_destination_rule_proto_depIdxs = nil
}
