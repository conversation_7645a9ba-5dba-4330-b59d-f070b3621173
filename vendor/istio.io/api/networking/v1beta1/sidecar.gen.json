{"openapi": "3.0.0", "info": {"title": "Configuration affecting network reachability of a sidecar.", "version": "v1beta1"}, "components": {"schemas": {"istio.networking.v1beta1.CaptureMode": {"description": "`CaptureMode` describes how traffic to a listener is expected to be captured. Applicable only when the listener is bound to an IP.", "type": "string", "enum": ["DEFAULT", "IPTABLES", "NONE"]}, "istio.networking.v1beta1.Destination": {"description": "Destination indicates the network addressable service to which the request/connection will be sent after processing a routing rule. The destination.host should unambiguously refer to a service in the service registry. Istio's service registry is composed of all the services found in the platform's service registry (e.g., Kubernetes services, Consul services), as well as services declared through the [ServiceEntry](https://istio.io/docs/reference/config/networking/service-entry/#ServiceEntry) resource.", "type": "object", "properties": {"host": {"description": "The name of a service from the service registry. Service names are looked up from the platform's service registry (e.g., Kubernetes services, Consul services, etc.) and from the hosts declared by [ServiceEntry](https://istio.io/docs/reference/config/networking/service-entry/#ServiceEntry). Traffic forwarded to destinations that are not found in either of the two, will be dropped.", "type": "string"}, "subset": {"description": "The name of a subset within the service. Applicable only to services within the mesh. The subset must be defined in a corresponding DestinationRule.", "type": "string"}, "port": {"$ref": "#/components/schemas/istio.networking.v1beta1.PortSelector"}}}, "istio.networking.v1beta1.IstioEgressListener": {"description": "`IstioEgressListener` specifies the properties of an outbound traffic listener on the sidecar proxy attached to a workload instance.", "type": "object", "properties": {"port": {"$ref": "#/components/schemas/istio.networking.v1beta1.Port"}, "bind": {"description": "The IP(IPv4 or IPv6) or the Unix domain socket to which the listener should be bound to. Port MUST be specified if bind is not empty. Format: IPv4 or IPv6 address formats or `unix:///path/to/uds` or `unix://@foobar` (Linux abstract namespace). If omitted, Istio will automatically configure the defaults based on imported services, the workload instances to which this configuration is applied to and the captureMode. If captureMode is `NONE`, bind will default to 127.0.0.1.", "type": "string"}, "captureMode": {"$ref": "#/components/schemas/istio.networking.v1beta1.CaptureMode"}, "hosts": {"description": "One or more service hosts exposed by the listener in `namespace/dnsName` format. Services in the specified namespace matching `dnsName` will be exposed. The corresponding service can be a service in the service registry (e.g., a Kubernetes or cloud foundry service) or a service specified using a `ServiceEntry` or `VirtualService` configuration. Any associated `DestinationRule` in the same namespace will also be used.", "type": "array", "items": {"type": "string"}}}}, "istio.networking.v1beta1.IstioIngressListener": {"description": "`IstioIngressListener` specifies the properties of an inbound traffic listener on the sidecar proxy attached to a workload instance.", "type": "object", "properties": {"port": {"$ref": "#/components/schemas/istio.networking.v1beta1.Port"}, "bind": {"description": "The IP(IPv4 or IPv6) to which the listener should be bound. Unix domain socket addresses are not allowed in the bind field for ingress listeners. If omitted, Is<PERSON>o will automatically configure the defaults based on imported services and the workload instances to which this configuration is applied to.", "type": "string"}, "captureMode": {"$ref": "#/components/schemas/istio.networking.v1beta1.CaptureMode"}, "defaultEndpoint": {"description": "The IP endpoint or Unix domain socket to which traffic should be forwarded to. This configuration can be used to redirect traffic arriving at the bind `IP:Port` on the sidecar to a `localhost:port` or Unix domain socket where the application workload instance is listening for connections. Arbitrary IPs are not supported. Format should be one of `127.0.0.1:PORT`, `[::1]:PORT` (forward to localhost), `0.0.0.0:PORT`, `[::]:PORT` (forward to the instance IP), or `unix:///path/to/socket` (forward to Unix domain socket).", "type": "string"}, "tls": {"$ref": "#/components/schemas/istio.networking.v1beta1.ServerTLSSettings"}}}, "istio.networking.v1beta1.OutboundTrafficPolicy": {"description": "`OutboundTrafficPolicy` sets the default behavior of the sidecar for handling outbound traffic from the application. If your application uses one or more external services that are not known apriori, setting the policy to `ALLOW_ANY` will cause the sidecars to route any unknown traffic originating from the application to its requested destination. Users are strongly encouraged to use `ServiceEntry` configurations to explicitly declare any external dependencies, instead of using `ALLOW_ANY`, so that traffic to these services can be monitored.", "type": "object", "properties": {"mode": {"$ref": "#/components/schemas/istio.networking.v1beta1.OutboundTrafficPolicy.Mode"}, "egressProxy": {"$ref": "#/components/schemas/istio.networking.v1beta1.Destination"}}}, "istio.networking.v1beta1.OutboundTrafficPolicy.Mode": {"type": "string", "enum": ["REGISTRY_ONLY", "ALLOW_ANY"]}, "istio.networking.v1beta1.Port": {"description": "Port describes the properties of a specific port of a service.", "type": "object", "properties": {"number": {"description": "A valid non-negative integer port number.", "type": "integer"}, "protocol": {"description": "The protocol exposed on the port. MUST BE one of HTTP|HTTPS|GRPC|HTTP2|MONGO|TCP|TLS. TLS implies the connection will be routed based on the SNI header to the destination without terminating the TLS connection.", "type": "string"}, "name": {"description": "Label assigned to the port.", "type": "string"}, "targetPort": {"description": "The port number on the endpoint where the traffic will be received. Applicable only when used with ServiceEntries.", "type": "integer"}}}, "istio.networking.v1beta1.PortSelector": {"description": "PortSelector specifies the number of a port to be used for matching or selection for final routing.", "type": "object", "properties": {"number": {"description": "Valid port number", "type": "integer"}}}, "istio.networking.v1beta1.ServerTLSSettings": {"type": "object", "properties": {"httpsRedirect": {"description": "If set to true, the load balancer will send a 301 redirect for all http connections, asking the clients to use HTTPS.", "type": "boolean"}, "mode": {"$ref": "#/components/schemas/istio.networking.v1beta1.ServerTLSSettings.TLSmode"}, "serverCertificate": {"description": "REQUIRED if mode is `SIMPLE` or `MUTUAL`. The path to the file holding the server-side TLS certificate to use.", "type": "string"}, "privateKey": {"description": "REQUIRED if mode is `SIMPLE` or `MUTUAL`. The path to the file holding the server's private key.", "type": "string"}, "caCertificates": {"description": "REQUIRED if mode is `MUTUAL`. The path to a file containing certificate authority certificates to use in verifying a presented client side certificate.", "type": "string"}, "credentialName": {"description": "For gateways running on Kubernetes, the name of the secret that holds the TLS certs including the CA certificates. Applicable only on Kubernetes. The secret (of type `generic`) should contain the following keys and values: `key: <privateKey>` and `cert: <serverCert>`. For mutual TLS, `cacert: <CACertificate>` can be provided in the same secret or a separate secret named `<secret>-cacert`. Secret of type tls for server certificates along with ca.crt key for CA certificates is also supported. Only one of server certificates and CA certificate or credentialName can be specified.", "type": "string"}, "subjectAltNames": {"description": "A list of alternate names to verify the subject identity in the certificate presented by the client.", "type": "array", "items": {"type": "string"}}, "verifyCertificateSpki": {"description": "An optional list of base64-encoded SHA-256 hashes of the SPKIs of authorized client certificates. Note: When both verify_certificate_hash and verify_certificate_spki are specified, a hash matching either value will result in the certificate being accepted.", "type": "array", "items": {"type": "string"}}, "verifyCertificateHash": {"description": "An optional list of hex-encoded SHA-256 hashes of the authorized client certificates. Both simple and colon separated formats are acceptable. Note: When both verify_certificate_hash and verify_certificate_spki are specified, a hash matching either value will result in the certificate being accepted.", "type": "array", "items": {"type": "string"}}, "minProtocolVersion": {"$ref": "#/components/schemas/istio.networking.v1beta1.ServerTLSSettings.TLSProtocol"}, "maxProtocolVersion": {"$ref": "#/components/schemas/istio.networking.v1beta1.ServerTLSSettings.TLSProtocol"}, "cipherSuites": {"description": "Optional: If specified, only support the specified cipher list. Otherwise default to the default cipher list supported by <PERSON>voy.", "type": "array", "items": {"type": "string"}}}}, "istio.networking.v1beta1.ServerTLSSettings.TLSProtocol": {"description": "TLS protocol versions.", "type": "string", "enum": ["TLS_AUTO", "TLSV1_0", "TLSV1_1", "TLSV1_2", "TLSV1_3"]}, "istio.networking.v1beta1.ServerTLSSettings.TLSmode": {"description": "TLS modes enforced by the proxy", "type": "string", "enum": ["PASSTHROUGH", "SIMPLE", "MUTUAL", "AUTO_PASSTHROUGH", "ISTIO_MUTUAL"]}, "istio.networking.v1beta1.Sidecar": {"description": "`Sidecar` describes the configuration of the sidecar proxy that mediates inbound and outbound communication of the workload instance to which it is attached.", "type": "object", "properties": {"workloadSelector": {"$ref": "#/components/schemas/istio.networking.v1beta1.WorkloadSelector"}, "ingress": {"description": "Ingress specifies the configuration of the sidecar for processing inbound traffic to the attached workload instance. If omitted, <PERSON><PERSON><PERSON> will automatically configure the sidecar based on the information about the workload obtained from the orchestration platform (e.g., exposed ports, services, etc.). If specified, inbound ports are configured if and only if the workload instance is associated with a service.", "type": "array", "items": {"$ref": "#/components/schemas/istio.networking.v1beta1.IstioIngressListener"}}, "egress": {"description": "Egress specifies the configuration of the sidecar for processing outbound traffic from the attached workload instance to other services in the mesh. If not specified, inherits the system detected defaults from the namespace-wide or the global default Sidecar.", "type": "array", "items": {"$ref": "#/components/schemas/istio.networking.v1beta1.IstioEgressListener"}}, "outboundTrafficPolicy": {"$ref": "#/components/schemas/istio.networking.v1beta1.OutboundTrafficPolicy"}}}, "istio.networking.v1beta1.WorkloadSelector": {"description": "`WorkloadSelector` specifies the criteria used to determine if the `Gateway`, `Sidecar`, `EnvoyFilter`, `ServiceEntry`, or `DestinationRule` configuration can be applied to a proxy. The matching criteria includes the metadata associated with a proxy, workload instance info such as labels attached to the pod/VM, or any other info that the proxy provides to <PERSON><PERSON><PERSON> during the initial handshake. If multiple conditions are specified, all conditions need to match in order for the workload instance to be selected. Currently, only label based selection mechanism is supported.", "type": "object", "properties": {"labels": {"description": "One or more labels that indicate a specific set of pods/VMs on which the configuration should be applied. The scope of label search is restricted to the configuration namespace in which the the resource is present.", "type": "object", "additionalProperties": {"type": "string"}}}}}}}