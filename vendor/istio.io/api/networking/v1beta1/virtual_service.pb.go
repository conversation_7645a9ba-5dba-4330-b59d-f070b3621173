// Copyright 2020 Istio Authors
//
//   Licensed under the Apache License, Version 2.0 (the "License");
//   you may not use this file except in compliance with the License.
//   You may obtain a copy of the License at
//
//       http://www.apache.org/licenses/LICENSE-2.0
//
//   Unless required by applicable law or agreed to in writing, software
//   distributed under the License is distributed on an "AS IS" BASIS,
//   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//   See the License for the specific language governing permissions and
//   limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        (unknown)
// source: networking/v1beta1/virtual_service.proto

// $schema: istio.networking.v1beta1.VirtualService
// $title: Virtual Service
// $description: Configuration affecting label/content routing, sni routing, etc.
// $location: https://istio.io/docs/reference/config/networking/virtual-service.html
// $aliases: [/docs/reference/config/networking/v1beta1/virtual-service]
// $mode: none

// Configuration affecting traffic routing. Here are a few terms useful to define
// in the context of traffic routing.
//
// `Service` a unit of application behavior bound to a unique name in a
// service registry. Services consist of multiple network *endpoints*
// implemented by workload instances running on pods, containers, VMs etc.
//
// `Service versions (a.k.a. subsets)` - In a continuous deployment
// scenario, for a given service, there can be distinct subsets of
// instances running different variants of the application binary. These
// variants are not necessarily different API versions. They could be
// iterative changes to the same service, deployed in different
// environments (prod, staging, dev, etc.). Common scenarios where this
// occurs include A/B testing, canary rollouts, etc. The choice of a
// particular version can be decided based on various criterion (headers,
// url, etc.) and/or by weights assigned to each version. Each service has
// a default version consisting of all its instances.
//
// `Source` - A downstream client calling a service.
//
// `Host` - The address used by a client when attempting to connect to a
// service.
//
// `Access model` - Applications address only the destination service
// (Host) without knowledge of individual service versions (subsets). The
// actual choice of the version is determined by the proxy/sidecar, enabling the
// application code to decouple itself from the evolution of dependent
// services.
//
// A `VirtualService` defines a set of traffic routing rules to apply when a host is
// addressed. Each routing rule defines matching criteria for traffic of a specific
// protocol. If the traffic is matched, then it is sent to a named destination service
// (or subset/version of it) defined in the registry.
//
// The source of traffic can also be matched in a routing rule. This allows routing
// to be customized for specific client contexts.
//
// The following example on Kubernetes, routes all HTTP traffic by default to
// pods of the reviews service with label "version: v1". In addition,
// HTTP requests with path starting with /wpcatalog/ or /consumercatalog/ will
// be rewritten to /newcatalog and sent to pods with label "version: v2".
//
//
// {{<tabset category-name="example">}}
// {{<tab name="v1alpha3" category-value="v1alpha3">}}
// ```yaml
// apiVersion: networking.istio.io/v1alpha3
// kind: VirtualService
// metadata:
//   name: reviews-route
// spec:
//   hosts:
//   - reviews.prod.svc.cluster.local
//   http:
//   - name: "reviews-v2-routes"
//     match:
//     - uri:
//         prefix: "/wpcatalog"
//     - uri:
//         prefix: "/consumercatalog"
//     rewrite:
//       uri: "/newcatalog"
//     route:
//     - destination:
//         host: reviews.prod.svc.cluster.local
//         subset: v2
//   - name: "reviews-v1-route"
//     route:
//     - destination:
//         host: reviews.prod.svc.cluster.local
//         subset: v1
// ```
// {{</tab>}}
//
// {{<tab name="v1beta1" category-value="v1beta1">}}
// ```yaml
// apiVersion: networking.istio.io/v1beta1
// kind: VirtualService
// metadata:
//   name: reviews-route
// spec:
//   hosts:
//   - reviews.prod.svc.cluster.local
//   http:
//   - name: "reviews-v2-routes"
//     match:
//     - uri:
//         prefix: "/wpcatalog"
//     - uri:
//         prefix: "/consumercatalog"
//     rewrite:
//       uri: "/newcatalog"
//     route:
//     - destination:
//         host: reviews.prod.svc.cluster.local
//         subset: v2
//   - name: "reviews-v1-route"
//     route:
//     - destination:
//         host: reviews.prod.svc.cluster.local
//         subset: v1
// ```
// {{</tab>}}
// {{</tabset>}}
//
// A subset/version of a route destination is identified with a reference
// to a named service subset which must be declared in a corresponding
// `DestinationRule`.
//
// {{<tabset category-name="example">}}
// {{<tab name="v1alpha3" category-value="v1alpha3">}}
// ```yaml
// apiVersion: networking.istio.io/v1alpha3
// kind: DestinationRule
// metadata:
//   name: reviews-destination
// spec:
//   host: reviews.prod.svc.cluster.local
//   subsets:
//   - name: v1
//     labels:
//       version: v1
//   - name: v2
//     labels:
//       version: v2
// ```
// {{</tab>}}
//
// {{<tab name="v1beta1" category-value="v1beta1">}}
// ```yaml
// apiVersion: networking.istio.io/v1beta1
// kind: DestinationRule
// metadata:
//   name: reviews-destination
// spec:
//   host: reviews.prod.svc.cluster.local
//   subsets:
//   - name: v1
//     labels:
//       version: v1
//   - name: v2
//     labels:
//       version: v2
// ```
// {{</tab>}}
// {{</tabset>}}
//

package v1beta1

import (
	duration "github.com/golang/protobuf/ptypes/duration"
	wrappers "github.com/golang/protobuf/ptypes/wrappers"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type HTTPRedirect_RedirectPortSelection int32

const (
	HTTPRedirect_FROM_PROTOCOL_DEFAULT HTTPRedirect_RedirectPortSelection = 0
	HTTPRedirect_FROM_REQUEST_PORT     HTTPRedirect_RedirectPortSelection = 1
)

// Enum value maps for HTTPRedirect_RedirectPortSelection.
var (
	HTTPRedirect_RedirectPortSelection_name = map[int32]string{
		0: "FROM_PROTOCOL_DEFAULT",
		1: "FROM_REQUEST_PORT",
	}
	HTTPRedirect_RedirectPortSelection_value = map[string]int32{
		"FROM_PROTOCOL_DEFAULT": 0,
		"FROM_REQUEST_PORT":     1,
	}
)

func (x HTTPRedirect_RedirectPortSelection) Enum() *HTTPRedirect_RedirectPortSelection {
	p := new(HTTPRedirect_RedirectPortSelection)
	*p = x
	return p
}

func (x HTTPRedirect_RedirectPortSelection) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (HTTPRedirect_RedirectPortSelection) Descriptor() protoreflect.EnumDescriptor {
	return file_networking_v1beta1_virtual_service_proto_enumTypes[0].Descriptor()
}

func (HTTPRedirect_RedirectPortSelection) Type() protoreflect.EnumType {
	return &file_networking_v1beta1_virtual_service_proto_enumTypes[0]
}

func (x HTTPRedirect_RedirectPortSelection) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use HTTPRedirect_RedirectPortSelection.Descriptor instead.
func (HTTPRedirect_RedirectPortSelection) EnumDescriptor() ([]byte, []int) {
	return file_networking_v1beta1_virtual_service_proto_rawDescGZIP(), []int{12, 0}
}

// Configuration affecting traffic routing.
//
// <!-- crd generation tags
// +cue-gen:VirtualService:groupName:networking.istio.io
// +cue-gen:VirtualService:version:v1beta1
// +cue-gen:VirtualService:annotations:helm.sh/resource-policy=keep
// +cue-gen:VirtualService:labels:app=istio-pilot,chart=istio,heritage=Tiller,release=istio
// +cue-gen:VirtualService:subresource:status
// +cue-gen:VirtualService:scope:Namespaced
// +cue-gen:VirtualService:resource:categories=istio-io,networking-istio-io,shortNames=vs
// +cue-gen:VirtualService:printerColumn:name=Gateways,type=string,JSONPath=.spec.gateways,description="The names of gateways and sidecars
// that should apply these routes"
// +cue-gen:VirtualService:printerColumn:name=Hosts,type=string,JSONPath=.spec.hosts,description="The destination hosts to which traffic is being sent"
// +cue-gen:VirtualService:printerColumn:name=Age,type=date,JSONPath=.metadata.creationTimestamp,description="CreationTimestamp is a timestamp
// representing the server time when this object was created. It is not guaranteed to be set in happens-before order across separate operations.
// Clients may not set this value. It is represented in RFC3339 form and is in UTC.
// Populated by the system. Read-only. Null for lists. More info: https://git.k8s.io/community/contributors/devel/api-conventions.md#metadata"
// +cue-gen:VirtualService:preserveUnknownFields:false
// -->
//
// <!-- go code generation tags
// +kubetype-gen
// +kubetype-gen:groupVersion=networking.istio.io/v1beta1
// +genclient
// +k8s:deepcopy-gen=true
// -->
// <!-- istio code generation tags
// +istio.io/sync-from:networking/v1alpha3/virtual_service.proto
// -->
type VirtualService struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The destination hosts to which traffic is being sent. Could
	// be a DNS name with wildcard prefix or an IP address.  Depending on the
	// platform, short-names can also be used instead of a FQDN (i.e. has no
	// dots in the name). In such a scenario, the FQDN of the host would be
	// derived based on the underlying platform.
	//
	// A single VirtualService can be used to describe all the traffic
	// properties of the corresponding hosts, including those for multiple
	// HTTP and TCP ports. Alternatively, the traffic properties of a host
	// can be defined using more than one VirtualService, with certain
	// caveats. Refer to the
	// [Operations Guide](https://istio.io/docs/ops/best-practices/traffic-management/#split-virtual-services)
	// for details.
	//
	// *Note for Kubernetes users*: When short names are used (e.g. "reviews"
	// instead of "reviews.default.svc.cluster.local"), Istio will interpret
	// the short name based on the namespace of the rule, not the service. A
	// rule in the "default" namespace containing a host "reviews" will be
	// interpreted as "reviews.default.svc.cluster.local", irrespective of
	// the actual namespace associated with the reviews service. _To avoid
	// potential misconfigurations, it is recommended to always use fully
	// qualified domain names over short names._
	//
	// The hosts field applies to both HTTP and TCP services. Service inside
	// the mesh, i.e., those found in the service registry, must always be
	// referred to using their alphanumeric names. IP addresses are allowed
	// only for services defined via the Gateway.
	//
	// *Note*: It must be empty for a delegate VirtualService.
	Hosts []string `protobuf:"bytes,1,rep,name=hosts,proto3" json:"hosts,omitempty"`
	// The names of gateways and sidecars that should apply these routes.
	// Gateways in other namespaces may be referred to by
	// `<gateway namespace>/<gateway name>`; specifying a gateway with no
	// namespace qualifier is the same as specifying the VirtualService's
	// namespace. A single VirtualService is used for sidecars inside the mesh as
	// well as for one or more gateways. The selection condition imposed by this
	// field can be overridden using the source field in the match conditions
	// of protocol-specific routes. The reserved word `mesh` is used to imply
	// all the sidecars in the mesh. When this field is omitted, the default
	// gateway (`mesh`) will be used, which would apply the rule to all
	// sidecars in the mesh. If a list of gateway names is provided, the
	// rules will apply only to the gateways. To apply the rules to both
	// gateways and sidecars, specify `mesh` as one of the gateway names.
	Gateways []string `protobuf:"bytes,2,rep,name=gateways,proto3" json:"gateways,omitempty"`
	// An ordered list of route rules for HTTP traffic. HTTP routes will be
	// applied to platform service ports named 'http-*'/'http2-*'/'grpc-*', gateway
	// ports with protocol HTTP/HTTP2/GRPC/ TLS-terminated-HTTPS and service
	// entry ports using HTTP/HTTP2/GRPC protocols.  The first rule matching
	// an incoming request is used.
	Http []*HTTPRoute `protobuf:"bytes,3,rep,name=http,proto3" json:"http,omitempty"`
	// An ordered list of route rule for non-terminated TLS & HTTPS
	// traffic. Routing is typically performed using the SNI value presented
	// by the ClientHello message. TLS routes will be applied to platform
	// service ports named 'https-*', 'tls-*', unterminated gateway ports using
	// HTTPS/TLS protocols (i.e. with "passthrough" TLS mode) and service
	// entry ports using HTTPS/TLS protocols.  The first rule matching an
	// incoming request is used.  NOTE: Traffic 'https-*' or 'tls-*' ports
	// without associated virtual service will be treated as opaque TCP
	// traffic.
	Tls []*TLSRoute `protobuf:"bytes,5,rep,name=tls,proto3" json:"tls,omitempty"`
	// An ordered list of route rules for opaque TCP traffic. TCP routes will
	// be applied to any port that is not a HTTP or TLS port. The first rule
	// matching an incoming request is used.
	Tcp []*TCPRoute `protobuf:"bytes,4,rep,name=tcp,proto3" json:"tcp,omitempty"`
	// A list of namespaces to which this virtual service is exported. Exporting a
	// virtual service allows it to be used by sidecars and gateways defined in
	// other namespaces. This feature provides a mechanism for service owners
	// and mesh administrators to control the visibility of virtual services
	// across namespace boundaries.
	//
	// If no namespaces are specified then the virtual service is exported to all
	// namespaces by default.
	//
	// The value "." is reserved and defines an export to the same namespace that
	// the virtual service is declared in. Similarly the value "*" is reserved and
	// defines an export to all namespaces.
	ExportTo []string `protobuf:"bytes,6,rep,name=export_to,json=exportTo,proto3" json:"export_to,omitempty"`
}

func (x *VirtualService) Reset() {
	*x = VirtualService{}
	if protoimpl.UnsafeEnabled {
		mi := &file_networking_v1beta1_virtual_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VirtualService) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VirtualService) ProtoMessage() {}

func (x *VirtualService) ProtoReflect() protoreflect.Message {
	mi := &file_networking_v1beta1_virtual_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VirtualService.ProtoReflect.Descriptor instead.
func (*VirtualService) Descriptor() ([]byte, []int) {
	return file_networking_v1beta1_virtual_service_proto_rawDescGZIP(), []int{0}
}

func (x *VirtualService) GetHosts() []string {
	if x != nil {
		return x.Hosts
	}
	return nil
}

func (x *VirtualService) GetGateways() []string {
	if x != nil {
		return x.Gateways
	}
	return nil
}

func (x *VirtualService) GetHttp() []*HTTPRoute {
	if x != nil {
		return x.Http
	}
	return nil
}

func (x *VirtualService) GetTls() []*TLSRoute {
	if x != nil {
		return x.Tls
	}
	return nil
}

func (x *VirtualService) GetTcp() []*TCPRoute {
	if x != nil {
		return x.Tcp
	}
	return nil
}

func (x *VirtualService) GetExportTo() []string {
	if x != nil {
		return x.ExportTo
	}
	return nil
}

// Destination indicates the network addressable service to which the
// request/connection will be sent after processing a routing rule. The
// destination.host should unambiguously refer to a service in the service
// registry. Istio's service registry is composed of all the services found
// in the platform's service registry (e.g., Kubernetes services, Consul
// services), as well as services declared through the
// [ServiceEntry](https://istio.io/docs/reference/config/networking/service-entry/#ServiceEntry) resource.
//
// *Note for Kubernetes users*: When short names are used (e.g. "reviews"
// instead of "reviews.default.svc.cluster.local"), Istio will interpret
// the short name based on the namespace of the rule, not the service. A
// rule in the "default" namespace containing a host "reviews will be
// interpreted as "reviews.default.svc.cluster.local", irrespective of the
// actual namespace associated with the reviews service. _To avoid potential
// misconfigurations, it is recommended to always use fully qualified
// domain names over short names._
//
// The following Kubernetes example routes all traffic by default to pods
// of the reviews service with label "version: v1" (i.e., subset v1), and
// some to subset v2, in a Kubernetes environment.
//
// {{<tabset category-name="example">}}
// {{<tab name="v1alpha3" category-value="v1alpha3">}}
// ```yaml
// apiVersion: networking.istio.io/v1alpha3
// kind: VirtualService
// metadata:
//
//	name: reviews-route
//	namespace: foo
//
// spec:
//
//	hosts:
//	- reviews # interpreted as reviews.foo.svc.cluster.local
//	http:
//	- match:
//	  - uri:
//	      prefix: "/wpcatalog"
//	  - uri:
//	      prefix: "/consumercatalog"
//	  rewrite:
//	    uri: "/newcatalog"
//	  route:
//	  - destination:
//	      host: reviews # interpreted as reviews.foo.svc.cluster.local
//	      subset: v2
//	- route:
//	  - destination:
//	      host: reviews # interpreted as reviews.foo.svc.cluster.local
//	      subset: v1
//
// ```
// {{</tab>}}
//
// {{<tab name="v1beta1" category-value="v1beta1">}}
// ```yaml
// apiVersion: networking.istio.io/v1beta1
// kind: VirtualService
// metadata:
//
//	name: reviews-route
//	namespace: foo
//
// spec:
//
//	hosts:
//	- reviews # interpreted as reviews.foo.svc.cluster.local
//	http:
//	- match:
//	  - uri:
//	      prefix: "/wpcatalog"
//	  - uri:
//	      prefix: "/consumercatalog"
//	  rewrite:
//	    uri: "/newcatalog"
//	  route:
//	  - destination:
//	      host: reviews # interpreted as reviews.foo.svc.cluster.local
//	      subset: v2
//	- route:
//	  - destination:
//	      host: reviews # interpreted as reviews.foo.svc.cluster.local
//	      subset: v1
//
// ```
// {{</tab>}}
// {{</tabset>}}
//
// # And the associated DestinationRule
//
// {{<tabset category-name="example">}}
// {{<tab name="v1alpha3" category-value="v1alpha3">}}
// ```yaml
// apiVersion: networking.istio.io/v1alpha3
// kind: DestinationRule
// metadata:
//
//	name: reviews-destination
//	namespace: foo
//
// spec:
//
//	host: reviews # interpreted as reviews.foo.svc.cluster.local
//	subsets:
//	- name: v1
//	  labels:
//	    version: v1
//	- name: v2
//	  labels:
//	    version: v2
//
// ```
// {{</tab>}}
//
// {{<tab name="v1beta1" category-value="v1beta1">}}
// ```yaml
// apiVersion: networking.istio.io/v1beta1
// kind: DestinationRule
// metadata:
//
//	name: reviews-destination
//	namespace: foo
//
// spec:
//
//	host: reviews # interpreted as reviews.foo.svc.cluster.local
//	subsets:
//	- name: v1
//	  labels:
//	    version: v1
//	- name: v2
//	  labels:
//	    version: v2
//
// ```
// {{</tab>}}
// {{</tabset>}}
//
// The following VirtualService sets a timeout of 5s for all calls to
// productpage.prod.svc.cluster.local service in Kubernetes. Notice that
// there are no subsets defined in this rule. Istio will fetch all
// instances of productpage.prod.svc.cluster.local service from the service
// registry and populate the sidecar's load balancing pool. Also, notice
// that this rule is set in the istio-system namespace but uses the fully
// qualified domain name of the productpage service,
// productpage.prod.svc.cluster.local. Therefore the rule's namespace does
// not have an impact in resolving the name of the productpage service.
//
// {{<tabset category-name="example">}}
// {{<tab name="v1alpha3" category-value="v1alpha3">}}
// ```yaml
// apiVersion: networking.istio.io/v1alpha3
// kind: VirtualService
// metadata:
//
//	name: my-productpage-rule
//	namespace: istio-system
//
// spec:
//
//	hosts:
//	- productpage.prod.svc.cluster.local # ignores rule namespace
//	http:
//	- timeout: 5s
//	  route:
//	  - destination:
//	      host: productpage.prod.svc.cluster.local
//
// ```
// {{</tab>}}
//
// {{<tab name="v1beta1" category-value="v1beta1">}}
// ```yaml
// apiVersion: networking.istio.io/v1beta1
// kind: VirtualService
// metadata:
//
//	name: my-productpage-rule
//	namespace: istio-system
//
// spec:
//
//	hosts:
//	- productpage.prod.svc.cluster.local # ignores rule namespace
//	http:
//	- timeout: 5s
//	  route:
//	  - destination:
//	      host: productpage.prod.svc.cluster.local
//
// ```
// {{</tab>}}
// {{</tabset>}}
//
// To control routing for traffic bound to services outside the mesh, external
// services must first be added to Istio's internal service registry using the
// ServiceEntry resource. VirtualServices can then be defined to control traffic
// bound to these external services. For example, the following rules define a
// Service for wikipedia.org and set a timeout of 5s for HTTP requests.
//
// {{<tabset category-name="example">}}
// {{<tab name="v1alpha3" category-value="v1alpha3">}}
// ```yaml
// apiVersion: networking.istio.io/v1alpha3
// kind: ServiceEntry
// metadata:
//
//	name: external-svc-wikipedia
//
// spec:
//
//	hosts:
//	- wikipedia.org
//	location: MESH_EXTERNAL
//	ports:
//	- number: 80
//	  name: example-http
//	  protocol: HTTP
//	resolution: DNS
//
// ---
// apiVersion: networking.istio.io/v1alpha3
// kind: VirtualService
// metadata:
//
//	name: my-wiki-rule
//
// spec:
//
//	hosts:
//	- wikipedia.org
//	http:
//	- timeout: 5s
//	  route:
//	  - destination:
//	      host: wikipedia.org
//
// ```
// {{</tab>}}
//
// {{<tab name="v1beta1" category-value="v1beta1">}}
// ```yaml
// apiVersion: networking.istio.io/v1beta1
// kind: ServiceEntry
// metadata:
//
//	name: external-svc-wikipedia
//
// spec:
//
//	hosts:
//	- wikipedia.org
//	location: MESH_EXTERNAL
//	ports:
//	- number: 80
//	  name: example-http
//	  protocol: HTTP
//	resolution: DNS
//
// ---
// apiVersion: networking.istio.io/v1beta1
// kind: VirtualService
// metadata:
//
//	name: my-wiki-rule
//
// spec:
//
//	hosts:
//	- wikipedia.org
//	http:
//	- timeout: 5s
//	  route:
//	  - destination:
//	      host: wikipedia.org
//
// ```
// {{</tab>}}
// {{</tabset>}}
type Destination struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The name of a service from the service registry. Service
	// names are looked up from the platform's service registry (e.g.,
	// Kubernetes services, Consul services, etc.) and from the hosts
	// declared by [ServiceEntry](https://istio.io/docs/reference/config/networking/service-entry/#ServiceEntry). Traffic forwarded to
	// destinations that are not found in either of the two, will be dropped.
	//
	// *Note for Kubernetes users*: When short names are used (e.g. "reviews"
	// instead of "reviews.default.svc.cluster.local"), Istio will interpret
	// the short name based on the namespace of the rule, not the service. A
	// rule in the "default" namespace containing a host "reviews will be
	// interpreted as "reviews.default.svc.cluster.local", irrespective of
	// the actual namespace associated with the reviews service. To avoid
	// potential misconfiguration, it is recommended to always use fully
	// qualified domain names over short names.
	Host string `protobuf:"bytes,1,opt,name=host,proto3" json:"host,omitempty"`
	// The name of a subset within the service. Applicable only to services
	// within the mesh. The subset must be defined in a corresponding
	// DestinationRule.
	Subset string `protobuf:"bytes,2,opt,name=subset,proto3" json:"subset,omitempty"`
	// Specifies the port on the host that is being addressed. If a service
	// exposes only a single port it is not required to explicitly select the
	// port.
	Port *PortSelector `protobuf:"bytes,3,opt,name=port,proto3" json:"port,omitempty"`
}

func (x *Destination) Reset() {
	*x = Destination{}
	if protoimpl.UnsafeEnabled {
		mi := &file_networking_v1beta1_virtual_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Destination) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Destination) ProtoMessage() {}

func (x *Destination) ProtoReflect() protoreflect.Message {
	mi := &file_networking_v1beta1_virtual_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Destination.ProtoReflect.Descriptor instead.
func (*Destination) Descriptor() ([]byte, []int) {
	return file_networking_v1beta1_virtual_service_proto_rawDescGZIP(), []int{1}
}

func (x *Destination) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *Destination) GetSubset() string {
	if x != nil {
		return x.Subset
	}
	return ""
}

func (x *Destination) GetPort() *PortSelector {
	if x != nil {
		return x.Port
	}
	return nil
}

// Describes match conditions and actions for routing HTTP/1.1, HTTP2, and
// gRPC traffic. See VirtualService for usage examples.
type HTTPRoute struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The name assigned to the route for debugging purposes. The
	// route's name will be concatenated with the match's name and will
	// be logged in the access logs for requests matching this
	// route/match.
	Name string `protobuf:"bytes,17,opt,name=name,proto3" json:"name,omitempty"`
	// Match conditions to be satisfied for the rule to be
	// activated. All conditions inside a single match block have AND
	// semantics, while the list of match blocks have OR semantics. The rule
	// is matched if any one of the match blocks succeed.
	Match []*HTTPMatchRequest `protobuf:"bytes,1,rep,name=match,proto3" json:"match,omitempty"`
	// A HTTP rule can either return a direct_response, redirect or forward (default) traffic.
	// The forwarding target can be one of several versions of a service (see
	// glossary in beginning of document). Weights associated with the
	// service version determine the proportion of traffic it receives.
	Route []*HTTPRouteDestination `protobuf:"bytes,2,rep,name=route,proto3" json:"route,omitempty"`
	// A HTTP rule can either return a direct_response, redirect or forward (default) traffic.
	// If traffic passthrough option is specified in the rule,
	// route/redirect will be ignored. The redirect primitive can be used to
	// send a HTTP 301 redirect to a different URI or Authority.
	Redirect *HTTPRedirect `protobuf:"bytes,3,opt,name=redirect,proto3" json:"redirect,omitempty"`
	// A HTTP rule can either return a direct_response, redirect or forward (default) traffic.
	// Direct Response is used to specify a fixed response that should
	// be sent to clients.
	//
	// It can be set only when `Route` and `Redirect` are empty.
	DirectResponse *HTTPDirectResponse `protobuf:"bytes,21,opt,name=direct_response,json=directResponse,proto3" json:"direct_response,omitempty"`
	// Delegate is used to specify the particular VirtualService which
	// can be used to define delegate HTTPRoute.
	//
	// It can be set only when `Route` and `Redirect` are empty, and the route
	// rules of the delegate VirtualService will be merged with that in the
	// current one.
	//
	// **NOTE**:
	//
	//  1. Only one level delegation is supported.
	//  2. The delegate's HTTPMatchRequest must be a strict subset of the root's,
	//     otherwise there is a conflict and the HTTPRoute will not take effect.
	Delegate *Delegate `protobuf:"bytes,20,opt,name=delegate,proto3" json:"delegate,omitempty"`
	// Rewrite HTTP URIs and Authority headers. Rewrite cannot be used with
	// Redirect primitive. Rewrite will be performed before forwarding.
	Rewrite *HTTPRewrite `protobuf:"bytes,4,opt,name=rewrite,proto3" json:"rewrite,omitempty"`
	// Timeout for HTTP requests, default is disabled.
	Timeout *duration.Duration `protobuf:"bytes,6,opt,name=timeout,proto3" json:"timeout,omitempty"`
	// Retry policy for HTTP requests.
	Retries *HTTPRetry `protobuf:"bytes,7,opt,name=retries,proto3" json:"retries,omitempty"`
	// Fault injection policy to apply on HTTP traffic at the client side.
	// Note that timeouts or retries will not be enabled when faults are
	// enabled on the client side.
	Fault *HTTPFaultInjection `protobuf:"bytes,8,opt,name=fault,proto3" json:"fault,omitempty"`
	// Mirror HTTP traffic to a another destination in addition to forwarding
	// the requests to the intended destination. Mirrored traffic is on a
	// best effort basis where the sidecar/gateway will not wait for the
	// mirrored cluster to respond before returning the response from the
	// original destination.  Statistics will be generated for the mirrored
	// destination.
	Mirror *Destination `protobuf:"bytes,9,opt,name=mirror,proto3" json:"mirror,omitempty"`
	// Percentage of the traffic to be mirrored by the `mirror` field.
	// Use of integer `mirror_percent` value is deprecated. Use the
	// double `mirror_percentage` field instead
	// $hide_from_docs
	//
	// Deprecated: Do not use.
	MirrorPercent *wrappers.UInt32Value `protobuf:"bytes,18,opt,name=mirror_percent,json=mirrorPercent,proto3" json:"mirror_percent,omitempty"`
	// Percentage of the traffic to be mirrored by the `mirror` field.
	// If this field is absent, all the traffic (100%) will be mirrored.
	// Max value is 100.
	MirrorPercentage *Percent `protobuf:"bytes,19,opt,name=mirror_percentage,json=mirrorPercentage,proto3" json:"mirror_percentage,omitempty"`
	// Cross-Origin Resource Sharing policy (CORS). Refer to
	// [CORS](https://developer.mozilla.org/en-US/docs/Web/HTTP/CORS)
	// for further details about cross origin resource sharing.
	CorsPolicy *CorsPolicy `protobuf:"bytes,10,opt,name=cors_policy,json=corsPolicy,proto3" json:"cors_policy,omitempty"`
	// Header manipulation rules
	Headers *Headers `protobuf:"bytes,16,opt,name=headers,proto3" json:"headers,omitempty"`
}

func (x *HTTPRoute) Reset() {
	*x = HTTPRoute{}
	if protoimpl.UnsafeEnabled {
		mi := &file_networking_v1beta1_virtual_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HTTPRoute) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HTTPRoute) ProtoMessage() {}

func (x *HTTPRoute) ProtoReflect() protoreflect.Message {
	mi := &file_networking_v1beta1_virtual_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HTTPRoute.ProtoReflect.Descriptor instead.
func (*HTTPRoute) Descriptor() ([]byte, []int) {
	return file_networking_v1beta1_virtual_service_proto_rawDescGZIP(), []int{2}
}

func (x *HTTPRoute) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *HTTPRoute) GetMatch() []*HTTPMatchRequest {
	if x != nil {
		return x.Match
	}
	return nil
}

func (x *HTTPRoute) GetRoute() []*HTTPRouteDestination {
	if x != nil {
		return x.Route
	}
	return nil
}

func (x *HTTPRoute) GetRedirect() *HTTPRedirect {
	if x != nil {
		return x.Redirect
	}
	return nil
}

func (x *HTTPRoute) GetDirectResponse() *HTTPDirectResponse {
	if x != nil {
		return x.DirectResponse
	}
	return nil
}

func (x *HTTPRoute) GetDelegate() *Delegate {
	if x != nil {
		return x.Delegate
	}
	return nil
}

func (x *HTTPRoute) GetRewrite() *HTTPRewrite {
	if x != nil {
		return x.Rewrite
	}
	return nil
}

func (x *HTTPRoute) GetTimeout() *duration.Duration {
	if x != nil {
		return x.Timeout
	}
	return nil
}

func (x *HTTPRoute) GetRetries() *HTTPRetry {
	if x != nil {
		return x.Retries
	}
	return nil
}

func (x *HTTPRoute) GetFault() *HTTPFaultInjection {
	if x != nil {
		return x.Fault
	}
	return nil
}

func (x *HTTPRoute) GetMirror() *Destination {
	if x != nil {
		return x.Mirror
	}
	return nil
}

// Deprecated: Do not use.
func (x *HTTPRoute) GetMirrorPercent() *wrappers.UInt32Value {
	if x != nil {
		return x.MirrorPercent
	}
	return nil
}

func (x *HTTPRoute) GetMirrorPercentage() *Percent {
	if x != nil {
		return x.MirrorPercentage
	}
	return nil
}

func (x *HTTPRoute) GetCorsPolicy() *CorsPolicy {
	if x != nil {
		return x.CorsPolicy
	}
	return nil
}

func (x *HTTPRoute) GetHeaders() *Headers {
	if x != nil {
		return x.Headers
	}
	return nil
}

// Describes the delegate VirtualService.
// The following routing rules forward the traffic to `/productpage` by a delegate VirtualService named `productpage`,
// forward the traffic to `/reviews` by a delegate VirtualService named `reviews`.
//
// ```yaml
// apiVersion: networking.istio.io/v1alpha3
// kind: VirtualService
// metadata:
//
//	name: bookinfo
//
// spec:
//
//	hosts:
//	- "bookinfo.com"
//	gateways:
//	- mygateway
//	http:
//	- match:
//	  - uri:
//	      prefix: "/productpage"
//	  delegate:
//	     name: productpage
//	     namespace: nsA
//	- match:
//	  - uri:
//	      prefix: "/reviews"
//	  delegate:
//	      name: reviews
//	      namespace: nsB
//
// ```
//
// ```yaml
// apiVersion: networking.istio.io/v1alpha3
// kind: VirtualService
// metadata:
//
//	name: productpage
//	namespace: nsA
//
// spec:
//
//	http:
//	- match:
//	   - uri:
//	      prefix: "/productpage/v1/"
//	  route:
//	  - destination:
//	      host: productpage-v1.nsA.svc.cluster.local
//	- route:
//	  - destination:
//	      host: productpage.nsA.svc.cluster.local
//
// ```
//
// ```yaml
// apiVersion: networking.istio.io/v1alpha3
// kind: VirtualService
// metadata:
//
//	name: reviews
//	namespace: nsB
//
// spec:
//
//	http:
//	- route:
//	  - destination:
//	      host: reviews.nsB.svc.cluster.local
//
// ```
type Delegate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Name specifies the name of the delegate VirtualService.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Namespace specifies the namespace where the delegate VirtualService resides.
	// By default, it is same to the root's.
	Namespace string `protobuf:"bytes,2,opt,name=namespace,proto3" json:"namespace,omitempty"`
}

func (x *Delegate) Reset() {
	*x = Delegate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_networking_v1beta1_virtual_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Delegate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Delegate) ProtoMessage() {}

func (x *Delegate) ProtoReflect() protoreflect.Message {
	mi := &file_networking_v1beta1_virtual_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Delegate.ProtoReflect.Descriptor instead.
func (*Delegate) Descriptor() ([]byte, []int) {
	return file_networking_v1beta1_virtual_service_proto_rawDescGZIP(), []int{3}
}

func (x *Delegate) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Delegate) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

// Message headers can be manipulated when Envoy forwards requests to,
// or responses from, a destination service. Header manipulation rules can
// be specified for a specific route destination or for all destinations.
// The following VirtualService adds a `test` header with the value `true`
// to requests that are routed to any `reviews` service destination.
// It also removes the `foo` response header, but only from responses
// coming from the `v1` subset (version) of the `reviews` service.
//
// {{<tabset category-name="example">}}
// {{<tab name="v1alpha3" category-value="v1alpha3">}}
// ```yaml
// apiVersion: networking.istio.io/v1alpha3
// kind: VirtualService
// metadata:
//
//	name: reviews-route
//
// spec:
//
//	hosts:
//	- reviews.prod.svc.cluster.local
//	http:
//	- headers:
//	    request:
//	      set:
//	        test: "true"
//	  route:
//	  - destination:
//	      host: reviews.prod.svc.cluster.local
//	      subset: v2
//	    weight: 25
//	  - destination:
//	      host: reviews.prod.svc.cluster.local
//	      subset: v1
//	    headers:
//	      response:
//	        remove:
//	        - foo
//	    weight: 75
//
// ```
// {{</tab>}}
//
// {{<tab name="v1beta1" category-value="v1beta1">}}
// ```yaml
// apiVersion: networking.istio.io/v1beta1
// kind: VirtualService
// metadata:
//
//	name: reviews-route
//
// spec:
//
//	hosts:
//	- reviews.prod.svc.cluster.local
//	http:
//	- headers:
//	    request:
//	      set:
//	        test: "true"
//	  route:
//	  - destination:
//	      host: reviews.prod.svc.cluster.local
//	      subset: v2
//	    weight: 25
//	  - destination:
//	      host: reviews.prod.svc.cluster.local
//	      subset: v1
//	    headers:
//	      response:
//	        remove:
//	        - foo
//	    weight: 75
//
// ```
// {{</tab>}}
// {{</tabset>}}
type Headers struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Header manipulation rules to apply before forwarding a request
	// to the destination service
	Request *Headers_HeaderOperations `protobuf:"bytes,1,opt,name=request,proto3" json:"request,omitempty"`
	// Header manipulation rules to apply before returning a response
	// to the caller
	Response *Headers_HeaderOperations `protobuf:"bytes,2,opt,name=response,proto3" json:"response,omitempty"`
}

func (x *Headers) Reset() {
	*x = Headers{}
	if protoimpl.UnsafeEnabled {
		mi := &file_networking_v1beta1_virtual_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Headers) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Headers) ProtoMessage() {}

func (x *Headers) ProtoReflect() protoreflect.Message {
	mi := &file_networking_v1beta1_virtual_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Headers.ProtoReflect.Descriptor instead.
func (*Headers) Descriptor() ([]byte, []int) {
	return file_networking_v1beta1_virtual_service_proto_rawDescGZIP(), []int{4}
}

func (x *Headers) GetRequest() *Headers_HeaderOperations {
	if x != nil {
		return x.Request
	}
	return nil
}

func (x *Headers) GetResponse() *Headers_HeaderOperations {
	if x != nil {
		return x.Response
	}
	return nil
}

// Describes match conditions and actions for routing unterminated TLS
// traffic (TLS/HTTPS) The following routing rule forwards unterminated TLS
// traffic arriving at port 443 of gateway called "mygateway" to internal
// services in the mesh based on the SNI value.
//
// {{<tabset category-name="example">}}
// {{<tab name="v1alpha3" category-value="v1alpha3">}}
// ```yaml
// apiVersion: networking.istio.io/v1alpha3
// kind: VirtualService
// metadata:
//
//	name: bookinfo-sni
//
// spec:
//
//	hosts:
//	- "*.bookinfo.com"
//	gateways:
//	- mygateway
//	tls:
//	- match:
//	  - port: 443
//	    sniHosts:
//	    - login.bookinfo.com
//	  route:
//	  - destination:
//	      host: login.prod.svc.cluster.local
//	- match:
//	  - port: 443
//	    sniHosts:
//	    - reviews.bookinfo.com
//	  route:
//	  - destination:
//	      host: reviews.prod.svc.cluster.local
//
// ```
// {{</tab>}}
//
// {{<tab name="v1beta1" category-value="v1beta1">}}
// ```yaml
// apiVersion: networking.istio.io/v1beta1
// kind: VirtualService
// metadata:
//
//	name: bookinfo-sni
//
// spec:
//
//	hosts:
//	- "*.bookinfo.com"
//	gateways:
//	- mygateway
//	tls:
//	- match:
//	  - port: 443
//	    sniHosts:
//	    - login.bookinfo.com
//	  route:
//	  - destination:
//	      host: login.prod.svc.cluster.local
//	- match:
//	  - port: 443
//	    sniHosts:
//	    - reviews.bookinfo.com
//	  route:
//	  - destination:
//	      host: reviews.prod.svc.cluster.local
//
// ```
// {{</tab>}}
// {{</tabset>}}
type TLSRoute struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Match conditions to be satisfied for the rule to be
	// activated. All conditions inside a single match block have AND
	// semantics, while the list of match blocks have OR semantics. The rule
	// is matched if any one of the match blocks succeed.
	Match []*TLSMatchAttributes `protobuf:"bytes,1,rep,name=match,proto3" json:"match,omitempty"`
	// The destination to which the connection should be forwarded to.
	Route []*RouteDestination `protobuf:"bytes,2,rep,name=route,proto3" json:"route,omitempty"`
}

func (x *TLSRoute) Reset() {
	*x = TLSRoute{}
	if protoimpl.UnsafeEnabled {
		mi := &file_networking_v1beta1_virtual_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TLSRoute) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TLSRoute) ProtoMessage() {}

func (x *TLSRoute) ProtoReflect() protoreflect.Message {
	mi := &file_networking_v1beta1_virtual_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TLSRoute.ProtoReflect.Descriptor instead.
func (*TLSRoute) Descriptor() ([]byte, []int) {
	return file_networking_v1beta1_virtual_service_proto_rawDescGZIP(), []int{5}
}

func (x *TLSRoute) GetMatch() []*TLSMatchAttributes {
	if x != nil {
		return x.Match
	}
	return nil
}

func (x *TLSRoute) GetRoute() []*RouteDestination {
	if x != nil {
		return x.Route
	}
	return nil
}

// Describes match conditions and actions for routing TCP traffic. The
// following routing rule forwards traffic arriving at port 27017 for
// mongo.prod.svc.cluster.local to another Mongo server on port 5555.
//
// {{<tabset category-name="example">}}
// {{<tab name="v1alpha3" category-value="v1alpha3">}}
// ```yaml
// apiVersion: networking.istio.io/v1alpha3
// kind: VirtualService
// metadata:
//
//	name: bookinfo-mongo
//
// spec:
//
//	hosts:
//	- mongo.prod.svc.cluster.local
//	tcp:
//	- match:
//	  - port: 27017
//	  route:
//	  - destination:
//	      host: mongo.backup.svc.cluster.local
//	      port:
//	        number: 5555
//
// ```
// {{</tab>}}
//
// {{<tab name="v1beta1" category-value="v1beta1">}}
// ```yaml
// apiVersion: networking.istio.io/v1beta1
// kind: VirtualService
// metadata:
//
//	name: bookinfo-mongo
//
// spec:
//
//	hosts:
//	- mongo.prod.svc.cluster.local
//	tcp:
//	- match:
//	  - port: 27017
//	  route:
//	  - destination:
//	      host: mongo.backup.svc.cluster.local
//	      port:
//	        number: 5555
//
// ```
// {{</tab>}}
// {{</tabset>}}
type TCPRoute struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Match conditions to be satisfied for the rule to be
	// activated. All conditions inside a single match block have AND
	// semantics, while the list of match blocks have OR semantics. The rule
	// is matched if any one of the match blocks succeed.
	Match []*L4MatchAttributes `protobuf:"bytes,1,rep,name=match,proto3" json:"match,omitempty"`
	// The destination to which the connection should be forwarded to.
	Route []*RouteDestination `protobuf:"bytes,2,rep,name=route,proto3" json:"route,omitempty"`
}

func (x *TCPRoute) Reset() {
	*x = TCPRoute{}
	if protoimpl.UnsafeEnabled {
		mi := &file_networking_v1beta1_virtual_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TCPRoute) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TCPRoute) ProtoMessage() {}

func (x *TCPRoute) ProtoReflect() protoreflect.Message {
	mi := &file_networking_v1beta1_virtual_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TCPRoute.ProtoReflect.Descriptor instead.
func (*TCPRoute) Descriptor() ([]byte, []int) {
	return file_networking_v1beta1_virtual_service_proto_rawDescGZIP(), []int{6}
}

func (x *TCPRoute) GetMatch() []*L4MatchAttributes {
	if x != nil {
		return x.Match
	}
	return nil
}

func (x *TCPRoute) GetRoute() []*RouteDestination {
	if x != nil {
		return x.Route
	}
	return nil
}

// HttpMatchRequest specifies a set of criterion to be met in order for the
// rule to be applied to the HTTP request. For example, the following
// restricts the rule to match only requests where the URL path
// starts with /ratings/v2/ and the request contains a custom `end-user` header
// with value `jason`.
//
// {{<tabset category-name="example">}}
// {{<tab name="v1alpha3" category-value="v1alpha3">}}
// ```yaml
// apiVersion: networking.istio.io/v1alpha3
// kind: VirtualService
// metadata:
//
//	name: ratings-route
//
// spec:
//
//	hosts:
//	- ratings.prod.svc.cluster.local
//	http:
//	- match:
//	  - headers:
//	      end-user:
//	        exact: jason
//	    uri:
//	      prefix: "/ratings/v2/"
//	    ignoreUriCase: true
//	  route:
//	  - destination:
//	      host: ratings.prod.svc.cluster.local
//
// ```
// {{</tab>}}
//
// {{<tab name="v1beta1" category-value="v1beta1">}}
// ```yaml
// apiVersion: networking.istio.io/v1beta1
// kind: VirtualService
// metadata:
//
//	name: ratings-route
//
// spec:
//
//	hosts:
//	- ratings.prod.svc.cluster.local
//	http:
//	- match:
//	  - headers:
//	      end-user:
//	        exact: jason
//	    uri:
//	      prefix: "/ratings/v2/"
//	    ignoreUriCase: true
//	  route:
//	  - destination:
//	      host: ratings.prod.svc.cluster.local
//
// ```
// {{</tab>}}
// {{</tabset>}}
//
// HTTPMatchRequest CANNOT be empty.
// **Note:** No regex string match can be set when delegate VirtualService is specified.
type HTTPMatchRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The name assigned to a match. The match's name will be
	// concatenated with the parent route's name and will be logged in
	// the access logs for requests matching this route.
	Name string `protobuf:"bytes,11,opt,name=name,proto3" json:"name,omitempty"`
	// URI to match
	// values are case-sensitive and formatted as follows:
	//
	// - `exact: "value"` for exact string match
	//
	// - `prefix: "value"` for prefix-based match
	//
	// - `regex: "value"` for RE2 style regex-based match (https://github.com/google/re2/wiki/Syntax).
	//
	// **Note:** Case-insensitive matching could be enabled via the
	// `ignore_uri_case` flag.
	Uri *StringMatch `protobuf:"bytes,1,opt,name=uri,proto3" json:"uri,omitempty"`
	// URI Scheme
	// values are case-sensitive and formatted as follows:
	//
	// - `exact: "value"` for exact string match
	//
	// - `prefix: "value"` for prefix-based match
	//
	// - `regex: "value"` for RE2 style regex-based match (https://github.com/google/re2/wiki/Syntax).
	Scheme *StringMatch `protobuf:"bytes,2,opt,name=scheme,proto3" json:"scheme,omitempty"`
	// HTTP Method
	// values are case-sensitive and formatted as follows:
	//
	// - `exact: "value"` for exact string match
	//
	// - `prefix: "value"` for prefix-based match
	//
	// - `regex: "value"` for RE2 style regex-based match (https://github.com/google/re2/wiki/Syntax).
	Method *StringMatch `protobuf:"bytes,3,opt,name=method,proto3" json:"method,omitempty"`
	// HTTP Authority
	// values are case-sensitive and formatted as follows:
	//
	// - `exact: "value"` for exact string match
	//
	// - `prefix: "value"` for prefix-based match
	//
	// - `regex: "value"` for RE2 style regex-based match (https://github.com/google/re2/wiki/Syntax).
	Authority *StringMatch `protobuf:"bytes,4,opt,name=authority,proto3" json:"authority,omitempty"`
	// The header keys must be lowercase and use hyphen as the separator,
	// e.g. _x-request-id_.
	//
	// Header values are case-sensitive and formatted as follows:
	//
	// - `exact: "value"` for exact string match
	//
	// - `prefix: "value"` for prefix-based match
	//
	// - `regex: "value"` for RE2 style regex-based match (https://github.com/google/re2/wiki/Syntax).
	//
	// If the value is empty and only the name of header is specfied, presence of the header is checked.
	// **Note:** The keys `uri`, `scheme`, `method`, and `authority` will be ignored.
	Headers map[string]*StringMatch `protobuf:"bytes,5,rep,name=headers,proto3" json:"headers,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// Specifies the ports on the host that is being addressed. Many services
	// only expose a single port or label ports with the protocols they support,
	// in these cases it is not required to explicitly select the port.
	Port uint32 `protobuf:"varint,6,opt,name=port,proto3" json:"port,omitempty"`
	// One or more labels that constrain the applicability of a rule to source (client) workloads
	// with the given labels. If the VirtualService has a list of gateways specified
	// in the top-level `gateways` field, it must include the reserved gateway
	// `mesh` for this field to be applicable.
	SourceLabels map[string]string `protobuf:"bytes,7,rep,name=source_labels,json=sourceLabels,proto3" json:"source_labels,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// Names of gateways where the rule should be applied. Gateway names
	// in the top-level `gateways` field of the VirtualService (if any) are overridden. The gateway
	// match is independent of sourceLabels.
	Gateways []string `protobuf:"bytes,8,rep,name=gateways,proto3" json:"gateways,omitempty"`
	// Query parameters for matching.
	//
	// Ex:
	//
	//   - For a query parameter like "?key=true", the map key would be "key" and
	//     the string match could be defined as `exact: "true"`.
	//
	//   - For a query parameter like "?key", the map key would be "key" and the
	//     string match could be defined as `exact: ""`.
	//
	//   - For a query parameter like "?key=123", the map key would be "key" and the
	//     string match could be defined as `regex: "\d+$"`. Note that this
	//     configuration will only match values like "123" but not "a123" or "123a".
	//
	// **Note:** `prefix` matching is currently not supported.
	QueryParams map[string]*StringMatch `protobuf:"bytes,9,rep,name=query_params,json=queryParams,proto3" json:"query_params,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// Flag to specify whether the URI matching should be case-insensitive.
	//
	// **Note:** The case will be ignored only in the case of `exact` and `prefix`
	// URI matches.
	IgnoreUriCase bool `protobuf:"varint,10,opt,name=ignore_uri_case,json=ignoreUriCase,proto3" json:"ignore_uri_case,omitempty"`
	// withoutHeader has the same syntax with the header, but has opposite meaning.
	// If a header is matched with a matching rule among withoutHeader, the traffic becomes not matched one.
	WithoutHeaders map[string]*StringMatch `protobuf:"bytes,12,rep,name=without_headers,json=withoutHeaders,proto3" json:"without_headers,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// Source namespace constraining the applicability of a rule to workloads in that namespace.
	// If the VirtualService has a list of gateways specified in the top-level `gateways` field,
	// it must include the reserved gateway `mesh` for this field to be applicable.
	SourceNamespace string `protobuf:"bytes,13,opt,name=source_namespace,json=sourceNamespace,proto3" json:"source_namespace,omitempty"`
	// The human readable prefix to use when emitting statistics for this route.
	// The statistics are generated with prefix route.<stat_prefix>.
	// This should be set for highly critical routes that one wishes to get "per-route" statistics on.
	// This prefix is only for proxy-level statistics (envoy_*) and not service-level (istio_*) statistics.
	// Refer to https://www.envoyproxy.io/docs/envoy/latest/api-v3/config/route/v3/route_components.proto#envoy-v3-api-field-config-route-v3-route-stat-prefix
	// for statistics that are generated when this is configured.
	StatPrefix string `protobuf:"bytes,14,opt,name=stat_prefix,json=statPrefix,proto3" json:"stat_prefix,omitempty"`
}

func (x *HTTPMatchRequest) Reset() {
	*x = HTTPMatchRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_networking_v1beta1_virtual_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HTTPMatchRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HTTPMatchRequest) ProtoMessage() {}

func (x *HTTPMatchRequest) ProtoReflect() protoreflect.Message {
	mi := &file_networking_v1beta1_virtual_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HTTPMatchRequest.ProtoReflect.Descriptor instead.
func (*HTTPMatchRequest) Descriptor() ([]byte, []int) {
	return file_networking_v1beta1_virtual_service_proto_rawDescGZIP(), []int{7}
}

func (x *HTTPMatchRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *HTTPMatchRequest) GetUri() *StringMatch {
	if x != nil {
		return x.Uri
	}
	return nil
}

func (x *HTTPMatchRequest) GetScheme() *StringMatch {
	if x != nil {
		return x.Scheme
	}
	return nil
}

func (x *HTTPMatchRequest) GetMethod() *StringMatch {
	if x != nil {
		return x.Method
	}
	return nil
}

func (x *HTTPMatchRequest) GetAuthority() *StringMatch {
	if x != nil {
		return x.Authority
	}
	return nil
}

func (x *HTTPMatchRequest) GetHeaders() map[string]*StringMatch {
	if x != nil {
		return x.Headers
	}
	return nil
}

func (x *HTTPMatchRequest) GetPort() uint32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *HTTPMatchRequest) GetSourceLabels() map[string]string {
	if x != nil {
		return x.SourceLabels
	}
	return nil
}

func (x *HTTPMatchRequest) GetGateways() []string {
	if x != nil {
		return x.Gateways
	}
	return nil
}

func (x *HTTPMatchRequest) GetQueryParams() map[string]*StringMatch {
	if x != nil {
		return x.QueryParams
	}
	return nil
}

func (x *HTTPMatchRequest) GetIgnoreUriCase() bool {
	if x != nil {
		return x.IgnoreUriCase
	}
	return false
}

func (x *HTTPMatchRequest) GetWithoutHeaders() map[string]*StringMatch {
	if x != nil {
		return x.WithoutHeaders
	}
	return nil
}

func (x *HTTPMatchRequest) GetSourceNamespace() string {
	if x != nil {
		return x.SourceNamespace
	}
	return ""
}

func (x *HTTPMatchRequest) GetStatPrefix() string {
	if x != nil {
		return x.StatPrefix
	}
	return ""
}

// Each routing rule is associated with one or more service versions (see
// glossary in beginning of document). Weights associated with the version
// determine the proportion of traffic it receives. For example, the
// following rule will route 25% of traffic for the "reviews" service to
// instances with the "v2" tag and the remaining traffic (i.e., 75%) to
// "v1".
//
// {{<tabset category-name="example">}}
// {{<tab name="v1alpha3" category-value="v1alpha3">}}
// ```yaml
// apiVersion: networking.istio.io/v1alpha3
// kind: VirtualService
// metadata:
//
//	name: reviews-route
//
// spec:
//
//	hosts:
//	- reviews.prod.svc.cluster.local
//	http:
//	- route:
//	  - destination:
//	      host: reviews.prod.svc.cluster.local
//	      subset: v2
//	    weight: 25
//	  - destination:
//	      host: reviews.prod.svc.cluster.local
//	      subset: v1
//	    weight: 75
//
// ```
// {{</tab>}}
//
// {{<tab name="v1beta1" category-value="v1beta1">}}
// ```yaml
// apiVersion: networking.istio.io/v1beta1
// kind: VirtualService
// metadata:
//
//	name: reviews-route
//
// spec:
//
//	hosts:
//	- reviews.prod.svc.cluster.local
//	http:
//	- route:
//	  - destination:
//	      host: reviews.prod.svc.cluster.local
//	      subset: v2
//	    weight: 25
//	  - destination:
//	      host: reviews.prod.svc.cluster.local
//	      subset: v1
//	    weight: 75
//
// ```
// {{</tab>}}
// {{</tabset>}}
//
// # And the associated DestinationRule
//
// {{<tabset category-name="example">}}
// {{<tab name="v1alpha3" category-value="v1alpha3">}}
// ```yaml
// apiVersion: networking.istio.io/v1alpha3
// kind: DestinationRule
// metadata:
//
//	name: reviews-destination
//
// spec:
//
//	host: reviews.prod.svc.cluster.local
//	subsets:
//	- name: v1
//	  labels:
//	    version: v1
//	- name: v2
//	  labels:
//	    version: v2
//
// ```
// {{</tab>}}
//
// {{<tab name="v1beta1" category-value="v1beta1">}}
// ```yaml
// apiVersion: networking.istio.io/v1beta1
// kind: DestinationRule
// metadata:
//
//	name: reviews-destination
//
// spec:
//
//	host: reviews.prod.svc.cluster.local
//	subsets:
//	- name: v1
//	  labels:
//	    version: v1
//	- name: v2
//	  labels:
//	    version: v2
//
// ```
// {{</tab>}}
// {{</tabset>}}
//
// Traffic can also be split across two entirely different services without
// having to define new subsets. For example, the following rule forwards 25% of
// traffic to reviews.com to dev.reviews.com
//
// {{<tabset category-name="example">}}
// {{<tab name="v1alpha3" category-value="v1alpha3">}}
// ```yaml
// apiVersion: networking.istio.io/v1alpha3
// kind: VirtualService
// metadata:
//
//	name: reviews-route-two-domains
//
// spec:
//
//	hosts:
//	- reviews.com
//	http:
//	- route:
//	  - destination:
//	      host: dev.reviews.com
//	    weight: 25
//	  - destination:
//	      host: reviews.com
//	    weight: 75
//
// ```
// {{</tab>}}
//
// {{<tab name="v1beta1" category-value="v1beta1">}}
// ```yaml
// apiVersion: networking.istio.io/v1beta1
// kind: VirtualService
// metadata:
//
//	name: reviews-route-two-domains
//
// spec:
//
//	hosts:
//	- reviews.com
//	http:
//	- route:
//	  - destination:
//	      host: dev.reviews.com
//	    weight: 25
//	  - destination:
//	      host: reviews.com
//	    weight: 75
//
// ```
// {{</tab>}}
// {{</tabset>}}
type HTTPRouteDestination struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Destination uniquely identifies the instances of a service
	// to which the request/connection should be forwarded to.
	Destination *Destination `protobuf:"bytes,1,opt,name=destination,proto3" json:"destination,omitempty"`
	// Weight specifies the relative proportion of traffic to be forwarded to the destination. A destination will receive `weight/(sum of all weights)` requests.
	// If there is only one destination in a rule, it will receive all traffic.
	// Otherwise, if weight is `0`, the destination will not receive any traffic.
	Weight int32 `protobuf:"varint,2,opt,name=weight,proto3" json:"weight,omitempty"`
	// Header manipulation rules
	Headers *Headers `protobuf:"bytes,7,opt,name=headers,proto3" json:"headers,omitempty"`
}

func (x *HTTPRouteDestination) Reset() {
	*x = HTTPRouteDestination{}
	if protoimpl.UnsafeEnabled {
		mi := &file_networking_v1beta1_virtual_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HTTPRouteDestination) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HTTPRouteDestination) ProtoMessage() {}

func (x *HTTPRouteDestination) ProtoReflect() protoreflect.Message {
	mi := &file_networking_v1beta1_virtual_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HTTPRouteDestination.ProtoReflect.Descriptor instead.
func (*HTTPRouteDestination) Descriptor() ([]byte, []int) {
	return file_networking_v1beta1_virtual_service_proto_rawDescGZIP(), []int{8}
}

func (x *HTTPRouteDestination) GetDestination() *Destination {
	if x != nil {
		return x.Destination
	}
	return nil
}

func (x *HTTPRouteDestination) GetWeight() int32 {
	if x != nil {
		return x.Weight
	}
	return 0
}

func (x *HTTPRouteDestination) GetHeaders() *Headers {
	if x != nil {
		return x.Headers
	}
	return nil
}

// L4 routing rule weighted destination.
type RouteDestination struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Destination uniquely identifies the instances of a service
	// to which the request/connection should be forwarded to.
	Destination *Destination `protobuf:"bytes,1,opt,name=destination,proto3" json:"destination,omitempty"`
	// Weight specifies the relative proportion of traffic to be forwarded to the destination. A destination will receive `weight/(sum of all weights)` requests.
	// If there is only one destination in a rule, it will receive all traffic.
	// Otherwise, if weight is `0`, the destination will not receive any traffic.
	Weight int32 `protobuf:"varint,2,opt,name=weight,proto3" json:"weight,omitempty"`
}

func (x *RouteDestination) Reset() {
	*x = RouteDestination{}
	if protoimpl.UnsafeEnabled {
		mi := &file_networking_v1beta1_virtual_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RouteDestination) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RouteDestination) ProtoMessage() {}

func (x *RouteDestination) ProtoReflect() protoreflect.Message {
	mi := &file_networking_v1beta1_virtual_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RouteDestination.ProtoReflect.Descriptor instead.
func (*RouteDestination) Descriptor() ([]byte, []int) {
	return file_networking_v1beta1_virtual_service_proto_rawDescGZIP(), []int{9}
}

func (x *RouteDestination) GetDestination() *Destination {
	if x != nil {
		return x.Destination
	}
	return nil
}

func (x *RouteDestination) GetWeight() int32 {
	if x != nil {
		return x.Weight
	}
	return 0
}

// L4 connection match attributes. Note that L4 connection matching support
// is incomplete.
type L4MatchAttributes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// IPv4 or IPv6 ip addresses of destination with optional subnet.  E.g.,
	// a.b.c.d/xx form or just a.b.c.d.
	DestinationSubnets []string `protobuf:"bytes,1,rep,name=destination_subnets,json=destinationSubnets,proto3" json:"destination_subnets,omitempty"`
	// Specifies the port on the host that is being addressed. Many services
	// only expose a single port or label ports with the protocols they support,
	// in these cases it is not required to explicitly select the port.
	Port uint32 `protobuf:"varint,2,opt,name=port,proto3" json:"port,omitempty"`
	// IPv4 or IPv6 ip address of source with optional subnet. E.g., a.b.c.d/xx
	// form or just a.b.c.d
	// $hide_from_docs
	SourceSubnet string `protobuf:"bytes,3,opt,name=source_subnet,json=sourceSubnet,proto3" json:"source_subnet,omitempty"`
	// One or more labels that constrain the applicability of a rule to
	// workloads with the given labels. If the VirtualService has a list of
	// gateways specified in the top-level `gateways` field, it should include the reserved gateway
	// `mesh` in order for this field to be applicable.
	SourceLabels map[string]string `protobuf:"bytes,4,rep,name=source_labels,json=sourceLabels,proto3" json:"source_labels,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// Names of gateways where the rule should be applied. Gateway names
	// in the top-level `gateways` field of the VirtualService (if any) are overridden. The gateway
	// match is independent of sourceLabels.
	Gateways []string `protobuf:"bytes,5,rep,name=gateways,proto3" json:"gateways,omitempty"`
	// Source namespace constraining the applicability of a rule to workloads in that namespace.
	// If the VirtualService has a list of gateways specified in the top-level `gateways` field,
	// it must include the reserved gateway `mesh` for this field to be applicable.
	SourceNamespace string `protobuf:"bytes,6,opt,name=source_namespace,json=sourceNamespace,proto3" json:"source_namespace,omitempty"`
}

func (x *L4MatchAttributes) Reset() {
	*x = L4MatchAttributes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_networking_v1beta1_virtual_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *L4MatchAttributes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*L4MatchAttributes) ProtoMessage() {}

func (x *L4MatchAttributes) ProtoReflect() protoreflect.Message {
	mi := &file_networking_v1beta1_virtual_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use L4MatchAttributes.ProtoReflect.Descriptor instead.
func (*L4MatchAttributes) Descriptor() ([]byte, []int) {
	return file_networking_v1beta1_virtual_service_proto_rawDescGZIP(), []int{10}
}

func (x *L4MatchAttributes) GetDestinationSubnets() []string {
	if x != nil {
		return x.DestinationSubnets
	}
	return nil
}

func (x *L4MatchAttributes) GetPort() uint32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *L4MatchAttributes) GetSourceSubnet() string {
	if x != nil {
		return x.SourceSubnet
	}
	return ""
}

func (x *L4MatchAttributes) GetSourceLabels() map[string]string {
	if x != nil {
		return x.SourceLabels
	}
	return nil
}

func (x *L4MatchAttributes) GetGateways() []string {
	if x != nil {
		return x.Gateways
	}
	return nil
}

func (x *L4MatchAttributes) GetSourceNamespace() string {
	if x != nil {
		return x.SourceNamespace
	}
	return ""
}

// TLS connection match attributes.
type TLSMatchAttributes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// SNI (server name indicator) to match on. Wildcard prefixes
	// can be used in the SNI value, e.g., *.com will match foo.example.com
	// as well as example.com. An SNI value must be a subset (i.e., fall
	// within the domain) of the corresponding virtual serivce's hosts.
	SniHosts []string `protobuf:"bytes,1,rep,name=sni_hosts,json=sniHosts,proto3" json:"sni_hosts,omitempty"`
	// IPv4 or IPv6 ip addresses of destination with optional subnet.  E.g.,
	// a.b.c.d/xx form or just a.b.c.d.
	DestinationSubnets []string `protobuf:"bytes,2,rep,name=destination_subnets,json=destinationSubnets,proto3" json:"destination_subnets,omitempty"`
	// Specifies the port on the host that is being addressed. Many services
	// only expose a single port or label ports with the protocols they
	// support, in these cases it is not required to explicitly select the
	// port.
	Port uint32 `protobuf:"varint,3,opt,name=port,proto3" json:"port,omitempty"`
	// One or more labels that constrain the applicability of a rule to
	// workloads with the given labels. If the VirtualService has a list of
	// gateways specified in the top-level `gateways` field, it should include the reserved gateway
	// `mesh` in order for this field to be applicable.
	SourceLabels map[string]string `protobuf:"bytes,5,rep,name=source_labels,json=sourceLabels,proto3" json:"source_labels,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// Names of gateways where the rule should be applied. Gateway names
	// in the top-level `gateways` field of the VirtualService (if any) are overridden. The gateway
	// match is independent of sourceLabels.
	Gateways []string `protobuf:"bytes,6,rep,name=gateways,proto3" json:"gateways,omitempty"`
	// Source namespace constraining the applicability of a rule to workloads in that namespace.
	// If the VirtualService has a list of gateways specified in the top-level `gateways` field,
	// it must include the reserved gateway `mesh` for this field to be applicable.
	SourceNamespace string `protobuf:"bytes,7,opt,name=source_namespace,json=sourceNamespace,proto3" json:"source_namespace,omitempty"`
}

func (x *TLSMatchAttributes) Reset() {
	*x = TLSMatchAttributes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_networking_v1beta1_virtual_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TLSMatchAttributes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TLSMatchAttributes) ProtoMessage() {}

func (x *TLSMatchAttributes) ProtoReflect() protoreflect.Message {
	mi := &file_networking_v1beta1_virtual_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TLSMatchAttributes.ProtoReflect.Descriptor instead.
func (*TLSMatchAttributes) Descriptor() ([]byte, []int) {
	return file_networking_v1beta1_virtual_service_proto_rawDescGZIP(), []int{11}
}

func (x *TLSMatchAttributes) GetSniHosts() []string {
	if x != nil {
		return x.SniHosts
	}
	return nil
}

func (x *TLSMatchAttributes) GetDestinationSubnets() []string {
	if x != nil {
		return x.DestinationSubnets
	}
	return nil
}

func (x *TLSMatchAttributes) GetPort() uint32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *TLSMatchAttributes) GetSourceLabels() map[string]string {
	if x != nil {
		return x.SourceLabels
	}
	return nil
}

func (x *TLSMatchAttributes) GetGateways() []string {
	if x != nil {
		return x.Gateways
	}
	return nil
}

func (x *TLSMatchAttributes) GetSourceNamespace() string {
	if x != nil {
		return x.SourceNamespace
	}
	return ""
}

// HTTPRedirect can be used to send a 301 redirect response to the caller,
// where the Authority/Host and the URI in the response can be swapped with
// the specified values. For example, the following rule redirects
// requests for /v1/getProductRatings API on the ratings service to
// /v1/bookRatings provided by the bookratings service.
//
// {{<tabset category-name="example">}}
// {{<tab name="v1alpha3" category-value="v1alpha3">}}
// ```yaml
// apiVersion: networking.istio.io/v1alpha3
// kind: VirtualService
// metadata:
//
//	name: ratings-route
//
// spec:
//
//	hosts:
//	- ratings.prod.svc.cluster.local
//	http:
//	- match:
//	  - uri:
//	      exact: /v1/getProductRatings
//	  redirect:
//	    uri: /v1/bookRatings
//	    authority: newratings.default.svc.cluster.local
//	...
//
// ```
// {{</tab>}}
//
// {{<tab name="v1beta1" category-value="v1beta1">}}
// ```yaml
// apiVersion: networking.istio.io/v1beta1
// kind: VirtualService
// metadata:
//
//	name: ratings-route
//
// spec:
//
//	hosts:
//	- ratings.prod.svc.cluster.local
//	http:
//	- match:
//	  - uri:
//	      exact: /v1/getProductRatings
//	  redirect:
//	    uri: /v1/bookRatings
//	    authority: newratings.default.svc.cluster.local
//	...
//
// ```
// {{</tab>}}
// {{</tabset>}}
type HTTPRedirect struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// On a redirect, overwrite the Path portion of the URL with this
	// value. Note that the entire path will be replaced, irrespective of the
	// request URI being matched as an exact path or prefix.
	Uri string `protobuf:"bytes,1,opt,name=uri,proto3" json:"uri,omitempty"`
	// On a redirect, overwrite the Authority/Host portion of the URL with
	// this value.
	Authority string `protobuf:"bytes,2,opt,name=authority,proto3" json:"authority,omitempty"`
	// Types that are assignable to RedirectPort:
	//
	//	*HTTPRedirect_Port
	//	*HTTPRedirect_DerivePort
	RedirectPort isHTTPRedirect_RedirectPort `protobuf_oneof:"redirect_port"`
	// On a redirect, overwrite the scheme portion of the URL with this value.
	// For example, `http` or `https`.
	// If unset, the original scheme will be used.
	// If `derivePort` is set to `FROM_PROTOCOL_DEFAULT`, this will impact the port used as well
	Scheme string `protobuf:"bytes,6,opt,name=scheme,proto3" json:"scheme,omitempty"`
	// On a redirect, Specifies the HTTP status code to use in the redirect
	// response. The default response code is MOVED_PERMANENTLY (301).
	RedirectCode uint32 `protobuf:"varint,3,opt,name=redirect_code,json=redirectCode,proto3" json:"redirect_code,omitempty"`
}

func (x *HTTPRedirect) Reset() {
	*x = HTTPRedirect{}
	if protoimpl.UnsafeEnabled {
		mi := &file_networking_v1beta1_virtual_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HTTPRedirect) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HTTPRedirect) ProtoMessage() {}

func (x *HTTPRedirect) ProtoReflect() protoreflect.Message {
	mi := &file_networking_v1beta1_virtual_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HTTPRedirect.ProtoReflect.Descriptor instead.
func (*HTTPRedirect) Descriptor() ([]byte, []int) {
	return file_networking_v1beta1_virtual_service_proto_rawDescGZIP(), []int{12}
}

func (x *HTTPRedirect) GetUri() string {
	if x != nil {
		return x.Uri
	}
	return ""
}

func (x *HTTPRedirect) GetAuthority() string {
	if x != nil {
		return x.Authority
	}
	return ""
}

func (m *HTTPRedirect) GetRedirectPort() isHTTPRedirect_RedirectPort {
	if m != nil {
		return m.RedirectPort
	}
	return nil
}

func (x *HTTPRedirect) GetPort() uint32 {
	if x, ok := x.GetRedirectPort().(*HTTPRedirect_Port); ok {
		return x.Port
	}
	return 0
}

func (x *HTTPRedirect) GetDerivePort() HTTPRedirect_RedirectPortSelection {
	if x, ok := x.GetRedirectPort().(*HTTPRedirect_DerivePort); ok {
		return x.DerivePort
	}
	return HTTPRedirect_FROM_PROTOCOL_DEFAULT
}

func (x *HTTPRedirect) GetScheme() string {
	if x != nil {
		return x.Scheme
	}
	return ""
}

func (x *HTTPRedirect) GetRedirectCode() uint32 {
	if x != nil {
		return x.RedirectCode
	}
	return 0
}

type isHTTPRedirect_RedirectPort interface {
	isHTTPRedirect_RedirectPort()
}

type HTTPRedirect_Port struct {
	// On a redirect, overwrite the port portion of the URL with this value.
	Port uint32 `protobuf:"varint,4,opt,name=port,proto3,oneof"`
}

type HTTPRedirect_DerivePort struct {
	// On a redirect, dynamically set the port:
	// * FROM_PROTOCOL_DEFAULT: automatically set to 80 for HTTP and 443 for HTTPS.
	// * FROM_REQUEST_PORT: automatically use the port of the request.
	DerivePort HTTPRedirect_RedirectPortSelection `protobuf:"varint,5,opt,name=derive_port,json=derivePort,proto3,enum=istio.networking.v1beta1.HTTPRedirect_RedirectPortSelection,oneof"`
}

func (*HTTPRedirect_Port) isHTTPRedirect_RedirectPort() {}

func (*HTTPRedirect_DerivePort) isHTTPRedirect_RedirectPort() {}

// HTTPDirectResponse can be used to send a fixed response to clients.
// For example, the following rule returns a fixed 503 status with a body
// to requests for /v1/getProductRatings API.
//
// {{<tabset category-name="example">}}
// {{<tab name="v1alpha3" category-value="v1alpha3">}}
// ```yaml
// apiVersion: networking.istio.io/v1alpha3
// kind: VirtualService
// metadata:
//
//	name: ratings-route
//
// spec:
//
//	hosts:
//	- ratings.prod.svc.cluster.local
//	http:
//	- match:
//	  - uri:
//	      exact: /v1/getProductRatings
//	  directResponse:
//	    status: 503
//	    body:
//	      string: "unknown error"
//	...
//
// ```
// {{</tab>}}
//
// {{<tab name="v1beta1" category-value="v1beta1">}}
// ```yaml
// apiVersion: networking.istio.io/v1beta1
// kind: VirtualService
// metadata:
//
//	name: ratings-route
//
// spec:
//
//	hosts:
//	- ratings.prod.svc.cluster.local
//	http:
//	- match:
//	  - uri:
//	      exact: /v1/getProductRatings
//	  directResponse:
//	    status: 503
//	    body:
//	      string: "unknown error"
//	...
//
// ```
// {{</tab>}}
// {{</tabset>}}
//
// It is also possible to specify a binary response body.
// This is mostly useful for non text-based protocols such as gRPC.
//
// {{<tabset category-name="example">}}
// {{<tab name="v1alpha3" category-value="v1alpha3">}}
// ```yaml
// apiVersion: networking.istio.io/v1alpha3
// kind: VirtualService
// metadata:
//
//	name: ratings-route
//
// spec:
//
//	hosts:
//	- ratings.prod.svc.cluster.local
//	http:
//	- match:
//	  - uri:
//	      exact: /v1/getProductRatings
//	  directResponse:
//	    status: 503
//	    body:
//	      bytes: "dW5rbm93biBlcnJvcg==" # "unknown error" in base64
//	...
//
// ```
// {{</tab>}}
//
// {{<tab name="v1beta1" category-value="v1beta1">}}
// ```yaml
// apiVersion: networking.istio.io/v1beta1
// kind: VirtualService
// metadata:
//
//	name: ratings-route
//
// spec:
//
//	hosts:
//	- ratings.prod.svc.cluster.local
//	http:
//	- match:
//	  - uri:
//	      exact: /v1/getProductRatings
//	  directResponse:
//	    status: 503
//	    body:
//	      bytes: "dW5rbm93biBlcnJvcg==" # "unknown error" in base64
//	...
//
// ```
// {{</tab>}}
// {{</tabset>}}
//
// It is good practice to add headers in the HTTPRoute
// as well as the direct_response, for example to specify
// the returned Content-Type.
//
// {{<tabset category-name="example">}}
// {{<tab name="v1alpha3" category-value="v1alpha3">}}
// ```yaml
// apiVersion: networking.istio.io/v1alpha3
// kind: VirtualService
// metadata:
//
//	name: ratings-route
//
// spec:
//
//	hosts:
//	- ratings.prod.svc.cluster.local
//	http:
//	- match:
//	  - uri:
//	      exact: /v1/getProductRatings
//	  directResponse:
//	    status: 503
//	    body:
//	      string: "{\"error\": \"unknown error\"}"
//	  headers:
//	    response:
//	      set:
//	        content-type: "appliation/json"
//	...
//
// ```
// {{</tab>}}
//
// {{<tab name="v1beta1" category-value="v1beta1">}}
// ```yaml
// apiVersion: networking.istio.io/v1beta1
// kind: VirtualService
// metadata:
//
//	name: ratings-route
//
// spec:
//
//	hosts:
//	- ratings.prod.svc.cluster.local
//	http:
//	- match:
//	  - uri:
//	      exact: /v1/getProductRatings
//	  directResponse:
//	    status: 503
//	    body:
//	      string: "{\"error\": \"unknown error\"}"
//	  headers:
//	    response:
//	      set:
//	        content-type: "text/plain"
//	...
//
// ```
// {{</tab>}}
// {{</tabset>}}
type HTTPDirectResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Specifies the HTTP response status to be returned.
	Status uint32 `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	// Specifies the content of the response body. If this setting is omitted,
	// no body is included in the generated response.
	Body *HTTPBody `protobuf:"bytes,2,opt,name=body,proto3" json:"body,omitempty"`
}

func (x *HTTPDirectResponse) Reset() {
	*x = HTTPDirectResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_networking_v1beta1_virtual_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HTTPDirectResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HTTPDirectResponse) ProtoMessage() {}

func (x *HTTPDirectResponse) ProtoReflect() protoreflect.Message {
	mi := &file_networking_v1beta1_virtual_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HTTPDirectResponse.ProtoReflect.Descriptor instead.
func (*HTTPDirectResponse) Descriptor() ([]byte, []int) {
	return file_networking_v1beta1_virtual_service_proto_rawDescGZIP(), []int{13}
}

func (x *HTTPDirectResponse) GetStatus() uint32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *HTTPDirectResponse) GetBody() *HTTPBody {
	if x != nil {
		return x.Body
	}
	return nil
}

type HTTPBody struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Specifier:
	//
	//	*HTTPBody_String_
	//	*HTTPBody_Bytes
	Specifier isHTTPBody_Specifier `protobuf_oneof:"specifier"`
}

func (x *HTTPBody) Reset() {
	*x = HTTPBody{}
	if protoimpl.UnsafeEnabled {
		mi := &file_networking_v1beta1_virtual_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HTTPBody) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HTTPBody) ProtoMessage() {}

func (x *HTTPBody) ProtoReflect() protoreflect.Message {
	mi := &file_networking_v1beta1_virtual_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HTTPBody.ProtoReflect.Descriptor instead.
func (*HTTPBody) Descriptor() ([]byte, []int) {
	return file_networking_v1beta1_virtual_service_proto_rawDescGZIP(), []int{14}
}

func (m *HTTPBody) GetSpecifier() isHTTPBody_Specifier {
	if m != nil {
		return m.Specifier
	}
	return nil
}

func (x *HTTPBody) GetString_() string {
	if x, ok := x.GetSpecifier().(*HTTPBody_String_); ok {
		return x.String_
	}
	return ""
}

func (x *HTTPBody) GetBytes() []byte {
	if x, ok := x.GetSpecifier().(*HTTPBody_Bytes); ok {
		return x.Bytes
	}
	return nil
}

type isHTTPBody_Specifier interface {
	isHTTPBody_Specifier()
}

type HTTPBody_String_ struct {
	// response body as a string
	String_ string `protobuf:"bytes,1,opt,name=string,proto3,oneof"`
}

type HTTPBody_Bytes struct {
	// response body as base64 encoded bytes.
	Bytes []byte `protobuf:"bytes,2,opt,name=bytes,proto3,oneof"`
}

func (*HTTPBody_String_) isHTTPBody_Specifier() {}

func (*HTTPBody_Bytes) isHTTPBody_Specifier() {}

// HTTPRewrite can be used to rewrite specific parts of a HTTP request
// before forwarding the request to the destination. Rewrite primitive can
// be used only with HTTPRouteDestination. The following example
// demonstrates how to rewrite the URL prefix for api call (/ratings) to
// ratings service before making the actual API call.
//
// {{<tabset category-name="example">}}
// {{<tab name="v1alpha3" category-value="v1alpha3">}}
// ```yaml
// apiVersion: networking.istio.io/v1alpha3
// kind: VirtualService
// metadata:
//
//	name: ratings-route
//
// spec:
//
//	hosts:
//	- ratings.prod.svc.cluster.local
//	http:
//	- match:
//	  - uri:
//	      prefix: /ratings
//	  rewrite:
//	    uri: /v1/bookRatings
//	  route:
//	  - destination:
//	      host: ratings.prod.svc.cluster.local
//	      subset: v1
//
// ```
// {{</tab>}}
//
// {{<tab name="v1beta1" category-value="v1beta1">}}
// ```yaml
// apiVersion: networking.istio.io/v1beta1
// kind: VirtualService
// metadata:
//
//	name: ratings-route
//
// spec:
//
//	hosts:
//	- ratings.prod.svc.cluster.local
//	http:
//	- match:
//	  - uri:
//	      prefix: /ratings
//	  rewrite:
//	    uri: /v1/bookRatings
//	  route:
//	  - destination:
//	      host: ratings.prod.svc.cluster.local
//	      subset: v1
//
// ```
// {{</tab>}}
// {{</tabset>}}
type HTTPRewrite struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rewrite the path (or the prefix) portion of the URI with this
	// value. If the original URI was matched based on prefix, the value
	// provided in this field will replace the corresponding matched prefix.
	Uri string `protobuf:"bytes,1,opt,name=uri,proto3" json:"uri,omitempty"`
	// rewrite the Authority/Host header with this value.
	Authority string `protobuf:"bytes,2,opt,name=authority,proto3" json:"authority,omitempty"`
}

func (x *HTTPRewrite) Reset() {
	*x = HTTPRewrite{}
	if protoimpl.UnsafeEnabled {
		mi := &file_networking_v1beta1_virtual_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HTTPRewrite) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HTTPRewrite) ProtoMessage() {}

func (x *HTTPRewrite) ProtoReflect() protoreflect.Message {
	mi := &file_networking_v1beta1_virtual_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HTTPRewrite.ProtoReflect.Descriptor instead.
func (*HTTPRewrite) Descriptor() ([]byte, []int) {
	return file_networking_v1beta1_virtual_service_proto_rawDescGZIP(), []int{15}
}

func (x *HTTPRewrite) GetUri() string {
	if x != nil {
		return x.Uri
	}
	return ""
}

func (x *HTTPRewrite) GetAuthority() string {
	if x != nil {
		return x.Authority
	}
	return ""
}

// Describes how to match a given string in HTTP headers. Match is
// case-sensitive.
type StringMatch struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to MatchType:
	//
	//	*StringMatch_Exact
	//	*StringMatch_Prefix
	//	*StringMatch_Regex
	MatchType isStringMatch_MatchType `protobuf_oneof:"match_type"`
}

func (x *StringMatch) Reset() {
	*x = StringMatch{}
	if protoimpl.UnsafeEnabled {
		mi := &file_networking_v1beta1_virtual_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringMatch) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringMatch) ProtoMessage() {}

func (x *StringMatch) ProtoReflect() protoreflect.Message {
	mi := &file_networking_v1beta1_virtual_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringMatch.ProtoReflect.Descriptor instead.
func (*StringMatch) Descriptor() ([]byte, []int) {
	return file_networking_v1beta1_virtual_service_proto_rawDescGZIP(), []int{16}
}

func (m *StringMatch) GetMatchType() isStringMatch_MatchType {
	if m != nil {
		return m.MatchType
	}
	return nil
}

func (x *StringMatch) GetExact() string {
	if x, ok := x.GetMatchType().(*StringMatch_Exact); ok {
		return x.Exact
	}
	return ""
}

func (x *StringMatch) GetPrefix() string {
	if x, ok := x.GetMatchType().(*StringMatch_Prefix); ok {
		return x.Prefix
	}
	return ""
}

func (x *StringMatch) GetRegex() string {
	if x, ok := x.GetMatchType().(*StringMatch_Regex); ok {
		return x.Regex
	}
	return ""
}

type isStringMatch_MatchType interface {
	isStringMatch_MatchType()
}

type StringMatch_Exact struct {
	// exact string match
	Exact string `protobuf:"bytes,1,opt,name=exact,proto3,oneof"`
}

type StringMatch_Prefix struct {
	// prefix-based match
	Prefix string `protobuf:"bytes,2,opt,name=prefix,proto3,oneof"`
}

type StringMatch_Regex struct {
	// RE2 style regex-based match (https://github.com/google/re2/wiki/Syntax).
	Regex string `protobuf:"bytes,3,opt,name=regex,proto3,oneof"`
}

func (*StringMatch_Exact) isStringMatch_MatchType() {}

func (*StringMatch_Prefix) isStringMatch_MatchType() {}

func (*StringMatch_Regex) isStringMatch_MatchType() {}

// Describes the retry policy to use when a HTTP request fails. For
// example, the following rule sets the maximum number of retries to 3 when
// calling ratings:v1 service, with a 2s timeout per retry attempt.
// A retry will be attempted if there is a connect-failure, refused_stream
// or when the upstream server responds with Service Unavailable(503).
//
// {{<tabset category-name="example">}}
// {{<tab name="v1alpha3" category-value="v1alpha3">}}
// ```yaml
// apiVersion: networking.istio.io/v1alpha3
// kind: VirtualService
// metadata:
//
//	name: ratings-route
//
// spec:
//
//	hosts:
//	- ratings.prod.svc.cluster.local
//	http:
//	- route:
//	  - destination:
//	      host: ratings.prod.svc.cluster.local
//	      subset: v1
//	  retries:
//	    attempts: 3
//	    perTryTimeout: 2s
//	    retryOn: connect-failure,refused-stream,503
//
// ```
// {{</tab>}}
//
// {{<tab name="v1beta1" category-value="v1beta1">}}
// ```yaml
// apiVersion: networking.istio.io/v1beta1
// kind: VirtualService
// metadata:
//
//	name: ratings-route
//
// spec:
//
//	hosts:
//	- ratings.prod.svc.cluster.local
//	http:
//	- route:
//	  - destination:
//	      host: ratings.prod.svc.cluster.local
//	      subset: v1
//	  retries:
//	    attempts: 3
//	    perTryTimeout: 2s
//	    retryOn: gateway-error,connect-failure,refused-stream
//
// ```
// {{</tab>}}
// {{</tabset>}}
type HTTPRetry struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Number of retries to be allowed for a given request. The interval
	// between retries will be determined automatically (25ms+). When request
	// `timeout` of the [HTTP route](https://istio.io/docs/reference/config/networking/virtual-service/#HTTPRoute)
	// or `per_try_timeout` is configured, the actual number of retries attempted also depends on
	// the specified request `timeout` and `per_try_timeout` values.
	Attempts int32 `protobuf:"varint,1,opt,name=attempts,proto3" json:"attempts,omitempty"`
	// Timeout per attempt for a given request, including the initial call and any retries. Format: 1h/1m/1s/1ms. MUST BE >=1ms.
	// Default is same value as request
	// `timeout` of the [HTTP route](https://istio.io/docs/reference/config/networking/virtual-service/#HTTPRoute),
	// which means no timeout.
	PerTryTimeout *duration.Duration `protobuf:"bytes,2,opt,name=per_try_timeout,json=perTryTimeout,proto3" json:"per_try_timeout,omitempty"`
	// Specifies the conditions under which retry takes place.
	// One or more policies can be specified using a ‘,’ delimited list.
	// If `retry_on` specifies a valid HTTP status, it will be added to retriable_status_codes retry policy.
	// See the [retry policies](https://www.envoyproxy.io/docs/envoy/latest/configuration/http/http_filters/router_filter#x-envoy-retry-on)
	// and [gRPC retry policies](https://www.envoyproxy.io/docs/envoy/latest/configuration/http/http_filters/router_filter#x-envoy-retry-grpc-on) for more details.
	RetryOn string `protobuf:"bytes,3,opt,name=retry_on,json=retryOn,proto3" json:"retry_on,omitempty"`
	// Flag to specify whether the retries should retry to other localities.
	// See the [retry plugin configuration](https://www.envoyproxy.io/docs/envoy/latest/intro/arch_overview/http/http_connection_management#retry-plugin-configuration) for more details.
	RetryRemoteLocalities *wrappers.BoolValue `protobuf:"bytes,4,opt,name=retry_remote_localities,json=retryRemoteLocalities,proto3" json:"retry_remote_localities,omitempty"`
}

func (x *HTTPRetry) Reset() {
	*x = HTTPRetry{}
	if protoimpl.UnsafeEnabled {
		mi := &file_networking_v1beta1_virtual_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HTTPRetry) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HTTPRetry) ProtoMessage() {}

func (x *HTTPRetry) ProtoReflect() protoreflect.Message {
	mi := &file_networking_v1beta1_virtual_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HTTPRetry.ProtoReflect.Descriptor instead.
func (*HTTPRetry) Descriptor() ([]byte, []int) {
	return file_networking_v1beta1_virtual_service_proto_rawDescGZIP(), []int{17}
}

func (x *HTTPRetry) GetAttempts() int32 {
	if x != nil {
		return x.Attempts
	}
	return 0
}

func (x *HTTPRetry) GetPerTryTimeout() *duration.Duration {
	if x != nil {
		return x.PerTryTimeout
	}
	return nil
}

func (x *HTTPRetry) GetRetryOn() string {
	if x != nil {
		return x.RetryOn
	}
	return ""
}

func (x *HTTPRetry) GetRetryRemoteLocalities() *wrappers.BoolValue {
	if x != nil {
		return x.RetryRemoteLocalities
	}
	return nil
}

// Describes the Cross-Origin Resource Sharing (CORS) policy, for a given
// service. Refer to [CORS](https://developer.mozilla.org/en-US/docs/Web/HTTP/Access_control_CORS)
// for further details about cross origin resource sharing. For example,
// the following rule restricts cross origin requests to those originating
// from example.com domain using HTTP POST/GET, and sets the
// `Access-Control-Allow-Credentials` header to false. In addition, it only
// exposes `X-Foo-bar` header and sets an expiry period of 1 day.
//
// {{<tabset category-name="example">}}
// {{<tab name="v1alpha3" category-value="v1alpha3">}}
// ```yaml
// apiVersion: networking.istio.io/v1alpha3
// kind: VirtualService
// metadata:
//
//	name: ratings-route
//
// spec:
//
//	hosts:
//	- ratings.prod.svc.cluster.local
//	http:
//	- route:
//	  - destination:
//	      host: ratings.prod.svc.cluster.local
//	      subset: v1
//	  corsPolicy:
//	    allowOrigins:
//	    - exact: https://example.com
//	    allowMethods:
//	    - POST
//	    - GET
//	    allowCredentials: false
//	    allowHeaders:
//	    - X-Foo-Bar
//	    maxAge: "24h"
//
// ```
// {{</tab>}}
//
// {{<tab name="v1beta1" category-value="v1beta1">}}
// ```yaml
// apiVersion: networking.istio.io/v1beta1
// kind: VirtualService
// metadata:
//
//	name: ratings-route
//
// spec:
//
//	hosts:
//	- ratings.prod.svc.cluster.local
//	http:
//	- route:
//	  - destination:
//	      host: ratings.prod.svc.cluster.local
//	      subset: v1
//	  corsPolicy:
//	    allowOrigins:
//	    - exact: https://example.com
//	    allowMethods:
//	    - POST
//	    - GET
//	    allowCredentials: false
//	    allowHeaders:
//	    - X-Foo-Bar
//	    maxAge: "24h"
//
// ```
// {{</tab>}}
// {{</tabset>}}
type CorsPolicy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The list of origins that are allowed to perform CORS requests. The
	// content will be serialized into the Access-Control-Allow-Origin
	// header. Wildcard * will allow all origins.
	// $hide_from_docs
	//
	// Deprecated: Do not use.
	AllowOrigin []string `protobuf:"bytes,1,rep,name=allow_origin,json=allowOrigin,proto3" json:"allow_origin,omitempty"`
	// String patterns that match allowed origins.
	// An origin is allowed if any of the string matchers match.
	// If a match is found, then the outgoing Access-Control-Allow-Origin would be set to the origin as provided by the client.
	AllowOrigins []*StringMatch `protobuf:"bytes,7,rep,name=allow_origins,json=allowOrigins,proto3" json:"allow_origins,omitempty"`
	// List of HTTP methods allowed to access the resource. The content will
	// be serialized into the Access-Control-Allow-Methods header.
	AllowMethods []string `protobuf:"bytes,2,rep,name=allow_methods,json=allowMethods,proto3" json:"allow_methods,omitempty"`
	// List of HTTP headers that can be used when requesting the
	// resource. Serialized to Access-Control-Allow-Headers header.
	AllowHeaders []string `protobuf:"bytes,3,rep,name=allow_headers,json=allowHeaders,proto3" json:"allow_headers,omitempty"`
	// A list of HTTP headers that the browsers are allowed to
	// access. Serialized into Access-Control-Expose-Headers header.
	ExposeHeaders []string `protobuf:"bytes,4,rep,name=expose_headers,json=exposeHeaders,proto3" json:"expose_headers,omitempty"`
	// Specifies how long the results of a preflight request can be
	// cached. Translates to the `Access-Control-Max-Age` header.
	MaxAge *duration.Duration `protobuf:"bytes,5,opt,name=max_age,json=maxAge,proto3" json:"max_age,omitempty"`
	// Indicates whether the caller is allowed to send the actual request
	// (not the preflight) using credentials. Translates to
	// `Access-Control-Allow-Credentials` header.
	AllowCredentials *wrappers.BoolValue `protobuf:"bytes,6,opt,name=allow_credentials,json=allowCredentials,proto3" json:"allow_credentials,omitempty"`
}

func (x *CorsPolicy) Reset() {
	*x = CorsPolicy{}
	if protoimpl.UnsafeEnabled {
		mi := &file_networking_v1beta1_virtual_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CorsPolicy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CorsPolicy) ProtoMessage() {}

func (x *CorsPolicy) ProtoReflect() protoreflect.Message {
	mi := &file_networking_v1beta1_virtual_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CorsPolicy.ProtoReflect.Descriptor instead.
func (*CorsPolicy) Descriptor() ([]byte, []int) {
	return file_networking_v1beta1_virtual_service_proto_rawDescGZIP(), []int{18}
}

// Deprecated: Do not use.
func (x *CorsPolicy) GetAllowOrigin() []string {
	if x != nil {
		return x.AllowOrigin
	}
	return nil
}

func (x *CorsPolicy) GetAllowOrigins() []*StringMatch {
	if x != nil {
		return x.AllowOrigins
	}
	return nil
}

func (x *CorsPolicy) GetAllowMethods() []string {
	if x != nil {
		return x.AllowMethods
	}
	return nil
}

func (x *CorsPolicy) GetAllowHeaders() []string {
	if x != nil {
		return x.AllowHeaders
	}
	return nil
}

func (x *CorsPolicy) GetExposeHeaders() []string {
	if x != nil {
		return x.ExposeHeaders
	}
	return nil
}

func (x *CorsPolicy) GetMaxAge() *duration.Duration {
	if x != nil {
		return x.MaxAge
	}
	return nil
}

func (x *CorsPolicy) GetAllowCredentials() *wrappers.BoolValue {
	if x != nil {
		return x.AllowCredentials
	}
	return nil
}

// HTTPFaultInjection can be used to specify one or more faults to inject
// while forwarding HTTP requests to the destination specified in a route.
// Fault specification is part of a VirtualService rule. Faults include
// aborting the Http request from downstream service, and/or delaying
// proxying of requests. A fault rule MUST HAVE delay or abort or both.
//
// *Note:* Delay and abort faults are independent of one another, even if
// both are specified simultaneously.
type HTTPFaultInjection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Delay requests before forwarding, emulating various failures such as
	// network issues, overloaded upstream service, etc.
	Delay *HTTPFaultInjection_Delay `protobuf:"bytes,1,opt,name=delay,proto3" json:"delay,omitempty"`
	// Abort Http request attempts and return error codes back to downstream
	// service, giving the impression that the upstream service is faulty.
	Abort *HTTPFaultInjection_Abort `protobuf:"bytes,2,opt,name=abort,proto3" json:"abort,omitempty"`
}

func (x *HTTPFaultInjection) Reset() {
	*x = HTTPFaultInjection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_networking_v1beta1_virtual_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HTTPFaultInjection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HTTPFaultInjection) ProtoMessage() {}

func (x *HTTPFaultInjection) ProtoReflect() protoreflect.Message {
	mi := &file_networking_v1beta1_virtual_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HTTPFaultInjection.ProtoReflect.Descriptor instead.
func (*HTTPFaultInjection) Descriptor() ([]byte, []int) {
	return file_networking_v1beta1_virtual_service_proto_rawDescGZIP(), []int{19}
}

func (x *HTTPFaultInjection) GetDelay() *HTTPFaultInjection_Delay {
	if x != nil {
		return x.Delay
	}
	return nil
}

func (x *HTTPFaultInjection) GetAbort() *HTTPFaultInjection_Abort {
	if x != nil {
		return x.Abort
	}
	return nil
}

// PortSelector specifies the number of a port to be used for
// matching or selection for final routing.
type PortSelector struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Valid port number
	Number uint32 `protobuf:"varint,1,opt,name=number,proto3" json:"number,omitempty"`
}

func (x *PortSelector) Reset() {
	*x = PortSelector{}
	if protoimpl.UnsafeEnabled {
		mi := &file_networking_v1beta1_virtual_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PortSelector) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PortSelector) ProtoMessage() {}

func (x *PortSelector) ProtoReflect() protoreflect.Message {
	mi := &file_networking_v1beta1_virtual_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PortSelector.ProtoReflect.Descriptor instead.
func (*PortSelector) Descriptor() ([]byte, []int) {
	return file_networking_v1beta1_virtual_service_proto_rawDescGZIP(), []int{20}
}

func (x *PortSelector) GetNumber() uint32 {
	if x != nil {
		return x.Number
	}
	return 0
}

// Percent specifies a percentage in the range of [0.0, 100.0].
type Percent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value float64 `protobuf:"fixed64,1,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *Percent) Reset() {
	*x = Percent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_networking_v1beta1_virtual_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Percent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Percent) ProtoMessage() {}

func (x *Percent) ProtoReflect() protoreflect.Message {
	mi := &file_networking_v1beta1_virtual_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Percent.ProtoReflect.Descriptor instead.
func (*Percent) Descriptor() ([]byte, []int) {
	return file_networking_v1beta1_virtual_service_proto_rawDescGZIP(), []int{21}
}

func (x *Percent) GetValue() float64 {
	if x != nil {
		return x.Value
	}
	return 0
}

// HeaderOperations Describes the header manipulations to apply
type Headers_HeaderOperations struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Overwrite the headers specified by key with the given values
	Set map[string]string `protobuf:"bytes,1,rep,name=set,proto3" json:"set,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// Append the given values to the headers specified by keys
	// (will create a comma-separated list of values)
	Add map[string]string `protobuf:"bytes,2,rep,name=add,proto3" json:"add,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// Remove the specified headers
	Remove []string `protobuf:"bytes,3,rep,name=remove,proto3" json:"remove,omitempty"`
}

func (x *Headers_HeaderOperations) Reset() {
	*x = Headers_HeaderOperations{}
	if protoimpl.UnsafeEnabled {
		mi := &file_networking_v1beta1_virtual_service_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Headers_HeaderOperations) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Headers_HeaderOperations) ProtoMessage() {}

func (x *Headers_HeaderOperations) ProtoReflect() protoreflect.Message {
	mi := &file_networking_v1beta1_virtual_service_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Headers_HeaderOperations.ProtoReflect.Descriptor instead.
func (*Headers_HeaderOperations) Descriptor() ([]byte, []int) {
	return file_networking_v1beta1_virtual_service_proto_rawDescGZIP(), []int{4, 0}
}

func (x *Headers_HeaderOperations) GetSet() map[string]string {
	if x != nil {
		return x.Set
	}
	return nil
}

func (x *Headers_HeaderOperations) GetAdd() map[string]string {
	if x != nil {
		return x.Add
	}
	return nil
}

func (x *Headers_HeaderOperations) GetRemove() []string {
	if x != nil {
		return x.Remove
	}
	return nil
}

// Delay specification is used to inject latency into the request
// forwarding path. The following example will introduce a 5 second delay
// in 1 out of every 1000 requests to the "v1" version of the "reviews"
// service from all pods with label env: prod
//
// {{<tabset category-name="example">}}
// {{<tab name="v1alpha3" category-value="v1alpha3">}}
// ```yaml
// apiVersion: networking.istio.io/v1alpha3
// kind: VirtualService
// metadata:
//
//	name: reviews-route
//
// spec:
//
//	hosts:
//	- reviews.prod.svc.cluster.local
//	http:
//	- match:
//	  - sourceLabels:
//	      env: prod
//	  route:
//	  - destination:
//	      host: reviews.prod.svc.cluster.local
//	      subset: v1
//	  fault:
//	    delay:
//	      percentage:
//	        value: 0.1
//	      fixedDelay: 5s
//
// ```
// {{</tab>}}
//
// {{<tab name="v1beta1" category-value="v1beta1">}}
// ```yaml
// apiVersion: networking.istio.io/v1beta1
// kind: VirtualService
// metadata:
//
//	name: reviews-route
//
// spec:
//
//	hosts:
//	- reviews.prod.svc.cluster.local
//	http:
//	- match:
//	  - sourceLabels:
//	      env: prod
//	  route:
//	  - destination:
//	      host: reviews.prod.svc.cluster.local
//	      subset: v1
//	  fault:
//	    delay:
//	      percentage:
//	        value: 0.1
//	      fixedDelay: 5s
//
// ```
// {{</tab>}}
// {{</tabset>}}
//
// The _fixedDelay_ field is used to indicate the amount of delay in seconds.
// The optional _percentage_ field can be used to only delay a certain
// percentage of requests. If left unspecified, no request will be delayed.
type HTTPFaultInjection_Delay struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Percentage of requests on which the delay will be injected (0-100).
	// Use of integer `percent` value is deprecated. Use the double `percentage`
	// field instead.
	//
	// Deprecated: Do not use.
	Percent int32 `protobuf:"varint,1,opt,name=percent,proto3" json:"percent,omitempty"`
	// Types that are assignable to HttpDelayType:
	//
	//	*HTTPFaultInjection_Delay_FixedDelay
	//	*HTTPFaultInjection_Delay_ExponentialDelay
	HttpDelayType isHTTPFaultInjection_Delay_HttpDelayType `protobuf_oneof:"http_delay_type"`
	// Percentage of requests on which the delay will be injected.
	// If left unspecified, no request will be delayed.
	Percentage *Percent `protobuf:"bytes,5,opt,name=percentage,proto3" json:"percentage,omitempty"`
}

func (x *HTTPFaultInjection_Delay) Reset() {
	*x = HTTPFaultInjection_Delay{}
	if protoimpl.UnsafeEnabled {
		mi := &file_networking_v1beta1_virtual_service_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HTTPFaultInjection_Delay) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HTTPFaultInjection_Delay) ProtoMessage() {}

func (x *HTTPFaultInjection_Delay) ProtoReflect() protoreflect.Message {
	mi := &file_networking_v1beta1_virtual_service_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HTTPFaultInjection_Delay.ProtoReflect.Descriptor instead.
func (*HTTPFaultInjection_Delay) Descriptor() ([]byte, []int) {
	return file_networking_v1beta1_virtual_service_proto_rawDescGZIP(), []int{19, 0}
}

// Deprecated: Do not use.
func (x *HTTPFaultInjection_Delay) GetPercent() int32 {
	if x != nil {
		return x.Percent
	}
	return 0
}

func (m *HTTPFaultInjection_Delay) GetHttpDelayType() isHTTPFaultInjection_Delay_HttpDelayType {
	if m != nil {
		return m.HttpDelayType
	}
	return nil
}

func (x *HTTPFaultInjection_Delay) GetFixedDelay() *duration.Duration {
	if x, ok := x.GetHttpDelayType().(*HTTPFaultInjection_Delay_FixedDelay); ok {
		return x.FixedDelay
	}
	return nil
}

func (x *HTTPFaultInjection_Delay) GetExponentialDelay() *duration.Duration {
	if x, ok := x.GetHttpDelayType().(*HTTPFaultInjection_Delay_ExponentialDelay); ok {
		return x.ExponentialDelay
	}
	return nil
}

func (x *HTTPFaultInjection_Delay) GetPercentage() *Percent {
	if x != nil {
		return x.Percentage
	}
	return nil
}

type isHTTPFaultInjection_Delay_HttpDelayType interface {
	isHTTPFaultInjection_Delay_HttpDelayType()
}

type HTTPFaultInjection_Delay_FixedDelay struct {
	// Add a fixed delay before forwarding the request. Format:
	// 1h/1m/1s/1ms. MUST be >=1ms.
	FixedDelay *duration.Duration `protobuf:"bytes,2,opt,name=fixed_delay,json=fixedDelay,proto3,oneof"`
}

type HTTPFaultInjection_Delay_ExponentialDelay struct {
	// $hide_from_docs
	ExponentialDelay *duration.Duration `protobuf:"bytes,3,opt,name=exponential_delay,json=exponentialDelay,proto3,oneof"`
}

func (*HTTPFaultInjection_Delay_FixedDelay) isHTTPFaultInjection_Delay_HttpDelayType() {}

func (*HTTPFaultInjection_Delay_ExponentialDelay) isHTTPFaultInjection_Delay_HttpDelayType() {}

// Abort specification is used to prematurely abort a request with a
// pre-specified error code. The following example will return an HTTP 400
// error code for 1 out of every 1000 requests to the "ratings" service "v1".
//
// {{<tabset category-name="example">}}
// {{<tab name="v1alpha3" category-value="v1alpha3">}}
// ```yaml
// apiVersion: networking.istio.io/v1alpha3
// kind: VirtualService
// metadata:
//
//	name: ratings-route
//
// spec:
//
//	hosts:
//	- ratings.prod.svc.cluster.local
//	http:
//	- route:
//	  - destination:
//	      host: ratings.prod.svc.cluster.local
//	      subset: v1
//	  fault:
//	    abort:
//	      percentage:
//	        value: 0.1
//	      httpStatus: 400
//
// ```
// {{</tab>}}
//
// {{<tab name="v1beta1" category-value="v1beta1">}}
// ```yaml
// apiVersion: networking.istio.io/v1beta1
// kind: VirtualService
// metadata:
//
//	name: ratings-route
//
// spec:
//
//	hosts:
//	- ratings.prod.svc.cluster.local
//	http:
//	- route:
//	  - destination:
//	      host: ratings.prod.svc.cluster.local
//	      subset: v1
//	  fault:
//	    abort:
//	      percentage:
//	        value: 0.1
//	      httpStatus: 400
//
// ```
// {{</tab>}}
// {{</tabset>}}
//
// The _httpStatus_ field is used to indicate the HTTP status code to
// return to the caller. The optional _percentage_ field can be used to only
// abort a certain percentage of requests. If not specified, no request will be
// aborted.
type HTTPFaultInjection_Abort struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to ErrorType:
	//
	//	*HTTPFaultInjection_Abort_HttpStatus
	//	*HTTPFaultInjection_Abort_GrpcStatus
	//	*HTTPFaultInjection_Abort_Http2Error
	ErrorType isHTTPFaultInjection_Abort_ErrorType `protobuf_oneof:"error_type"`
	// Percentage of requests to be aborted with the error code provided.
	// If not specified, no request will be aborted.
	Percentage *Percent `protobuf:"bytes,5,opt,name=percentage,proto3" json:"percentage,omitempty"`
}

func (x *HTTPFaultInjection_Abort) Reset() {
	*x = HTTPFaultInjection_Abort{}
	if protoimpl.UnsafeEnabled {
		mi := &file_networking_v1beta1_virtual_service_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HTTPFaultInjection_Abort) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HTTPFaultInjection_Abort) ProtoMessage() {}

func (x *HTTPFaultInjection_Abort) ProtoReflect() protoreflect.Message {
	mi := &file_networking_v1beta1_virtual_service_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HTTPFaultInjection_Abort.ProtoReflect.Descriptor instead.
func (*HTTPFaultInjection_Abort) Descriptor() ([]byte, []int) {
	return file_networking_v1beta1_virtual_service_proto_rawDescGZIP(), []int{19, 1}
}

func (m *HTTPFaultInjection_Abort) GetErrorType() isHTTPFaultInjection_Abort_ErrorType {
	if m != nil {
		return m.ErrorType
	}
	return nil
}

func (x *HTTPFaultInjection_Abort) GetHttpStatus() int32 {
	if x, ok := x.GetErrorType().(*HTTPFaultInjection_Abort_HttpStatus); ok {
		return x.HttpStatus
	}
	return 0
}

func (x *HTTPFaultInjection_Abort) GetGrpcStatus() string {
	if x, ok := x.GetErrorType().(*HTTPFaultInjection_Abort_GrpcStatus); ok {
		return x.GrpcStatus
	}
	return ""
}

func (x *HTTPFaultInjection_Abort) GetHttp2Error() string {
	if x, ok := x.GetErrorType().(*HTTPFaultInjection_Abort_Http2Error); ok {
		return x.Http2Error
	}
	return ""
}

func (x *HTTPFaultInjection_Abort) GetPercentage() *Percent {
	if x != nil {
		return x.Percentage
	}
	return nil
}

type isHTTPFaultInjection_Abort_ErrorType interface {
	isHTTPFaultInjection_Abort_ErrorType()
}

type HTTPFaultInjection_Abort_HttpStatus struct {
	// HTTP status code to use to abort the Http request.
	HttpStatus int32 `protobuf:"varint,2,opt,name=http_status,json=httpStatus,proto3,oneof"`
}

type HTTPFaultInjection_Abort_GrpcStatus struct {
	// GRPC status code to use to abort the request. The supported
	// codes are documented in https://github.com/grpc/grpc/blob/master/doc/statuscodes.md
	// Note: If you want to return the status "Unavailable", then you should
	// specify the code as `UNAVAILABLE`(all caps), but not `14`.
	GrpcStatus string `protobuf:"bytes,3,opt,name=grpc_status,json=grpcStatus,proto3,oneof"`
}

type HTTPFaultInjection_Abort_Http2Error struct {
	// $hide_from_docs
	Http2Error string `protobuf:"bytes,4,opt,name=http2_error,json=http2Error,proto3,oneof"`
}

func (*HTTPFaultInjection_Abort_HttpStatus) isHTTPFaultInjection_Abort_ErrorType() {}

func (*HTTPFaultInjection_Abort_GrpcStatus) isHTTPFaultInjection_Abort_ErrorType() {}

func (*HTTPFaultInjection_Abort_Http2Error) isHTTPFaultInjection_Abort_ErrorType() {}

var File_networking_v1beta1_virtual_service_proto protoreflect.FileDescriptor

var file_networking_v1beta1_virtual_service_proto_rawDesc = []byte{
	0x0a, 0x28, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x62,
	0x65, 0x74, 0x61, 0x31, 0x2f, 0x76, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x18, 0x69, 0x73, 0x74, 0x69,
	0x6f, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x62,
	0x65, 0x74, 0x61, 0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x62, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x84, 0x02, 0x0a, 0x0e, 0x56, 0x69, 0x72, 0x74, 0x75, 0x61,
	0x6c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x68, 0x6f, 0x73, 0x74,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x68, 0x6f, 0x73, 0x74, 0x73, 0x12, 0x1a,
	0x0a, 0x08, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x08, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x73, 0x12, 0x37, 0x0a, 0x04, 0x68, 0x74,
	0x74, 0x70, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x69, 0x73, 0x74, 0x69, 0x6f,
	0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x62, 0x65,
	0x74, 0x61, 0x31, 0x2e, 0x48, 0x54, 0x54, 0x50, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x52, 0x04, 0x68,
	0x74, 0x74, 0x70, 0x12, 0x34, 0x0a, 0x03, 0x74, 0x6c, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x22, 0x2e, 0x69, 0x73, 0x74, 0x69, 0x6f, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x54, 0x4c, 0x53, 0x52,
	0x6f, 0x75, 0x74, 0x65, 0x52, 0x03, 0x74, 0x6c, 0x73, 0x12, 0x34, 0x0a, 0x03, 0x74, 0x63, 0x70,
	0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x69, 0x73, 0x74, 0x69, 0x6f, 0x2e, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61,
	0x31, 0x2e, 0x54, 0x43, 0x50, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x52, 0x03, 0x74, 0x63, 0x70, 0x12,
	0x1b, 0x0a, 0x09, 0x65, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x74, 0x6f, 0x18, 0x06, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x08, 0x65, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x6f, 0x22, 0x7b, 0x0a, 0x0b,
	0x44, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x04, 0x68,
	0x6f, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x04, 0xe2, 0x41, 0x01, 0x02, 0x52,
	0x04, 0x68, 0x6f, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x75, 0x62, 0x73, 0x65, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x75, 0x62, 0x73, 0x65, 0x74, 0x12, 0x3a, 0x0a,
	0x04, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x69, 0x73,
	0x74, 0x69, 0x6f, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x50, 0x6f, 0x72, 0x74, 0x53, 0x65, 0x6c, 0x65, 0x63,
	0x74, 0x6f, 0x72, 0x52, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x22, 0xe8, 0x08, 0x0a, 0x09, 0x48, 0x54,
	0x54, 0x50, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x40, 0x0a, 0x05, 0x6d,
	0x61, 0x74, 0x63, 0x68, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x69, 0x73, 0x74,
	0x69, 0x6f, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x48, 0x54, 0x54, 0x50, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x05, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x12, 0x44, 0x0a,
	0x05, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x69,
	0x73, 0x74, 0x69, 0x6f, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x48, 0x54, 0x54, 0x50, 0x52, 0x6f, 0x75, 0x74,
	0x65, 0x44, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x05, 0x72, 0x6f,
	0x75, 0x74, 0x65, 0x12, 0x42, 0x0a, 0x08, 0x72, 0x65, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x69, 0x73, 0x74, 0x69, 0x6f, 0x2e, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31,
	0x2e, 0x48, 0x54, 0x54, 0x50, 0x52, 0x65, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x52, 0x08, 0x72,
	0x65, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x12, 0x55, 0x0a, 0x0f, 0x64, 0x69, 0x72, 0x65, 0x63,
	0x74, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2c, 0x2e, 0x69, 0x73, 0x74, 0x69, 0x6f, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x48, 0x54, 0x54, 0x50,
	0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0e,
	0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3e,
	0x0a, 0x08, 0x64, 0x65, 0x6c, 0x65, 0x67, 0x61, 0x74, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x22, 0x2e, 0x69, 0x73, 0x74, 0x69, 0x6f, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65,
	0x67, 0x61, 0x74, 0x65, 0x52, 0x08, 0x64, 0x65, 0x6c, 0x65, 0x67, 0x61, 0x74, 0x65, 0x12, 0x3f,
	0x0a, 0x07, 0x72, 0x65, 0x77, 0x72, 0x69, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x25, 0x2e, 0x69, 0x73, 0x74, 0x69, 0x6f, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x48, 0x54, 0x54, 0x50, 0x52,
	0x65, 0x77, 0x72, 0x69, 0x74, 0x65, 0x52, 0x07, 0x72, 0x65, 0x77, 0x72, 0x69, 0x74, 0x65, 0x12,
	0x33, 0x0a, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x74, 0x69, 0x6d,
	0x65, 0x6f, 0x75, 0x74, 0x12, 0x3d, 0x0a, 0x07, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x73, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x69, 0x73, 0x74, 0x69, 0x6f, 0x2e, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31,
	0x2e, 0x48, 0x54, 0x54, 0x50, 0x52, 0x65, 0x74, 0x72, 0x79, 0x52, 0x07, 0x72, 0x65, 0x74, 0x72,
	0x69, 0x65, 0x73, 0x12, 0x42, 0x0a, 0x05, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x69, 0x73, 0x74, 0x69, 0x6f, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x48, 0x54,
	0x54, 0x50, 0x46, 0x61, 0x75, 0x6c, 0x74, 0x49, 0x6e, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x05, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x12, 0x3d, 0x0a, 0x06, 0x6d, 0x69, 0x72, 0x72, 0x6f,
	0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x69, 0x73, 0x74, 0x69, 0x6f, 0x2e,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74,
	0x61, 0x31, 0x2e, 0x44, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x06,
	0x6d, 0x69, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x47, 0x0a, 0x0e, 0x6d, 0x69, 0x72, 0x72, 0x6f, 0x72,
	0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x02, 0x18, 0x01,
	0x52, 0x0d, 0x6d, 0x69, 0x72, 0x72, 0x6f, 0x72, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x12,
	0x4e, 0x0a, 0x11, 0x6d, 0x69, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e,
	0x74, 0x61, 0x67, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x69, 0x73, 0x74,
	0x69, 0x6f, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x52, 0x10, 0x6d,
	0x69, 0x72, 0x72, 0x6f, 0x72, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x12,
	0x45, 0x0a, 0x0b, 0x63, 0x6f, 0x72, 0x73, 0x5f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x69, 0x73, 0x74, 0x69, 0x6f, 0x2e, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e,
	0x43, 0x6f, 0x72, 0x73, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x0a, 0x63, 0x6f, 0x72, 0x73,
	0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x3b, 0x0a, 0x07, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x73, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x69, 0x73, 0x74, 0x69, 0x6f, 0x2e,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74,
	0x61, 0x31, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x52, 0x07, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x73, 0x4a, 0x04, 0x08, 0x05, 0x10, 0x06, 0x4a, 0x04, 0x08, 0x0b, 0x10, 0x10, 0x52,
	0x11, 0x77, 0x65, 0x62, 0x73, 0x6f, 0x63, 0x6b, 0x65, 0x74, 0x5f, 0x75, 0x70, 0x67, 0x72, 0x61,
	0x64, 0x65, 0x52, 0x0e, 0x61, 0x70, 0x70, 0x65, 0x6e, 0x64, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x73, 0x52, 0x17, 0x72, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x52, 0x17, 0x61, 0x70, 0x70,
	0x65, 0x6e, 0x64, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x73, 0x52, 0x16, 0x72, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x5f, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x52, 0x16, 0x61, 0x70,
	0x70, 0x65, 0x6e, 0x64, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x73, 0x22, 0x3c, 0x0a, 0x08, 0x44, 0x65, 0x6c, 0x65, 0x67, 0x61, 0x74, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x22, 0xe2, 0x03, 0x0a, 0x07, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x12, 0x4c,
	0x0a, 0x07, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x32, 0x2e, 0x69, 0x73, 0x74, 0x69, 0x6f, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x73, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x52, 0x07, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x4e, 0x0a, 0x08,
	0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32,
	0x2e, 0x69, 0x73, 0x74, 0x69, 0x6f, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x73, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x52, 0x08, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x1a, 0xb8, 0x02, 0x0a,
	0x10, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x12, 0x4d, 0x0a, 0x03, 0x73, 0x65, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3b,
	0x2e, 0x69, 0x73, 0x74, 0x69, 0x6f, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x73, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2e, 0x53, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x03, 0x73, 0x65, 0x74,
	0x12, 0x4d, 0x0a, 0x03, 0x61, 0x64, 0x64, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3b, 0x2e,
	0x69, 0x73, 0x74, 0x69, 0x6f, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73,
	0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2e, 0x41, 0x64, 0x64, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x03, 0x61, 0x64, 0x64, 0x12,
	0x16, 0x0a, 0x06, 0x72, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x06, 0x72, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x1a, 0x36, 0x0a, 0x08, 0x53, 0x65, 0x74, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a,
	0x36, 0x0a, 0x08, 0x41, 0x64, 0x64, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x96, 0x01, 0x0a, 0x08, 0x54, 0x4c, 0x53, 0x52,
	0x6f, 0x75, 0x74, 0x65, 0x12, 0x48, 0x0a, 0x05, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x69, 0x73, 0x74, 0x69, 0x6f, 0x2e, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x54,
	0x4c, 0x53, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65,
	0x73, 0x42, 0x04, 0xe2, 0x41, 0x01, 0x02, 0x52, 0x05, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x12, 0x40,
	0x0a, 0x05, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e,
	0x69, 0x73, 0x74, 0x69, 0x6f, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x44, 0x65,
	0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x05, 0x72, 0x6f, 0x75, 0x74, 0x65,
	0x22, 0x8f, 0x01, 0x0a, 0x08, 0x54, 0x43, 0x50, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x12, 0x41, 0x0a,
	0x05, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x69,
	0x73, 0x74, 0x69, 0x6f, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x4c, 0x34, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x41,
	0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x52, 0x05, 0x6d, 0x61, 0x74, 0x63, 0x68,
	0x12, 0x40, 0x0a, 0x05, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x2a, 0x2e, 0x69, 0x73, 0x74, 0x69, 0x6f, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x52, 0x6f, 0x75, 0x74, 0x65,
	0x44, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x05, 0x72, 0x6f, 0x75,
	0x74, 0x65, 0x22, 0xba, 0x09, 0x0a, 0x10, 0x48, 0x54, 0x54, 0x50, 0x4d, 0x61, 0x74, 0x63, 0x68,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x37, 0x0a, 0x03, 0x75,
	0x72, 0x69, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x69, 0x73, 0x74, 0x69, 0x6f,
	0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x62, 0x65,
	0x74, 0x61, 0x31, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52,
	0x03, 0x75, 0x72, 0x69, 0x12, 0x3d, 0x0a, 0x06, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x69, 0x73, 0x74, 0x69, 0x6f, 0x2e, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52, 0x06, 0x73, 0x63, 0x68,
	0x65, 0x6d, 0x65, 0x12, 0x3d, 0x0a, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x69, 0x73, 0x74, 0x69, 0x6f, 0x2e, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x53,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52, 0x06, 0x6d, 0x65, 0x74, 0x68,
	0x6f, 0x64, 0x12, 0x43, 0x0a, 0x09, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x69, 0x73, 0x74, 0x69, 0x6f, 0x2e, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31,
	0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52, 0x09, 0x61, 0x75,
	0x74, 0x68, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x12, 0x51, 0x0a, 0x07, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x69, 0x73, 0x74, 0x69, 0x6f,
	0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x62, 0x65,
	0x74, 0x61, 0x31, 0x2e, 0x48, 0x54, 0x54, 0x50, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x07, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x6f,
	0x72, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x61,
	0x0a, 0x0d, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18,
	0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x69, 0x73, 0x74, 0x69, 0x6f, 0x2e, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31,
	0x2e, 0x48, 0x54, 0x54, 0x50, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x0c, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4c, 0x61, 0x62, 0x65, 0x6c,
	0x73, 0x12, 0x1a, 0x0a, 0x08, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x73, 0x18, 0x08, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x73, 0x12, 0x5e, 0x0a,
	0x0c, 0x71, 0x75, 0x65, 0x72, 0x79, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x09, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x69, 0x73, 0x74, 0x69, 0x6f, 0x2e, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x48,
	0x54, 0x54, 0x50, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x0b, 0x71, 0x75, 0x65, 0x72, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x26, 0x0a,
	0x0f, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x5f, 0x75, 0x72, 0x69, 0x5f, 0x63, 0x61, 0x73, 0x65,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x55, 0x72,
	0x69, 0x43, 0x61, 0x73, 0x65, 0x12, 0x67, 0x0a, 0x0f, 0x77, 0x69, 0x74, 0x68, 0x6f, 0x75, 0x74,
	0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3e,
	0x2e, 0x69, 0x73, 0x74, 0x69, 0x6f, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x48, 0x54, 0x54, 0x50, 0x4d, 0x61,
	0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x57, 0x69, 0x74, 0x68, 0x6f,
	0x75, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0e,
	0x77, 0x69, 0x74, 0x68, 0x6f, 0x75, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x12, 0x29,
	0x0a, 0x10, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x74, 0x61,
	0x74, 0x5f, 0x70, 0x72, 0x65, 0x66, 0x69, 0x78, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x73, 0x74, 0x61, 0x74, 0x50, 0x72, 0x65, 0x66, 0x69, 0x78, 0x1a, 0x61, 0x0a, 0x0c, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x3b, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x69, 0x73,
	0x74, 0x69, 0x6f, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4d, 0x61, 0x74,
	0x63, 0x68, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x3f, 0x0a,
	0x11, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x65,
	0x0a, 0x10, 0x51, 0x75, 0x65, 0x72, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x3b, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x69, 0x73, 0x74, 0x69, 0x6f, 0x2e, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x53,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x68, 0x0a, 0x13, 0x57, 0x69, 0x74, 0x68, 0x6f, 0x75, 0x74,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x3b,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e,
	0x69, 0x73, 0x74, 0x69, 0x6f, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4d,
	0x61, 0x74, 0x63, 0x68, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22,
	0xa2, 0x02, 0x0a, 0x14, 0x48, 0x54, 0x54, 0x50, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x44, 0x65, 0x73,
	0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4d, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x74,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e,
	0x69, 0x73, 0x74, 0x69, 0x6f, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x44, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x42, 0x04, 0xe2, 0x41, 0x01, 0x02, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x74,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x77, 0x65, 0x69, 0x67, 0x68,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12,
	0x3b, 0x0a, 0x07, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x69, 0x73, 0x74, 0x69, 0x6f, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x73, 0x52, 0x07, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x4a, 0x04, 0x08, 0x03,
	0x10, 0x07, 0x52, 0x17, 0x72, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x52, 0x17, 0x61, 0x70, 0x70,
	0x65, 0x6e, 0x64, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x73, 0x52, 0x16, 0x72, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x5f, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x52, 0x16, 0x61, 0x70,
	0x70, 0x65, 0x6e, 0x64, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x73, 0x22, 0x79, 0x0a, 0x10, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x44, 0x65, 0x73,
	0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4d, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x74,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e,
	0x69, 0x73, 0x74, 0x69, 0x6f, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x44, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x42, 0x04, 0xe2, 0x41, 0x01, 0x02, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x74,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x77, 0x65, 0x69, 0x67, 0x68,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x22,
	0xe9, 0x02, 0x0a, 0x11, 0x4c, 0x34, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x41, 0x74, 0x74, 0x72, 0x69,
	0x62, 0x75, 0x74, 0x65, 0x73, 0x12, 0x2f, 0x0a, 0x13, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x75, 0x62, 0x6e, 0x65, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x12, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x75, 0x62, 0x6e, 0x65, 0x74, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x73, 0x75, 0x62, 0x6e, 0x65, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x53, 0x75, 0x62, 0x6e, 0x65, 0x74, 0x12,
	0x62, 0x0a, 0x0d, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73,
	0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x69, 0x73, 0x74, 0x69, 0x6f, 0x2e, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61,
	0x31, 0x2e, 0x4c, 0x34, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75,
	0x74, 0x65, 0x73, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0c, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4c, 0x61, 0x62,
	0x65, 0x6c, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x73, 0x18,
	0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x73, 0x12,
	0x29, 0x0a, 0x10, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x1a, 0x3f, 0x0a, 0x11, 0x53, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xfe, 0x02, 0x0a, 0x12,
	0x54, 0x4c, 0x53, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74,
	0x65, 0x73, 0x12, 0x21, 0x0a, 0x09, 0x73, 0x6e, 0x69, 0x5f, 0x68, 0x6f, 0x73, 0x74, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x09, 0x42, 0x04, 0xe2, 0x41, 0x01, 0x02, 0x52, 0x08, 0x73, 0x6e, 0x69,
	0x48, 0x6f, 0x73, 0x74, 0x73, 0x12, 0x2f, 0x0a, 0x13, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x75, 0x62, 0x6e, 0x65, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x12, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x75, 0x62, 0x6e, 0x65, 0x74, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x63, 0x0a, 0x0d, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x3e, 0x2e, 0x69, 0x73, 0x74, 0x69, 0x6f, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x54, 0x4c, 0x53,
	0x4d, 0x61, 0x74, 0x63, 0x68, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x2e,
	0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x0c, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12,
	0x1a, 0x0a, 0x08, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x08, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x73, 0x12, 0x29, 0x0a, 0x10, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x1a, 0x3f, 0x0a, 0x11, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x4a, 0x04, 0x08, 0x04, 0x10, 0x05, 0x52, 0x0d, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x73, 0x75, 0x62, 0x6e, 0x65, 0x74, 0x22, 0xce, 0x02, 0x0a,
	0x0c, 0x48, 0x54, 0x54, 0x50, 0x52, 0x65, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x12, 0x10, 0x0a,
	0x03, 0x75, 0x72, 0x69, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x69, 0x12,
	0x1c, 0x0a, 0x09, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x12, 0x14, 0x0a,
	0x04, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x48, 0x00, 0x52, 0x04, 0x70,
	0x6f, 0x72, 0x74, 0x12, 0x5f, 0x0a, 0x0b, 0x64, 0x65, 0x72, 0x69, 0x76, 0x65, 0x5f, 0x70, 0x6f,
	0x72, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3c, 0x2e, 0x69, 0x73, 0x74, 0x69, 0x6f,
	0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x62, 0x65,
	0x74, 0x61, 0x31, 0x2e, 0x48, 0x54, 0x54, 0x50, 0x52, 0x65, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74,
	0x2e, 0x52, 0x65, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x50, 0x6f, 0x72, 0x74, 0x53, 0x65, 0x6c,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x00, 0x52, 0x0a, 0x64, 0x65, 0x72, 0x69, 0x76, 0x65,
	0x50, 0x6f, 0x72, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0d,
	0x72, 0x65, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0c, 0x72, 0x65, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x43, 0x6f, 0x64,
	0x65, 0x22, 0x49, 0x0a, 0x15, 0x52, 0x65, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x50, 0x6f, 0x72,
	0x74, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x19, 0x0a, 0x15, 0x46, 0x52,
	0x4f, 0x4d, 0x5f, 0x50, 0x52, 0x4f, 0x54, 0x4f, 0x43, 0x4f, 0x4c, 0x5f, 0x44, 0x45, 0x46, 0x41,
	0x55, 0x4c, 0x54, 0x10, 0x00, 0x12, 0x15, 0x0a, 0x11, 0x46, 0x52, 0x4f, 0x4d, 0x5f, 0x52, 0x45,
	0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x50, 0x4f, 0x52, 0x54, 0x10, 0x01, 0x42, 0x0f, 0x0a, 0x0d,
	0x72, 0x65, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x5f, 0x70, 0x6f, 0x72, 0x74, 0x22, 0x6a, 0x0a,
	0x12, 0x48, 0x54, 0x54, 0x50, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x1c, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0d, 0x42, 0x04, 0xe2, 0x41, 0x01, 0x02, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x36, 0x0a, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x22, 0x2e, 0x69, 0x73, 0x74, 0x69, 0x6f, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x48, 0x54, 0x54, 0x50, 0x42,
	0x6f, 0x64, 0x79, 0x52, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x22, 0x49, 0x0a, 0x08, 0x48, 0x54, 0x54,
	0x50, 0x42, 0x6f, 0x64, 0x79, 0x12, 0x18, 0x0a, 0x06, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x06, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x12,
	0x16, 0x0a, 0x05, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x48, 0x00,
	0x52, 0x05, 0x62, 0x79, 0x74, 0x65, 0x73, 0x42, 0x0b, 0x0a, 0x09, 0x73, 0x70, 0x65, 0x63, 0x69,
	0x66, 0x69, 0x65, 0x72, 0x22, 0x3d, 0x0a, 0x0b, 0x48, 0x54, 0x54, 0x50, 0x52, 0x65, 0x77, 0x72,
	0x69, 0x74, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x69, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x75, 0x72, 0x69, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69,
	0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72,
	0x69, 0x74, 0x79, 0x22, 0x65, 0x0a, 0x0b, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4d, 0x61, 0x74,
	0x63, 0x68, 0x12, 0x16, 0x0a, 0x05, 0x65, 0x78, 0x61, 0x63, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x00, 0x52, 0x05, 0x65, 0x78, 0x61, 0x63, 0x74, 0x12, 0x18, 0x0a, 0x06, 0x70, 0x72,
	0x65, 0x66, 0x69, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x06, 0x70, 0x72,
	0x65, 0x66, 0x69, 0x78, 0x12, 0x16, 0x0a, 0x05, 0x72, 0x65, 0x67, 0x65, 0x78, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x05, 0x72, 0x65, 0x67, 0x65, 0x78, 0x42, 0x0c, 0x0a, 0x0a,
	0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x22, 0xdf, 0x01, 0x0a, 0x09, 0x48,
	0x54, 0x54, 0x50, 0x52, 0x65, 0x74, 0x72, 0x79, 0x12, 0x20, 0x0a, 0x08, 0x61, 0x74, 0x74, 0x65,
	0x6d, 0x70, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x04, 0xe2, 0x41, 0x01, 0x02,
	0x52, 0x08, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x12, 0x41, 0x0a, 0x0f, 0x70, 0x65,
	0x72, 0x5f, 0x74, 0x72, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0d,
	0x70, 0x65, 0x72, 0x54, 0x72, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x12, 0x19, 0x0a,
	0x08, 0x72, 0x65, 0x74, 0x72, 0x79, 0x5f, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x72, 0x65, 0x74, 0x72, 0x79, 0x4f, 0x6e, 0x12, 0x52, 0x0a, 0x17, 0x72, 0x65, 0x74, 0x72,
	0x79, 0x5f, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x69, 0x74,
	0x69, 0x65, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x42, 0x6f, 0x6f, 0x6c,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x15, 0x72, 0x65, 0x74, 0x72, 0x79, 0x52, 0x65, 0x6d, 0x6f,
	0x74, 0x65, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x69, 0x74, 0x69, 0x65, 0x73, 0x22, 0xed, 0x02, 0x0a,
	0x0a, 0x43, 0x6f, 0x72, 0x73, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x25, 0x0a, 0x0c, 0x61,
	0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0b, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x4f, 0x72, 0x69, 0x67,
	0x69, 0x6e, 0x12, 0x4a, 0x0a, 0x0d, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x6f, 0x72, 0x69, 0x67,
	0x69, 0x6e, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x69, 0x73, 0x74, 0x69,
	0x6f, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x62,
	0x65, 0x74, 0x61, 0x31, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4d, 0x61, 0x74, 0x63, 0x68,
	0x52, 0x0c, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x4f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x73, 0x12, 0x23,
	0x0a, 0x0d, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x4d, 0x65, 0x74, 0x68,
	0x6f, 0x64, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x6c, 0x6c, 0x6f,
	0x77, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x65, 0x78, 0x70, 0x6f,
	0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x0d, 0x65, 0x78, 0x70, 0x6f, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x12,
	0x32, 0x0a, 0x07, 0x6d, 0x61, 0x78, 0x5f, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x06, 0x6d, 0x61, 0x78,
	0x41, 0x67, 0x65, 0x12, 0x47, 0x0a, 0x11, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x63, 0x72, 0x65,
	0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x10, 0x61, 0x6c, 0x6c, 0x6f,
	0x77, 0x43, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x73, 0x22, 0x8d, 0x05, 0x0a,
	0x12, 0x48, 0x54, 0x54, 0x50, 0x46, 0x61, 0x75, 0x6c, 0x74, 0x49, 0x6e, 0x6a, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x48, 0x0a, 0x05, 0x64, 0x65, 0x6c, 0x61, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x32, 0x2e, 0x69, 0x73, 0x74, 0x69, 0x6f, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x48, 0x54,
	0x54, 0x50, 0x46, 0x61, 0x75, 0x6c, 0x74, 0x49, 0x6e, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x44, 0x65, 0x6c, 0x61, 0x79, 0x52, 0x05, 0x64, 0x65, 0x6c, 0x61, 0x79, 0x12, 0x48, 0x0a,
	0x05, 0x61, 0x62, 0x6f, 0x72, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x69,
	0x73, 0x74, 0x69, 0x6f, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x48, 0x54, 0x54, 0x50, 0x46, 0x61, 0x75, 0x6c,
	0x74, 0x49, 0x6e, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x41, 0x62, 0x6f, 0x72, 0x74,
	0x52, 0x05, 0x61, 0x62, 0x6f, 0x72, 0x74, 0x1a, 0x89, 0x02, 0x0a, 0x05, 0x44, 0x65, 0x6c, 0x61,
	0x79, 0x12, 0x1c, 0x0a, 0x07, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x42, 0x02, 0x18, 0x01, 0x52, 0x07, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x12,
	0x42, 0x0a, 0x0b, 0x66, 0x69, 0x78, 0x65, 0x64, 0x5f, 0x64, 0x65, 0x6c, 0x61, 0x79, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42,
	0x04, 0xe2, 0x41, 0x01, 0x02, 0x48, 0x00, 0x52, 0x0a, 0x66, 0x69, 0x78, 0x65, 0x64, 0x44, 0x65,
	0x6c, 0x61, 0x79, 0x12, 0x48, 0x0a, 0x11, 0x65, 0x78, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x69,
	0x61, 0x6c, 0x5f, 0x64, 0x65, 0x6c, 0x61, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x00, 0x52, 0x10, 0x65, 0x78, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x44, 0x65, 0x6c, 0x61, 0x79, 0x12, 0x41, 0x0a,
	0x0a, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x21, 0x2e, 0x69, 0x73, 0x74, 0x69, 0x6f, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x50, 0x65, 0x72,
	0x63, 0x65, 0x6e, 0x74, 0x52, 0x0a, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65,
	0x42, 0x11, 0x0a, 0x0f, 0x68, 0x74, 0x74, 0x70, 0x5f, 0x64, 0x65, 0x6c, 0x61, 0x79, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x1a, 0xd6, 0x01, 0x0a, 0x05, 0x41, 0x62, 0x6f, 0x72, 0x74, 0x12, 0x27, 0x0a,
	0x0b, 0x68, 0x74, 0x74, 0x70, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x42, 0x04, 0xe2, 0x41, 0x01, 0x02, 0x48, 0x00, 0x52, 0x0a, 0x68, 0x74, 0x74, 0x70,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x21, 0x0a, 0x0b, 0x67, 0x72, 0x70, 0x63, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0a, 0x67,
	0x72, 0x70, 0x63, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x21, 0x0a, 0x0b, 0x68, 0x74, 0x74,
	0x70, 0x32, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00,
	0x52, 0x0a, 0x68, 0x74, 0x74, 0x70, 0x32, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x41, 0x0a, 0x0a,
	0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x69, 0x73, 0x74, 0x69, 0x6f, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x50, 0x65, 0x72, 0x63,
	0x65, 0x6e, 0x74, 0x52, 0x0a, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x42,
	0x0c, 0x0a, 0x0a, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x4a, 0x04, 0x08,
	0x01, 0x10, 0x02, 0x52, 0x07, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x22, 0x32, 0x0a, 0x0c,
	0x50, 0x6f, 0x72, 0x74, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x16, 0x0a, 0x06,
	0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x6e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x4a, 0x04, 0x08, 0x02, 0x10, 0x03, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x22, 0x1f, 0x0a, 0x07, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x42, 0x21, 0x5a, 0x1f, 0x69, 0x73, 0x74, 0x69, 0x6f, 0x2e, 0x69, 0x6f, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x62,
	0x65, 0x74, 0x61, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_networking_v1beta1_virtual_service_proto_rawDescOnce sync.Once
	file_networking_v1beta1_virtual_service_proto_rawDescData = file_networking_v1beta1_virtual_service_proto_rawDesc
)

func file_networking_v1beta1_virtual_service_proto_rawDescGZIP() []byte {
	file_networking_v1beta1_virtual_service_proto_rawDescOnce.Do(func() {
		file_networking_v1beta1_virtual_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_networking_v1beta1_virtual_service_proto_rawDescData)
	})
	return file_networking_v1beta1_virtual_service_proto_rawDescData
}

var file_networking_v1beta1_virtual_service_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_networking_v1beta1_virtual_service_proto_msgTypes = make([]protoimpl.MessageInfo, 33)
var file_networking_v1beta1_virtual_service_proto_goTypes = []interface{}{
	(HTTPRedirect_RedirectPortSelection)(0), // 0: istio.networking.v1beta1.HTTPRedirect.RedirectPortSelection
	(*VirtualService)(nil),                  // 1: istio.networking.v1beta1.VirtualService
	(*Destination)(nil),                     // 2: istio.networking.v1beta1.Destination
	(*HTTPRoute)(nil),                       // 3: istio.networking.v1beta1.HTTPRoute
	(*Delegate)(nil),                        // 4: istio.networking.v1beta1.Delegate
	(*Headers)(nil),                         // 5: istio.networking.v1beta1.Headers
	(*TLSRoute)(nil),                        // 6: istio.networking.v1beta1.TLSRoute
	(*TCPRoute)(nil),                        // 7: istio.networking.v1beta1.TCPRoute
	(*HTTPMatchRequest)(nil),                // 8: istio.networking.v1beta1.HTTPMatchRequest
	(*HTTPRouteDestination)(nil),            // 9: istio.networking.v1beta1.HTTPRouteDestination
	(*RouteDestination)(nil),                // 10: istio.networking.v1beta1.RouteDestination
	(*L4MatchAttributes)(nil),               // 11: istio.networking.v1beta1.L4MatchAttributes
	(*TLSMatchAttributes)(nil),              // 12: istio.networking.v1beta1.TLSMatchAttributes
	(*HTTPRedirect)(nil),                    // 13: istio.networking.v1beta1.HTTPRedirect
	(*HTTPDirectResponse)(nil),              // 14: istio.networking.v1beta1.HTTPDirectResponse
	(*HTTPBody)(nil),                        // 15: istio.networking.v1beta1.HTTPBody
	(*HTTPRewrite)(nil),                     // 16: istio.networking.v1beta1.HTTPRewrite
	(*StringMatch)(nil),                     // 17: istio.networking.v1beta1.StringMatch
	(*HTTPRetry)(nil),                       // 18: istio.networking.v1beta1.HTTPRetry
	(*CorsPolicy)(nil),                      // 19: istio.networking.v1beta1.CorsPolicy
	(*HTTPFaultInjection)(nil),              // 20: istio.networking.v1beta1.HTTPFaultInjection
	(*PortSelector)(nil),                    // 21: istio.networking.v1beta1.PortSelector
	(*Percent)(nil),                         // 22: istio.networking.v1beta1.Percent
	(*Headers_HeaderOperations)(nil),        // 23: istio.networking.v1beta1.Headers.HeaderOperations
	nil,                                     // 24: istio.networking.v1beta1.Headers.HeaderOperations.SetEntry
	nil,                                     // 25: istio.networking.v1beta1.Headers.HeaderOperations.AddEntry
	nil,                                     // 26: istio.networking.v1beta1.HTTPMatchRequest.HeadersEntry
	nil,                                     // 27: istio.networking.v1beta1.HTTPMatchRequest.SourceLabelsEntry
	nil,                                     // 28: istio.networking.v1beta1.HTTPMatchRequest.QueryParamsEntry
	nil,                                     // 29: istio.networking.v1beta1.HTTPMatchRequest.WithoutHeadersEntry
	nil,                                     // 30: istio.networking.v1beta1.L4MatchAttributes.SourceLabelsEntry
	nil,                                     // 31: istio.networking.v1beta1.TLSMatchAttributes.SourceLabelsEntry
	(*HTTPFaultInjection_Delay)(nil),        // 32: istio.networking.v1beta1.HTTPFaultInjection.Delay
	(*HTTPFaultInjection_Abort)(nil),        // 33: istio.networking.v1beta1.HTTPFaultInjection.Abort
	(*duration.Duration)(nil),               // 34: google.protobuf.Duration
	(*wrappers.UInt32Value)(nil),            // 35: google.protobuf.UInt32Value
	(*wrappers.BoolValue)(nil),              // 36: google.protobuf.BoolValue
}
var file_networking_v1beta1_virtual_service_proto_depIdxs = []int32{
	3,  // 0: istio.networking.v1beta1.VirtualService.http:type_name -> istio.networking.v1beta1.HTTPRoute
	6,  // 1: istio.networking.v1beta1.VirtualService.tls:type_name -> istio.networking.v1beta1.TLSRoute
	7,  // 2: istio.networking.v1beta1.VirtualService.tcp:type_name -> istio.networking.v1beta1.TCPRoute
	21, // 3: istio.networking.v1beta1.Destination.port:type_name -> istio.networking.v1beta1.PortSelector
	8,  // 4: istio.networking.v1beta1.HTTPRoute.match:type_name -> istio.networking.v1beta1.HTTPMatchRequest
	9,  // 5: istio.networking.v1beta1.HTTPRoute.route:type_name -> istio.networking.v1beta1.HTTPRouteDestination
	13, // 6: istio.networking.v1beta1.HTTPRoute.redirect:type_name -> istio.networking.v1beta1.HTTPRedirect
	14, // 7: istio.networking.v1beta1.HTTPRoute.direct_response:type_name -> istio.networking.v1beta1.HTTPDirectResponse
	4,  // 8: istio.networking.v1beta1.HTTPRoute.delegate:type_name -> istio.networking.v1beta1.Delegate
	16, // 9: istio.networking.v1beta1.HTTPRoute.rewrite:type_name -> istio.networking.v1beta1.HTTPRewrite
	34, // 10: istio.networking.v1beta1.HTTPRoute.timeout:type_name -> google.protobuf.Duration
	18, // 11: istio.networking.v1beta1.HTTPRoute.retries:type_name -> istio.networking.v1beta1.HTTPRetry
	20, // 12: istio.networking.v1beta1.HTTPRoute.fault:type_name -> istio.networking.v1beta1.HTTPFaultInjection
	2,  // 13: istio.networking.v1beta1.HTTPRoute.mirror:type_name -> istio.networking.v1beta1.Destination
	35, // 14: istio.networking.v1beta1.HTTPRoute.mirror_percent:type_name -> google.protobuf.UInt32Value
	22, // 15: istio.networking.v1beta1.HTTPRoute.mirror_percentage:type_name -> istio.networking.v1beta1.Percent
	19, // 16: istio.networking.v1beta1.HTTPRoute.cors_policy:type_name -> istio.networking.v1beta1.CorsPolicy
	5,  // 17: istio.networking.v1beta1.HTTPRoute.headers:type_name -> istio.networking.v1beta1.Headers
	23, // 18: istio.networking.v1beta1.Headers.request:type_name -> istio.networking.v1beta1.Headers.HeaderOperations
	23, // 19: istio.networking.v1beta1.Headers.response:type_name -> istio.networking.v1beta1.Headers.HeaderOperations
	12, // 20: istio.networking.v1beta1.TLSRoute.match:type_name -> istio.networking.v1beta1.TLSMatchAttributes
	10, // 21: istio.networking.v1beta1.TLSRoute.route:type_name -> istio.networking.v1beta1.RouteDestination
	11, // 22: istio.networking.v1beta1.TCPRoute.match:type_name -> istio.networking.v1beta1.L4MatchAttributes
	10, // 23: istio.networking.v1beta1.TCPRoute.route:type_name -> istio.networking.v1beta1.RouteDestination
	17, // 24: istio.networking.v1beta1.HTTPMatchRequest.uri:type_name -> istio.networking.v1beta1.StringMatch
	17, // 25: istio.networking.v1beta1.HTTPMatchRequest.scheme:type_name -> istio.networking.v1beta1.StringMatch
	17, // 26: istio.networking.v1beta1.HTTPMatchRequest.method:type_name -> istio.networking.v1beta1.StringMatch
	17, // 27: istio.networking.v1beta1.HTTPMatchRequest.authority:type_name -> istio.networking.v1beta1.StringMatch
	26, // 28: istio.networking.v1beta1.HTTPMatchRequest.headers:type_name -> istio.networking.v1beta1.HTTPMatchRequest.HeadersEntry
	27, // 29: istio.networking.v1beta1.HTTPMatchRequest.source_labels:type_name -> istio.networking.v1beta1.HTTPMatchRequest.SourceLabelsEntry
	28, // 30: istio.networking.v1beta1.HTTPMatchRequest.query_params:type_name -> istio.networking.v1beta1.HTTPMatchRequest.QueryParamsEntry
	29, // 31: istio.networking.v1beta1.HTTPMatchRequest.without_headers:type_name -> istio.networking.v1beta1.HTTPMatchRequest.WithoutHeadersEntry
	2,  // 32: istio.networking.v1beta1.HTTPRouteDestination.destination:type_name -> istio.networking.v1beta1.Destination
	5,  // 33: istio.networking.v1beta1.HTTPRouteDestination.headers:type_name -> istio.networking.v1beta1.Headers
	2,  // 34: istio.networking.v1beta1.RouteDestination.destination:type_name -> istio.networking.v1beta1.Destination
	30, // 35: istio.networking.v1beta1.L4MatchAttributes.source_labels:type_name -> istio.networking.v1beta1.L4MatchAttributes.SourceLabelsEntry
	31, // 36: istio.networking.v1beta1.TLSMatchAttributes.source_labels:type_name -> istio.networking.v1beta1.TLSMatchAttributes.SourceLabelsEntry
	0,  // 37: istio.networking.v1beta1.HTTPRedirect.derive_port:type_name -> istio.networking.v1beta1.HTTPRedirect.RedirectPortSelection
	15, // 38: istio.networking.v1beta1.HTTPDirectResponse.body:type_name -> istio.networking.v1beta1.HTTPBody
	34, // 39: istio.networking.v1beta1.HTTPRetry.per_try_timeout:type_name -> google.protobuf.Duration
	36, // 40: istio.networking.v1beta1.HTTPRetry.retry_remote_localities:type_name -> google.protobuf.BoolValue
	17, // 41: istio.networking.v1beta1.CorsPolicy.allow_origins:type_name -> istio.networking.v1beta1.StringMatch
	34, // 42: istio.networking.v1beta1.CorsPolicy.max_age:type_name -> google.protobuf.Duration
	36, // 43: istio.networking.v1beta1.CorsPolicy.allow_credentials:type_name -> google.protobuf.BoolValue
	32, // 44: istio.networking.v1beta1.HTTPFaultInjection.delay:type_name -> istio.networking.v1beta1.HTTPFaultInjection.Delay
	33, // 45: istio.networking.v1beta1.HTTPFaultInjection.abort:type_name -> istio.networking.v1beta1.HTTPFaultInjection.Abort
	24, // 46: istio.networking.v1beta1.Headers.HeaderOperations.set:type_name -> istio.networking.v1beta1.Headers.HeaderOperations.SetEntry
	25, // 47: istio.networking.v1beta1.Headers.HeaderOperations.add:type_name -> istio.networking.v1beta1.Headers.HeaderOperations.AddEntry
	17, // 48: istio.networking.v1beta1.HTTPMatchRequest.HeadersEntry.value:type_name -> istio.networking.v1beta1.StringMatch
	17, // 49: istio.networking.v1beta1.HTTPMatchRequest.QueryParamsEntry.value:type_name -> istio.networking.v1beta1.StringMatch
	17, // 50: istio.networking.v1beta1.HTTPMatchRequest.WithoutHeadersEntry.value:type_name -> istio.networking.v1beta1.StringMatch
	34, // 51: istio.networking.v1beta1.HTTPFaultInjection.Delay.fixed_delay:type_name -> google.protobuf.Duration
	34, // 52: istio.networking.v1beta1.HTTPFaultInjection.Delay.exponential_delay:type_name -> google.protobuf.Duration
	22, // 53: istio.networking.v1beta1.HTTPFaultInjection.Delay.percentage:type_name -> istio.networking.v1beta1.Percent
	22, // 54: istio.networking.v1beta1.HTTPFaultInjection.Abort.percentage:type_name -> istio.networking.v1beta1.Percent
	55, // [55:55] is the sub-list for method output_type
	55, // [55:55] is the sub-list for method input_type
	55, // [55:55] is the sub-list for extension type_name
	55, // [55:55] is the sub-list for extension extendee
	0,  // [0:55] is the sub-list for field type_name
}

func init() { file_networking_v1beta1_virtual_service_proto_init() }
func file_networking_v1beta1_virtual_service_proto_init() {
	if File_networking_v1beta1_virtual_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_networking_v1beta1_virtual_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VirtualService); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_networking_v1beta1_virtual_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Destination); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_networking_v1beta1_virtual_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HTTPRoute); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_networking_v1beta1_virtual_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Delegate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_networking_v1beta1_virtual_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Headers); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_networking_v1beta1_virtual_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TLSRoute); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_networking_v1beta1_virtual_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TCPRoute); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_networking_v1beta1_virtual_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HTTPMatchRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_networking_v1beta1_virtual_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HTTPRouteDestination); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_networking_v1beta1_virtual_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RouteDestination); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_networking_v1beta1_virtual_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*L4MatchAttributes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_networking_v1beta1_virtual_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TLSMatchAttributes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_networking_v1beta1_virtual_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HTTPRedirect); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_networking_v1beta1_virtual_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HTTPDirectResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_networking_v1beta1_virtual_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HTTPBody); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_networking_v1beta1_virtual_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HTTPRewrite); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_networking_v1beta1_virtual_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StringMatch); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_networking_v1beta1_virtual_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HTTPRetry); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_networking_v1beta1_virtual_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CorsPolicy); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_networking_v1beta1_virtual_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HTTPFaultInjection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_networking_v1beta1_virtual_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PortSelector); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_networking_v1beta1_virtual_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Percent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_networking_v1beta1_virtual_service_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Headers_HeaderOperations); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_networking_v1beta1_virtual_service_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HTTPFaultInjection_Delay); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_networking_v1beta1_virtual_service_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HTTPFaultInjection_Abort); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_networking_v1beta1_virtual_service_proto_msgTypes[12].OneofWrappers = []interface{}{
		(*HTTPRedirect_Port)(nil),
		(*HTTPRedirect_DerivePort)(nil),
	}
	file_networking_v1beta1_virtual_service_proto_msgTypes[14].OneofWrappers = []interface{}{
		(*HTTPBody_String_)(nil),
		(*HTTPBody_Bytes)(nil),
	}
	file_networking_v1beta1_virtual_service_proto_msgTypes[16].OneofWrappers = []interface{}{
		(*StringMatch_Exact)(nil),
		(*StringMatch_Prefix)(nil),
		(*StringMatch_Regex)(nil),
	}
	file_networking_v1beta1_virtual_service_proto_msgTypes[31].OneofWrappers = []interface{}{
		(*HTTPFaultInjection_Delay_FixedDelay)(nil),
		(*HTTPFaultInjection_Delay_ExponentialDelay)(nil),
	}
	file_networking_v1beta1_virtual_service_proto_msgTypes[32].OneofWrappers = []interface{}{
		(*HTTPFaultInjection_Abort_HttpStatus)(nil),
		(*HTTPFaultInjection_Abort_GrpcStatus)(nil),
		(*HTTPFaultInjection_Abort_Http2Error)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_networking_v1beta1_virtual_service_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   33,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_networking_v1beta1_virtual_service_proto_goTypes,
		DependencyIndexes: file_networking_v1beta1_virtual_service_proto_depIdxs,
		EnumInfos:         file_networking_v1beta1_virtual_service_proto_enumTypes,
		MessageInfos:      file_networking_v1beta1_virtual_service_proto_msgTypes,
	}.Build()
	File_networking_v1beta1_virtual_service_proto = out.File
	file_networking_v1beta1_virtual_service_proto_rawDesc = nil
	file_networking_v1beta1_virtual_service_proto_goTypes = nil
	file_networking_v1beta1_virtual_service_proto_depIdxs = nil
}
