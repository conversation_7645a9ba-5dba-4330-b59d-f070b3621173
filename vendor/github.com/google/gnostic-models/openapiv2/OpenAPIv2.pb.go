// Copyright 2020 Google LLC. All Rights Reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//    http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// THIS FILE IS AUTOMATICALLY GENERATED.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.1
// 	protoc        v4.23.4
// source: openapiv2/OpenAPIv2.proto

package openapi_v2

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	anypb "google.golang.org/protobuf/types/known/anypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AdditionalPropertiesItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Oneof:
	//
	//	*AdditionalPropertiesItem_Schema
	//	*AdditionalPropertiesItem_Boolean
	Oneof isAdditionalPropertiesItem_Oneof `protobuf_oneof:"oneof"`
}

func (x *AdditionalPropertiesItem) Reset() {
	*x = AdditionalPropertiesItem{}
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AdditionalPropertiesItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdditionalPropertiesItem) ProtoMessage() {}

func (x *AdditionalPropertiesItem) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdditionalPropertiesItem.ProtoReflect.Descriptor instead.
func (*AdditionalPropertiesItem) Descriptor() ([]byte, []int) {
	return file_openapiv2_OpenAPIv2_proto_rawDescGZIP(), []int{0}
}

func (m *AdditionalPropertiesItem) GetOneof() isAdditionalPropertiesItem_Oneof {
	if m != nil {
		return m.Oneof
	}
	return nil
}

func (x *AdditionalPropertiesItem) GetSchema() *Schema {
	if x, ok := x.GetOneof().(*AdditionalPropertiesItem_Schema); ok {
		return x.Schema
	}
	return nil
}

func (x *AdditionalPropertiesItem) GetBoolean() bool {
	if x, ok := x.GetOneof().(*AdditionalPropertiesItem_Boolean); ok {
		return x.Boolean
	}
	return false
}

type isAdditionalPropertiesItem_Oneof interface {
	isAdditionalPropertiesItem_Oneof()
}

type AdditionalPropertiesItem_Schema struct {
	Schema *Schema `protobuf:"bytes,1,opt,name=schema,proto3,oneof"`
}

type AdditionalPropertiesItem_Boolean struct {
	Boolean bool `protobuf:"varint,2,opt,name=boolean,proto3,oneof"`
}

func (*AdditionalPropertiesItem_Schema) isAdditionalPropertiesItem_Oneof() {}

func (*AdditionalPropertiesItem_Boolean) isAdditionalPropertiesItem_Oneof() {}

type Any struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value *anypb.Any `protobuf:"bytes,1,opt,name=value,proto3" json:"value,omitempty"`
	Yaml  string     `protobuf:"bytes,2,opt,name=yaml,proto3" json:"yaml,omitempty"`
}

func (x *Any) Reset() {
	*x = Any{}
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Any) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Any) ProtoMessage() {}

func (x *Any) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Any.ProtoReflect.Descriptor instead.
func (*Any) Descriptor() ([]byte, []int) {
	return file_openapiv2_OpenAPIv2_proto_rawDescGZIP(), []int{1}
}

func (x *Any) GetValue() *anypb.Any {
	if x != nil {
		return x.Value
	}
	return nil
}

func (x *Any) GetYaml() string {
	if x != nil {
		return x.Yaml
	}
	return ""
}

type ApiKeySecurity struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type            string      `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`
	Name            string      `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	In              string      `protobuf:"bytes,3,opt,name=in,proto3" json:"in,omitempty"`
	Description     string      `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	VendorExtension []*NamedAny `protobuf:"bytes,5,rep,name=vendor_extension,json=vendorExtension,proto3" json:"vendor_extension,omitempty"`
}

func (x *ApiKeySecurity) Reset() {
	*x = ApiKeySecurity{}
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ApiKeySecurity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApiKeySecurity) ProtoMessage() {}

func (x *ApiKeySecurity) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApiKeySecurity.ProtoReflect.Descriptor instead.
func (*ApiKeySecurity) Descriptor() ([]byte, []int) {
	return file_openapiv2_OpenAPIv2_proto_rawDescGZIP(), []int{2}
}

func (x *ApiKeySecurity) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *ApiKeySecurity) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ApiKeySecurity) GetIn() string {
	if x != nil {
		return x.In
	}
	return ""
}

func (x *ApiKeySecurity) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ApiKeySecurity) GetVendorExtension() []*NamedAny {
	if x != nil {
		return x.VendorExtension
	}
	return nil
}

type BasicAuthenticationSecurity struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type            string      `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`
	Description     string      `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	VendorExtension []*NamedAny `protobuf:"bytes,3,rep,name=vendor_extension,json=vendorExtension,proto3" json:"vendor_extension,omitempty"`
}

func (x *BasicAuthenticationSecurity) Reset() {
	*x = BasicAuthenticationSecurity{}
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BasicAuthenticationSecurity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BasicAuthenticationSecurity) ProtoMessage() {}

func (x *BasicAuthenticationSecurity) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BasicAuthenticationSecurity.ProtoReflect.Descriptor instead.
func (*BasicAuthenticationSecurity) Descriptor() ([]byte, []int) {
	return file_openapiv2_OpenAPIv2_proto_rawDescGZIP(), []int{3}
}

func (x *BasicAuthenticationSecurity) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *BasicAuthenticationSecurity) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *BasicAuthenticationSecurity) GetVendorExtension() []*NamedAny {
	if x != nil {
		return x.VendorExtension
	}
	return nil
}

type BodyParameter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A brief description of the parameter. This could contain examples of use.  GitHub Flavored Markdown is allowed.
	Description string `protobuf:"bytes,1,opt,name=description,proto3" json:"description,omitempty"`
	// The name of the parameter.
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// Determines the location of the parameter.
	In string `protobuf:"bytes,3,opt,name=in,proto3" json:"in,omitempty"`
	// Determines whether or not this parameter is required or optional.
	Required        bool        `protobuf:"varint,4,opt,name=required,proto3" json:"required,omitempty"`
	Schema          *Schema     `protobuf:"bytes,5,opt,name=schema,proto3" json:"schema,omitempty"`
	VendorExtension []*NamedAny `protobuf:"bytes,6,rep,name=vendor_extension,json=vendorExtension,proto3" json:"vendor_extension,omitempty"`
}

func (x *BodyParameter) Reset() {
	*x = BodyParameter{}
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BodyParameter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BodyParameter) ProtoMessage() {}

func (x *BodyParameter) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BodyParameter.ProtoReflect.Descriptor instead.
func (*BodyParameter) Descriptor() ([]byte, []int) {
	return file_openapiv2_OpenAPIv2_proto_rawDescGZIP(), []int{4}
}

func (x *BodyParameter) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *BodyParameter) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BodyParameter) GetIn() string {
	if x != nil {
		return x.In
	}
	return ""
}

func (x *BodyParameter) GetRequired() bool {
	if x != nil {
		return x.Required
	}
	return false
}

func (x *BodyParameter) GetSchema() *Schema {
	if x != nil {
		return x.Schema
	}
	return nil
}

func (x *BodyParameter) GetVendorExtension() []*NamedAny {
	if x != nil {
		return x.VendorExtension
	}
	return nil
}

// Contact information for the owners of the API.
type Contact struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The identifying name of the contact person/organization.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// The URL pointing to the contact information.
	Url string `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	// The email address of the contact person/organization.
	Email           string      `protobuf:"bytes,3,opt,name=email,proto3" json:"email,omitempty"`
	VendorExtension []*NamedAny `protobuf:"bytes,4,rep,name=vendor_extension,json=vendorExtension,proto3" json:"vendor_extension,omitempty"`
}

func (x *Contact) Reset() {
	*x = Contact{}
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Contact) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Contact) ProtoMessage() {}

func (x *Contact) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Contact.ProtoReflect.Descriptor instead.
func (*Contact) Descriptor() ([]byte, []int) {
	return file_openapiv2_OpenAPIv2_proto_rawDescGZIP(), []int{5}
}

func (x *Contact) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Contact) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *Contact) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *Contact) GetVendorExtension() []*NamedAny {
	if x != nil {
		return x.VendorExtension
	}
	return nil
}

type Default struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AdditionalProperties []*NamedAny `protobuf:"bytes,1,rep,name=additional_properties,json=additionalProperties,proto3" json:"additional_properties,omitempty"`
}

func (x *Default) Reset() {
	*x = Default{}
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Default) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Default) ProtoMessage() {}

func (x *Default) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Default.ProtoReflect.Descriptor instead.
func (*Default) Descriptor() ([]byte, []int) {
	return file_openapiv2_OpenAPIv2_proto_rawDescGZIP(), []int{6}
}

func (x *Default) GetAdditionalProperties() []*NamedAny {
	if x != nil {
		return x.AdditionalProperties
	}
	return nil
}

// One or more JSON objects describing the schemas being consumed and produced by the API.
type Definitions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AdditionalProperties []*NamedSchema `protobuf:"bytes,1,rep,name=additional_properties,json=additionalProperties,proto3" json:"additional_properties,omitempty"`
}

func (x *Definitions) Reset() {
	*x = Definitions{}
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Definitions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Definitions) ProtoMessage() {}

func (x *Definitions) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Definitions.ProtoReflect.Descriptor instead.
func (*Definitions) Descriptor() ([]byte, []int) {
	return file_openapiv2_OpenAPIv2_proto_rawDescGZIP(), []int{7}
}

func (x *Definitions) GetAdditionalProperties() []*NamedSchema {
	if x != nil {
		return x.AdditionalProperties
	}
	return nil
}

type Document struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The Swagger version of this document.
	Swagger string `protobuf:"bytes,1,opt,name=swagger,proto3" json:"swagger,omitempty"`
	Info    *Info  `protobuf:"bytes,2,opt,name=info,proto3" json:"info,omitempty"`
	// The host (name or ip) of the API. Example: 'swagger.io'
	Host string `protobuf:"bytes,3,opt,name=host,proto3" json:"host,omitempty"`
	// The base path to the API. Example: '/api'.
	BasePath string `protobuf:"bytes,4,opt,name=base_path,json=basePath,proto3" json:"base_path,omitempty"`
	// The transfer protocol of the API.
	Schemes []string `protobuf:"bytes,5,rep,name=schemes,proto3" json:"schemes,omitempty"`
	// A list of MIME types accepted by the API.
	Consumes []string `protobuf:"bytes,6,rep,name=consumes,proto3" json:"consumes,omitempty"`
	// A list of MIME types the API can produce.
	Produces            []string               `protobuf:"bytes,7,rep,name=produces,proto3" json:"produces,omitempty"`
	Paths               *Paths                 `protobuf:"bytes,8,opt,name=paths,proto3" json:"paths,omitempty"`
	Definitions         *Definitions           `protobuf:"bytes,9,opt,name=definitions,proto3" json:"definitions,omitempty"`
	Parameters          *ParameterDefinitions  `protobuf:"bytes,10,opt,name=parameters,proto3" json:"parameters,omitempty"`
	Responses           *ResponseDefinitions   `protobuf:"bytes,11,opt,name=responses,proto3" json:"responses,omitempty"`
	Security            []*SecurityRequirement `protobuf:"bytes,12,rep,name=security,proto3" json:"security,omitempty"`
	SecurityDefinitions *SecurityDefinitions   `protobuf:"bytes,13,opt,name=security_definitions,json=securityDefinitions,proto3" json:"security_definitions,omitempty"`
	Tags                []*Tag                 `protobuf:"bytes,14,rep,name=tags,proto3" json:"tags,omitempty"`
	ExternalDocs        *ExternalDocs          `protobuf:"bytes,15,opt,name=external_docs,json=externalDocs,proto3" json:"external_docs,omitempty"`
	VendorExtension     []*NamedAny            `protobuf:"bytes,16,rep,name=vendor_extension,json=vendorExtension,proto3" json:"vendor_extension,omitempty"`
}

func (x *Document) Reset() {
	*x = Document{}
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Document) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Document) ProtoMessage() {}

func (x *Document) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Document.ProtoReflect.Descriptor instead.
func (*Document) Descriptor() ([]byte, []int) {
	return file_openapiv2_OpenAPIv2_proto_rawDescGZIP(), []int{8}
}

func (x *Document) GetSwagger() string {
	if x != nil {
		return x.Swagger
	}
	return ""
}

func (x *Document) GetInfo() *Info {
	if x != nil {
		return x.Info
	}
	return nil
}

func (x *Document) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *Document) GetBasePath() string {
	if x != nil {
		return x.BasePath
	}
	return ""
}

func (x *Document) GetSchemes() []string {
	if x != nil {
		return x.Schemes
	}
	return nil
}

func (x *Document) GetConsumes() []string {
	if x != nil {
		return x.Consumes
	}
	return nil
}

func (x *Document) GetProduces() []string {
	if x != nil {
		return x.Produces
	}
	return nil
}

func (x *Document) GetPaths() *Paths {
	if x != nil {
		return x.Paths
	}
	return nil
}

func (x *Document) GetDefinitions() *Definitions {
	if x != nil {
		return x.Definitions
	}
	return nil
}

func (x *Document) GetParameters() *ParameterDefinitions {
	if x != nil {
		return x.Parameters
	}
	return nil
}

func (x *Document) GetResponses() *ResponseDefinitions {
	if x != nil {
		return x.Responses
	}
	return nil
}

func (x *Document) GetSecurity() []*SecurityRequirement {
	if x != nil {
		return x.Security
	}
	return nil
}

func (x *Document) GetSecurityDefinitions() *SecurityDefinitions {
	if x != nil {
		return x.SecurityDefinitions
	}
	return nil
}

func (x *Document) GetTags() []*Tag {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *Document) GetExternalDocs() *ExternalDocs {
	if x != nil {
		return x.ExternalDocs
	}
	return nil
}

func (x *Document) GetVendorExtension() []*NamedAny {
	if x != nil {
		return x.VendorExtension
	}
	return nil
}

type Examples struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AdditionalProperties []*NamedAny `protobuf:"bytes,1,rep,name=additional_properties,json=additionalProperties,proto3" json:"additional_properties,omitempty"`
}

func (x *Examples) Reset() {
	*x = Examples{}
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Examples) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Examples) ProtoMessage() {}

func (x *Examples) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Examples.ProtoReflect.Descriptor instead.
func (*Examples) Descriptor() ([]byte, []int) {
	return file_openapiv2_OpenAPIv2_proto_rawDescGZIP(), []int{9}
}

func (x *Examples) GetAdditionalProperties() []*NamedAny {
	if x != nil {
		return x.AdditionalProperties
	}
	return nil
}

// information about external documentation
type ExternalDocs struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Description     string      `protobuf:"bytes,1,opt,name=description,proto3" json:"description,omitempty"`
	Url             string      `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	VendorExtension []*NamedAny `protobuf:"bytes,3,rep,name=vendor_extension,json=vendorExtension,proto3" json:"vendor_extension,omitempty"`
}

func (x *ExternalDocs) Reset() {
	*x = ExternalDocs{}
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExternalDocs) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExternalDocs) ProtoMessage() {}

func (x *ExternalDocs) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExternalDocs.ProtoReflect.Descriptor instead.
func (*ExternalDocs) Descriptor() ([]byte, []int) {
	return file_openapiv2_OpenAPIv2_proto_rawDescGZIP(), []int{10}
}

func (x *ExternalDocs) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ExternalDocs) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *ExternalDocs) GetVendorExtension() []*NamedAny {
	if x != nil {
		return x.VendorExtension
	}
	return nil
}

// A deterministic version of a JSON Schema object.
type FileSchema struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Format          string        `protobuf:"bytes,1,opt,name=format,proto3" json:"format,omitempty"`
	Title           string        `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Description     string        `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	Default         *Any          `protobuf:"bytes,4,opt,name=default,proto3" json:"default,omitempty"`
	Required        []string      `protobuf:"bytes,5,rep,name=required,proto3" json:"required,omitempty"`
	Type            string        `protobuf:"bytes,6,opt,name=type,proto3" json:"type,omitempty"`
	ReadOnly        bool          `protobuf:"varint,7,opt,name=read_only,json=readOnly,proto3" json:"read_only,omitempty"`
	ExternalDocs    *ExternalDocs `protobuf:"bytes,8,opt,name=external_docs,json=externalDocs,proto3" json:"external_docs,omitempty"`
	Example         *Any          `protobuf:"bytes,9,opt,name=example,proto3" json:"example,omitempty"`
	VendorExtension []*NamedAny   `protobuf:"bytes,10,rep,name=vendor_extension,json=vendorExtension,proto3" json:"vendor_extension,omitempty"`
}

func (x *FileSchema) Reset() {
	*x = FileSchema{}
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileSchema) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileSchema) ProtoMessage() {}

func (x *FileSchema) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileSchema.ProtoReflect.Descriptor instead.
func (*FileSchema) Descriptor() ([]byte, []int) {
	return file_openapiv2_OpenAPIv2_proto_rawDescGZIP(), []int{11}
}

func (x *FileSchema) GetFormat() string {
	if x != nil {
		return x.Format
	}
	return ""
}

func (x *FileSchema) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *FileSchema) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *FileSchema) GetDefault() *Any {
	if x != nil {
		return x.Default
	}
	return nil
}

func (x *FileSchema) GetRequired() []string {
	if x != nil {
		return x.Required
	}
	return nil
}

func (x *FileSchema) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *FileSchema) GetReadOnly() bool {
	if x != nil {
		return x.ReadOnly
	}
	return false
}

func (x *FileSchema) GetExternalDocs() *ExternalDocs {
	if x != nil {
		return x.ExternalDocs
	}
	return nil
}

func (x *FileSchema) GetExample() *Any {
	if x != nil {
		return x.Example
	}
	return nil
}

func (x *FileSchema) GetVendorExtension() []*NamedAny {
	if x != nil {
		return x.VendorExtension
	}
	return nil
}

type FormDataParameterSubSchema struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Determines whether or not this parameter is required or optional.
	Required bool `protobuf:"varint,1,opt,name=required,proto3" json:"required,omitempty"`
	// Determines the location of the parameter.
	In string `protobuf:"bytes,2,opt,name=in,proto3" json:"in,omitempty"`
	// A brief description of the parameter. This could contain examples of use.  GitHub Flavored Markdown is allowed.
	Description string `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	// The name of the parameter.
	Name string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	// allows sending a parameter by name only or with an empty value.
	AllowEmptyValue  bool             `protobuf:"varint,5,opt,name=allow_empty_value,json=allowEmptyValue,proto3" json:"allow_empty_value,omitempty"`
	Type             string           `protobuf:"bytes,6,opt,name=type,proto3" json:"type,omitempty"`
	Format           string           `protobuf:"bytes,7,opt,name=format,proto3" json:"format,omitempty"`
	Items            *PrimitivesItems `protobuf:"bytes,8,opt,name=items,proto3" json:"items,omitempty"`
	CollectionFormat string           `protobuf:"bytes,9,opt,name=collection_format,json=collectionFormat,proto3" json:"collection_format,omitempty"`
	Default          *Any             `protobuf:"bytes,10,opt,name=default,proto3" json:"default,omitempty"`
	Maximum          float64          `protobuf:"fixed64,11,opt,name=maximum,proto3" json:"maximum,omitempty"`
	ExclusiveMaximum bool             `protobuf:"varint,12,opt,name=exclusive_maximum,json=exclusiveMaximum,proto3" json:"exclusive_maximum,omitempty"`
	Minimum          float64          `protobuf:"fixed64,13,opt,name=minimum,proto3" json:"minimum,omitempty"`
	ExclusiveMinimum bool             `protobuf:"varint,14,opt,name=exclusive_minimum,json=exclusiveMinimum,proto3" json:"exclusive_minimum,omitempty"`
	MaxLength        int64            `protobuf:"varint,15,opt,name=max_length,json=maxLength,proto3" json:"max_length,omitempty"`
	MinLength        int64            `protobuf:"varint,16,opt,name=min_length,json=minLength,proto3" json:"min_length,omitempty"`
	Pattern          string           `protobuf:"bytes,17,opt,name=pattern,proto3" json:"pattern,omitempty"`
	MaxItems         int64            `protobuf:"varint,18,opt,name=max_items,json=maxItems,proto3" json:"max_items,omitempty"`
	MinItems         int64            `protobuf:"varint,19,opt,name=min_items,json=minItems,proto3" json:"min_items,omitempty"`
	UniqueItems      bool             `protobuf:"varint,20,opt,name=unique_items,json=uniqueItems,proto3" json:"unique_items,omitempty"`
	Enum             []*Any           `protobuf:"bytes,21,rep,name=enum,proto3" json:"enum,omitempty"`
	MultipleOf       float64          `protobuf:"fixed64,22,opt,name=multiple_of,json=multipleOf,proto3" json:"multiple_of,omitempty"`
	VendorExtension  []*NamedAny      `protobuf:"bytes,23,rep,name=vendor_extension,json=vendorExtension,proto3" json:"vendor_extension,omitempty"`
}

func (x *FormDataParameterSubSchema) Reset() {
	*x = FormDataParameterSubSchema{}
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FormDataParameterSubSchema) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FormDataParameterSubSchema) ProtoMessage() {}

func (x *FormDataParameterSubSchema) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FormDataParameterSubSchema.ProtoReflect.Descriptor instead.
func (*FormDataParameterSubSchema) Descriptor() ([]byte, []int) {
	return file_openapiv2_OpenAPIv2_proto_rawDescGZIP(), []int{12}
}

func (x *FormDataParameterSubSchema) GetRequired() bool {
	if x != nil {
		return x.Required
	}
	return false
}

func (x *FormDataParameterSubSchema) GetIn() string {
	if x != nil {
		return x.In
	}
	return ""
}

func (x *FormDataParameterSubSchema) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *FormDataParameterSubSchema) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *FormDataParameterSubSchema) GetAllowEmptyValue() bool {
	if x != nil {
		return x.AllowEmptyValue
	}
	return false
}

func (x *FormDataParameterSubSchema) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *FormDataParameterSubSchema) GetFormat() string {
	if x != nil {
		return x.Format
	}
	return ""
}

func (x *FormDataParameterSubSchema) GetItems() *PrimitivesItems {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *FormDataParameterSubSchema) GetCollectionFormat() string {
	if x != nil {
		return x.CollectionFormat
	}
	return ""
}

func (x *FormDataParameterSubSchema) GetDefault() *Any {
	if x != nil {
		return x.Default
	}
	return nil
}

func (x *FormDataParameterSubSchema) GetMaximum() float64 {
	if x != nil {
		return x.Maximum
	}
	return 0
}

func (x *FormDataParameterSubSchema) GetExclusiveMaximum() bool {
	if x != nil {
		return x.ExclusiveMaximum
	}
	return false
}

func (x *FormDataParameterSubSchema) GetMinimum() float64 {
	if x != nil {
		return x.Minimum
	}
	return 0
}

func (x *FormDataParameterSubSchema) GetExclusiveMinimum() bool {
	if x != nil {
		return x.ExclusiveMinimum
	}
	return false
}

func (x *FormDataParameterSubSchema) GetMaxLength() int64 {
	if x != nil {
		return x.MaxLength
	}
	return 0
}

func (x *FormDataParameterSubSchema) GetMinLength() int64 {
	if x != nil {
		return x.MinLength
	}
	return 0
}

func (x *FormDataParameterSubSchema) GetPattern() string {
	if x != nil {
		return x.Pattern
	}
	return ""
}

func (x *FormDataParameterSubSchema) GetMaxItems() int64 {
	if x != nil {
		return x.MaxItems
	}
	return 0
}

func (x *FormDataParameterSubSchema) GetMinItems() int64 {
	if x != nil {
		return x.MinItems
	}
	return 0
}

func (x *FormDataParameterSubSchema) GetUniqueItems() bool {
	if x != nil {
		return x.UniqueItems
	}
	return false
}

func (x *FormDataParameterSubSchema) GetEnum() []*Any {
	if x != nil {
		return x.Enum
	}
	return nil
}

func (x *FormDataParameterSubSchema) GetMultipleOf() float64 {
	if x != nil {
		return x.MultipleOf
	}
	return 0
}

func (x *FormDataParameterSubSchema) GetVendorExtension() []*NamedAny {
	if x != nil {
		return x.VendorExtension
	}
	return nil
}

type Header struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type             string           `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`
	Format           string           `protobuf:"bytes,2,opt,name=format,proto3" json:"format,omitempty"`
	Items            *PrimitivesItems `protobuf:"bytes,3,opt,name=items,proto3" json:"items,omitempty"`
	CollectionFormat string           `protobuf:"bytes,4,opt,name=collection_format,json=collectionFormat,proto3" json:"collection_format,omitempty"`
	Default          *Any             `protobuf:"bytes,5,opt,name=default,proto3" json:"default,omitempty"`
	Maximum          float64          `protobuf:"fixed64,6,opt,name=maximum,proto3" json:"maximum,omitempty"`
	ExclusiveMaximum bool             `protobuf:"varint,7,opt,name=exclusive_maximum,json=exclusiveMaximum,proto3" json:"exclusive_maximum,omitempty"`
	Minimum          float64          `protobuf:"fixed64,8,opt,name=minimum,proto3" json:"minimum,omitempty"`
	ExclusiveMinimum bool             `protobuf:"varint,9,opt,name=exclusive_minimum,json=exclusiveMinimum,proto3" json:"exclusive_minimum,omitempty"`
	MaxLength        int64            `protobuf:"varint,10,opt,name=max_length,json=maxLength,proto3" json:"max_length,omitempty"`
	MinLength        int64            `protobuf:"varint,11,opt,name=min_length,json=minLength,proto3" json:"min_length,omitempty"`
	Pattern          string           `protobuf:"bytes,12,opt,name=pattern,proto3" json:"pattern,omitempty"`
	MaxItems         int64            `protobuf:"varint,13,opt,name=max_items,json=maxItems,proto3" json:"max_items,omitempty"`
	MinItems         int64            `protobuf:"varint,14,opt,name=min_items,json=minItems,proto3" json:"min_items,omitempty"`
	UniqueItems      bool             `protobuf:"varint,15,opt,name=unique_items,json=uniqueItems,proto3" json:"unique_items,omitempty"`
	Enum             []*Any           `protobuf:"bytes,16,rep,name=enum,proto3" json:"enum,omitempty"`
	MultipleOf       float64          `protobuf:"fixed64,17,opt,name=multiple_of,json=multipleOf,proto3" json:"multiple_of,omitempty"`
	Description      string           `protobuf:"bytes,18,opt,name=description,proto3" json:"description,omitempty"`
	VendorExtension  []*NamedAny      `protobuf:"bytes,19,rep,name=vendor_extension,json=vendorExtension,proto3" json:"vendor_extension,omitempty"`
}

func (x *Header) Reset() {
	*x = Header{}
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Header) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Header) ProtoMessage() {}

func (x *Header) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Header.ProtoReflect.Descriptor instead.
func (*Header) Descriptor() ([]byte, []int) {
	return file_openapiv2_OpenAPIv2_proto_rawDescGZIP(), []int{13}
}

func (x *Header) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *Header) GetFormat() string {
	if x != nil {
		return x.Format
	}
	return ""
}

func (x *Header) GetItems() *PrimitivesItems {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *Header) GetCollectionFormat() string {
	if x != nil {
		return x.CollectionFormat
	}
	return ""
}

func (x *Header) GetDefault() *Any {
	if x != nil {
		return x.Default
	}
	return nil
}

func (x *Header) GetMaximum() float64 {
	if x != nil {
		return x.Maximum
	}
	return 0
}

func (x *Header) GetExclusiveMaximum() bool {
	if x != nil {
		return x.ExclusiveMaximum
	}
	return false
}

func (x *Header) GetMinimum() float64 {
	if x != nil {
		return x.Minimum
	}
	return 0
}

func (x *Header) GetExclusiveMinimum() bool {
	if x != nil {
		return x.ExclusiveMinimum
	}
	return false
}

func (x *Header) GetMaxLength() int64 {
	if x != nil {
		return x.MaxLength
	}
	return 0
}

func (x *Header) GetMinLength() int64 {
	if x != nil {
		return x.MinLength
	}
	return 0
}

func (x *Header) GetPattern() string {
	if x != nil {
		return x.Pattern
	}
	return ""
}

func (x *Header) GetMaxItems() int64 {
	if x != nil {
		return x.MaxItems
	}
	return 0
}

func (x *Header) GetMinItems() int64 {
	if x != nil {
		return x.MinItems
	}
	return 0
}

func (x *Header) GetUniqueItems() bool {
	if x != nil {
		return x.UniqueItems
	}
	return false
}

func (x *Header) GetEnum() []*Any {
	if x != nil {
		return x.Enum
	}
	return nil
}

func (x *Header) GetMultipleOf() float64 {
	if x != nil {
		return x.MultipleOf
	}
	return 0
}

func (x *Header) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Header) GetVendorExtension() []*NamedAny {
	if x != nil {
		return x.VendorExtension
	}
	return nil
}

type HeaderParameterSubSchema struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Determines whether or not this parameter is required or optional.
	Required bool `protobuf:"varint,1,opt,name=required,proto3" json:"required,omitempty"`
	// Determines the location of the parameter.
	In string `protobuf:"bytes,2,opt,name=in,proto3" json:"in,omitempty"`
	// A brief description of the parameter. This could contain examples of use.  GitHub Flavored Markdown is allowed.
	Description string `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	// The name of the parameter.
	Name             string           `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	Type             string           `protobuf:"bytes,5,opt,name=type,proto3" json:"type,omitempty"`
	Format           string           `protobuf:"bytes,6,opt,name=format,proto3" json:"format,omitempty"`
	Items            *PrimitivesItems `protobuf:"bytes,7,opt,name=items,proto3" json:"items,omitempty"`
	CollectionFormat string           `protobuf:"bytes,8,opt,name=collection_format,json=collectionFormat,proto3" json:"collection_format,omitempty"`
	Default          *Any             `protobuf:"bytes,9,opt,name=default,proto3" json:"default,omitempty"`
	Maximum          float64          `protobuf:"fixed64,10,opt,name=maximum,proto3" json:"maximum,omitempty"`
	ExclusiveMaximum bool             `protobuf:"varint,11,opt,name=exclusive_maximum,json=exclusiveMaximum,proto3" json:"exclusive_maximum,omitempty"`
	Minimum          float64          `protobuf:"fixed64,12,opt,name=minimum,proto3" json:"minimum,omitempty"`
	ExclusiveMinimum bool             `protobuf:"varint,13,opt,name=exclusive_minimum,json=exclusiveMinimum,proto3" json:"exclusive_minimum,omitempty"`
	MaxLength        int64            `protobuf:"varint,14,opt,name=max_length,json=maxLength,proto3" json:"max_length,omitempty"`
	MinLength        int64            `protobuf:"varint,15,opt,name=min_length,json=minLength,proto3" json:"min_length,omitempty"`
	Pattern          string           `protobuf:"bytes,16,opt,name=pattern,proto3" json:"pattern,omitempty"`
	MaxItems         int64            `protobuf:"varint,17,opt,name=max_items,json=maxItems,proto3" json:"max_items,omitempty"`
	MinItems         int64            `protobuf:"varint,18,opt,name=min_items,json=minItems,proto3" json:"min_items,omitempty"`
	UniqueItems      bool             `protobuf:"varint,19,opt,name=unique_items,json=uniqueItems,proto3" json:"unique_items,omitempty"`
	Enum             []*Any           `protobuf:"bytes,20,rep,name=enum,proto3" json:"enum,omitempty"`
	MultipleOf       float64          `protobuf:"fixed64,21,opt,name=multiple_of,json=multipleOf,proto3" json:"multiple_of,omitempty"`
	VendorExtension  []*NamedAny      `protobuf:"bytes,22,rep,name=vendor_extension,json=vendorExtension,proto3" json:"vendor_extension,omitempty"`
}

func (x *HeaderParameterSubSchema) Reset() {
	*x = HeaderParameterSubSchema{}
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HeaderParameterSubSchema) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HeaderParameterSubSchema) ProtoMessage() {}

func (x *HeaderParameterSubSchema) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HeaderParameterSubSchema.ProtoReflect.Descriptor instead.
func (*HeaderParameterSubSchema) Descriptor() ([]byte, []int) {
	return file_openapiv2_OpenAPIv2_proto_rawDescGZIP(), []int{14}
}

func (x *HeaderParameterSubSchema) GetRequired() bool {
	if x != nil {
		return x.Required
	}
	return false
}

func (x *HeaderParameterSubSchema) GetIn() string {
	if x != nil {
		return x.In
	}
	return ""
}

func (x *HeaderParameterSubSchema) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *HeaderParameterSubSchema) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *HeaderParameterSubSchema) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *HeaderParameterSubSchema) GetFormat() string {
	if x != nil {
		return x.Format
	}
	return ""
}

func (x *HeaderParameterSubSchema) GetItems() *PrimitivesItems {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *HeaderParameterSubSchema) GetCollectionFormat() string {
	if x != nil {
		return x.CollectionFormat
	}
	return ""
}

func (x *HeaderParameterSubSchema) GetDefault() *Any {
	if x != nil {
		return x.Default
	}
	return nil
}

func (x *HeaderParameterSubSchema) GetMaximum() float64 {
	if x != nil {
		return x.Maximum
	}
	return 0
}

func (x *HeaderParameterSubSchema) GetExclusiveMaximum() bool {
	if x != nil {
		return x.ExclusiveMaximum
	}
	return false
}

func (x *HeaderParameterSubSchema) GetMinimum() float64 {
	if x != nil {
		return x.Minimum
	}
	return 0
}

func (x *HeaderParameterSubSchema) GetExclusiveMinimum() bool {
	if x != nil {
		return x.ExclusiveMinimum
	}
	return false
}

func (x *HeaderParameterSubSchema) GetMaxLength() int64 {
	if x != nil {
		return x.MaxLength
	}
	return 0
}

func (x *HeaderParameterSubSchema) GetMinLength() int64 {
	if x != nil {
		return x.MinLength
	}
	return 0
}

func (x *HeaderParameterSubSchema) GetPattern() string {
	if x != nil {
		return x.Pattern
	}
	return ""
}

func (x *HeaderParameterSubSchema) GetMaxItems() int64 {
	if x != nil {
		return x.MaxItems
	}
	return 0
}

func (x *HeaderParameterSubSchema) GetMinItems() int64 {
	if x != nil {
		return x.MinItems
	}
	return 0
}

func (x *HeaderParameterSubSchema) GetUniqueItems() bool {
	if x != nil {
		return x.UniqueItems
	}
	return false
}

func (x *HeaderParameterSubSchema) GetEnum() []*Any {
	if x != nil {
		return x.Enum
	}
	return nil
}

func (x *HeaderParameterSubSchema) GetMultipleOf() float64 {
	if x != nil {
		return x.MultipleOf
	}
	return 0
}

func (x *HeaderParameterSubSchema) GetVendorExtension() []*NamedAny {
	if x != nil {
		return x.VendorExtension
	}
	return nil
}

type Headers struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AdditionalProperties []*NamedHeader `protobuf:"bytes,1,rep,name=additional_properties,json=additionalProperties,proto3" json:"additional_properties,omitempty"`
}

func (x *Headers) Reset() {
	*x = Headers{}
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Headers) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Headers) ProtoMessage() {}

func (x *Headers) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Headers.ProtoReflect.Descriptor instead.
func (*Headers) Descriptor() ([]byte, []int) {
	return file_openapiv2_OpenAPIv2_proto_rawDescGZIP(), []int{15}
}

func (x *Headers) GetAdditionalProperties() []*NamedHeader {
	if x != nil {
		return x.AdditionalProperties
	}
	return nil
}

// General information about the API.
type Info struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A unique and precise title of the API.
	Title string `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	// A semantic version number of the API.
	Version string `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
	// A longer description of the API. Should be different from the title.  GitHub Flavored Markdown is allowed.
	Description string `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	// The terms of service for the API.
	TermsOfService  string      `protobuf:"bytes,4,opt,name=terms_of_service,json=termsOfService,proto3" json:"terms_of_service,omitempty"`
	Contact         *Contact    `protobuf:"bytes,5,opt,name=contact,proto3" json:"contact,omitempty"`
	License         *License    `protobuf:"bytes,6,opt,name=license,proto3" json:"license,omitempty"`
	VendorExtension []*NamedAny `protobuf:"bytes,7,rep,name=vendor_extension,json=vendorExtension,proto3" json:"vendor_extension,omitempty"`
}

func (x *Info) Reset() {
	*x = Info{}
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Info) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Info) ProtoMessage() {}

func (x *Info) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Info.ProtoReflect.Descriptor instead.
func (*Info) Descriptor() ([]byte, []int) {
	return file_openapiv2_OpenAPIv2_proto_rawDescGZIP(), []int{16}
}

func (x *Info) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *Info) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *Info) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Info) GetTermsOfService() string {
	if x != nil {
		return x.TermsOfService
	}
	return ""
}

func (x *Info) GetContact() *Contact {
	if x != nil {
		return x.Contact
	}
	return nil
}

func (x *Info) GetLicense() *License {
	if x != nil {
		return x.License
	}
	return nil
}

func (x *Info) GetVendorExtension() []*NamedAny {
	if x != nil {
		return x.VendorExtension
	}
	return nil
}

type ItemsItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Schema []*Schema `protobuf:"bytes,1,rep,name=schema,proto3" json:"schema,omitempty"`
}

func (x *ItemsItem) Reset() {
	*x = ItemsItem{}
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ItemsItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ItemsItem) ProtoMessage() {}

func (x *ItemsItem) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ItemsItem.ProtoReflect.Descriptor instead.
func (*ItemsItem) Descriptor() ([]byte, []int) {
	return file_openapiv2_OpenAPIv2_proto_rawDescGZIP(), []int{17}
}

func (x *ItemsItem) GetSchema() []*Schema {
	if x != nil {
		return x.Schema
	}
	return nil
}

type JsonReference struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	XRef        string `protobuf:"bytes,1,opt,name=_ref,json=Ref,proto3" json:"_ref,omitempty"`
	Description string `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
}

func (x *JsonReference) Reset() {
	*x = JsonReference{}
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JsonReference) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JsonReference) ProtoMessage() {}

func (x *JsonReference) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JsonReference.ProtoReflect.Descriptor instead.
func (*JsonReference) Descriptor() ([]byte, []int) {
	return file_openapiv2_OpenAPIv2_proto_rawDescGZIP(), []int{18}
}

func (x *JsonReference) GetXRef() string {
	if x != nil {
		return x.XRef
	}
	return ""
}

func (x *JsonReference) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

type License struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The name of the license type. It's encouraged to use an OSI compatible license.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// The URL pointing to the license.
	Url             string      `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	VendorExtension []*NamedAny `protobuf:"bytes,3,rep,name=vendor_extension,json=vendorExtension,proto3" json:"vendor_extension,omitempty"`
}

func (x *License) Reset() {
	*x = License{}
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *License) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*License) ProtoMessage() {}

func (x *License) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use License.ProtoReflect.Descriptor instead.
func (*License) Descriptor() ([]byte, []int) {
	return file_openapiv2_OpenAPIv2_proto_rawDescGZIP(), []int{19}
}

func (x *License) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *License) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *License) GetVendorExtension() []*NamedAny {
	if x != nil {
		return x.VendorExtension
	}
	return nil
}

// Automatically-generated message used to represent maps of Any as ordered (name,value) pairs.
type NamedAny struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Map key
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Mapped value
	Value *Any `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *NamedAny) Reset() {
	*x = NamedAny{}
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NamedAny) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NamedAny) ProtoMessage() {}

func (x *NamedAny) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NamedAny.ProtoReflect.Descriptor instead.
func (*NamedAny) Descriptor() ([]byte, []int) {
	return file_openapiv2_OpenAPIv2_proto_rawDescGZIP(), []int{20}
}

func (x *NamedAny) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NamedAny) GetValue() *Any {
	if x != nil {
		return x.Value
	}
	return nil
}

// Automatically-generated message used to represent maps of Header as ordered (name,value) pairs.
type NamedHeader struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Map key
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Mapped value
	Value *Header `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *NamedHeader) Reset() {
	*x = NamedHeader{}
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NamedHeader) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NamedHeader) ProtoMessage() {}

func (x *NamedHeader) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NamedHeader.ProtoReflect.Descriptor instead.
func (*NamedHeader) Descriptor() ([]byte, []int) {
	return file_openapiv2_OpenAPIv2_proto_rawDescGZIP(), []int{21}
}

func (x *NamedHeader) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NamedHeader) GetValue() *Header {
	if x != nil {
		return x.Value
	}
	return nil
}

// Automatically-generated message used to represent maps of Parameter as ordered (name,value) pairs.
type NamedParameter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Map key
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Mapped value
	Value *Parameter `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *NamedParameter) Reset() {
	*x = NamedParameter{}
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NamedParameter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NamedParameter) ProtoMessage() {}

func (x *NamedParameter) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NamedParameter.ProtoReflect.Descriptor instead.
func (*NamedParameter) Descriptor() ([]byte, []int) {
	return file_openapiv2_OpenAPIv2_proto_rawDescGZIP(), []int{22}
}

func (x *NamedParameter) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NamedParameter) GetValue() *Parameter {
	if x != nil {
		return x.Value
	}
	return nil
}

// Automatically-generated message used to represent maps of PathItem as ordered (name,value) pairs.
type NamedPathItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Map key
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Mapped value
	Value *PathItem `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *NamedPathItem) Reset() {
	*x = NamedPathItem{}
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NamedPathItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NamedPathItem) ProtoMessage() {}

func (x *NamedPathItem) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NamedPathItem.ProtoReflect.Descriptor instead.
func (*NamedPathItem) Descriptor() ([]byte, []int) {
	return file_openapiv2_OpenAPIv2_proto_rawDescGZIP(), []int{23}
}

func (x *NamedPathItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NamedPathItem) GetValue() *PathItem {
	if x != nil {
		return x.Value
	}
	return nil
}

// Automatically-generated message used to represent maps of Response as ordered (name,value) pairs.
type NamedResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Map key
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Mapped value
	Value *Response `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *NamedResponse) Reset() {
	*x = NamedResponse{}
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NamedResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NamedResponse) ProtoMessage() {}

func (x *NamedResponse) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NamedResponse.ProtoReflect.Descriptor instead.
func (*NamedResponse) Descriptor() ([]byte, []int) {
	return file_openapiv2_OpenAPIv2_proto_rawDescGZIP(), []int{24}
}

func (x *NamedResponse) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NamedResponse) GetValue() *Response {
	if x != nil {
		return x.Value
	}
	return nil
}

// Automatically-generated message used to represent maps of ResponseValue as ordered (name,value) pairs.
type NamedResponseValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Map key
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Mapped value
	Value *ResponseValue `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *NamedResponseValue) Reset() {
	*x = NamedResponseValue{}
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NamedResponseValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NamedResponseValue) ProtoMessage() {}

func (x *NamedResponseValue) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NamedResponseValue.ProtoReflect.Descriptor instead.
func (*NamedResponseValue) Descriptor() ([]byte, []int) {
	return file_openapiv2_OpenAPIv2_proto_rawDescGZIP(), []int{25}
}

func (x *NamedResponseValue) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NamedResponseValue) GetValue() *ResponseValue {
	if x != nil {
		return x.Value
	}
	return nil
}

// Automatically-generated message used to represent maps of Schema as ordered (name,value) pairs.
type NamedSchema struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Map key
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Mapped value
	Value *Schema `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *NamedSchema) Reset() {
	*x = NamedSchema{}
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NamedSchema) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NamedSchema) ProtoMessage() {}

func (x *NamedSchema) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NamedSchema.ProtoReflect.Descriptor instead.
func (*NamedSchema) Descriptor() ([]byte, []int) {
	return file_openapiv2_OpenAPIv2_proto_rawDescGZIP(), []int{26}
}

func (x *NamedSchema) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NamedSchema) GetValue() *Schema {
	if x != nil {
		return x.Value
	}
	return nil
}

// Automatically-generated message used to represent maps of SecurityDefinitionsItem as ordered (name,value) pairs.
type NamedSecurityDefinitionsItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Map key
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Mapped value
	Value *SecurityDefinitionsItem `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *NamedSecurityDefinitionsItem) Reset() {
	*x = NamedSecurityDefinitionsItem{}
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NamedSecurityDefinitionsItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NamedSecurityDefinitionsItem) ProtoMessage() {}

func (x *NamedSecurityDefinitionsItem) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NamedSecurityDefinitionsItem.ProtoReflect.Descriptor instead.
func (*NamedSecurityDefinitionsItem) Descriptor() ([]byte, []int) {
	return file_openapiv2_OpenAPIv2_proto_rawDescGZIP(), []int{27}
}

func (x *NamedSecurityDefinitionsItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NamedSecurityDefinitionsItem) GetValue() *SecurityDefinitionsItem {
	if x != nil {
		return x.Value
	}
	return nil
}

// Automatically-generated message used to represent maps of string as ordered (name,value) pairs.
type NamedString struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Map key
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Mapped value
	Value string `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *NamedString) Reset() {
	*x = NamedString{}
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NamedString) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NamedString) ProtoMessage() {}

func (x *NamedString) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NamedString.ProtoReflect.Descriptor instead.
func (*NamedString) Descriptor() ([]byte, []int) {
	return file_openapiv2_OpenAPIv2_proto_rawDescGZIP(), []int{28}
}

func (x *NamedString) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NamedString) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

// Automatically-generated message used to represent maps of StringArray as ordered (name,value) pairs.
type NamedStringArray struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Map key
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Mapped value
	Value *StringArray `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *NamedStringArray) Reset() {
	*x = NamedStringArray{}
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NamedStringArray) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NamedStringArray) ProtoMessage() {}

func (x *NamedStringArray) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NamedStringArray.ProtoReflect.Descriptor instead.
func (*NamedStringArray) Descriptor() ([]byte, []int) {
	return file_openapiv2_OpenAPIv2_proto_rawDescGZIP(), []int{29}
}

func (x *NamedStringArray) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NamedStringArray) GetValue() *StringArray {
	if x != nil {
		return x.Value
	}
	return nil
}

type NonBodyParameter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Oneof:
	//
	//	*NonBodyParameter_HeaderParameterSubSchema
	//	*NonBodyParameter_FormDataParameterSubSchema
	//	*NonBodyParameter_QueryParameterSubSchema
	//	*NonBodyParameter_PathParameterSubSchema
	Oneof isNonBodyParameter_Oneof `protobuf_oneof:"oneof"`
}

func (x *NonBodyParameter) Reset() {
	*x = NonBodyParameter{}
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NonBodyParameter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NonBodyParameter) ProtoMessage() {}

func (x *NonBodyParameter) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NonBodyParameter.ProtoReflect.Descriptor instead.
func (*NonBodyParameter) Descriptor() ([]byte, []int) {
	return file_openapiv2_OpenAPIv2_proto_rawDescGZIP(), []int{30}
}

func (m *NonBodyParameter) GetOneof() isNonBodyParameter_Oneof {
	if m != nil {
		return m.Oneof
	}
	return nil
}

func (x *NonBodyParameter) GetHeaderParameterSubSchema() *HeaderParameterSubSchema {
	if x, ok := x.GetOneof().(*NonBodyParameter_HeaderParameterSubSchema); ok {
		return x.HeaderParameterSubSchema
	}
	return nil
}

func (x *NonBodyParameter) GetFormDataParameterSubSchema() *FormDataParameterSubSchema {
	if x, ok := x.GetOneof().(*NonBodyParameter_FormDataParameterSubSchema); ok {
		return x.FormDataParameterSubSchema
	}
	return nil
}

func (x *NonBodyParameter) GetQueryParameterSubSchema() *QueryParameterSubSchema {
	if x, ok := x.GetOneof().(*NonBodyParameter_QueryParameterSubSchema); ok {
		return x.QueryParameterSubSchema
	}
	return nil
}

func (x *NonBodyParameter) GetPathParameterSubSchema() *PathParameterSubSchema {
	if x, ok := x.GetOneof().(*NonBodyParameter_PathParameterSubSchema); ok {
		return x.PathParameterSubSchema
	}
	return nil
}

type isNonBodyParameter_Oneof interface {
	isNonBodyParameter_Oneof()
}

type NonBodyParameter_HeaderParameterSubSchema struct {
	HeaderParameterSubSchema *HeaderParameterSubSchema `protobuf:"bytes,1,opt,name=header_parameter_sub_schema,json=headerParameterSubSchema,proto3,oneof"`
}

type NonBodyParameter_FormDataParameterSubSchema struct {
	FormDataParameterSubSchema *FormDataParameterSubSchema `protobuf:"bytes,2,opt,name=form_data_parameter_sub_schema,json=formDataParameterSubSchema,proto3,oneof"`
}

type NonBodyParameter_QueryParameterSubSchema struct {
	QueryParameterSubSchema *QueryParameterSubSchema `protobuf:"bytes,3,opt,name=query_parameter_sub_schema,json=queryParameterSubSchema,proto3,oneof"`
}

type NonBodyParameter_PathParameterSubSchema struct {
	PathParameterSubSchema *PathParameterSubSchema `protobuf:"bytes,4,opt,name=path_parameter_sub_schema,json=pathParameterSubSchema,proto3,oneof"`
}

func (*NonBodyParameter_HeaderParameterSubSchema) isNonBodyParameter_Oneof() {}

func (*NonBodyParameter_FormDataParameterSubSchema) isNonBodyParameter_Oneof() {}

func (*NonBodyParameter_QueryParameterSubSchema) isNonBodyParameter_Oneof() {}

func (*NonBodyParameter_PathParameterSubSchema) isNonBodyParameter_Oneof() {}

type Oauth2AccessCodeSecurity struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type             string        `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`
	Flow             string        `protobuf:"bytes,2,opt,name=flow,proto3" json:"flow,omitempty"`
	Scopes           *Oauth2Scopes `protobuf:"bytes,3,opt,name=scopes,proto3" json:"scopes,omitempty"`
	AuthorizationUrl string        `protobuf:"bytes,4,opt,name=authorization_url,json=authorizationUrl,proto3" json:"authorization_url,omitempty"`
	TokenUrl         string        `protobuf:"bytes,5,opt,name=token_url,json=tokenUrl,proto3" json:"token_url,omitempty"`
	Description      string        `protobuf:"bytes,6,opt,name=description,proto3" json:"description,omitempty"`
	VendorExtension  []*NamedAny   `protobuf:"bytes,7,rep,name=vendor_extension,json=vendorExtension,proto3" json:"vendor_extension,omitempty"`
}

func (x *Oauth2AccessCodeSecurity) Reset() {
	*x = Oauth2AccessCodeSecurity{}
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Oauth2AccessCodeSecurity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Oauth2AccessCodeSecurity) ProtoMessage() {}

func (x *Oauth2AccessCodeSecurity) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Oauth2AccessCodeSecurity.ProtoReflect.Descriptor instead.
func (*Oauth2AccessCodeSecurity) Descriptor() ([]byte, []int) {
	return file_openapiv2_OpenAPIv2_proto_rawDescGZIP(), []int{31}
}

func (x *Oauth2AccessCodeSecurity) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *Oauth2AccessCodeSecurity) GetFlow() string {
	if x != nil {
		return x.Flow
	}
	return ""
}

func (x *Oauth2AccessCodeSecurity) GetScopes() *Oauth2Scopes {
	if x != nil {
		return x.Scopes
	}
	return nil
}

func (x *Oauth2AccessCodeSecurity) GetAuthorizationUrl() string {
	if x != nil {
		return x.AuthorizationUrl
	}
	return ""
}

func (x *Oauth2AccessCodeSecurity) GetTokenUrl() string {
	if x != nil {
		return x.TokenUrl
	}
	return ""
}

func (x *Oauth2AccessCodeSecurity) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Oauth2AccessCodeSecurity) GetVendorExtension() []*NamedAny {
	if x != nil {
		return x.VendorExtension
	}
	return nil
}

type Oauth2ApplicationSecurity struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type            string        `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`
	Flow            string        `protobuf:"bytes,2,opt,name=flow,proto3" json:"flow,omitempty"`
	Scopes          *Oauth2Scopes `protobuf:"bytes,3,opt,name=scopes,proto3" json:"scopes,omitempty"`
	TokenUrl        string        `protobuf:"bytes,4,opt,name=token_url,json=tokenUrl,proto3" json:"token_url,omitempty"`
	Description     string        `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`
	VendorExtension []*NamedAny   `protobuf:"bytes,6,rep,name=vendor_extension,json=vendorExtension,proto3" json:"vendor_extension,omitempty"`
}

func (x *Oauth2ApplicationSecurity) Reset() {
	*x = Oauth2ApplicationSecurity{}
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Oauth2ApplicationSecurity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Oauth2ApplicationSecurity) ProtoMessage() {}

func (x *Oauth2ApplicationSecurity) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Oauth2ApplicationSecurity.ProtoReflect.Descriptor instead.
func (*Oauth2ApplicationSecurity) Descriptor() ([]byte, []int) {
	return file_openapiv2_OpenAPIv2_proto_rawDescGZIP(), []int{32}
}

func (x *Oauth2ApplicationSecurity) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *Oauth2ApplicationSecurity) GetFlow() string {
	if x != nil {
		return x.Flow
	}
	return ""
}

func (x *Oauth2ApplicationSecurity) GetScopes() *Oauth2Scopes {
	if x != nil {
		return x.Scopes
	}
	return nil
}

func (x *Oauth2ApplicationSecurity) GetTokenUrl() string {
	if x != nil {
		return x.TokenUrl
	}
	return ""
}

func (x *Oauth2ApplicationSecurity) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Oauth2ApplicationSecurity) GetVendorExtension() []*NamedAny {
	if x != nil {
		return x.VendorExtension
	}
	return nil
}

type Oauth2ImplicitSecurity struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type             string        `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`
	Flow             string        `protobuf:"bytes,2,opt,name=flow,proto3" json:"flow,omitempty"`
	Scopes           *Oauth2Scopes `protobuf:"bytes,3,opt,name=scopes,proto3" json:"scopes,omitempty"`
	AuthorizationUrl string        `protobuf:"bytes,4,opt,name=authorization_url,json=authorizationUrl,proto3" json:"authorization_url,omitempty"`
	Description      string        `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`
	VendorExtension  []*NamedAny   `protobuf:"bytes,6,rep,name=vendor_extension,json=vendorExtension,proto3" json:"vendor_extension,omitempty"`
}

func (x *Oauth2ImplicitSecurity) Reset() {
	*x = Oauth2ImplicitSecurity{}
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Oauth2ImplicitSecurity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Oauth2ImplicitSecurity) ProtoMessage() {}

func (x *Oauth2ImplicitSecurity) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Oauth2ImplicitSecurity.ProtoReflect.Descriptor instead.
func (*Oauth2ImplicitSecurity) Descriptor() ([]byte, []int) {
	return file_openapiv2_OpenAPIv2_proto_rawDescGZIP(), []int{33}
}

func (x *Oauth2ImplicitSecurity) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *Oauth2ImplicitSecurity) GetFlow() string {
	if x != nil {
		return x.Flow
	}
	return ""
}

func (x *Oauth2ImplicitSecurity) GetScopes() *Oauth2Scopes {
	if x != nil {
		return x.Scopes
	}
	return nil
}

func (x *Oauth2ImplicitSecurity) GetAuthorizationUrl() string {
	if x != nil {
		return x.AuthorizationUrl
	}
	return ""
}

func (x *Oauth2ImplicitSecurity) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Oauth2ImplicitSecurity) GetVendorExtension() []*NamedAny {
	if x != nil {
		return x.VendorExtension
	}
	return nil
}

type Oauth2PasswordSecurity struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type            string        `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`
	Flow            string        `protobuf:"bytes,2,opt,name=flow,proto3" json:"flow,omitempty"`
	Scopes          *Oauth2Scopes `protobuf:"bytes,3,opt,name=scopes,proto3" json:"scopes,omitempty"`
	TokenUrl        string        `protobuf:"bytes,4,opt,name=token_url,json=tokenUrl,proto3" json:"token_url,omitempty"`
	Description     string        `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`
	VendorExtension []*NamedAny   `protobuf:"bytes,6,rep,name=vendor_extension,json=vendorExtension,proto3" json:"vendor_extension,omitempty"`
}

func (x *Oauth2PasswordSecurity) Reset() {
	*x = Oauth2PasswordSecurity{}
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Oauth2PasswordSecurity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Oauth2PasswordSecurity) ProtoMessage() {}

func (x *Oauth2PasswordSecurity) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Oauth2PasswordSecurity.ProtoReflect.Descriptor instead.
func (*Oauth2PasswordSecurity) Descriptor() ([]byte, []int) {
	return file_openapiv2_OpenAPIv2_proto_rawDescGZIP(), []int{34}
}

func (x *Oauth2PasswordSecurity) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *Oauth2PasswordSecurity) GetFlow() string {
	if x != nil {
		return x.Flow
	}
	return ""
}

func (x *Oauth2PasswordSecurity) GetScopes() *Oauth2Scopes {
	if x != nil {
		return x.Scopes
	}
	return nil
}

func (x *Oauth2PasswordSecurity) GetTokenUrl() string {
	if x != nil {
		return x.TokenUrl
	}
	return ""
}

func (x *Oauth2PasswordSecurity) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Oauth2PasswordSecurity) GetVendorExtension() []*NamedAny {
	if x != nil {
		return x.VendorExtension
	}
	return nil
}

type Oauth2Scopes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AdditionalProperties []*NamedString `protobuf:"bytes,1,rep,name=additional_properties,json=additionalProperties,proto3" json:"additional_properties,omitempty"`
}

func (x *Oauth2Scopes) Reset() {
	*x = Oauth2Scopes{}
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Oauth2Scopes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Oauth2Scopes) ProtoMessage() {}

func (x *Oauth2Scopes) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Oauth2Scopes.ProtoReflect.Descriptor instead.
func (*Oauth2Scopes) Descriptor() ([]byte, []int) {
	return file_openapiv2_OpenAPIv2_proto_rawDescGZIP(), []int{35}
}

func (x *Oauth2Scopes) GetAdditionalProperties() []*NamedString {
	if x != nil {
		return x.AdditionalProperties
	}
	return nil
}

type Operation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Tags []string `protobuf:"bytes,1,rep,name=tags,proto3" json:"tags,omitempty"`
	// A brief summary of the operation.
	Summary string `protobuf:"bytes,2,opt,name=summary,proto3" json:"summary,omitempty"`
	// A longer description of the operation, GitHub Flavored Markdown is allowed.
	Description  string        `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	ExternalDocs *ExternalDocs `protobuf:"bytes,4,opt,name=external_docs,json=externalDocs,proto3" json:"external_docs,omitempty"`
	// A unique identifier of the operation.
	OperationId string `protobuf:"bytes,5,opt,name=operation_id,json=operationId,proto3" json:"operation_id,omitempty"`
	// A list of MIME types the API can produce.
	Produces []string `protobuf:"bytes,6,rep,name=produces,proto3" json:"produces,omitempty"`
	// A list of MIME types the API can consume.
	Consumes []string `protobuf:"bytes,7,rep,name=consumes,proto3" json:"consumes,omitempty"`
	// The parameters needed to send a valid API call.
	Parameters []*ParametersItem `protobuf:"bytes,8,rep,name=parameters,proto3" json:"parameters,omitempty"`
	Responses  *Responses        `protobuf:"bytes,9,opt,name=responses,proto3" json:"responses,omitempty"`
	// The transfer protocol of the API.
	Schemes         []string               `protobuf:"bytes,10,rep,name=schemes,proto3" json:"schemes,omitempty"`
	Deprecated      bool                   `protobuf:"varint,11,opt,name=deprecated,proto3" json:"deprecated,omitempty"`
	Security        []*SecurityRequirement `protobuf:"bytes,12,rep,name=security,proto3" json:"security,omitempty"`
	VendorExtension []*NamedAny            `protobuf:"bytes,13,rep,name=vendor_extension,json=vendorExtension,proto3" json:"vendor_extension,omitempty"`
}

func (x *Operation) Reset() {
	*x = Operation{}
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Operation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Operation) ProtoMessage() {}

func (x *Operation) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Operation.ProtoReflect.Descriptor instead.
func (*Operation) Descriptor() ([]byte, []int) {
	return file_openapiv2_OpenAPIv2_proto_rawDescGZIP(), []int{36}
}

func (x *Operation) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *Operation) GetSummary() string {
	if x != nil {
		return x.Summary
	}
	return ""
}

func (x *Operation) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Operation) GetExternalDocs() *ExternalDocs {
	if x != nil {
		return x.ExternalDocs
	}
	return nil
}

func (x *Operation) GetOperationId() string {
	if x != nil {
		return x.OperationId
	}
	return ""
}

func (x *Operation) GetProduces() []string {
	if x != nil {
		return x.Produces
	}
	return nil
}

func (x *Operation) GetConsumes() []string {
	if x != nil {
		return x.Consumes
	}
	return nil
}

func (x *Operation) GetParameters() []*ParametersItem {
	if x != nil {
		return x.Parameters
	}
	return nil
}

func (x *Operation) GetResponses() *Responses {
	if x != nil {
		return x.Responses
	}
	return nil
}

func (x *Operation) GetSchemes() []string {
	if x != nil {
		return x.Schemes
	}
	return nil
}

func (x *Operation) GetDeprecated() bool {
	if x != nil {
		return x.Deprecated
	}
	return false
}

func (x *Operation) GetSecurity() []*SecurityRequirement {
	if x != nil {
		return x.Security
	}
	return nil
}

func (x *Operation) GetVendorExtension() []*NamedAny {
	if x != nil {
		return x.VendorExtension
	}
	return nil
}

type Parameter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Oneof:
	//
	//	*Parameter_BodyParameter
	//	*Parameter_NonBodyParameter
	Oneof isParameter_Oneof `protobuf_oneof:"oneof"`
}

func (x *Parameter) Reset() {
	*x = Parameter{}
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Parameter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Parameter) ProtoMessage() {}

func (x *Parameter) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Parameter.ProtoReflect.Descriptor instead.
func (*Parameter) Descriptor() ([]byte, []int) {
	return file_openapiv2_OpenAPIv2_proto_rawDescGZIP(), []int{37}
}

func (m *Parameter) GetOneof() isParameter_Oneof {
	if m != nil {
		return m.Oneof
	}
	return nil
}

func (x *Parameter) GetBodyParameter() *BodyParameter {
	if x, ok := x.GetOneof().(*Parameter_BodyParameter); ok {
		return x.BodyParameter
	}
	return nil
}

func (x *Parameter) GetNonBodyParameter() *NonBodyParameter {
	if x, ok := x.GetOneof().(*Parameter_NonBodyParameter); ok {
		return x.NonBodyParameter
	}
	return nil
}

type isParameter_Oneof interface {
	isParameter_Oneof()
}

type Parameter_BodyParameter struct {
	BodyParameter *BodyParameter `protobuf:"bytes,1,opt,name=body_parameter,json=bodyParameter,proto3,oneof"`
}

type Parameter_NonBodyParameter struct {
	NonBodyParameter *NonBodyParameter `protobuf:"bytes,2,opt,name=non_body_parameter,json=nonBodyParameter,proto3,oneof"`
}

func (*Parameter_BodyParameter) isParameter_Oneof() {}

func (*Parameter_NonBodyParameter) isParameter_Oneof() {}

// One or more JSON representations for parameters
type ParameterDefinitions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AdditionalProperties []*NamedParameter `protobuf:"bytes,1,rep,name=additional_properties,json=additionalProperties,proto3" json:"additional_properties,omitempty"`
}

func (x *ParameterDefinitions) Reset() {
	*x = ParameterDefinitions{}
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ParameterDefinitions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ParameterDefinitions) ProtoMessage() {}

func (x *ParameterDefinitions) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ParameterDefinitions.ProtoReflect.Descriptor instead.
func (*ParameterDefinitions) Descriptor() ([]byte, []int) {
	return file_openapiv2_OpenAPIv2_proto_rawDescGZIP(), []int{38}
}

func (x *ParameterDefinitions) GetAdditionalProperties() []*NamedParameter {
	if x != nil {
		return x.AdditionalProperties
	}
	return nil
}

type ParametersItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Oneof:
	//
	//	*ParametersItem_Parameter
	//	*ParametersItem_JsonReference
	Oneof isParametersItem_Oneof `protobuf_oneof:"oneof"`
}

func (x *ParametersItem) Reset() {
	*x = ParametersItem{}
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ParametersItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ParametersItem) ProtoMessage() {}

func (x *ParametersItem) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ParametersItem.ProtoReflect.Descriptor instead.
func (*ParametersItem) Descriptor() ([]byte, []int) {
	return file_openapiv2_OpenAPIv2_proto_rawDescGZIP(), []int{39}
}

func (m *ParametersItem) GetOneof() isParametersItem_Oneof {
	if m != nil {
		return m.Oneof
	}
	return nil
}

func (x *ParametersItem) GetParameter() *Parameter {
	if x, ok := x.GetOneof().(*ParametersItem_Parameter); ok {
		return x.Parameter
	}
	return nil
}

func (x *ParametersItem) GetJsonReference() *JsonReference {
	if x, ok := x.GetOneof().(*ParametersItem_JsonReference); ok {
		return x.JsonReference
	}
	return nil
}

type isParametersItem_Oneof interface {
	isParametersItem_Oneof()
}

type ParametersItem_Parameter struct {
	Parameter *Parameter `protobuf:"bytes,1,opt,name=parameter,proto3,oneof"`
}

type ParametersItem_JsonReference struct {
	JsonReference *JsonReference `protobuf:"bytes,2,opt,name=json_reference,json=jsonReference,proto3,oneof"`
}

func (*ParametersItem_Parameter) isParametersItem_Oneof() {}

func (*ParametersItem_JsonReference) isParametersItem_Oneof() {}

type PathItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	XRef    string     `protobuf:"bytes,1,opt,name=_ref,json=Ref,proto3" json:"_ref,omitempty"`
	Get     *Operation `protobuf:"bytes,2,opt,name=get,proto3" json:"get,omitempty"`
	Put     *Operation `protobuf:"bytes,3,opt,name=put,proto3" json:"put,omitempty"`
	Post    *Operation `protobuf:"bytes,4,opt,name=post,proto3" json:"post,omitempty"`
	Delete  *Operation `protobuf:"bytes,5,opt,name=delete,proto3" json:"delete,omitempty"`
	Options *Operation `protobuf:"bytes,6,opt,name=options,proto3" json:"options,omitempty"`
	Head    *Operation `protobuf:"bytes,7,opt,name=head,proto3" json:"head,omitempty"`
	Patch   *Operation `protobuf:"bytes,8,opt,name=patch,proto3" json:"patch,omitempty"`
	// The parameters needed to send a valid API call.
	Parameters      []*ParametersItem `protobuf:"bytes,9,rep,name=parameters,proto3" json:"parameters,omitempty"`
	VendorExtension []*NamedAny       `protobuf:"bytes,10,rep,name=vendor_extension,json=vendorExtension,proto3" json:"vendor_extension,omitempty"`
}

func (x *PathItem) Reset() {
	*x = PathItem{}
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PathItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PathItem) ProtoMessage() {}

func (x *PathItem) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PathItem.ProtoReflect.Descriptor instead.
func (*PathItem) Descriptor() ([]byte, []int) {
	return file_openapiv2_OpenAPIv2_proto_rawDescGZIP(), []int{40}
}

func (x *PathItem) GetXRef() string {
	if x != nil {
		return x.XRef
	}
	return ""
}

func (x *PathItem) GetGet() *Operation {
	if x != nil {
		return x.Get
	}
	return nil
}

func (x *PathItem) GetPut() *Operation {
	if x != nil {
		return x.Put
	}
	return nil
}

func (x *PathItem) GetPost() *Operation {
	if x != nil {
		return x.Post
	}
	return nil
}

func (x *PathItem) GetDelete() *Operation {
	if x != nil {
		return x.Delete
	}
	return nil
}

func (x *PathItem) GetOptions() *Operation {
	if x != nil {
		return x.Options
	}
	return nil
}

func (x *PathItem) GetHead() *Operation {
	if x != nil {
		return x.Head
	}
	return nil
}

func (x *PathItem) GetPatch() *Operation {
	if x != nil {
		return x.Patch
	}
	return nil
}

func (x *PathItem) GetParameters() []*ParametersItem {
	if x != nil {
		return x.Parameters
	}
	return nil
}

func (x *PathItem) GetVendorExtension() []*NamedAny {
	if x != nil {
		return x.VendorExtension
	}
	return nil
}

type PathParameterSubSchema struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Determines whether or not this parameter is required or optional.
	Required bool `protobuf:"varint,1,opt,name=required,proto3" json:"required,omitempty"`
	// Determines the location of the parameter.
	In string `protobuf:"bytes,2,opt,name=in,proto3" json:"in,omitempty"`
	// A brief description of the parameter. This could contain examples of use.  GitHub Flavored Markdown is allowed.
	Description string `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	// The name of the parameter.
	Name             string           `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	Type             string           `protobuf:"bytes,5,opt,name=type,proto3" json:"type,omitempty"`
	Format           string           `protobuf:"bytes,6,opt,name=format,proto3" json:"format,omitempty"`
	Items            *PrimitivesItems `protobuf:"bytes,7,opt,name=items,proto3" json:"items,omitempty"`
	CollectionFormat string           `protobuf:"bytes,8,opt,name=collection_format,json=collectionFormat,proto3" json:"collection_format,omitempty"`
	Default          *Any             `protobuf:"bytes,9,opt,name=default,proto3" json:"default,omitempty"`
	Maximum          float64          `protobuf:"fixed64,10,opt,name=maximum,proto3" json:"maximum,omitempty"`
	ExclusiveMaximum bool             `protobuf:"varint,11,opt,name=exclusive_maximum,json=exclusiveMaximum,proto3" json:"exclusive_maximum,omitempty"`
	Minimum          float64          `protobuf:"fixed64,12,opt,name=minimum,proto3" json:"minimum,omitempty"`
	ExclusiveMinimum bool             `protobuf:"varint,13,opt,name=exclusive_minimum,json=exclusiveMinimum,proto3" json:"exclusive_minimum,omitempty"`
	MaxLength        int64            `protobuf:"varint,14,opt,name=max_length,json=maxLength,proto3" json:"max_length,omitempty"`
	MinLength        int64            `protobuf:"varint,15,opt,name=min_length,json=minLength,proto3" json:"min_length,omitempty"`
	Pattern          string           `protobuf:"bytes,16,opt,name=pattern,proto3" json:"pattern,omitempty"`
	MaxItems         int64            `protobuf:"varint,17,opt,name=max_items,json=maxItems,proto3" json:"max_items,omitempty"`
	MinItems         int64            `protobuf:"varint,18,opt,name=min_items,json=minItems,proto3" json:"min_items,omitempty"`
	UniqueItems      bool             `protobuf:"varint,19,opt,name=unique_items,json=uniqueItems,proto3" json:"unique_items,omitempty"`
	Enum             []*Any           `protobuf:"bytes,20,rep,name=enum,proto3" json:"enum,omitempty"`
	MultipleOf       float64          `protobuf:"fixed64,21,opt,name=multiple_of,json=multipleOf,proto3" json:"multiple_of,omitempty"`
	VendorExtension  []*NamedAny      `protobuf:"bytes,22,rep,name=vendor_extension,json=vendorExtension,proto3" json:"vendor_extension,omitempty"`
}

func (x *PathParameterSubSchema) Reset() {
	*x = PathParameterSubSchema{}
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PathParameterSubSchema) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PathParameterSubSchema) ProtoMessage() {}

func (x *PathParameterSubSchema) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PathParameterSubSchema.ProtoReflect.Descriptor instead.
func (*PathParameterSubSchema) Descriptor() ([]byte, []int) {
	return file_openapiv2_OpenAPIv2_proto_rawDescGZIP(), []int{41}
}

func (x *PathParameterSubSchema) GetRequired() bool {
	if x != nil {
		return x.Required
	}
	return false
}

func (x *PathParameterSubSchema) GetIn() string {
	if x != nil {
		return x.In
	}
	return ""
}

func (x *PathParameterSubSchema) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *PathParameterSubSchema) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PathParameterSubSchema) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *PathParameterSubSchema) GetFormat() string {
	if x != nil {
		return x.Format
	}
	return ""
}

func (x *PathParameterSubSchema) GetItems() *PrimitivesItems {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *PathParameterSubSchema) GetCollectionFormat() string {
	if x != nil {
		return x.CollectionFormat
	}
	return ""
}

func (x *PathParameterSubSchema) GetDefault() *Any {
	if x != nil {
		return x.Default
	}
	return nil
}

func (x *PathParameterSubSchema) GetMaximum() float64 {
	if x != nil {
		return x.Maximum
	}
	return 0
}

func (x *PathParameterSubSchema) GetExclusiveMaximum() bool {
	if x != nil {
		return x.ExclusiveMaximum
	}
	return false
}

func (x *PathParameterSubSchema) GetMinimum() float64 {
	if x != nil {
		return x.Minimum
	}
	return 0
}

func (x *PathParameterSubSchema) GetExclusiveMinimum() bool {
	if x != nil {
		return x.ExclusiveMinimum
	}
	return false
}

func (x *PathParameterSubSchema) GetMaxLength() int64 {
	if x != nil {
		return x.MaxLength
	}
	return 0
}

func (x *PathParameterSubSchema) GetMinLength() int64 {
	if x != nil {
		return x.MinLength
	}
	return 0
}

func (x *PathParameterSubSchema) GetPattern() string {
	if x != nil {
		return x.Pattern
	}
	return ""
}

func (x *PathParameterSubSchema) GetMaxItems() int64 {
	if x != nil {
		return x.MaxItems
	}
	return 0
}

func (x *PathParameterSubSchema) GetMinItems() int64 {
	if x != nil {
		return x.MinItems
	}
	return 0
}

func (x *PathParameterSubSchema) GetUniqueItems() bool {
	if x != nil {
		return x.UniqueItems
	}
	return false
}

func (x *PathParameterSubSchema) GetEnum() []*Any {
	if x != nil {
		return x.Enum
	}
	return nil
}

func (x *PathParameterSubSchema) GetMultipleOf() float64 {
	if x != nil {
		return x.MultipleOf
	}
	return 0
}

func (x *PathParameterSubSchema) GetVendorExtension() []*NamedAny {
	if x != nil {
		return x.VendorExtension
	}
	return nil
}

// Relative paths to the individual endpoints. They must be relative to the 'basePath'.
type Paths struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VendorExtension []*NamedAny      `protobuf:"bytes,1,rep,name=vendor_extension,json=vendorExtension,proto3" json:"vendor_extension,omitempty"`
	Path            []*NamedPathItem `protobuf:"bytes,2,rep,name=path,proto3" json:"path,omitempty"`
}

func (x *Paths) Reset() {
	*x = Paths{}
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[42]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Paths) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Paths) ProtoMessage() {}

func (x *Paths) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[42]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Paths.ProtoReflect.Descriptor instead.
func (*Paths) Descriptor() ([]byte, []int) {
	return file_openapiv2_OpenAPIv2_proto_rawDescGZIP(), []int{42}
}

func (x *Paths) GetVendorExtension() []*NamedAny {
	if x != nil {
		return x.VendorExtension
	}
	return nil
}

func (x *Paths) GetPath() []*NamedPathItem {
	if x != nil {
		return x.Path
	}
	return nil
}

type PrimitivesItems struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type             string           `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`
	Format           string           `protobuf:"bytes,2,opt,name=format,proto3" json:"format,omitempty"`
	Items            *PrimitivesItems `protobuf:"bytes,3,opt,name=items,proto3" json:"items,omitempty"`
	CollectionFormat string           `protobuf:"bytes,4,opt,name=collection_format,json=collectionFormat,proto3" json:"collection_format,omitempty"`
	Default          *Any             `protobuf:"bytes,5,opt,name=default,proto3" json:"default,omitempty"`
	Maximum          float64          `protobuf:"fixed64,6,opt,name=maximum,proto3" json:"maximum,omitempty"`
	ExclusiveMaximum bool             `protobuf:"varint,7,opt,name=exclusive_maximum,json=exclusiveMaximum,proto3" json:"exclusive_maximum,omitempty"`
	Minimum          float64          `protobuf:"fixed64,8,opt,name=minimum,proto3" json:"minimum,omitempty"`
	ExclusiveMinimum bool             `protobuf:"varint,9,opt,name=exclusive_minimum,json=exclusiveMinimum,proto3" json:"exclusive_minimum,omitempty"`
	MaxLength        int64            `protobuf:"varint,10,opt,name=max_length,json=maxLength,proto3" json:"max_length,omitempty"`
	MinLength        int64            `protobuf:"varint,11,opt,name=min_length,json=minLength,proto3" json:"min_length,omitempty"`
	Pattern          string           `protobuf:"bytes,12,opt,name=pattern,proto3" json:"pattern,omitempty"`
	MaxItems         int64            `protobuf:"varint,13,opt,name=max_items,json=maxItems,proto3" json:"max_items,omitempty"`
	MinItems         int64            `protobuf:"varint,14,opt,name=min_items,json=minItems,proto3" json:"min_items,omitempty"`
	UniqueItems      bool             `protobuf:"varint,15,opt,name=unique_items,json=uniqueItems,proto3" json:"unique_items,omitempty"`
	Enum             []*Any           `protobuf:"bytes,16,rep,name=enum,proto3" json:"enum,omitempty"`
	MultipleOf       float64          `protobuf:"fixed64,17,opt,name=multiple_of,json=multipleOf,proto3" json:"multiple_of,omitempty"`
	VendorExtension  []*NamedAny      `protobuf:"bytes,18,rep,name=vendor_extension,json=vendorExtension,proto3" json:"vendor_extension,omitempty"`
}

func (x *PrimitivesItems) Reset() {
	*x = PrimitivesItems{}
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[43]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PrimitivesItems) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PrimitivesItems) ProtoMessage() {}

func (x *PrimitivesItems) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[43]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PrimitivesItems.ProtoReflect.Descriptor instead.
func (*PrimitivesItems) Descriptor() ([]byte, []int) {
	return file_openapiv2_OpenAPIv2_proto_rawDescGZIP(), []int{43}
}

func (x *PrimitivesItems) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *PrimitivesItems) GetFormat() string {
	if x != nil {
		return x.Format
	}
	return ""
}

func (x *PrimitivesItems) GetItems() *PrimitivesItems {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *PrimitivesItems) GetCollectionFormat() string {
	if x != nil {
		return x.CollectionFormat
	}
	return ""
}

func (x *PrimitivesItems) GetDefault() *Any {
	if x != nil {
		return x.Default
	}
	return nil
}

func (x *PrimitivesItems) GetMaximum() float64 {
	if x != nil {
		return x.Maximum
	}
	return 0
}

func (x *PrimitivesItems) GetExclusiveMaximum() bool {
	if x != nil {
		return x.ExclusiveMaximum
	}
	return false
}

func (x *PrimitivesItems) GetMinimum() float64 {
	if x != nil {
		return x.Minimum
	}
	return 0
}

func (x *PrimitivesItems) GetExclusiveMinimum() bool {
	if x != nil {
		return x.ExclusiveMinimum
	}
	return false
}

func (x *PrimitivesItems) GetMaxLength() int64 {
	if x != nil {
		return x.MaxLength
	}
	return 0
}

func (x *PrimitivesItems) GetMinLength() int64 {
	if x != nil {
		return x.MinLength
	}
	return 0
}

func (x *PrimitivesItems) GetPattern() string {
	if x != nil {
		return x.Pattern
	}
	return ""
}

func (x *PrimitivesItems) GetMaxItems() int64 {
	if x != nil {
		return x.MaxItems
	}
	return 0
}

func (x *PrimitivesItems) GetMinItems() int64 {
	if x != nil {
		return x.MinItems
	}
	return 0
}

func (x *PrimitivesItems) GetUniqueItems() bool {
	if x != nil {
		return x.UniqueItems
	}
	return false
}

func (x *PrimitivesItems) GetEnum() []*Any {
	if x != nil {
		return x.Enum
	}
	return nil
}

func (x *PrimitivesItems) GetMultipleOf() float64 {
	if x != nil {
		return x.MultipleOf
	}
	return 0
}

func (x *PrimitivesItems) GetVendorExtension() []*NamedAny {
	if x != nil {
		return x.VendorExtension
	}
	return nil
}

type Properties struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AdditionalProperties []*NamedSchema `protobuf:"bytes,1,rep,name=additional_properties,json=additionalProperties,proto3" json:"additional_properties,omitempty"`
}

func (x *Properties) Reset() {
	*x = Properties{}
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[44]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Properties) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Properties) ProtoMessage() {}

func (x *Properties) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[44]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Properties.ProtoReflect.Descriptor instead.
func (*Properties) Descriptor() ([]byte, []int) {
	return file_openapiv2_OpenAPIv2_proto_rawDescGZIP(), []int{44}
}

func (x *Properties) GetAdditionalProperties() []*NamedSchema {
	if x != nil {
		return x.AdditionalProperties
	}
	return nil
}

type QueryParameterSubSchema struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Determines whether or not this parameter is required or optional.
	Required bool `protobuf:"varint,1,opt,name=required,proto3" json:"required,omitempty"`
	// Determines the location of the parameter.
	In string `protobuf:"bytes,2,opt,name=in,proto3" json:"in,omitempty"`
	// A brief description of the parameter. This could contain examples of use.  GitHub Flavored Markdown is allowed.
	Description string `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	// The name of the parameter.
	Name string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	// allows sending a parameter by name only or with an empty value.
	AllowEmptyValue  bool             `protobuf:"varint,5,opt,name=allow_empty_value,json=allowEmptyValue,proto3" json:"allow_empty_value,omitempty"`
	Type             string           `protobuf:"bytes,6,opt,name=type,proto3" json:"type,omitempty"`
	Format           string           `protobuf:"bytes,7,opt,name=format,proto3" json:"format,omitempty"`
	Items            *PrimitivesItems `protobuf:"bytes,8,opt,name=items,proto3" json:"items,omitempty"`
	CollectionFormat string           `protobuf:"bytes,9,opt,name=collection_format,json=collectionFormat,proto3" json:"collection_format,omitempty"`
	Default          *Any             `protobuf:"bytes,10,opt,name=default,proto3" json:"default,omitempty"`
	Maximum          float64          `protobuf:"fixed64,11,opt,name=maximum,proto3" json:"maximum,omitempty"`
	ExclusiveMaximum bool             `protobuf:"varint,12,opt,name=exclusive_maximum,json=exclusiveMaximum,proto3" json:"exclusive_maximum,omitempty"`
	Minimum          float64          `protobuf:"fixed64,13,opt,name=minimum,proto3" json:"minimum,omitempty"`
	ExclusiveMinimum bool             `protobuf:"varint,14,opt,name=exclusive_minimum,json=exclusiveMinimum,proto3" json:"exclusive_minimum,omitempty"`
	MaxLength        int64            `protobuf:"varint,15,opt,name=max_length,json=maxLength,proto3" json:"max_length,omitempty"`
	MinLength        int64            `protobuf:"varint,16,opt,name=min_length,json=minLength,proto3" json:"min_length,omitempty"`
	Pattern          string           `protobuf:"bytes,17,opt,name=pattern,proto3" json:"pattern,omitempty"`
	MaxItems         int64            `protobuf:"varint,18,opt,name=max_items,json=maxItems,proto3" json:"max_items,omitempty"`
	MinItems         int64            `protobuf:"varint,19,opt,name=min_items,json=minItems,proto3" json:"min_items,omitempty"`
	UniqueItems      bool             `protobuf:"varint,20,opt,name=unique_items,json=uniqueItems,proto3" json:"unique_items,omitempty"`
	Enum             []*Any           `protobuf:"bytes,21,rep,name=enum,proto3" json:"enum,omitempty"`
	MultipleOf       float64          `protobuf:"fixed64,22,opt,name=multiple_of,json=multipleOf,proto3" json:"multiple_of,omitempty"`
	VendorExtension  []*NamedAny      `protobuf:"bytes,23,rep,name=vendor_extension,json=vendorExtension,proto3" json:"vendor_extension,omitempty"`
}

func (x *QueryParameterSubSchema) Reset() {
	*x = QueryParameterSubSchema{}
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[45]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueryParameterSubSchema) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryParameterSubSchema) ProtoMessage() {}

func (x *QueryParameterSubSchema) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[45]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryParameterSubSchema.ProtoReflect.Descriptor instead.
func (*QueryParameterSubSchema) Descriptor() ([]byte, []int) {
	return file_openapiv2_OpenAPIv2_proto_rawDescGZIP(), []int{45}
}

func (x *QueryParameterSubSchema) GetRequired() bool {
	if x != nil {
		return x.Required
	}
	return false
}

func (x *QueryParameterSubSchema) GetIn() string {
	if x != nil {
		return x.In
	}
	return ""
}

func (x *QueryParameterSubSchema) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *QueryParameterSubSchema) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *QueryParameterSubSchema) GetAllowEmptyValue() bool {
	if x != nil {
		return x.AllowEmptyValue
	}
	return false
}

func (x *QueryParameterSubSchema) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *QueryParameterSubSchema) GetFormat() string {
	if x != nil {
		return x.Format
	}
	return ""
}

func (x *QueryParameterSubSchema) GetItems() *PrimitivesItems {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *QueryParameterSubSchema) GetCollectionFormat() string {
	if x != nil {
		return x.CollectionFormat
	}
	return ""
}

func (x *QueryParameterSubSchema) GetDefault() *Any {
	if x != nil {
		return x.Default
	}
	return nil
}

func (x *QueryParameterSubSchema) GetMaximum() float64 {
	if x != nil {
		return x.Maximum
	}
	return 0
}

func (x *QueryParameterSubSchema) GetExclusiveMaximum() bool {
	if x != nil {
		return x.ExclusiveMaximum
	}
	return false
}

func (x *QueryParameterSubSchema) GetMinimum() float64 {
	if x != nil {
		return x.Minimum
	}
	return 0
}

func (x *QueryParameterSubSchema) GetExclusiveMinimum() bool {
	if x != nil {
		return x.ExclusiveMinimum
	}
	return false
}

func (x *QueryParameterSubSchema) GetMaxLength() int64 {
	if x != nil {
		return x.MaxLength
	}
	return 0
}

func (x *QueryParameterSubSchema) GetMinLength() int64 {
	if x != nil {
		return x.MinLength
	}
	return 0
}

func (x *QueryParameterSubSchema) GetPattern() string {
	if x != nil {
		return x.Pattern
	}
	return ""
}

func (x *QueryParameterSubSchema) GetMaxItems() int64 {
	if x != nil {
		return x.MaxItems
	}
	return 0
}

func (x *QueryParameterSubSchema) GetMinItems() int64 {
	if x != nil {
		return x.MinItems
	}
	return 0
}

func (x *QueryParameterSubSchema) GetUniqueItems() bool {
	if x != nil {
		return x.UniqueItems
	}
	return false
}

func (x *QueryParameterSubSchema) GetEnum() []*Any {
	if x != nil {
		return x.Enum
	}
	return nil
}

func (x *QueryParameterSubSchema) GetMultipleOf() float64 {
	if x != nil {
		return x.MultipleOf
	}
	return 0
}

func (x *QueryParameterSubSchema) GetVendorExtension() []*NamedAny {
	if x != nil {
		return x.VendorExtension
	}
	return nil
}

type Response struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Description     string      `protobuf:"bytes,1,opt,name=description,proto3" json:"description,omitempty"`
	Schema          *SchemaItem `protobuf:"bytes,2,opt,name=schema,proto3" json:"schema,omitempty"`
	Headers         *Headers    `protobuf:"bytes,3,opt,name=headers,proto3" json:"headers,omitempty"`
	Examples        *Examples   `protobuf:"bytes,4,opt,name=examples,proto3" json:"examples,omitempty"`
	VendorExtension []*NamedAny `protobuf:"bytes,5,rep,name=vendor_extension,json=vendorExtension,proto3" json:"vendor_extension,omitempty"`
}

func (x *Response) Reset() {
	*x = Response{}
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[46]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Response) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Response) ProtoMessage() {}

func (x *Response) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[46]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Response.ProtoReflect.Descriptor instead.
func (*Response) Descriptor() ([]byte, []int) {
	return file_openapiv2_OpenAPIv2_proto_rawDescGZIP(), []int{46}
}

func (x *Response) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Response) GetSchema() *SchemaItem {
	if x != nil {
		return x.Schema
	}
	return nil
}

func (x *Response) GetHeaders() *Headers {
	if x != nil {
		return x.Headers
	}
	return nil
}

func (x *Response) GetExamples() *Examples {
	if x != nil {
		return x.Examples
	}
	return nil
}

func (x *Response) GetVendorExtension() []*NamedAny {
	if x != nil {
		return x.VendorExtension
	}
	return nil
}

// One or more JSON representations for responses
type ResponseDefinitions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AdditionalProperties []*NamedResponse `protobuf:"bytes,1,rep,name=additional_properties,json=additionalProperties,proto3" json:"additional_properties,omitempty"`
}

func (x *ResponseDefinitions) Reset() {
	*x = ResponseDefinitions{}
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[47]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResponseDefinitions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResponseDefinitions) ProtoMessage() {}

func (x *ResponseDefinitions) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[47]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResponseDefinitions.ProtoReflect.Descriptor instead.
func (*ResponseDefinitions) Descriptor() ([]byte, []int) {
	return file_openapiv2_OpenAPIv2_proto_rawDescGZIP(), []int{47}
}

func (x *ResponseDefinitions) GetAdditionalProperties() []*NamedResponse {
	if x != nil {
		return x.AdditionalProperties
	}
	return nil
}

type ResponseValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Oneof:
	//
	//	*ResponseValue_Response
	//	*ResponseValue_JsonReference
	Oneof isResponseValue_Oneof `protobuf_oneof:"oneof"`
}

func (x *ResponseValue) Reset() {
	*x = ResponseValue{}
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[48]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResponseValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResponseValue) ProtoMessage() {}

func (x *ResponseValue) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[48]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResponseValue.ProtoReflect.Descriptor instead.
func (*ResponseValue) Descriptor() ([]byte, []int) {
	return file_openapiv2_OpenAPIv2_proto_rawDescGZIP(), []int{48}
}

func (m *ResponseValue) GetOneof() isResponseValue_Oneof {
	if m != nil {
		return m.Oneof
	}
	return nil
}

func (x *ResponseValue) GetResponse() *Response {
	if x, ok := x.GetOneof().(*ResponseValue_Response); ok {
		return x.Response
	}
	return nil
}

func (x *ResponseValue) GetJsonReference() *JsonReference {
	if x, ok := x.GetOneof().(*ResponseValue_JsonReference); ok {
		return x.JsonReference
	}
	return nil
}

type isResponseValue_Oneof interface {
	isResponseValue_Oneof()
}

type ResponseValue_Response struct {
	Response *Response `protobuf:"bytes,1,opt,name=response,proto3,oneof"`
}

type ResponseValue_JsonReference struct {
	JsonReference *JsonReference `protobuf:"bytes,2,opt,name=json_reference,json=jsonReference,proto3,oneof"`
}

func (*ResponseValue_Response) isResponseValue_Oneof() {}

func (*ResponseValue_JsonReference) isResponseValue_Oneof() {}

// Response objects names can either be any valid HTTP status code or 'default'.
type Responses struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseCode    []*NamedResponseValue `protobuf:"bytes,1,rep,name=response_code,json=responseCode,proto3" json:"response_code,omitempty"`
	VendorExtension []*NamedAny           `protobuf:"bytes,2,rep,name=vendor_extension,json=vendorExtension,proto3" json:"vendor_extension,omitempty"`
}

func (x *Responses) Reset() {
	*x = Responses{}
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[49]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Responses) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Responses) ProtoMessage() {}

func (x *Responses) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[49]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Responses.ProtoReflect.Descriptor instead.
func (*Responses) Descriptor() ([]byte, []int) {
	return file_openapiv2_OpenAPIv2_proto_rawDescGZIP(), []int{49}
}

func (x *Responses) GetResponseCode() []*NamedResponseValue {
	if x != nil {
		return x.ResponseCode
	}
	return nil
}

func (x *Responses) GetVendorExtension() []*NamedAny {
	if x != nil {
		return x.VendorExtension
	}
	return nil
}

// A deterministic version of a JSON Schema object.
type Schema struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	XRef                 string                    `protobuf:"bytes,1,opt,name=_ref,json=Ref,proto3" json:"_ref,omitempty"`
	Format               string                    `protobuf:"bytes,2,opt,name=format,proto3" json:"format,omitempty"`
	Title                string                    `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	Description          string                    `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	Default              *Any                      `protobuf:"bytes,5,opt,name=default,proto3" json:"default,omitempty"`
	MultipleOf           float64                   `protobuf:"fixed64,6,opt,name=multiple_of,json=multipleOf,proto3" json:"multiple_of,omitempty"`
	Maximum              float64                   `protobuf:"fixed64,7,opt,name=maximum,proto3" json:"maximum,omitempty"`
	ExclusiveMaximum     bool                      `protobuf:"varint,8,opt,name=exclusive_maximum,json=exclusiveMaximum,proto3" json:"exclusive_maximum,omitempty"`
	Minimum              float64                   `protobuf:"fixed64,9,opt,name=minimum,proto3" json:"minimum,omitempty"`
	ExclusiveMinimum     bool                      `protobuf:"varint,10,opt,name=exclusive_minimum,json=exclusiveMinimum,proto3" json:"exclusive_minimum,omitempty"`
	MaxLength            int64                     `protobuf:"varint,11,opt,name=max_length,json=maxLength,proto3" json:"max_length,omitempty"`
	MinLength            int64                     `protobuf:"varint,12,opt,name=min_length,json=minLength,proto3" json:"min_length,omitempty"`
	Pattern              string                    `protobuf:"bytes,13,opt,name=pattern,proto3" json:"pattern,omitempty"`
	MaxItems             int64                     `protobuf:"varint,14,opt,name=max_items,json=maxItems,proto3" json:"max_items,omitempty"`
	MinItems             int64                     `protobuf:"varint,15,opt,name=min_items,json=minItems,proto3" json:"min_items,omitempty"`
	UniqueItems          bool                      `protobuf:"varint,16,opt,name=unique_items,json=uniqueItems,proto3" json:"unique_items,omitempty"`
	MaxProperties        int64                     `protobuf:"varint,17,opt,name=max_properties,json=maxProperties,proto3" json:"max_properties,omitempty"`
	MinProperties        int64                     `protobuf:"varint,18,opt,name=min_properties,json=minProperties,proto3" json:"min_properties,omitempty"`
	Required             []string                  `protobuf:"bytes,19,rep,name=required,proto3" json:"required,omitempty"`
	Enum                 []*Any                    `protobuf:"bytes,20,rep,name=enum,proto3" json:"enum,omitempty"`
	AdditionalProperties *AdditionalPropertiesItem `protobuf:"bytes,21,opt,name=additional_properties,json=additionalProperties,proto3" json:"additional_properties,omitempty"`
	Type                 *TypeItem                 `protobuf:"bytes,22,opt,name=type,proto3" json:"type,omitempty"`
	Items                *ItemsItem                `protobuf:"bytes,23,opt,name=items,proto3" json:"items,omitempty"`
	AllOf                []*Schema                 `protobuf:"bytes,24,rep,name=all_of,json=allOf,proto3" json:"all_of,omitempty"`
	Properties           *Properties               `protobuf:"bytes,25,opt,name=properties,proto3" json:"properties,omitempty"`
	Discriminator        string                    `protobuf:"bytes,26,opt,name=discriminator,proto3" json:"discriminator,omitempty"`
	ReadOnly             bool                      `protobuf:"varint,27,opt,name=read_only,json=readOnly,proto3" json:"read_only,omitempty"`
	Xml                  *Xml                      `protobuf:"bytes,28,opt,name=xml,proto3" json:"xml,omitempty"`
	ExternalDocs         *ExternalDocs             `protobuf:"bytes,29,opt,name=external_docs,json=externalDocs,proto3" json:"external_docs,omitempty"`
	Example              *Any                      `protobuf:"bytes,30,opt,name=example,proto3" json:"example,omitempty"`
	VendorExtension      []*NamedAny               `protobuf:"bytes,31,rep,name=vendor_extension,json=vendorExtension,proto3" json:"vendor_extension,omitempty"`
}

func (x *Schema) Reset() {
	*x = Schema{}
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[50]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Schema) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Schema) ProtoMessage() {}

func (x *Schema) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[50]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Schema.ProtoReflect.Descriptor instead.
func (*Schema) Descriptor() ([]byte, []int) {
	return file_openapiv2_OpenAPIv2_proto_rawDescGZIP(), []int{50}
}

func (x *Schema) GetXRef() string {
	if x != nil {
		return x.XRef
	}
	return ""
}

func (x *Schema) GetFormat() string {
	if x != nil {
		return x.Format
	}
	return ""
}

func (x *Schema) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *Schema) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Schema) GetDefault() *Any {
	if x != nil {
		return x.Default
	}
	return nil
}

func (x *Schema) GetMultipleOf() float64 {
	if x != nil {
		return x.MultipleOf
	}
	return 0
}

func (x *Schema) GetMaximum() float64 {
	if x != nil {
		return x.Maximum
	}
	return 0
}

func (x *Schema) GetExclusiveMaximum() bool {
	if x != nil {
		return x.ExclusiveMaximum
	}
	return false
}

func (x *Schema) GetMinimum() float64 {
	if x != nil {
		return x.Minimum
	}
	return 0
}

func (x *Schema) GetExclusiveMinimum() bool {
	if x != nil {
		return x.ExclusiveMinimum
	}
	return false
}

func (x *Schema) GetMaxLength() int64 {
	if x != nil {
		return x.MaxLength
	}
	return 0
}

func (x *Schema) GetMinLength() int64 {
	if x != nil {
		return x.MinLength
	}
	return 0
}

func (x *Schema) GetPattern() string {
	if x != nil {
		return x.Pattern
	}
	return ""
}

func (x *Schema) GetMaxItems() int64 {
	if x != nil {
		return x.MaxItems
	}
	return 0
}

func (x *Schema) GetMinItems() int64 {
	if x != nil {
		return x.MinItems
	}
	return 0
}

func (x *Schema) GetUniqueItems() bool {
	if x != nil {
		return x.UniqueItems
	}
	return false
}

func (x *Schema) GetMaxProperties() int64 {
	if x != nil {
		return x.MaxProperties
	}
	return 0
}

func (x *Schema) GetMinProperties() int64 {
	if x != nil {
		return x.MinProperties
	}
	return 0
}

func (x *Schema) GetRequired() []string {
	if x != nil {
		return x.Required
	}
	return nil
}

func (x *Schema) GetEnum() []*Any {
	if x != nil {
		return x.Enum
	}
	return nil
}

func (x *Schema) GetAdditionalProperties() *AdditionalPropertiesItem {
	if x != nil {
		return x.AdditionalProperties
	}
	return nil
}

func (x *Schema) GetType() *TypeItem {
	if x != nil {
		return x.Type
	}
	return nil
}

func (x *Schema) GetItems() *ItemsItem {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *Schema) GetAllOf() []*Schema {
	if x != nil {
		return x.AllOf
	}
	return nil
}

func (x *Schema) GetProperties() *Properties {
	if x != nil {
		return x.Properties
	}
	return nil
}

func (x *Schema) GetDiscriminator() string {
	if x != nil {
		return x.Discriminator
	}
	return ""
}

func (x *Schema) GetReadOnly() bool {
	if x != nil {
		return x.ReadOnly
	}
	return false
}

func (x *Schema) GetXml() *Xml {
	if x != nil {
		return x.Xml
	}
	return nil
}

func (x *Schema) GetExternalDocs() *ExternalDocs {
	if x != nil {
		return x.ExternalDocs
	}
	return nil
}

func (x *Schema) GetExample() *Any {
	if x != nil {
		return x.Example
	}
	return nil
}

func (x *Schema) GetVendorExtension() []*NamedAny {
	if x != nil {
		return x.VendorExtension
	}
	return nil
}

type SchemaItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Oneof:
	//
	//	*SchemaItem_Schema
	//	*SchemaItem_FileSchema
	Oneof isSchemaItem_Oneof `protobuf_oneof:"oneof"`
}

func (x *SchemaItem) Reset() {
	*x = SchemaItem{}
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[51]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SchemaItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SchemaItem) ProtoMessage() {}

func (x *SchemaItem) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[51]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SchemaItem.ProtoReflect.Descriptor instead.
func (*SchemaItem) Descriptor() ([]byte, []int) {
	return file_openapiv2_OpenAPIv2_proto_rawDescGZIP(), []int{51}
}

func (m *SchemaItem) GetOneof() isSchemaItem_Oneof {
	if m != nil {
		return m.Oneof
	}
	return nil
}

func (x *SchemaItem) GetSchema() *Schema {
	if x, ok := x.GetOneof().(*SchemaItem_Schema); ok {
		return x.Schema
	}
	return nil
}

func (x *SchemaItem) GetFileSchema() *FileSchema {
	if x, ok := x.GetOneof().(*SchemaItem_FileSchema); ok {
		return x.FileSchema
	}
	return nil
}

type isSchemaItem_Oneof interface {
	isSchemaItem_Oneof()
}

type SchemaItem_Schema struct {
	Schema *Schema `protobuf:"bytes,1,opt,name=schema,proto3,oneof"`
}

type SchemaItem_FileSchema struct {
	FileSchema *FileSchema `protobuf:"bytes,2,opt,name=file_schema,json=fileSchema,proto3,oneof"`
}

func (*SchemaItem_Schema) isSchemaItem_Oneof() {}

func (*SchemaItem_FileSchema) isSchemaItem_Oneof() {}

type SecurityDefinitions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AdditionalProperties []*NamedSecurityDefinitionsItem `protobuf:"bytes,1,rep,name=additional_properties,json=additionalProperties,proto3" json:"additional_properties,omitempty"`
}

func (x *SecurityDefinitions) Reset() {
	*x = SecurityDefinitions{}
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[52]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SecurityDefinitions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SecurityDefinitions) ProtoMessage() {}

func (x *SecurityDefinitions) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[52]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SecurityDefinitions.ProtoReflect.Descriptor instead.
func (*SecurityDefinitions) Descriptor() ([]byte, []int) {
	return file_openapiv2_OpenAPIv2_proto_rawDescGZIP(), []int{52}
}

func (x *SecurityDefinitions) GetAdditionalProperties() []*NamedSecurityDefinitionsItem {
	if x != nil {
		return x.AdditionalProperties
	}
	return nil
}

type SecurityDefinitionsItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Oneof:
	//
	//	*SecurityDefinitionsItem_BasicAuthenticationSecurity
	//	*SecurityDefinitionsItem_ApiKeySecurity
	//	*SecurityDefinitionsItem_Oauth2ImplicitSecurity
	//	*SecurityDefinitionsItem_Oauth2PasswordSecurity
	//	*SecurityDefinitionsItem_Oauth2ApplicationSecurity
	//	*SecurityDefinitionsItem_Oauth2AccessCodeSecurity
	Oneof isSecurityDefinitionsItem_Oneof `protobuf_oneof:"oneof"`
}

func (x *SecurityDefinitionsItem) Reset() {
	*x = SecurityDefinitionsItem{}
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[53]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SecurityDefinitionsItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SecurityDefinitionsItem) ProtoMessage() {}

func (x *SecurityDefinitionsItem) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[53]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SecurityDefinitionsItem.ProtoReflect.Descriptor instead.
func (*SecurityDefinitionsItem) Descriptor() ([]byte, []int) {
	return file_openapiv2_OpenAPIv2_proto_rawDescGZIP(), []int{53}
}

func (m *SecurityDefinitionsItem) GetOneof() isSecurityDefinitionsItem_Oneof {
	if m != nil {
		return m.Oneof
	}
	return nil
}

func (x *SecurityDefinitionsItem) GetBasicAuthenticationSecurity() *BasicAuthenticationSecurity {
	if x, ok := x.GetOneof().(*SecurityDefinitionsItem_BasicAuthenticationSecurity); ok {
		return x.BasicAuthenticationSecurity
	}
	return nil
}

func (x *SecurityDefinitionsItem) GetApiKeySecurity() *ApiKeySecurity {
	if x, ok := x.GetOneof().(*SecurityDefinitionsItem_ApiKeySecurity); ok {
		return x.ApiKeySecurity
	}
	return nil
}

func (x *SecurityDefinitionsItem) GetOauth2ImplicitSecurity() *Oauth2ImplicitSecurity {
	if x, ok := x.GetOneof().(*SecurityDefinitionsItem_Oauth2ImplicitSecurity); ok {
		return x.Oauth2ImplicitSecurity
	}
	return nil
}

func (x *SecurityDefinitionsItem) GetOauth2PasswordSecurity() *Oauth2PasswordSecurity {
	if x, ok := x.GetOneof().(*SecurityDefinitionsItem_Oauth2PasswordSecurity); ok {
		return x.Oauth2PasswordSecurity
	}
	return nil
}

func (x *SecurityDefinitionsItem) GetOauth2ApplicationSecurity() *Oauth2ApplicationSecurity {
	if x, ok := x.GetOneof().(*SecurityDefinitionsItem_Oauth2ApplicationSecurity); ok {
		return x.Oauth2ApplicationSecurity
	}
	return nil
}

func (x *SecurityDefinitionsItem) GetOauth2AccessCodeSecurity() *Oauth2AccessCodeSecurity {
	if x, ok := x.GetOneof().(*SecurityDefinitionsItem_Oauth2AccessCodeSecurity); ok {
		return x.Oauth2AccessCodeSecurity
	}
	return nil
}

type isSecurityDefinitionsItem_Oneof interface {
	isSecurityDefinitionsItem_Oneof()
}

type SecurityDefinitionsItem_BasicAuthenticationSecurity struct {
	BasicAuthenticationSecurity *BasicAuthenticationSecurity `protobuf:"bytes,1,opt,name=basic_authentication_security,json=basicAuthenticationSecurity,proto3,oneof"`
}

type SecurityDefinitionsItem_ApiKeySecurity struct {
	ApiKeySecurity *ApiKeySecurity `protobuf:"bytes,2,opt,name=api_key_security,json=apiKeySecurity,proto3,oneof"`
}

type SecurityDefinitionsItem_Oauth2ImplicitSecurity struct {
	Oauth2ImplicitSecurity *Oauth2ImplicitSecurity `protobuf:"bytes,3,opt,name=oauth2_implicit_security,json=oauth2ImplicitSecurity,proto3,oneof"`
}

type SecurityDefinitionsItem_Oauth2PasswordSecurity struct {
	Oauth2PasswordSecurity *Oauth2PasswordSecurity `protobuf:"bytes,4,opt,name=oauth2_password_security,json=oauth2PasswordSecurity,proto3,oneof"`
}

type SecurityDefinitionsItem_Oauth2ApplicationSecurity struct {
	Oauth2ApplicationSecurity *Oauth2ApplicationSecurity `protobuf:"bytes,5,opt,name=oauth2_application_security,json=oauth2ApplicationSecurity,proto3,oneof"`
}

type SecurityDefinitionsItem_Oauth2AccessCodeSecurity struct {
	Oauth2AccessCodeSecurity *Oauth2AccessCodeSecurity `protobuf:"bytes,6,opt,name=oauth2_access_code_security,json=oauth2AccessCodeSecurity,proto3,oneof"`
}

func (*SecurityDefinitionsItem_BasicAuthenticationSecurity) isSecurityDefinitionsItem_Oneof() {}

func (*SecurityDefinitionsItem_ApiKeySecurity) isSecurityDefinitionsItem_Oneof() {}

func (*SecurityDefinitionsItem_Oauth2ImplicitSecurity) isSecurityDefinitionsItem_Oneof() {}

func (*SecurityDefinitionsItem_Oauth2PasswordSecurity) isSecurityDefinitionsItem_Oneof() {}

func (*SecurityDefinitionsItem_Oauth2ApplicationSecurity) isSecurityDefinitionsItem_Oneof() {}

func (*SecurityDefinitionsItem_Oauth2AccessCodeSecurity) isSecurityDefinitionsItem_Oneof() {}

type SecurityRequirement struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AdditionalProperties []*NamedStringArray `protobuf:"bytes,1,rep,name=additional_properties,json=additionalProperties,proto3" json:"additional_properties,omitempty"`
}

func (x *SecurityRequirement) Reset() {
	*x = SecurityRequirement{}
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[54]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SecurityRequirement) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SecurityRequirement) ProtoMessage() {}

func (x *SecurityRequirement) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[54]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SecurityRequirement.ProtoReflect.Descriptor instead.
func (*SecurityRequirement) Descriptor() ([]byte, []int) {
	return file_openapiv2_OpenAPIv2_proto_rawDescGZIP(), []int{54}
}

func (x *SecurityRequirement) GetAdditionalProperties() []*NamedStringArray {
	if x != nil {
		return x.AdditionalProperties
	}
	return nil
}

type StringArray struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value []string `protobuf:"bytes,1,rep,name=value,proto3" json:"value,omitempty"`
}

func (x *StringArray) Reset() {
	*x = StringArray{}
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[55]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StringArray) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringArray) ProtoMessage() {}

func (x *StringArray) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[55]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringArray.ProtoReflect.Descriptor instead.
func (*StringArray) Descriptor() ([]byte, []int) {
	return file_openapiv2_OpenAPIv2_proto_rawDescGZIP(), []int{55}
}

func (x *StringArray) GetValue() []string {
	if x != nil {
		return x.Value
	}
	return nil
}

type Tag struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name            string        `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Description     string        `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	ExternalDocs    *ExternalDocs `protobuf:"bytes,3,opt,name=external_docs,json=externalDocs,proto3" json:"external_docs,omitempty"`
	VendorExtension []*NamedAny   `protobuf:"bytes,4,rep,name=vendor_extension,json=vendorExtension,proto3" json:"vendor_extension,omitempty"`
}

func (x *Tag) Reset() {
	*x = Tag{}
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[56]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Tag) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Tag) ProtoMessage() {}

func (x *Tag) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[56]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Tag.ProtoReflect.Descriptor instead.
func (*Tag) Descriptor() ([]byte, []int) {
	return file_openapiv2_OpenAPIv2_proto_rawDescGZIP(), []int{56}
}

func (x *Tag) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Tag) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Tag) GetExternalDocs() *ExternalDocs {
	if x != nil {
		return x.ExternalDocs
	}
	return nil
}

func (x *Tag) GetVendorExtension() []*NamedAny {
	if x != nil {
		return x.VendorExtension
	}
	return nil
}

type TypeItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value []string `protobuf:"bytes,1,rep,name=value,proto3" json:"value,omitempty"`
}

func (x *TypeItem) Reset() {
	*x = TypeItem{}
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[57]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TypeItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TypeItem) ProtoMessage() {}

func (x *TypeItem) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[57]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TypeItem.ProtoReflect.Descriptor instead.
func (*TypeItem) Descriptor() ([]byte, []int) {
	return file_openapiv2_OpenAPIv2_proto_rawDescGZIP(), []int{57}
}

func (x *TypeItem) GetValue() []string {
	if x != nil {
		return x.Value
	}
	return nil
}

// Any property starting with x- is valid.
type VendorExtension struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AdditionalProperties []*NamedAny `protobuf:"bytes,1,rep,name=additional_properties,json=additionalProperties,proto3" json:"additional_properties,omitempty"`
}

func (x *VendorExtension) Reset() {
	*x = VendorExtension{}
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[58]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VendorExtension) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VendorExtension) ProtoMessage() {}

func (x *VendorExtension) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[58]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VendorExtension.ProtoReflect.Descriptor instead.
func (*VendorExtension) Descriptor() ([]byte, []int) {
	return file_openapiv2_OpenAPIv2_proto_rawDescGZIP(), []int{58}
}

func (x *VendorExtension) GetAdditionalProperties() []*NamedAny {
	if x != nil {
		return x.AdditionalProperties
	}
	return nil
}

type Xml struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name            string      `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Namespace       string      `protobuf:"bytes,2,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Prefix          string      `protobuf:"bytes,3,opt,name=prefix,proto3" json:"prefix,omitempty"`
	Attribute       bool        `protobuf:"varint,4,opt,name=attribute,proto3" json:"attribute,omitempty"`
	Wrapped         bool        `protobuf:"varint,5,opt,name=wrapped,proto3" json:"wrapped,omitempty"`
	VendorExtension []*NamedAny `protobuf:"bytes,6,rep,name=vendor_extension,json=vendorExtension,proto3" json:"vendor_extension,omitempty"`
}

func (x *Xml) Reset() {
	*x = Xml{}
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[59]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Xml) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Xml) ProtoMessage() {}

func (x *Xml) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv2_OpenAPIv2_proto_msgTypes[59]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Xml.ProtoReflect.Descriptor instead.
func (*Xml) Descriptor() ([]byte, []int) {
	return file_openapiv2_OpenAPIv2_proto_rawDescGZIP(), []int{59}
}

func (x *Xml) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Xml) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *Xml) GetPrefix() string {
	if x != nil {
		return x.Prefix
	}
	return ""
}

func (x *Xml) GetAttribute() bool {
	if x != nil {
		return x.Attribute
	}
	return false
}

func (x *Xml) GetWrapped() bool {
	if x != nil {
		return x.Wrapped
	}
	return false
}

func (x *Xml) GetVendorExtension() []*NamedAny {
	if x != nil {
		return x.VendorExtension
	}
	return nil
}

var File_openapiv2_OpenAPIv2_proto protoreflect.FileDescriptor

var file_openapiv2_OpenAPIv2_proto_rawDesc = []byte{
	0x0a, 0x19, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x76, 0x32, 0x2f, 0x4f, 0x70, 0x65, 0x6e,
	0x41, 0x50, 0x49, 0x76, 0x32, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x6f, 0x70, 0x65,
	0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x1a, 0x19, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x61, 0x6e, 0x79, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0x6d, 0x0a, 0x18, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x2c,
	0x0a, 0x06, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x53, 0x63, 0x68, 0x65,
	0x6d, 0x61, 0x48, 0x00, 0x52, 0x06, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x12, 0x1a, 0x0a, 0x07,
	0x62, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52,
	0x07, 0x62, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x42, 0x07, 0x0a, 0x05, 0x6f, 0x6e, 0x65, 0x6f,
	0x66, 0x22, 0x45, 0x0a, 0x03, 0x41, 0x6e, 0x79, 0x12, 0x2a, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x41, 0x6e, 0x79, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x79, 0x61, 0x6d, 0x6c, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x79, 0x61, 0x6d, 0x6c, 0x22, 0xab, 0x01, 0x0a, 0x0e, 0x41, 0x70, 0x69,
	0x4b, 0x65, 0x79, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3f, 0x0a, 0x10, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f,
	0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x14, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x4e, 0x61, 0x6d,
	0x65, 0x64, 0x41, 0x6e, 0x79, 0x52, 0x0f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x45, 0x78, 0x74,
	0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x94, 0x01, 0x0a, 0x1b, 0x42, 0x61, 0x73, 0x69, 0x63,
	0x41, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65,
	0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3f, 0x0a, 0x10,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69,
	0x2e, 0x76, 0x32, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x41, 0x6e, 0x79, 0x52, 0x0f, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0xde, 0x01,
	0x0a, 0x0d, 0x42, 0x6f, 0x64, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x12,
	0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x12, 0x2a, 0x0a, 0x06, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x53,
	0x63, 0x68, 0x65, 0x6d, 0x61, 0x52, 0x06, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x12, 0x3f, 0x0a,
	0x10, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70,
	0x69, 0x2e, 0x76, 0x32, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x41, 0x6e, 0x79, 0x52, 0x0f, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x86,
	0x01, 0x0a, 0x07, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x10,
	0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c,
	0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x3f, 0x0a, 0x10, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x14, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x4e, 0x61,
	0x6d, 0x65, 0x64, 0x41, 0x6e, 0x79, 0x52, 0x0f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x45, 0x78,
	0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x54, 0x0a, 0x07, 0x44, 0x65, 0x66, 0x61, 0x75,
	0x6c, 0x74, 0x12, 0x49, 0x0a, 0x15, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x5f, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x14, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x4e,
	0x61, 0x6d, 0x65, 0x64, 0x41, 0x6e, 0x79, 0x52, 0x14, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x22, 0x5b, 0x0a,
	0x0b, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x4c, 0x0a, 0x15,
	0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x70, 0x72, 0x6f, 0x70, 0x65,
	0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6f, 0x70,
	0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x53, 0x63,
	0x68, 0x65, 0x6d, 0x61, 0x52, 0x14, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x22, 0xe8, 0x05, 0x0a, 0x08, 0x44,
	0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x77, 0x61, 0x67, 0x67,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x77, 0x61, 0x67, 0x67, 0x65,
	0x72, 0x12, 0x24, 0x0a, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x10, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x62,
	0x61, 0x73, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x62, 0x61, 0x73, 0x65, 0x50, 0x61, 0x74, 0x68, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x63, 0x68, 0x65,
	0x6d, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x73, 0x63, 0x68, 0x65, 0x6d,
	0x65, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x73, 0x18, 0x06,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x73, 0x12, 0x1a,
	0x0a, 0x08, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x65, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x08, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x65, 0x73, 0x12, 0x27, 0x0a, 0x05, 0x70, 0x61,
	0x74, 0x68, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x6f, 0x70, 0x65, 0x6e,
	0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x74, 0x68, 0x73, 0x52, 0x05, 0x70, 0x61,
	0x74, 0x68, 0x73, 0x12, 0x39, 0x0a, 0x0b, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61,
	0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x52, 0x0b, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x40,
	0x0a, 0x0a, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x20, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x52, 0x0a, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73,
	0x12, 0x3d, 0x0a, 0x09, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x73, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32,
	0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x52, 0x09, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x73, 0x12,
	0x3b, 0x0a, 0x08, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x18, 0x0c, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1f, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x53,
	0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x52, 0x08, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x12, 0x52, 0x0a, 0x14,
	0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x6f, 0x70, 0x65,
	0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79,
	0x44, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x13, 0x73, 0x65, 0x63,
	0x75, 0x72, 0x69, 0x74, 0x79, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x12, 0x23, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f,
	0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x54, 0x61, 0x67, 0x52,
	0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x3d, 0x0a, 0x0d, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61,
	0x6c, 0x5f, 0x64, 0x6f, 0x63, 0x73, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x6f,
	0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6c, 0x44, 0x6f, 0x63, 0x73, 0x52, 0x0c, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x44, 0x6f, 0x63, 0x73, 0x12, 0x3f, 0x0a, 0x10, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x65,
	0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x10, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14,
	0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x4e, 0x61, 0x6d, 0x65,
	0x64, 0x41, 0x6e, 0x79, 0x52, 0x0f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x45, 0x78, 0x74, 0x65,
	0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x55, 0x0a, 0x08, 0x45, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65,
	0x73, 0x12, 0x49, 0x0a, 0x15, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f,
	0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x14, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x4e, 0x61,
	0x6d, 0x65, 0x64, 0x41, 0x6e, 0x79, 0x52, 0x14, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x22, 0x83, 0x01, 0x0a,
	0x0c, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x44, 0x6f, 0x63, 0x73, 0x12, 0x20, 0x0a,
	0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72,
	0x6c, 0x12, 0x3f, 0x0a, 0x10, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x65, 0x78, 0x74, 0x65,
	0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6f, 0x70,
	0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x41, 0x6e,
	0x79, 0x52, 0x0f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69,
	0x6f, 0x6e, 0x22, 0xff, 0x02, 0x0a, 0x0a, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x63, 0x68, 0x65, 0x6d,
	0x61, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74,
	0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12,
	0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x29, 0x0a, 0x07, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e,
	0x41, 0x6e, 0x79, 0x52, 0x07, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x12, 0x1a, 0x0a, 0x08,
	0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08,
	0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09,
	0x72, 0x65, 0x61, 0x64, 0x5f, 0x6f, 0x6e, 0x6c, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x08, 0x72, 0x65, 0x61, 0x64, 0x4f, 0x6e, 0x6c, 0x79, 0x12, 0x3d, 0x0a, 0x0d, 0x65, 0x78, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x64, 0x6f, 0x63, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x45, 0x78,
	0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x44, 0x6f, 0x63, 0x73, 0x52, 0x0c, 0x65, 0x78, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x6c, 0x44, 0x6f, 0x63, 0x73, 0x12, 0x29, 0x0a, 0x07, 0x65, 0x78, 0x61, 0x6d,
	0x70, 0x6c, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x6f, 0x70, 0x65, 0x6e,
	0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x41, 0x6e, 0x79, 0x52, 0x07, 0x65, 0x78, 0x61, 0x6d,
	0x70, 0x6c, 0x65, 0x12, 0x3f, 0x0a, 0x10, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x65, 0x78,
	0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e,
	0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x64,
	0x41, 0x6e, 0x79, 0x52, 0x0f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x45, 0x78, 0x74, 0x65, 0x6e,
	0x73, 0x69, 0x6f, 0x6e, 0x22, 0xab, 0x06, 0x0a, 0x1a, 0x46, 0x6f, 0x72, 0x6d, 0x44, 0x61, 0x74,
	0x61, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x53, 0x75, 0x62, 0x53, 0x63, 0x68,
	0x65, 0x6d, 0x61, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x6e, 0x12,
	0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x65,
	0x6d, 0x70, 0x74, 0x79, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0f, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x12, 0x31, 0x0a,
	0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x6f,
	0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x72, 0x69, 0x6d, 0x69, 0x74,
	0x69, 0x76, 0x65, 0x73, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73,
	0x12, 0x2b, 0x0a, 0x11, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x66,
	0x6f, 0x72, 0x6d, 0x61, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x63, 0x6f, 0x6c,
	0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x12, 0x29, 0x0a,
	0x07, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f,
	0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x41, 0x6e, 0x79, 0x52,
	0x07, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x61, 0x78, 0x69,
	0x6d, 0x75, 0x6d, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x01, 0x52, 0x07, 0x6d, 0x61, 0x78, 0x69, 0x6d,
	0x75, 0x6d, 0x12, 0x2b, 0x0a, 0x11, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x76, 0x65, 0x5f,
	0x6d, 0x61, 0x78, 0x69, 0x6d, 0x75, 0x6d, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x65,
	0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x76, 0x65, 0x4d, 0x61, 0x78, 0x69, 0x6d, 0x75, 0x6d, 0x12,
	0x18, 0x0a, 0x07, 0x6d, 0x69, 0x6e, 0x69, 0x6d, 0x75, 0x6d, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x07, 0x6d, 0x69, 0x6e, 0x69, 0x6d, 0x75, 0x6d, 0x12, 0x2b, 0x0a, 0x11, 0x65, 0x78, 0x63,
	0x6c, 0x75, 0x73, 0x69, 0x76, 0x65, 0x5f, 0x6d, 0x69, 0x6e, 0x69, 0x6d, 0x75, 0x6d, 0x18, 0x0e,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x76, 0x65, 0x4d,
	0x69, 0x6e, 0x69, 0x6d, 0x75, 0x6d, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x61, 0x78, 0x5f, 0x6c, 0x65,
	0x6e, 0x67, 0x74, 0x68, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6d, 0x61, 0x78, 0x4c,
	0x65, 0x6e, 0x67, 0x74, 0x68, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x69, 0x6e, 0x5f, 0x6c, 0x65, 0x6e,
	0x67, 0x74, 0x68, 0x18, 0x10, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6d, 0x69, 0x6e, 0x4c, 0x65,
	0x6e, 0x67, 0x74, 0x68, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x61, 0x74, 0x74, 0x65, 0x72, 0x6e, 0x18,
	0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x61, 0x74, 0x74, 0x65, 0x72, 0x6e, 0x12, 0x1b,
	0x0a, 0x09, 0x6d, 0x61, 0x78, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x12, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x08, 0x6d, 0x61, 0x78, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x6d,
	0x69, 0x6e, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x13, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08,
	0x6d, 0x69, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x75, 0x6e, 0x69, 0x71,
	0x75, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x14, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b,
	0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x23, 0x0a, 0x04, 0x65,
	0x6e, 0x75, 0x6d, 0x18, 0x15, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x6f, 0x70, 0x65, 0x6e,
	0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x41, 0x6e, 0x79, 0x52, 0x04, 0x65, 0x6e, 0x75, 0x6d,
	0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x65, 0x5f, 0x6f, 0x66, 0x18,
	0x16, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0a, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x65, 0x4f,
	0x66, 0x12, 0x3f, 0x0a, 0x10, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x65, 0x78, 0x74, 0x65,
	0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x17, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6f, 0x70,
	0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x41, 0x6e,
	0x79, 0x52, 0x0f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69,
	0x6f, 0x6e, 0x22, 0xab, 0x05, 0x0a, 0x06, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x12, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x12, 0x31, 0x0a, 0x05, 0x69, 0x74, 0x65,
	0x6d, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61,
	0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x72, 0x69, 0x6d, 0x69, 0x74, 0x69, 0x76, 0x65, 0x73,
	0x49, 0x74, 0x65, 0x6d, 0x73, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x2b, 0x0a, 0x11,
	0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x61,
	0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x12, 0x29, 0x0a, 0x07, 0x64, 0x65, 0x66,
	0x61, 0x75, 0x6c, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x6f, 0x70, 0x65,
	0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x41, 0x6e, 0x79, 0x52, 0x07, 0x64, 0x65, 0x66,
	0x61, 0x75, 0x6c, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x61, 0x78, 0x69, 0x6d, 0x75, 0x6d, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x01, 0x52, 0x07, 0x6d, 0x61, 0x78, 0x69, 0x6d, 0x75, 0x6d, 0x12, 0x2b,
	0x0a, 0x11, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x76, 0x65, 0x5f, 0x6d, 0x61, 0x78, 0x69,
	0x6d, 0x75, 0x6d, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x65, 0x78, 0x63, 0x6c, 0x75,
	0x73, 0x69, 0x76, 0x65, 0x4d, 0x61, 0x78, 0x69, 0x6d, 0x75, 0x6d, 0x12, 0x18, 0x0a, 0x07, 0x6d,
	0x69, 0x6e, 0x69, 0x6d, 0x75, 0x6d, 0x18, 0x08, 0x20, 0x01, 0x28, 0x01, 0x52, 0x07, 0x6d, 0x69,
	0x6e, 0x69, 0x6d, 0x75, 0x6d, 0x12, 0x2b, 0x0a, 0x11, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69,
	0x76, 0x65, 0x5f, 0x6d, 0x69, 0x6e, 0x69, 0x6d, 0x75, 0x6d, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x10, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x76, 0x65, 0x4d, 0x69, 0x6e, 0x69, 0x6d,
	0x75, 0x6d, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x61, 0x78, 0x5f, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6d, 0x61, 0x78, 0x4c, 0x65, 0x6e, 0x67, 0x74,
	0x68, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x69, 0x6e, 0x5f, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6d, 0x69, 0x6e, 0x4c, 0x65, 0x6e, 0x67, 0x74, 0x68,
	0x12, 0x18, 0x0a, 0x07, 0x70, 0x61, 0x74, 0x74, 0x65, 0x72, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x70, 0x61, 0x74, 0x74, 0x65, 0x72, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x61,
	0x78, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x6d,
	0x61, 0x78, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x69, 0x6e, 0x5f, 0x69,
	0x74, 0x65, 0x6d, 0x73, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x6d, 0x69, 0x6e, 0x49,
	0x74, 0x65, 0x6d, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x5f, 0x69,
	0x74, 0x65, 0x6d, 0x73, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x75, 0x6e, 0x69, 0x71,
	0x75, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x23, 0x0a, 0x04, 0x65, 0x6e, 0x75, 0x6d, 0x18,
	0x10, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e,
	0x76, 0x32, 0x2e, 0x41, 0x6e, 0x79, 0x52, 0x04, 0x65, 0x6e, 0x75, 0x6d, 0x12, 0x1f, 0x0a, 0x0b,
	0x6d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x65, 0x5f, 0x6f, 0x66, 0x18, 0x11, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x0a, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x65, 0x4f, 0x66, 0x12, 0x20, 0x0a,
	0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x12, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x3f, 0x0a, 0x10, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73,
	0x69, 0x6f, 0x6e, 0x18, 0x13, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6f, 0x70, 0x65, 0x6e,
	0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x41, 0x6e, 0x79, 0x52,
	0x0f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e,
	0x22, 0xfd, 0x05, 0x0a, 0x18, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x65, 0x74, 0x65, 0x72, 0x53, 0x75, 0x62, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x12, 0x1a, 0x0a,
	0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x12, 0x31, 0x0a, 0x05, 0x69,
	0x74, 0x65, 0x6d, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x6f, 0x70, 0x65,
	0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x72, 0x69, 0x6d, 0x69, 0x74, 0x69, 0x76,
	0x65, 0x73, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x2b,
	0x0a, 0x11, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x66, 0x6f, 0x72,
	0x6d, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x63, 0x6f, 0x6c, 0x6c, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x12, 0x29, 0x0a, 0x07, 0x64,
	0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x6f,
	0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x41, 0x6e, 0x79, 0x52, 0x07, 0x64,
	0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x61, 0x78, 0x69, 0x6d, 0x75,
	0x6d, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x01, 0x52, 0x07, 0x6d, 0x61, 0x78, 0x69, 0x6d, 0x75, 0x6d,
	0x12, 0x2b, 0x0a, 0x11, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x76, 0x65, 0x5f, 0x6d, 0x61,
	0x78, 0x69, 0x6d, 0x75, 0x6d, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x65, 0x78, 0x63,
	0x6c, 0x75, 0x73, 0x69, 0x76, 0x65, 0x4d, 0x61, 0x78, 0x69, 0x6d, 0x75, 0x6d, 0x12, 0x18, 0x0a,
	0x07, 0x6d, 0x69, 0x6e, 0x69, 0x6d, 0x75, 0x6d, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x01, 0x52, 0x07,
	0x6d, 0x69, 0x6e, 0x69, 0x6d, 0x75, 0x6d, 0x12, 0x2b, 0x0a, 0x11, 0x65, 0x78, 0x63, 0x6c, 0x75,
	0x73, 0x69, 0x76, 0x65, 0x5f, 0x6d, 0x69, 0x6e, 0x69, 0x6d, 0x75, 0x6d, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x10, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x76, 0x65, 0x4d, 0x69, 0x6e,
	0x69, 0x6d, 0x75, 0x6d, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x61, 0x78, 0x5f, 0x6c, 0x65, 0x6e, 0x67,
	0x74, 0x68, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6d, 0x61, 0x78, 0x4c, 0x65, 0x6e,
	0x67, 0x74, 0x68, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x69, 0x6e, 0x5f, 0x6c, 0x65, 0x6e, 0x67, 0x74,
	0x68, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6d, 0x69, 0x6e, 0x4c, 0x65, 0x6e, 0x67,
	0x74, 0x68, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x61, 0x74, 0x74, 0x65, 0x72, 0x6e, 0x18, 0x10, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x61, 0x74, 0x74, 0x65, 0x72, 0x6e, 0x12, 0x1b, 0x0a, 0x09,
	0x6d, 0x61, 0x78, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x11, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x08, 0x6d, 0x61, 0x78, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x69, 0x6e,
	0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x12, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x6d, 0x69,
	0x6e, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65,
	0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x13, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x75, 0x6e,
	0x69, 0x71, 0x75, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x23, 0x0a, 0x04, 0x65, 0x6e, 0x75,
	0x6d, 0x18, 0x14, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70,
	0x69, 0x2e, 0x76, 0x32, 0x2e, 0x41, 0x6e, 0x79, 0x52, 0x04, 0x65, 0x6e, 0x75, 0x6d, 0x12, 0x1f,
	0x0a, 0x0b, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x65, 0x5f, 0x6f, 0x66, 0x18, 0x15, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x0a, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x65, 0x4f, 0x66, 0x12,
	0x3f, 0x0a, 0x10, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73,
	0x69, 0x6f, 0x6e, 0x18, 0x16, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6f, 0x70, 0x65, 0x6e,
	0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x41, 0x6e, 0x79, 0x52,
	0x0f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e,
	0x22, 0x57, 0x0a, 0x07, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x12, 0x4c, 0x0a, 0x15, 0x61,
	0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72,
	0x74, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6f, 0x70, 0x65,
	0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x52, 0x14, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x50,
	0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x22, 0xa1, 0x02, 0x0a, 0x04, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x28, 0x0a, 0x10, 0x74, 0x65, 0x72, 0x6d, 0x73, 0x5f, 0x6f, 0x66,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e,
	0x74, 0x65, 0x72, 0x6d, 0x73, 0x4f, 0x66, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x2d,
	0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x13, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x6f, 0x6e,
	0x74, 0x61, 0x63, 0x74, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x12, 0x2d, 0x0a,
	0x07, 0x6c, 0x69, 0x63, 0x65, 0x6e, 0x73, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13,
	0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x63, 0x65,
	0x6e, 0x73, 0x65, 0x52, 0x07, 0x6c, 0x69, 0x63, 0x65, 0x6e, 0x73, 0x65, 0x12, 0x3f, 0x0a, 0x10,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69,
	0x2e, 0x76, 0x32, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x41, 0x6e, 0x79, 0x52, 0x0f, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x37, 0x0a,
	0x09, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x2a, 0x0a, 0x06, 0x73, 0x63,
	0x68, 0x65, 0x6d, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x6f, 0x70, 0x65,
	0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x52, 0x06,
	0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x22, 0x44, 0x0a, 0x0d, 0x4a, 0x73, 0x6f, 0x6e, 0x52, 0x65,
	0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x11, 0x0a, 0x04, 0x5f, 0x72, 0x65, 0x66, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x52, 0x65, 0x66, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x70, 0x0a, 0x07,
	0x4c, 0x69, 0x63, 0x65, 0x6e, 0x73, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75,
	0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x3f, 0x0a,
	0x10, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70,
	0x69, 0x2e, 0x76, 0x32, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x41, 0x6e, 0x79, 0x52, 0x0f, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x45,
	0x0a, 0x08, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x41, 0x6e, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x25,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e,
	0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x41, 0x6e, 0x79, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x4b, 0x0a, 0x0b, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70,
	0x69, 0x2e, 0x76, 0x32, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x22, 0x51, 0x0a, 0x0e, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x65, 0x74, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2b, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70,
	0x69, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x4f, 0x0a, 0x0d, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x50, 0x61,
	0x74, 0x68, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6f, 0x70, 0x65, 0x6e,
	0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x74, 0x68, 0x49, 0x74, 0x65, 0x6d, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x4f, 0x0a, 0x0d, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6f, 0x70, 0x65,
	0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x59, 0x0a, 0x12, 0x4e, 0x61, 0x6d, 0x65, 0x64,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x2f, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x19, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x22, 0x4b, 0x0a, 0x0b, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x53, 0x63, 0x68, 0x65, 0x6d,
	0x61, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76,
	0x32, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22,
	0x6d, 0x0a, 0x1c, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79,
	0x44, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x49, 0x74, 0x65, 0x6d, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x39, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x23, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e,
	0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x37,
	0x0a, 0x0b, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x55, 0x0a, 0x10, 0x4e, 0x61, 0x6d, 0x65, 0x64,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x41, 0x72, 0x72, 0x61, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x2d, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17,
	0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x41, 0x72, 0x72, 0x61, 0x79, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0xb5,
	0x03, 0x0a, 0x10, 0x4e, 0x6f, 0x6e, 0x42, 0x6f, 0x64, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65,
	0x74, 0x65, 0x72, 0x12, 0x65, 0x0a, 0x1b, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x5f, 0x70, 0x61,
	0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x5f, 0x73, 0x75, 0x62, 0x5f, 0x73, 0x63, 0x68, 0x65,
	0x6d, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61,
	0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x65, 0x74, 0x65, 0x72, 0x53, 0x75, 0x62, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x48, 0x00,
	0x52, 0x18, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65,
	0x72, 0x53, 0x75, 0x62, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x12, 0x6c, 0x0a, 0x1e, 0x66, 0x6f,
	0x72, 0x6d, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65,
	0x72, 0x5f, 0x73, 0x75, 0x62, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e,
	0x46, 0x6f, 0x72, 0x6d, 0x44, 0x61, 0x74, 0x61, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65,
	0x72, 0x53, 0x75, 0x62, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x48, 0x00, 0x52, 0x1a, 0x66, 0x6f,
	0x72, 0x6d, 0x44, 0x61, 0x74, 0x61, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x53,
	0x75, 0x62, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x12, 0x62, 0x0a, 0x1a, 0x71, 0x75, 0x65, 0x72,
	0x79, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x5f, 0x73, 0x75, 0x62, 0x5f,
	0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x6f,
	0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x53, 0x75, 0x62, 0x53, 0x63, 0x68, 0x65, 0x6d,
	0x61, 0x48, 0x00, 0x52, 0x17, 0x71, 0x75, 0x65, 0x72, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65,
	0x74, 0x65, 0x72, 0x53, 0x75, 0x62, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x12, 0x5f, 0x0a, 0x19,
	0x70, 0x61, 0x74, 0x68, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x5f, 0x73,
	0x75, 0x62, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x22, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x74,
	0x68, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x53, 0x75, 0x62, 0x53, 0x63, 0x68,
	0x65, 0x6d, 0x61, 0x48, 0x00, 0x52, 0x16, 0x70, 0x61, 0x74, 0x68, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x65, 0x74, 0x65, 0x72, 0x53, 0x75, 0x62, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x42, 0x07, 0x0a,
	0x05, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x22, 0xa1, 0x02, 0x0a, 0x18, 0x4f, 0x61, 0x75, 0x74, 0x68,
	0x32, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x53, 0x65, 0x63, 0x75, 0x72,
	0x69, 0x74, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x66, 0x6c, 0x6f, 0x77, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x66, 0x6c, 0x6f, 0x77, 0x12, 0x30, 0x0a, 0x06, 0x73,
	0x63, 0x6f, 0x70, 0x65, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x6f, 0x70,
	0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x4f, 0x61, 0x75, 0x74, 0x68, 0x32, 0x53,
	0x63, 0x6f, 0x70, 0x65, 0x73, 0x52, 0x06, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x73, 0x12, 0x2b, 0x0a,
	0x11, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x75,
	0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3f, 0x0a, 0x10, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32,
	0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x41, 0x6e, 0x79, 0x52, 0x0f, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0xf5, 0x01, 0x0a, 0x19, 0x4f,
	0x61, 0x75, 0x74, 0x68, 0x32, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x66, 0x6c, 0x6f, 0x77, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x66, 0x6c, 0x6f, 0x77,
	0x12, 0x30, 0x0a, 0x06, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x4f, 0x61,
	0x75, 0x74, 0x68, 0x32, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x73, 0x52, 0x06, 0x73, 0x63, 0x6f, 0x70,
	0x65, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x55, 0x72, 0x6c, 0x12,
	0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x3f, 0x0a, 0x10, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x65, 0x78, 0x74, 0x65,
	0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6f, 0x70,
	0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x41, 0x6e,
	0x79, 0x52, 0x0f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69,
	0x6f, 0x6e, 0x22, 0x82, 0x02, 0x0a, 0x16, 0x4f, 0x61, 0x75, 0x74, 0x68, 0x32, 0x49, 0x6d, 0x70,
	0x6c, 0x69, 0x63, 0x69, 0x74, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x12, 0x12, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x66, 0x6c, 0x6f, 0x77, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x66, 0x6c, 0x6f, 0x77, 0x12, 0x30, 0x0a, 0x06, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x73, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e,
	0x76, 0x32, 0x2e, 0x4f, 0x61, 0x75, 0x74, 0x68, 0x32, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x73, 0x52,
	0x06, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x73, 0x12, 0x2b, 0x0a, 0x11, 0x61, 0x75, 0x74, 0x68, 0x6f,
	0x72, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x10, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x55, 0x72, 0x6c, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3f, 0x0a, 0x10, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x14, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x4e, 0x61,
	0x6d, 0x65, 0x64, 0x41, 0x6e, 0x79, 0x52, 0x0f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x45, 0x78,
	0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0xf2, 0x01, 0x0a, 0x16, 0x4f, 0x61, 0x75, 0x74,
	0x68, 0x32, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69,
	0x74, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x66, 0x6c, 0x6f, 0x77, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x66, 0x6c, 0x6f, 0x77, 0x12, 0x30, 0x0a, 0x06, 0x73, 0x63,
	0x6f, 0x70, 0x65, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x6f, 0x70, 0x65,
	0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x4f, 0x61, 0x75, 0x74, 0x68, 0x32, 0x53, 0x63,
	0x6f, 0x70, 0x65, 0x73, 0x52, 0x06, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x73, 0x12, 0x1b, 0x0a, 0x09,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3f, 0x0a, 0x10, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e,
	0x76, 0x32, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x41, 0x6e, 0x79, 0x52, 0x0f, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x5c, 0x0a, 0x0c,
	0x4f, 0x61, 0x75, 0x74, 0x68, 0x32, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x73, 0x12, 0x4c, 0x0a, 0x15,
	0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x70, 0x72, 0x6f, 0x70, 0x65,
	0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6f, 0x70,
	0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x52, 0x14, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x22, 0x9e, 0x04, 0x0a, 0x09, 0x4f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x18, 0x0a, 0x07,
	0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73,
	0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3d, 0x0a, 0x0d, 0x65, 0x78, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x64, 0x6f, 0x63, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x18, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x45, 0x78, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x6c, 0x44, 0x6f, 0x63, 0x73, 0x52, 0x0c, 0x65, 0x78, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x44, 0x6f, 0x63, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72,
	0x6f, 0x64, 0x75, 0x63, 0x65, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x70, 0x72,
	0x6f, 0x64, 0x75, 0x63, 0x65, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d,
	0x65, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d,
	0x65, 0x73, 0x12, 0x3a, 0x0a, 0x0a, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73,
	0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69,
	0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x49, 0x74,
	0x65, 0x6d, 0x52, 0x0a, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x12, 0x33,
	0x0a, 0x09, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x15, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x73, 0x52, 0x09, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x73, 0x18, 0x0a,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x73, 0x12, 0x1e, 0x0a,
	0x0a, 0x64, 0x65, 0x70, 0x72, 0x65, 0x63, 0x61, 0x74, 0x65, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0a, 0x64, 0x65, 0x70, 0x72, 0x65, 0x63, 0x61, 0x74, 0x65, 0x64, 0x12, 0x3b, 0x0a,
	0x08, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1f, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x53, 0x65, 0x63,
	0x75, 0x72, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x52, 0x08, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x12, 0x3f, 0x0a, 0x10, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0d,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76,
	0x32, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x41, 0x6e, 0x79, 0x52, 0x0f, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0xa6, 0x01, 0x0a, 0x09,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x12, 0x42, 0x0a, 0x0e, 0x62, 0x6f, 0x64,
	0x79, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x19, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x42,
	0x6f, 0x64, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x48, 0x00, 0x52, 0x0d,
	0x62, 0x6f, 0x64, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x12, 0x4c, 0x0a,
	0x12, 0x6e, 0x6f, 0x6e, 0x5f, 0x62, 0x6f, 0x64, 0x79, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65,
	0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6f, 0x70, 0x65, 0x6e,
	0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x4e, 0x6f, 0x6e, 0x42, 0x6f, 0x64, 0x79, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x48, 0x00, 0x52, 0x10, 0x6e, 0x6f, 0x6e, 0x42, 0x6f,
	0x64, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x42, 0x07, 0x0a, 0x05, 0x6f,
	0x6e, 0x65, 0x6f, 0x66, 0x22, 0x67, 0x0a, 0x14, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65,
	0x72, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x4f, 0x0a, 0x15,
	0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x70, 0x72, 0x6f, 0x70, 0x65,
	0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x6f, 0x70,
	0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x52, 0x14, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x22, 0x94, 0x01,
	0x0a, 0x0e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x49, 0x74, 0x65, 0x6d,
	0x12, 0x35, 0x0a, 0x09, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32,
	0x2e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x48, 0x00, 0x52, 0x09, 0x70, 0x61,
	0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x12, 0x42, 0x0a, 0x0e, 0x6a, 0x73, 0x6f, 0x6e, 0x5f,
	0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x19, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x4a, 0x73, 0x6f,
	0x6e, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x48, 0x00, 0x52, 0x0d, 0x6a, 0x73,
	0x6f, 0x6e, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x42, 0x07, 0x0a, 0x05, 0x6f,
	0x6e, 0x65, 0x6f, 0x66, 0x22, 0xcf, 0x03, 0x0a, 0x08, 0x50, 0x61, 0x74, 0x68, 0x49, 0x74, 0x65,
	0x6d, 0x12, 0x11, 0x0a, 0x04, 0x5f, 0x72, 0x65, 0x66, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x52, 0x65, 0x66, 0x12, 0x27, 0x0a, 0x03, 0x67, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x15, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x4f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x03, 0x67, 0x65, 0x74, 0x12, 0x27, 0x0a,
	0x03, 0x70, 0x75, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6f, 0x70, 0x65,
	0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x03, 0x70, 0x75, 0x74, 0x12, 0x29, 0x0a, 0x04, 0x70, 0x6f, 0x73, 0x74, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76,
	0x32, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x04, 0x70, 0x6f, 0x73,
	0x74, 0x12, 0x2d, 0x0a, 0x06, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x15, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x4f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x06, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x12, 0x2f, 0x0a, 0x07, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x15, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x4f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x12, 0x29, 0x0a, 0x04, 0x68, 0x65, 0x61, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x15, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x4f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x04, 0x68, 0x65, 0x61, 0x64, 0x12, 0x2b, 0x0a, 0x05,
	0x70, 0x61, 0x74, 0x63, 0x68, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6f, 0x70,
	0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x05, 0x70, 0x61, 0x74, 0x63, 0x68, 0x12, 0x3a, 0x0a, 0x0a, 0x70, 0x61, 0x72,
	0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x65, 0x74, 0x65, 0x72, 0x73, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x0a, 0x70, 0x61, 0x72, 0x61, 0x6d,
	0x65, 0x74, 0x65, 0x72, 0x73, 0x12, 0x3f, 0x0a, 0x10, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f,
	0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x14, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x4e, 0x61, 0x6d,
	0x65, 0x64, 0x41, 0x6e, 0x79, 0x52, 0x0f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x45, 0x78, 0x74,
	0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0xfb, 0x05, 0x0a, 0x16, 0x50, 0x61, 0x74, 0x68, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x53, 0x75, 0x62, 0x53, 0x63, 0x68, 0x65, 0x6d,
	0x61, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x6e, 0x12, 0x20, 0x0a,
	0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x6f, 0x72, 0x6d, 0x61,
	0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x12,
	0x31, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b,
	0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x72, 0x69, 0x6d,
	0x69, 0x74, 0x69, 0x76, 0x65, 0x73, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x52, 0x05, 0x69, 0x74, 0x65,
	0x6d, 0x73, 0x12, 0x2b, 0x0a, 0x11, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x63,
	0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x12,
	0x29, 0x0a, 0x07, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0f, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x41, 0x6e,
	0x79, 0x52, 0x07, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x61,
	0x78, 0x69, 0x6d, 0x75, 0x6d, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x01, 0x52, 0x07, 0x6d, 0x61, 0x78,
	0x69, 0x6d, 0x75, 0x6d, 0x12, 0x2b, 0x0a, 0x11, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x76,
	0x65, 0x5f, 0x6d, 0x61, 0x78, 0x69, 0x6d, 0x75, 0x6d, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x10, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x76, 0x65, 0x4d, 0x61, 0x78, 0x69, 0x6d, 0x75,
	0x6d, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x69, 0x6e, 0x69, 0x6d, 0x75, 0x6d, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x07, 0x6d, 0x69, 0x6e, 0x69, 0x6d, 0x75, 0x6d, 0x12, 0x2b, 0x0a, 0x11, 0x65,
	0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x76, 0x65, 0x5f, 0x6d, 0x69, 0x6e, 0x69, 0x6d, 0x75, 0x6d,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x76,
	0x65, 0x4d, 0x69, 0x6e, 0x69, 0x6d, 0x75, 0x6d, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x61, 0x78, 0x5f,
	0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6d, 0x61,
	0x78, 0x4c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x69, 0x6e, 0x5f, 0x6c,
	0x65, 0x6e, 0x67, 0x74, 0x68, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6d, 0x69, 0x6e,
	0x4c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x61, 0x74, 0x74, 0x65, 0x72,
	0x6e, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x61, 0x74, 0x74, 0x65, 0x72, 0x6e,
	0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x61, 0x78, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x11, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x08, 0x6d, 0x61, 0x78, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x1b, 0x0a,
	0x09, 0x6d, 0x69, 0x6e, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x12, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x08, 0x6d, 0x69, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x75, 0x6e,
	0x69, 0x71, 0x75, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x13, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0b, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x23, 0x0a,
	0x04, 0x65, 0x6e, 0x75, 0x6d, 0x18, 0x14, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x6f, 0x70,
	0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x41, 0x6e, 0x79, 0x52, 0x04, 0x65, 0x6e,
	0x75, 0x6d, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x65, 0x5f, 0x6f,
	0x66, 0x18, 0x15, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0a, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c,
	0x65, 0x4f, 0x66, 0x12, 0x3f, 0x0a, 0x10, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x65, 0x78,
	0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x16, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e,
	0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x64,
	0x41, 0x6e, 0x79, 0x52, 0x0f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x45, 0x78, 0x74, 0x65, 0x6e,
	0x73, 0x69, 0x6f, 0x6e, 0x22, 0x77, 0x0a, 0x05, 0x50, 0x61, 0x74, 0x68, 0x73, 0x12, 0x3f, 0x0a,
	0x10, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70,
	0x69, 0x2e, 0x76, 0x32, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x41, 0x6e, 0x79, 0x52, 0x0f, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x2d,
	0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6f,
	0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x50,
	0x61, 0x74, 0x68, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x22, 0x92, 0x05,
	0x0a, 0x0f, 0x50, 0x72, 0x69, 0x6d, 0x69, 0x74, 0x69, 0x76, 0x65, 0x73, 0x49, 0x74, 0x65, 0x6d,
	0x73, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x12, 0x31, 0x0a,
	0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x6f,
	0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x72, 0x69, 0x6d, 0x69, 0x74,
	0x69, 0x76, 0x65, 0x73, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73,
	0x12, 0x2b, 0x0a, 0x11, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x66,
	0x6f, 0x72, 0x6d, 0x61, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x63, 0x6f, 0x6c,
	0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x12, 0x29, 0x0a,
	0x07, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f,
	0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x41, 0x6e, 0x79, 0x52,
	0x07, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x61, 0x78, 0x69,
	0x6d, 0x75, 0x6d, 0x18, 0x06, 0x20, 0x01, 0x28, 0x01, 0x52, 0x07, 0x6d, 0x61, 0x78, 0x69, 0x6d,
	0x75, 0x6d, 0x12, 0x2b, 0x0a, 0x11, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x76, 0x65, 0x5f,
	0x6d, 0x61, 0x78, 0x69, 0x6d, 0x75, 0x6d, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x65,
	0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x76, 0x65, 0x4d, 0x61, 0x78, 0x69, 0x6d, 0x75, 0x6d, 0x12,
	0x18, 0x0a, 0x07, 0x6d, 0x69, 0x6e, 0x69, 0x6d, 0x75, 0x6d, 0x18, 0x08, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x07, 0x6d, 0x69, 0x6e, 0x69, 0x6d, 0x75, 0x6d, 0x12, 0x2b, 0x0a, 0x11, 0x65, 0x78, 0x63,
	0x6c, 0x75, 0x73, 0x69, 0x76, 0x65, 0x5f, 0x6d, 0x69, 0x6e, 0x69, 0x6d, 0x75, 0x6d, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x76, 0x65, 0x4d,
	0x69, 0x6e, 0x69, 0x6d, 0x75, 0x6d, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x61, 0x78, 0x5f, 0x6c, 0x65,
	0x6e, 0x67, 0x74, 0x68, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6d, 0x61, 0x78, 0x4c,
	0x65, 0x6e, 0x67, 0x74, 0x68, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x69, 0x6e, 0x5f, 0x6c, 0x65, 0x6e,
	0x67, 0x74, 0x68, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6d, 0x69, 0x6e, 0x4c, 0x65,
	0x6e, 0x67, 0x74, 0x68, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x61, 0x74, 0x74, 0x65, 0x72, 0x6e, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x61, 0x74, 0x74, 0x65, 0x72, 0x6e, 0x12, 0x1b,
	0x0a, 0x09, 0x6d, 0x61, 0x78, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x08, 0x6d, 0x61, 0x78, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x6d,
	0x69, 0x6e, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08,
	0x6d, 0x69, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x75, 0x6e, 0x69, 0x71,
	0x75, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b,
	0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x23, 0x0a, 0x04, 0x65,
	0x6e, 0x75, 0x6d, 0x18, 0x10, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x6f, 0x70, 0x65, 0x6e,
	0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x41, 0x6e, 0x79, 0x52, 0x04, 0x65, 0x6e, 0x75, 0x6d,
	0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x65, 0x5f, 0x6f, 0x66, 0x18,
	0x11, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0a, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x65, 0x4f,
	0x66, 0x12, 0x3f, 0x0a, 0x10, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x65, 0x78, 0x74, 0x65,
	0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x12, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6f, 0x70,
	0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x41, 0x6e,
	0x79, 0x52, 0x0f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69,
	0x6f, 0x6e, 0x22, 0x5a, 0x0a, 0x0a, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73,
	0x12, 0x4c, 0x0a, 0x15, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x70,
	0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x17, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x4e, 0x61, 0x6d,
	0x65, 0x64, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x52, 0x14, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x22, 0xa8,
	0x06, 0x0a, 0x17, 0x51, 0x75, 0x65, 0x72, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65,
	0x72, 0x53, 0x75, 0x62, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x72, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x11,
	0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x5f, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x66, 0x6f,
	0x72, 0x6d, 0x61, 0x74, 0x12, 0x31, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32,
	0x2e, 0x50, 0x72, 0x69, 0x6d, 0x69, 0x74, 0x69, 0x76, 0x65, 0x73, 0x49, 0x74, 0x65, 0x6d, 0x73,
	0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x2b, 0x0a, 0x11, 0x63, 0x6f, 0x6c, 0x6c, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x10, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f,
	0x72, 0x6d, 0x61, 0x74, 0x12, 0x29, 0x0a, 0x07, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e,
	0x76, 0x32, 0x2e, 0x41, 0x6e, 0x79, 0x52, 0x07, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x12,
	0x18, 0x0a, 0x07, 0x6d, 0x61, 0x78, 0x69, 0x6d, 0x75, 0x6d, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x07, 0x6d, 0x61, 0x78, 0x69, 0x6d, 0x75, 0x6d, 0x12, 0x2b, 0x0a, 0x11, 0x65, 0x78, 0x63,
	0x6c, 0x75, 0x73, 0x69, 0x76, 0x65, 0x5f, 0x6d, 0x61, 0x78, 0x69, 0x6d, 0x75, 0x6d, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x76, 0x65, 0x4d,
	0x61, 0x78, 0x69, 0x6d, 0x75, 0x6d, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x69, 0x6e, 0x69, 0x6d, 0x75,
	0x6d, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x01, 0x52, 0x07, 0x6d, 0x69, 0x6e, 0x69, 0x6d, 0x75, 0x6d,
	0x12, 0x2b, 0x0a, 0x11, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x76, 0x65, 0x5f, 0x6d, 0x69,
	0x6e, 0x69, 0x6d, 0x75, 0x6d, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x65, 0x78, 0x63,
	0x6c, 0x75, 0x73, 0x69, 0x76, 0x65, 0x4d, 0x69, 0x6e, 0x69, 0x6d, 0x75, 0x6d, 0x12, 0x1d, 0x0a,
	0x0a, 0x6d, 0x61, 0x78, 0x5f, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x09, 0x6d, 0x61, 0x78, 0x4c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x12, 0x1d, 0x0a, 0x0a,
	0x6d, 0x69, 0x6e, 0x5f, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x18, 0x10, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x6d, 0x69, 0x6e, 0x4c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x12, 0x18, 0x0a, 0x07, 0x70,
	0x61, 0x74, 0x74, 0x65, 0x72, 0x6e, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x61,
	0x74, 0x74, 0x65, 0x72, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x61, 0x78, 0x5f, 0x69, 0x74, 0x65,
	0x6d, 0x73, 0x18, 0x12, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x6d, 0x61, 0x78, 0x49, 0x74, 0x65,
	0x6d, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x69, 0x6e, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18,
	0x13, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x6d, 0x69, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12,
	0x21, 0x0a, 0x0c, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18,
	0x14, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x49, 0x74, 0x65,
	0x6d, 0x73, 0x12, 0x23, 0x0a, 0x04, 0x65, 0x6e, 0x75, 0x6d, 0x18, 0x15, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x0f, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x41, 0x6e,
	0x79, 0x52, 0x04, 0x65, 0x6e, 0x75, 0x6d, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x75, 0x6c, 0x74, 0x69,
	0x70, 0x6c, 0x65, 0x5f, 0x6f, 0x66, 0x18, 0x16, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0a, 0x6d, 0x75,
	0x6c, 0x74, 0x69, 0x70, 0x6c, 0x65, 0x4f, 0x66, 0x12, 0x3f, 0x0a, 0x10, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x17, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e,
	0x4e, 0x61, 0x6d, 0x65, 0x64, 0x41, 0x6e, 0x79, 0x52, 0x0f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0xfe, 0x01, 0x0a, 0x08, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2e, 0x0a, 0x06, 0x73, 0x63, 0x68, 0x65,
	0x6d, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61,
	0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x49, 0x74, 0x65, 0x6d,
	0x52, 0x06, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x12, 0x2d, 0x0a, 0x07, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x6f, 0x70, 0x65, 0x6e,
	0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x52, 0x07,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x12, 0x30, 0x0a, 0x08, 0x65, 0x78, 0x61, 0x6d, 0x70,
	0x6c, 0x65, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6f, 0x70, 0x65, 0x6e,
	0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x45, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x73, 0x52,
	0x08, 0x65, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x73, 0x12, 0x3f, 0x0a, 0x10, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32,
	0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x41, 0x6e, 0x79, 0x52, 0x0f, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x65, 0x0a, 0x13, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x12, 0x4e, 0x0a, 0x15, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f,
	0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x19, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x4e, 0x61,
	0x6d, 0x65, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x14, 0x61, 0x64, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65,
	0x73, 0x22, 0x90, 0x01, 0x0a, 0x0d, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x12, 0x32, 0x0a, 0x08, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e,
	0x76, 0x32, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x00, 0x52, 0x08, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x42, 0x0a, 0x0e, 0x6a, 0x73, 0x6f, 0x6e, 0x5f,
	0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x19, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x4a, 0x73, 0x6f,
	0x6e, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x48, 0x00, 0x52, 0x0d, 0x6a, 0x73,
	0x6f, 0x6e, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x42, 0x07, 0x0a, 0x05, 0x6f,
	0x6e, 0x65, 0x6f, 0x66, 0x22, 0x91, 0x01, 0x0a, 0x09, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x73, 0x12, 0x43, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x6f, 0x70, 0x65, 0x6e,
	0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x3f, 0x0a, 0x10, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x14, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x4e,
	0x61, 0x6d, 0x65, 0x64, 0x41, 0x6e, 0x79, 0x52, 0x0f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x45,
	0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0xaf, 0x09, 0x0a, 0x06, 0x53, 0x63, 0x68,
	0x65, 0x6d, 0x61, 0x12, 0x11, 0x0a, 0x04, 0x5f, 0x72, 0x65, 0x66, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x52, 0x65, 0x66, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x12, 0x14,
	0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x29, 0x0a, 0x07, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c,
	0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70,
	0x69, 0x2e, 0x76, 0x32, 0x2e, 0x41, 0x6e, 0x79, 0x52, 0x07, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c,
	0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x65, 0x5f, 0x6f, 0x66,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0a, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x65,
	0x4f, 0x66, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x61, 0x78, 0x69, 0x6d, 0x75, 0x6d, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x07, 0x6d, 0x61, 0x78, 0x69, 0x6d, 0x75, 0x6d, 0x12, 0x2b, 0x0a, 0x11,
	0x65, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x76, 0x65, 0x5f, 0x6d, 0x61, 0x78, 0x69, 0x6d, 0x75,
	0x6d, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69,
	0x76, 0x65, 0x4d, 0x61, 0x78, 0x69, 0x6d, 0x75, 0x6d, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x69, 0x6e,
	0x69, 0x6d, 0x75, 0x6d, 0x18, 0x09, 0x20, 0x01, 0x28, 0x01, 0x52, 0x07, 0x6d, 0x69, 0x6e, 0x69,
	0x6d, 0x75, 0x6d, 0x12, 0x2b, 0x0a, 0x11, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x76, 0x65,
	0x5f, 0x6d, 0x69, 0x6e, 0x69, 0x6d, 0x75, 0x6d, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10,
	0x65, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x76, 0x65, 0x4d, 0x69, 0x6e, 0x69, 0x6d, 0x75, 0x6d,
	0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x61, 0x78, 0x5f, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6d, 0x61, 0x78, 0x4c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x12,
	0x1d, 0x0a, 0x0a, 0x6d, 0x69, 0x6e, 0x5f, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x6d, 0x69, 0x6e, 0x4c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x12, 0x18,
	0x0a, 0x07, 0x70, 0x61, 0x74, 0x74, 0x65, 0x72, 0x6e, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x70, 0x61, 0x74, 0x74, 0x65, 0x72, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x61, 0x78, 0x5f,
	0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x6d, 0x61, 0x78,
	0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x69, 0x6e, 0x5f, 0x69, 0x74, 0x65,
	0x6d, 0x73, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x6d, 0x69, 0x6e, 0x49, 0x74, 0x65,
	0x6d, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x5f, 0x69, 0x74, 0x65,
	0x6d, 0x73, 0x18, 0x10, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65,
	0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x6d, 0x61, 0x78, 0x5f, 0x70, 0x72, 0x6f,
	0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x11, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x6d,
	0x61, 0x78, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x12, 0x25, 0x0a, 0x0e,
	0x6d, 0x69, 0x6e, 0x5f, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x12,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x6d, 0x69, 0x6e, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74,
	0x69, 0x65, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x18,
	0x13, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x12,
	0x23, 0x0a, 0x04, 0x65, 0x6e, 0x75, 0x6d, 0x18, 0x14, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e,
	0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x41, 0x6e, 0x79, 0x52, 0x04,
	0x65, 0x6e, 0x75, 0x6d, 0x12, 0x59, 0x0a, 0x15, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x5f, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x15, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32,
	0x2e, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x50, 0x72, 0x6f, 0x70, 0x65,
	0x72, 0x74, 0x69, 0x65, 0x73, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x14, 0x61, 0x64, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x12,
	0x28, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e,
	0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x49,
	0x74, 0x65, 0x6d, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x2b, 0x0a, 0x05, 0x69, 0x74, 0x65,
	0x6d, 0x73, 0x18, 0x17, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61,
	0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x49, 0x74, 0x65, 0x6d, 0x52,
	0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x29, 0x0a, 0x06, 0x61, 0x6c, 0x6c, 0x5f, 0x6f, 0x66,
	0x18, 0x18, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69,
	0x2e, 0x76, 0x32, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x52, 0x05, 0x61, 0x6c, 0x6c, 0x4f,
	0x66, 0x12, 0x36, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18,
	0x19, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e,
	0x76, 0x32, 0x2e, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x52, 0x0a, 0x70,
	0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x12, 0x24, 0x0a, 0x0d, 0x64, 0x69, 0x73,
	0x63, 0x72, 0x69, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x64, 0x69, 0x73, 0x63, 0x72, 0x69, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x12,
	0x1b, 0x0a, 0x09, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x6f, 0x6e, 0x6c, 0x79, 0x18, 0x1b, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x08, 0x72, 0x65, 0x61, 0x64, 0x4f, 0x6e, 0x6c, 0x79, 0x12, 0x21, 0x0a, 0x03,
	0x78, 0x6d, 0x6c, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x6f, 0x70, 0x65, 0x6e,
	0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x58, 0x6d, 0x6c, 0x52, 0x03, 0x78, 0x6d, 0x6c, 0x12,
	0x3d, 0x0a, 0x0d, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x64, 0x6f, 0x63, 0x73,
	0x18, 0x1d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69,
	0x2e, 0x76, 0x32, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x44, 0x6f, 0x63, 0x73,
	0x52, 0x0c, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x44, 0x6f, 0x63, 0x73, 0x12, 0x29,
	0x0a, 0x07, 0x65, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0f, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x41, 0x6e, 0x79,
	0x52, 0x07, 0x65, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x12, 0x3f, 0x0a, 0x10, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x1f, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32,
	0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x41, 0x6e, 0x79, 0x52, 0x0f, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x7e, 0x0a, 0x0a, 0x53, 0x63,
	0x68, 0x65, 0x6d, 0x61, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x2c, 0x0a, 0x06, 0x73, 0x63, 0x68, 0x65,
	0x6d, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61,
	0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x48, 0x00, 0x52, 0x06,
	0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x12, 0x39, 0x0a, 0x0b, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73,
	0x63, 0x68, 0x65, 0x6d, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x6f, 0x70,
	0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x63, 0x68,
	0x65, 0x6d, 0x61, 0x48, 0x00, 0x52, 0x0a, 0x66, 0x69, 0x6c, 0x65, 0x53, 0x63, 0x68, 0x65, 0x6d,
	0x61, 0x42, 0x07, 0x0a, 0x05, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x22, 0x74, 0x0a, 0x13, 0x53, 0x65,
	0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x12, 0x5d, 0x0a, 0x15, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f,
	0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x28, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x4e, 0x61,
	0x6d, 0x65, 0x64, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x44, 0x65, 0x66, 0x69, 0x6e,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x14, 0x61, 0x64, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73,
	0x22, 0xe9, 0x04, 0x0a, 0x17, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x44, 0x65, 0x66,
	0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x6d, 0x0a, 0x1d,
	0x62, 0x61, 0x73, 0x69, 0x63, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32,
	0x2e, 0x42, 0x61, 0x73, 0x69, 0x63, 0x41, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x48, 0x00, 0x52, 0x1b,
	0x62, 0x61, 0x73, 0x69, 0x63, 0x41, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x12, 0x46, 0x0a, 0x10, 0x61,
	0x70, 0x69, 0x5f, 0x6b, 0x65, 0x79, 0x5f, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e,
	0x76, 0x32, 0x2e, 0x41, 0x70, 0x69, 0x4b, 0x65, 0x79, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74,
	0x79, 0x48, 0x00, 0x52, 0x0e, 0x61, 0x70, 0x69, 0x4b, 0x65, 0x79, 0x53, 0x65, 0x63, 0x75, 0x72,
	0x69, 0x74, 0x79, 0x12, 0x5e, 0x0a, 0x18, 0x6f, 0x61, 0x75, 0x74, 0x68, 0x32, 0x5f, 0x69, 0x6d,
	0x70, 0x6c, 0x69, 0x63, 0x69, 0x74, 0x5f, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e,
	0x76, 0x32, 0x2e, 0x4f, 0x61, 0x75, 0x74, 0x68, 0x32, 0x49, 0x6d, 0x70, 0x6c, 0x69, 0x63, 0x69,
	0x74, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x48, 0x00, 0x52, 0x16, 0x6f, 0x61, 0x75,
	0x74, 0x68, 0x32, 0x49, 0x6d, 0x70, 0x6c, 0x69, 0x63, 0x69, 0x74, 0x53, 0x65, 0x63, 0x75, 0x72,
	0x69, 0x74, 0x79, 0x12, 0x5e, 0x0a, 0x18, 0x6f, 0x61, 0x75, 0x74, 0x68, 0x32, 0x5f, 0x70, 0x61,
	0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x5f, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e,
	0x76, 0x32, 0x2e, 0x4f, 0x61, 0x75, 0x74, 0x68, 0x32, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72,
	0x64, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x48, 0x00, 0x52, 0x16, 0x6f, 0x61, 0x75,
	0x74, 0x68, 0x32, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x53, 0x65, 0x63, 0x75, 0x72,
	0x69, 0x74, 0x79, 0x12, 0x67, 0x0a, 0x1b, 0x6f, 0x61, 0x75, 0x74, 0x68, 0x32, 0x5f, 0x61, 0x70,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69,
	0x74, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61,
	0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x4f, 0x61, 0x75, 0x74, 0x68, 0x32, 0x41, 0x70, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x48,
	0x00, 0x52, 0x19, 0x6f, 0x61, 0x75, 0x74, 0x68, 0x32, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x12, 0x65, 0x0a, 0x1b,
	0x6f, 0x61, 0x75, 0x74, 0x68, 0x32, 0x5f, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x5f, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x24, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x4f,
	0x61, 0x75, 0x74, 0x68, 0x32, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x53,
	0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x48, 0x00, 0x52, 0x18, 0x6f, 0x61, 0x75, 0x74, 0x68,
	0x32, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x53, 0x65, 0x63, 0x75, 0x72,
	0x69, 0x74, 0x79, 0x42, 0x07, 0x0a, 0x05, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x22, 0x68, 0x0a, 0x13,
	0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x12, 0x51, 0x0a, 0x15, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x5f, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e,
	0x4e, 0x61, 0x6d, 0x65, 0x64, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x41, 0x72, 0x72, 0x61, 0x79,
	0x52, 0x14, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x50, 0x72, 0x6f, 0x70,
	0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x22, 0x23, 0x0a, 0x0b, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x41, 0x72, 0x72, 0x61, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0xbb, 0x01, 0x0a, 0x03,
	0x54, 0x61, 0x67, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3d, 0x0a, 0x0d, 0x65, 0x78, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x64, 0x6f, 0x63, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x45, 0x78,
	0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x44, 0x6f, 0x63, 0x73, 0x52, 0x0c, 0x65, 0x78, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x6c, 0x44, 0x6f, 0x63, 0x73, 0x12, 0x3f, 0x0a, 0x10, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e,
	0x4e, 0x61, 0x6d, 0x65, 0x64, 0x41, 0x6e, 0x79, 0x52, 0x0f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x20, 0x0a, 0x08, 0x54, 0x79, 0x70,
	0x65, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x5c, 0x0a, 0x0f, 0x56,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x49,
	0x0a, 0x15, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x70, 0x72, 0x6f,
	0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e,
	0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x64,
	0x41, 0x6e, 0x79, 0x52, 0x14, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x50,
	0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x22, 0xc8, 0x01, 0x0a, 0x03, 0x58, 0x6d,
	0x6c, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x72, 0x65, 0x66, 0x69, 0x78, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x72, 0x65, 0x66, 0x69, 0x78, 0x12, 0x1c, 0x0a, 0x09, 0x61,
	0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09,
	0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x77, 0x72, 0x61,
	0x70, 0x70, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x77, 0x72, 0x61, 0x70,
	0x70, 0x65, 0x64, 0x12, 0x3f, 0x0a, 0x10, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x65, 0x78,
	0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e,
	0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x32, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x64,
	0x41, 0x6e, 0x79, 0x52, 0x0f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x45, 0x78, 0x74, 0x65, 0x6e,
	0x73, 0x69, 0x6f, 0x6e, 0x42, 0x3e, 0x0a, 0x0e, 0x6f, 0x72, 0x67, 0x2e, 0x6f, 0x70, 0x65, 0x6e,
	0x61, 0x70, 0x69, 0x5f, 0x76, 0x32, 0x42, 0x0c, 0x4f, 0x70, 0x65, 0x6e, 0x41, 0x50, 0x49, 0x50,
	0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01, 0x5a, 0x16, 0x2e, 0x2f, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70,
	0x69, 0x76, 0x32, 0x3b, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x5f, 0x76, 0x32, 0xa2, 0x02,
	0x03, 0x4f, 0x41, 0x53, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_openapiv2_OpenAPIv2_proto_rawDescOnce sync.Once
	file_openapiv2_OpenAPIv2_proto_rawDescData = file_openapiv2_OpenAPIv2_proto_rawDesc
)

func file_openapiv2_OpenAPIv2_proto_rawDescGZIP() []byte {
	file_openapiv2_OpenAPIv2_proto_rawDescOnce.Do(func() {
		file_openapiv2_OpenAPIv2_proto_rawDescData = protoimpl.X.CompressGZIP(file_openapiv2_OpenAPIv2_proto_rawDescData)
	})
	return file_openapiv2_OpenAPIv2_proto_rawDescData
}

var file_openapiv2_OpenAPIv2_proto_msgTypes = make([]protoimpl.MessageInfo, 60)
var file_openapiv2_OpenAPIv2_proto_goTypes = []any{
	(*AdditionalPropertiesItem)(nil),     // 0: openapi.v2.AdditionalPropertiesItem
	(*Any)(nil),                          // 1: openapi.v2.Any
	(*ApiKeySecurity)(nil),               // 2: openapi.v2.ApiKeySecurity
	(*BasicAuthenticationSecurity)(nil),  // 3: openapi.v2.BasicAuthenticationSecurity
	(*BodyParameter)(nil),                // 4: openapi.v2.BodyParameter
	(*Contact)(nil),                      // 5: openapi.v2.Contact
	(*Default)(nil),                      // 6: openapi.v2.Default
	(*Definitions)(nil),                  // 7: openapi.v2.Definitions
	(*Document)(nil),                     // 8: openapi.v2.Document
	(*Examples)(nil),                     // 9: openapi.v2.Examples
	(*ExternalDocs)(nil),                 // 10: openapi.v2.ExternalDocs
	(*FileSchema)(nil),                   // 11: openapi.v2.FileSchema
	(*FormDataParameterSubSchema)(nil),   // 12: openapi.v2.FormDataParameterSubSchema
	(*Header)(nil),                       // 13: openapi.v2.Header
	(*HeaderParameterSubSchema)(nil),     // 14: openapi.v2.HeaderParameterSubSchema
	(*Headers)(nil),                      // 15: openapi.v2.Headers
	(*Info)(nil),                         // 16: openapi.v2.Info
	(*ItemsItem)(nil),                    // 17: openapi.v2.ItemsItem
	(*JsonReference)(nil),                // 18: openapi.v2.JsonReference
	(*License)(nil),                      // 19: openapi.v2.License
	(*NamedAny)(nil),                     // 20: openapi.v2.NamedAny
	(*NamedHeader)(nil),                  // 21: openapi.v2.NamedHeader
	(*NamedParameter)(nil),               // 22: openapi.v2.NamedParameter
	(*NamedPathItem)(nil),                // 23: openapi.v2.NamedPathItem
	(*NamedResponse)(nil),                // 24: openapi.v2.NamedResponse
	(*NamedResponseValue)(nil),           // 25: openapi.v2.NamedResponseValue
	(*NamedSchema)(nil),                  // 26: openapi.v2.NamedSchema
	(*NamedSecurityDefinitionsItem)(nil), // 27: openapi.v2.NamedSecurityDefinitionsItem
	(*NamedString)(nil),                  // 28: openapi.v2.NamedString
	(*NamedStringArray)(nil),             // 29: openapi.v2.NamedStringArray
	(*NonBodyParameter)(nil),             // 30: openapi.v2.NonBodyParameter
	(*Oauth2AccessCodeSecurity)(nil),     // 31: openapi.v2.Oauth2AccessCodeSecurity
	(*Oauth2ApplicationSecurity)(nil),    // 32: openapi.v2.Oauth2ApplicationSecurity
	(*Oauth2ImplicitSecurity)(nil),       // 33: openapi.v2.Oauth2ImplicitSecurity
	(*Oauth2PasswordSecurity)(nil),       // 34: openapi.v2.Oauth2PasswordSecurity
	(*Oauth2Scopes)(nil),                 // 35: openapi.v2.Oauth2Scopes
	(*Operation)(nil),                    // 36: openapi.v2.Operation
	(*Parameter)(nil),                    // 37: openapi.v2.Parameter
	(*ParameterDefinitions)(nil),         // 38: openapi.v2.ParameterDefinitions
	(*ParametersItem)(nil),               // 39: openapi.v2.ParametersItem
	(*PathItem)(nil),                     // 40: openapi.v2.PathItem
	(*PathParameterSubSchema)(nil),       // 41: openapi.v2.PathParameterSubSchema
	(*Paths)(nil),                        // 42: openapi.v2.Paths
	(*PrimitivesItems)(nil),              // 43: openapi.v2.PrimitivesItems
	(*Properties)(nil),                   // 44: openapi.v2.Properties
	(*QueryParameterSubSchema)(nil),      // 45: openapi.v2.QueryParameterSubSchema
	(*Response)(nil),                     // 46: openapi.v2.Response
	(*ResponseDefinitions)(nil),          // 47: openapi.v2.ResponseDefinitions
	(*ResponseValue)(nil),                // 48: openapi.v2.ResponseValue
	(*Responses)(nil),                    // 49: openapi.v2.Responses
	(*Schema)(nil),                       // 50: openapi.v2.Schema
	(*SchemaItem)(nil),                   // 51: openapi.v2.SchemaItem
	(*SecurityDefinitions)(nil),          // 52: openapi.v2.SecurityDefinitions
	(*SecurityDefinitionsItem)(nil),      // 53: openapi.v2.SecurityDefinitionsItem
	(*SecurityRequirement)(nil),          // 54: openapi.v2.SecurityRequirement
	(*StringArray)(nil),                  // 55: openapi.v2.StringArray
	(*Tag)(nil),                          // 56: openapi.v2.Tag
	(*TypeItem)(nil),                     // 57: openapi.v2.TypeItem
	(*VendorExtension)(nil),              // 58: openapi.v2.VendorExtension
	(*Xml)(nil),                          // 59: openapi.v2.Xml
	(*anypb.Any)(nil),                    // 60: google.protobuf.Any
}
var file_openapiv2_OpenAPIv2_proto_depIdxs = []int32{
	50,  // 0: openapi.v2.AdditionalPropertiesItem.schema:type_name -> openapi.v2.Schema
	60,  // 1: openapi.v2.Any.value:type_name -> google.protobuf.Any
	20,  // 2: openapi.v2.ApiKeySecurity.vendor_extension:type_name -> openapi.v2.NamedAny
	20,  // 3: openapi.v2.BasicAuthenticationSecurity.vendor_extension:type_name -> openapi.v2.NamedAny
	50,  // 4: openapi.v2.BodyParameter.schema:type_name -> openapi.v2.Schema
	20,  // 5: openapi.v2.BodyParameter.vendor_extension:type_name -> openapi.v2.NamedAny
	20,  // 6: openapi.v2.Contact.vendor_extension:type_name -> openapi.v2.NamedAny
	20,  // 7: openapi.v2.Default.additional_properties:type_name -> openapi.v2.NamedAny
	26,  // 8: openapi.v2.Definitions.additional_properties:type_name -> openapi.v2.NamedSchema
	16,  // 9: openapi.v2.Document.info:type_name -> openapi.v2.Info
	42,  // 10: openapi.v2.Document.paths:type_name -> openapi.v2.Paths
	7,   // 11: openapi.v2.Document.definitions:type_name -> openapi.v2.Definitions
	38,  // 12: openapi.v2.Document.parameters:type_name -> openapi.v2.ParameterDefinitions
	47,  // 13: openapi.v2.Document.responses:type_name -> openapi.v2.ResponseDefinitions
	54,  // 14: openapi.v2.Document.security:type_name -> openapi.v2.SecurityRequirement
	52,  // 15: openapi.v2.Document.security_definitions:type_name -> openapi.v2.SecurityDefinitions
	56,  // 16: openapi.v2.Document.tags:type_name -> openapi.v2.Tag
	10,  // 17: openapi.v2.Document.external_docs:type_name -> openapi.v2.ExternalDocs
	20,  // 18: openapi.v2.Document.vendor_extension:type_name -> openapi.v2.NamedAny
	20,  // 19: openapi.v2.Examples.additional_properties:type_name -> openapi.v2.NamedAny
	20,  // 20: openapi.v2.ExternalDocs.vendor_extension:type_name -> openapi.v2.NamedAny
	1,   // 21: openapi.v2.FileSchema.default:type_name -> openapi.v2.Any
	10,  // 22: openapi.v2.FileSchema.external_docs:type_name -> openapi.v2.ExternalDocs
	1,   // 23: openapi.v2.FileSchema.example:type_name -> openapi.v2.Any
	20,  // 24: openapi.v2.FileSchema.vendor_extension:type_name -> openapi.v2.NamedAny
	43,  // 25: openapi.v2.FormDataParameterSubSchema.items:type_name -> openapi.v2.PrimitivesItems
	1,   // 26: openapi.v2.FormDataParameterSubSchema.default:type_name -> openapi.v2.Any
	1,   // 27: openapi.v2.FormDataParameterSubSchema.enum:type_name -> openapi.v2.Any
	20,  // 28: openapi.v2.FormDataParameterSubSchema.vendor_extension:type_name -> openapi.v2.NamedAny
	43,  // 29: openapi.v2.Header.items:type_name -> openapi.v2.PrimitivesItems
	1,   // 30: openapi.v2.Header.default:type_name -> openapi.v2.Any
	1,   // 31: openapi.v2.Header.enum:type_name -> openapi.v2.Any
	20,  // 32: openapi.v2.Header.vendor_extension:type_name -> openapi.v2.NamedAny
	43,  // 33: openapi.v2.HeaderParameterSubSchema.items:type_name -> openapi.v2.PrimitivesItems
	1,   // 34: openapi.v2.HeaderParameterSubSchema.default:type_name -> openapi.v2.Any
	1,   // 35: openapi.v2.HeaderParameterSubSchema.enum:type_name -> openapi.v2.Any
	20,  // 36: openapi.v2.HeaderParameterSubSchema.vendor_extension:type_name -> openapi.v2.NamedAny
	21,  // 37: openapi.v2.Headers.additional_properties:type_name -> openapi.v2.NamedHeader
	5,   // 38: openapi.v2.Info.contact:type_name -> openapi.v2.Contact
	19,  // 39: openapi.v2.Info.license:type_name -> openapi.v2.License
	20,  // 40: openapi.v2.Info.vendor_extension:type_name -> openapi.v2.NamedAny
	50,  // 41: openapi.v2.ItemsItem.schema:type_name -> openapi.v2.Schema
	20,  // 42: openapi.v2.License.vendor_extension:type_name -> openapi.v2.NamedAny
	1,   // 43: openapi.v2.NamedAny.value:type_name -> openapi.v2.Any
	13,  // 44: openapi.v2.NamedHeader.value:type_name -> openapi.v2.Header
	37,  // 45: openapi.v2.NamedParameter.value:type_name -> openapi.v2.Parameter
	40,  // 46: openapi.v2.NamedPathItem.value:type_name -> openapi.v2.PathItem
	46,  // 47: openapi.v2.NamedResponse.value:type_name -> openapi.v2.Response
	48,  // 48: openapi.v2.NamedResponseValue.value:type_name -> openapi.v2.ResponseValue
	50,  // 49: openapi.v2.NamedSchema.value:type_name -> openapi.v2.Schema
	53,  // 50: openapi.v2.NamedSecurityDefinitionsItem.value:type_name -> openapi.v2.SecurityDefinitionsItem
	55,  // 51: openapi.v2.NamedStringArray.value:type_name -> openapi.v2.StringArray
	14,  // 52: openapi.v2.NonBodyParameter.header_parameter_sub_schema:type_name -> openapi.v2.HeaderParameterSubSchema
	12,  // 53: openapi.v2.NonBodyParameter.form_data_parameter_sub_schema:type_name -> openapi.v2.FormDataParameterSubSchema
	45,  // 54: openapi.v2.NonBodyParameter.query_parameter_sub_schema:type_name -> openapi.v2.QueryParameterSubSchema
	41,  // 55: openapi.v2.NonBodyParameter.path_parameter_sub_schema:type_name -> openapi.v2.PathParameterSubSchema
	35,  // 56: openapi.v2.Oauth2AccessCodeSecurity.scopes:type_name -> openapi.v2.Oauth2Scopes
	20,  // 57: openapi.v2.Oauth2AccessCodeSecurity.vendor_extension:type_name -> openapi.v2.NamedAny
	35,  // 58: openapi.v2.Oauth2ApplicationSecurity.scopes:type_name -> openapi.v2.Oauth2Scopes
	20,  // 59: openapi.v2.Oauth2ApplicationSecurity.vendor_extension:type_name -> openapi.v2.NamedAny
	35,  // 60: openapi.v2.Oauth2ImplicitSecurity.scopes:type_name -> openapi.v2.Oauth2Scopes
	20,  // 61: openapi.v2.Oauth2ImplicitSecurity.vendor_extension:type_name -> openapi.v2.NamedAny
	35,  // 62: openapi.v2.Oauth2PasswordSecurity.scopes:type_name -> openapi.v2.Oauth2Scopes
	20,  // 63: openapi.v2.Oauth2PasswordSecurity.vendor_extension:type_name -> openapi.v2.NamedAny
	28,  // 64: openapi.v2.Oauth2Scopes.additional_properties:type_name -> openapi.v2.NamedString
	10,  // 65: openapi.v2.Operation.external_docs:type_name -> openapi.v2.ExternalDocs
	39,  // 66: openapi.v2.Operation.parameters:type_name -> openapi.v2.ParametersItem
	49,  // 67: openapi.v2.Operation.responses:type_name -> openapi.v2.Responses
	54,  // 68: openapi.v2.Operation.security:type_name -> openapi.v2.SecurityRequirement
	20,  // 69: openapi.v2.Operation.vendor_extension:type_name -> openapi.v2.NamedAny
	4,   // 70: openapi.v2.Parameter.body_parameter:type_name -> openapi.v2.BodyParameter
	30,  // 71: openapi.v2.Parameter.non_body_parameter:type_name -> openapi.v2.NonBodyParameter
	22,  // 72: openapi.v2.ParameterDefinitions.additional_properties:type_name -> openapi.v2.NamedParameter
	37,  // 73: openapi.v2.ParametersItem.parameter:type_name -> openapi.v2.Parameter
	18,  // 74: openapi.v2.ParametersItem.json_reference:type_name -> openapi.v2.JsonReference
	36,  // 75: openapi.v2.PathItem.get:type_name -> openapi.v2.Operation
	36,  // 76: openapi.v2.PathItem.put:type_name -> openapi.v2.Operation
	36,  // 77: openapi.v2.PathItem.post:type_name -> openapi.v2.Operation
	36,  // 78: openapi.v2.PathItem.delete:type_name -> openapi.v2.Operation
	36,  // 79: openapi.v2.PathItem.options:type_name -> openapi.v2.Operation
	36,  // 80: openapi.v2.PathItem.head:type_name -> openapi.v2.Operation
	36,  // 81: openapi.v2.PathItem.patch:type_name -> openapi.v2.Operation
	39,  // 82: openapi.v2.PathItem.parameters:type_name -> openapi.v2.ParametersItem
	20,  // 83: openapi.v2.PathItem.vendor_extension:type_name -> openapi.v2.NamedAny
	43,  // 84: openapi.v2.PathParameterSubSchema.items:type_name -> openapi.v2.PrimitivesItems
	1,   // 85: openapi.v2.PathParameterSubSchema.default:type_name -> openapi.v2.Any
	1,   // 86: openapi.v2.PathParameterSubSchema.enum:type_name -> openapi.v2.Any
	20,  // 87: openapi.v2.PathParameterSubSchema.vendor_extension:type_name -> openapi.v2.NamedAny
	20,  // 88: openapi.v2.Paths.vendor_extension:type_name -> openapi.v2.NamedAny
	23,  // 89: openapi.v2.Paths.path:type_name -> openapi.v2.NamedPathItem
	43,  // 90: openapi.v2.PrimitivesItems.items:type_name -> openapi.v2.PrimitivesItems
	1,   // 91: openapi.v2.PrimitivesItems.default:type_name -> openapi.v2.Any
	1,   // 92: openapi.v2.PrimitivesItems.enum:type_name -> openapi.v2.Any
	20,  // 93: openapi.v2.PrimitivesItems.vendor_extension:type_name -> openapi.v2.NamedAny
	26,  // 94: openapi.v2.Properties.additional_properties:type_name -> openapi.v2.NamedSchema
	43,  // 95: openapi.v2.QueryParameterSubSchema.items:type_name -> openapi.v2.PrimitivesItems
	1,   // 96: openapi.v2.QueryParameterSubSchema.default:type_name -> openapi.v2.Any
	1,   // 97: openapi.v2.QueryParameterSubSchema.enum:type_name -> openapi.v2.Any
	20,  // 98: openapi.v2.QueryParameterSubSchema.vendor_extension:type_name -> openapi.v2.NamedAny
	51,  // 99: openapi.v2.Response.schema:type_name -> openapi.v2.SchemaItem
	15,  // 100: openapi.v2.Response.headers:type_name -> openapi.v2.Headers
	9,   // 101: openapi.v2.Response.examples:type_name -> openapi.v2.Examples
	20,  // 102: openapi.v2.Response.vendor_extension:type_name -> openapi.v2.NamedAny
	24,  // 103: openapi.v2.ResponseDefinitions.additional_properties:type_name -> openapi.v2.NamedResponse
	46,  // 104: openapi.v2.ResponseValue.response:type_name -> openapi.v2.Response
	18,  // 105: openapi.v2.ResponseValue.json_reference:type_name -> openapi.v2.JsonReference
	25,  // 106: openapi.v2.Responses.response_code:type_name -> openapi.v2.NamedResponseValue
	20,  // 107: openapi.v2.Responses.vendor_extension:type_name -> openapi.v2.NamedAny
	1,   // 108: openapi.v2.Schema.default:type_name -> openapi.v2.Any
	1,   // 109: openapi.v2.Schema.enum:type_name -> openapi.v2.Any
	0,   // 110: openapi.v2.Schema.additional_properties:type_name -> openapi.v2.AdditionalPropertiesItem
	57,  // 111: openapi.v2.Schema.type:type_name -> openapi.v2.TypeItem
	17,  // 112: openapi.v2.Schema.items:type_name -> openapi.v2.ItemsItem
	50,  // 113: openapi.v2.Schema.all_of:type_name -> openapi.v2.Schema
	44,  // 114: openapi.v2.Schema.properties:type_name -> openapi.v2.Properties
	59,  // 115: openapi.v2.Schema.xml:type_name -> openapi.v2.Xml
	10,  // 116: openapi.v2.Schema.external_docs:type_name -> openapi.v2.ExternalDocs
	1,   // 117: openapi.v2.Schema.example:type_name -> openapi.v2.Any
	20,  // 118: openapi.v2.Schema.vendor_extension:type_name -> openapi.v2.NamedAny
	50,  // 119: openapi.v2.SchemaItem.schema:type_name -> openapi.v2.Schema
	11,  // 120: openapi.v2.SchemaItem.file_schema:type_name -> openapi.v2.FileSchema
	27,  // 121: openapi.v2.SecurityDefinitions.additional_properties:type_name -> openapi.v2.NamedSecurityDefinitionsItem
	3,   // 122: openapi.v2.SecurityDefinitionsItem.basic_authentication_security:type_name -> openapi.v2.BasicAuthenticationSecurity
	2,   // 123: openapi.v2.SecurityDefinitionsItem.api_key_security:type_name -> openapi.v2.ApiKeySecurity
	33,  // 124: openapi.v2.SecurityDefinitionsItem.oauth2_implicit_security:type_name -> openapi.v2.Oauth2ImplicitSecurity
	34,  // 125: openapi.v2.SecurityDefinitionsItem.oauth2_password_security:type_name -> openapi.v2.Oauth2PasswordSecurity
	32,  // 126: openapi.v2.SecurityDefinitionsItem.oauth2_application_security:type_name -> openapi.v2.Oauth2ApplicationSecurity
	31,  // 127: openapi.v2.SecurityDefinitionsItem.oauth2_access_code_security:type_name -> openapi.v2.Oauth2AccessCodeSecurity
	29,  // 128: openapi.v2.SecurityRequirement.additional_properties:type_name -> openapi.v2.NamedStringArray
	10,  // 129: openapi.v2.Tag.external_docs:type_name -> openapi.v2.ExternalDocs
	20,  // 130: openapi.v2.Tag.vendor_extension:type_name -> openapi.v2.NamedAny
	20,  // 131: openapi.v2.VendorExtension.additional_properties:type_name -> openapi.v2.NamedAny
	20,  // 132: openapi.v2.Xml.vendor_extension:type_name -> openapi.v2.NamedAny
	133, // [133:133] is the sub-list for method output_type
	133, // [133:133] is the sub-list for method input_type
	133, // [133:133] is the sub-list for extension type_name
	133, // [133:133] is the sub-list for extension extendee
	0,   // [0:133] is the sub-list for field type_name
}

func init() { file_openapiv2_OpenAPIv2_proto_init() }
func file_openapiv2_OpenAPIv2_proto_init() {
	if File_openapiv2_OpenAPIv2_proto != nil {
		return
	}
	file_openapiv2_OpenAPIv2_proto_msgTypes[0].OneofWrappers = []any{
		(*AdditionalPropertiesItem_Schema)(nil),
		(*AdditionalPropertiesItem_Boolean)(nil),
	}
	file_openapiv2_OpenAPIv2_proto_msgTypes[30].OneofWrappers = []any{
		(*NonBodyParameter_HeaderParameterSubSchema)(nil),
		(*NonBodyParameter_FormDataParameterSubSchema)(nil),
		(*NonBodyParameter_QueryParameterSubSchema)(nil),
		(*NonBodyParameter_PathParameterSubSchema)(nil),
	}
	file_openapiv2_OpenAPIv2_proto_msgTypes[37].OneofWrappers = []any{
		(*Parameter_BodyParameter)(nil),
		(*Parameter_NonBodyParameter)(nil),
	}
	file_openapiv2_OpenAPIv2_proto_msgTypes[39].OneofWrappers = []any{
		(*ParametersItem_Parameter)(nil),
		(*ParametersItem_JsonReference)(nil),
	}
	file_openapiv2_OpenAPIv2_proto_msgTypes[48].OneofWrappers = []any{
		(*ResponseValue_Response)(nil),
		(*ResponseValue_JsonReference)(nil),
	}
	file_openapiv2_OpenAPIv2_proto_msgTypes[51].OneofWrappers = []any{
		(*SchemaItem_Schema)(nil),
		(*SchemaItem_FileSchema)(nil),
	}
	file_openapiv2_OpenAPIv2_proto_msgTypes[53].OneofWrappers = []any{
		(*SecurityDefinitionsItem_BasicAuthenticationSecurity)(nil),
		(*SecurityDefinitionsItem_ApiKeySecurity)(nil),
		(*SecurityDefinitionsItem_Oauth2ImplicitSecurity)(nil),
		(*SecurityDefinitionsItem_Oauth2PasswordSecurity)(nil),
		(*SecurityDefinitionsItem_Oauth2ApplicationSecurity)(nil),
		(*SecurityDefinitionsItem_Oauth2AccessCodeSecurity)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_openapiv2_OpenAPIv2_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   60,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_openapiv2_OpenAPIv2_proto_goTypes,
		DependencyIndexes: file_openapiv2_OpenAPIv2_proto_depIdxs,
		MessageInfos:      file_openapiv2_OpenAPIv2_proto_msgTypes,
	}.Build()
	File_openapiv2_OpenAPIv2_proto = out.File
	file_openapiv2_OpenAPIv2_proto_rawDesc = nil
	file_openapiv2_OpenAPIv2_proto_goTypes = nil
	file_openapiv2_OpenAPIv2_proto_depIdxs = nil
}
