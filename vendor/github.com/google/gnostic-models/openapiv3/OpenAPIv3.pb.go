// Copyright 2020 Google LLC. All Rights Reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//    http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// THIS FILE IS AUTOMATICALLY GENERATED.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.1
// 	protoc        v4.23.4
// source: openapiv3/OpenAPIv3.proto

package openapi_v3

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	anypb "google.golang.org/protobuf/types/known/anypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AdditionalPropertiesItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Oneof:
	//
	//	*AdditionalPropertiesItem_SchemaOrReference
	//	*AdditionalPropertiesItem_Boolean
	Oneof isAdditionalPropertiesItem_Oneof `protobuf_oneof:"oneof"`
}

func (x *AdditionalPropertiesItem) Reset() {
	*x = AdditionalPropertiesItem{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AdditionalPropertiesItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdditionalPropertiesItem) ProtoMessage() {}

func (x *AdditionalPropertiesItem) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdditionalPropertiesItem.ProtoReflect.Descriptor instead.
func (*AdditionalPropertiesItem) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{0}
}

func (m *AdditionalPropertiesItem) GetOneof() isAdditionalPropertiesItem_Oneof {
	if m != nil {
		return m.Oneof
	}
	return nil
}

func (x *AdditionalPropertiesItem) GetSchemaOrReference() *SchemaOrReference {
	if x, ok := x.GetOneof().(*AdditionalPropertiesItem_SchemaOrReference); ok {
		return x.SchemaOrReference
	}
	return nil
}

func (x *AdditionalPropertiesItem) GetBoolean() bool {
	if x, ok := x.GetOneof().(*AdditionalPropertiesItem_Boolean); ok {
		return x.Boolean
	}
	return false
}

type isAdditionalPropertiesItem_Oneof interface {
	isAdditionalPropertiesItem_Oneof()
}

type AdditionalPropertiesItem_SchemaOrReference struct {
	SchemaOrReference *SchemaOrReference `protobuf:"bytes,1,opt,name=schema_or_reference,json=schemaOrReference,proto3,oneof"`
}

type AdditionalPropertiesItem_Boolean struct {
	Boolean bool `protobuf:"varint,2,opt,name=boolean,proto3,oneof"`
}

func (*AdditionalPropertiesItem_SchemaOrReference) isAdditionalPropertiesItem_Oneof() {}

func (*AdditionalPropertiesItem_Boolean) isAdditionalPropertiesItem_Oneof() {}

type Any struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value *anypb.Any `protobuf:"bytes,1,opt,name=value,proto3" json:"value,omitempty"`
	Yaml  string     `protobuf:"bytes,2,opt,name=yaml,proto3" json:"yaml,omitempty"`
}

func (x *Any) Reset() {
	*x = Any{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Any) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Any) ProtoMessage() {}

func (x *Any) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Any.ProtoReflect.Descriptor instead.
func (*Any) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{1}
}

func (x *Any) GetValue() *anypb.Any {
	if x != nil {
		return x.Value
	}
	return nil
}

func (x *Any) GetYaml() string {
	if x != nil {
		return x.Yaml
	}
	return ""
}

type AnyOrExpression struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Oneof:
	//
	//	*AnyOrExpression_Any
	//	*AnyOrExpression_Expression
	Oneof isAnyOrExpression_Oneof `protobuf_oneof:"oneof"`
}

func (x *AnyOrExpression) Reset() {
	*x = AnyOrExpression{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AnyOrExpression) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnyOrExpression) ProtoMessage() {}

func (x *AnyOrExpression) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnyOrExpression.ProtoReflect.Descriptor instead.
func (*AnyOrExpression) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{2}
}

func (m *AnyOrExpression) GetOneof() isAnyOrExpression_Oneof {
	if m != nil {
		return m.Oneof
	}
	return nil
}

func (x *AnyOrExpression) GetAny() *Any {
	if x, ok := x.GetOneof().(*AnyOrExpression_Any); ok {
		return x.Any
	}
	return nil
}

func (x *AnyOrExpression) GetExpression() *Expression {
	if x, ok := x.GetOneof().(*AnyOrExpression_Expression); ok {
		return x.Expression
	}
	return nil
}

type isAnyOrExpression_Oneof interface {
	isAnyOrExpression_Oneof()
}

type AnyOrExpression_Any struct {
	Any *Any `protobuf:"bytes,1,opt,name=any,proto3,oneof"`
}

type AnyOrExpression_Expression struct {
	Expression *Expression `protobuf:"bytes,2,opt,name=expression,proto3,oneof"`
}

func (*AnyOrExpression_Any) isAnyOrExpression_Oneof() {}

func (*AnyOrExpression_Expression) isAnyOrExpression_Oneof() {}

// A map of possible out-of band callbacks related to the parent operation. Each value in the map is a Path Item Object that describes a set of requests that may be initiated by the API provider and the expected responses. The key value used to identify the callback object is an expression, evaluated at runtime, that identifies a URL to use for the callback operation.
type Callback struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Path                   []*NamedPathItem `protobuf:"bytes,1,rep,name=path,proto3" json:"path,omitempty"`
	SpecificationExtension []*NamedAny      `protobuf:"bytes,2,rep,name=specification_extension,json=specificationExtension,proto3" json:"specification_extension,omitempty"`
}

func (x *Callback) Reset() {
	*x = Callback{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Callback) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Callback) ProtoMessage() {}

func (x *Callback) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Callback.ProtoReflect.Descriptor instead.
func (*Callback) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{3}
}

func (x *Callback) GetPath() []*NamedPathItem {
	if x != nil {
		return x.Path
	}
	return nil
}

func (x *Callback) GetSpecificationExtension() []*NamedAny {
	if x != nil {
		return x.SpecificationExtension
	}
	return nil
}

type CallbackOrReference struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Oneof:
	//
	//	*CallbackOrReference_Callback
	//	*CallbackOrReference_Reference
	Oneof isCallbackOrReference_Oneof `protobuf_oneof:"oneof"`
}

func (x *CallbackOrReference) Reset() {
	*x = CallbackOrReference{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CallbackOrReference) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CallbackOrReference) ProtoMessage() {}

func (x *CallbackOrReference) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CallbackOrReference.ProtoReflect.Descriptor instead.
func (*CallbackOrReference) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{4}
}

func (m *CallbackOrReference) GetOneof() isCallbackOrReference_Oneof {
	if m != nil {
		return m.Oneof
	}
	return nil
}

func (x *CallbackOrReference) GetCallback() *Callback {
	if x, ok := x.GetOneof().(*CallbackOrReference_Callback); ok {
		return x.Callback
	}
	return nil
}

func (x *CallbackOrReference) GetReference() *Reference {
	if x, ok := x.GetOneof().(*CallbackOrReference_Reference); ok {
		return x.Reference
	}
	return nil
}

type isCallbackOrReference_Oneof interface {
	isCallbackOrReference_Oneof()
}

type CallbackOrReference_Callback struct {
	Callback *Callback `protobuf:"bytes,1,opt,name=callback,proto3,oneof"`
}

type CallbackOrReference_Reference struct {
	Reference *Reference `protobuf:"bytes,2,opt,name=reference,proto3,oneof"`
}

func (*CallbackOrReference_Callback) isCallbackOrReference_Oneof() {}

func (*CallbackOrReference_Reference) isCallbackOrReference_Oneof() {}

type CallbacksOrReferences struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AdditionalProperties []*NamedCallbackOrReference `protobuf:"bytes,1,rep,name=additional_properties,json=additionalProperties,proto3" json:"additional_properties,omitempty"`
}

func (x *CallbacksOrReferences) Reset() {
	*x = CallbacksOrReferences{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CallbacksOrReferences) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CallbacksOrReferences) ProtoMessage() {}

func (x *CallbacksOrReferences) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CallbacksOrReferences.ProtoReflect.Descriptor instead.
func (*CallbacksOrReferences) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{5}
}

func (x *CallbacksOrReferences) GetAdditionalProperties() []*NamedCallbackOrReference {
	if x != nil {
		return x.AdditionalProperties
	}
	return nil
}

// Holds a set of reusable objects for different aspects of the OAS. All objects defined within the components object will have no effect on the API unless they are explicitly referenced from properties outside the components object.
type Components struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Schemas                *SchemasOrReferences         `protobuf:"bytes,1,opt,name=schemas,proto3" json:"schemas,omitempty"`
	Responses              *ResponsesOrReferences       `protobuf:"bytes,2,opt,name=responses,proto3" json:"responses,omitempty"`
	Parameters             *ParametersOrReferences      `protobuf:"bytes,3,opt,name=parameters,proto3" json:"parameters,omitempty"`
	Examples               *ExamplesOrReferences        `protobuf:"bytes,4,opt,name=examples,proto3" json:"examples,omitempty"`
	RequestBodies          *RequestBodiesOrReferences   `protobuf:"bytes,5,opt,name=request_bodies,json=requestBodies,proto3" json:"request_bodies,omitempty"`
	Headers                *HeadersOrReferences         `protobuf:"bytes,6,opt,name=headers,proto3" json:"headers,omitempty"`
	SecuritySchemes        *SecuritySchemesOrReferences `protobuf:"bytes,7,opt,name=security_schemes,json=securitySchemes,proto3" json:"security_schemes,omitempty"`
	Links                  *LinksOrReferences           `protobuf:"bytes,8,opt,name=links,proto3" json:"links,omitempty"`
	Callbacks              *CallbacksOrReferences       `protobuf:"bytes,9,opt,name=callbacks,proto3" json:"callbacks,omitempty"`
	SpecificationExtension []*NamedAny                  `protobuf:"bytes,10,rep,name=specification_extension,json=specificationExtension,proto3" json:"specification_extension,omitempty"`
}

func (x *Components) Reset() {
	*x = Components{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Components) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Components) ProtoMessage() {}

func (x *Components) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Components.ProtoReflect.Descriptor instead.
func (*Components) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{6}
}

func (x *Components) GetSchemas() *SchemasOrReferences {
	if x != nil {
		return x.Schemas
	}
	return nil
}

func (x *Components) GetResponses() *ResponsesOrReferences {
	if x != nil {
		return x.Responses
	}
	return nil
}

func (x *Components) GetParameters() *ParametersOrReferences {
	if x != nil {
		return x.Parameters
	}
	return nil
}

func (x *Components) GetExamples() *ExamplesOrReferences {
	if x != nil {
		return x.Examples
	}
	return nil
}

func (x *Components) GetRequestBodies() *RequestBodiesOrReferences {
	if x != nil {
		return x.RequestBodies
	}
	return nil
}

func (x *Components) GetHeaders() *HeadersOrReferences {
	if x != nil {
		return x.Headers
	}
	return nil
}

func (x *Components) GetSecuritySchemes() *SecuritySchemesOrReferences {
	if x != nil {
		return x.SecuritySchemes
	}
	return nil
}

func (x *Components) GetLinks() *LinksOrReferences {
	if x != nil {
		return x.Links
	}
	return nil
}

func (x *Components) GetCallbacks() *CallbacksOrReferences {
	if x != nil {
		return x.Callbacks
	}
	return nil
}

func (x *Components) GetSpecificationExtension() []*NamedAny {
	if x != nil {
		return x.SpecificationExtension
	}
	return nil
}

// Contact information for the exposed API.
type Contact struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name                   string      `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Url                    string      `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	Email                  string      `protobuf:"bytes,3,opt,name=email,proto3" json:"email,omitempty"`
	SpecificationExtension []*NamedAny `protobuf:"bytes,4,rep,name=specification_extension,json=specificationExtension,proto3" json:"specification_extension,omitempty"`
}

func (x *Contact) Reset() {
	*x = Contact{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Contact) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Contact) ProtoMessage() {}

func (x *Contact) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Contact.ProtoReflect.Descriptor instead.
func (*Contact) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{7}
}

func (x *Contact) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Contact) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *Contact) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *Contact) GetSpecificationExtension() []*NamedAny {
	if x != nil {
		return x.SpecificationExtension
	}
	return nil
}

type DefaultType struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Oneof:
	//
	//	*DefaultType_Number
	//	*DefaultType_Boolean
	//	*DefaultType_String_
	Oneof isDefaultType_Oneof `protobuf_oneof:"oneof"`
}

func (x *DefaultType) Reset() {
	*x = DefaultType{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DefaultType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DefaultType) ProtoMessage() {}

func (x *DefaultType) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DefaultType.ProtoReflect.Descriptor instead.
func (*DefaultType) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{8}
}

func (m *DefaultType) GetOneof() isDefaultType_Oneof {
	if m != nil {
		return m.Oneof
	}
	return nil
}

func (x *DefaultType) GetNumber() float64 {
	if x, ok := x.GetOneof().(*DefaultType_Number); ok {
		return x.Number
	}
	return 0
}

func (x *DefaultType) GetBoolean() bool {
	if x, ok := x.GetOneof().(*DefaultType_Boolean); ok {
		return x.Boolean
	}
	return false
}

func (x *DefaultType) GetString_() string {
	if x, ok := x.GetOneof().(*DefaultType_String_); ok {
		return x.String_
	}
	return ""
}

type isDefaultType_Oneof interface {
	isDefaultType_Oneof()
}

type DefaultType_Number struct {
	Number float64 `protobuf:"fixed64,1,opt,name=number,proto3,oneof"`
}

type DefaultType_Boolean struct {
	Boolean bool `protobuf:"varint,2,opt,name=boolean,proto3,oneof"`
}

type DefaultType_String_ struct {
	String_ string `protobuf:"bytes,3,opt,name=string,proto3,oneof"`
}

func (*DefaultType_Number) isDefaultType_Oneof() {}

func (*DefaultType_Boolean) isDefaultType_Oneof() {}

func (*DefaultType_String_) isDefaultType_Oneof() {}

// When request bodies or response payloads may be one of a number of different schemas, a `discriminator` object can be used to aid in serialization, deserialization, and validation.  The discriminator is a specific object in a schema which is used to inform the consumer of the specification of an alternative schema based on the value associated with it.  When using the discriminator, _inline_ schemas will not be considered.
type Discriminator struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PropertyName           string      `protobuf:"bytes,1,opt,name=property_name,json=propertyName,proto3" json:"property_name,omitempty"`
	Mapping                *Strings    `protobuf:"bytes,2,opt,name=mapping,proto3" json:"mapping,omitempty"`
	SpecificationExtension []*NamedAny `protobuf:"bytes,3,rep,name=specification_extension,json=specificationExtension,proto3" json:"specification_extension,omitempty"`
}

func (x *Discriminator) Reset() {
	*x = Discriminator{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Discriminator) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Discriminator) ProtoMessage() {}

func (x *Discriminator) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Discriminator.ProtoReflect.Descriptor instead.
func (*Discriminator) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{9}
}

func (x *Discriminator) GetPropertyName() string {
	if x != nil {
		return x.PropertyName
	}
	return ""
}

func (x *Discriminator) GetMapping() *Strings {
	if x != nil {
		return x.Mapping
	}
	return nil
}

func (x *Discriminator) GetSpecificationExtension() []*NamedAny {
	if x != nil {
		return x.SpecificationExtension
	}
	return nil
}

type Document struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Openapi                string                 `protobuf:"bytes,1,opt,name=openapi,proto3" json:"openapi,omitempty"`
	Info                   *Info                  `protobuf:"bytes,2,opt,name=info,proto3" json:"info,omitempty"`
	Servers                []*Server              `protobuf:"bytes,3,rep,name=servers,proto3" json:"servers,omitempty"`
	Paths                  *Paths                 `protobuf:"bytes,4,opt,name=paths,proto3" json:"paths,omitempty"`
	Components             *Components            `protobuf:"bytes,5,opt,name=components,proto3" json:"components,omitempty"`
	Security               []*SecurityRequirement `protobuf:"bytes,6,rep,name=security,proto3" json:"security,omitempty"`
	Tags                   []*Tag                 `protobuf:"bytes,7,rep,name=tags,proto3" json:"tags,omitempty"`
	ExternalDocs           *ExternalDocs          `protobuf:"bytes,8,opt,name=external_docs,json=externalDocs,proto3" json:"external_docs,omitempty"`
	SpecificationExtension []*NamedAny            `protobuf:"bytes,9,rep,name=specification_extension,json=specificationExtension,proto3" json:"specification_extension,omitempty"`
}

func (x *Document) Reset() {
	*x = Document{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Document) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Document) ProtoMessage() {}

func (x *Document) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Document.ProtoReflect.Descriptor instead.
func (*Document) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{10}
}

func (x *Document) GetOpenapi() string {
	if x != nil {
		return x.Openapi
	}
	return ""
}

func (x *Document) GetInfo() *Info {
	if x != nil {
		return x.Info
	}
	return nil
}

func (x *Document) GetServers() []*Server {
	if x != nil {
		return x.Servers
	}
	return nil
}

func (x *Document) GetPaths() *Paths {
	if x != nil {
		return x.Paths
	}
	return nil
}

func (x *Document) GetComponents() *Components {
	if x != nil {
		return x.Components
	}
	return nil
}

func (x *Document) GetSecurity() []*SecurityRequirement {
	if x != nil {
		return x.Security
	}
	return nil
}

func (x *Document) GetTags() []*Tag {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *Document) GetExternalDocs() *ExternalDocs {
	if x != nil {
		return x.ExternalDocs
	}
	return nil
}

func (x *Document) GetSpecificationExtension() []*NamedAny {
	if x != nil {
		return x.SpecificationExtension
	}
	return nil
}

// A single encoding definition applied to a single schema property.
type Encoding struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ContentType            string               `protobuf:"bytes,1,opt,name=content_type,json=contentType,proto3" json:"content_type,omitempty"`
	Headers                *HeadersOrReferences `protobuf:"bytes,2,opt,name=headers,proto3" json:"headers,omitempty"`
	Style                  string               `protobuf:"bytes,3,opt,name=style,proto3" json:"style,omitempty"`
	Explode                bool                 `protobuf:"varint,4,opt,name=explode,proto3" json:"explode,omitempty"`
	AllowReserved          bool                 `protobuf:"varint,5,opt,name=allow_reserved,json=allowReserved,proto3" json:"allow_reserved,omitempty"`
	SpecificationExtension []*NamedAny          `protobuf:"bytes,6,rep,name=specification_extension,json=specificationExtension,proto3" json:"specification_extension,omitempty"`
}

func (x *Encoding) Reset() {
	*x = Encoding{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Encoding) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Encoding) ProtoMessage() {}

func (x *Encoding) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Encoding.ProtoReflect.Descriptor instead.
func (*Encoding) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{11}
}

func (x *Encoding) GetContentType() string {
	if x != nil {
		return x.ContentType
	}
	return ""
}

func (x *Encoding) GetHeaders() *HeadersOrReferences {
	if x != nil {
		return x.Headers
	}
	return nil
}

func (x *Encoding) GetStyle() string {
	if x != nil {
		return x.Style
	}
	return ""
}

func (x *Encoding) GetExplode() bool {
	if x != nil {
		return x.Explode
	}
	return false
}

func (x *Encoding) GetAllowReserved() bool {
	if x != nil {
		return x.AllowReserved
	}
	return false
}

func (x *Encoding) GetSpecificationExtension() []*NamedAny {
	if x != nil {
		return x.SpecificationExtension
	}
	return nil
}

type Encodings struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AdditionalProperties []*NamedEncoding `protobuf:"bytes,1,rep,name=additional_properties,json=additionalProperties,proto3" json:"additional_properties,omitempty"`
}

func (x *Encodings) Reset() {
	*x = Encodings{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Encodings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Encodings) ProtoMessage() {}

func (x *Encodings) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Encodings.ProtoReflect.Descriptor instead.
func (*Encodings) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{12}
}

func (x *Encodings) GetAdditionalProperties() []*NamedEncoding {
	if x != nil {
		return x.AdditionalProperties
	}
	return nil
}

type Example struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Summary                string      `protobuf:"bytes,1,opt,name=summary,proto3" json:"summary,omitempty"`
	Description            string      `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	Value                  *Any        `protobuf:"bytes,3,opt,name=value,proto3" json:"value,omitempty"`
	ExternalValue          string      `protobuf:"bytes,4,opt,name=external_value,json=externalValue,proto3" json:"external_value,omitempty"`
	SpecificationExtension []*NamedAny `protobuf:"bytes,5,rep,name=specification_extension,json=specificationExtension,proto3" json:"specification_extension,omitempty"`
}

func (x *Example) Reset() {
	*x = Example{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Example) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Example) ProtoMessage() {}

func (x *Example) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Example.ProtoReflect.Descriptor instead.
func (*Example) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{13}
}

func (x *Example) GetSummary() string {
	if x != nil {
		return x.Summary
	}
	return ""
}

func (x *Example) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Example) GetValue() *Any {
	if x != nil {
		return x.Value
	}
	return nil
}

func (x *Example) GetExternalValue() string {
	if x != nil {
		return x.ExternalValue
	}
	return ""
}

func (x *Example) GetSpecificationExtension() []*NamedAny {
	if x != nil {
		return x.SpecificationExtension
	}
	return nil
}

type ExampleOrReference struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Oneof:
	//
	//	*ExampleOrReference_Example
	//	*ExampleOrReference_Reference
	Oneof isExampleOrReference_Oneof `protobuf_oneof:"oneof"`
}

func (x *ExampleOrReference) Reset() {
	*x = ExampleOrReference{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExampleOrReference) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExampleOrReference) ProtoMessage() {}

func (x *ExampleOrReference) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExampleOrReference.ProtoReflect.Descriptor instead.
func (*ExampleOrReference) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{14}
}

func (m *ExampleOrReference) GetOneof() isExampleOrReference_Oneof {
	if m != nil {
		return m.Oneof
	}
	return nil
}

func (x *ExampleOrReference) GetExample() *Example {
	if x, ok := x.GetOneof().(*ExampleOrReference_Example); ok {
		return x.Example
	}
	return nil
}

func (x *ExampleOrReference) GetReference() *Reference {
	if x, ok := x.GetOneof().(*ExampleOrReference_Reference); ok {
		return x.Reference
	}
	return nil
}

type isExampleOrReference_Oneof interface {
	isExampleOrReference_Oneof()
}

type ExampleOrReference_Example struct {
	Example *Example `protobuf:"bytes,1,opt,name=example,proto3,oneof"`
}

type ExampleOrReference_Reference struct {
	Reference *Reference `protobuf:"bytes,2,opt,name=reference,proto3,oneof"`
}

func (*ExampleOrReference_Example) isExampleOrReference_Oneof() {}

func (*ExampleOrReference_Reference) isExampleOrReference_Oneof() {}

type ExamplesOrReferences struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AdditionalProperties []*NamedExampleOrReference `protobuf:"bytes,1,rep,name=additional_properties,json=additionalProperties,proto3" json:"additional_properties,omitempty"`
}

func (x *ExamplesOrReferences) Reset() {
	*x = ExamplesOrReferences{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExamplesOrReferences) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExamplesOrReferences) ProtoMessage() {}

func (x *ExamplesOrReferences) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExamplesOrReferences.ProtoReflect.Descriptor instead.
func (*ExamplesOrReferences) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{15}
}

func (x *ExamplesOrReferences) GetAdditionalProperties() []*NamedExampleOrReference {
	if x != nil {
		return x.AdditionalProperties
	}
	return nil
}

type Expression struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AdditionalProperties []*NamedAny `protobuf:"bytes,1,rep,name=additional_properties,json=additionalProperties,proto3" json:"additional_properties,omitempty"`
}

func (x *Expression) Reset() {
	*x = Expression{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Expression) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Expression) ProtoMessage() {}

func (x *Expression) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Expression.ProtoReflect.Descriptor instead.
func (*Expression) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{16}
}

func (x *Expression) GetAdditionalProperties() []*NamedAny {
	if x != nil {
		return x.AdditionalProperties
	}
	return nil
}

// Allows referencing an external resource for extended documentation.
type ExternalDocs struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Description            string      `protobuf:"bytes,1,opt,name=description,proto3" json:"description,omitempty"`
	Url                    string      `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	SpecificationExtension []*NamedAny `protobuf:"bytes,3,rep,name=specification_extension,json=specificationExtension,proto3" json:"specification_extension,omitempty"`
}

func (x *ExternalDocs) Reset() {
	*x = ExternalDocs{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExternalDocs) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExternalDocs) ProtoMessage() {}

func (x *ExternalDocs) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExternalDocs.ProtoReflect.Descriptor instead.
func (*ExternalDocs) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{17}
}

func (x *ExternalDocs) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ExternalDocs) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *ExternalDocs) GetSpecificationExtension() []*NamedAny {
	if x != nil {
		return x.SpecificationExtension
	}
	return nil
}

// The Header Object follows the structure of the Parameter Object with the following changes:  1. `name` MUST NOT be specified, it is given in the corresponding `headers` map. 1. `in` MUST NOT be specified, it is implicitly in `header`. 1. All traits that are affected by the location MUST be applicable to a location of `header` (for example, `style`).
type Header struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Description            string                `protobuf:"bytes,1,opt,name=description,proto3" json:"description,omitempty"`
	Required               bool                  `protobuf:"varint,2,opt,name=required,proto3" json:"required,omitempty"`
	Deprecated             bool                  `protobuf:"varint,3,opt,name=deprecated,proto3" json:"deprecated,omitempty"`
	AllowEmptyValue        bool                  `protobuf:"varint,4,opt,name=allow_empty_value,json=allowEmptyValue,proto3" json:"allow_empty_value,omitempty"`
	Style                  string                `protobuf:"bytes,5,opt,name=style,proto3" json:"style,omitempty"`
	Explode                bool                  `protobuf:"varint,6,opt,name=explode,proto3" json:"explode,omitempty"`
	AllowReserved          bool                  `protobuf:"varint,7,opt,name=allow_reserved,json=allowReserved,proto3" json:"allow_reserved,omitempty"`
	Schema                 *SchemaOrReference    `protobuf:"bytes,8,opt,name=schema,proto3" json:"schema,omitempty"`
	Example                *Any                  `protobuf:"bytes,9,opt,name=example,proto3" json:"example,omitempty"`
	Examples               *ExamplesOrReferences `protobuf:"bytes,10,opt,name=examples,proto3" json:"examples,omitempty"`
	Content                *MediaTypes           `protobuf:"bytes,11,opt,name=content,proto3" json:"content,omitempty"`
	SpecificationExtension []*NamedAny           `protobuf:"bytes,12,rep,name=specification_extension,json=specificationExtension,proto3" json:"specification_extension,omitempty"`
}

func (x *Header) Reset() {
	*x = Header{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Header) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Header) ProtoMessage() {}

func (x *Header) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Header.ProtoReflect.Descriptor instead.
func (*Header) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{18}
}

func (x *Header) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Header) GetRequired() bool {
	if x != nil {
		return x.Required
	}
	return false
}

func (x *Header) GetDeprecated() bool {
	if x != nil {
		return x.Deprecated
	}
	return false
}

func (x *Header) GetAllowEmptyValue() bool {
	if x != nil {
		return x.AllowEmptyValue
	}
	return false
}

func (x *Header) GetStyle() string {
	if x != nil {
		return x.Style
	}
	return ""
}

func (x *Header) GetExplode() bool {
	if x != nil {
		return x.Explode
	}
	return false
}

func (x *Header) GetAllowReserved() bool {
	if x != nil {
		return x.AllowReserved
	}
	return false
}

func (x *Header) GetSchema() *SchemaOrReference {
	if x != nil {
		return x.Schema
	}
	return nil
}

func (x *Header) GetExample() *Any {
	if x != nil {
		return x.Example
	}
	return nil
}

func (x *Header) GetExamples() *ExamplesOrReferences {
	if x != nil {
		return x.Examples
	}
	return nil
}

func (x *Header) GetContent() *MediaTypes {
	if x != nil {
		return x.Content
	}
	return nil
}

func (x *Header) GetSpecificationExtension() []*NamedAny {
	if x != nil {
		return x.SpecificationExtension
	}
	return nil
}

type HeaderOrReference struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Oneof:
	//
	//	*HeaderOrReference_Header
	//	*HeaderOrReference_Reference
	Oneof isHeaderOrReference_Oneof `protobuf_oneof:"oneof"`
}

func (x *HeaderOrReference) Reset() {
	*x = HeaderOrReference{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HeaderOrReference) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HeaderOrReference) ProtoMessage() {}

func (x *HeaderOrReference) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HeaderOrReference.ProtoReflect.Descriptor instead.
func (*HeaderOrReference) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{19}
}

func (m *HeaderOrReference) GetOneof() isHeaderOrReference_Oneof {
	if m != nil {
		return m.Oneof
	}
	return nil
}

func (x *HeaderOrReference) GetHeader() *Header {
	if x, ok := x.GetOneof().(*HeaderOrReference_Header); ok {
		return x.Header
	}
	return nil
}

func (x *HeaderOrReference) GetReference() *Reference {
	if x, ok := x.GetOneof().(*HeaderOrReference_Reference); ok {
		return x.Reference
	}
	return nil
}

type isHeaderOrReference_Oneof interface {
	isHeaderOrReference_Oneof()
}

type HeaderOrReference_Header struct {
	Header *Header `protobuf:"bytes,1,opt,name=header,proto3,oneof"`
}

type HeaderOrReference_Reference struct {
	Reference *Reference `protobuf:"bytes,2,opt,name=reference,proto3,oneof"`
}

func (*HeaderOrReference_Header) isHeaderOrReference_Oneof() {}

func (*HeaderOrReference_Reference) isHeaderOrReference_Oneof() {}

type HeadersOrReferences struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AdditionalProperties []*NamedHeaderOrReference `protobuf:"bytes,1,rep,name=additional_properties,json=additionalProperties,proto3" json:"additional_properties,omitempty"`
}

func (x *HeadersOrReferences) Reset() {
	*x = HeadersOrReferences{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HeadersOrReferences) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HeadersOrReferences) ProtoMessage() {}

func (x *HeadersOrReferences) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HeadersOrReferences.ProtoReflect.Descriptor instead.
func (*HeadersOrReferences) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{20}
}

func (x *HeadersOrReferences) GetAdditionalProperties() []*NamedHeaderOrReference {
	if x != nil {
		return x.AdditionalProperties
	}
	return nil
}

// The object provides metadata about the API. The metadata MAY be used by the clients if needed, and MAY be presented in editing or documentation generation tools for convenience.
type Info struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Title                  string      `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	Description            string      `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	TermsOfService         string      `protobuf:"bytes,3,opt,name=terms_of_service,json=termsOfService,proto3" json:"terms_of_service,omitempty"`
	Contact                *Contact    `protobuf:"bytes,4,opt,name=contact,proto3" json:"contact,omitempty"`
	License                *License    `protobuf:"bytes,5,opt,name=license,proto3" json:"license,omitempty"`
	Version                string      `protobuf:"bytes,6,opt,name=version,proto3" json:"version,omitempty"`
	SpecificationExtension []*NamedAny `protobuf:"bytes,7,rep,name=specification_extension,json=specificationExtension,proto3" json:"specification_extension,omitempty"`
	Summary                string      `protobuf:"bytes,8,opt,name=summary,proto3" json:"summary,omitempty"`
}

func (x *Info) Reset() {
	*x = Info{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Info) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Info) ProtoMessage() {}

func (x *Info) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Info.ProtoReflect.Descriptor instead.
func (*Info) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{21}
}

func (x *Info) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *Info) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Info) GetTermsOfService() string {
	if x != nil {
		return x.TermsOfService
	}
	return ""
}

func (x *Info) GetContact() *Contact {
	if x != nil {
		return x.Contact
	}
	return nil
}

func (x *Info) GetLicense() *License {
	if x != nil {
		return x.License
	}
	return nil
}

func (x *Info) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *Info) GetSpecificationExtension() []*NamedAny {
	if x != nil {
		return x.SpecificationExtension
	}
	return nil
}

func (x *Info) GetSummary() string {
	if x != nil {
		return x.Summary
	}
	return ""
}

type ItemsItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SchemaOrReference []*SchemaOrReference `protobuf:"bytes,1,rep,name=schema_or_reference,json=schemaOrReference,proto3" json:"schema_or_reference,omitempty"`
}

func (x *ItemsItem) Reset() {
	*x = ItemsItem{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ItemsItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ItemsItem) ProtoMessage() {}

func (x *ItemsItem) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ItemsItem.ProtoReflect.Descriptor instead.
func (*ItemsItem) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{22}
}

func (x *ItemsItem) GetSchemaOrReference() []*SchemaOrReference {
	if x != nil {
		return x.SchemaOrReference
	}
	return nil
}

// License information for the exposed API.
type License struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name                   string      `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Url                    string      `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	SpecificationExtension []*NamedAny `protobuf:"bytes,3,rep,name=specification_extension,json=specificationExtension,proto3" json:"specification_extension,omitempty"`
}

func (x *License) Reset() {
	*x = License{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *License) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*License) ProtoMessage() {}

func (x *License) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use License.ProtoReflect.Descriptor instead.
func (*License) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{23}
}

func (x *License) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *License) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *License) GetSpecificationExtension() []*NamedAny {
	if x != nil {
		return x.SpecificationExtension
	}
	return nil
}

// The `Link object` represents a possible design-time link for a response. The presence of a link does not guarantee the caller's ability to successfully invoke it, rather it provides a known relationship and traversal mechanism between responses and other operations.  Unlike _dynamic_ links (i.e. links provided **in** the response payload), the OAS linking mechanism does not require link information in the runtime response.  For computing links, and providing instructions to execute them, a runtime expression is used for accessing values in an operation and using them as parameters while invoking the linked operation.
type Link struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OperationRef           string           `protobuf:"bytes,1,opt,name=operation_ref,json=operationRef,proto3" json:"operation_ref,omitempty"`
	OperationId            string           `protobuf:"bytes,2,opt,name=operation_id,json=operationId,proto3" json:"operation_id,omitempty"`
	Parameters             *AnyOrExpression `protobuf:"bytes,3,opt,name=parameters,proto3" json:"parameters,omitempty"`
	RequestBody            *AnyOrExpression `protobuf:"bytes,4,opt,name=request_body,json=requestBody,proto3" json:"request_body,omitempty"`
	Description            string           `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`
	Server                 *Server          `protobuf:"bytes,6,opt,name=server,proto3" json:"server,omitempty"`
	SpecificationExtension []*NamedAny      `protobuf:"bytes,7,rep,name=specification_extension,json=specificationExtension,proto3" json:"specification_extension,omitempty"`
}

func (x *Link) Reset() {
	*x = Link{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Link) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Link) ProtoMessage() {}

func (x *Link) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Link.ProtoReflect.Descriptor instead.
func (*Link) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{24}
}

func (x *Link) GetOperationRef() string {
	if x != nil {
		return x.OperationRef
	}
	return ""
}

func (x *Link) GetOperationId() string {
	if x != nil {
		return x.OperationId
	}
	return ""
}

func (x *Link) GetParameters() *AnyOrExpression {
	if x != nil {
		return x.Parameters
	}
	return nil
}

func (x *Link) GetRequestBody() *AnyOrExpression {
	if x != nil {
		return x.RequestBody
	}
	return nil
}

func (x *Link) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Link) GetServer() *Server {
	if x != nil {
		return x.Server
	}
	return nil
}

func (x *Link) GetSpecificationExtension() []*NamedAny {
	if x != nil {
		return x.SpecificationExtension
	}
	return nil
}

type LinkOrReference struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Oneof:
	//
	//	*LinkOrReference_Link
	//	*LinkOrReference_Reference
	Oneof isLinkOrReference_Oneof `protobuf_oneof:"oneof"`
}

func (x *LinkOrReference) Reset() {
	*x = LinkOrReference{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LinkOrReference) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LinkOrReference) ProtoMessage() {}

func (x *LinkOrReference) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LinkOrReference.ProtoReflect.Descriptor instead.
func (*LinkOrReference) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{25}
}

func (m *LinkOrReference) GetOneof() isLinkOrReference_Oneof {
	if m != nil {
		return m.Oneof
	}
	return nil
}

func (x *LinkOrReference) GetLink() *Link {
	if x, ok := x.GetOneof().(*LinkOrReference_Link); ok {
		return x.Link
	}
	return nil
}

func (x *LinkOrReference) GetReference() *Reference {
	if x, ok := x.GetOneof().(*LinkOrReference_Reference); ok {
		return x.Reference
	}
	return nil
}

type isLinkOrReference_Oneof interface {
	isLinkOrReference_Oneof()
}

type LinkOrReference_Link struct {
	Link *Link `protobuf:"bytes,1,opt,name=link,proto3,oneof"`
}

type LinkOrReference_Reference struct {
	Reference *Reference `protobuf:"bytes,2,opt,name=reference,proto3,oneof"`
}

func (*LinkOrReference_Link) isLinkOrReference_Oneof() {}

func (*LinkOrReference_Reference) isLinkOrReference_Oneof() {}

type LinksOrReferences struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AdditionalProperties []*NamedLinkOrReference `protobuf:"bytes,1,rep,name=additional_properties,json=additionalProperties,proto3" json:"additional_properties,omitempty"`
}

func (x *LinksOrReferences) Reset() {
	*x = LinksOrReferences{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LinksOrReferences) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LinksOrReferences) ProtoMessage() {}

func (x *LinksOrReferences) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LinksOrReferences.ProtoReflect.Descriptor instead.
func (*LinksOrReferences) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{26}
}

func (x *LinksOrReferences) GetAdditionalProperties() []*NamedLinkOrReference {
	if x != nil {
		return x.AdditionalProperties
	}
	return nil
}

// Each Media Type Object provides schema and examples for the media type identified by its key.
type MediaType struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Schema                 *SchemaOrReference    `protobuf:"bytes,1,opt,name=schema,proto3" json:"schema,omitempty"`
	Example                *Any                  `protobuf:"bytes,2,opt,name=example,proto3" json:"example,omitempty"`
	Examples               *ExamplesOrReferences `protobuf:"bytes,3,opt,name=examples,proto3" json:"examples,omitempty"`
	Encoding               *Encodings            `protobuf:"bytes,4,opt,name=encoding,proto3" json:"encoding,omitempty"`
	SpecificationExtension []*NamedAny           `protobuf:"bytes,5,rep,name=specification_extension,json=specificationExtension,proto3" json:"specification_extension,omitempty"`
}

func (x *MediaType) Reset() {
	*x = MediaType{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MediaType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MediaType) ProtoMessage() {}

func (x *MediaType) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MediaType.ProtoReflect.Descriptor instead.
func (*MediaType) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{27}
}

func (x *MediaType) GetSchema() *SchemaOrReference {
	if x != nil {
		return x.Schema
	}
	return nil
}

func (x *MediaType) GetExample() *Any {
	if x != nil {
		return x.Example
	}
	return nil
}

func (x *MediaType) GetExamples() *ExamplesOrReferences {
	if x != nil {
		return x.Examples
	}
	return nil
}

func (x *MediaType) GetEncoding() *Encodings {
	if x != nil {
		return x.Encoding
	}
	return nil
}

func (x *MediaType) GetSpecificationExtension() []*NamedAny {
	if x != nil {
		return x.SpecificationExtension
	}
	return nil
}

type MediaTypes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AdditionalProperties []*NamedMediaType `protobuf:"bytes,1,rep,name=additional_properties,json=additionalProperties,proto3" json:"additional_properties,omitempty"`
}

func (x *MediaTypes) Reset() {
	*x = MediaTypes{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MediaTypes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MediaTypes) ProtoMessage() {}

func (x *MediaTypes) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MediaTypes.ProtoReflect.Descriptor instead.
func (*MediaTypes) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{28}
}

func (x *MediaTypes) GetAdditionalProperties() []*NamedMediaType {
	if x != nil {
		return x.AdditionalProperties
	}
	return nil
}

// Automatically-generated message used to represent maps of Any as ordered (name,value) pairs.
type NamedAny struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Map key
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Mapped value
	Value *Any `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *NamedAny) Reset() {
	*x = NamedAny{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NamedAny) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NamedAny) ProtoMessage() {}

func (x *NamedAny) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NamedAny.ProtoReflect.Descriptor instead.
func (*NamedAny) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{29}
}

func (x *NamedAny) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NamedAny) GetValue() *Any {
	if x != nil {
		return x.Value
	}
	return nil
}

// Automatically-generated message used to represent maps of CallbackOrReference as ordered (name,value) pairs.
type NamedCallbackOrReference struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Map key
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Mapped value
	Value *CallbackOrReference `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *NamedCallbackOrReference) Reset() {
	*x = NamedCallbackOrReference{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NamedCallbackOrReference) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NamedCallbackOrReference) ProtoMessage() {}

func (x *NamedCallbackOrReference) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NamedCallbackOrReference.ProtoReflect.Descriptor instead.
func (*NamedCallbackOrReference) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{30}
}

func (x *NamedCallbackOrReference) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NamedCallbackOrReference) GetValue() *CallbackOrReference {
	if x != nil {
		return x.Value
	}
	return nil
}

// Automatically-generated message used to represent maps of Encoding as ordered (name,value) pairs.
type NamedEncoding struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Map key
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Mapped value
	Value *Encoding `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *NamedEncoding) Reset() {
	*x = NamedEncoding{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NamedEncoding) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NamedEncoding) ProtoMessage() {}

func (x *NamedEncoding) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NamedEncoding.ProtoReflect.Descriptor instead.
func (*NamedEncoding) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{31}
}

func (x *NamedEncoding) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NamedEncoding) GetValue() *Encoding {
	if x != nil {
		return x.Value
	}
	return nil
}

// Automatically-generated message used to represent maps of ExampleOrReference as ordered (name,value) pairs.
type NamedExampleOrReference struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Map key
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Mapped value
	Value *ExampleOrReference `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *NamedExampleOrReference) Reset() {
	*x = NamedExampleOrReference{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NamedExampleOrReference) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NamedExampleOrReference) ProtoMessage() {}

func (x *NamedExampleOrReference) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NamedExampleOrReference.ProtoReflect.Descriptor instead.
func (*NamedExampleOrReference) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{32}
}

func (x *NamedExampleOrReference) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NamedExampleOrReference) GetValue() *ExampleOrReference {
	if x != nil {
		return x.Value
	}
	return nil
}

// Automatically-generated message used to represent maps of HeaderOrReference as ordered (name,value) pairs.
type NamedHeaderOrReference struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Map key
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Mapped value
	Value *HeaderOrReference `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *NamedHeaderOrReference) Reset() {
	*x = NamedHeaderOrReference{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NamedHeaderOrReference) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NamedHeaderOrReference) ProtoMessage() {}

func (x *NamedHeaderOrReference) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NamedHeaderOrReference.ProtoReflect.Descriptor instead.
func (*NamedHeaderOrReference) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{33}
}

func (x *NamedHeaderOrReference) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NamedHeaderOrReference) GetValue() *HeaderOrReference {
	if x != nil {
		return x.Value
	}
	return nil
}

// Automatically-generated message used to represent maps of LinkOrReference as ordered (name,value) pairs.
type NamedLinkOrReference struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Map key
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Mapped value
	Value *LinkOrReference `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *NamedLinkOrReference) Reset() {
	*x = NamedLinkOrReference{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NamedLinkOrReference) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NamedLinkOrReference) ProtoMessage() {}

func (x *NamedLinkOrReference) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NamedLinkOrReference.ProtoReflect.Descriptor instead.
func (*NamedLinkOrReference) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{34}
}

func (x *NamedLinkOrReference) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NamedLinkOrReference) GetValue() *LinkOrReference {
	if x != nil {
		return x.Value
	}
	return nil
}

// Automatically-generated message used to represent maps of MediaType as ordered (name,value) pairs.
type NamedMediaType struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Map key
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Mapped value
	Value *MediaType `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *NamedMediaType) Reset() {
	*x = NamedMediaType{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NamedMediaType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NamedMediaType) ProtoMessage() {}

func (x *NamedMediaType) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NamedMediaType.ProtoReflect.Descriptor instead.
func (*NamedMediaType) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{35}
}

func (x *NamedMediaType) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NamedMediaType) GetValue() *MediaType {
	if x != nil {
		return x.Value
	}
	return nil
}

// Automatically-generated message used to represent maps of ParameterOrReference as ordered (name,value) pairs.
type NamedParameterOrReference struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Map key
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Mapped value
	Value *ParameterOrReference `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *NamedParameterOrReference) Reset() {
	*x = NamedParameterOrReference{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NamedParameterOrReference) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NamedParameterOrReference) ProtoMessage() {}

func (x *NamedParameterOrReference) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NamedParameterOrReference.ProtoReflect.Descriptor instead.
func (*NamedParameterOrReference) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{36}
}

func (x *NamedParameterOrReference) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NamedParameterOrReference) GetValue() *ParameterOrReference {
	if x != nil {
		return x.Value
	}
	return nil
}

// Automatically-generated message used to represent maps of PathItem as ordered (name,value) pairs.
type NamedPathItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Map key
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Mapped value
	Value *PathItem `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *NamedPathItem) Reset() {
	*x = NamedPathItem{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NamedPathItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NamedPathItem) ProtoMessage() {}

func (x *NamedPathItem) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NamedPathItem.ProtoReflect.Descriptor instead.
func (*NamedPathItem) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{37}
}

func (x *NamedPathItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NamedPathItem) GetValue() *PathItem {
	if x != nil {
		return x.Value
	}
	return nil
}

// Automatically-generated message used to represent maps of RequestBodyOrReference as ordered (name,value) pairs.
type NamedRequestBodyOrReference struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Map key
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Mapped value
	Value *RequestBodyOrReference `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *NamedRequestBodyOrReference) Reset() {
	*x = NamedRequestBodyOrReference{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NamedRequestBodyOrReference) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NamedRequestBodyOrReference) ProtoMessage() {}

func (x *NamedRequestBodyOrReference) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NamedRequestBodyOrReference.ProtoReflect.Descriptor instead.
func (*NamedRequestBodyOrReference) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{38}
}

func (x *NamedRequestBodyOrReference) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NamedRequestBodyOrReference) GetValue() *RequestBodyOrReference {
	if x != nil {
		return x.Value
	}
	return nil
}

// Automatically-generated message used to represent maps of ResponseOrReference as ordered (name,value) pairs.
type NamedResponseOrReference struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Map key
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Mapped value
	Value *ResponseOrReference `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *NamedResponseOrReference) Reset() {
	*x = NamedResponseOrReference{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NamedResponseOrReference) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NamedResponseOrReference) ProtoMessage() {}

func (x *NamedResponseOrReference) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NamedResponseOrReference.ProtoReflect.Descriptor instead.
func (*NamedResponseOrReference) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{39}
}

func (x *NamedResponseOrReference) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NamedResponseOrReference) GetValue() *ResponseOrReference {
	if x != nil {
		return x.Value
	}
	return nil
}

// Automatically-generated message used to represent maps of SchemaOrReference as ordered (name,value) pairs.
type NamedSchemaOrReference struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Map key
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Mapped value
	Value *SchemaOrReference `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *NamedSchemaOrReference) Reset() {
	*x = NamedSchemaOrReference{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NamedSchemaOrReference) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NamedSchemaOrReference) ProtoMessage() {}

func (x *NamedSchemaOrReference) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NamedSchemaOrReference.ProtoReflect.Descriptor instead.
func (*NamedSchemaOrReference) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{40}
}

func (x *NamedSchemaOrReference) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NamedSchemaOrReference) GetValue() *SchemaOrReference {
	if x != nil {
		return x.Value
	}
	return nil
}

// Automatically-generated message used to represent maps of SecuritySchemeOrReference as ordered (name,value) pairs.
type NamedSecuritySchemeOrReference struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Map key
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Mapped value
	Value *SecuritySchemeOrReference `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *NamedSecuritySchemeOrReference) Reset() {
	*x = NamedSecuritySchemeOrReference{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NamedSecuritySchemeOrReference) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NamedSecuritySchemeOrReference) ProtoMessage() {}

func (x *NamedSecuritySchemeOrReference) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NamedSecuritySchemeOrReference.ProtoReflect.Descriptor instead.
func (*NamedSecuritySchemeOrReference) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{41}
}

func (x *NamedSecuritySchemeOrReference) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NamedSecuritySchemeOrReference) GetValue() *SecuritySchemeOrReference {
	if x != nil {
		return x.Value
	}
	return nil
}

// Automatically-generated message used to represent maps of ServerVariable as ordered (name,value) pairs.
type NamedServerVariable struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Map key
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Mapped value
	Value *ServerVariable `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *NamedServerVariable) Reset() {
	*x = NamedServerVariable{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[42]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NamedServerVariable) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NamedServerVariable) ProtoMessage() {}

func (x *NamedServerVariable) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[42]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NamedServerVariable.ProtoReflect.Descriptor instead.
func (*NamedServerVariable) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{42}
}

func (x *NamedServerVariable) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NamedServerVariable) GetValue() *ServerVariable {
	if x != nil {
		return x.Value
	}
	return nil
}

// Automatically-generated message used to represent maps of string as ordered (name,value) pairs.
type NamedString struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Map key
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Mapped value
	Value string `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *NamedString) Reset() {
	*x = NamedString{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[43]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NamedString) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NamedString) ProtoMessage() {}

func (x *NamedString) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[43]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NamedString.ProtoReflect.Descriptor instead.
func (*NamedString) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{43}
}

func (x *NamedString) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NamedString) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

// Automatically-generated message used to represent maps of StringArray as ordered (name,value) pairs.
type NamedStringArray struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Map key
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Mapped value
	Value *StringArray `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *NamedStringArray) Reset() {
	*x = NamedStringArray{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[44]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NamedStringArray) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NamedStringArray) ProtoMessage() {}

func (x *NamedStringArray) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[44]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NamedStringArray.ProtoReflect.Descriptor instead.
func (*NamedStringArray) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{44}
}

func (x *NamedStringArray) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NamedStringArray) GetValue() *StringArray {
	if x != nil {
		return x.Value
	}
	return nil
}

// Configuration details for a supported OAuth Flow
type OauthFlow struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AuthorizationUrl       string      `protobuf:"bytes,1,opt,name=authorization_url,json=authorizationUrl,proto3" json:"authorization_url,omitempty"`
	TokenUrl               string      `protobuf:"bytes,2,opt,name=token_url,json=tokenUrl,proto3" json:"token_url,omitempty"`
	RefreshUrl             string      `protobuf:"bytes,3,opt,name=refresh_url,json=refreshUrl,proto3" json:"refresh_url,omitempty"`
	Scopes                 *Strings    `protobuf:"bytes,4,opt,name=scopes,proto3" json:"scopes,omitempty"`
	SpecificationExtension []*NamedAny `protobuf:"bytes,5,rep,name=specification_extension,json=specificationExtension,proto3" json:"specification_extension,omitempty"`
}

func (x *OauthFlow) Reset() {
	*x = OauthFlow{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[45]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OauthFlow) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OauthFlow) ProtoMessage() {}

func (x *OauthFlow) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[45]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OauthFlow.ProtoReflect.Descriptor instead.
func (*OauthFlow) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{45}
}

func (x *OauthFlow) GetAuthorizationUrl() string {
	if x != nil {
		return x.AuthorizationUrl
	}
	return ""
}

func (x *OauthFlow) GetTokenUrl() string {
	if x != nil {
		return x.TokenUrl
	}
	return ""
}

func (x *OauthFlow) GetRefreshUrl() string {
	if x != nil {
		return x.RefreshUrl
	}
	return ""
}

func (x *OauthFlow) GetScopes() *Strings {
	if x != nil {
		return x.Scopes
	}
	return nil
}

func (x *OauthFlow) GetSpecificationExtension() []*NamedAny {
	if x != nil {
		return x.SpecificationExtension
	}
	return nil
}

// Allows configuration of the supported OAuth Flows.
type OauthFlows struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Implicit               *OauthFlow  `protobuf:"bytes,1,opt,name=implicit,proto3" json:"implicit,omitempty"`
	Password               *OauthFlow  `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"`
	ClientCredentials      *OauthFlow  `protobuf:"bytes,3,opt,name=client_credentials,json=clientCredentials,proto3" json:"client_credentials,omitempty"`
	AuthorizationCode      *OauthFlow  `protobuf:"bytes,4,opt,name=authorization_code,json=authorizationCode,proto3" json:"authorization_code,omitempty"`
	SpecificationExtension []*NamedAny `protobuf:"bytes,5,rep,name=specification_extension,json=specificationExtension,proto3" json:"specification_extension,omitempty"`
}

func (x *OauthFlows) Reset() {
	*x = OauthFlows{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[46]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OauthFlows) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OauthFlows) ProtoMessage() {}

func (x *OauthFlows) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[46]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OauthFlows.ProtoReflect.Descriptor instead.
func (*OauthFlows) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{46}
}

func (x *OauthFlows) GetImplicit() *OauthFlow {
	if x != nil {
		return x.Implicit
	}
	return nil
}

func (x *OauthFlows) GetPassword() *OauthFlow {
	if x != nil {
		return x.Password
	}
	return nil
}

func (x *OauthFlows) GetClientCredentials() *OauthFlow {
	if x != nil {
		return x.ClientCredentials
	}
	return nil
}

func (x *OauthFlows) GetAuthorizationCode() *OauthFlow {
	if x != nil {
		return x.AuthorizationCode
	}
	return nil
}

func (x *OauthFlows) GetSpecificationExtension() []*NamedAny {
	if x != nil {
		return x.SpecificationExtension
	}
	return nil
}

type Object struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AdditionalProperties []*NamedAny `protobuf:"bytes,1,rep,name=additional_properties,json=additionalProperties,proto3" json:"additional_properties,omitempty"`
}

func (x *Object) Reset() {
	*x = Object{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[47]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Object) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Object) ProtoMessage() {}

func (x *Object) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[47]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Object.ProtoReflect.Descriptor instead.
func (*Object) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{47}
}

func (x *Object) GetAdditionalProperties() []*NamedAny {
	if x != nil {
		return x.AdditionalProperties
	}
	return nil
}

// Describes a single API operation on a path.
type Operation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Tags                   []string                `protobuf:"bytes,1,rep,name=tags,proto3" json:"tags,omitempty"`
	Summary                string                  `protobuf:"bytes,2,opt,name=summary,proto3" json:"summary,omitempty"`
	Description            string                  `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	ExternalDocs           *ExternalDocs           `protobuf:"bytes,4,opt,name=external_docs,json=externalDocs,proto3" json:"external_docs,omitempty"`
	OperationId            string                  `protobuf:"bytes,5,opt,name=operation_id,json=operationId,proto3" json:"operation_id,omitempty"`
	Parameters             []*ParameterOrReference `protobuf:"bytes,6,rep,name=parameters,proto3" json:"parameters,omitempty"`
	RequestBody            *RequestBodyOrReference `protobuf:"bytes,7,opt,name=request_body,json=requestBody,proto3" json:"request_body,omitempty"`
	Responses              *Responses              `protobuf:"bytes,8,opt,name=responses,proto3" json:"responses,omitempty"`
	Callbacks              *CallbacksOrReferences  `protobuf:"bytes,9,opt,name=callbacks,proto3" json:"callbacks,omitempty"`
	Deprecated             bool                    `protobuf:"varint,10,opt,name=deprecated,proto3" json:"deprecated,omitempty"`
	Security               []*SecurityRequirement  `protobuf:"bytes,11,rep,name=security,proto3" json:"security,omitempty"`
	Servers                []*Server               `protobuf:"bytes,12,rep,name=servers,proto3" json:"servers,omitempty"`
	SpecificationExtension []*NamedAny             `protobuf:"bytes,13,rep,name=specification_extension,json=specificationExtension,proto3" json:"specification_extension,omitempty"`
}

func (x *Operation) Reset() {
	*x = Operation{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[48]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Operation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Operation) ProtoMessage() {}

func (x *Operation) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[48]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Operation.ProtoReflect.Descriptor instead.
func (*Operation) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{48}
}

func (x *Operation) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *Operation) GetSummary() string {
	if x != nil {
		return x.Summary
	}
	return ""
}

func (x *Operation) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Operation) GetExternalDocs() *ExternalDocs {
	if x != nil {
		return x.ExternalDocs
	}
	return nil
}

func (x *Operation) GetOperationId() string {
	if x != nil {
		return x.OperationId
	}
	return ""
}

func (x *Operation) GetParameters() []*ParameterOrReference {
	if x != nil {
		return x.Parameters
	}
	return nil
}

func (x *Operation) GetRequestBody() *RequestBodyOrReference {
	if x != nil {
		return x.RequestBody
	}
	return nil
}

func (x *Operation) GetResponses() *Responses {
	if x != nil {
		return x.Responses
	}
	return nil
}

func (x *Operation) GetCallbacks() *CallbacksOrReferences {
	if x != nil {
		return x.Callbacks
	}
	return nil
}

func (x *Operation) GetDeprecated() bool {
	if x != nil {
		return x.Deprecated
	}
	return false
}

func (x *Operation) GetSecurity() []*SecurityRequirement {
	if x != nil {
		return x.Security
	}
	return nil
}

func (x *Operation) GetServers() []*Server {
	if x != nil {
		return x.Servers
	}
	return nil
}

func (x *Operation) GetSpecificationExtension() []*NamedAny {
	if x != nil {
		return x.SpecificationExtension
	}
	return nil
}

// Describes a single operation parameter.  A unique parameter is defined by a combination of a name and location.
type Parameter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name                   string                `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	In                     string                `protobuf:"bytes,2,opt,name=in,proto3" json:"in,omitempty"`
	Description            string                `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	Required               bool                  `protobuf:"varint,4,opt,name=required,proto3" json:"required,omitempty"`
	Deprecated             bool                  `protobuf:"varint,5,opt,name=deprecated,proto3" json:"deprecated,omitempty"`
	AllowEmptyValue        bool                  `protobuf:"varint,6,opt,name=allow_empty_value,json=allowEmptyValue,proto3" json:"allow_empty_value,omitempty"`
	Style                  string                `protobuf:"bytes,7,opt,name=style,proto3" json:"style,omitempty"`
	Explode                bool                  `protobuf:"varint,8,opt,name=explode,proto3" json:"explode,omitempty"`
	AllowReserved          bool                  `protobuf:"varint,9,opt,name=allow_reserved,json=allowReserved,proto3" json:"allow_reserved,omitempty"`
	Schema                 *SchemaOrReference    `protobuf:"bytes,10,opt,name=schema,proto3" json:"schema,omitempty"`
	Example                *Any                  `protobuf:"bytes,11,opt,name=example,proto3" json:"example,omitempty"`
	Examples               *ExamplesOrReferences `protobuf:"bytes,12,opt,name=examples,proto3" json:"examples,omitempty"`
	Content                *MediaTypes           `protobuf:"bytes,13,opt,name=content,proto3" json:"content,omitempty"`
	SpecificationExtension []*NamedAny           `protobuf:"bytes,14,rep,name=specification_extension,json=specificationExtension,proto3" json:"specification_extension,omitempty"`
}

func (x *Parameter) Reset() {
	*x = Parameter{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[49]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Parameter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Parameter) ProtoMessage() {}

func (x *Parameter) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[49]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Parameter.ProtoReflect.Descriptor instead.
func (*Parameter) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{49}
}

func (x *Parameter) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Parameter) GetIn() string {
	if x != nil {
		return x.In
	}
	return ""
}

func (x *Parameter) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Parameter) GetRequired() bool {
	if x != nil {
		return x.Required
	}
	return false
}

func (x *Parameter) GetDeprecated() bool {
	if x != nil {
		return x.Deprecated
	}
	return false
}

func (x *Parameter) GetAllowEmptyValue() bool {
	if x != nil {
		return x.AllowEmptyValue
	}
	return false
}

func (x *Parameter) GetStyle() string {
	if x != nil {
		return x.Style
	}
	return ""
}

func (x *Parameter) GetExplode() bool {
	if x != nil {
		return x.Explode
	}
	return false
}

func (x *Parameter) GetAllowReserved() bool {
	if x != nil {
		return x.AllowReserved
	}
	return false
}

func (x *Parameter) GetSchema() *SchemaOrReference {
	if x != nil {
		return x.Schema
	}
	return nil
}

func (x *Parameter) GetExample() *Any {
	if x != nil {
		return x.Example
	}
	return nil
}

func (x *Parameter) GetExamples() *ExamplesOrReferences {
	if x != nil {
		return x.Examples
	}
	return nil
}

func (x *Parameter) GetContent() *MediaTypes {
	if x != nil {
		return x.Content
	}
	return nil
}

func (x *Parameter) GetSpecificationExtension() []*NamedAny {
	if x != nil {
		return x.SpecificationExtension
	}
	return nil
}

type ParameterOrReference struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Oneof:
	//
	//	*ParameterOrReference_Parameter
	//	*ParameterOrReference_Reference
	Oneof isParameterOrReference_Oneof `protobuf_oneof:"oneof"`
}

func (x *ParameterOrReference) Reset() {
	*x = ParameterOrReference{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[50]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ParameterOrReference) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ParameterOrReference) ProtoMessage() {}

func (x *ParameterOrReference) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[50]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ParameterOrReference.ProtoReflect.Descriptor instead.
func (*ParameterOrReference) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{50}
}

func (m *ParameterOrReference) GetOneof() isParameterOrReference_Oneof {
	if m != nil {
		return m.Oneof
	}
	return nil
}

func (x *ParameterOrReference) GetParameter() *Parameter {
	if x, ok := x.GetOneof().(*ParameterOrReference_Parameter); ok {
		return x.Parameter
	}
	return nil
}

func (x *ParameterOrReference) GetReference() *Reference {
	if x, ok := x.GetOneof().(*ParameterOrReference_Reference); ok {
		return x.Reference
	}
	return nil
}

type isParameterOrReference_Oneof interface {
	isParameterOrReference_Oneof()
}

type ParameterOrReference_Parameter struct {
	Parameter *Parameter `protobuf:"bytes,1,opt,name=parameter,proto3,oneof"`
}

type ParameterOrReference_Reference struct {
	Reference *Reference `protobuf:"bytes,2,opt,name=reference,proto3,oneof"`
}

func (*ParameterOrReference_Parameter) isParameterOrReference_Oneof() {}

func (*ParameterOrReference_Reference) isParameterOrReference_Oneof() {}

type ParametersOrReferences struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AdditionalProperties []*NamedParameterOrReference `protobuf:"bytes,1,rep,name=additional_properties,json=additionalProperties,proto3" json:"additional_properties,omitempty"`
}

func (x *ParametersOrReferences) Reset() {
	*x = ParametersOrReferences{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[51]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ParametersOrReferences) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ParametersOrReferences) ProtoMessage() {}

func (x *ParametersOrReferences) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[51]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ParametersOrReferences.ProtoReflect.Descriptor instead.
func (*ParametersOrReferences) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{51}
}

func (x *ParametersOrReferences) GetAdditionalProperties() []*NamedParameterOrReference {
	if x != nil {
		return x.AdditionalProperties
	}
	return nil
}

// Describes the operations available on a single path. A Path Item MAY be empty, due to ACL constraints. The path itself is still exposed to the documentation viewer but they will not know which operations and parameters are available.
type PathItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	XRef                   string                  `protobuf:"bytes,1,opt,name=_ref,json=Ref,proto3" json:"_ref,omitempty"`
	Summary                string                  `protobuf:"bytes,2,opt,name=summary,proto3" json:"summary,omitempty"`
	Description            string                  `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	Get                    *Operation              `protobuf:"bytes,4,opt,name=get,proto3" json:"get,omitempty"`
	Put                    *Operation              `protobuf:"bytes,5,opt,name=put,proto3" json:"put,omitempty"`
	Post                   *Operation              `protobuf:"bytes,6,opt,name=post,proto3" json:"post,omitempty"`
	Delete                 *Operation              `protobuf:"bytes,7,opt,name=delete,proto3" json:"delete,omitempty"`
	Options                *Operation              `protobuf:"bytes,8,opt,name=options,proto3" json:"options,omitempty"`
	Head                   *Operation              `protobuf:"bytes,9,opt,name=head,proto3" json:"head,omitempty"`
	Patch                  *Operation              `protobuf:"bytes,10,opt,name=patch,proto3" json:"patch,omitempty"`
	Trace                  *Operation              `protobuf:"bytes,11,opt,name=trace,proto3" json:"trace,omitempty"`
	Servers                []*Server               `protobuf:"bytes,12,rep,name=servers,proto3" json:"servers,omitempty"`
	Parameters             []*ParameterOrReference `protobuf:"bytes,13,rep,name=parameters,proto3" json:"parameters,omitempty"`
	SpecificationExtension []*NamedAny             `protobuf:"bytes,14,rep,name=specification_extension,json=specificationExtension,proto3" json:"specification_extension,omitempty"`
}

func (x *PathItem) Reset() {
	*x = PathItem{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[52]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PathItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PathItem) ProtoMessage() {}

func (x *PathItem) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[52]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PathItem.ProtoReflect.Descriptor instead.
func (*PathItem) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{52}
}

func (x *PathItem) GetXRef() string {
	if x != nil {
		return x.XRef
	}
	return ""
}

func (x *PathItem) GetSummary() string {
	if x != nil {
		return x.Summary
	}
	return ""
}

func (x *PathItem) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *PathItem) GetGet() *Operation {
	if x != nil {
		return x.Get
	}
	return nil
}

func (x *PathItem) GetPut() *Operation {
	if x != nil {
		return x.Put
	}
	return nil
}

func (x *PathItem) GetPost() *Operation {
	if x != nil {
		return x.Post
	}
	return nil
}

func (x *PathItem) GetDelete() *Operation {
	if x != nil {
		return x.Delete
	}
	return nil
}

func (x *PathItem) GetOptions() *Operation {
	if x != nil {
		return x.Options
	}
	return nil
}

func (x *PathItem) GetHead() *Operation {
	if x != nil {
		return x.Head
	}
	return nil
}

func (x *PathItem) GetPatch() *Operation {
	if x != nil {
		return x.Patch
	}
	return nil
}

func (x *PathItem) GetTrace() *Operation {
	if x != nil {
		return x.Trace
	}
	return nil
}

func (x *PathItem) GetServers() []*Server {
	if x != nil {
		return x.Servers
	}
	return nil
}

func (x *PathItem) GetParameters() []*ParameterOrReference {
	if x != nil {
		return x.Parameters
	}
	return nil
}

func (x *PathItem) GetSpecificationExtension() []*NamedAny {
	if x != nil {
		return x.SpecificationExtension
	}
	return nil
}

// Holds the relative paths to the individual endpoints and their operations. The path is appended to the URL from the `Server Object` in order to construct the full URL.  The Paths MAY be empty, due to ACL constraints.
type Paths struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Path                   []*NamedPathItem `protobuf:"bytes,1,rep,name=path,proto3" json:"path,omitempty"`
	SpecificationExtension []*NamedAny      `protobuf:"bytes,2,rep,name=specification_extension,json=specificationExtension,proto3" json:"specification_extension,omitempty"`
}

func (x *Paths) Reset() {
	*x = Paths{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[53]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Paths) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Paths) ProtoMessage() {}

func (x *Paths) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[53]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Paths.ProtoReflect.Descriptor instead.
func (*Paths) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{53}
}

func (x *Paths) GetPath() []*NamedPathItem {
	if x != nil {
		return x.Path
	}
	return nil
}

func (x *Paths) GetSpecificationExtension() []*NamedAny {
	if x != nil {
		return x.SpecificationExtension
	}
	return nil
}

type Properties struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AdditionalProperties []*NamedSchemaOrReference `protobuf:"bytes,1,rep,name=additional_properties,json=additionalProperties,proto3" json:"additional_properties,omitempty"`
}

func (x *Properties) Reset() {
	*x = Properties{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[54]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Properties) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Properties) ProtoMessage() {}

func (x *Properties) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[54]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Properties.ProtoReflect.Descriptor instead.
func (*Properties) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{54}
}

func (x *Properties) GetAdditionalProperties() []*NamedSchemaOrReference {
	if x != nil {
		return x.AdditionalProperties
	}
	return nil
}

// A simple object to allow referencing other components in the specification, internally and externally.  The Reference Object is defined by JSON Reference and follows the same structure, behavior and rules.   For this specification, reference resolution is accomplished as defined by the JSON Reference specification and not by the JSON Schema specification.
type Reference struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	XRef        string `protobuf:"bytes,1,opt,name=_ref,json=Ref,proto3" json:"_ref,omitempty"`
	Summary     string `protobuf:"bytes,2,opt,name=summary,proto3" json:"summary,omitempty"`
	Description string `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
}

func (x *Reference) Reset() {
	*x = Reference{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[55]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Reference) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Reference) ProtoMessage() {}

func (x *Reference) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[55]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Reference.ProtoReflect.Descriptor instead.
func (*Reference) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{55}
}

func (x *Reference) GetXRef() string {
	if x != nil {
		return x.XRef
	}
	return ""
}

func (x *Reference) GetSummary() string {
	if x != nil {
		return x.Summary
	}
	return ""
}

func (x *Reference) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

type RequestBodiesOrReferences struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AdditionalProperties []*NamedRequestBodyOrReference `protobuf:"bytes,1,rep,name=additional_properties,json=additionalProperties,proto3" json:"additional_properties,omitempty"`
}

func (x *RequestBodiesOrReferences) Reset() {
	*x = RequestBodiesOrReferences{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[56]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RequestBodiesOrReferences) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RequestBodiesOrReferences) ProtoMessage() {}

func (x *RequestBodiesOrReferences) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[56]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RequestBodiesOrReferences.ProtoReflect.Descriptor instead.
func (*RequestBodiesOrReferences) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{56}
}

func (x *RequestBodiesOrReferences) GetAdditionalProperties() []*NamedRequestBodyOrReference {
	if x != nil {
		return x.AdditionalProperties
	}
	return nil
}

// Describes a single request body.
type RequestBody struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Description            string      `protobuf:"bytes,1,opt,name=description,proto3" json:"description,omitempty"`
	Content                *MediaTypes `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	Required               bool        `protobuf:"varint,3,opt,name=required,proto3" json:"required,omitempty"`
	SpecificationExtension []*NamedAny `protobuf:"bytes,4,rep,name=specification_extension,json=specificationExtension,proto3" json:"specification_extension,omitempty"`
}

func (x *RequestBody) Reset() {
	*x = RequestBody{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[57]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RequestBody) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RequestBody) ProtoMessage() {}

func (x *RequestBody) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[57]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RequestBody.ProtoReflect.Descriptor instead.
func (*RequestBody) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{57}
}

func (x *RequestBody) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *RequestBody) GetContent() *MediaTypes {
	if x != nil {
		return x.Content
	}
	return nil
}

func (x *RequestBody) GetRequired() bool {
	if x != nil {
		return x.Required
	}
	return false
}

func (x *RequestBody) GetSpecificationExtension() []*NamedAny {
	if x != nil {
		return x.SpecificationExtension
	}
	return nil
}

type RequestBodyOrReference struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Oneof:
	//
	//	*RequestBodyOrReference_RequestBody
	//	*RequestBodyOrReference_Reference
	Oneof isRequestBodyOrReference_Oneof `protobuf_oneof:"oneof"`
}

func (x *RequestBodyOrReference) Reset() {
	*x = RequestBodyOrReference{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[58]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RequestBodyOrReference) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RequestBodyOrReference) ProtoMessage() {}

func (x *RequestBodyOrReference) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[58]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RequestBodyOrReference.ProtoReflect.Descriptor instead.
func (*RequestBodyOrReference) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{58}
}

func (m *RequestBodyOrReference) GetOneof() isRequestBodyOrReference_Oneof {
	if m != nil {
		return m.Oneof
	}
	return nil
}

func (x *RequestBodyOrReference) GetRequestBody() *RequestBody {
	if x, ok := x.GetOneof().(*RequestBodyOrReference_RequestBody); ok {
		return x.RequestBody
	}
	return nil
}

func (x *RequestBodyOrReference) GetReference() *Reference {
	if x, ok := x.GetOneof().(*RequestBodyOrReference_Reference); ok {
		return x.Reference
	}
	return nil
}

type isRequestBodyOrReference_Oneof interface {
	isRequestBodyOrReference_Oneof()
}

type RequestBodyOrReference_RequestBody struct {
	RequestBody *RequestBody `protobuf:"bytes,1,opt,name=request_body,json=requestBody,proto3,oneof"`
}

type RequestBodyOrReference_Reference struct {
	Reference *Reference `protobuf:"bytes,2,opt,name=reference,proto3,oneof"`
}

func (*RequestBodyOrReference_RequestBody) isRequestBodyOrReference_Oneof() {}

func (*RequestBodyOrReference_Reference) isRequestBodyOrReference_Oneof() {}

// Describes a single response from an API Operation, including design-time, static  `links` to operations based on the response.
type Response struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Description            string               `protobuf:"bytes,1,opt,name=description,proto3" json:"description,omitempty"`
	Headers                *HeadersOrReferences `protobuf:"bytes,2,opt,name=headers,proto3" json:"headers,omitempty"`
	Content                *MediaTypes          `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"`
	Links                  *LinksOrReferences   `protobuf:"bytes,4,opt,name=links,proto3" json:"links,omitempty"`
	SpecificationExtension []*NamedAny          `protobuf:"bytes,5,rep,name=specification_extension,json=specificationExtension,proto3" json:"specification_extension,omitempty"`
}

func (x *Response) Reset() {
	*x = Response{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[59]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Response) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Response) ProtoMessage() {}

func (x *Response) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[59]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Response.ProtoReflect.Descriptor instead.
func (*Response) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{59}
}

func (x *Response) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Response) GetHeaders() *HeadersOrReferences {
	if x != nil {
		return x.Headers
	}
	return nil
}

func (x *Response) GetContent() *MediaTypes {
	if x != nil {
		return x.Content
	}
	return nil
}

func (x *Response) GetLinks() *LinksOrReferences {
	if x != nil {
		return x.Links
	}
	return nil
}

func (x *Response) GetSpecificationExtension() []*NamedAny {
	if x != nil {
		return x.SpecificationExtension
	}
	return nil
}

type ResponseOrReference struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Oneof:
	//
	//	*ResponseOrReference_Response
	//	*ResponseOrReference_Reference
	Oneof isResponseOrReference_Oneof `protobuf_oneof:"oneof"`
}

func (x *ResponseOrReference) Reset() {
	*x = ResponseOrReference{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[60]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResponseOrReference) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResponseOrReference) ProtoMessage() {}

func (x *ResponseOrReference) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[60]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResponseOrReference.ProtoReflect.Descriptor instead.
func (*ResponseOrReference) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{60}
}

func (m *ResponseOrReference) GetOneof() isResponseOrReference_Oneof {
	if m != nil {
		return m.Oneof
	}
	return nil
}

func (x *ResponseOrReference) GetResponse() *Response {
	if x, ok := x.GetOneof().(*ResponseOrReference_Response); ok {
		return x.Response
	}
	return nil
}

func (x *ResponseOrReference) GetReference() *Reference {
	if x, ok := x.GetOneof().(*ResponseOrReference_Reference); ok {
		return x.Reference
	}
	return nil
}

type isResponseOrReference_Oneof interface {
	isResponseOrReference_Oneof()
}

type ResponseOrReference_Response struct {
	Response *Response `protobuf:"bytes,1,opt,name=response,proto3,oneof"`
}

type ResponseOrReference_Reference struct {
	Reference *Reference `protobuf:"bytes,2,opt,name=reference,proto3,oneof"`
}

func (*ResponseOrReference_Response) isResponseOrReference_Oneof() {}

func (*ResponseOrReference_Reference) isResponseOrReference_Oneof() {}

// A container for the expected responses of an operation. The container maps a HTTP response code to the expected response.  The documentation is not necessarily expected to cover all possible HTTP response codes because they may not be known in advance. However, documentation is expected to cover a successful operation response and any known errors.  The `default` MAY be used as a default response object for all HTTP codes  that are not covered individually by the specification.  The `Responses Object` MUST contain at least one response code, and it  SHOULD be the response for a successful operation call.
type Responses struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Default                *ResponseOrReference        `protobuf:"bytes,1,opt,name=default,proto3" json:"default,omitempty"`
	ResponseOrReference    []*NamedResponseOrReference `protobuf:"bytes,2,rep,name=response_or_reference,json=responseOrReference,proto3" json:"response_or_reference,omitempty"`
	SpecificationExtension []*NamedAny                 `protobuf:"bytes,3,rep,name=specification_extension,json=specificationExtension,proto3" json:"specification_extension,omitempty"`
}

func (x *Responses) Reset() {
	*x = Responses{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[61]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Responses) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Responses) ProtoMessage() {}

func (x *Responses) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[61]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Responses.ProtoReflect.Descriptor instead.
func (*Responses) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{61}
}

func (x *Responses) GetDefault() *ResponseOrReference {
	if x != nil {
		return x.Default
	}
	return nil
}

func (x *Responses) GetResponseOrReference() []*NamedResponseOrReference {
	if x != nil {
		return x.ResponseOrReference
	}
	return nil
}

func (x *Responses) GetSpecificationExtension() []*NamedAny {
	if x != nil {
		return x.SpecificationExtension
	}
	return nil
}

type ResponsesOrReferences struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AdditionalProperties []*NamedResponseOrReference `protobuf:"bytes,1,rep,name=additional_properties,json=additionalProperties,proto3" json:"additional_properties,omitempty"`
}

func (x *ResponsesOrReferences) Reset() {
	*x = ResponsesOrReferences{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[62]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResponsesOrReferences) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResponsesOrReferences) ProtoMessage() {}

func (x *ResponsesOrReferences) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[62]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResponsesOrReferences.ProtoReflect.Descriptor instead.
func (*ResponsesOrReferences) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{62}
}

func (x *ResponsesOrReferences) GetAdditionalProperties() []*NamedResponseOrReference {
	if x != nil {
		return x.AdditionalProperties
	}
	return nil
}

// The Schema Object allows the definition of input and output data types. These types can be objects, but also primitives and arrays. This object is an extended subset of the JSON Schema Specification Wright Draft 00.  For more information about the properties, see JSON Schema Core and JSON Schema Validation. Unless stated otherwise, the property definitions follow the JSON Schema.
type Schema struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Nullable               bool                      `protobuf:"varint,1,opt,name=nullable,proto3" json:"nullable,omitempty"`
	Discriminator          *Discriminator            `protobuf:"bytes,2,opt,name=discriminator,proto3" json:"discriminator,omitempty"`
	ReadOnly               bool                      `protobuf:"varint,3,opt,name=read_only,json=readOnly,proto3" json:"read_only,omitempty"`
	WriteOnly              bool                      `protobuf:"varint,4,opt,name=write_only,json=writeOnly,proto3" json:"write_only,omitempty"`
	Xml                    *Xml                      `protobuf:"bytes,5,opt,name=xml,proto3" json:"xml,omitempty"`
	ExternalDocs           *ExternalDocs             `protobuf:"bytes,6,opt,name=external_docs,json=externalDocs,proto3" json:"external_docs,omitempty"`
	Example                *Any                      `protobuf:"bytes,7,opt,name=example,proto3" json:"example,omitempty"`
	Deprecated             bool                      `protobuf:"varint,8,opt,name=deprecated,proto3" json:"deprecated,omitempty"`
	Title                  string                    `protobuf:"bytes,9,opt,name=title,proto3" json:"title,omitempty"`
	MultipleOf             float64                   `protobuf:"fixed64,10,opt,name=multiple_of,json=multipleOf,proto3" json:"multiple_of,omitempty"`
	Maximum                float64                   `protobuf:"fixed64,11,opt,name=maximum,proto3" json:"maximum,omitempty"`
	ExclusiveMaximum       bool                      `protobuf:"varint,12,opt,name=exclusive_maximum,json=exclusiveMaximum,proto3" json:"exclusive_maximum,omitempty"`
	Minimum                float64                   `protobuf:"fixed64,13,opt,name=minimum,proto3" json:"minimum,omitempty"`
	ExclusiveMinimum       bool                      `protobuf:"varint,14,opt,name=exclusive_minimum,json=exclusiveMinimum,proto3" json:"exclusive_minimum,omitempty"`
	MaxLength              int64                     `protobuf:"varint,15,opt,name=max_length,json=maxLength,proto3" json:"max_length,omitempty"`
	MinLength              int64                     `protobuf:"varint,16,opt,name=min_length,json=minLength,proto3" json:"min_length,omitempty"`
	Pattern                string                    `protobuf:"bytes,17,opt,name=pattern,proto3" json:"pattern,omitempty"`
	MaxItems               int64                     `protobuf:"varint,18,opt,name=max_items,json=maxItems,proto3" json:"max_items,omitempty"`
	MinItems               int64                     `protobuf:"varint,19,opt,name=min_items,json=minItems,proto3" json:"min_items,omitempty"`
	UniqueItems            bool                      `protobuf:"varint,20,opt,name=unique_items,json=uniqueItems,proto3" json:"unique_items,omitempty"`
	MaxProperties          int64                     `protobuf:"varint,21,opt,name=max_properties,json=maxProperties,proto3" json:"max_properties,omitempty"`
	MinProperties          int64                     `protobuf:"varint,22,opt,name=min_properties,json=minProperties,proto3" json:"min_properties,omitempty"`
	Required               []string                  `protobuf:"bytes,23,rep,name=required,proto3" json:"required,omitempty"`
	Enum                   []*Any                    `protobuf:"bytes,24,rep,name=enum,proto3" json:"enum,omitempty"`
	Type                   string                    `protobuf:"bytes,25,opt,name=type,proto3" json:"type,omitempty"`
	AllOf                  []*SchemaOrReference      `protobuf:"bytes,26,rep,name=all_of,json=allOf,proto3" json:"all_of,omitempty"`
	OneOf                  []*SchemaOrReference      `protobuf:"bytes,27,rep,name=one_of,json=oneOf,proto3" json:"one_of,omitempty"`
	AnyOf                  []*SchemaOrReference      `protobuf:"bytes,28,rep,name=any_of,json=anyOf,proto3" json:"any_of,omitempty"`
	Not                    *Schema                   `protobuf:"bytes,29,opt,name=not,proto3" json:"not,omitempty"`
	Items                  *ItemsItem                `protobuf:"bytes,30,opt,name=items,proto3" json:"items,omitempty"`
	Properties             *Properties               `protobuf:"bytes,31,opt,name=properties,proto3" json:"properties,omitempty"`
	AdditionalProperties   *AdditionalPropertiesItem `protobuf:"bytes,32,opt,name=additional_properties,json=additionalProperties,proto3" json:"additional_properties,omitempty"`
	Default                *DefaultType              `protobuf:"bytes,33,opt,name=default,proto3" json:"default,omitempty"`
	Description            string                    `protobuf:"bytes,34,opt,name=description,proto3" json:"description,omitempty"`
	Format                 string                    `protobuf:"bytes,35,opt,name=format,proto3" json:"format,omitempty"`
	SpecificationExtension []*NamedAny               `protobuf:"bytes,36,rep,name=specification_extension,json=specificationExtension,proto3" json:"specification_extension,omitempty"`
}

func (x *Schema) Reset() {
	*x = Schema{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[63]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Schema) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Schema) ProtoMessage() {}

func (x *Schema) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[63]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Schema.ProtoReflect.Descriptor instead.
func (*Schema) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{63}
}

func (x *Schema) GetNullable() bool {
	if x != nil {
		return x.Nullable
	}
	return false
}

func (x *Schema) GetDiscriminator() *Discriminator {
	if x != nil {
		return x.Discriminator
	}
	return nil
}

func (x *Schema) GetReadOnly() bool {
	if x != nil {
		return x.ReadOnly
	}
	return false
}

func (x *Schema) GetWriteOnly() bool {
	if x != nil {
		return x.WriteOnly
	}
	return false
}

func (x *Schema) GetXml() *Xml {
	if x != nil {
		return x.Xml
	}
	return nil
}

func (x *Schema) GetExternalDocs() *ExternalDocs {
	if x != nil {
		return x.ExternalDocs
	}
	return nil
}

func (x *Schema) GetExample() *Any {
	if x != nil {
		return x.Example
	}
	return nil
}

func (x *Schema) GetDeprecated() bool {
	if x != nil {
		return x.Deprecated
	}
	return false
}

func (x *Schema) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *Schema) GetMultipleOf() float64 {
	if x != nil {
		return x.MultipleOf
	}
	return 0
}

func (x *Schema) GetMaximum() float64 {
	if x != nil {
		return x.Maximum
	}
	return 0
}

func (x *Schema) GetExclusiveMaximum() bool {
	if x != nil {
		return x.ExclusiveMaximum
	}
	return false
}

func (x *Schema) GetMinimum() float64 {
	if x != nil {
		return x.Minimum
	}
	return 0
}

func (x *Schema) GetExclusiveMinimum() bool {
	if x != nil {
		return x.ExclusiveMinimum
	}
	return false
}

func (x *Schema) GetMaxLength() int64 {
	if x != nil {
		return x.MaxLength
	}
	return 0
}

func (x *Schema) GetMinLength() int64 {
	if x != nil {
		return x.MinLength
	}
	return 0
}

func (x *Schema) GetPattern() string {
	if x != nil {
		return x.Pattern
	}
	return ""
}

func (x *Schema) GetMaxItems() int64 {
	if x != nil {
		return x.MaxItems
	}
	return 0
}

func (x *Schema) GetMinItems() int64 {
	if x != nil {
		return x.MinItems
	}
	return 0
}

func (x *Schema) GetUniqueItems() bool {
	if x != nil {
		return x.UniqueItems
	}
	return false
}

func (x *Schema) GetMaxProperties() int64 {
	if x != nil {
		return x.MaxProperties
	}
	return 0
}

func (x *Schema) GetMinProperties() int64 {
	if x != nil {
		return x.MinProperties
	}
	return 0
}

func (x *Schema) GetRequired() []string {
	if x != nil {
		return x.Required
	}
	return nil
}

func (x *Schema) GetEnum() []*Any {
	if x != nil {
		return x.Enum
	}
	return nil
}

func (x *Schema) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *Schema) GetAllOf() []*SchemaOrReference {
	if x != nil {
		return x.AllOf
	}
	return nil
}

func (x *Schema) GetOneOf() []*SchemaOrReference {
	if x != nil {
		return x.OneOf
	}
	return nil
}

func (x *Schema) GetAnyOf() []*SchemaOrReference {
	if x != nil {
		return x.AnyOf
	}
	return nil
}

func (x *Schema) GetNot() *Schema {
	if x != nil {
		return x.Not
	}
	return nil
}

func (x *Schema) GetItems() *ItemsItem {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *Schema) GetProperties() *Properties {
	if x != nil {
		return x.Properties
	}
	return nil
}

func (x *Schema) GetAdditionalProperties() *AdditionalPropertiesItem {
	if x != nil {
		return x.AdditionalProperties
	}
	return nil
}

func (x *Schema) GetDefault() *DefaultType {
	if x != nil {
		return x.Default
	}
	return nil
}

func (x *Schema) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Schema) GetFormat() string {
	if x != nil {
		return x.Format
	}
	return ""
}

func (x *Schema) GetSpecificationExtension() []*NamedAny {
	if x != nil {
		return x.SpecificationExtension
	}
	return nil
}

type SchemaOrReference struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Oneof:
	//
	//	*SchemaOrReference_Schema
	//	*SchemaOrReference_Reference
	Oneof isSchemaOrReference_Oneof `protobuf_oneof:"oneof"`
}

func (x *SchemaOrReference) Reset() {
	*x = SchemaOrReference{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[64]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SchemaOrReference) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SchemaOrReference) ProtoMessage() {}

func (x *SchemaOrReference) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[64]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SchemaOrReference.ProtoReflect.Descriptor instead.
func (*SchemaOrReference) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{64}
}

func (m *SchemaOrReference) GetOneof() isSchemaOrReference_Oneof {
	if m != nil {
		return m.Oneof
	}
	return nil
}

func (x *SchemaOrReference) GetSchema() *Schema {
	if x, ok := x.GetOneof().(*SchemaOrReference_Schema); ok {
		return x.Schema
	}
	return nil
}

func (x *SchemaOrReference) GetReference() *Reference {
	if x, ok := x.GetOneof().(*SchemaOrReference_Reference); ok {
		return x.Reference
	}
	return nil
}

type isSchemaOrReference_Oneof interface {
	isSchemaOrReference_Oneof()
}

type SchemaOrReference_Schema struct {
	Schema *Schema `protobuf:"bytes,1,opt,name=schema,proto3,oneof"`
}

type SchemaOrReference_Reference struct {
	Reference *Reference `protobuf:"bytes,2,opt,name=reference,proto3,oneof"`
}

func (*SchemaOrReference_Schema) isSchemaOrReference_Oneof() {}

func (*SchemaOrReference_Reference) isSchemaOrReference_Oneof() {}

type SchemasOrReferences struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AdditionalProperties []*NamedSchemaOrReference `protobuf:"bytes,1,rep,name=additional_properties,json=additionalProperties,proto3" json:"additional_properties,omitempty"`
}

func (x *SchemasOrReferences) Reset() {
	*x = SchemasOrReferences{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[65]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SchemasOrReferences) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SchemasOrReferences) ProtoMessage() {}

func (x *SchemasOrReferences) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[65]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SchemasOrReferences.ProtoReflect.Descriptor instead.
func (*SchemasOrReferences) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{65}
}

func (x *SchemasOrReferences) GetAdditionalProperties() []*NamedSchemaOrReference {
	if x != nil {
		return x.AdditionalProperties
	}
	return nil
}

// Lists the required security schemes to execute this operation. The name used for each property MUST correspond to a security scheme declared in the Security Schemes under the Components Object.  Security Requirement Objects that contain multiple schemes require that all schemes MUST be satisfied for a request to be authorized. This enables support for scenarios where multiple query parameters or HTTP headers are required to convey security information.  When a list of Security Requirement Objects is defined on the OpenAPI Object or Operation Object, only one of the Security Requirement Objects in the list needs to be satisfied to authorize the request.
type SecurityRequirement struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AdditionalProperties []*NamedStringArray `protobuf:"bytes,1,rep,name=additional_properties,json=additionalProperties,proto3" json:"additional_properties,omitempty"`
}

func (x *SecurityRequirement) Reset() {
	*x = SecurityRequirement{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[66]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SecurityRequirement) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SecurityRequirement) ProtoMessage() {}

func (x *SecurityRequirement) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[66]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SecurityRequirement.ProtoReflect.Descriptor instead.
func (*SecurityRequirement) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{66}
}

func (x *SecurityRequirement) GetAdditionalProperties() []*NamedStringArray {
	if x != nil {
		return x.AdditionalProperties
	}
	return nil
}

// Defines a security scheme that can be used by the operations. Supported schemes are HTTP authentication, an API key (either as a header, a cookie parameter or as a query parameter), mutual TLS (use of a client certificate), OAuth2's common flows (implicit, password, application and access code) as defined in RFC6749, and OpenID Connect.   Please note that currently (2019) the implicit flow is about to be deprecated OAuth 2.0 Security Best Current Practice. Recommended for most use case is Authorization Code Grant flow with PKCE.
type SecurityScheme struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type                   string      `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`
	Description            string      `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	Name                   string      `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	In                     string      `protobuf:"bytes,4,opt,name=in,proto3" json:"in,omitempty"`
	Scheme                 string      `protobuf:"bytes,5,opt,name=scheme,proto3" json:"scheme,omitempty"`
	BearerFormat           string      `protobuf:"bytes,6,opt,name=bearer_format,json=bearerFormat,proto3" json:"bearer_format,omitempty"`
	Flows                  *OauthFlows `protobuf:"bytes,7,opt,name=flows,proto3" json:"flows,omitempty"`
	OpenIdConnectUrl       string      `protobuf:"bytes,8,opt,name=open_id_connect_url,json=openIdConnectUrl,proto3" json:"open_id_connect_url,omitempty"`
	SpecificationExtension []*NamedAny `protobuf:"bytes,9,rep,name=specification_extension,json=specificationExtension,proto3" json:"specification_extension,omitempty"`
}

func (x *SecurityScheme) Reset() {
	*x = SecurityScheme{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[67]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SecurityScheme) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SecurityScheme) ProtoMessage() {}

func (x *SecurityScheme) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[67]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SecurityScheme.ProtoReflect.Descriptor instead.
func (*SecurityScheme) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{67}
}

func (x *SecurityScheme) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *SecurityScheme) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *SecurityScheme) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SecurityScheme) GetIn() string {
	if x != nil {
		return x.In
	}
	return ""
}

func (x *SecurityScheme) GetScheme() string {
	if x != nil {
		return x.Scheme
	}
	return ""
}

func (x *SecurityScheme) GetBearerFormat() string {
	if x != nil {
		return x.BearerFormat
	}
	return ""
}

func (x *SecurityScheme) GetFlows() *OauthFlows {
	if x != nil {
		return x.Flows
	}
	return nil
}

func (x *SecurityScheme) GetOpenIdConnectUrl() string {
	if x != nil {
		return x.OpenIdConnectUrl
	}
	return ""
}

func (x *SecurityScheme) GetSpecificationExtension() []*NamedAny {
	if x != nil {
		return x.SpecificationExtension
	}
	return nil
}

type SecuritySchemeOrReference struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Oneof:
	//
	//	*SecuritySchemeOrReference_SecurityScheme
	//	*SecuritySchemeOrReference_Reference
	Oneof isSecuritySchemeOrReference_Oneof `protobuf_oneof:"oneof"`
}

func (x *SecuritySchemeOrReference) Reset() {
	*x = SecuritySchemeOrReference{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[68]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SecuritySchemeOrReference) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SecuritySchemeOrReference) ProtoMessage() {}

func (x *SecuritySchemeOrReference) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[68]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SecuritySchemeOrReference.ProtoReflect.Descriptor instead.
func (*SecuritySchemeOrReference) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{68}
}

func (m *SecuritySchemeOrReference) GetOneof() isSecuritySchemeOrReference_Oneof {
	if m != nil {
		return m.Oneof
	}
	return nil
}

func (x *SecuritySchemeOrReference) GetSecurityScheme() *SecurityScheme {
	if x, ok := x.GetOneof().(*SecuritySchemeOrReference_SecurityScheme); ok {
		return x.SecurityScheme
	}
	return nil
}

func (x *SecuritySchemeOrReference) GetReference() *Reference {
	if x, ok := x.GetOneof().(*SecuritySchemeOrReference_Reference); ok {
		return x.Reference
	}
	return nil
}

type isSecuritySchemeOrReference_Oneof interface {
	isSecuritySchemeOrReference_Oneof()
}

type SecuritySchemeOrReference_SecurityScheme struct {
	SecurityScheme *SecurityScheme `protobuf:"bytes,1,opt,name=security_scheme,json=securityScheme,proto3,oneof"`
}

type SecuritySchemeOrReference_Reference struct {
	Reference *Reference `protobuf:"bytes,2,opt,name=reference,proto3,oneof"`
}

func (*SecuritySchemeOrReference_SecurityScheme) isSecuritySchemeOrReference_Oneof() {}

func (*SecuritySchemeOrReference_Reference) isSecuritySchemeOrReference_Oneof() {}

type SecuritySchemesOrReferences struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AdditionalProperties []*NamedSecuritySchemeOrReference `protobuf:"bytes,1,rep,name=additional_properties,json=additionalProperties,proto3" json:"additional_properties,omitempty"`
}

func (x *SecuritySchemesOrReferences) Reset() {
	*x = SecuritySchemesOrReferences{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[69]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SecuritySchemesOrReferences) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SecuritySchemesOrReferences) ProtoMessage() {}

func (x *SecuritySchemesOrReferences) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[69]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SecuritySchemesOrReferences.ProtoReflect.Descriptor instead.
func (*SecuritySchemesOrReferences) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{69}
}

func (x *SecuritySchemesOrReferences) GetAdditionalProperties() []*NamedSecuritySchemeOrReference {
	if x != nil {
		return x.AdditionalProperties
	}
	return nil
}

// An object representing a Server.
type Server struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url                    string           `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	Description            string           `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	Variables              *ServerVariables `protobuf:"bytes,3,opt,name=variables,proto3" json:"variables,omitempty"`
	SpecificationExtension []*NamedAny      `protobuf:"bytes,4,rep,name=specification_extension,json=specificationExtension,proto3" json:"specification_extension,omitempty"`
}

func (x *Server) Reset() {
	*x = Server{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[70]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Server) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Server) ProtoMessage() {}

func (x *Server) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[70]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Server.ProtoReflect.Descriptor instead.
func (*Server) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{70}
}

func (x *Server) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *Server) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Server) GetVariables() *ServerVariables {
	if x != nil {
		return x.Variables
	}
	return nil
}

func (x *Server) GetSpecificationExtension() []*NamedAny {
	if x != nil {
		return x.SpecificationExtension
	}
	return nil
}

// An object representing a Server Variable for server URL template substitution.
type ServerVariable struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enum                   []string    `protobuf:"bytes,1,rep,name=enum,proto3" json:"enum,omitempty"`
	Default                string      `protobuf:"bytes,2,opt,name=default,proto3" json:"default,omitempty"`
	Description            string      `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	SpecificationExtension []*NamedAny `protobuf:"bytes,4,rep,name=specification_extension,json=specificationExtension,proto3" json:"specification_extension,omitempty"`
}

func (x *ServerVariable) Reset() {
	*x = ServerVariable{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[71]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServerVariable) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServerVariable) ProtoMessage() {}

func (x *ServerVariable) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[71]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServerVariable.ProtoReflect.Descriptor instead.
func (*ServerVariable) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{71}
}

func (x *ServerVariable) GetEnum() []string {
	if x != nil {
		return x.Enum
	}
	return nil
}

func (x *ServerVariable) GetDefault() string {
	if x != nil {
		return x.Default
	}
	return ""
}

func (x *ServerVariable) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ServerVariable) GetSpecificationExtension() []*NamedAny {
	if x != nil {
		return x.SpecificationExtension
	}
	return nil
}

type ServerVariables struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AdditionalProperties []*NamedServerVariable `protobuf:"bytes,1,rep,name=additional_properties,json=additionalProperties,proto3" json:"additional_properties,omitempty"`
}

func (x *ServerVariables) Reset() {
	*x = ServerVariables{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[72]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServerVariables) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServerVariables) ProtoMessage() {}

func (x *ServerVariables) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[72]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServerVariables.ProtoReflect.Descriptor instead.
func (*ServerVariables) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{72}
}

func (x *ServerVariables) GetAdditionalProperties() []*NamedServerVariable {
	if x != nil {
		return x.AdditionalProperties
	}
	return nil
}

// Any property starting with x- is valid.
type SpecificationExtension struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Oneof:
	//
	//	*SpecificationExtension_Number
	//	*SpecificationExtension_Boolean
	//	*SpecificationExtension_String_
	Oneof isSpecificationExtension_Oneof `protobuf_oneof:"oneof"`
}

func (x *SpecificationExtension) Reset() {
	*x = SpecificationExtension{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[73]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SpecificationExtension) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SpecificationExtension) ProtoMessage() {}

func (x *SpecificationExtension) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[73]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SpecificationExtension.ProtoReflect.Descriptor instead.
func (*SpecificationExtension) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{73}
}

func (m *SpecificationExtension) GetOneof() isSpecificationExtension_Oneof {
	if m != nil {
		return m.Oneof
	}
	return nil
}

func (x *SpecificationExtension) GetNumber() float64 {
	if x, ok := x.GetOneof().(*SpecificationExtension_Number); ok {
		return x.Number
	}
	return 0
}

func (x *SpecificationExtension) GetBoolean() bool {
	if x, ok := x.GetOneof().(*SpecificationExtension_Boolean); ok {
		return x.Boolean
	}
	return false
}

func (x *SpecificationExtension) GetString_() string {
	if x, ok := x.GetOneof().(*SpecificationExtension_String_); ok {
		return x.String_
	}
	return ""
}

type isSpecificationExtension_Oneof interface {
	isSpecificationExtension_Oneof()
}

type SpecificationExtension_Number struct {
	Number float64 `protobuf:"fixed64,1,opt,name=number,proto3,oneof"`
}

type SpecificationExtension_Boolean struct {
	Boolean bool `protobuf:"varint,2,opt,name=boolean,proto3,oneof"`
}

type SpecificationExtension_String_ struct {
	String_ string `protobuf:"bytes,3,opt,name=string,proto3,oneof"`
}

func (*SpecificationExtension_Number) isSpecificationExtension_Oneof() {}

func (*SpecificationExtension_Boolean) isSpecificationExtension_Oneof() {}

func (*SpecificationExtension_String_) isSpecificationExtension_Oneof() {}

type StringArray struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value []string `protobuf:"bytes,1,rep,name=value,proto3" json:"value,omitempty"`
}

func (x *StringArray) Reset() {
	*x = StringArray{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[74]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StringArray) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringArray) ProtoMessage() {}

func (x *StringArray) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[74]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringArray.ProtoReflect.Descriptor instead.
func (*StringArray) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{74}
}

func (x *StringArray) GetValue() []string {
	if x != nil {
		return x.Value
	}
	return nil
}

type Strings struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AdditionalProperties []*NamedString `protobuf:"bytes,1,rep,name=additional_properties,json=additionalProperties,proto3" json:"additional_properties,omitempty"`
}

func (x *Strings) Reset() {
	*x = Strings{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[75]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Strings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Strings) ProtoMessage() {}

func (x *Strings) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[75]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Strings.ProtoReflect.Descriptor instead.
func (*Strings) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{75}
}

func (x *Strings) GetAdditionalProperties() []*NamedString {
	if x != nil {
		return x.AdditionalProperties
	}
	return nil
}

// Adds metadata to a single tag that is used by the Operation Object. It is not mandatory to have a Tag Object per tag defined in the Operation Object instances.
type Tag struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name                   string        `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Description            string        `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	ExternalDocs           *ExternalDocs `protobuf:"bytes,3,opt,name=external_docs,json=externalDocs,proto3" json:"external_docs,omitempty"`
	SpecificationExtension []*NamedAny   `protobuf:"bytes,4,rep,name=specification_extension,json=specificationExtension,proto3" json:"specification_extension,omitempty"`
}

func (x *Tag) Reset() {
	*x = Tag{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[76]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Tag) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Tag) ProtoMessage() {}

func (x *Tag) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[76]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Tag.ProtoReflect.Descriptor instead.
func (*Tag) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{76}
}

func (x *Tag) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Tag) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Tag) GetExternalDocs() *ExternalDocs {
	if x != nil {
		return x.ExternalDocs
	}
	return nil
}

func (x *Tag) GetSpecificationExtension() []*NamedAny {
	if x != nil {
		return x.SpecificationExtension
	}
	return nil
}

// A metadata object that allows for more fine-tuned XML model definitions.  When using arrays, XML element names are *not* inferred (for singular/plural forms) and the `name` property SHOULD be used to add that information. See examples for expected behavior.
type Xml struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name                   string      `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Namespace              string      `protobuf:"bytes,2,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Prefix                 string      `protobuf:"bytes,3,opt,name=prefix,proto3" json:"prefix,omitempty"`
	Attribute              bool        `protobuf:"varint,4,opt,name=attribute,proto3" json:"attribute,omitempty"`
	Wrapped                bool        `protobuf:"varint,5,opt,name=wrapped,proto3" json:"wrapped,omitempty"`
	SpecificationExtension []*NamedAny `protobuf:"bytes,6,rep,name=specification_extension,json=specificationExtension,proto3" json:"specification_extension,omitempty"`
}

func (x *Xml) Reset() {
	*x = Xml{}
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[77]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Xml) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Xml) ProtoMessage() {}

func (x *Xml) ProtoReflect() protoreflect.Message {
	mi := &file_openapiv3_OpenAPIv3_proto_msgTypes[77]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Xml.ProtoReflect.Descriptor instead.
func (*Xml) Descriptor() ([]byte, []int) {
	return file_openapiv3_OpenAPIv3_proto_rawDescGZIP(), []int{77}
}

func (x *Xml) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Xml) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *Xml) GetPrefix() string {
	if x != nil {
		return x.Prefix
	}
	return ""
}

func (x *Xml) GetAttribute() bool {
	if x != nil {
		return x.Attribute
	}
	return false
}

func (x *Xml) GetWrapped() bool {
	if x != nil {
		return x.Wrapped
	}
	return false
}

func (x *Xml) GetSpecificationExtension() []*NamedAny {
	if x != nil {
		return x.SpecificationExtension
	}
	return nil
}

var File_openapiv3_OpenAPIv3_proto protoreflect.FileDescriptor

var file_openapiv3_OpenAPIv3_proto_rawDesc = []byte{
	0x0a, 0x19, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x76, 0x33, 0x2f, 0x4f, 0x70, 0x65, 0x6e,
	0x41, 0x50, 0x49, 0x76, 0x33, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x6f, 0x70, 0x65,
	0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x1a, 0x19, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x61, 0x6e, 0x79, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0x90, 0x01, 0x0a, 0x18, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x49, 0x74, 0x65, 0x6d, 0x12,
	0x4f, 0x0a, 0x13, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x5f, 0x6f, 0x72, 0x5f, 0x72, 0x65, 0x66,
	0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6f,
	0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61,
	0x4f, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x48, 0x00, 0x52, 0x11, 0x73,
	0x63, 0x68, 0x65, 0x6d, 0x61, 0x4f, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65,
	0x12, 0x1a, 0x0a, 0x07, 0x62, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x08, 0x48, 0x00, 0x52, 0x07, 0x62, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x42, 0x07, 0x0a, 0x05,
	0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x22, 0x45, 0x0a, 0x03, 0x41, 0x6e, 0x79, 0x12, 0x2a, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x41, 0x6e,
	0x79, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x79, 0x61, 0x6d, 0x6c,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x79, 0x61, 0x6d, 0x6c, 0x22, 0x79, 0x0a, 0x0f,
	0x41, 0x6e, 0x79, 0x4f, 0x72, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12,
	0x23, 0x0a, 0x03, 0x61, 0x6e, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x6f,
	0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x41, 0x6e, 0x79, 0x48, 0x00, 0x52,
	0x03, 0x61, 0x6e, 0x79, 0x12, 0x38, 0x0a, 0x0a, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61,
	0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x48, 0x00, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x07,
	0x0a, 0x05, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x22, 0x88, 0x01, 0x0a, 0x08, 0x43, 0x61, 0x6c, 0x6c,
	0x62, 0x61, 0x63, 0x6b, 0x12, 0x2d, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e,
	0x4e, 0x61, 0x6d, 0x65, 0x64, 0x50, 0x61, 0x74, 0x68, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x04, 0x70,
	0x61, 0x74, 0x68, 0x12, 0x4d, 0x0a, 0x17, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76,
	0x33, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x41, 0x6e, 0x79, 0x52, 0x16, 0x73, 0x70, 0x65, 0x63,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69,
	0x6f, 0x6e, 0x22, 0x89, 0x01, 0x0a, 0x13, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x4f,
	0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x32, 0x0a, 0x08, 0x63, 0x61,
	0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6f,
	0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61,
	0x63, 0x6b, 0x48, 0x00, 0x52, 0x08, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x12, 0x35,
	0x0a, 0x09, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x15, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x52,
	0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x48, 0x00, 0x52, 0x09, 0x72, 0x65, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x42, 0x07, 0x0a, 0x05, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x22, 0x72,
	0x0a, 0x15, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x73, 0x4f, 0x72, 0x52, 0x65, 0x66,
	0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x12, 0x59, 0x0a, 0x15, 0x61, 0x64, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69,
	0x2e, 0x76, 0x33, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63,
	0x6b, 0x4f, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x14, 0x61, 0x64,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69,
	0x65, 0x73, 0x22, 0xac, 0x05, 0x0a, 0x0a, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x73, 0x12, 0x39, 0x0a, 0x07, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e,
	0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x73, 0x4f, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x73, 0x52, 0x07, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x73, 0x12, 0x3f, 0x0a, 0x09,
	0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x73, 0x4f, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x73, 0x52, 0x09, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x73, 0x12, 0x42, 0x0a,
	0x0a, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x22, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x4f, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x73, 0x52, 0x0a, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72,
	0x73, 0x12, 0x3c, 0x0a, 0x08, 0x65, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x73, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33,
	0x2e, 0x45, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x73, 0x4f, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x73, 0x52, 0x08, 0x65, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x73, 0x12,
	0x4c, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x62, 0x6f, 0x64, 0x69, 0x65,
	0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70,
	0x69, 0x2e, 0x76, 0x33, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x42, 0x6f, 0x64, 0x69,
	0x65, 0x73, 0x4f, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x52, 0x0d,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x42, 0x6f, 0x64, 0x69, 0x65, 0x73, 0x12, 0x39, 0x0a,
	0x07, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f,
	0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x73, 0x4f, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x52,
	0x07, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x12, 0x52, 0x0a, 0x10, 0x73, 0x65, 0x63, 0x75,
	0x72, 0x69, 0x74, 0x79, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x73, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e,
	0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x73, 0x4f,
	0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x52, 0x0f, 0x73, 0x65, 0x63,
	0x75, 0x72, 0x69, 0x74, 0x79, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x73, 0x12, 0x33, 0x0a, 0x05,
	0x6c, 0x69, 0x6e, 0x6b, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6f, 0x70,
	0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x4c, 0x69, 0x6e, 0x6b, 0x73, 0x4f, 0x72,
	0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x52, 0x05, 0x6c, 0x69, 0x6e, 0x6b,
	0x73, 0x12, 0x3f, 0x0a, 0x09, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x73, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76,
	0x33, 0x2e, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x73, 0x4f, 0x72, 0x52, 0x65, 0x66,
	0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x52, 0x09, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63,
	0x6b, 0x73, 0x12, 0x4d, 0x0a, 0x17, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33,
	0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x41, 0x6e, 0x79, 0x52, 0x16, 0x73, 0x70, 0x65, 0x63, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f,
	0x6e, 0x22, 0x94, 0x01, 0x0a, 0x07, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x75, 0x72, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x4d, 0x0a, 0x17, 0x73, 0x70, 0x65,
	0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6f, 0x70, 0x65,
	0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x41, 0x6e, 0x79,
	0x52, 0x16, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45,
	0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x66, 0x0a, 0x0b, 0x44, 0x65, 0x66, 0x61,
	0x75, 0x6c, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x48, 0x00, 0x52, 0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x12, 0x1a, 0x0a, 0x07, 0x62, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x08, 0x48, 0x00, 0x52, 0x07, 0x62, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x12, 0x18, 0x0a,
	0x06, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52,
	0x06, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x42, 0x07, 0x0a, 0x05, 0x6f, 0x6e, 0x65, 0x6f, 0x66,
	0x22, 0xb2, 0x01, 0x0a, 0x0d, 0x44, 0x69, 0x73, 0x63, 0x72, 0x69, 0x6d, 0x69, 0x6e, 0x61, 0x74,
	0x6f, 0x72, 0x12, 0x23, 0x0a, 0x0d, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x72, 0x6f, 0x70, 0x65,
	0x72, 0x74, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2d, 0x0a, 0x07, 0x6d, 0x61, 0x70, 0x70, 0x69,
	0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61,
	0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x07, 0x6d,
	0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x12, 0x4d, 0x0a, 0x17, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70,
	0x69, 0x2e, 0x76, 0x33, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x41, 0x6e, 0x79, 0x52, 0x16, 0x73,
	0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x78, 0x74, 0x65,
	0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0xc9, 0x03, 0x0a, 0x08, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65,
	0x6e, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x12, 0x24, 0x0a, 0x04,
	0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x6f, 0x70, 0x65,
	0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x69, 0x6e,
	0x66, 0x6f, 0x12, 0x2c, 0x0a, 0x07, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x73, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33,
	0x2e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x73,
	0x12, 0x27, 0x0a, 0x05, 0x70, 0x61, 0x74, 0x68, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x11, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x50, 0x61, 0x74,
	0x68, 0x73, 0x52, 0x05, 0x70, 0x61, 0x74, 0x68, 0x73, 0x12, 0x36, 0x0a, 0x0a, 0x63, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e,
	0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x73, 0x12, 0x3b, 0x0a, 0x08, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x18, 0x06, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33,
	0x2e, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x52, 0x08, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x12, 0x23,
	0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x6f,
	0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x54, 0x61, 0x67, 0x52, 0x04, 0x74,
	0x61, 0x67, 0x73, 0x12, 0x3d, 0x0a, 0x0d, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f,
	0x64, 0x6f, 0x63, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x6f, 0x70, 0x65,
	0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x44, 0x6f, 0x63, 0x73, 0x52, 0x0c, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x44, 0x6f,
	0x63, 0x73, 0x12, 0x4d, 0x0a, 0x17, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33,
	0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x41, 0x6e, 0x79, 0x52, 0x16, 0x73, 0x70, 0x65, 0x63, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f,
	0x6e, 0x22, 0x8e, 0x02, 0x0a, 0x08, 0x45, 0x6e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x21,
	0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x39, 0x0a, 0x07, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x4f, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x73, 0x52, 0x07, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x12, 0x14, 0x0a, 0x05,
	0x73, 0x74, 0x79, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x79,
	0x6c, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x78, 0x70, 0x6c, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x78, 0x70, 0x6c, 0x6f, 0x64, 0x65, 0x12, 0x25, 0x0a, 0x0e,
	0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x72, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x64, 0x12, 0x4d, 0x0a, 0x17, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x06,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76,
	0x33, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x41, 0x6e, 0x79, 0x52, 0x16, 0x73, 0x70, 0x65, 0x63,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69,
	0x6f, 0x6e, 0x22, 0x5b, 0x0a, 0x09, 0x45, 0x6e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x12,
	0x4e, 0x0a, 0x15, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x70, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19,
	0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x4e, 0x61, 0x6d, 0x65,
	0x64, 0x45, 0x6e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x14, 0x61, 0x64, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x22,
	0xe2, 0x01, 0x0a, 0x07, 0x45, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73,
	0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x75,
	0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x25, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69,
	0x2e, 0x76, 0x33, 0x2e, 0x41, 0x6e, 0x79, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x25,
	0x0a, 0x0e, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x4d, 0x0a, 0x17, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69,
	0x2e, 0x76, 0x33, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x41, 0x6e, 0x79, 0x52, 0x16, 0x73, 0x70,
	0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x78, 0x74, 0x65, 0x6e,
	0x73, 0x69, 0x6f, 0x6e, 0x22, 0x85, 0x01, 0x0a, 0x12, 0x45, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65,
	0x4f, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x2f, 0x0a, 0x07, 0x65,
	0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x6f,
	0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x45, 0x78, 0x61, 0x6d, 0x70, 0x6c,
	0x65, 0x48, 0x00, 0x52, 0x07, 0x65, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x12, 0x35, 0x0a, 0x09,
	0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x15, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x52, 0x65, 0x66,
	0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x48, 0x00, 0x52, 0x09, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x42, 0x07, 0x0a, 0x05, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x22, 0x70, 0x0a, 0x14,
	0x45, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x73, 0x4f, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x73, 0x12, 0x58, 0x0a, 0x15, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x5f, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33,
	0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x45, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x4f, 0x72, 0x52,
	0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x14, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x22, 0x57,
	0x0a, 0x0a, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x49, 0x0a, 0x15,
	0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x70, 0x72, 0x6f, 0x70, 0x65,
	0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6f, 0x70,
	0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x41, 0x6e,
	0x79, 0x52, 0x14, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x50, 0x72, 0x6f,
	0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x22, 0x91, 0x01, 0x0a, 0x0c, 0x45, 0x78, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x6c, 0x44, 0x6f, 0x63, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72,
	0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x4d, 0x0a, 0x17,
	0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x78,
	0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e,
	0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x64,
	0x41, 0x6e, 0x79, 0x52, 0x16, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x8a, 0x04, 0x0a, 0x06,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x72, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x65, 0x70, 0x72, 0x65, 0x63, 0x61, 0x74,
	0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x64, 0x65, 0x70, 0x72, 0x65, 0x63,
	0x61, 0x74, 0x65, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x65, 0x6d,
	0x70, 0x74, 0x79, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0f, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x79, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x73, 0x74, 0x79, 0x6c, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x78, 0x70, 0x6c, 0x6f, 0x64,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x78, 0x70, 0x6c, 0x6f, 0x64, 0x65,
	0x12, 0x25, 0x0a, 0x0e, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x72, 0x65, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x52,
	0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x64, 0x12, 0x35, 0x0a, 0x06, 0x73, 0x63, 0x68, 0x65, 0x6d,
	0x61, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70,
	0x69, 0x2e, 0x76, 0x33, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x4f, 0x72, 0x52, 0x65, 0x66,
	0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x06, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x12, 0x29,
	0x0a, 0x07, 0x65, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0f, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x41, 0x6e, 0x79,
	0x52, 0x07, 0x65, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x12, 0x3c, 0x0a, 0x08, 0x65, 0x78, 0x61,
	0x6d, 0x70, 0x6c, 0x65, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x6f, 0x70,
	0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x45, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65,
	0x73, 0x4f, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x52, 0x08, 0x65,
	0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x73, 0x12, 0x30, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61,
	0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x54, 0x79, 0x70, 0x65, 0x73,
	0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x4d, 0x0a, 0x17, 0x73, 0x70, 0x65,
	0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6f, 0x70, 0x65,
	0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x41, 0x6e, 0x79,
	0x52, 0x16, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45,
	0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x81, 0x01, 0x0a, 0x11, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x4f, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x2c,
	0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x48, 0x00, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x35, 0x0a, 0x09,
	0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x15, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x52, 0x65, 0x66,
	0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x48, 0x00, 0x52, 0x09, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x42, 0x07, 0x0a, 0x05, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x22, 0x6e, 0x0a, 0x13,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x4f, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x73, 0x12, 0x57, 0x0a, 0x15, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x5f, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e,
	0x4e, 0x61, 0x6d, 0x65, 0x64, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x4f, 0x72, 0x52, 0x65, 0x66,
	0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x14, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x22, 0xc9, 0x02, 0x0a,
	0x04, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x28, 0x0a,
	0x10, 0x74, 0x65, 0x72, 0x6d, 0x73, 0x5f, 0x6f, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x74, 0x65, 0x72, 0x6d, 0x73, 0x4f, 0x66,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x2d, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x61,
	0x63, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61,
	0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x52, 0x07, 0x63,
	0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x12, 0x2d, 0x0a, 0x07, 0x6c, 0x69, 0x63, 0x65, 0x6e, 0x73,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70,
	0x69, 0x2e, 0x76, 0x33, 0x2e, 0x4c, 0x69, 0x63, 0x65, 0x6e, 0x73, 0x65, 0x52, 0x07, 0x6c, 0x69,
	0x63, 0x65, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12,
	0x4d, 0x0a, 0x17, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x14, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x4e, 0x61,
	0x6d, 0x65, 0x64, 0x41, 0x6e, 0x79, 0x52, 0x16, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x18,
	0x0a, 0x07, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x22, 0x5a, 0x0a, 0x09, 0x49, 0x74, 0x65, 0x6d,
	0x73, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x4d, 0x0a, 0x13, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x5f,
	0x6f, 0x72, 0x5f, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e,
	0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x4f, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x52, 0x11, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x4f, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x22, 0x7e, 0x0a, 0x07, 0x4c, 0x69, 0x63, 0x65, 0x6e, 0x73, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x4d, 0x0a, 0x17, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69,
	0x2e, 0x76, 0x33, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x41, 0x6e, 0x79, 0x52, 0x16, 0x73, 0x70,
	0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x78, 0x74, 0x65, 0x6e,
	0x73, 0x69, 0x6f, 0x6e, 0x22, 0xe8, 0x02, 0x0a, 0x04, 0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x23, 0x0a,
	0x0d, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x66, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x66, 0x12, 0x21, 0x0a, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x3b, 0x0a, 0x0a, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74,
	0x65, 0x72, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x6f, 0x70, 0x65, 0x6e,
	0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x41, 0x6e, 0x79, 0x4f, 0x72, 0x45, 0x78, 0x70, 0x72,
	0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65,
	0x72, 0x73, 0x12, 0x3e, 0x0a, 0x0c, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x62, 0x6f,
	0x64, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61,
	0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x41, 0x6e, 0x79, 0x4f, 0x72, 0x45, 0x78, 0x70, 0x72, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x42, 0x6f,
	0x64, 0x79, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2a, 0x0a, 0x06, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76,
	0x33, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x52, 0x06, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x12, 0x4d, 0x0a, 0x17, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x14, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x4e,
	0x61, 0x6d, 0x65, 0x64, 0x41, 0x6e, 0x79, 0x52, 0x16, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x22,
	0x79, 0x0a, 0x0f, 0x4c, 0x69, 0x6e, 0x6b, 0x4f, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x12, 0x26, 0x0a, 0x04, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x10, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x4c, 0x69,
	0x6e, 0x6b, 0x48, 0x00, 0x52, 0x04, 0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x35, 0x0a, 0x09, 0x72, 0x65,
	0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e,
	0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x52, 0x65, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x48, 0x00, 0x52, 0x09, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x42, 0x07, 0x0a, 0x05, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x22, 0x6a, 0x0a, 0x11, 0x4c, 0x69,
	0x6e, 0x6b, 0x73, 0x4f, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x12,
	0x55, 0x0a, 0x15, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x70, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20,
	0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x4e, 0x61, 0x6d, 0x65,
	0x64, 0x4c, 0x69, 0x6e, 0x6b, 0x4f, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65,
	0x52, 0x14, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x50, 0x72, 0x6f, 0x70,
	0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x22, 0xad, 0x02, 0x0a, 0x09, 0x4d, 0x65, 0x64, 0x69, 0x61,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x35, 0x0a, 0x06, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76,
	0x33, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x4f, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x52, 0x06, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x12, 0x29, 0x0a, 0x07, 0x65,
	0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x6f,
	0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x41, 0x6e, 0x79, 0x52, 0x07, 0x65,
	0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x12, 0x3c, 0x0a, 0x08, 0x65, 0x78, 0x61, 0x6d, 0x70, 0x6c,
	0x65, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61,
	0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x45, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x73, 0x4f, 0x72,
	0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x52, 0x08, 0x65, 0x78, 0x61, 0x6d,
	0x70, 0x6c, 0x65, 0x73, 0x12, 0x31, 0x0a, 0x08, 0x65, 0x6e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69,
	0x2e, 0x76, 0x33, 0x2e, 0x45, 0x6e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x08, 0x65,
	0x6e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x4d, 0x0a, 0x17, 0x73, 0x70, 0x65, 0x63, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61,
	0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x41, 0x6e, 0x79, 0x52, 0x16,
	0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x78, 0x74,
	0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x5d, 0x0a, 0x0a, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x54,
	0x79, 0x70, 0x65, 0x73, 0x12, 0x4f, 0x0a, 0x15, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x5f, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33,
	0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x14, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x50, 0x72, 0x6f, 0x70, 0x65,
	0x72, 0x74, 0x69, 0x65, 0x73, 0x22, 0x45, 0x0a, 0x08, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x41, 0x6e,
	0x79, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76,
	0x33, 0x2e, 0x41, 0x6e, 0x79, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x65, 0x0a, 0x18,
	0x4e, 0x61, 0x6d, 0x65, 0x64, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x4f, 0x72, 0x52,
	0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x35, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x6f, 0x70,
	0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63,
	0x6b, 0x4f, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x22, 0x4f, 0x0a, 0x0d, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x45, 0x6e, 0x63, 0x6f,
	0x64, 0x69, 0x6e, 0x67, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70,
	0x69, 0x2e, 0x76, 0x33, 0x2e, 0x45, 0x6e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x22, 0x63, 0x0a, 0x17, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x45, 0x78, 0x61,
	0x6d, 0x70, 0x6c, 0x65, 0x4f, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x34, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e,
	0x45, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x4f, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x61, 0x0a, 0x16, 0x4e, 0x61, 0x6d,
	0x65, 0x64, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x4f, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x33, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69,
	0x2e, 0x76, 0x33, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x4f, 0x72, 0x52, 0x65, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x5d, 0x0a, 0x14,
	0x4e, 0x61, 0x6d, 0x65, 0x64, 0x4c, 0x69, 0x6e, 0x6b, 0x4f, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x31, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70,
	0x69, 0x2e, 0x76, 0x33, 0x2e, 0x4c, 0x69, 0x6e, 0x6b, 0x4f, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x51, 0x0a, 0x0e, 0x4e,
	0x61, 0x6d, 0x65, 0x64, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x2b, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x15, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x4d, 0x65,
	0x64, 0x69, 0x61, 0x54, 0x79, 0x70, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x67,
	0x0a, 0x19, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72,
	0x4f, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x36, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20,
	0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x65, 0x74, 0x65, 0x72, 0x4f, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x4f, 0x0a, 0x0d, 0x4e, 0x61, 0x6d, 0x65, 0x64,
	0x50, 0x61, 0x74, 0x68, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6f, 0x70,
	0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x50, 0x61, 0x74, 0x68, 0x49, 0x74, 0x65,
	0x6d, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x6b, 0x0a, 0x1b, 0x4e, 0x61, 0x6d, 0x65,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x42, 0x6f, 0x64, 0x79, 0x4f, 0x72, 0x52, 0x65,
	0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x38, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6f, 0x70, 0x65,
	0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x42,
	0x6f, 0x64, 0x79, 0x4f, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x65, 0x0a, 0x18, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x4f, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x35, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76,
	0x33, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x4f, 0x72, 0x52, 0x65, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x61, 0x0a, 0x16,
	0x4e, 0x61, 0x6d, 0x65, 0x64, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x4f, 0x72, 0x52, 0x65, 0x66,
	0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x33, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6f, 0x70, 0x65, 0x6e,
	0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x4f, 0x72, 0x52,
	0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22,
	0x71, 0x0a, 0x1e, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79,
	0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x4f, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x3b, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76,
	0x33, 0x2e, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65,
	0x4f, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x22, 0x5b, 0x0a, 0x13, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x53, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x30, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x6f,
	0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22,
	0x37, 0x0a, 0x0b, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x55, 0x0a, 0x10, 0x4e, 0x61, 0x6d, 0x65,
	0x64, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x41, 0x72, 0x72, 0x61, 0x79, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x2d, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x17, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x53, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x41, 0x72, 0x72, 0x61, 0x79, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22,
	0xf2, 0x01, 0x0a, 0x09, 0x4f, 0x61, 0x75, 0x74, 0x68, 0x46, 0x6c, 0x6f, 0x77, 0x12, 0x2b, 0x0a,
	0x11, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x75,
	0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x66, 0x72, 0x65,
	0x73, 0x68, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65,
	0x66, 0x72, 0x65, 0x73, 0x68, 0x55, 0x72, 0x6c, 0x12, 0x2b, 0x0a, 0x06, 0x73, 0x63, 0x6f, 0x70,
	0x65, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61,
	0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x06, 0x73,
	0x63, 0x6f, 0x70, 0x65, 0x73, 0x12, 0x4d, 0x0a, 0x17, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69,
	0x2e, 0x76, 0x33, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x41, 0x6e, 0x79, 0x52, 0x16, 0x73, 0x70,
	0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x78, 0x74, 0x65, 0x6e,
	0x73, 0x69, 0x6f, 0x6e, 0x22, 0xcd, 0x02, 0x0a, 0x0a, 0x4f, 0x61, 0x75, 0x74, 0x68, 0x46, 0x6c,
	0x6f, 0x77, 0x73, 0x12, 0x31, 0x0a, 0x08, 0x69, 0x6d, 0x70, 0x6c, 0x69, 0x63, 0x69, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e,
	0x76, 0x33, 0x2e, 0x4f, 0x61, 0x75, 0x74, 0x68, 0x46, 0x6c, 0x6f, 0x77, 0x52, 0x08, 0x69, 0x6d,
	0x70, 0x6c, 0x69, 0x63, 0x69, 0x74, 0x12, 0x31, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f,
	0x72, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61,
	0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x4f, 0x61, 0x75, 0x74, 0x68, 0x46, 0x6c, 0x6f, 0x77, 0x52,
	0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x44, 0x0a, 0x12, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x5f, 0x63, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x73, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e,
	0x76, 0x33, 0x2e, 0x4f, 0x61, 0x75, 0x74, 0x68, 0x46, 0x6c, 0x6f, 0x77, 0x52, 0x11, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x43, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x73, 0x12,
	0x44, 0x0a, 0x12, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6f, 0x70,
	0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x4f, 0x61, 0x75, 0x74, 0x68, 0x46, 0x6c,
	0x6f, 0x77, 0x52, 0x11, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x4d, 0x0a, 0x17, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69,
	0x2e, 0x76, 0x33, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x41, 0x6e, 0x79, 0x52, 0x16, 0x73, 0x70,
	0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x78, 0x74, 0x65, 0x6e,
	0x73, 0x69, 0x6f, 0x6e, 0x22, 0x53, 0x0a, 0x06, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x49,
	0x0a, 0x15, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x70, 0x72, 0x6f,
	0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e,
	0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x64,
	0x41, 0x6e, 0x79, 0x52, 0x14, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x50,
	0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x22, 0x96, 0x05, 0x0a, 0x09, 0x4f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x73,
	0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x75,
	0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3d, 0x0a, 0x0d, 0x65, 0x78, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x5f, 0x64, 0x6f, 0x63, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18,
	0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x45, 0x78, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x6c, 0x44, 0x6f, 0x63, 0x73, 0x52, 0x0c, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6c, 0x44, 0x6f, 0x63, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x40, 0x0a, 0x0a, 0x70, 0x61, 0x72,
	0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e,
	0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x65, 0x74, 0x65, 0x72, 0x4f, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52,
	0x0a, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x12, 0x45, 0x0a, 0x0c, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x22, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x42, 0x6f, 0x64, 0x79, 0x4f, 0x72, 0x52, 0x65, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x0b, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x42, 0x6f,
	0x64, 0x79, 0x12, 0x33, 0x0a, 0x09, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x73, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e,
	0x76, 0x33, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x73, 0x52, 0x09, 0x72, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x73, 0x12, 0x3f, 0x0a, 0x09, 0x63, 0x61, 0x6c, 0x6c, 0x62,
	0x61, 0x63, 0x6b, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6f, 0x70, 0x65,
	0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b,
	0x73, 0x4f, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x52, 0x09, 0x63,
	0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x65, 0x70, 0x72,
	0x65, 0x63, 0x61, 0x74, 0x65, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x64, 0x65,
	0x70, 0x72, 0x65, 0x63, 0x61, 0x74, 0x65, 0x64, 0x12, 0x3b, 0x0a, 0x08, 0x73, 0x65, 0x63, 0x75,
	0x72, 0x69, 0x74, 0x79, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x6f, 0x70, 0x65,
	0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79,
	0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x08, 0x73, 0x65, 0x63,
	0x75, 0x72, 0x69, 0x74, 0x79, 0x12, 0x2c, 0x0a, 0x07, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x73,
	0x18, 0x0c, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69,
	0x2e, 0x76, 0x33, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x52, 0x07, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x73, 0x12, 0x4d, 0x0a, 0x17, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0d,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76,
	0x33, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x41, 0x6e, 0x79, 0x52, 0x16, 0x73, 0x70, 0x65, 0x63,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69,
	0x6f, 0x6e, 0x22, 0xb1, 0x04, 0x0a, 0x09, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x69, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x65, 0x70, 0x72, 0x65, 0x63, 0x61, 0x74, 0x65, 0x64,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x64, 0x65, 0x70, 0x72, 0x65, 0x63, 0x61, 0x74,
	0x65, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x65, 0x6d, 0x70, 0x74,
	0x79, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x61,
	0x6c, 0x6c, 0x6f, 0x77, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x73, 0x74, 0x79, 0x6c, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73,
	0x74, 0x79, 0x6c, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x78, 0x70, 0x6c, 0x6f, 0x64, 0x65, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x78, 0x70, 0x6c, 0x6f, 0x64, 0x65, 0x12, 0x25,
	0x0a, 0x0e, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x72, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x64,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x64, 0x12, 0x35, 0x0a, 0x06, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e,
	0x76, 0x33, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x4f, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x52, 0x06, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x12, 0x29, 0x0a, 0x07,
	0x65, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e,
	0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x41, 0x6e, 0x79, 0x52, 0x07,
	0x65, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x12, 0x3c, 0x0a, 0x08, 0x65, 0x78, 0x61, 0x6d, 0x70,
	0x6c, 0x65, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x6f, 0x70, 0x65, 0x6e,
	0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x45, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x73, 0x4f,
	0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x52, 0x08, 0x65, 0x78, 0x61,
	0x6d, 0x70, 0x6c, 0x65, 0x73, 0x12, 0x30, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69,
	0x2e, 0x76, 0x33, 0x2e, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x54, 0x79, 0x70, 0x65, 0x73, 0x52, 0x07,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x4d, 0x0a, 0x17, 0x73, 0x70, 0x65, 0x63, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61,
	0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x41, 0x6e, 0x79, 0x52, 0x16,
	0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x78, 0x74,
	0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x8d, 0x01, 0x0a, 0x14, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x65, 0x74, 0x65, 0x72, 0x4f, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x12,
	0x35, 0x0a, 0x09, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x48, 0x00, 0x52, 0x09, 0x70, 0x61, 0x72,
	0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x12, 0x35, 0x0a, 0x09, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6f, 0x70, 0x65, 0x6e,
	0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65,
	0x48, 0x00, 0x52, 0x09, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x42, 0x07, 0x0a,
	0x05, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x22, 0x74, 0x0a, 0x16, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65,
	0x74, 0x65, 0x72, 0x73, 0x4f, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x73,
	0x12, 0x5a, 0x0a, 0x15, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x70,
	0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x25, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x4e, 0x61, 0x6d,
	0x65, 0x64, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x4f, 0x72, 0x52, 0x65, 0x66,
	0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x14, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x22, 0xfa, 0x04, 0x0a,
	0x08, 0x50, 0x61, 0x74, 0x68, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x11, 0x0a, 0x04, 0x5f, 0x72, 0x65,
	0x66, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x52, 0x65, 0x66, 0x12, 0x18, 0x0a, 0x07,
	0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73,
	0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x27, 0x0a, 0x03, 0x67, 0x65, 0x74, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e,
	0x76, 0x33, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x03, 0x67, 0x65,
	0x74, 0x12, 0x27, 0x0a, 0x03, 0x70, 0x75, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15,
	0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x4f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x03, 0x70, 0x75, 0x74, 0x12, 0x29, 0x0a, 0x04, 0x70, 0x6f,
	0x73, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61,
	0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x04, 0x70, 0x6f, 0x73, 0x74, 0x12, 0x2d, 0x0a, 0x06, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e,
	0x76, 0x33, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x06, 0x64, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x12, 0x2f, 0x0a, 0x07, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e,
	0x76, 0x33, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x29, 0x0a, 0x04, 0x68, 0x65, 0x61, 0x64, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33,
	0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x04, 0x68, 0x65, 0x61, 0x64,
	0x12, 0x2b, 0x0a, 0x05, 0x70, 0x61, 0x74, 0x63, 0x68, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x15, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x4f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x05, 0x70, 0x61, 0x74, 0x63, 0x68, 0x12, 0x2b, 0x0a,
	0x05, 0x74, 0x72, 0x61, 0x63, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6f,
	0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x05, 0x74, 0x72, 0x61, 0x63, 0x65, 0x12, 0x2c, 0x0a, 0x07, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x6f, 0x70,
	0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x52,
	0x07, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x73, 0x12, 0x40, 0x0a, 0x0a, 0x70, 0x61, 0x72, 0x61,
	0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x6f,
	0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65,
	0x74, 0x65, 0x72, 0x4f, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x0a,
	0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x12, 0x4d, 0x0a, 0x17, 0x73, 0x70,
	0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x78, 0x74, 0x65,
	0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6f, 0x70,
	0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x41, 0x6e,
	0x79, 0x52, 0x16, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x85, 0x01, 0x0a, 0x05, 0x50, 0x61,
	0x74, 0x68, 0x73, 0x12, 0x2d, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x19, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x4e,
	0x61, 0x6d, 0x65, 0x64, 0x50, 0x61, 0x74, 0x68, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x04, 0x70, 0x61,
	0x74, 0x68, 0x12, 0x4d, 0x0a, 0x17, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33,
	0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x41, 0x6e, 0x79, 0x52, 0x16, 0x73, 0x70, 0x65, 0x63, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f,
	0x6e, 0x22, 0x65, 0x0a, 0x0a, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x12,
	0x57, 0x0a, 0x15, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x70, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22,
	0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x4e, 0x61, 0x6d, 0x65,
	0x64, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x4f, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x52, 0x14, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x50, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x22, 0x5a, 0x0a, 0x09, 0x52, 0x65, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x11, 0x0a, 0x04, 0x5f, 0x72, 0x65, 0x66, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x52, 0x65, 0x66, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x6d, 0x6d,
	0x61, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x75, 0x6d, 0x6d, 0x61,
	0x72, 0x79, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x22, 0x79, 0x0a, 0x19, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x42,
	0x6f, 0x64, 0x69, 0x65, 0x73, 0x4f, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65,
	0x73, 0x12, 0x5c, 0x0a, 0x15, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f,
	0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x27, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x4e, 0x61,
	0x6d, 0x65, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x42, 0x6f, 0x64, 0x79, 0x4f, 0x72,
	0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x14, 0x61, 0x64, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x22,
	0xcc, 0x01, 0x0a, 0x0b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x42, 0x6f, 0x64, 0x79, 0x12,
	0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x30, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x16, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e,
	0x4d, 0x65, 0x64, 0x69, 0x61, 0x54, 0x79, 0x70, 0x65, 0x73, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x12,
	0x4d, 0x0a, 0x17, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x14, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x4e, 0x61,
	0x6d, 0x65, 0x64, 0x41, 0x6e, 0x79, 0x52, 0x16, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x96,
	0x01, 0x0a, 0x16, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x42, 0x6f, 0x64, 0x79, 0x4f, 0x72,
	0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x3c, 0x0a, 0x0c, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x5f, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x17, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x42, 0x6f, 0x64, 0x79, 0x48, 0x00, 0x52, 0x0b, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x42, 0x6f, 0x64, 0x79, 0x12, 0x35, 0x0a, 0x09, 0x72, 0x65, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6f, 0x70, 0x65,
	0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x48, 0x00, 0x52, 0x09, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x42, 0x07,
	0x0a, 0x05, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x22, 0x9d, 0x02, 0x0a, 0x08, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x39, 0x0a, 0x07, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70,
	0x69, 0x2e, 0x76, 0x33, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x4f, 0x72, 0x52, 0x65,
	0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x52, 0x07, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x73, 0x12, 0x30, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x16, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e,
	0x4d, 0x65, 0x64, 0x69, 0x61, 0x54, 0x79, 0x70, 0x65, 0x73, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x12, 0x33, 0x0a, 0x05, 0x6c, 0x69, 0x6e, 0x6b, 0x73, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e,
	0x4c, 0x69, 0x6e, 0x6b, 0x73, 0x4f, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65,
	0x73, 0x52, 0x05, 0x6c, 0x69, 0x6e, 0x6b, 0x73, 0x12, 0x4d, 0x0a, 0x17, 0x73, 0x70, 0x65, 0x63,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73,
	0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6f, 0x70, 0x65, 0x6e,
	0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x41, 0x6e, 0x79, 0x52,
	0x16, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x78,
	0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x89, 0x01, 0x0a, 0x13, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x4f, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x12,
	0x32, 0x0a, 0x08, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x14, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x00, 0x52, 0x08, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x35, 0x0a, 0x09, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69,
	0x2e, 0x76, 0x33, 0x2e, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x48, 0x00, 0x52,
	0x09, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x42, 0x07, 0x0a, 0x05, 0x6f, 0x6e,
	0x65, 0x6f, 0x66, 0x22, 0xef, 0x01, 0x0a, 0x09, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x73, 0x12, 0x39, 0x0a, 0x07, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x4f, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x52, 0x07, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x12, 0x58, 0x0a, 0x15,
	0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x6f, 0x72, 0x5f, 0x72, 0x65, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6f, 0x70,
	0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x4f, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x52, 0x13, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x4f, 0x72, 0x52, 0x65, 0x66,
	0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x4d, 0x0a, 0x17, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70,
	0x69, 0x2e, 0x76, 0x33, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x41, 0x6e, 0x79, 0x52, 0x16, 0x73,
	0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x78, 0x74, 0x65,
	0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x72, 0x0a, 0x15, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x73, 0x4f, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x12, 0x59,
	0x0a, 0x15, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x70, 0x72, 0x6f,
	0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e,
	0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x64,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x4f, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x52, 0x14, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x50,
	0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x22, 0xaf, 0x0b, 0x0a, 0x06, 0x53, 0x63,
	0x68, 0x65, 0x6d, 0x61, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x75, 0x6c, 0x6c, 0x61, 0x62, 0x6c, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x6e, 0x75, 0x6c, 0x6c, 0x61, 0x62, 0x6c, 0x65,
	0x12, 0x3f, 0x0a, 0x0d, 0x64, 0x69, 0x73, 0x63, 0x72, 0x69, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x6f,
	0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70,
	0x69, 0x2e, 0x76, 0x33, 0x2e, 0x44, 0x69, 0x73, 0x63, 0x72, 0x69, 0x6d, 0x69, 0x6e, 0x61, 0x74,
	0x6f, 0x72, 0x52, 0x0d, 0x64, 0x69, 0x73, 0x63, 0x72, 0x69, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x6f,
	0x72, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x6f, 0x6e, 0x6c, 0x79, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x72, 0x65, 0x61, 0x64, 0x4f, 0x6e, 0x6c, 0x79, 0x12, 0x1d,
	0x0a, 0x0a, 0x77, 0x72, 0x69, 0x74, 0x65, 0x5f, 0x6f, 0x6e, 0x6c, 0x79, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x09, 0x77, 0x72, 0x69, 0x74, 0x65, 0x4f, 0x6e, 0x6c, 0x79, 0x12, 0x21, 0x0a,
	0x03, 0x78, 0x6d, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x6f, 0x70, 0x65,
	0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x58, 0x6d, 0x6c, 0x52, 0x03, 0x78, 0x6d, 0x6c,
	0x12, 0x3d, 0x0a, 0x0d, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x64, 0x6f, 0x63,
	0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70,
	0x69, 0x2e, 0x76, 0x33, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x44, 0x6f, 0x63,
	0x73, 0x52, 0x0c, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x44, 0x6f, 0x63, 0x73, 0x12,
	0x29, 0x0a, 0x07, 0x65, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0f, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x41, 0x6e,
	0x79, 0x52, 0x07, 0x65, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x65,
	0x70, 0x72, 0x65, 0x63, 0x61, 0x74, 0x65, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a,
	0x64, 0x65, 0x70, 0x72, 0x65, 0x63, 0x61, 0x74, 0x65, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x65, 0x5f, 0x6f, 0x66, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0a, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x65, 0x4f,
	0x66, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x61, 0x78, 0x69, 0x6d, 0x75, 0x6d, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x07, 0x6d, 0x61, 0x78, 0x69, 0x6d, 0x75, 0x6d, 0x12, 0x2b, 0x0a, 0x11, 0x65,
	0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x76, 0x65, 0x5f, 0x6d, 0x61, 0x78, 0x69, 0x6d, 0x75, 0x6d,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x76,
	0x65, 0x4d, 0x61, 0x78, 0x69, 0x6d, 0x75, 0x6d, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x69, 0x6e, 0x69,
	0x6d, 0x75, 0x6d, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x01, 0x52, 0x07, 0x6d, 0x69, 0x6e, 0x69, 0x6d,
	0x75, 0x6d, 0x12, 0x2b, 0x0a, 0x11, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x76, 0x65, 0x5f,
	0x6d, 0x69, 0x6e, 0x69, 0x6d, 0x75, 0x6d, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x65,
	0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x76, 0x65, 0x4d, 0x69, 0x6e, 0x69, 0x6d, 0x75, 0x6d, 0x12,
	0x1d, 0x0a, 0x0a, 0x6d, 0x61, 0x78, 0x5f, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x18, 0x0f, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x6d, 0x61, 0x78, 0x4c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x12, 0x1d,
	0x0a, 0x0a, 0x6d, 0x69, 0x6e, 0x5f, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x18, 0x10, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x09, 0x6d, 0x69, 0x6e, 0x4c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x12, 0x18, 0x0a,
	0x07, 0x70, 0x61, 0x74, 0x74, 0x65, 0x72, 0x6e, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x70, 0x61, 0x74, 0x74, 0x65, 0x72, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x61, 0x78, 0x5f, 0x69,
	0x74, 0x65, 0x6d, 0x73, 0x18, 0x12, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x6d, 0x61, 0x78, 0x49,
	0x74, 0x65, 0x6d, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x69, 0x6e, 0x5f, 0x69, 0x74, 0x65, 0x6d,
	0x73, 0x18, 0x13, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x6d, 0x69, 0x6e, 0x49, 0x74, 0x65, 0x6d,
	0x73, 0x12, 0x21, 0x0a, 0x0c, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d,
	0x73, 0x18, 0x14, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x49,
	0x74, 0x65, 0x6d, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x6d, 0x61, 0x78, 0x5f, 0x70, 0x72, 0x6f, 0x70,
	0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x15, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x6d, 0x61,
	0x78, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x6d,
	0x69, 0x6e, 0x5f, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x16, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0d, 0x6d, 0x69, 0x6e, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69,
	0x65, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x18, 0x17,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x12, 0x23,
	0x0a, 0x04, 0x65, 0x6e, 0x75, 0x6d, 0x18, 0x18, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x6f,
	0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x41, 0x6e, 0x79, 0x52, 0x04, 0x65,
	0x6e, 0x75, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x19, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x34, 0x0a, 0x06, 0x61, 0x6c, 0x6c, 0x5f, 0x6f,
	0x66, 0x18, 0x1a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70,
	0x69, 0x2e, 0x76, 0x33, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x4f, 0x72, 0x52, 0x65, 0x66,
	0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x05, 0x61, 0x6c, 0x6c, 0x4f, 0x66, 0x12, 0x34, 0x0a,
	0x06, 0x6f, 0x6e, 0x65, 0x5f, 0x6f, 0x66, 0x18, 0x1b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e,
	0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x6d,
	0x61, 0x4f, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x05, 0x6f, 0x6e,
	0x65, 0x4f, 0x66, 0x12, 0x34, 0x0a, 0x06, 0x61, 0x6e, 0x79, 0x5f, 0x6f, 0x66, 0x18, 0x1c, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33,
	0x2e, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x4f, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x52, 0x05, 0x61, 0x6e, 0x79, 0x4f, 0x66, 0x12, 0x24, 0x0a, 0x03, 0x6e, 0x6f, 0x74,
	0x18, 0x1d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69,
	0x2e, 0x76, 0x33, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x52, 0x03, 0x6e, 0x6f, 0x74, 0x12,
	0x2b, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15,
	0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x49, 0x74, 0x65, 0x6d,
	0x73, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x36, 0x0a, 0x0a,
	0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x16, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x50, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72,
	0x74, 0x69, 0x65, 0x73, 0x12, 0x59, 0x0a, 0x15, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x5f, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x20, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33,
	0x2e, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x50, 0x72, 0x6f, 0x70, 0x65,
	0x72, 0x74, 0x69, 0x65, 0x73, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x14, 0x61, 0x64, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x12,
	0x31, 0x0a, 0x07, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x18, 0x21, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x17, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x44, 0x65,
	0x66, 0x61, 0x75, 0x6c, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x64, 0x65, 0x66, 0x61, 0x75,
	0x6c, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x22, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x18, 0x23,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x12, 0x4d, 0x0a, 0x17,
	0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x78,
	0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x24, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e,
	0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x64,
	0x41, 0x6e, 0x79, 0x52, 0x16, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x81, 0x01, 0x0a, 0x11,
	0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x4f, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x12, 0x2c, 0x0a, 0x06, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x53,
	0x63, 0x68, 0x65, 0x6d, 0x61, 0x48, 0x00, 0x52, 0x06, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x12,
	0x35, 0x0a, 0x09, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e,
	0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x48, 0x00, 0x52, 0x09, 0x72, 0x65, 0x66,
	0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x42, 0x07, 0x0a, 0x05, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x22,
	0x6e, 0x0a, 0x13, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x73, 0x4f, 0x72, 0x52, 0x65, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x12, 0x57, 0x0a, 0x15, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e,
	0x76, 0x33, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x4f, 0x72,
	0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x14, 0x61, 0x64, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x22,
	0x68, 0x0a, 0x13, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x51, 0x0a, 0x15, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e,
	0x76, 0x33, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x41, 0x72,
	0x72, 0x61, 0x79, 0x52, 0x14, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x50,
	0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x22, 0xd3, 0x02, 0x0a, 0x0e, 0x53, 0x65,
	0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x6e, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x12, 0x23,
	0x0a, 0x0d, 0x62, 0x65, 0x61, 0x72, 0x65, 0x72, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x62, 0x65, 0x61, 0x72, 0x65, 0x72, 0x46, 0x6f, 0x72,
	0x6d, 0x61, 0x74, 0x12, 0x2c, 0x0a, 0x05, 0x66, 0x6c, 0x6f, 0x77, 0x73, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x16, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e,
	0x4f, 0x61, 0x75, 0x74, 0x68, 0x46, 0x6c, 0x6f, 0x77, 0x73, 0x52, 0x05, 0x66, 0x6c, 0x6f, 0x77,
	0x73, 0x12, 0x2d, 0x0a, 0x13, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x69, 0x64, 0x5f, 0x63, 0x6f, 0x6e,
	0x6e, 0x65, 0x63, 0x74, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10,
	0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x55, 0x72, 0x6c,
	0x12, 0x4d, 0x0a, 0x17, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x14, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x4e,
	0x61, 0x6d, 0x65, 0x64, 0x41, 0x6e, 0x79, 0x52, 0x16, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x22,
	0xa2, 0x01, 0x0a, 0x19, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x53, 0x63, 0x68, 0x65,
	0x6d, 0x65, 0x4f, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x45, 0x0a,
	0x0f, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69,
	0x2e, 0x76, 0x33, 0x2e, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x53, 0x63, 0x68, 0x65,
	0x6d, 0x65, 0x48, 0x00, 0x52, 0x0e, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x53, 0x63,
	0x68, 0x65, 0x6d, 0x65, 0x12, 0x35, 0x0a, 0x09, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70,
	0x69, 0x2e, 0x76, 0x33, 0x2e, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x48, 0x00,
	0x52, 0x09, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x42, 0x07, 0x0a, 0x05, 0x6f,
	0x6e, 0x65, 0x6f, 0x66, 0x22, 0x7e, 0x0a, 0x1b, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79,
	0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x73, 0x4f, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x73, 0x12, 0x5f, 0x0a, 0x15, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x5f, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e,
	0x4e, 0x61, 0x6d, 0x65, 0x64, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x53, 0x63, 0x68,
	0x65, 0x6d, 0x65, 0x4f, 0x72, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x14,
	0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72,
	0x74, 0x69, 0x65, 0x73, 0x22, 0xc6, 0x01, 0x0a, 0x06, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x12,
	0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72,
	0x6c, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x39, 0x0a, 0x09, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69,
	0x2e, 0x76, 0x33, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62,
	0x6c, 0x65, 0x73, 0x52, 0x09, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x12, 0x4d,
	0x0a, 0x17, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x14, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x4e, 0x61, 0x6d,
	0x65, 0x64, 0x41, 0x6e, 0x79, 0x52, 0x16, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0xaf, 0x01,
	0x0a, 0x0e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x65, 0x6e, 0x75, 0x6d, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04,
	0x65, 0x6e, 0x75, 0x6d, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x12, 0x20,
	0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x4d, 0x0a, 0x17, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x14, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x4e,
	0x61, 0x6d, 0x65, 0x64, 0x41, 0x6e, 0x79, 0x52, 0x16, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x22,
	0x67, 0x0a, 0x0f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c,
	0x65, 0x73, 0x12, 0x54, 0x0a, 0x15, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x5f, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1f, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x4e,
	0x61, 0x6d, 0x65, 0x64, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62,
	0x6c, 0x65, 0x52, 0x14, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x50, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x22, 0x71, 0x0a, 0x16, 0x53, 0x70, 0x65, 0x63,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x01, 0x48, 0x00, 0x52, 0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x1a, 0x0a, 0x07,
	0x62, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52,
	0x07, 0x62, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x12, 0x18, 0x0a, 0x06, 0x73, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x06, 0x73, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x42, 0x07, 0x0a, 0x05, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x22, 0x23, 0x0a, 0x0b, 0x53,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x41, 0x72, 0x72, 0x61, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x22, 0x57, 0x0a, 0x07, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x4c, 0x0a, 0x15, 0x61,
	0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72,
	0x74, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6f, 0x70, 0x65,
	0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x53, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x52, 0x14, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x50,
	0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x22, 0xc9, 0x01, 0x0a, 0x03, 0x54, 0x61,
	0x67, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3d, 0x0a, 0x0d, 0x65, 0x78, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x5f, 0x64, 0x6f, 0x63, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18,
	0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x45, 0x78, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x6c, 0x44, 0x6f, 0x63, 0x73, 0x52, 0x0c, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6c, 0x44, 0x6f, 0x63, 0x73, 0x12, 0x4d, 0x0a, 0x17, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70,
	0x69, 0x2e, 0x76, 0x33, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x41, 0x6e, 0x79, 0x52, 0x16, 0x73,
	0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x78, 0x74, 0x65,
	0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0xd6, 0x01, 0x0a, 0x03, 0x58, 0x6d, 0x6c, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x70, 0x72, 0x65, 0x66, 0x69, 0x78, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x70, 0x72, 0x65, 0x66, 0x69, 0x78, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x74, 0x74, 0x72, 0x69,
	0x62, 0x75, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x61, 0x74, 0x74, 0x72,
	0x69, 0x62, 0x75, 0x74, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65, 0x64,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65, 0x64, 0x12,
	0x4d, 0x0a, 0x17, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x14, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x33, 0x2e, 0x4e, 0x61,
	0x6d, 0x65, 0x64, 0x41, 0x6e, 0x79, 0x52, 0x16, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x3e,
	0x0a, 0x0e, 0x6f, 0x72, 0x67, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x5f, 0x76, 0x33,
	0x42, 0x0c, 0x4f, 0x70, 0x65, 0x6e, 0x41, 0x50, 0x49, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01,
	0x5a, 0x16, 0x2e, 0x2f, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x76, 0x33, 0x3b, 0x6f, 0x70,
	0x65, 0x6e, 0x61, 0x70, 0x69, 0x5f, 0x76, 0x33, 0xa2, 0x02, 0x03, 0x4f, 0x41, 0x53, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_openapiv3_OpenAPIv3_proto_rawDescOnce sync.Once
	file_openapiv3_OpenAPIv3_proto_rawDescData = file_openapiv3_OpenAPIv3_proto_rawDesc
)

func file_openapiv3_OpenAPIv3_proto_rawDescGZIP() []byte {
	file_openapiv3_OpenAPIv3_proto_rawDescOnce.Do(func() {
		file_openapiv3_OpenAPIv3_proto_rawDescData = protoimpl.X.CompressGZIP(file_openapiv3_OpenAPIv3_proto_rawDescData)
	})
	return file_openapiv3_OpenAPIv3_proto_rawDescData
}

var file_openapiv3_OpenAPIv3_proto_msgTypes = make([]protoimpl.MessageInfo, 78)
var file_openapiv3_OpenAPIv3_proto_goTypes = []any{
	(*AdditionalPropertiesItem)(nil),       // 0: openapi.v3.AdditionalPropertiesItem
	(*Any)(nil),                            // 1: openapi.v3.Any
	(*AnyOrExpression)(nil),                // 2: openapi.v3.AnyOrExpression
	(*Callback)(nil),                       // 3: openapi.v3.Callback
	(*CallbackOrReference)(nil),            // 4: openapi.v3.CallbackOrReference
	(*CallbacksOrReferences)(nil),          // 5: openapi.v3.CallbacksOrReferences
	(*Components)(nil),                     // 6: openapi.v3.Components
	(*Contact)(nil),                        // 7: openapi.v3.Contact
	(*DefaultType)(nil),                    // 8: openapi.v3.DefaultType
	(*Discriminator)(nil),                  // 9: openapi.v3.Discriminator
	(*Document)(nil),                       // 10: openapi.v3.Document
	(*Encoding)(nil),                       // 11: openapi.v3.Encoding
	(*Encodings)(nil),                      // 12: openapi.v3.Encodings
	(*Example)(nil),                        // 13: openapi.v3.Example
	(*ExampleOrReference)(nil),             // 14: openapi.v3.ExampleOrReference
	(*ExamplesOrReferences)(nil),           // 15: openapi.v3.ExamplesOrReferences
	(*Expression)(nil),                     // 16: openapi.v3.Expression
	(*ExternalDocs)(nil),                   // 17: openapi.v3.ExternalDocs
	(*Header)(nil),                         // 18: openapi.v3.Header
	(*HeaderOrReference)(nil),              // 19: openapi.v3.HeaderOrReference
	(*HeadersOrReferences)(nil),            // 20: openapi.v3.HeadersOrReferences
	(*Info)(nil),                           // 21: openapi.v3.Info
	(*ItemsItem)(nil),                      // 22: openapi.v3.ItemsItem
	(*License)(nil),                        // 23: openapi.v3.License
	(*Link)(nil),                           // 24: openapi.v3.Link
	(*LinkOrReference)(nil),                // 25: openapi.v3.LinkOrReference
	(*LinksOrReferences)(nil),              // 26: openapi.v3.LinksOrReferences
	(*MediaType)(nil),                      // 27: openapi.v3.MediaType
	(*MediaTypes)(nil),                     // 28: openapi.v3.MediaTypes
	(*NamedAny)(nil),                       // 29: openapi.v3.NamedAny
	(*NamedCallbackOrReference)(nil),       // 30: openapi.v3.NamedCallbackOrReference
	(*NamedEncoding)(nil),                  // 31: openapi.v3.NamedEncoding
	(*NamedExampleOrReference)(nil),        // 32: openapi.v3.NamedExampleOrReference
	(*NamedHeaderOrReference)(nil),         // 33: openapi.v3.NamedHeaderOrReference
	(*NamedLinkOrReference)(nil),           // 34: openapi.v3.NamedLinkOrReference
	(*NamedMediaType)(nil),                 // 35: openapi.v3.NamedMediaType
	(*NamedParameterOrReference)(nil),      // 36: openapi.v3.NamedParameterOrReference
	(*NamedPathItem)(nil),                  // 37: openapi.v3.NamedPathItem
	(*NamedRequestBodyOrReference)(nil),    // 38: openapi.v3.NamedRequestBodyOrReference
	(*NamedResponseOrReference)(nil),       // 39: openapi.v3.NamedResponseOrReference
	(*NamedSchemaOrReference)(nil),         // 40: openapi.v3.NamedSchemaOrReference
	(*NamedSecuritySchemeOrReference)(nil), // 41: openapi.v3.NamedSecuritySchemeOrReference
	(*NamedServerVariable)(nil),            // 42: openapi.v3.NamedServerVariable
	(*NamedString)(nil),                    // 43: openapi.v3.NamedString
	(*NamedStringArray)(nil),               // 44: openapi.v3.NamedStringArray
	(*OauthFlow)(nil),                      // 45: openapi.v3.OauthFlow
	(*OauthFlows)(nil),                     // 46: openapi.v3.OauthFlows
	(*Object)(nil),                         // 47: openapi.v3.Object
	(*Operation)(nil),                      // 48: openapi.v3.Operation
	(*Parameter)(nil),                      // 49: openapi.v3.Parameter
	(*ParameterOrReference)(nil),           // 50: openapi.v3.ParameterOrReference
	(*ParametersOrReferences)(nil),         // 51: openapi.v3.ParametersOrReferences
	(*PathItem)(nil),                       // 52: openapi.v3.PathItem
	(*Paths)(nil),                          // 53: openapi.v3.Paths
	(*Properties)(nil),                     // 54: openapi.v3.Properties
	(*Reference)(nil),                      // 55: openapi.v3.Reference
	(*RequestBodiesOrReferences)(nil),      // 56: openapi.v3.RequestBodiesOrReferences
	(*RequestBody)(nil),                    // 57: openapi.v3.RequestBody
	(*RequestBodyOrReference)(nil),         // 58: openapi.v3.RequestBodyOrReference
	(*Response)(nil),                       // 59: openapi.v3.Response
	(*ResponseOrReference)(nil),            // 60: openapi.v3.ResponseOrReference
	(*Responses)(nil),                      // 61: openapi.v3.Responses
	(*ResponsesOrReferences)(nil),          // 62: openapi.v3.ResponsesOrReferences
	(*Schema)(nil),                         // 63: openapi.v3.Schema
	(*SchemaOrReference)(nil),              // 64: openapi.v3.SchemaOrReference
	(*SchemasOrReferences)(nil),            // 65: openapi.v3.SchemasOrReferences
	(*SecurityRequirement)(nil),            // 66: openapi.v3.SecurityRequirement
	(*SecurityScheme)(nil),                 // 67: openapi.v3.SecurityScheme
	(*SecuritySchemeOrReference)(nil),      // 68: openapi.v3.SecuritySchemeOrReference
	(*SecuritySchemesOrReferences)(nil),    // 69: openapi.v3.SecuritySchemesOrReferences
	(*Server)(nil),                         // 70: openapi.v3.Server
	(*ServerVariable)(nil),                 // 71: openapi.v3.ServerVariable
	(*ServerVariables)(nil),                // 72: openapi.v3.ServerVariables
	(*SpecificationExtension)(nil),         // 73: openapi.v3.SpecificationExtension
	(*StringArray)(nil),                    // 74: openapi.v3.StringArray
	(*Strings)(nil),                        // 75: openapi.v3.Strings
	(*Tag)(nil),                            // 76: openapi.v3.Tag
	(*Xml)(nil),                            // 77: openapi.v3.Xml
	(*anypb.Any)(nil),                      // 78: google.protobuf.Any
}
var file_openapiv3_OpenAPIv3_proto_depIdxs = []int32{
	64,  // 0: openapi.v3.AdditionalPropertiesItem.schema_or_reference:type_name -> openapi.v3.SchemaOrReference
	78,  // 1: openapi.v3.Any.value:type_name -> google.protobuf.Any
	1,   // 2: openapi.v3.AnyOrExpression.any:type_name -> openapi.v3.Any
	16,  // 3: openapi.v3.AnyOrExpression.expression:type_name -> openapi.v3.Expression
	37,  // 4: openapi.v3.Callback.path:type_name -> openapi.v3.NamedPathItem
	29,  // 5: openapi.v3.Callback.specification_extension:type_name -> openapi.v3.NamedAny
	3,   // 6: openapi.v3.CallbackOrReference.callback:type_name -> openapi.v3.Callback
	55,  // 7: openapi.v3.CallbackOrReference.reference:type_name -> openapi.v3.Reference
	30,  // 8: openapi.v3.CallbacksOrReferences.additional_properties:type_name -> openapi.v3.NamedCallbackOrReference
	65,  // 9: openapi.v3.Components.schemas:type_name -> openapi.v3.SchemasOrReferences
	62,  // 10: openapi.v3.Components.responses:type_name -> openapi.v3.ResponsesOrReferences
	51,  // 11: openapi.v3.Components.parameters:type_name -> openapi.v3.ParametersOrReferences
	15,  // 12: openapi.v3.Components.examples:type_name -> openapi.v3.ExamplesOrReferences
	56,  // 13: openapi.v3.Components.request_bodies:type_name -> openapi.v3.RequestBodiesOrReferences
	20,  // 14: openapi.v3.Components.headers:type_name -> openapi.v3.HeadersOrReferences
	69,  // 15: openapi.v3.Components.security_schemes:type_name -> openapi.v3.SecuritySchemesOrReferences
	26,  // 16: openapi.v3.Components.links:type_name -> openapi.v3.LinksOrReferences
	5,   // 17: openapi.v3.Components.callbacks:type_name -> openapi.v3.CallbacksOrReferences
	29,  // 18: openapi.v3.Components.specification_extension:type_name -> openapi.v3.NamedAny
	29,  // 19: openapi.v3.Contact.specification_extension:type_name -> openapi.v3.NamedAny
	75,  // 20: openapi.v3.Discriminator.mapping:type_name -> openapi.v3.Strings
	29,  // 21: openapi.v3.Discriminator.specification_extension:type_name -> openapi.v3.NamedAny
	21,  // 22: openapi.v3.Document.info:type_name -> openapi.v3.Info
	70,  // 23: openapi.v3.Document.servers:type_name -> openapi.v3.Server
	53,  // 24: openapi.v3.Document.paths:type_name -> openapi.v3.Paths
	6,   // 25: openapi.v3.Document.components:type_name -> openapi.v3.Components
	66,  // 26: openapi.v3.Document.security:type_name -> openapi.v3.SecurityRequirement
	76,  // 27: openapi.v3.Document.tags:type_name -> openapi.v3.Tag
	17,  // 28: openapi.v3.Document.external_docs:type_name -> openapi.v3.ExternalDocs
	29,  // 29: openapi.v3.Document.specification_extension:type_name -> openapi.v3.NamedAny
	20,  // 30: openapi.v3.Encoding.headers:type_name -> openapi.v3.HeadersOrReferences
	29,  // 31: openapi.v3.Encoding.specification_extension:type_name -> openapi.v3.NamedAny
	31,  // 32: openapi.v3.Encodings.additional_properties:type_name -> openapi.v3.NamedEncoding
	1,   // 33: openapi.v3.Example.value:type_name -> openapi.v3.Any
	29,  // 34: openapi.v3.Example.specification_extension:type_name -> openapi.v3.NamedAny
	13,  // 35: openapi.v3.ExampleOrReference.example:type_name -> openapi.v3.Example
	55,  // 36: openapi.v3.ExampleOrReference.reference:type_name -> openapi.v3.Reference
	32,  // 37: openapi.v3.ExamplesOrReferences.additional_properties:type_name -> openapi.v3.NamedExampleOrReference
	29,  // 38: openapi.v3.Expression.additional_properties:type_name -> openapi.v3.NamedAny
	29,  // 39: openapi.v3.ExternalDocs.specification_extension:type_name -> openapi.v3.NamedAny
	64,  // 40: openapi.v3.Header.schema:type_name -> openapi.v3.SchemaOrReference
	1,   // 41: openapi.v3.Header.example:type_name -> openapi.v3.Any
	15,  // 42: openapi.v3.Header.examples:type_name -> openapi.v3.ExamplesOrReferences
	28,  // 43: openapi.v3.Header.content:type_name -> openapi.v3.MediaTypes
	29,  // 44: openapi.v3.Header.specification_extension:type_name -> openapi.v3.NamedAny
	18,  // 45: openapi.v3.HeaderOrReference.header:type_name -> openapi.v3.Header
	55,  // 46: openapi.v3.HeaderOrReference.reference:type_name -> openapi.v3.Reference
	33,  // 47: openapi.v3.HeadersOrReferences.additional_properties:type_name -> openapi.v3.NamedHeaderOrReference
	7,   // 48: openapi.v3.Info.contact:type_name -> openapi.v3.Contact
	23,  // 49: openapi.v3.Info.license:type_name -> openapi.v3.License
	29,  // 50: openapi.v3.Info.specification_extension:type_name -> openapi.v3.NamedAny
	64,  // 51: openapi.v3.ItemsItem.schema_or_reference:type_name -> openapi.v3.SchemaOrReference
	29,  // 52: openapi.v3.License.specification_extension:type_name -> openapi.v3.NamedAny
	2,   // 53: openapi.v3.Link.parameters:type_name -> openapi.v3.AnyOrExpression
	2,   // 54: openapi.v3.Link.request_body:type_name -> openapi.v3.AnyOrExpression
	70,  // 55: openapi.v3.Link.server:type_name -> openapi.v3.Server
	29,  // 56: openapi.v3.Link.specification_extension:type_name -> openapi.v3.NamedAny
	24,  // 57: openapi.v3.LinkOrReference.link:type_name -> openapi.v3.Link
	55,  // 58: openapi.v3.LinkOrReference.reference:type_name -> openapi.v3.Reference
	34,  // 59: openapi.v3.LinksOrReferences.additional_properties:type_name -> openapi.v3.NamedLinkOrReference
	64,  // 60: openapi.v3.MediaType.schema:type_name -> openapi.v3.SchemaOrReference
	1,   // 61: openapi.v3.MediaType.example:type_name -> openapi.v3.Any
	15,  // 62: openapi.v3.MediaType.examples:type_name -> openapi.v3.ExamplesOrReferences
	12,  // 63: openapi.v3.MediaType.encoding:type_name -> openapi.v3.Encodings
	29,  // 64: openapi.v3.MediaType.specification_extension:type_name -> openapi.v3.NamedAny
	35,  // 65: openapi.v3.MediaTypes.additional_properties:type_name -> openapi.v3.NamedMediaType
	1,   // 66: openapi.v3.NamedAny.value:type_name -> openapi.v3.Any
	4,   // 67: openapi.v3.NamedCallbackOrReference.value:type_name -> openapi.v3.CallbackOrReference
	11,  // 68: openapi.v3.NamedEncoding.value:type_name -> openapi.v3.Encoding
	14,  // 69: openapi.v3.NamedExampleOrReference.value:type_name -> openapi.v3.ExampleOrReference
	19,  // 70: openapi.v3.NamedHeaderOrReference.value:type_name -> openapi.v3.HeaderOrReference
	25,  // 71: openapi.v3.NamedLinkOrReference.value:type_name -> openapi.v3.LinkOrReference
	27,  // 72: openapi.v3.NamedMediaType.value:type_name -> openapi.v3.MediaType
	50,  // 73: openapi.v3.NamedParameterOrReference.value:type_name -> openapi.v3.ParameterOrReference
	52,  // 74: openapi.v3.NamedPathItem.value:type_name -> openapi.v3.PathItem
	58,  // 75: openapi.v3.NamedRequestBodyOrReference.value:type_name -> openapi.v3.RequestBodyOrReference
	60,  // 76: openapi.v3.NamedResponseOrReference.value:type_name -> openapi.v3.ResponseOrReference
	64,  // 77: openapi.v3.NamedSchemaOrReference.value:type_name -> openapi.v3.SchemaOrReference
	68,  // 78: openapi.v3.NamedSecuritySchemeOrReference.value:type_name -> openapi.v3.SecuritySchemeOrReference
	71,  // 79: openapi.v3.NamedServerVariable.value:type_name -> openapi.v3.ServerVariable
	74,  // 80: openapi.v3.NamedStringArray.value:type_name -> openapi.v3.StringArray
	75,  // 81: openapi.v3.OauthFlow.scopes:type_name -> openapi.v3.Strings
	29,  // 82: openapi.v3.OauthFlow.specification_extension:type_name -> openapi.v3.NamedAny
	45,  // 83: openapi.v3.OauthFlows.implicit:type_name -> openapi.v3.OauthFlow
	45,  // 84: openapi.v3.OauthFlows.password:type_name -> openapi.v3.OauthFlow
	45,  // 85: openapi.v3.OauthFlows.client_credentials:type_name -> openapi.v3.OauthFlow
	45,  // 86: openapi.v3.OauthFlows.authorization_code:type_name -> openapi.v3.OauthFlow
	29,  // 87: openapi.v3.OauthFlows.specification_extension:type_name -> openapi.v3.NamedAny
	29,  // 88: openapi.v3.Object.additional_properties:type_name -> openapi.v3.NamedAny
	17,  // 89: openapi.v3.Operation.external_docs:type_name -> openapi.v3.ExternalDocs
	50,  // 90: openapi.v3.Operation.parameters:type_name -> openapi.v3.ParameterOrReference
	58,  // 91: openapi.v3.Operation.request_body:type_name -> openapi.v3.RequestBodyOrReference
	61,  // 92: openapi.v3.Operation.responses:type_name -> openapi.v3.Responses
	5,   // 93: openapi.v3.Operation.callbacks:type_name -> openapi.v3.CallbacksOrReferences
	66,  // 94: openapi.v3.Operation.security:type_name -> openapi.v3.SecurityRequirement
	70,  // 95: openapi.v3.Operation.servers:type_name -> openapi.v3.Server
	29,  // 96: openapi.v3.Operation.specification_extension:type_name -> openapi.v3.NamedAny
	64,  // 97: openapi.v3.Parameter.schema:type_name -> openapi.v3.SchemaOrReference
	1,   // 98: openapi.v3.Parameter.example:type_name -> openapi.v3.Any
	15,  // 99: openapi.v3.Parameter.examples:type_name -> openapi.v3.ExamplesOrReferences
	28,  // 100: openapi.v3.Parameter.content:type_name -> openapi.v3.MediaTypes
	29,  // 101: openapi.v3.Parameter.specification_extension:type_name -> openapi.v3.NamedAny
	49,  // 102: openapi.v3.ParameterOrReference.parameter:type_name -> openapi.v3.Parameter
	55,  // 103: openapi.v3.ParameterOrReference.reference:type_name -> openapi.v3.Reference
	36,  // 104: openapi.v3.ParametersOrReferences.additional_properties:type_name -> openapi.v3.NamedParameterOrReference
	48,  // 105: openapi.v3.PathItem.get:type_name -> openapi.v3.Operation
	48,  // 106: openapi.v3.PathItem.put:type_name -> openapi.v3.Operation
	48,  // 107: openapi.v3.PathItem.post:type_name -> openapi.v3.Operation
	48,  // 108: openapi.v3.PathItem.delete:type_name -> openapi.v3.Operation
	48,  // 109: openapi.v3.PathItem.options:type_name -> openapi.v3.Operation
	48,  // 110: openapi.v3.PathItem.head:type_name -> openapi.v3.Operation
	48,  // 111: openapi.v3.PathItem.patch:type_name -> openapi.v3.Operation
	48,  // 112: openapi.v3.PathItem.trace:type_name -> openapi.v3.Operation
	70,  // 113: openapi.v3.PathItem.servers:type_name -> openapi.v3.Server
	50,  // 114: openapi.v3.PathItem.parameters:type_name -> openapi.v3.ParameterOrReference
	29,  // 115: openapi.v3.PathItem.specification_extension:type_name -> openapi.v3.NamedAny
	37,  // 116: openapi.v3.Paths.path:type_name -> openapi.v3.NamedPathItem
	29,  // 117: openapi.v3.Paths.specification_extension:type_name -> openapi.v3.NamedAny
	40,  // 118: openapi.v3.Properties.additional_properties:type_name -> openapi.v3.NamedSchemaOrReference
	38,  // 119: openapi.v3.RequestBodiesOrReferences.additional_properties:type_name -> openapi.v3.NamedRequestBodyOrReference
	28,  // 120: openapi.v3.RequestBody.content:type_name -> openapi.v3.MediaTypes
	29,  // 121: openapi.v3.RequestBody.specification_extension:type_name -> openapi.v3.NamedAny
	57,  // 122: openapi.v3.RequestBodyOrReference.request_body:type_name -> openapi.v3.RequestBody
	55,  // 123: openapi.v3.RequestBodyOrReference.reference:type_name -> openapi.v3.Reference
	20,  // 124: openapi.v3.Response.headers:type_name -> openapi.v3.HeadersOrReferences
	28,  // 125: openapi.v3.Response.content:type_name -> openapi.v3.MediaTypes
	26,  // 126: openapi.v3.Response.links:type_name -> openapi.v3.LinksOrReferences
	29,  // 127: openapi.v3.Response.specification_extension:type_name -> openapi.v3.NamedAny
	59,  // 128: openapi.v3.ResponseOrReference.response:type_name -> openapi.v3.Response
	55,  // 129: openapi.v3.ResponseOrReference.reference:type_name -> openapi.v3.Reference
	60,  // 130: openapi.v3.Responses.default:type_name -> openapi.v3.ResponseOrReference
	39,  // 131: openapi.v3.Responses.response_or_reference:type_name -> openapi.v3.NamedResponseOrReference
	29,  // 132: openapi.v3.Responses.specification_extension:type_name -> openapi.v3.NamedAny
	39,  // 133: openapi.v3.ResponsesOrReferences.additional_properties:type_name -> openapi.v3.NamedResponseOrReference
	9,   // 134: openapi.v3.Schema.discriminator:type_name -> openapi.v3.Discriminator
	77,  // 135: openapi.v3.Schema.xml:type_name -> openapi.v3.Xml
	17,  // 136: openapi.v3.Schema.external_docs:type_name -> openapi.v3.ExternalDocs
	1,   // 137: openapi.v3.Schema.example:type_name -> openapi.v3.Any
	1,   // 138: openapi.v3.Schema.enum:type_name -> openapi.v3.Any
	64,  // 139: openapi.v3.Schema.all_of:type_name -> openapi.v3.SchemaOrReference
	64,  // 140: openapi.v3.Schema.one_of:type_name -> openapi.v3.SchemaOrReference
	64,  // 141: openapi.v3.Schema.any_of:type_name -> openapi.v3.SchemaOrReference
	63,  // 142: openapi.v3.Schema.not:type_name -> openapi.v3.Schema
	22,  // 143: openapi.v3.Schema.items:type_name -> openapi.v3.ItemsItem
	54,  // 144: openapi.v3.Schema.properties:type_name -> openapi.v3.Properties
	0,   // 145: openapi.v3.Schema.additional_properties:type_name -> openapi.v3.AdditionalPropertiesItem
	8,   // 146: openapi.v3.Schema.default:type_name -> openapi.v3.DefaultType
	29,  // 147: openapi.v3.Schema.specification_extension:type_name -> openapi.v3.NamedAny
	63,  // 148: openapi.v3.SchemaOrReference.schema:type_name -> openapi.v3.Schema
	55,  // 149: openapi.v3.SchemaOrReference.reference:type_name -> openapi.v3.Reference
	40,  // 150: openapi.v3.SchemasOrReferences.additional_properties:type_name -> openapi.v3.NamedSchemaOrReference
	44,  // 151: openapi.v3.SecurityRequirement.additional_properties:type_name -> openapi.v3.NamedStringArray
	46,  // 152: openapi.v3.SecurityScheme.flows:type_name -> openapi.v3.OauthFlows
	29,  // 153: openapi.v3.SecurityScheme.specification_extension:type_name -> openapi.v3.NamedAny
	67,  // 154: openapi.v3.SecuritySchemeOrReference.security_scheme:type_name -> openapi.v3.SecurityScheme
	55,  // 155: openapi.v3.SecuritySchemeOrReference.reference:type_name -> openapi.v3.Reference
	41,  // 156: openapi.v3.SecuritySchemesOrReferences.additional_properties:type_name -> openapi.v3.NamedSecuritySchemeOrReference
	72,  // 157: openapi.v3.Server.variables:type_name -> openapi.v3.ServerVariables
	29,  // 158: openapi.v3.Server.specification_extension:type_name -> openapi.v3.NamedAny
	29,  // 159: openapi.v3.ServerVariable.specification_extension:type_name -> openapi.v3.NamedAny
	42,  // 160: openapi.v3.ServerVariables.additional_properties:type_name -> openapi.v3.NamedServerVariable
	43,  // 161: openapi.v3.Strings.additional_properties:type_name -> openapi.v3.NamedString
	17,  // 162: openapi.v3.Tag.external_docs:type_name -> openapi.v3.ExternalDocs
	29,  // 163: openapi.v3.Tag.specification_extension:type_name -> openapi.v3.NamedAny
	29,  // 164: openapi.v3.Xml.specification_extension:type_name -> openapi.v3.NamedAny
	165, // [165:165] is the sub-list for method output_type
	165, // [165:165] is the sub-list for method input_type
	165, // [165:165] is the sub-list for extension type_name
	165, // [165:165] is the sub-list for extension extendee
	0,   // [0:165] is the sub-list for field type_name
}

func init() { file_openapiv3_OpenAPIv3_proto_init() }
func file_openapiv3_OpenAPIv3_proto_init() {
	if File_openapiv3_OpenAPIv3_proto != nil {
		return
	}
	file_openapiv3_OpenAPIv3_proto_msgTypes[0].OneofWrappers = []any{
		(*AdditionalPropertiesItem_SchemaOrReference)(nil),
		(*AdditionalPropertiesItem_Boolean)(nil),
	}
	file_openapiv3_OpenAPIv3_proto_msgTypes[2].OneofWrappers = []any{
		(*AnyOrExpression_Any)(nil),
		(*AnyOrExpression_Expression)(nil),
	}
	file_openapiv3_OpenAPIv3_proto_msgTypes[4].OneofWrappers = []any{
		(*CallbackOrReference_Callback)(nil),
		(*CallbackOrReference_Reference)(nil),
	}
	file_openapiv3_OpenAPIv3_proto_msgTypes[8].OneofWrappers = []any{
		(*DefaultType_Number)(nil),
		(*DefaultType_Boolean)(nil),
		(*DefaultType_String_)(nil),
	}
	file_openapiv3_OpenAPIv3_proto_msgTypes[14].OneofWrappers = []any{
		(*ExampleOrReference_Example)(nil),
		(*ExampleOrReference_Reference)(nil),
	}
	file_openapiv3_OpenAPIv3_proto_msgTypes[19].OneofWrappers = []any{
		(*HeaderOrReference_Header)(nil),
		(*HeaderOrReference_Reference)(nil),
	}
	file_openapiv3_OpenAPIv3_proto_msgTypes[25].OneofWrappers = []any{
		(*LinkOrReference_Link)(nil),
		(*LinkOrReference_Reference)(nil),
	}
	file_openapiv3_OpenAPIv3_proto_msgTypes[50].OneofWrappers = []any{
		(*ParameterOrReference_Parameter)(nil),
		(*ParameterOrReference_Reference)(nil),
	}
	file_openapiv3_OpenAPIv3_proto_msgTypes[58].OneofWrappers = []any{
		(*RequestBodyOrReference_RequestBody)(nil),
		(*RequestBodyOrReference_Reference)(nil),
	}
	file_openapiv3_OpenAPIv3_proto_msgTypes[60].OneofWrappers = []any{
		(*ResponseOrReference_Response)(nil),
		(*ResponseOrReference_Reference)(nil),
	}
	file_openapiv3_OpenAPIv3_proto_msgTypes[64].OneofWrappers = []any{
		(*SchemaOrReference_Schema)(nil),
		(*SchemaOrReference_Reference)(nil),
	}
	file_openapiv3_OpenAPIv3_proto_msgTypes[68].OneofWrappers = []any{
		(*SecuritySchemeOrReference_SecurityScheme)(nil),
		(*SecuritySchemeOrReference_Reference)(nil),
	}
	file_openapiv3_OpenAPIv3_proto_msgTypes[73].OneofWrappers = []any{
		(*SpecificationExtension_Number)(nil),
		(*SpecificationExtension_Boolean)(nil),
		(*SpecificationExtension_String_)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_openapiv3_OpenAPIv3_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   78,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_openapiv3_OpenAPIv3_proto_goTypes,
		DependencyIndexes: file_openapiv3_OpenAPIv3_proto_depIdxs,
		MessageInfos:      file_openapiv3_OpenAPIv3_proto_msgTypes,
	}.Build()
	File_openapiv3_OpenAPIv3_proto = out.File
	file_openapiv3_OpenAPIv3_proto_rawDesc = nil
	file_openapiv3_OpenAPIv3_proto_goTypes = nil
	file_openapiv3_OpenAPIv3_proto_depIdxs = nil
}
