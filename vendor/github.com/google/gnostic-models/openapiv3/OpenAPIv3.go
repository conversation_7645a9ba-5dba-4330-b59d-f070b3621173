// Copyright 2020 Google LLC. All Rights Reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//    http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// THIS FILE IS AUTOMATICALLY GENERATED.

package openapi_v3

import (
	"fmt"
	"regexp"
	"strings"

	"gopkg.in/yaml.v3"

	"github.com/google/gnostic-models/compiler"
)

// Version returns the package name (and OpenAPI version).
func Version() string {
	return "openapi_v3"
}

// NewAdditionalPropertiesItem creates an object of type AdditionalPropertiesItem if possible, returning an error if not.
func NewAdditionalPropertiesItem(in *yaml.Node, context *compiler.Context) (*AdditionalPropertiesItem, error) {
	errors := make([]error, 0)
	x := &AdditionalPropertiesItem{}
	matched := false
	// SchemaOrReference schema_or_reference = 1;
	{
		m, ok := compiler.UnpackMap(in)
		if ok {
			// errors might be ok here, they mean we just don't have the right subtype
			t, matchingError := NewSchemaOrReference(m, compiler.NewContext("schemaOrReference", m, context))
			if matchingError == nil {
				x.Oneof = &AdditionalPropertiesItem_SchemaOrReference{SchemaOrReference: t}
				matched = true
			} else {
				errors = append(errors, matchingError)
			}
		}
	}
	// bool boolean = 2;
	boolValue, ok := compiler.BoolForScalarNode(in)
	if ok {
		x.Oneof = &AdditionalPropertiesItem_Boolean{Boolean: boolValue}
		matched = true
	}
	if matched {
		// since the oneof matched one of its possibilities, discard any matching errors
		errors = make([]error, 0)
	} else {
		message := fmt.Sprintf("contains an invalid AdditionalPropertiesItem")
		err := compiler.NewError(context, message)
		errors = []error{err}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewAny creates an object of type Any if possible, returning an error if not.
func NewAny(in *yaml.Node, context *compiler.Context) (*Any, error) {
	errors := make([]error, 0)
	x := &Any{}
	bytes := compiler.Marshal(in)
	x.Yaml = string(bytes)
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewAnyOrExpression creates an object of type AnyOrExpression if possible, returning an error if not.
func NewAnyOrExpression(in *yaml.Node, context *compiler.Context) (*AnyOrExpression, error) {
	errors := make([]error, 0)
	x := &AnyOrExpression{}
	matched := false
	// Any any = 1;
	{
		m, ok := compiler.UnpackMap(in)
		if ok {
			// errors might be ok here, they mean we just don't have the right subtype
			t, matchingError := NewAny(m, compiler.NewContext("any", m, context))
			if matchingError == nil {
				x.Oneof = &AnyOrExpression_Any{Any: t}
				matched = true
			} else {
				errors = append(errors, matchingError)
			}
		}
	}
	// Expression expression = 2;
	{
		m, ok := compiler.UnpackMap(in)
		if ok {
			// errors might be ok here, they mean we just don't have the right subtype
			t, matchingError := NewExpression(m, compiler.NewContext("expression", m, context))
			if matchingError == nil {
				x.Oneof = &AnyOrExpression_Expression{Expression: t}
				matched = true
			} else {
				errors = append(errors, matchingError)
			}
		}
	}
	if matched {
		// since the oneof matched one of its possibilities, discard any matching errors
		errors = make([]error, 0)
	} else {
		message := fmt.Sprintf("contains an invalid AnyOrExpression")
		err := compiler.NewError(context, message)
		errors = []error{err}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewCallback creates an object of type Callback if possible, returning an error if not.
func NewCallback(in *yaml.Node, context *compiler.Context) (*Callback, error) {
	errors := make([]error, 0)
	x := &Callback{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		allowedKeys := []string{}
		allowedPatterns := []*regexp.Regexp{pattern0, pattern1}
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// repeated NamedPathItem path = 1;
		// MAP: PathItem ^
		x.Path = make([]*NamedPathItem, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				if true {
					pair := &NamedPathItem{}
					pair.Name = k
					var err error
					pair.Value, err = NewPathItem(v, compiler.NewContext(k, v, context))
					if err != nil {
						errors = append(errors, err)
					}
					x.Path = append(x.Path, pair)
				}
			}
		}
		// repeated NamedAny specification_extension = 2;
		// MAP: Any ^x-
		x.SpecificationExtension = make([]*NamedAny, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				if strings.HasPrefix(k, "x-") {
					pair := &NamedAny{}
					pair.Name = k
					result := &Any{}
					handled, resultFromExt, err := compiler.CallExtension(context, v, k)
					if handled {
						if err != nil {
							errors = append(errors, err)
						} else {
							bytes := compiler.Marshal(v)
							result.Yaml = string(bytes)
							result.Value = resultFromExt
							pair.Value = result
						}
					} else {
						pair.Value, err = NewAny(v, compiler.NewContext(k, v, context))
						if err != nil {
							errors = append(errors, err)
						}
					}
					x.SpecificationExtension = append(x.SpecificationExtension, pair)
				}
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewCallbackOrReference creates an object of type CallbackOrReference if possible, returning an error if not.
func NewCallbackOrReference(in *yaml.Node, context *compiler.Context) (*CallbackOrReference, error) {
	errors := make([]error, 0)
	x := &CallbackOrReference{}
	matched := false
	// Callback callback = 1;
	{
		m, ok := compiler.UnpackMap(in)
		if ok {
			// errors might be ok here, they mean we just don't have the right subtype
			t, matchingError := NewCallback(m, compiler.NewContext("callback", m, context))
			if matchingError == nil {
				x.Oneof = &CallbackOrReference_Callback{Callback: t}
				matched = true
			} else {
				errors = append(errors, matchingError)
			}
		}
	}
	// Reference reference = 2;
	{
		m, ok := compiler.UnpackMap(in)
		if ok {
			// errors might be ok here, they mean we just don't have the right subtype
			t, matchingError := NewReference(m, compiler.NewContext("reference", m, context))
			if matchingError == nil {
				x.Oneof = &CallbackOrReference_Reference{Reference: t}
				matched = true
			} else {
				errors = append(errors, matchingError)
			}
		}
	}
	if matched {
		// since the oneof matched one of its possibilities, discard any matching errors
		errors = make([]error, 0)
	} else {
		message := fmt.Sprintf("contains an invalid CallbackOrReference")
		err := compiler.NewError(context, message)
		errors = []error{err}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewCallbacksOrReferences creates an object of type CallbacksOrReferences if possible, returning an error if not.
func NewCallbacksOrReferences(in *yaml.Node, context *compiler.Context) (*CallbacksOrReferences, error) {
	errors := make([]error, 0)
	x := &CallbacksOrReferences{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		// repeated NamedCallbackOrReference additional_properties = 1;
		// MAP: CallbackOrReference
		x.AdditionalProperties = make([]*NamedCallbackOrReference, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				pair := &NamedCallbackOrReference{}
				pair.Name = k
				var err error
				pair.Value, err = NewCallbackOrReference(v, compiler.NewContext(k, v, context))
				if err != nil {
					errors = append(errors, err)
				}
				x.AdditionalProperties = append(x.AdditionalProperties, pair)
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewComponents creates an object of type Components if possible, returning an error if not.
func NewComponents(in *yaml.Node, context *compiler.Context) (*Components, error) {
	errors := make([]error, 0)
	x := &Components{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		allowedKeys := []string{"callbacks", "examples", "headers", "links", "parameters", "requestBodies", "responses", "schemas", "securitySchemes"}
		allowedPatterns := []*regexp.Regexp{pattern1}
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// SchemasOrReferences schemas = 1;
		v1 := compiler.MapValueForKey(m, "schemas")
		if v1 != nil {
			var err error
			x.Schemas, err = NewSchemasOrReferences(v1, compiler.NewContext("schemas", v1, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// ResponsesOrReferences responses = 2;
		v2 := compiler.MapValueForKey(m, "responses")
		if v2 != nil {
			var err error
			x.Responses, err = NewResponsesOrReferences(v2, compiler.NewContext("responses", v2, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// ParametersOrReferences parameters = 3;
		v3 := compiler.MapValueForKey(m, "parameters")
		if v3 != nil {
			var err error
			x.Parameters, err = NewParametersOrReferences(v3, compiler.NewContext("parameters", v3, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// ExamplesOrReferences examples = 4;
		v4 := compiler.MapValueForKey(m, "examples")
		if v4 != nil {
			var err error
			x.Examples, err = NewExamplesOrReferences(v4, compiler.NewContext("examples", v4, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// RequestBodiesOrReferences request_bodies = 5;
		v5 := compiler.MapValueForKey(m, "requestBodies")
		if v5 != nil {
			var err error
			x.RequestBodies, err = NewRequestBodiesOrReferences(v5, compiler.NewContext("requestBodies", v5, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// HeadersOrReferences headers = 6;
		v6 := compiler.MapValueForKey(m, "headers")
		if v6 != nil {
			var err error
			x.Headers, err = NewHeadersOrReferences(v6, compiler.NewContext("headers", v6, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// SecuritySchemesOrReferences security_schemes = 7;
		v7 := compiler.MapValueForKey(m, "securitySchemes")
		if v7 != nil {
			var err error
			x.SecuritySchemes, err = NewSecuritySchemesOrReferences(v7, compiler.NewContext("securitySchemes", v7, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// LinksOrReferences links = 8;
		v8 := compiler.MapValueForKey(m, "links")
		if v8 != nil {
			var err error
			x.Links, err = NewLinksOrReferences(v8, compiler.NewContext("links", v8, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// CallbacksOrReferences callbacks = 9;
		v9 := compiler.MapValueForKey(m, "callbacks")
		if v9 != nil {
			var err error
			x.Callbacks, err = NewCallbacksOrReferences(v9, compiler.NewContext("callbacks", v9, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// repeated NamedAny specification_extension = 10;
		// MAP: Any ^x-
		x.SpecificationExtension = make([]*NamedAny, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				if strings.HasPrefix(k, "x-") {
					pair := &NamedAny{}
					pair.Name = k
					result := &Any{}
					handled, resultFromExt, err := compiler.CallExtension(context, v, k)
					if handled {
						if err != nil {
							errors = append(errors, err)
						} else {
							bytes := compiler.Marshal(v)
							result.Yaml = string(bytes)
							result.Value = resultFromExt
							pair.Value = result
						}
					} else {
						pair.Value, err = NewAny(v, compiler.NewContext(k, v, context))
						if err != nil {
							errors = append(errors, err)
						}
					}
					x.SpecificationExtension = append(x.SpecificationExtension, pair)
				}
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewContact creates an object of type Contact if possible, returning an error if not.
func NewContact(in *yaml.Node, context *compiler.Context) (*Contact, error) {
	errors := make([]error, 0)
	x := &Contact{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		allowedKeys := []string{"email", "name", "url"}
		allowedPatterns := []*regexp.Regexp{pattern1}
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// string name = 1;
		v1 := compiler.MapValueForKey(m, "name")
		if v1 != nil {
			x.Name, ok = compiler.StringForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for name: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string url = 2;
		v2 := compiler.MapValueForKey(m, "url")
		if v2 != nil {
			x.Url, ok = compiler.StringForScalarNode(v2)
			if !ok {
				message := fmt.Sprintf("has unexpected value for url: %s", compiler.Display(v2))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string email = 3;
		v3 := compiler.MapValueForKey(m, "email")
		if v3 != nil {
			x.Email, ok = compiler.StringForScalarNode(v3)
			if !ok {
				message := fmt.Sprintf("has unexpected value for email: %s", compiler.Display(v3))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// repeated NamedAny specification_extension = 4;
		// MAP: Any ^x-
		x.SpecificationExtension = make([]*NamedAny, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				if strings.HasPrefix(k, "x-") {
					pair := &NamedAny{}
					pair.Name = k
					result := &Any{}
					handled, resultFromExt, err := compiler.CallExtension(context, v, k)
					if handled {
						if err != nil {
							errors = append(errors, err)
						} else {
							bytes := compiler.Marshal(v)
							result.Yaml = string(bytes)
							result.Value = resultFromExt
							pair.Value = result
						}
					} else {
						pair.Value, err = NewAny(v, compiler.NewContext(k, v, context))
						if err != nil {
							errors = append(errors, err)
						}
					}
					x.SpecificationExtension = append(x.SpecificationExtension, pair)
				}
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewDefaultType creates an object of type DefaultType if possible, returning an error if not.
func NewDefaultType(in *yaml.Node, context *compiler.Context) (*DefaultType, error) {
	errors := make([]error, 0)
	x := &DefaultType{}
	matched := false
	switch in.Tag {
	case "!!bool":
		var v bool
		v, matched = compiler.BoolForScalarNode(in)
		x.Oneof = &DefaultType_Boolean{Boolean: v}
	case "!!str":
		var v string
		v, matched = compiler.StringForScalarNode(in)
		x.Oneof = &DefaultType_String_{String_: v}
	case "!!float":
		var v float64
		v, matched = compiler.FloatForScalarNode(in)
		x.Oneof = &DefaultType_Number{Number: v}
	case "!!int":
		var v int64
		v, matched = compiler.IntForScalarNode(in)
		x.Oneof = &DefaultType_Number{Number: float64(v)}
	}
	if matched {
		// since the oneof matched one of its possibilities, discard any matching errors
		errors = make([]error, 0)
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewDiscriminator creates an object of type Discriminator if possible, returning an error if not.
func NewDiscriminator(in *yaml.Node, context *compiler.Context) (*Discriminator, error) {
	errors := make([]error, 0)
	x := &Discriminator{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		requiredKeys := []string{"propertyName"}
		missingKeys := compiler.MissingKeysInMap(m, requiredKeys)
		if len(missingKeys) > 0 {
			message := fmt.Sprintf("is missing required %s: %+v", compiler.PluralProperties(len(missingKeys)), strings.Join(missingKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		allowedKeys := []string{"mapping", "propertyName"}
		allowedPatterns := []*regexp.Regexp{pattern1}
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// string property_name = 1;
		v1 := compiler.MapValueForKey(m, "propertyName")
		if v1 != nil {
			x.PropertyName, ok = compiler.StringForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for propertyName: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// Strings mapping = 2;
		v2 := compiler.MapValueForKey(m, "mapping")
		if v2 != nil {
			var err error
			x.Mapping, err = NewStrings(v2, compiler.NewContext("mapping", v2, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// repeated NamedAny specification_extension = 3;
		// MAP: Any ^x-
		x.SpecificationExtension = make([]*NamedAny, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				if strings.HasPrefix(k, "x-") {
					pair := &NamedAny{}
					pair.Name = k
					result := &Any{}
					handled, resultFromExt, err := compiler.CallExtension(context, v, k)
					if handled {
						if err != nil {
							errors = append(errors, err)
						} else {
							bytes := compiler.Marshal(v)
							result.Yaml = string(bytes)
							result.Value = resultFromExt
							pair.Value = result
						}
					} else {
						pair.Value, err = NewAny(v, compiler.NewContext(k, v, context))
						if err != nil {
							errors = append(errors, err)
						}
					}
					x.SpecificationExtension = append(x.SpecificationExtension, pair)
				}
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewDocument creates an object of type Document if possible, returning an error if not.
func NewDocument(in *yaml.Node, context *compiler.Context) (*Document, error) {
	errors := make([]error, 0)
	x := &Document{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		requiredKeys := []string{"info", "openapi", "paths"}
		missingKeys := compiler.MissingKeysInMap(m, requiredKeys)
		if len(missingKeys) > 0 {
			message := fmt.Sprintf("is missing required %s: %+v", compiler.PluralProperties(len(missingKeys)), strings.Join(missingKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		allowedKeys := []string{"components", "externalDocs", "info", "openapi", "paths", "security", "servers", "tags"}
		allowedPatterns := []*regexp.Regexp{pattern1}
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// string openapi = 1;
		v1 := compiler.MapValueForKey(m, "openapi")
		if v1 != nil {
			x.Openapi, ok = compiler.StringForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for openapi: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// Info info = 2;
		v2 := compiler.MapValueForKey(m, "info")
		if v2 != nil {
			var err error
			x.Info, err = NewInfo(v2, compiler.NewContext("info", v2, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// repeated Server servers = 3;
		v3 := compiler.MapValueForKey(m, "servers")
		if v3 != nil {
			// repeated Server
			x.Servers = make([]*Server, 0)
			a, ok := compiler.SequenceNodeForNode(v3)
			if ok {
				for _, item := range a.Content {
					y, err := NewServer(item, compiler.NewContext("servers", item, context))
					if err != nil {
						errors = append(errors, err)
					}
					x.Servers = append(x.Servers, y)
				}
			}
		}
		// Paths paths = 4;
		v4 := compiler.MapValueForKey(m, "paths")
		if v4 != nil {
			var err error
			x.Paths, err = NewPaths(v4, compiler.NewContext("paths", v4, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// Components components = 5;
		v5 := compiler.MapValueForKey(m, "components")
		if v5 != nil {
			var err error
			x.Components, err = NewComponents(v5, compiler.NewContext("components", v5, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// repeated SecurityRequirement security = 6;
		v6 := compiler.MapValueForKey(m, "security")
		if v6 != nil {
			// repeated SecurityRequirement
			x.Security = make([]*SecurityRequirement, 0)
			a, ok := compiler.SequenceNodeForNode(v6)
			if ok {
				for _, item := range a.Content {
					y, err := NewSecurityRequirement(item, compiler.NewContext("security", item, context))
					if err != nil {
						errors = append(errors, err)
					}
					x.Security = append(x.Security, y)
				}
			}
		}
		// repeated Tag tags = 7;
		v7 := compiler.MapValueForKey(m, "tags")
		if v7 != nil {
			// repeated Tag
			x.Tags = make([]*Tag, 0)
			a, ok := compiler.SequenceNodeForNode(v7)
			if ok {
				for _, item := range a.Content {
					y, err := NewTag(item, compiler.NewContext("tags", item, context))
					if err != nil {
						errors = append(errors, err)
					}
					x.Tags = append(x.Tags, y)
				}
			}
		}
		// ExternalDocs external_docs = 8;
		v8 := compiler.MapValueForKey(m, "externalDocs")
		if v8 != nil {
			var err error
			x.ExternalDocs, err = NewExternalDocs(v8, compiler.NewContext("externalDocs", v8, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// repeated NamedAny specification_extension = 9;
		// MAP: Any ^x-
		x.SpecificationExtension = make([]*NamedAny, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				if strings.HasPrefix(k, "x-") {
					pair := &NamedAny{}
					pair.Name = k
					result := &Any{}
					handled, resultFromExt, err := compiler.CallExtension(context, v, k)
					if handled {
						if err != nil {
							errors = append(errors, err)
						} else {
							bytes := compiler.Marshal(v)
							result.Yaml = string(bytes)
							result.Value = resultFromExt
							pair.Value = result
						}
					} else {
						pair.Value, err = NewAny(v, compiler.NewContext(k, v, context))
						if err != nil {
							errors = append(errors, err)
						}
					}
					x.SpecificationExtension = append(x.SpecificationExtension, pair)
				}
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewEncoding creates an object of type Encoding if possible, returning an error if not.
func NewEncoding(in *yaml.Node, context *compiler.Context) (*Encoding, error) {
	errors := make([]error, 0)
	x := &Encoding{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		allowedKeys := []string{"allowReserved", "contentType", "explode", "headers", "style"}
		allowedPatterns := []*regexp.Regexp{pattern1}
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// string content_type = 1;
		v1 := compiler.MapValueForKey(m, "contentType")
		if v1 != nil {
			x.ContentType, ok = compiler.StringForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for contentType: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// HeadersOrReferences headers = 2;
		v2 := compiler.MapValueForKey(m, "headers")
		if v2 != nil {
			var err error
			x.Headers, err = NewHeadersOrReferences(v2, compiler.NewContext("headers", v2, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// string style = 3;
		v3 := compiler.MapValueForKey(m, "style")
		if v3 != nil {
			x.Style, ok = compiler.StringForScalarNode(v3)
			if !ok {
				message := fmt.Sprintf("has unexpected value for style: %s", compiler.Display(v3))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// bool explode = 4;
		v4 := compiler.MapValueForKey(m, "explode")
		if v4 != nil {
			x.Explode, ok = compiler.BoolForScalarNode(v4)
			if !ok {
				message := fmt.Sprintf("has unexpected value for explode: %s", compiler.Display(v4))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// bool allow_reserved = 5;
		v5 := compiler.MapValueForKey(m, "allowReserved")
		if v5 != nil {
			x.AllowReserved, ok = compiler.BoolForScalarNode(v5)
			if !ok {
				message := fmt.Sprintf("has unexpected value for allowReserved: %s", compiler.Display(v5))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// repeated NamedAny specification_extension = 6;
		// MAP: Any ^x-
		x.SpecificationExtension = make([]*NamedAny, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				if strings.HasPrefix(k, "x-") {
					pair := &NamedAny{}
					pair.Name = k
					result := &Any{}
					handled, resultFromExt, err := compiler.CallExtension(context, v, k)
					if handled {
						if err != nil {
							errors = append(errors, err)
						} else {
							bytes := compiler.Marshal(v)
							result.Yaml = string(bytes)
							result.Value = resultFromExt
							pair.Value = result
						}
					} else {
						pair.Value, err = NewAny(v, compiler.NewContext(k, v, context))
						if err != nil {
							errors = append(errors, err)
						}
					}
					x.SpecificationExtension = append(x.SpecificationExtension, pair)
				}
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewEncodings creates an object of type Encodings if possible, returning an error if not.
func NewEncodings(in *yaml.Node, context *compiler.Context) (*Encodings, error) {
	errors := make([]error, 0)
	x := &Encodings{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		// repeated NamedEncoding additional_properties = 1;
		// MAP: Encoding
		x.AdditionalProperties = make([]*NamedEncoding, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				pair := &NamedEncoding{}
				pair.Name = k
				var err error
				pair.Value, err = NewEncoding(v, compiler.NewContext(k, v, context))
				if err != nil {
					errors = append(errors, err)
				}
				x.AdditionalProperties = append(x.AdditionalProperties, pair)
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewExample creates an object of type Example if possible, returning an error if not.
func NewExample(in *yaml.Node, context *compiler.Context) (*Example, error) {
	errors := make([]error, 0)
	x := &Example{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		allowedKeys := []string{"description", "externalValue", "summary", "value"}
		allowedPatterns := []*regexp.Regexp{pattern1}
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// string summary = 1;
		v1 := compiler.MapValueForKey(m, "summary")
		if v1 != nil {
			x.Summary, ok = compiler.StringForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for summary: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string description = 2;
		v2 := compiler.MapValueForKey(m, "description")
		if v2 != nil {
			x.Description, ok = compiler.StringForScalarNode(v2)
			if !ok {
				message := fmt.Sprintf("has unexpected value for description: %s", compiler.Display(v2))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// Any value = 3;
		v3 := compiler.MapValueForKey(m, "value")
		if v3 != nil {
			var err error
			x.Value, err = NewAny(v3, compiler.NewContext("value", v3, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// string external_value = 4;
		v4 := compiler.MapValueForKey(m, "externalValue")
		if v4 != nil {
			x.ExternalValue, ok = compiler.StringForScalarNode(v4)
			if !ok {
				message := fmt.Sprintf("has unexpected value for externalValue: %s", compiler.Display(v4))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// repeated NamedAny specification_extension = 5;
		// MAP: Any ^x-
		x.SpecificationExtension = make([]*NamedAny, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				if strings.HasPrefix(k, "x-") {
					pair := &NamedAny{}
					pair.Name = k
					result := &Any{}
					handled, resultFromExt, err := compiler.CallExtension(context, v, k)
					if handled {
						if err != nil {
							errors = append(errors, err)
						} else {
							bytes := compiler.Marshal(v)
							result.Yaml = string(bytes)
							result.Value = resultFromExt
							pair.Value = result
						}
					} else {
						pair.Value, err = NewAny(v, compiler.NewContext(k, v, context))
						if err != nil {
							errors = append(errors, err)
						}
					}
					x.SpecificationExtension = append(x.SpecificationExtension, pair)
				}
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewExampleOrReference creates an object of type ExampleOrReference if possible, returning an error if not.
func NewExampleOrReference(in *yaml.Node, context *compiler.Context) (*ExampleOrReference, error) {
	errors := make([]error, 0)
	x := &ExampleOrReference{}
	matched := false
	// Example example = 1;
	{
		m, ok := compiler.UnpackMap(in)
		if ok {
			// errors might be ok here, they mean we just don't have the right subtype
			t, matchingError := NewExample(m, compiler.NewContext("example", m, context))
			if matchingError == nil {
				x.Oneof = &ExampleOrReference_Example{Example: t}
				matched = true
			} else {
				errors = append(errors, matchingError)
			}
		}
	}
	// Reference reference = 2;
	{
		m, ok := compiler.UnpackMap(in)
		if ok {
			// errors might be ok here, they mean we just don't have the right subtype
			t, matchingError := NewReference(m, compiler.NewContext("reference", m, context))
			if matchingError == nil {
				x.Oneof = &ExampleOrReference_Reference{Reference: t}
				matched = true
			} else {
				errors = append(errors, matchingError)
			}
		}
	}
	if matched {
		// since the oneof matched one of its possibilities, discard any matching errors
		errors = make([]error, 0)
	} else {
		message := fmt.Sprintf("contains an invalid ExampleOrReference")
		err := compiler.NewError(context, message)
		errors = []error{err}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewExamplesOrReferences creates an object of type ExamplesOrReferences if possible, returning an error if not.
func NewExamplesOrReferences(in *yaml.Node, context *compiler.Context) (*ExamplesOrReferences, error) {
	errors := make([]error, 0)
	x := &ExamplesOrReferences{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		// repeated NamedExampleOrReference additional_properties = 1;
		// MAP: ExampleOrReference
		x.AdditionalProperties = make([]*NamedExampleOrReference, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				pair := &NamedExampleOrReference{}
				pair.Name = k
				var err error
				pair.Value, err = NewExampleOrReference(v, compiler.NewContext(k, v, context))
				if err != nil {
					errors = append(errors, err)
				}
				x.AdditionalProperties = append(x.AdditionalProperties, pair)
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewExpression creates an object of type Expression if possible, returning an error if not.
func NewExpression(in *yaml.Node, context *compiler.Context) (*Expression, error) {
	errors := make([]error, 0)
	x := &Expression{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		// repeated NamedAny additional_properties = 1;
		// MAP: Any
		x.AdditionalProperties = make([]*NamedAny, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				pair := &NamedAny{}
				pair.Name = k
				result := &Any{}
				handled, resultFromExt, err := compiler.CallExtension(context, v, k)
				if handled {
					if err != nil {
						errors = append(errors, err)
					} else {
						bytes := compiler.Marshal(v)
						result.Yaml = string(bytes)
						result.Value = resultFromExt
						pair.Value = result
					}
				} else {
					pair.Value, err = NewAny(v, compiler.NewContext(k, v, context))
					if err != nil {
						errors = append(errors, err)
					}
				}
				x.AdditionalProperties = append(x.AdditionalProperties, pair)
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewExternalDocs creates an object of type ExternalDocs if possible, returning an error if not.
func NewExternalDocs(in *yaml.Node, context *compiler.Context) (*ExternalDocs, error) {
	errors := make([]error, 0)
	x := &ExternalDocs{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		requiredKeys := []string{"url"}
		missingKeys := compiler.MissingKeysInMap(m, requiredKeys)
		if len(missingKeys) > 0 {
			message := fmt.Sprintf("is missing required %s: %+v", compiler.PluralProperties(len(missingKeys)), strings.Join(missingKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		allowedKeys := []string{"description", "url"}
		allowedPatterns := []*regexp.Regexp{pattern1}
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// string description = 1;
		v1 := compiler.MapValueForKey(m, "description")
		if v1 != nil {
			x.Description, ok = compiler.StringForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for description: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string url = 2;
		v2 := compiler.MapValueForKey(m, "url")
		if v2 != nil {
			x.Url, ok = compiler.StringForScalarNode(v2)
			if !ok {
				message := fmt.Sprintf("has unexpected value for url: %s", compiler.Display(v2))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// repeated NamedAny specification_extension = 3;
		// MAP: Any ^x-
		x.SpecificationExtension = make([]*NamedAny, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				if strings.HasPrefix(k, "x-") {
					pair := &NamedAny{}
					pair.Name = k
					result := &Any{}
					handled, resultFromExt, err := compiler.CallExtension(context, v, k)
					if handled {
						if err != nil {
							errors = append(errors, err)
						} else {
							bytes := compiler.Marshal(v)
							result.Yaml = string(bytes)
							result.Value = resultFromExt
							pair.Value = result
						}
					} else {
						pair.Value, err = NewAny(v, compiler.NewContext(k, v, context))
						if err != nil {
							errors = append(errors, err)
						}
					}
					x.SpecificationExtension = append(x.SpecificationExtension, pair)
				}
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewHeader creates an object of type Header if possible, returning an error if not.
func NewHeader(in *yaml.Node, context *compiler.Context) (*Header, error) {
	errors := make([]error, 0)
	x := &Header{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		allowedKeys := []string{"allowEmptyValue", "allowReserved", "content", "deprecated", "description", "example", "examples", "explode", "required", "schema", "style"}
		allowedPatterns := []*regexp.Regexp{pattern1}
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// string description = 1;
		v1 := compiler.MapValueForKey(m, "description")
		if v1 != nil {
			x.Description, ok = compiler.StringForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for description: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// bool required = 2;
		v2 := compiler.MapValueForKey(m, "required")
		if v2 != nil {
			x.Required, ok = compiler.BoolForScalarNode(v2)
			if !ok {
				message := fmt.Sprintf("has unexpected value for required: %s", compiler.Display(v2))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// bool deprecated = 3;
		v3 := compiler.MapValueForKey(m, "deprecated")
		if v3 != nil {
			x.Deprecated, ok = compiler.BoolForScalarNode(v3)
			if !ok {
				message := fmt.Sprintf("has unexpected value for deprecated: %s", compiler.Display(v3))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// bool allow_empty_value = 4;
		v4 := compiler.MapValueForKey(m, "allowEmptyValue")
		if v4 != nil {
			x.AllowEmptyValue, ok = compiler.BoolForScalarNode(v4)
			if !ok {
				message := fmt.Sprintf("has unexpected value for allowEmptyValue: %s", compiler.Display(v4))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string style = 5;
		v5 := compiler.MapValueForKey(m, "style")
		if v5 != nil {
			x.Style, ok = compiler.StringForScalarNode(v5)
			if !ok {
				message := fmt.Sprintf("has unexpected value for style: %s", compiler.Display(v5))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// bool explode = 6;
		v6 := compiler.MapValueForKey(m, "explode")
		if v6 != nil {
			x.Explode, ok = compiler.BoolForScalarNode(v6)
			if !ok {
				message := fmt.Sprintf("has unexpected value for explode: %s", compiler.Display(v6))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// bool allow_reserved = 7;
		v7 := compiler.MapValueForKey(m, "allowReserved")
		if v7 != nil {
			x.AllowReserved, ok = compiler.BoolForScalarNode(v7)
			if !ok {
				message := fmt.Sprintf("has unexpected value for allowReserved: %s", compiler.Display(v7))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// SchemaOrReference schema = 8;
		v8 := compiler.MapValueForKey(m, "schema")
		if v8 != nil {
			var err error
			x.Schema, err = NewSchemaOrReference(v8, compiler.NewContext("schema", v8, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// Any example = 9;
		v9 := compiler.MapValueForKey(m, "example")
		if v9 != nil {
			var err error
			x.Example, err = NewAny(v9, compiler.NewContext("example", v9, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// ExamplesOrReferences examples = 10;
		v10 := compiler.MapValueForKey(m, "examples")
		if v10 != nil {
			var err error
			x.Examples, err = NewExamplesOrReferences(v10, compiler.NewContext("examples", v10, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// MediaTypes content = 11;
		v11 := compiler.MapValueForKey(m, "content")
		if v11 != nil {
			var err error
			x.Content, err = NewMediaTypes(v11, compiler.NewContext("content", v11, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// repeated NamedAny specification_extension = 12;
		// MAP: Any ^x-
		x.SpecificationExtension = make([]*NamedAny, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				if strings.HasPrefix(k, "x-") {
					pair := &NamedAny{}
					pair.Name = k
					result := &Any{}
					handled, resultFromExt, err := compiler.CallExtension(context, v, k)
					if handled {
						if err != nil {
							errors = append(errors, err)
						} else {
							bytes := compiler.Marshal(v)
							result.Yaml = string(bytes)
							result.Value = resultFromExt
							pair.Value = result
						}
					} else {
						pair.Value, err = NewAny(v, compiler.NewContext(k, v, context))
						if err != nil {
							errors = append(errors, err)
						}
					}
					x.SpecificationExtension = append(x.SpecificationExtension, pair)
				}
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewHeaderOrReference creates an object of type HeaderOrReference if possible, returning an error if not.
func NewHeaderOrReference(in *yaml.Node, context *compiler.Context) (*HeaderOrReference, error) {
	errors := make([]error, 0)
	x := &HeaderOrReference{}
	matched := false
	// Header header = 1;
	{
		m, ok := compiler.UnpackMap(in)
		if ok {
			// errors might be ok here, they mean we just don't have the right subtype
			t, matchingError := NewHeader(m, compiler.NewContext("header", m, context))
			if matchingError == nil {
				x.Oneof = &HeaderOrReference_Header{Header: t}
				matched = true
			} else {
				errors = append(errors, matchingError)
			}
		}
	}
	// Reference reference = 2;
	{
		m, ok := compiler.UnpackMap(in)
		if ok {
			// errors might be ok here, they mean we just don't have the right subtype
			t, matchingError := NewReference(m, compiler.NewContext("reference", m, context))
			if matchingError == nil {
				x.Oneof = &HeaderOrReference_Reference{Reference: t}
				matched = true
			} else {
				errors = append(errors, matchingError)
			}
		}
	}
	if matched {
		// since the oneof matched one of its possibilities, discard any matching errors
		errors = make([]error, 0)
	} else {
		message := fmt.Sprintf("contains an invalid HeaderOrReference")
		err := compiler.NewError(context, message)
		errors = []error{err}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewHeadersOrReferences creates an object of type HeadersOrReferences if possible, returning an error if not.
func NewHeadersOrReferences(in *yaml.Node, context *compiler.Context) (*HeadersOrReferences, error) {
	errors := make([]error, 0)
	x := &HeadersOrReferences{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		// repeated NamedHeaderOrReference additional_properties = 1;
		// MAP: HeaderOrReference
		x.AdditionalProperties = make([]*NamedHeaderOrReference, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				pair := &NamedHeaderOrReference{}
				pair.Name = k
				var err error
				pair.Value, err = NewHeaderOrReference(v, compiler.NewContext(k, v, context))
				if err != nil {
					errors = append(errors, err)
				}
				x.AdditionalProperties = append(x.AdditionalProperties, pair)
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewInfo creates an object of type Info if possible, returning an error if not.
func NewInfo(in *yaml.Node, context *compiler.Context) (*Info, error) {
	errors := make([]error, 0)
	x := &Info{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		requiredKeys := []string{"title", "version"}
		missingKeys := compiler.MissingKeysInMap(m, requiredKeys)
		if len(missingKeys) > 0 {
			message := fmt.Sprintf("is missing required %s: %+v", compiler.PluralProperties(len(missingKeys)), strings.Join(missingKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		allowedKeys := []string{"contact", "description", "license", "summary", "termsOfService", "title", "version"}
		allowedPatterns := []*regexp.Regexp{pattern1}
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// string title = 1;
		v1 := compiler.MapValueForKey(m, "title")
		if v1 != nil {
			x.Title, ok = compiler.StringForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for title: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string description = 2;
		v2 := compiler.MapValueForKey(m, "description")
		if v2 != nil {
			x.Description, ok = compiler.StringForScalarNode(v2)
			if !ok {
				message := fmt.Sprintf("has unexpected value for description: %s", compiler.Display(v2))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string terms_of_service = 3;
		v3 := compiler.MapValueForKey(m, "termsOfService")
		if v3 != nil {
			x.TermsOfService, ok = compiler.StringForScalarNode(v3)
			if !ok {
				message := fmt.Sprintf("has unexpected value for termsOfService: %s", compiler.Display(v3))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// Contact contact = 4;
		v4 := compiler.MapValueForKey(m, "contact")
		if v4 != nil {
			var err error
			x.Contact, err = NewContact(v4, compiler.NewContext("contact", v4, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// License license = 5;
		v5 := compiler.MapValueForKey(m, "license")
		if v5 != nil {
			var err error
			x.License, err = NewLicense(v5, compiler.NewContext("license", v5, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// string version = 6;
		v6 := compiler.MapValueForKey(m, "version")
		if v6 != nil {
			x.Version, ok = compiler.StringForScalarNode(v6)
			if !ok {
				message := fmt.Sprintf("has unexpected value for version: %s", compiler.Display(v6))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string summary = 7;
		v7 := compiler.MapValueForKey(m, "summary")
		if v7 != nil {
			x.Summary, ok = compiler.StringForScalarNode(v7)
			if !ok {
				message := fmt.Sprintf("has unexpected value for summary: %s", compiler.Display(v7))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// repeated NamedAny specification_extension = 8;
		// MAP: Any ^x-
		x.SpecificationExtension = make([]*NamedAny, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				if strings.HasPrefix(k, "x-") {
					pair := &NamedAny{}
					pair.Name = k
					result := &Any{}
					handled, resultFromExt, err := compiler.CallExtension(context, v, k)
					if handled {
						if err != nil {
							errors = append(errors, err)
						} else {
							bytes := compiler.Marshal(v)
							result.Yaml = string(bytes)
							result.Value = resultFromExt
							pair.Value = result
						}
					} else {
						pair.Value, err = NewAny(v, compiler.NewContext(k, v, context))
						if err != nil {
							errors = append(errors, err)
						}
					}
					x.SpecificationExtension = append(x.SpecificationExtension, pair)
				}
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewItemsItem creates an object of type ItemsItem if possible, returning an error if not.
func NewItemsItem(in *yaml.Node, context *compiler.Context) (*ItemsItem, error) {
	errors := make([]error, 0)
	x := &ItemsItem{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value for item array: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		x.SchemaOrReference = make([]*SchemaOrReference, 0)
		y, err := NewSchemaOrReference(m, compiler.NewContext("<array>", m, context))
		if err != nil {
			return nil, err
		}
		x.SchemaOrReference = append(x.SchemaOrReference, y)
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewLicense creates an object of type License if possible, returning an error if not.
func NewLicense(in *yaml.Node, context *compiler.Context) (*License, error) {
	errors := make([]error, 0)
	x := &License{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		requiredKeys := []string{"name"}
		missingKeys := compiler.MissingKeysInMap(m, requiredKeys)
		if len(missingKeys) > 0 {
			message := fmt.Sprintf("is missing required %s: %+v", compiler.PluralProperties(len(missingKeys)), strings.Join(missingKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		allowedKeys := []string{"name", "url"}
		allowedPatterns := []*regexp.Regexp{pattern1}
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// string name = 1;
		v1 := compiler.MapValueForKey(m, "name")
		if v1 != nil {
			x.Name, ok = compiler.StringForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for name: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string url = 2;
		v2 := compiler.MapValueForKey(m, "url")
		if v2 != nil {
			x.Url, ok = compiler.StringForScalarNode(v2)
			if !ok {
				message := fmt.Sprintf("has unexpected value for url: %s", compiler.Display(v2))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// repeated NamedAny specification_extension = 3;
		// MAP: Any ^x-
		x.SpecificationExtension = make([]*NamedAny, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				if strings.HasPrefix(k, "x-") {
					pair := &NamedAny{}
					pair.Name = k
					result := &Any{}
					handled, resultFromExt, err := compiler.CallExtension(context, v, k)
					if handled {
						if err != nil {
							errors = append(errors, err)
						} else {
							bytes := compiler.Marshal(v)
							result.Yaml = string(bytes)
							result.Value = resultFromExt
							pair.Value = result
						}
					} else {
						pair.Value, err = NewAny(v, compiler.NewContext(k, v, context))
						if err != nil {
							errors = append(errors, err)
						}
					}
					x.SpecificationExtension = append(x.SpecificationExtension, pair)
				}
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewLink creates an object of type Link if possible, returning an error if not.
func NewLink(in *yaml.Node, context *compiler.Context) (*Link, error) {
	errors := make([]error, 0)
	x := &Link{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		allowedKeys := []string{"description", "operationId", "operationRef", "parameters", "requestBody", "server"}
		allowedPatterns := []*regexp.Regexp{pattern1}
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// string operation_ref = 1;
		v1 := compiler.MapValueForKey(m, "operationRef")
		if v1 != nil {
			x.OperationRef, ok = compiler.StringForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for operationRef: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string operation_id = 2;
		v2 := compiler.MapValueForKey(m, "operationId")
		if v2 != nil {
			x.OperationId, ok = compiler.StringForScalarNode(v2)
			if !ok {
				message := fmt.Sprintf("has unexpected value for operationId: %s", compiler.Display(v2))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// AnyOrExpression parameters = 3;
		v3 := compiler.MapValueForKey(m, "parameters")
		if v3 != nil {
			var err error
			x.Parameters, err = NewAnyOrExpression(v3, compiler.NewContext("parameters", v3, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// AnyOrExpression request_body = 4;
		v4 := compiler.MapValueForKey(m, "requestBody")
		if v4 != nil {
			var err error
			x.RequestBody, err = NewAnyOrExpression(v4, compiler.NewContext("requestBody", v4, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// string description = 5;
		v5 := compiler.MapValueForKey(m, "description")
		if v5 != nil {
			x.Description, ok = compiler.StringForScalarNode(v5)
			if !ok {
				message := fmt.Sprintf("has unexpected value for description: %s", compiler.Display(v5))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// Server server = 6;
		v6 := compiler.MapValueForKey(m, "server")
		if v6 != nil {
			var err error
			x.Server, err = NewServer(v6, compiler.NewContext("server", v6, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// repeated NamedAny specification_extension = 7;
		// MAP: Any ^x-
		x.SpecificationExtension = make([]*NamedAny, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				if strings.HasPrefix(k, "x-") {
					pair := &NamedAny{}
					pair.Name = k
					result := &Any{}
					handled, resultFromExt, err := compiler.CallExtension(context, v, k)
					if handled {
						if err != nil {
							errors = append(errors, err)
						} else {
							bytes := compiler.Marshal(v)
							result.Yaml = string(bytes)
							result.Value = resultFromExt
							pair.Value = result
						}
					} else {
						pair.Value, err = NewAny(v, compiler.NewContext(k, v, context))
						if err != nil {
							errors = append(errors, err)
						}
					}
					x.SpecificationExtension = append(x.SpecificationExtension, pair)
				}
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewLinkOrReference creates an object of type LinkOrReference if possible, returning an error if not.
func NewLinkOrReference(in *yaml.Node, context *compiler.Context) (*LinkOrReference, error) {
	errors := make([]error, 0)
	x := &LinkOrReference{}
	matched := false
	// Link link = 1;
	{
		m, ok := compiler.UnpackMap(in)
		if ok {
			// errors might be ok here, they mean we just don't have the right subtype
			t, matchingError := NewLink(m, compiler.NewContext("link", m, context))
			if matchingError == nil {
				x.Oneof = &LinkOrReference_Link{Link: t}
				matched = true
			} else {
				errors = append(errors, matchingError)
			}
		}
	}
	// Reference reference = 2;
	{
		m, ok := compiler.UnpackMap(in)
		if ok {
			// errors might be ok here, they mean we just don't have the right subtype
			t, matchingError := NewReference(m, compiler.NewContext("reference", m, context))
			if matchingError == nil {
				x.Oneof = &LinkOrReference_Reference{Reference: t}
				matched = true
			} else {
				errors = append(errors, matchingError)
			}
		}
	}
	if matched {
		// since the oneof matched one of its possibilities, discard any matching errors
		errors = make([]error, 0)
	} else {
		message := fmt.Sprintf("contains an invalid LinkOrReference")
		err := compiler.NewError(context, message)
		errors = []error{err}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewLinksOrReferences creates an object of type LinksOrReferences if possible, returning an error if not.
func NewLinksOrReferences(in *yaml.Node, context *compiler.Context) (*LinksOrReferences, error) {
	errors := make([]error, 0)
	x := &LinksOrReferences{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		// repeated NamedLinkOrReference additional_properties = 1;
		// MAP: LinkOrReference
		x.AdditionalProperties = make([]*NamedLinkOrReference, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				pair := &NamedLinkOrReference{}
				pair.Name = k
				var err error
				pair.Value, err = NewLinkOrReference(v, compiler.NewContext(k, v, context))
				if err != nil {
					errors = append(errors, err)
				}
				x.AdditionalProperties = append(x.AdditionalProperties, pair)
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewMediaType creates an object of type MediaType if possible, returning an error if not.
func NewMediaType(in *yaml.Node, context *compiler.Context) (*MediaType, error) {
	errors := make([]error, 0)
	x := &MediaType{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		allowedKeys := []string{"encoding", "example", "examples", "schema"}
		allowedPatterns := []*regexp.Regexp{pattern1}
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// SchemaOrReference schema = 1;
		v1 := compiler.MapValueForKey(m, "schema")
		if v1 != nil {
			var err error
			x.Schema, err = NewSchemaOrReference(v1, compiler.NewContext("schema", v1, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// Any example = 2;
		v2 := compiler.MapValueForKey(m, "example")
		if v2 != nil {
			var err error
			x.Example, err = NewAny(v2, compiler.NewContext("example", v2, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// ExamplesOrReferences examples = 3;
		v3 := compiler.MapValueForKey(m, "examples")
		if v3 != nil {
			var err error
			x.Examples, err = NewExamplesOrReferences(v3, compiler.NewContext("examples", v3, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// Encodings encoding = 4;
		v4 := compiler.MapValueForKey(m, "encoding")
		if v4 != nil {
			var err error
			x.Encoding, err = NewEncodings(v4, compiler.NewContext("encoding", v4, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// repeated NamedAny specification_extension = 5;
		// MAP: Any ^x-
		x.SpecificationExtension = make([]*NamedAny, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				if strings.HasPrefix(k, "x-") {
					pair := &NamedAny{}
					pair.Name = k
					result := &Any{}
					handled, resultFromExt, err := compiler.CallExtension(context, v, k)
					if handled {
						if err != nil {
							errors = append(errors, err)
						} else {
							bytes := compiler.Marshal(v)
							result.Yaml = string(bytes)
							result.Value = resultFromExt
							pair.Value = result
						}
					} else {
						pair.Value, err = NewAny(v, compiler.NewContext(k, v, context))
						if err != nil {
							errors = append(errors, err)
						}
					}
					x.SpecificationExtension = append(x.SpecificationExtension, pair)
				}
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewMediaTypes creates an object of type MediaTypes if possible, returning an error if not.
func NewMediaTypes(in *yaml.Node, context *compiler.Context) (*MediaTypes, error) {
	errors := make([]error, 0)
	x := &MediaTypes{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		// repeated NamedMediaType additional_properties = 1;
		// MAP: MediaType
		x.AdditionalProperties = make([]*NamedMediaType, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				pair := &NamedMediaType{}
				pair.Name = k
				var err error
				pair.Value, err = NewMediaType(v, compiler.NewContext(k, v, context))
				if err != nil {
					errors = append(errors, err)
				}
				x.AdditionalProperties = append(x.AdditionalProperties, pair)
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewNamedAny creates an object of type NamedAny if possible, returning an error if not.
func NewNamedAny(in *yaml.Node, context *compiler.Context) (*NamedAny, error) {
	errors := make([]error, 0)
	x := &NamedAny{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		allowedKeys := []string{"name", "value"}
		var allowedPatterns []*regexp.Regexp
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// string name = 1;
		v1 := compiler.MapValueForKey(m, "name")
		if v1 != nil {
			x.Name, ok = compiler.StringForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for name: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// Any value = 2;
		v2 := compiler.MapValueForKey(m, "value")
		if v2 != nil {
			var err error
			x.Value, err = NewAny(v2, compiler.NewContext("value", v2, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewNamedCallbackOrReference creates an object of type NamedCallbackOrReference if possible, returning an error if not.
func NewNamedCallbackOrReference(in *yaml.Node, context *compiler.Context) (*NamedCallbackOrReference, error) {
	errors := make([]error, 0)
	x := &NamedCallbackOrReference{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		allowedKeys := []string{"name", "value"}
		var allowedPatterns []*regexp.Regexp
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// string name = 1;
		v1 := compiler.MapValueForKey(m, "name")
		if v1 != nil {
			x.Name, ok = compiler.StringForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for name: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// CallbackOrReference value = 2;
		v2 := compiler.MapValueForKey(m, "value")
		if v2 != nil {
			var err error
			x.Value, err = NewCallbackOrReference(v2, compiler.NewContext("value", v2, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewNamedEncoding creates an object of type NamedEncoding if possible, returning an error if not.
func NewNamedEncoding(in *yaml.Node, context *compiler.Context) (*NamedEncoding, error) {
	errors := make([]error, 0)
	x := &NamedEncoding{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		allowedKeys := []string{"name", "value"}
		var allowedPatterns []*regexp.Regexp
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// string name = 1;
		v1 := compiler.MapValueForKey(m, "name")
		if v1 != nil {
			x.Name, ok = compiler.StringForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for name: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// Encoding value = 2;
		v2 := compiler.MapValueForKey(m, "value")
		if v2 != nil {
			var err error
			x.Value, err = NewEncoding(v2, compiler.NewContext("value", v2, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewNamedExampleOrReference creates an object of type NamedExampleOrReference if possible, returning an error if not.
func NewNamedExampleOrReference(in *yaml.Node, context *compiler.Context) (*NamedExampleOrReference, error) {
	errors := make([]error, 0)
	x := &NamedExampleOrReference{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		allowedKeys := []string{"name", "value"}
		var allowedPatterns []*regexp.Regexp
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// string name = 1;
		v1 := compiler.MapValueForKey(m, "name")
		if v1 != nil {
			x.Name, ok = compiler.StringForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for name: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// ExampleOrReference value = 2;
		v2 := compiler.MapValueForKey(m, "value")
		if v2 != nil {
			var err error
			x.Value, err = NewExampleOrReference(v2, compiler.NewContext("value", v2, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewNamedHeaderOrReference creates an object of type NamedHeaderOrReference if possible, returning an error if not.
func NewNamedHeaderOrReference(in *yaml.Node, context *compiler.Context) (*NamedHeaderOrReference, error) {
	errors := make([]error, 0)
	x := &NamedHeaderOrReference{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		allowedKeys := []string{"name", "value"}
		var allowedPatterns []*regexp.Regexp
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// string name = 1;
		v1 := compiler.MapValueForKey(m, "name")
		if v1 != nil {
			x.Name, ok = compiler.StringForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for name: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// HeaderOrReference value = 2;
		v2 := compiler.MapValueForKey(m, "value")
		if v2 != nil {
			var err error
			x.Value, err = NewHeaderOrReference(v2, compiler.NewContext("value", v2, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewNamedLinkOrReference creates an object of type NamedLinkOrReference if possible, returning an error if not.
func NewNamedLinkOrReference(in *yaml.Node, context *compiler.Context) (*NamedLinkOrReference, error) {
	errors := make([]error, 0)
	x := &NamedLinkOrReference{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		allowedKeys := []string{"name", "value"}
		var allowedPatterns []*regexp.Regexp
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// string name = 1;
		v1 := compiler.MapValueForKey(m, "name")
		if v1 != nil {
			x.Name, ok = compiler.StringForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for name: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// LinkOrReference value = 2;
		v2 := compiler.MapValueForKey(m, "value")
		if v2 != nil {
			var err error
			x.Value, err = NewLinkOrReference(v2, compiler.NewContext("value", v2, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewNamedMediaType creates an object of type NamedMediaType if possible, returning an error if not.
func NewNamedMediaType(in *yaml.Node, context *compiler.Context) (*NamedMediaType, error) {
	errors := make([]error, 0)
	x := &NamedMediaType{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		allowedKeys := []string{"name", "value"}
		var allowedPatterns []*regexp.Regexp
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// string name = 1;
		v1 := compiler.MapValueForKey(m, "name")
		if v1 != nil {
			x.Name, ok = compiler.StringForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for name: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// MediaType value = 2;
		v2 := compiler.MapValueForKey(m, "value")
		if v2 != nil {
			var err error
			x.Value, err = NewMediaType(v2, compiler.NewContext("value", v2, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewNamedParameterOrReference creates an object of type NamedParameterOrReference if possible, returning an error if not.
func NewNamedParameterOrReference(in *yaml.Node, context *compiler.Context) (*NamedParameterOrReference, error) {
	errors := make([]error, 0)
	x := &NamedParameterOrReference{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		allowedKeys := []string{"name", "value"}
		var allowedPatterns []*regexp.Regexp
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// string name = 1;
		v1 := compiler.MapValueForKey(m, "name")
		if v1 != nil {
			x.Name, ok = compiler.StringForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for name: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// ParameterOrReference value = 2;
		v2 := compiler.MapValueForKey(m, "value")
		if v2 != nil {
			var err error
			x.Value, err = NewParameterOrReference(v2, compiler.NewContext("value", v2, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewNamedPathItem creates an object of type NamedPathItem if possible, returning an error if not.
func NewNamedPathItem(in *yaml.Node, context *compiler.Context) (*NamedPathItem, error) {
	errors := make([]error, 0)
	x := &NamedPathItem{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		allowedKeys := []string{"name", "value"}
		var allowedPatterns []*regexp.Regexp
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// string name = 1;
		v1 := compiler.MapValueForKey(m, "name")
		if v1 != nil {
			x.Name, ok = compiler.StringForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for name: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// PathItem value = 2;
		v2 := compiler.MapValueForKey(m, "value")
		if v2 != nil {
			var err error
			x.Value, err = NewPathItem(v2, compiler.NewContext("value", v2, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewNamedRequestBodyOrReference creates an object of type NamedRequestBodyOrReference if possible, returning an error if not.
func NewNamedRequestBodyOrReference(in *yaml.Node, context *compiler.Context) (*NamedRequestBodyOrReference, error) {
	errors := make([]error, 0)
	x := &NamedRequestBodyOrReference{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		allowedKeys := []string{"name", "value"}
		var allowedPatterns []*regexp.Regexp
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// string name = 1;
		v1 := compiler.MapValueForKey(m, "name")
		if v1 != nil {
			x.Name, ok = compiler.StringForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for name: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// RequestBodyOrReference value = 2;
		v2 := compiler.MapValueForKey(m, "value")
		if v2 != nil {
			var err error
			x.Value, err = NewRequestBodyOrReference(v2, compiler.NewContext("value", v2, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewNamedResponseOrReference creates an object of type NamedResponseOrReference if possible, returning an error if not.
func NewNamedResponseOrReference(in *yaml.Node, context *compiler.Context) (*NamedResponseOrReference, error) {
	errors := make([]error, 0)
	x := &NamedResponseOrReference{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		allowedKeys := []string{"name", "value"}
		var allowedPatterns []*regexp.Regexp
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// string name = 1;
		v1 := compiler.MapValueForKey(m, "name")
		if v1 != nil {
			x.Name, ok = compiler.StringForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for name: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// ResponseOrReference value = 2;
		v2 := compiler.MapValueForKey(m, "value")
		if v2 != nil {
			var err error
			x.Value, err = NewResponseOrReference(v2, compiler.NewContext("value", v2, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewNamedSchemaOrReference creates an object of type NamedSchemaOrReference if possible, returning an error if not.
func NewNamedSchemaOrReference(in *yaml.Node, context *compiler.Context) (*NamedSchemaOrReference, error) {
	errors := make([]error, 0)
	x := &NamedSchemaOrReference{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		allowedKeys := []string{"name", "value"}
		var allowedPatterns []*regexp.Regexp
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// string name = 1;
		v1 := compiler.MapValueForKey(m, "name")
		if v1 != nil {
			x.Name, ok = compiler.StringForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for name: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// SchemaOrReference value = 2;
		v2 := compiler.MapValueForKey(m, "value")
		if v2 != nil {
			var err error
			x.Value, err = NewSchemaOrReference(v2, compiler.NewContext("value", v2, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewNamedSecuritySchemeOrReference creates an object of type NamedSecuritySchemeOrReference if possible, returning an error if not.
func NewNamedSecuritySchemeOrReference(in *yaml.Node, context *compiler.Context) (*NamedSecuritySchemeOrReference, error) {
	errors := make([]error, 0)
	x := &NamedSecuritySchemeOrReference{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		allowedKeys := []string{"name", "value"}
		var allowedPatterns []*regexp.Regexp
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// string name = 1;
		v1 := compiler.MapValueForKey(m, "name")
		if v1 != nil {
			x.Name, ok = compiler.StringForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for name: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// SecuritySchemeOrReference value = 2;
		v2 := compiler.MapValueForKey(m, "value")
		if v2 != nil {
			var err error
			x.Value, err = NewSecuritySchemeOrReference(v2, compiler.NewContext("value", v2, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewNamedServerVariable creates an object of type NamedServerVariable if possible, returning an error if not.
func NewNamedServerVariable(in *yaml.Node, context *compiler.Context) (*NamedServerVariable, error) {
	errors := make([]error, 0)
	x := &NamedServerVariable{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		allowedKeys := []string{"name", "value"}
		var allowedPatterns []*regexp.Regexp
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// string name = 1;
		v1 := compiler.MapValueForKey(m, "name")
		if v1 != nil {
			x.Name, ok = compiler.StringForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for name: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// ServerVariable value = 2;
		v2 := compiler.MapValueForKey(m, "value")
		if v2 != nil {
			var err error
			x.Value, err = NewServerVariable(v2, compiler.NewContext("value", v2, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewNamedString creates an object of type NamedString if possible, returning an error if not.
func NewNamedString(in *yaml.Node, context *compiler.Context) (*NamedString, error) {
	errors := make([]error, 0)
	x := &NamedString{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		allowedKeys := []string{"name", "value"}
		var allowedPatterns []*regexp.Regexp
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// string name = 1;
		v1 := compiler.MapValueForKey(m, "name")
		if v1 != nil {
			x.Name, ok = compiler.StringForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for name: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string value = 2;
		v2 := compiler.MapValueForKey(m, "value")
		if v2 != nil {
			x.Value, ok = compiler.StringForScalarNode(v2)
			if !ok {
				message := fmt.Sprintf("has unexpected value for value: %s", compiler.Display(v2))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewNamedStringArray creates an object of type NamedStringArray if possible, returning an error if not.
func NewNamedStringArray(in *yaml.Node, context *compiler.Context) (*NamedStringArray, error) {
	errors := make([]error, 0)
	x := &NamedStringArray{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		allowedKeys := []string{"name", "value"}
		var allowedPatterns []*regexp.Regexp
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// string name = 1;
		v1 := compiler.MapValueForKey(m, "name")
		if v1 != nil {
			x.Name, ok = compiler.StringForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for name: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// StringArray value = 2;
		v2 := compiler.MapValueForKey(m, "value")
		if v2 != nil {
			var err error
			x.Value, err = NewStringArray(v2, compiler.NewContext("value", v2, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewOauthFlow creates an object of type OauthFlow if possible, returning an error if not.
func NewOauthFlow(in *yaml.Node, context *compiler.Context) (*OauthFlow, error) {
	errors := make([]error, 0)
	x := &OauthFlow{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		allowedKeys := []string{"authorizationUrl", "refreshUrl", "scopes", "tokenUrl"}
		allowedPatterns := []*regexp.Regexp{pattern1}
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// string authorization_url = 1;
		v1 := compiler.MapValueForKey(m, "authorizationUrl")
		if v1 != nil {
			x.AuthorizationUrl, ok = compiler.StringForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for authorizationUrl: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string token_url = 2;
		v2 := compiler.MapValueForKey(m, "tokenUrl")
		if v2 != nil {
			x.TokenUrl, ok = compiler.StringForScalarNode(v2)
			if !ok {
				message := fmt.Sprintf("has unexpected value for tokenUrl: %s", compiler.Display(v2))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string refresh_url = 3;
		v3 := compiler.MapValueForKey(m, "refreshUrl")
		if v3 != nil {
			x.RefreshUrl, ok = compiler.StringForScalarNode(v3)
			if !ok {
				message := fmt.Sprintf("has unexpected value for refreshUrl: %s", compiler.Display(v3))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// Strings scopes = 4;
		v4 := compiler.MapValueForKey(m, "scopes")
		if v4 != nil {
			var err error
			x.Scopes, err = NewStrings(v4, compiler.NewContext("scopes", v4, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// repeated NamedAny specification_extension = 5;
		// MAP: Any ^x-
		x.SpecificationExtension = make([]*NamedAny, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				if strings.HasPrefix(k, "x-") {
					pair := &NamedAny{}
					pair.Name = k
					result := &Any{}
					handled, resultFromExt, err := compiler.CallExtension(context, v, k)
					if handled {
						if err != nil {
							errors = append(errors, err)
						} else {
							bytes := compiler.Marshal(v)
							result.Yaml = string(bytes)
							result.Value = resultFromExt
							pair.Value = result
						}
					} else {
						pair.Value, err = NewAny(v, compiler.NewContext(k, v, context))
						if err != nil {
							errors = append(errors, err)
						}
					}
					x.SpecificationExtension = append(x.SpecificationExtension, pair)
				}
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewOauthFlows creates an object of type OauthFlows if possible, returning an error if not.
func NewOauthFlows(in *yaml.Node, context *compiler.Context) (*OauthFlows, error) {
	errors := make([]error, 0)
	x := &OauthFlows{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		allowedKeys := []string{"authorizationCode", "clientCredentials", "implicit", "password"}
		allowedPatterns := []*regexp.Regexp{pattern1}
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// OauthFlow implicit = 1;
		v1 := compiler.MapValueForKey(m, "implicit")
		if v1 != nil {
			var err error
			x.Implicit, err = NewOauthFlow(v1, compiler.NewContext("implicit", v1, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// OauthFlow password = 2;
		v2 := compiler.MapValueForKey(m, "password")
		if v2 != nil {
			var err error
			x.Password, err = NewOauthFlow(v2, compiler.NewContext("password", v2, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// OauthFlow client_credentials = 3;
		v3 := compiler.MapValueForKey(m, "clientCredentials")
		if v3 != nil {
			var err error
			x.ClientCredentials, err = NewOauthFlow(v3, compiler.NewContext("clientCredentials", v3, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// OauthFlow authorization_code = 4;
		v4 := compiler.MapValueForKey(m, "authorizationCode")
		if v4 != nil {
			var err error
			x.AuthorizationCode, err = NewOauthFlow(v4, compiler.NewContext("authorizationCode", v4, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// repeated NamedAny specification_extension = 5;
		// MAP: Any ^x-
		x.SpecificationExtension = make([]*NamedAny, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				if strings.HasPrefix(k, "x-") {
					pair := &NamedAny{}
					pair.Name = k
					result := &Any{}
					handled, resultFromExt, err := compiler.CallExtension(context, v, k)
					if handled {
						if err != nil {
							errors = append(errors, err)
						} else {
							bytes := compiler.Marshal(v)
							result.Yaml = string(bytes)
							result.Value = resultFromExt
							pair.Value = result
						}
					} else {
						pair.Value, err = NewAny(v, compiler.NewContext(k, v, context))
						if err != nil {
							errors = append(errors, err)
						}
					}
					x.SpecificationExtension = append(x.SpecificationExtension, pair)
				}
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewObject creates an object of type Object if possible, returning an error if not.
func NewObject(in *yaml.Node, context *compiler.Context) (*Object, error) {
	errors := make([]error, 0)
	x := &Object{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		// repeated NamedAny additional_properties = 1;
		// MAP: Any
		x.AdditionalProperties = make([]*NamedAny, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				pair := &NamedAny{}
				pair.Name = k
				result := &Any{}
				handled, resultFromExt, err := compiler.CallExtension(context, v, k)
				if handled {
					if err != nil {
						errors = append(errors, err)
					} else {
						bytes := compiler.Marshal(v)
						result.Yaml = string(bytes)
						result.Value = resultFromExt
						pair.Value = result
					}
				} else {
					pair.Value, err = NewAny(v, compiler.NewContext(k, v, context))
					if err != nil {
						errors = append(errors, err)
					}
				}
				x.AdditionalProperties = append(x.AdditionalProperties, pair)
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewOperation creates an object of type Operation if possible, returning an error if not.
func NewOperation(in *yaml.Node, context *compiler.Context) (*Operation, error) {
	errors := make([]error, 0)
	x := &Operation{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		requiredKeys := []string{"responses"}
		missingKeys := compiler.MissingKeysInMap(m, requiredKeys)
		if len(missingKeys) > 0 {
			message := fmt.Sprintf("is missing required %s: %+v", compiler.PluralProperties(len(missingKeys)), strings.Join(missingKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		allowedKeys := []string{"callbacks", "deprecated", "description", "externalDocs", "operationId", "parameters", "requestBody", "responses", "security", "servers", "summary", "tags"}
		allowedPatterns := []*regexp.Regexp{pattern1}
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// repeated string tags = 1;
		v1 := compiler.MapValueForKey(m, "tags")
		if v1 != nil {
			v, ok := compiler.SequenceNodeForNode(v1)
			if ok {
				x.Tags = compiler.StringArrayForSequenceNode(v)
			} else {
				message := fmt.Sprintf("has unexpected value for tags: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string summary = 2;
		v2 := compiler.MapValueForKey(m, "summary")
		if v2 != nil {
			x.Summary, ok = compiler.StringForScalarNode(v2)
			if !ok {
				message := fmt.Sprintf("has unexpected value for summary: %s", compiler.Display(v2))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string description = 3;
		v3 := compiler.MapValueForKey(m, "description")
		if v3 != nil {
			x.Description, ok = compiler.StringForScalarNode(v3)
			if !ok {
				message := fmt.Sprintf("has unexpected value for description: %s", compiler.Display(v3))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// ExternalDocs external_docs = 4;
		v4 := compiler.MapValueForKey(m, "externalDocs")
		if v4 != nil {
			var err error
			x.ExternalDocs, err = NewExternalDocs(v4, compiler.NewContext("externalDocs", v4, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// string operation_id = 5;
		v5 := compiler.MapValueForKey(m, "operationId")
		if v5 != nil {
			x.OperationId, ok = compiler.StringForScalarNode(v5)
			if !ok {
				message := fmt.Sprintf("has unexpected value for operationId: %s", compiler.Display(v5))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// repeated ParameterOrReference parameters = 6;
		v6 := compiler.MapValueForKey(m, "parameters")
		if v6 != nil {
			// repeated ParameterOrReference
			x.Parameters = make([]*ParameterOrReference, 0)
			a, ok := compiler.SequenceNodeForNode(v6)
			if ok {
				for _, item := range a.Content {
					y, err := NewParameterOrReference(item, compiler.NewContext("parameters", item, context))
					if err != nil {
						errors = append(errors, err)
					}
					x.Parameters = append(x.Parameters, y)
				}
			}
		}
		// RequestBodyOrReference request_body = 7;
		v7 := compiler.MapValueForKey(m, "requestBody")
		if v7 != nil {
			var err error
			x.RequestBody, err = NewRequestBodyOrReference(v7, compiler.NewContext("requestBody", v7, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// Responses responses = 8;
		v8 := compiler.MapValueForKey(m, "responses")
		if v8 != nil {
			var err error
			x.Responses, err = NewResponses(v8, compiler.NewContext("responses", v8, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// CallbacksOrReferences callbacks = 9;
		v9 := compiler.MapValueForKey(m, "callbacks")
		if v9 != nil {
			var err error
			x.Callbacks, err = NewCallbacksOrReferences(v9, compiler.NewContext("callbacks", v9, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// bool deprecated = 10;
		v10 := compiler.MapValueForKey(m, "deprecated")
		if v10 != nil {
			x.Deprecated, ok = compiler.BoolForScalarNode(v10)
			if !ok {
				message := fmt.Sprintf("has unexpected value for deprecated: %s", compiler.Display(v10))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// repeated SecurityRequirement security = 11;
		v11 := compiler.MapValueForKey(m, "security")
		if v11 != nil {
			// repeated SecurityRequirement
			x.Security = make([]*SecurityRequirement, 0)
			a, ok := compiler.SequenceNodeForNode(v11)
			if ok {
				for _, item := range a.Content {
					y, err := NewSecurityRequirement(item, compiler.NewContext("security", item, context))
					if err != nil {
						errors = append(errors, err)
					}
					x.Security = append(x.Security, y)
				}
			}
		}
		// repeated Server servers = 12;
		v12 := compiler.MapValueForKey(m, "servers")
		if v12 != nil {
			// repeated Server
			x.Servers = make([]*Server, 0)
			a, ok := compiler.SequenceNodeForNode(v12)
			if ok {
				for _, item := range a.Content {
					y, err := NewServer(item, compiler.NewContext("servers", item, context))
					if err != nil {
						errors = append(errors, err)
					}
					x.Servers = append(x.Servers, y)
				}
			}
		}
		// repeated NamedAny specification_extension = 13;
		// MAP: Any ^x-
		x.SpecificationExtension = make([]*NamedAny, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				if strings.HasPrefix(k, "x-") {
					pair := &NamedAny{}
					pair.Name = k
					result := &Any{}
					handled, resultFromExt, err := compiler.CallExtension(context, v, k)
					if handled {
						if err != nil {
							errors = append(errors, err)
						} else {
							bytes := compiler.Marshal(v)
							result.Yaml = string(bytes)
							result.Value = resultFromExt
							pair.Value = result
						}
					} else {
						pair.Value, err = NewAny(v, compiler.NewContext(k, v, context))
						if err != nil {
							errors = append(errors, err)
						}
					}
					x.SpecificationExtension = append(x.SpecificationExtension, pair)
				}
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewParameter creates an object of type Parameter if possible, returning an error if not.
func NewParameter(in *yaml.Node, context *compiler.Context) (*Parameter, error) {
	errors := make([]error, 0)
	x := &Parameter{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		requiredKeys := []string{"in", "name"}
		missingKeys := compiler.MissingKeysInMap(m, requiredKeys)
		if len(missingKeys) > 0 {
			message := fmt.Sprintf("is missing required %s: %+v", compiler.PluralProperties(len(missingKeys)), strings.Join(missingKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		allowedKeys := []string{"allowEmptyValue", "allowReserved", "content", "deprecated", "description", "example", "examples", "explode", "in", "name", "required", "schema", "style"}
		allowedPatterns := []*regexp.Regexp{pattern1}
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// string name = 1;
		v1 := compiler.MapValueForKey(m, "name")
		if v1 != nil {
			x.Name, ok = compiler.StringForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for name: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string in = 2;
		v2 := compiler.MapValueForKey(m, "in")
		if v2 != nil {
			x.In, ok = compiler.StringForScalarNode(v2)
			if !ok {
				message := fmt.Sprintf("has unexpected value for in: %s", compiler.Display(v2))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string description = 3;
		v3 := compiler.MapValueForKey(m, "description")
		if v3 != nil {
			x.Description, ok = compiler.StringForScalarNode(v3)
			if !ok {
				message := fmt.Sprintf("has unexpected value for description: %s", compiler.Display(v3))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// bool required = 4;
		v4 := compiler.MapValueForKey(m, "required")
		if v4 != nil {
			x.Required, ok = compiler.BoolForScalarNode(v4)
			if !ok {
				message := fmt.Sprintf("has unexpected value for required: %s", compiler.Display(v4))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// bool deprecated = 5;
		v5 := compiler.MapValueForKey(m, "deprecated")
		if v5 != nil {
			x.Deprecated, ok = compiler.BoolForScalarNode(v5)
			if !ok {
				message := fmt.Sprintf("has unexpected value for deprecated: %s", compiler.Display(v5))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// bool allow_empty_value = 6;
		v6 := compiler.MapValueForKey(m, "allowEmptyValue")
		if v6 != nil {
			x.AllowEmptyValue, ok = compiler.BoolForScalarNode(v6)
			if !ok {
				message := fmt.Sprintf("has unexpected value for allowEmptyValue: %s", compiler.Display(v6))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string style = 7;
		v7 := compiler.MapValueForKey(m, "style")
		if v7 != nil {
			x.Style, ok = compiler.StringForScalarNode(v7)
			if !ok {
				message := fmt.Sprintf("has unexpected value for style: %s", compiler.Display(v7))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// bool explode = 8;
		v8 := compiler.MapValueForKey(m, "explode")
		if v8 != nil {
			x.Explode, ok = compiler.BoolForScalarNode(v8)
			if !ok {
				message := fmt.Sprintf("has unexpected value for explode: %s", compiler.Display(v8))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// bool allow_reserved = 9;
		v9 := compiler.MapValueForKey(m, "allowReserved")
		if v9 != nil {
			x.AllowReserved, ok = compiler.BoolForScalarNode(v9)
			if !ok {
				message := fmt.Sprintf("has unexpected value for allowReserved: %s", compiler.Display(v9))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// SchemaOrReference schema = 10;
		v10 := compiler.MapValueForKey(m, "schema")
		if v10 != nil {
			var err error
			x.Schema, err = NewSchemaOrReference(v10, compiler.NewContext("schema", v10, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// Any example = 11;
		v11 := compiler.MapValueForKey(m, "example")
		if v11 != nil {
			var err error
			x.Example, err = NewAny(v11, compiler.NewContext("example", v11, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// ExamplesOrReferences examples = 12;
		v12 := compiler.MapValueForKey(m, "examples")
		if v12 != nil {
			var err error
			x.Examples, err = NewExamplesOrReferences(v12, compiler.NewContext("examples", v12, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// MediaTypes content = 13;
		v13 := compiler.MapValueForKey(m, "content")
		if v13 != nil {
			var err error
			x.Content, err = NewMediaTypes(v13, compiler.NewContext("content", v13, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// repeated NamedAny specification_extension = 14;
		// MAP: Any ^x-
		x.SpecificationExtension = make([]*NamedAny, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				if strings.HasPrefix(k, "x-") {
					pair := &NamedAny{}
					pair.Name = k
					result := &Any{}
					handled, resultFromExt, err := compiler.CallExtension(context, v, k)
					if handled {
						if err != nil {
							errors = append(errors, err)
						} else {
							bytes := compiler.Marshal(v)
							result.Yaml = string(bytes)
							result.Value = resultFromExt
							pair.Value = result
						}
					} else {
						pair.Value, err = NewAny(v, compiler.NewContext(k, v, context))
						if err != nil {
							errors = append(errors, err)
						}
					}
					x.SpecificationExtension = append(x.SpecificationExtension, pair)
				}
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewParameterOrReference creates an object of type ParameterOrReference if possible, returning an error if not.
func NewParameterOrReference(in *yaml.Node, context *compiler.Context) (*ParameterOrReference, error) {
	errors := make([]error, 0)
	x := &ParameterOrReference{}
	matched := false
	// Parameter parameter = 1;
	{
		m, ok := compiler.UnpackMap(in)
		if ok {
			// errors might be ok here, they mean we just don't have the right subtype
			t, matchingError := NewParameter(m, compiler.NewContext("parameter", m, context))
			if matchingError == nil {
				x.Oneof = &ParameterOrReference_Parameter{Parameter: t}
				matched = true
			} else {
				errors = append(errors, matchingError)
			}
		}
	}
	// Reference reference = 2;
	{
		m, ok := compiler.UnpackMap(in)
		if ok {
			// errors might be ok here, they mean we just don't have the right subtype
			t, matchingError := NewReference(m, compiler.NewContext("reference", m, context))
			if matchingError == nil {
				x.Oneof = &ParameterOrReference_Reference{Reference: t}
				matched = true
			} else {
				errors = append(errors, matchingError)
			}
		}
	}
	if matched {
		// since the oneof matched one of its possibilities, discard any matching errors
		errors = make([]error, 0)
	} else {
		message := fmt.Sprintf("contains an invalid ParameterOrReference")
		err := compiler.NewError(context, message)
		errors = []error{err}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewParametersOrReferences creates an object of type ParametersOrReferences if possible, returning an error if not.
func NewParametersOrReferences(in *yaml.Node, context *compiler.Context) (*ParametersOrReferences, error) {
	errors := make([]error, 0)
	x := &ParametersOrReferences{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		// repeated NamedParameterOrReference additional_properties = 1;
		// MAP: ParameterOrReference
		x.AdditionalProperties = make([]*NamedParameterOrReference, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				pair := &NamedParameterOrReference{}
				pair.Name = k
				var err error
				pair.Value, err = NewParameterOrReference(v, compiler.NewContext(k, v, context))
				if err != nil {
					errors = append(errors, err)
				}
				x.AdditionalProperties = append(x.AdditionalProperties, pair)
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewPathItem creates an object of type PathItem if possible, returning an error if not.
func NewPathItem(in *yaml.Node, context *compiler.Context) (*PathItem, error) {
	errors := make([]error, 0)
	x := &PathItem{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		allowedKeys := []string{"$ref", "delete", "description", "get", "head", "options", "parameters", "patch", "post", "put", "servers", "summary", "trace"}
		allowedPatterns := []*regexp.Regexp{pattern1}
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// string _ref = 1;
		v1 := compiler.MapValueForKey(m, "$ref")
		if v1 != nil {
			x.XRef, ok = compiler.StringForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for $ref: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string summary = 2;
		v2 := compiler.MapValueForKey(m, "summary")
		if v2 != nil {
			x.Summary, ok = compiler.StringForScalarNode(v2)
			if !ok {
				message := fmt.Sprintf("has unexpected value for summary: %s", compiler.Display(v2))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string description = 3;
		v3 := compiler.MapValueForKey(m, "description")
		if v3 != nil {
			x.Description, ok = compiler.StringForScalarNode(v3)
			if !ok {
				message := fmt.Sprintf("has unexpected value for description: %s", compiler.Display(v3))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// Operation get = 4;
		v4 := compiler.MapValueForKey(m, "get")
		if v4 != nil {
			var err error
			x.Get, err = NewOperation(v4, compiler.NewContext("get", v4, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// Operation put = 5;
		v5 := compiler.MapValueForKey(m, "put")
		if v5 != nil {
			var err error
			x.Put, err = NewOperation(v5, compiler.NewContext("put", v5, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// Operation post = 6;
		v6 := compiler.MapValueForKey(m, "post")
		if v6 != nil {
			var err error
			x.Post, err = NewOperation(v6, compiler.NewContext("post", v6, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// Operation delete = 7;
		v7 := compiler.MapValueForKey(m, "delete")
		if v7 != nil {
			var err error
			x.Delete, err = NewOperation(v7, compiler.NewContext("delete", v7, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// Operation options = 8;
		v8 := compiler.MapValueForKey(m, "options")
		if v8 != nil {
			var err error
			x.Options, err = NewOperation(v8, compiler.NewContext("options", v8, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// Operation head = 9;
		v9 := compiler.MapValueForKey(m, "head")
		if v9 != nil {
			var err error
			x.Head, err = NewOperation(v9, compiler.NewContext("head", v9, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// Operation patch = 10;
		v10 := compiler.MapValueForKey(m, "patch")
		if v10 != nil {
			var err error
			x.Patch, err = NewOperation(v10, compiler.NewContext("patch", v10, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// Operation trace = 11;
		v11 := compiler.MapValueForKey(m, "trace")
		if v11 != nil {
			var err error
			x.Trace, err = NewOperation(v11, compiler.NewContext("trace", v11, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// repeated Server servers = 12;
		v12 := compiler.MapValueForKey(m, "servers")
		if v12 != nil {
			// repeated Server
			x.Servers = make([]*Server, 0)
			a, ok := compiler.SequenceNodeForNode(v12)
			if ok {
				for _, item := range a.Content {
					y, err := NewServer(item, compiler.NewContext("servers", item, context))
					if err != nil {
						errors = append(errors, err)
					}
					x.Servers = append(x.Servers, y)
				}
			}
		}
		// repeated ParameterOrReference parameters = 13;
		v13 := compiler.MapValueForKey(m, "parameters")
		if v13 != nil {
			// repeated ParameterOrReference
			x.Parameters = make([]*ParameterOrReference, 0)
			a, ok := compiler.SequenceNodeForNode(v13)
			if ok {
				for _, item := range a.Content {
					y, err := NewParameterOrReference(item, compiler.NewContext("parameters", item, context))
					if err != nil {
						errors = append(errors, err)
					}
					x.Parameters = append(x.Parameters, y)
				}
			}
		}
		// repeated NamedAny specification_extension = 14;
		// MAP: Any ^x-
		x.SpecificationExtension = make([]*NamedAny, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				if strings.HasPrefix(k, "x-") {
					pair := &NamedAny{}
					pair.Name = k
					result := &Any{}
					handled, resultFromExt, err := compiler.CallExtension(context, v, k)
					if handled {
						if err != nil {
							errors = append(errors, err)
						} else {
							bytes := compiler.Marshal(v)
							result.Yaml = string(bytes)
							result.Value = resultFromExt
							pair.Value = result
						}
					} else {
						pair.Value, err = NewAny(v, compiler.NewContext(k, v, context))
						if err != nil {
							errors = append(errors, err)
						}
					}
					x.SpecificationExtension = append(x.SpecificationExtension, pair)
				}
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewPaths creates an object of type Paths if possible, returning an error if not.
func NewPaths(in *yaml.Node, context *compiler.Context) (*Paths, error) {
	errors := make([]error, 0)
	x := &Paths{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		allowedKeys := []string{}
		allowedPatterns := []*regexp.Regexp{pattern2, pattern1}
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// repeated NamedPathItem path = 1;
		// MAP: PathItem ^/
		x.Path = make([]*NamedPathItem, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				if strings.HasPrefix(k, "/") {
					pair := &NamedPathItem{}
					pair.Name = k
					var err error
					pair.Value, err = NewPathItem(v, compiler.NewContext(k, v, context))
					if err != nil {
						errors = append(errors, err)
					}
					x.Path = append(x.Path, pair)
				}
			}
		}
		// repeated NamedAny specification_extension = 2;
		// MAP: Any ^x-
		x.SpecificationExtension = make([]*NamedAny, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				if strings.HasPrefix(k, "x-") {
					pair := &NamedAny{}
					pair.Name = k
					result := &Any{}
					handled, resultFromExt, err := compiler.CallExtension(context, v, k)
					if handled {
						if err != nil {
							errors = append(errors, err)
						} else {
							bytes := compiler.Marshal(v)
							result.Yaml = string(bytes)
							result.Value = resultFromExt
							pair.Value = result
						}
					} else {
						pair.Value, err = NewAny(v, compiler.NewContext(k, v, context))
						if err != nil {
							errors = append(errors, err)
						}
					}
					x.SpecificationExtension = append(x.SpecificationExtension, pair)
				}
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewProperties creates an object of type Properties if possible, returning an error if not.
func NewProperties(in *yaml.Node, context *compiler.Context) (*Properties, error) {
	errors := make([]error, 0)
	x := &Properties{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		// repeated NamedSchemaOrReference additional_properties = 1;
		// MAP: SchemaOrReference
		x.AdditionalProperties = make([]*NamedSchemaOrReference, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				pair := &NamedSchemaOrReference{}
				pair.Name = k
				var err error
				pair.Value, err = NewSchemaOrReference(v, compiler.NewContext(k, v, context))
				if err != nil {
					errors = append(errors, err)
				}
				x.AdditionalProperties = append(x.AdditionalProperties, pair)
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewReference creates an object of type Reference if possible, returning an error if not.
func NewReference(in *yaml.Node, context *compiler.Context) (*Reference, error) {
	errors := make([]error, 0)
	x := &Reference{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		requiredKeys := []string{"$ref"}
		missingKeys := compiler.MissingKeysInMap(m, requiredKeys)
		if len(missingKeys) > 0 {
			message := fmt.Sprintf("is missing required %s: %+v", compiler.PluralProperties(len(missingKeys)), strings.Join(missingKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// string _ref = 1;
		v1 := compiler.MapValueForKey(m, "$ref")
		if v1 != nil {
			x.XRef, ok = compiler.StringForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for $ref: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string summary = 2;
		v2 := compiler.MapValueForKey(m, "summary")
		if v2 != nil {
			x.Summary, ok = compiler.StringForScalarNode(v2)
			if !ok {
				message := fmt.Sprintf("has unexpected value for summary: %s", compiler.Display(v2))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string description = 3;
		v3 := compiler.MapValueForKey(m, "description")
		if v3 != nil {
			x.Description, ok = compiler.StringForScalarNode(v3)
			if !ok {
				message := fmt.Sprintf("has unexpected value for description: %s", compiler.Display(v3))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewRequestBodiesOrReferences creates an object of type RequestBodiesOrReferences if possible, returning an error if not.
func NewRequestBodiesOrReferences(in *yaml.Node, context *compiler.Context) (*RequestBodiesOrReferences, error) {
	errors := make([]error, 0)
	x := &RequestBodiesOrReferences{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		// repeated NamedRequestBodyOrReference additional_properties = 1;
		// MAP: RequestBodyOrReference
		x.AdditionalProperties = make([]*NamedRequestBodyOrReference, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				pair := &NamedRequestBodyOrReference{}
				pair.Name = k
				var err error
				pair.Value, err = NewRequestBodyOrReference(v, compiler.NewContext(k, v, context))
				if err != nil {
					errors = append(errors, err)
				}
				x.AdditionalProperties = append(x.AdditionalProperties, pair)
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewRequestBody creates an object of type RequestBody if possible, returning an error if not.
func NewRequestBody(in *yaml.Node, context *compiler.Context) (*RequestBody, error) {
	errors := make([]error, 0)
	x := &RequestBody{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		requiredKeys := []string{"content"}
		missingKeys := compiler.MissingKeysInMap(m, requiredKeys)
		if len(missingKeys) > 0 {
			message := fmt.Sprintf("is missing required %s: %+v", compiler.PluralProperties(len(missingKeys)), strings.Join(missingKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		allowedKeys := []string{"content", "description", "required"}
		allowedPatterns := []*regexp.Regexp{pattern1}
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// string description = 1;
		v1 := compiler.MapValueForKey(m, "description")
		if v1 != nil {
			x.Description, ok = compiler.StringForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for description: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// MediaTypes content = 2;
		v2 := compiler.MapValueForKey(m, "content")
		if v2 != nil {
			var err error
			x.Content, err = NewMediaTypes(v2, compiler.NewContext("content", v2, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// bool required = 3;
		v3 := compiler.MapValueForKey(m, "required")
		if v3 != nil {
			x.Required, ok = compiler.BoolForScalarNode(v3)
			if !ok {
				message := fmt.Sprintf("has unexpected value for required: %s", compiler.Display(v3))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// repeated NamedAny specification_extension = 4;
		// MAP: Any ^x-
		x.SpecificationExtension = make([]*NamedAny, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				if strings.HasPrefix(k, "x-") {
					pair := &NamedAny{}
					pair.Name = k
					result := &Any{}
					handled, resultFromExt, err := compiler.CallExtension(context, v, k)
					if handled {
						if err != nil {
							errors = append(errors, err)
						} else {
							bytes := compiler.Marshal(v)
							result.Yaml = string(bytes)
							result.Value = resultFromExt
							pair.Value = result
						}
					} else {
						pair.Value, err = NewAny(v, compiler.NewContext(k, v, context))
						if err != nil {
							errors = append(errors, err)
						}
					}
					x.SpecificationExtension = append(x.SpecificationExtension, pair)
				}
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewRequestBodyOrReference creates an object of type RequestBodyOrReference if possible, returning an error if not.
func NewRequestBodyOrReference(in *yaml.Node, context *compiler.Context) (*RequestBodyOrReference, error) {
	errors := make([]error, 0)
	x := &RequestBodyOrReference{}
	matched := false
	// RequestBody request_body = 1;
	{
		m, ok := compiler.UnpackMap(in)
		if ok {
			// errors might be ok here, they mean we just don't have the right subtype
			t, matchingError := NewRequestBody(m, compiler.NewContext("requestBody", m, context))
			if matchingError == nil {
				x.Oneof = &RequestBodyOrReference_RequestBody{RequestBody: t}
				matched = true
			} else {
				errors = append(errors, matchingError)
			}
		}
	}
	// Reference reference = 2;
	{
		m, ok := compiler.UnpackMap(in)
		if ok {
			// errors might be ok here, they mean we just don't have the right subtype
			t, matchingError := NewReference(m, compiler.NewContext("reference", m, context))
			if matchingError == nil {
				x.Oneof = &RequestBodyOrReference_Reference{Reference: t}
				matched = true
			} else {
				errors = append(errors, matchingError)
			}
		}
	}
	if matched {
		// since the oneof matched one of its possibilities, discard any matching errors
		errors = make([]error, 0)
	} else {
		message := fmt.Sprintf("contains an invalid RequestBodyOrReference")
		err := compiler.NewError(context, message)
		errors = []error{err}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewResponse creates an object of type Response if possible, returning an error if not.
func NewResponse(in *yaml.Node, context *compiler.Context) (*Response, error) {
	errors := make([]error, 0)
	x := &Response{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		requiredKeys := []string{"description"}
		missingKeys := compiler.MissingKeysInMap(m, requiredKeys)
		if len(missingKeys) > 0 {
			message := fmt.Sprintf("is missing required %s: %+v", compiler.PluralProperties(len(missingKeys)), strings.Join(missingKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		allowedKeys := []string{"content", "description", "headers", "links"}
		allowedPatterns := []*regexp.Regexp{pattern1}
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// string description = 1;
		v1 := compiler.MapValueForKey(m, "description")
		if v1 != nil {
			x.Description, ok = compiler.StringForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for description: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// HeadersOrReferences headers = 2;
		v2 := compiler.MapValueForKey(m, "headers")
		if v2 != nil {
			var err error
			x.Headers, err = NewHeadersOrReferences(v2, compiler.NewContext("headers", v2, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// MediaTypes content = 3;
		v3 := compiler.MapValueForKey(m, "content")
		if v3 != nil {
			var err error
			x.Content, err = NewMediaTypes(v3, compiler.NewContext("content", v3, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// LinksOrReferences links = 4;
		v4 := compiler.MapValueForKey(m, "links")
		if v4 != nil {
			var err error
			x.Links, err = NewLinksOrReferences(v4, compiler.NewContext("links", v4, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// repeated NamedAny specification_extension = 5;
		// MAP: Any ^x-
		x.SpecificationExtension = make([]*NamedAny, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				if strings.HasPrefix(k, "x-") {
					pair := &NamedAny{}
					pair.Name = k
					result := &Any{}
					handled, resultFromExt, err := compiler.CallExtension(context, v, k)
					if handled {
						if err != nil {
							errors = append(errors, err)
						} else {
							bytes := compiler.Marshal(v)
							result.Yaml = string(bytes)
							result.Value = resultFromExt
							pair.Value = result
						}
					} else {
						pair.Value, err = NewAny(v, compiler.NewContext(k, v, context))
						if err != nil {
							errors = append(errors, err)
						}
					}
					x.SpecificationExtension = append(x.SpecificationExtension, pair)
				}
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewResponseOrReference creates an object of type ResponseOrReference if possible, returning an error if not.
func NewResponseOrReference(in *yaml.Node, context *compiler.Context) (*ResponseOrReference, error) {
	errors := make([]error, 0)
	x := &ResponseOrReference{}
	matched := false
	// Response response = 1;
	{
		m, ok := compiler.UnpackMap(in)
		if ok {
			// errors might be ok here, they mean we just don't have the right subtype
			t, matchingError := NewResponse(m, compiler.NewContext("response", m, context))
			if matchingError == nil {
				x.Oneof = &ResponseOrReference_Response{Response: t}
				matched = true
			} else {
				errors = append(errors, matchingError)
			}
		}
	}
	// Reference reference = 2;
	{
		m, ok := compiler.UnpackMap(in)
		if ok {
			// errors might be ok here, they mean we just don't have the right subtype
			t, matchingError := NewReference(m, compiler.NewContext("reference", m, context))
			if matchingError == nil {
				x.Oneof = &ResponseOrReference_Reference{Reference: t}
				matched = true
			} else {
				errors = append(errors, matchingError)
			}
		}
	}
	if matched {
		// since the oneof matched one of its possibilities, discard any matching errors
		errors = make([]error, 0)
	} else {
		message := fmt.Sprintf("contains an invalid ResponseOrReference")
		err := compiler.NewError(context, message)
		errors = []error{err}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewResponses creates an object of type Responses if possible, returning an error if not.
func NewResponses(in *yaml.Node, context *compiler.Context) (*Responses, error) {
	errors := make([]error, 0)
	x := &Responses{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		allowedKeys := []string{"default"}
		allowedPatterns := []*regexp.Regexp{pattern3, pattern1}
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// ResponseOrReference default = 1;
		v1 := compiler.MapValueForKey(m, "default")
		if v1 != nil {
			var err error
			x.Default, err = NewResponseOrReference(v1, compiler.NewContext("default", v1, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// repeated NamedResponseOrReference response_or_reference = 2;
		// MAP: ResponseOrReference ^([0-9X]{3})$
		x.ResponseOrReference = make([]*NamedResponseOrReference, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				if pattern3.MatchString(k) {
					pair := &NamedResponseOrReference{}
					pair.Name = k
					var err error
					pair.Value, err = NewResponseOrReference(v, compiler.NewContext(k, v, context))
					if err != nil {
						errors = append(errors, err)
					}
					x.ResponseOrReference = append(x.ResponseOrReference, pair)
				}
			}
		}
		// repeated NamedAny specification_extension = 3;
		// MAP: Any ^x-
		x.SpecificationExtension = make([]*NamedAny, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				if strings.HasPrefix(k, "x-") {
					pair := &NamedAny{}
					pair.Name = k
					result := &Any{}
					handled, resultFromExt, err := compiler.CallExtension(context, v, k)
					if handled {
						if err != nil {
							errors = append(errors, err)
						} else {
							bytes := compiler.Marshal(v)
							result.Yaml = string(bytes)
							result.Value = resultFromExt
							pair.Value = result
						}
					} else {
						pair.Value, err = NewAny(v, compiler.NewContext(k, v, context))
						if err != nil {
							errors = append(errors, err)
						}
					}
					x.SpecificationExtension = append(x.SpecificationExtension, pair)
				}
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewResponsesOrReferences creates an object of type ResponsesOrReferences if possible, returning an error if not.
func NewResponsesOrReferences(in *yaml.Node, context *compiler.Context) (*ResponsesOrReferences, error) {
	errors := make([]error, 0)
	x := &ResponsesOrReferences{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		// repeated NamedResponseOrReference additional_properties = 1;
		// MAP: ResponseOrReference
		x.AdditionalProperties = make([]*NamedResponseOrReference, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				pair := &NamedResponseOrReference{}
				pair.Name = k
				var err error
				pair.Value, err = NewResponseOrReference(v, compiler.NewContext(k, v, context))
				if err != nil {
					errors = append(errors, err)
				}
				x.AdditionalProperties = append(x.AdditionalProperties, pair)
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewSchema creates an object of type Schema if possible, returning an error if not.
func NewSchema(in *yaml.Node, context *compiler.Context) (*Schema, error) {
	errors := make([]error, 0)
	x := &Schema{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		allowedKeys := []string{"additionalProperties", "allOf", "anyOf", "default", "deprecated", "description", "discriminator", "enum", "example", "exclusiveMaximum", "exclusiveMinimum", "externalDocs", "format", "items", "maxItems", "maxLength", "maxProperties", "maximum", "minItems", "minLength", "minProperties", "minimum", "multipleOf", "not", "nullable", "oneOf", "pattern", "properties", "readOnly", "required", "title", "type", "uniqueItems", "writeOnly", "xml"}
		allowedPatterns := []*regexp.Regexp{pattern1}
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// bool nullable = 1;
		v1 := compiler.MapValueForKey(m, "nullable")
		if v1 != nil {
			x.Nullable, ok = compiler.BoolForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for nullable: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// Discriminator discriminator = 2;
		v2 := compiler.MapValueForKey(m, "discriminator")
		if v2 != nil {
			var err error
			x.Discriminator, err = NewDiscriminator(v2, compiler.NewContext("discriminator", v2, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// bool read_only = 3;
		v3 := compiler.MapValueForKey(m, "readOnly")
		if v3 != nil {
			x.ReadOnly, ok = compiler.BoolForScalarNode(v3)
			if !ok {
				message := fmt.Sprintf("has unexpected value for readOnly: %s", compiler.Display(v3))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// bool write_only = 4;
		v4 := compiler.MapValueForKey(m, "writeOnly")
		if v4 != nil {
			x.WriteOnly, ok = compiler.BoolForScalarNode(v4)
			if !ok {
				message := fmt.Sprintf("has unexpected value for writeOnly: %s", compiler.Display(v4))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// Xml xml = 5;
		v5 := compiler.MapValueForKey(m, "xml")
		if v5 != nil {
			var err error
			x.Xml, err = NewXml(v5, compiler.NewContext("xml", v5, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// ExternalDocs external_docs = 6;
		v6 := compiler.MapValueForKey(m, "externalDocs")
		if v6 != nil {
			var err error
			x.ExternalDocs, err = NewExternalDocs(v6, compiler.NewContext("externalDocs", v6, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// Any example = 7;
		v7 := compiler.MapValueForKey(m, "example")
		if v7 != nil {
			var err error
			x.Example, err = NewAny(v7, compiler.NewContext("example", v7, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// bool deprecated = 8;
		v8 := compiler.MapValueForKey(m, "deprecated")
		if v8 != nil {
			x.Deprecated, ok = compiler.BoolForScalarNode(v8)
			if !ok {
				message := fmt.Sprintf("has unexpected value for deprecated: %s", compiler.Display(v8))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string title = 9;
		v9 := compiler.MapValueForKey(m, "title")
		if v9 != nil {
			x.Title, ok = compiler.StringForScalarNode(v9)
			if !ok {
				message := fmt.Sprintf("has unexpected value for title: %s", compiler.Display(v9))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// float multiple_of = 10;
		v10 := compiler.MapValueForKey(m, "multipleOf")
		if v10 != nil {
			v, ok := compiler.FloatForScalarNode(v10)
			if ok {
				x.MultipleOf = v
			} else {
				message := fmt.Sprintf("has unexpected value for multipleOf: %s", compiler.Display(v10))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// float maximum = 11;
		v11 := compiler.MapValueForKey(m, "maximum")
		if v11 != nil {
			v, ok := compiler.FloatForScalarNode(v11)
			if ok {
				x.Maximum = v
			} else {
				message := fmt.Sprintf("has unexpected value for maximum: %s", compiler.Display(v11))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// bool exclusive_maximum = 12;
		v12 := compiler.MapValueForKey(m, "exclusiveMaximum")
		if v12 != nil {
			x.ExclusiveMaximum, ok = compiler.BoolForScalarNode(v12)
			if !ok {
				message := fmt.Sprintf("has unexpected value for exclusiveMaximum: %s", compiler.Display(v12))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// float minimum = 13;
		v13 := compiler.MapValueForKey(m, "minimum")
		if v13 != nil {
			v, ok := compiler.FloatForScalarNode(v13)
			if ok {
				x.Minimum = v
			} else {
				message := fmt.Sprintf("has unexpected value for minimum: %s", compiler.Display(v13))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// bool exclusive_minimum = 14;
		v14 := compiler.MapValueForKey(m, "exclusiveMinimum")
		if v14 != nil {
			x.ExclusiveMinimum, ok = compiler.BoolForScalarNode(v14)
			if !ok {
				message := fmt.Sprintf("has unexpected value for exclusiveMinimum: %s", compiler.Display(v14))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// int64 max_length = 15;
		v15 := compiler.MapValueForKey(m, "maxLength")
		if v15 != nil {
			t, ok := compiler.IntForScalarNode(v15)
			if ok {
				x.MaxLength = int64(t)
			} else {
				message := fmt.Sprintf("has unexpected value for maxLength: %s", compiler.Display(v15))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// int64 min_length = 16;
		v16 := compiler.MapValueForKey(m, "minLength")
		if v16 != nil {
			t, ok := compiler.IntForScalarNode(v16)
			if ok {
				x.MinLength = int64(t)
			} else {
				message := fmt.Sprintf("has unexpected value for minLength: %s", compiler.Display(v16))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string pattern = 17;
		v17 := compiler.MapValueForKey(m, "pattern")
		if v17 != nil {
			x.Pattern, ok = compiler.StringForScalarNode(v17)
			if !ok {
				message := fmt.Sprintf("has unexpected value for pattern: %s", compiler.Display(v17))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// int64 max_items = 18;
		v18 := compiler.MapValueForKey(m, "maxItems")
		if v18 != nil {
			t, ok := compiler.IntForScalarNode(v18)
			if ok {
				x.MaxItems = int64(t)
			} else {
				message := fmt.Sprintf("has unexpected value for maxItems: %s", compiler.Display(v18))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// int64 min_items = 19;
		v19 := compiler.MapValueForKey(m, "minItems")
		if v19 != nil {
			t, ok := compiler.IntForScalarNode(v19)
			if ok {
				x.MinItems = int64(t)
			} else {
				message := fmt.Sprintf("has unexpected value for minItems: %s", compiler.Display(v19))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// bool unique_items = 20;
		v20 := compiler.MapValueForKey(m, "uniqueItems")
		if v20 != nil {
			x.UniqueItems, ok = compiler.BoolForScalarNode(v20)
			if !ok {
				message := fmt.Sprintf("has unexpected value for uniqueItems: %s", compiler.Display(v20))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// int64 max_properties = 21;
		v21 := compiler.MapValueForKey(m, "maxProperties")
		if v21 != nil {
			t, ok := compiler.IntForScalarNode(v21)
			if ok {
				x.MaxProperties = int64(t)
			} else {
				message := fmt.Sprintf("has unexpected value for maxProperties: %s", compiler.Display(v21))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// int64 min_properties = 22;
		v22 := compiler.MapValueForKey(m, "minProperties")
		if v22 != nil {
			t, ok := compiler.IntForScalarNode(v22)
			if ok {
				x.MinProperties = int64(t)
			} else {
				message := fmt.Sprintf("has unexpected value for minProperties: %s", compiler.Display(v22))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// repeated string required = 23;
		v23 := compiler.MapValueForKey(m, "required")
		if v23 != nil {
			v, ok := compiler.SequenceNodeForNode(v23)
			if ok {
				x.Required = compiler.StringArrayForSequenceNode(v)
			} else {
				message := fmt.Sprintf("has unexpected value for required: %s", compiler.Display(v23))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// repeated Any enum = 24;
		v24 := compiler.MapValueForKey(m, "enum")
		if v24 != nil {
			// repeated Any
			x.Enum = make([]*Any, 0)
			a, ok := compiler.SequenceNodeForNode(v24)
			if ok {
				for _, item := range a.Content {
					y, err := NewAny(item, compiler.NewContext("enum", item, context))
					if err != nil {
						errors = append(errors, err)
					}
					x.Enum = append(x.Enum, y)
				}
			}
		}
		// string type = 25;
		v25 := compiler.MapValueForKey(m, "type")
		if v25 != nil {
			x.Type, ok = compiler.StringForScalarNode(v25)
			if !ok {
				message := fmt.Sprintf("has unexpected value for type: %s", compiler.Display(v25))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// repeated SchemaOrReference all_of = 26;
		v26 := compiler.MapValueForKey(m, "allOf")
		if v26 != nil {
			// repeated SchemaOrReference
			x.AllOf = make([]*SchemaOrReference, 0)
			a, ok := compiler.SequenceNodeForNode(v26)
			if ok {
				for _, item := range a.Content {
					y, err := NewSchemaOrReference(item, compiler.NewContext("allOf", item, context))
					if err != nil {
						errors = append(errors, err)
					}
					x.AllOf = append(x.AllOf, y)
				}
			}
		}
		// repeated SchemaOrReference one_of = 27;
		v27 := compiler.MapValueForKey(m, "oneOf")
		if v27 != nil {
			// repeated SchemaOrReference
			x.OneOf = make([]*SchemaOrReference, 0)
			a, ok := compiler.SequenceNodeForNode(v27)
			if ok {
				for _, item := range a.Content {
					y, err := NewSchemaOrReference(item, compiler.NewContext("oneOf", item, context))
					if err != nil {
						errors = append(errors, err)
					}
					x.OneOf = append(x.OneOf, y)
				}
			}
		}
		// repeated SchemaOrReference any_of = 28;
		v28 := compiler.MapValueForKey(m, "anyOf")
		if v28 != nil {
			// repeated SchemaOrReference
			x.AnyOf = make([]*SchemaOrReference, 0)
			a, ok := compiler.SequenceNodeForNode(v28)
			if ok {
				for _, item := range a.Content {
					y, err := NewSchemaOrReference(item, compiler.NewContext("anyOf", item, context))
					if err != nil {
						errors = append(errors, err)
					}
					x.AnyOf = append(x.AnyOf, y)
				}
			}
		}
		// Schema not = 29;
		v29 := compiler.MapValueForKey(m, "not")
		if v29 != nil {
			var err error
			x.Not, err = NewSchema(v29, compiler.NewContext("not", v29, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// ItemsItem items = 30;
		v30 := compiler.MapValueForKey(m, "items")
		if v30 != nil {
			var err error
			x.Items, err = NewItemsItem(v30, compiler.NewContext("items", v30, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// Properties properties = 31;
		v31 := compiler.MapValueForKey(m, "properties")
		if v31 != nil {
			var err error
			x.Properties, err = NewProperties(v31, compiler.NewContext("properties", v31, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// AdditionalPropertiesItem additional_properties = 32;
		v32 := compiler.MapValueForKey(m, "additionalProperties")
		if v32 != nil {
			var err error
			x.AdditionalProperties, err = NewAdditionalPropertiesItem(v32, compiler.NewContext("additionalProperties", v32, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// DefaultType default = 33;
		v33 := compiler.MapValueForKey(m, "default")
		if v33 != nil {
			var err error
			x.Default, err = NewDefaultType(v33, compiler.NewContext("default", v33, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// string description = 34;
		v34 := compiler.MapValueForKey(m, "description")
		if v34 != nil {
			x.Description, ok = compiler.StringForScalarNode(v34)
			if !ok {
				message := fmt.Sprintf("has unexpected value for description: %s", compiler.Display(v34))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string format = 35;
		v35 := compiler.MapValueForKey(m, "format")
		if v35 != nil {
			x.Format, ok = compiler.StringForScalarNode(v35)
			if !ok {
				message := fmt.Sprintf("has unexpected value for format: %s", compiler.Display(v35))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// repeated NamedAny specification_extension = 36;
		// MAP: Any ^x-
		x.SpecificationExtension = make([]*NamedAny, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				if strings.HasPrefix(k, "x-") {
					pair := &NamedAny{}
					pair.Name = k
					result := &Any{}
					handled, resultFromExt, err := compiler.CallExtension(context, v, k)
					if handled {
						if err != nil {
							errors = append(errors, err)
						} else {
							bytes := compiler.Marshal(v)
							result.Yaml = string(bytes)
							result.Value = resultFromExt
							pair.Value = result
						}
					} else {
						pair.Value, err = NewAny(v, compiler.NewContext(k, v, context))
						if err != nil {
							errors = append(errors, err)
						}
					}
					x.SpecificationExtension = append(x.SpecificationExtension, pair)
				}
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewSchemaOrReference creates an object of type SchemaOrReference if possible, returning an error if not.
func NewSchemaOrReference(in *yaml.Node, context *compiler.Context) (*SchemaOrReference, error) {
	errors := make([]error, 0)
	x := &SchemaOrReference{}
	matched := false
	// Schema schema = 1;
	{
		m, ok := compiler.UnpackMap(in)
		if ok {
			// errors might be ok here, they mean we just don't have the right subtype
			t, matchingError := NewSchema(m, compiler.NewContext("schema", m, context))
			if matchingError == nil {
				x.Oneof = &SchemaOrReference_Schema{Schema: t}
				matched = true
			} else {
				errors = append(errors, matchingError)
			}
		}
	}
	// Reference reference = 2;
	{
		m, ok := compiler.UnpackMap(in)
		if ok {
			// errors might be ok here, they mean we just don't have the right subtype
			t, matchingError := NewReference(m, compiler.NewContext("reference", m, context))
			if matchingError == nil {
				x.Oneof = &SchemaOrReference_Reference{Reference: t}
				matched = true
			} else {
				errors = append(errors, matchingError)
			}
		}
	}
	if matched {
		// since the oneof matched one of its possibilities, discard any matching errors
		errors = make([]error, 0)
	} else {
		message := fmt.Sprintf("contains an invalid SchemaOrReference")
		err := compiler.NewError(context, message)
		errors = []error{err}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewSchemasOrReferences creates an object of type SchemasOrReferences if possible, returning an error if not.
func NewSchemasOrReferences(in *yaml.Node, context *compiler.Context) (*SchemasOrReferences, error) {
	errors := make([]error, 0)
	x := &SchemasOrReferences{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		// repeated NamedSchemaOrReference additional_properties = 1;
		// MAP: SchemaOrReference
		x.AdditionalProperties = make([]*NamedSchemaOrReference, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				pair := &NamedSchemaOrReference{}
				pair.Name = k
				var err error
				pair.Value, err = NewSchemaOrReference(v, compiler.NewContext(k, v, context))
				if err != nil {
					errors = append(errors, err)
				}
				x.AdditionalProperties = append(x.AdditionalProperties, pair)
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewSecurityRequirement creates an object of type SecurityRequirement if possible, returning an error if not.
func NewSecurityRequirement(in *yaml.Node, context *compiler.Context) (*SecurityRequirement, error) {
	errors := make([]error, 0)
	x := &SecurityRequirement{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		// repeated NamedStringArray additional_properties = 1;
		// MAP: StringArray
		x.AdditionalProperties = make([]*NamedStringArray, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				pair := &NamedStringArray{}
				pair.Name = k
				var err error
				pair.Value, err = NewStringArray(v, compiler.NewContext(k, v, context))
				if err != nil {
					errors = append(errors, err)
				}
				x.AdditionalProperties = append(x.AdditionalProperties, pair)
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewSecurityScheme creates an object of type SecurityScheme if possible, returning an error if not.
func NewSecurityScheme(in *yaml.Node, context *compiler.Context) (*SecurityScheme, error) {
	errors := make([]error, 0)
	x := &SecurityScheme{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		requiredKeys := []string{"type"}
		missingKeys := compiler.MissingKeysInMap(m, requiredKeys)
		if len(missingKeys) > 0 {
			message := fmt.Sprintf("is missing required %s: %+v", compiler.PluralProperties(len(missingKeys)), strings.Join(missingKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		allowedKeys := []string{"bearerFormat", "description", "flows", "in", "name", "openIdConnectUrl", "scheme", "type"}
		allowedPatterns := []*regexp.Regexp{pattern1}
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// string type = 1;
		v1 := compiler.MapValueForKey(m, "type")
		if v1 != nil {
			x.Type, ok = compiler.StringForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for type: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string description = 2;
		v2 := compiler.MapValueForKey(m, "description")
		if v2 != nil {
			x.Description, ok = compiler.StringForScalarNode(v2)
			if !ok {
				message := fmt.Sprintf("has unexpected value for description: %s", compiler.Display(v2))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string name = 3;
		v3 := compiler.MapValueForKey(m, "name")
		if v3 != nil {
			x.Name, ok = compiler.StringForScalarNode(v3)
			if !ok {
				message := fmt.Sprintf("has unexpected value for name: %s", compiler.Display(v3))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string in = 4;
		v4 := compiler.MapValueForKey(m, "in")
		if v4 != nil {
			x.In, ok = compiler.StringForScalarNode(v4)
			if !ok {
				message := fmt.Sprintf("has unexpected value for in: %s", compiler.Display(v4))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string scheme = 5;
		v5 := compiler.MapValueForKey(m, "scheme")
		if v5 != nil {
			x.Scheme, ok = compiler.StringForScalarNode(v5)
			if !ok {
				message := fmt.Sprintf("has unexpected value for scheme: %s", compiler.Display(v5))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string bearer_format = 6;
		v6 := compiler.MapValueForKey(m, "bearerFormat")
		if v6 != nil {
			x.BearerFormat, ok = compiler.StringForScalarNode(v6)
			if !ok {
				message := fmt.Sprintf("has unexpected value for bearerFormat: %s", compiler.Display(v6))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// OauthFlows flows = 7;
		v7 := compiler.MapValueForKey(m, "flows")
		if v7 != nil {
			var err error
			x.Flows, err = NewOauthFlows(v7, compiler.NewContext("flows", v7, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// string open_id_connect_url = 8;
		v8 := compiler.MapValueForKey(m, "openIdConnectUrl")
		if v8 != nil {
			x.OpenIdConnectUrl, ok = compiler.StringForScalarNode(v8)
			if !ok {
				message := fmt.Sprintf("has unexpected value for openIdConnectUrl: %s", compiler.Display(v8))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// repeated NamedAny specification_extension = 9;
		// MAP: Any ^x-
		x.SpecificationExtension = make([]*NamedAny, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				if strings.HasPrefix(k, "x-") {
					pair := &NamedAny{}
					pair.Name = k
					result := &Any{}
					handled, resultFromExt, err := compiler.CallExtension(context, v, k)
					if handled {
						if err != nil {
							errors = append(errors, err)
						} else {
							bytes := compiler.Marshal(v)
							result.Yaml = string(bytes)
							result.Value = resultFromExt
							pair.Value = result
						}
					} else {
						pair.Value, err = NewAny(v, compiler.NewContext(k, v, context))
						if err != nil {
							errors = append(errors, err)
						}
					}
					x.SpecificationExtension = append(x.SpecificationExtension, pair)
				}
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewSecuritySchemeOrReference creates an object of type SecuritySchemeOrReference if possible, returning an error if not.
func NewSecuritySchemeOrReference(in *yaml.Node, context *compiler.Context) (*SecuritySchemeOrReference, error) {
	errors := make([]error, 0)
	x := &SecuritySchemeOrReference{}
	matched := false
	// SecurityScheme security_scheme = 1;
	{
		m, ok := compiler.UnpackMap(in)
		if ok {
			// errors might be ok here, they mean we just don't have the right subtype
			t, matchingError := NewSecurityScheme(m, compiler.NewContext("securityScheme", m, context))
			if matchingError == nil {
				x.Oneof = &SecuritySchemeOrReference_SecurityScheme{SecurityScheme: t}
				matched = true
			} else {
				errors = append(errors, matchingError)
			}
		}
	}
	// Reference reference = 2;
	{
		m, ok := compiler.UnpackMap(in)
		if ok {
			// errors might be ok here, they mean we just don't have the right subtype
			t, matchingError := NewReference(m, compiler.NewContext("reference", m, context))
			if matchingError == nil {
				x.Oneof = &SecuritySchemeOrReference_Reference{Reference: t}
				matched = true
			} else {
				errors = append(errors, matchingError)
			}
		}
	}
	if matched {
		// since the oneof matched one of its possibilities, discard any matching errors
		errors = make([]error, 0)
	} else {
		message := fmt.Sprintf("contains an invalid SecuritySchemeOrReference")
		err := compiler.NewError(context, message)
		errors = []error{err}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewSecuritySchemesOrReferences creates an object of type SecuritySchemesOrReferences if possible, returning an error if not.
func NewSecuritySchemesOrReferences(in *yaml.Node, context *compiler.Context) (*SecuritySchemesOrReferences, error) {
	errors := make([]error, 0)
	x := &SecuritySchemesOrReferences{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		// repeated NamedSecuritySchemeOrReference additional_properties = 1;
		// MAP: SecuritySchemeOrReference
		x.AdditionalProperties = make([]*NamedSecuritySchemeOrReference, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				pair := &NamedSecuritySchemeOrReference{}
				pair.Name = k
				var err error
				pair.Value, err = NewSecuritySchemeOrReference(v, compiler.NewContext(k, v, context))
				if err != nil {
					errors = append(errors, err)
				}
				x.AdditionalProperties = append(x.AdditionalProperties, pair)
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewServer creates an object of type Server if possible, returning an error if not.
func NewServer(in *yaml.Node, context *compiler.Context) (*Server, error) {
	errors := make([]error, 0)
	x := &Server{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		requiredKeys := []string{"url"}
		missingKeys := compiler.MissingKeysInMap(m, requiredKeys)
		if len(missingKeys) > 0 {
			message := fmt.Sprintf("is missing required %s: %+v", compiler.PluralProperties(len(missingKeys)), strings.Join(missingKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		allowedKeys := []string{"description", "url", "variables"}
		allowedPatterns := []*regexp.Regexp{pattern1}
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// string url = 1;
		v1 := compiler.MapValueForKey(m, "url")
		if v1 != nil {
			x.Url, ok = compiler.StringForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for url: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string description = 2;
		v2 := compiler.MapValueForKey(m, "description")
		if v2 != nil {
			x.Description, ok = compiler.StringForScalarNode(v2)
			if !ok {
				message := fmt.Sprintf("has unexpected value for description: %s", compiler.Display(v2))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// ServerVariables variables = 3;
		v3 := compiler.MapValueForKey(m, "variables")
		if v3 != nil {
			var err error
			x.Variables, err = NewServerVariables(v3, compiler.NewContext("variables", v3, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// repeated NamedAny specification_extension = 4;
		// MAP: Any ^x-
		x.SpecificationExtension = make([]*NamedAny, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				if strings.HasPrefix(k, "x-") {
					pair := &NamedAny{}
					pair.Name = k
					result := &Any{}
					handled, resultFromExt, err := compiler.CallExtension(context, v, k)
					if handled {
						if err != nil {
							errors = append(errors, err)
						} else {
							bytes := compiler.Marshal(v)
							result.Yaml = string(bytes)
							result.Value = resultFromExt
							pair.Value = result
						}
					} else {
						pair.Value, err = NewAny(v, compiler.NewContext(k, v, context))
						if err != nil {
							errors = append(errors, err)
						}
					}
					x.SpecificationExtension = append(x.SpecificationExtension, pair)
				}
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewServerVariable creates an object of type ServerVariable if possible, returning an error if not.
func NewServerVariable(in *yaml.Node, context *compiler.Context) (*ServerVariable, error) {
	errors := make([]error, 0)
	x := &ServerVariable{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		requiredKeys := []string{"default"}
		missingKeys := compiler.MissingKeysInMap(m, requiredKeys)
		if len(missingKeys) > 0 {
			message := fmt.Sprintf("is missing required %s: %+v", compiler.PluralProperties(len(missingKeys)), strings.Join(missingKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		allowedKeys := []string{"default", "description", "enum"}
		allowedPatterns := []*regexp.Regexp{pattern1}
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// repeated string enum = 1;
		v1 := compiler.MapValueForKey(m, "enum")
		if v1 != nil {
			v, ok := compiler.SequenceNodeForNode(v1)
			if ok {
				x.Enum = compiler.StringArrayForSequenceNode(v)
			} else {
				message := fmt.Sprintf("has unexpected value for enum: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string default = 2;
		v2 := compiler.MapValueForKey(m, "default")
		if v2 != nil {
			x.Default, ok = compiler.StringForScalarNode(v2)
			if !ok {
				message := fmt.Sprintf("has unexpected value for default: %s", compiler.Display(v2))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string description = 3;
		v3 := compiler.MapValueForKey(m, "description")
		if v3 != nil {
			x.Description, ok = compiler.StringForScalarNode(v3)
			if !ok {
				message := fmt.Sprintf("has unexpected value for description: %s", compiler.Display(v3))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// repeated NamedAny specification_extension = 4;
		// MAP: Any ^x-
		x.SpecificationExtension = make([]*NamedAny, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				if strings.HasPrefix(k, "x-") {
					pair := &NamedAny{}
					pair.Name = k
					result := &Any{}
					handled, resultFromExt, err := compiler.CallExtension(context, v, k)
					if handled {
						if err != nil {
							errors = append(errors, err)
						} else {
							bytes := compiler.Marshal(v)
							result.Yaml = string(bytes)
							result.Value = resultFromExt
							pair.Value = result
						}
					} else {
						pair.Value, err = NewAny(v, compiler.NewContext(k, v, context))
						if err != nil {
							errors = append(errors, err)
						}
					}
					x.SpecificationExtension = append(x.SpecificationExtension, pair)
				}
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewServerVariables creates an object of type ServerVariables if possible, returning an error if not.
func NewServerVariables(in *yaml.Node, context *compiler.Context) (*ServerVariables, error) {
	errors := make([]error, 0)
	x := &ServerVariables{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		// repeated NamedServerVariable additional_properties = 1;
		// MAP: ServerVariable
		x.AdditionalProperties = make([]*NamedServerVariable, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				pair := &NamedServerVariable{}
				pair.Name = k
				var err error
				pair.Value, err = NewServerVariable(v, compiler.NewContext(k, v, context))
				if err != nil {
					errors = append(errors, err)
				}
				x.AdditionalProperties = append(x.AdditionalProperties, pair)
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewSpecificationExtension creates an object of type SpecificationExtension if possible, returning an error if not.
func NewSpecificationExtension(in *yaml.Node, context *compiler.Context) (*SpecificationExtension, error) {
	errors := make([]error, 0)
	x := &SpecificationExtension{}
	matched := false
	switch in.Tag {
	case "!!bool":
		var v bool
		v, matched = compiler.BoolForScalarNode(in)
		x.Oneof = &SpecificationExtension_Boolean{Boolean: v}
	case "!!str":
		var v string
		v, matched = compiler.StringForScalarNode(in)
		x.Oneof = &SpecificationExtension_String_{String_: v}
	case "!!float":
		var v float64
		v, matched = compiler.FloatForScalarNode(in)
		x.Oneof = &SpecificationExtension_Number{Number: v}
	case "!!int":
		var v int64
		v, matched = compiler.IntForScalarNode(in)
		x.Oneof = &SpecificationExtension_Number{Number: float64(v)}
	}
	if matched {
		// since the oneof matched one of its possibilities, discard any matching errors
		errors = make([]error, 0)
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewStringArray creates an object of type StringArray if possible, returning an error if not.
func NewStringArray(in *yaml.Node, context *compiler.Context) (*StringArray, error) {
	errors := make([]error, 0)
	x := &StringArray{}
	x.Value = make([]string, 0)
	for _, node := range in.Content {
		s, _ := compiler.StringForScalarNode(node)
		x.Value = append(x.Value, s)
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewStrings creates an object of type Strings if possible, returning an error if not.
func NewStrings(in *yaml.Node, context *compiler.Context) (*Strings, error) {
	errors := make([]error, 0)
	x := &Strings{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		// repeated NamedString additional_properties = 1;
		// MAP: string
		x.AdditionalProperties = make([]*NamedString, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				pair := &NamedString{}
				pair.Name = k
				pair.Value, _ = compiler.StringForScalarNode(v)
				x.AdditionalProperties = append(x.AdditionalProperties, pair)
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewTag creates an object of type Tag if possible, returning an error if not.
func NewTag(in *yaml.Node, context *compiler.Context) (*Tag, error) {
	errors := make([]error, 0)
	x := &Tag{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		requiredKeys := []string{"name"}
		missingKeys := compiler.MissingKeysInMap(m, requiredKeys)
		if len(missingKeys) > 0 {
			message := fmt.Sprintf("is missing required %s: %+v", compiler.PluralProperties(len(missingKeys)), strings.Join(missingKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		allowedKeys := []string{"description", "externalDocs", "name"}
		allowedPatterns := []*regexp.Regexp{pattern1}
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// string name = 1;
		v1 := compiler.MapValueForKey(m, "name")
		if v1 != nil {
			x.Name, ok = compiler.StringForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for name: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string description = 2;
		v2 := compiler.MapValueForKey(m, "description")
		if v2 != nil {
			x.Description, ok = compiler.StringForScalarNode(v2)
			if !ok {
				message := fmt.Sprintf("has unexpected value for description: %s", compiler.Display(v2))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// ExternalDocs external_docs = 3;
		v3 := compiler.MapValueForKey(m, "externalDocs")
		if v3 != nil {
			var err error
			x.ExternalDocs, err = NewExternalDocs(v3, compiler.NewContext("externalDocs", v3, context))
			if err != nil {
				errors = append(errors, err)
			}
		}
		// repeated NamedAny specification_extension = 4;
		// MAP: Any ^x-
		x.SpecificationExtension = make([]*NamedAny, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				if strings.HasPrefix(k, "x-") {
					pair := &NamedAny{}
					pair.Name = k
					result := &Any{}
					handled, resultFromExt, err := compiler.CallExtension(context, v, k)
					if handled {
						if err != nil {
							errors = append(errors, err)
						} else {
							bytes := compiler.Marshal(v)
							result.Yaml = string(bytes)
							result.Value = resultFromExt
							pair.Value = result
						}
					} else {
						pair.Value, err = NewAny(v, compiler.NewContext(k, v, context))
						if err != nil {
							errors = append(errors, err)
						}
					}
					x.SpecificationExtension = append(x.SpecificationExtension, pair)
				}
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// NewXml creates an object of type Xml if possible, returning an error if not.
func NewXml(in *yaml.Node, context *compiler.Context) (*Xml, error) {
	errors := make([]error, 0)
	x := &Xml{}
	m, ok := compiler.UnpackMap(in)
	if !ok {
		message := fmt.Sprintf("has unexpected value: %+v (%T)", in, in)
		errors = append(errors, compiler.NewError(context, message))
	} else {
		allowedKeys := []string{"attribute", "name", "namespace", "prefix", "wrapped"}
		allowedPatterns := []*regexp.Regexp{pattern1}
		invalidKeys := compiler.InvalidKeysInMap(m, allowedKeys, allowedPatterns)
		if len(invalidKeys) > 0 {
			message := fmt.Sprintf("has invalid %s: %+v", compiler.PluralProperties(len(invalidKeys)), strings.Join(invalidKeys, ", "))
			errors = append(errors, compiler.NewError(context, message))
		}
		// string name = 1;
		v1 := compiler.MapValueForKey(m, "name")
		if v1 != nil {
			x.Name, ok = compiler.StringForScalarNode(v1)
			if !ok {
				message := fmt.Sprintf("has unexpected value for name: %s", compiler.Display(v1))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string namespace = 2;
		v2 := compiler.MapValueForKey(m, "namespace")
		if v2 != nil {
			x.Namespace, ok = compiler.StringForScalarNode(v2)
			if !ok {
				message := fmt.Sprintf("has unexpected value for namespace: %s", compiler.Display(v2))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// string prefix = 3;
		v3 := compiler.MapValueForKey(m, "prefix")
		if v3 != nil {
			x.Prefix, ok = compiler.StringForScalarNode(v3)
			if !ok {
				message := fmt.Sprintf("has unexpected value for prefix: %s", compiler.Display(v3))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// bool attribute = 4;
		v4 := compiler.MapValueForKey(m, "attribute")
		if v4 != nil {
			x.Attribute, ok = compiler.BoolForScalarNode(v4)
			if !ok {
				message := fmt.Sprintf("has unexpected value for attribute: %s", compiler.Display(v4))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// bool wrapped = 5;
		v5 := compiler.MapValueForKey(m, "wrapped")
		if v5 != nil {
			x.Wrapped, ok = compiler.BoolForScalarNode(v5)
			if !ok {
				message := fmt.Sprintf("has unexpected value for wrapped: %s", compiler.Display(v5))
				errors = append(errors, compiler.NewError(context, message))
			}
		}
		// repeated NamedAny specification_extension = 6;
		// MAP: Any ^x-
		x.SpecificationExtension = make([]*NamedAny, 0)
		for i := 0; i < len(m.Content); i += 2 {
			k, ok := compiler.StringForScalarNode(m.Content[i])
			if ok {
				v := m.Content[i+1]
				if strings.HasPrefix(k, "x-") {
					pair := &NamedAny{}
					pair.Name = k
					result := &Any{}
					handled, resultFromExt, err := compiler.CallExtension(context, v, k)
					if handled {
						if err != nil {
							errors = append(errors, err)
						} else {
							bytes := compiler.Marshal(v)
							result.Yaml = string(bytes)
							result.Value = resultFromExt
							pair.Value = result
						}
					} else {
						pair.Value, err = NewAny(v, compiler.NewContext(k, v, context))
						if err != nil {
							errors = append(errors, err)
						}
					}
					x.SpecificationExtension = append(x.SpecificationExtension, pair)
				}
			}
		}
	}
	return x, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside AdditionalPropertiesItem objects.
func (m *AdditionalPropertiesItem) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	{
		p, ok := m.Oneof.(*AdditionalPropertiesItem_SchemaOrReference)
		if ok {
			_, err := p.SchemaOrReference.ResolveReferences(root)
			if err != nil {
				return nil, err
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside Any objects.
func (m *Any) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside AnyOrExpression objects.
func (m *AnyOrExpression) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	{
		p, ok := m.Oneof.(*AnyOrExpression_Any)
		if ok {
			_, err := p.Any.ResolveReferences(root)
			if err != nil {
				return nil, err
			}
		}
	}
	{
		p, ok := m.Oneof.(*AnyOrExpression_Expression)
		if ok {
			_, err := p.Expression.ResolveReferences(root)
			if err != nil {
				return nil, err
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside Callback objects.
func (m *Callback) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	for _, item := range m.Path {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	for _, item := range m.SpecificationExtension {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside CallbackOrReference objects.
func (m *CallbackOrReference) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	{
		p, ok := m.Oneof.(*CallbackOrReference_Callback)
		if ok {
			_, err := p.Callback.ResolveReferences(root)
			if err != nil {
				return nil, err
			}
		}
	}
	{
		p, ok := m.Oneof.(*CallbackOrReference_Reference)
		if ok {
			_, err := p.Reference.ResolveReferences(root)
			if err != nil {
				return nil, err
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside CallbacksOrReferences objects.
func (m *CallbacksOrReferences) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	for _, item := range m.AdditionalProperties {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside Components objects.
func (m *Components) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	if m.Schemas != nil {
		_, err := m.Schemas.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	if m.Responses != nil {
		_, err := m.Responses.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	if m.Parameters != nil {
		_, err := m.Parameters.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	if m.Examples != nil {
		_, err := m.Examples.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	if m.RequestBodies != nil {
		_, err := m.RequestBodies.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	if m.Headers != nil {
		_, err := m.Headers.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	if m.SecuritySchemes != nil {
		_, err := m.SecuritySchemes.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	if m.Links != nil {
		_, err := m.Links.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	if m.Callbacks != nil {
		_, err := m.Callbacks.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	for _, item := range m.SpecificationExtension {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside Contact objects.
func (m *Contact) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	for _, item := range m.SpecificationExtension {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside DefaultType objects.
func (m *DefaultType) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside Discriminator objects.
func (m *Discriminator) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	if m.Mapping != nil {
		_, err := m.Mapping.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	for _, item := range m.SpecificationExtension {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside Document objects.
func (m *Document) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	if m.Info != nil {
		_, err := m.Info.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	for _, item := range m.Servers {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	if m.Paths != nil {
		_, err := m.Paths.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	if m.Components != nil {
		_, err := m.Components.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	for _, item := range m.Security {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	for _, item := range m.Tags {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	if m.ExternalDocs != nil {
		_, err := m.ExternalDocs.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	for _, item := range m.SpecificationExtension {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside Encoding objects.
func (m *Encoding) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	if m.Headers != nil {
		_, err := m.Headers.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	for _, item := range m.SpecificationExtension {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside Encodings objects.
func (m *Encodings) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	for _, item := range m.AdditionalProperties {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside Example objects.
func (m *Example) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	if m.Value != nil {
		_, err := m.Value.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	for _, item := range m.SpecificationExtension {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside ExampleOrReference objects.
func (m *ExampleOrReference) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	{
		p, ok := m.Oneof.(*ExampleOrReference_Example)
		if ok {
			_, err := p.Example.ResolveReferences(root)
			if err != nil {
				return nil, err
			}
		}
	}
	{
		p, ok := m.Oneof.(*ExampleOrReference_Reference)
		if ok {
			_, err := p.Reference.ResolveReferences(root)
			if err != nil {
				return nil, err
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside ExamplesOrReferences objects.
func (m *ExamplesOrReferences) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	for _, item := range m.AdditionalProperties {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside Expression objects.
func (m *Expression) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	for _, item := range m.AdditionalProperties {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside ExternalDocs objects.
func (m *ExternalDocs) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	for _, item := range m.SpecificationExtension {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside Header objects.
func (m *Header) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	if m.Schema != nil {
		_, err := m.Schema.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	if m.Example != nil {
		_, err := m.Example.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	if m.Examples != nil {
		_, err := m.Examples.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	if m.Content != nil {
		_, err := m.Content.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	for _, item := range m.SpecificationExtension {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside HeaderOrReference objects.
func (m *HeaderOrReference) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	{
		p, ok := m.Oneof.(*HeaderOrReference_Header)
		if ok {
			_, err := p.Header.ResolveReferences(root)
			if err != nil {
				return nil, err
			}
		}
	}
	{
		p, ok := m.Oneof.(*HeaderOrReference_Reference)
		if ok {
			_, err := p.Reference.ResolveReferences(root)
			if err != nil {
				return nil, err
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside HeadersOrReferences objects.
func (m *HeadersOrReferences) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	for _, item := range m.AdditionalProperties {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside Info objects.
func (m *Info) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	if m.Contact != nil {
		_, err := m.Contact.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	if m.License != nil {
		_, err := m.License.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	for _, item := range m.SpecificationExtension {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside ItemsItem objects.
func (m *ItemsItem) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	for _, item := range m.SchemaOrReference {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside License objects.
func (m *License) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	for _, item := range m.SpecificationExtension {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside Link objects.
func (m *Link) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	if m.Parameters != nil {
		_, err := m.Parameters.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	if m.RequestBody != nil {
		_, err := m.RequestBody.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	if m.Server != nil {
		_, err := m.Server.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	for _, item := range m.SpecificationExtension {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside LinkOrReference objects.
func (m *LinkOrReference) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	{
		p, ok := m.Oneof.(*LinkOrReference_Link)
		if ok {
			_, err := p.Link.ResolveReferences(root)
			if err != nil {
				return nil, err
			}
		}
	}
	{
		p, ok := m.Oneof.(*LinkOrReference_Reference)
		if ok {
			_, err := p.Reference.ResolveReferences(root)
			if err != nil {
				return nil, err
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside LinksOrReferences objects.
func (m *LinksOrReferences) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	for _, item := range m.AdditionalProperties {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside MediaType objects.
func (m *MediaType) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	if m.Schema != nil {
		_, err := m.Schema.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	if m.Example != nil {
		_, err := m.Example.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	if m.Examples != nil {
		_, err := m.Examples.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	if m.Encoding != nil {
		_, err := m.Encoding.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	for _, item := range m.SpecificationExtension {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside MediaTypes objects.
func (m *MediaTypes) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	for _, item := range m.AdditionalProperties {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside NamedAny objects.
func (m *NamedAny) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	if m.Value != nil {
		_, err := m.Value.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside NamedCallbackOrReference objects.
func (m *NamedCallbackOrReference) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	if m.Value != nil {
		_, err := m.Value.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside NamedEncoding objects.
func (m *NamedEncoding) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	if m.Value != nil {
		_, err := m.Value.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside NamedExampleOrReference objects.
func (m *NamedExampleOrReference) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	if m.Value != nil {
		_, err := m.Value.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside NamedHeaderOrReference objects.
func (m *NamedHeaderOrReference) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	if m.Value != nil {
		_, err := m.Value.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside NamedLinkOrReference objects.
func (m *NamedLinkOrReference) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	if m.Value != nil {
		_, err := m.Value.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside NamedMediaType objects.
func (m *NamedMediaType) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	if m.Value != nil {
		_, err := m.Value.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside NamedParameterOrReference objects.
func (m *NamedParameterOrReference) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	if m.Value != nil {
		_, err := m.Value.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside NamedPathItem objects.
func (m *NamedPathItem) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	if m.Value != nil {
		_, err := m.Value.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside NamedRequestBodyOrReference objects.
func (m *NamedRequestBodyOrReference) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	if m.Value != nil {
		_, err := m.Value.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside NamedResponseOrReference objects.
func (m *NamedResponseOrReference) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	if m.Value != nil {
		_, err := m.Value.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside NamedSchemaOrReference objects.
func (m *NamedSchemaOrReference) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	if m.Value != nil {
		_, err := m.Value.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside NamedSecuritySchemeOrReference objects.
func (m *NamedSecuritySchemeOrReference) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	if m.Value != nil {
		_, err := m.Value.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside NamedServerVariable objects.
func (m *NamedServerVariable) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	if m.Value != nil {
		_, err := m.Value.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside NamedString objects.
func (m *NamedString) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside NamedStringArray objects.
func (m *NamedStringArray) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	if m.Value != nil {
		_, err := m.Value.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside OauthFlow objects.
func (m *OauthFlow) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	if m.Scopes != nil {
		_, err := m.Scopes.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	for _, item := range m.SpecificationExtension {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside OauthFlows objects.
func (m *OauthFlows) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	if m.Implicit != nil {
		_, err := m.Implicit.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	if m.Password != nil {
		_, err := m.Password.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	if m.ClientCredentials != nil {
		_, err := m.ClientCredentials.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	if m.AuthorizationCode != nil {
		_, err := m.AuthorizationCode.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	for _, item := range m.SpecificationExtension {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside Object objects.
func (m *Object) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	for _, item := range m.AdditionalProperties {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside Operation objects.
func (m *Operation) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	if m.ExternalDocs != nil {
		_, err := m.ExternalDocs.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	for _, item := range m.Parameters {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	if m.RequestBody != nil {
		_, err := m.RequestBody.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	if m.Responses != nil {
		_, err := m.Responses.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	if m.Callbacks != nil {
		_, err := m.Callbacks.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	for _, item := range m.Security {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	for _, item := range m.Servers {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	for _, item := range m.SpecificationExtension {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside Parameter objects.
func (m *Parameter) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	if m.Schema != nil {
		_, err := m.Schema.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	if m.Example != nil {
		_, err := m.Example.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	if m.Examples != nil {
		_, err := m.Examples.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	if m.Content != nil {
		_, err := m.Content.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	for _, item := range m.SpecificationExtension {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside ParameterOrReference objects.
func (m *ParameterOrReference) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	{
		p, ok := m.Oneof.(*ParameterOrReference_Parameter)
		if ok {
			_, err := p.Parameter.ResolveReferences(root)
			if err != nil {
				return nil, err
			}
		}
	}
	{
		p, ok := m.Oneof.(*ParameterOrReference_Reference)
		if ok {
			_, err := p.Reference.ResolveReferences(root)
			if err != nil {
				return nil, err
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside ParametersOrReferences objects.
func (m *ParametersOrReferences) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	for _, item := range m.AdditionalProperties {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside PathItem objects.
func (m *PathItem) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	if m.XRef != "" {
		info, err := compiler.ReadInfoForRef(root, m.XRef)
		if err != nil {
			return nil, err
		}
		if info != nil {
			replacement, err := NewPathItem(info, nil)
			if err == nil {
				*m = *replacement
				return m.ResolveReferences(root)
			}
		}
		return info, nil
	}
	if m.Get != nil {
		_, err := m.Get.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	if m.Put != nil {
		_, err := m.Put.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	if m.Post != nil {
		_, err := m.Post.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	if m.Delete != nil {
		_, err := m.Delete.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	if m.Options != nil {
		_, err := m.Options.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	if m.Head != nil {
		_, err := m.Head.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	if m.Patch != nil {
		_, err := m.Patch.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	if m.Trace != nil {
		_, err := m.Trace.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	for _, item := range m.Servers {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	for _, item := range m.Parameters {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	for _, item := range m.SpecificationExtension {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside Paths objects.
func (m *Paths) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	for _, item := range m.Path {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	for _, item := range m.SpecificationExtension {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside Properties objects.
func (m *Properties) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	for _, item := range m.AdditionalProperties {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside Reference objects.
func (m *Reference) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	if m.XRef != "" {
		info, err := compiler.ReadInfoForRef(root, m.XRef)
		if err != nil {
			return nil, err
		}
		if info != nil {
			replacement, err := NewReference(info, nil)
			if err == nil {
				*m = *replacement
				return m.ResolveReferences(root)
			}
		}
		return info, nil
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside RequestBodiesOrReferences objects.
func (m *RequestBodiesOrReferences) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	for _, item := range m.AdditionalProperties {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside RequestBody objects.
func (m *RequestBody) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	if m.Content != nil {
		_, err := m.Content.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	for _, item := range m.SpecificationExtension {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside RequestBodyOrReference objects.
func (m *RequestBodyOrReference) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	{
		p, ok := m.Oneof.(*RequestBodyOrReference_RequestBody)
		if ok {
			_, err := p.RequestBody.ResolveReferences(root)
			if err != nil {
				return nil, err
			}
		}
	}
	{
		p, ok := m.Oneof.(*RequestBodyOrReference_Reference)
		if ok {
			_, err := p.Reference.ResolveReferences(root)
			if err != nil {
				return nil, err
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside Response objects.
func (m *Response) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	if m.Headers != nil {
		_, err := m.Headers.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	if m.Content != nil {
		_, err := m.Content.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	if m.Links != nil {
		_, err := m.Links.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	for _, item := range m.SpecificationExtension {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside ResponseOrReference objects.
func (m *ResponseOrReference) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	{
		p, ok := m.Oneof.(*ResponseOrReference_Response)
		if ok {
			_, err := p.Response.ResolveReferences(root)
			if err != nil {
				return nil, err
			}
		}
	}
	{
		p, ok := m.Oneof.(*ResponseOrReference_Reference)
		if ok {
			_, err := p.Reference.ResolveReferences(root)
			if err != nil {
				return nil, err
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside Responses objects.
func (m *Responses) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	if m.Default != nil {
		_, err := m.Default.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	for _, item := range m.ResponseOrReference {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	for _, item := range m.SpecificationExtension {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside ResponsesOrReferences objects.
func (m *ResponsesOrReferences) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	for _, item := range m.AdditionalProperties {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside Schema objects.
func (m *Schema) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	if m.Discriminator != nil {
		_, err := m.Discriminator.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	if m.Xml != nil {
		_, err := m.Xml.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	if m.ExternalDocs != nil {
		_, err := m.ExternalDocs.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	if m.Example != nil {
		_, err := m.Example.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	for _, item := range m.Enum {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	for _, item := range m.AllOf {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	for _, item := range m.OneOf {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	for _, item := range m.AnyOf {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	if m.Not != nil {
		_, err := m.Not.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	if m.Items != nil {
		_, err := m.Items.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	if m.Properties != nil {
		_, err := m.Properties.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	if m.AdditionalProperties != nil {
		_, err := m.AdditionalProperties.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	if m.Default != nil {
		_, err := m.Default.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	for _, item := range m.SpecificationExtension {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside SchemaOrReference objects.
func (m *SchemaOrReference) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	{
		p, ok := m.Oneof.(*SchemaOrReference_Schema)
		if ok {
			_, err := p.Schema.ResolveReferences(root)
			if err != nil {
				return nil, err
			}
		}
	}
	{
		p, ok := m.Oneof.(*SchemaOrReference_Reference)
		if ok {
			_, err := p.Reference.ResolveReferences(root)
			if err != nil {
				return nil, err
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside SchemasOrReferences objects.
func (m *SchemasOrReferences) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	for _, item := range m.AdditionalProperties {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside SecurityRequirement objects.
func (m *SecurityRequirement) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	for _, item := range m.AdditionalProperties {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside SecurityScheme objects.
func (m *SecurityScheme) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	if m.Flows != nil {
		_, err := m.Flows.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	for _, item := range m.SpecificationExtension {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside SecuritySchemeOrReference objects.
func (m *SecuritySchemeOrReference) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	{
		p, ok := m.Oneof.(*SecuritySchemeOrReference_SecurityScheme)
		if ok {
			_, err := p.SecurityScheme.ResolveReferences(root)
			if err != nil {
				return nil, err
			}
		}
	}
	{
		p, ok := m.Oneof.(*SecuritySchemeOrReference_Reference)
		if ok {
			_, err := p.Reference.ResolveReferences(root)
			if err != nil {
				return nil, err
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside SecuritySchemesOrReferences objects.
func (m *SecuritySchemesOrReferences) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	for _, item := range m.AdditionalProperties {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside Server objects.
func (m *Server) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	if m.Variables != nil {
		_, err := m.Variables.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	for _, item := range m.SpecificationExtension {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside ServerVariable objects.
func (m *ServerVariable) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	for _, item := range m.SpecificationExtension {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside ServerVariables objects.
func (m *ServerVariables) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	for _, item := range m.AdditionalProperties {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside SpecificationExtension objects.
func (m *SpecificationExtension) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside StringArray objects.
func (m *StringArray) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside Strings objects.
func (m *Strings) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	for _, item := range m.AdditionalProperties {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside Tag objects.
func (m *Tag) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	if m.ExternalDocs != nil {
		_, err := m.ExternalDocs.ResolveReferences(root)
		if err != nil {
			errors = append(errors, err)
		}
	}
	for _, item := range m.SpecificationExtension {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ResolveReferences resolves references found inside Xml objects.
func (m *Xml) ResolveReferences(root string) (*yaml.Node, error) {
	errors := make([]error, 0)
	for _, item := range m.SpecificationExtension {
		if item != nil {
			_, err := item.ResolveReferences(root)
			if err != nil {
				errors = append(errors, err)
			}
		}
	}
	return nil, compiler.NewErrorGroupOrNil(errors)
}

// ToRawInfo returns a description of AdditionalPropertiesItem suitable for JSON or YAML export.
func (m *AdditionalPropertiesItem) ToRawInfo() *yaml.Node {
	// ONE OF WRAPPER
	// AdditionalPropertiesItem
	// {Name:schemaOrReference Type:SchemaOrReference StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:}
	v0 := m.GetSchemaOrReference()
	if v0 != nil {
		return v0.ToRawInfo()
	}
	// {Name:boolean Type:bool StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:}
	if v1, ok := m.GetOneof().(*AdditionalPropertiesItem_Boolean); ok {
		return compiler.NewScalarNodeForBool(v1.Boolean)
	}
	return compiler.NewNullNode()
}

// ToRawInfo returns a description of Any suitable for JSON or YAML export.
func (m *Any) ToRawInfo() *yaml.Node {
	var err error
	var node yaml.Node
	err = yaml.Unmarshal([]byte(m.Yaml), &node)
	if err == nil {
		if node.Kind == yaml.DocumentNode {
			return node.Content[0]
		}
		return &node
	}
	return compiler.NewNullNode()
}

// ToRawInfo returns a description of AnyOrExpression suitable for JSON or YAML export.
func (m *AnyOrExpression) ToRawInfo() *yaml.Node {
	// ONE OF WRAPPER
	// AnyOrExpression
	// {Name:any Type:Any StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:}
	v0 := m.GetAny()
	if v0 != nil {
		return v0.ToRawInfo()
	}
	// {Name:expression Type:Expression StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:}
	v1 := m.GetExpression()
	if v1 != nil {
		return v1.ToRawInfo()
	}
	return compiler.NewNullNode()
}

// ToRawInfo returns a description of Callback suitable for JSON or YAML export.
func (m *Callback) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.Path != nil {
		for _, item := range m.Path {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	if m.SpecificationExtension != nil {
		for _, item := range m.SpecificationExtension {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of CallbackOrReference suitable for JSON or YAML export.
func (m *CallbackOrReference) ToRawInfo() *yaml.Node {
	// ONE OF WRAPPER
	// CallbackOrReference
	// {Name:callback Type:Callback StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:}
	v0 := m.GetCallback()
	if v0 != nil {
		return v0.ToRawInfo()
	}
	// {Name:reference Type:Reference StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:}
	v1 := m.GetReference()
	if v1 != nil {
		return v1.ToRawInfo()
	}
	return compiler.NewNullNode()
}

// ToRawInfo returns a description of CallbacksOrReferences suitable for JSON or YAML export.
func (m *CallbacksOrReferences) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.AdditionalProperties != nil {
		for _, item := range m.AdditionalProperties {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of Components suitable for JSON or YAML export.
func (m *Components) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.Schemas != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("schemas"))
		info.Content = append(info.Content, m.Schemas.ToRawInfo())
	}
	if m.Responses != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("responses"))
		info.Content = append(info.Content, m.Responses.ToRawInfo())
	}
	if m.Parameters != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("parameters"))
		info.Content = append(info.Content, m.Parameters.ToRawInfo())
	}
	if m.Examples != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("examples"))
		info.Content = append(info.Content, m.Examples.ToRawInfo())
	}
	if m.RequestBodies != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("requestBodies"))
		info.Content = append(info.Content, m.RequestBodies.ToRawInfo())
	}
	if m.Headers != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("headers"))
		info.Content = append(info.Content, m.Headers.ToRawInfo())
	}
	if m.SecuritySchemes != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("securitySchemes"))
		info.Content = append(info.Content, m.SecuritySchemes.ToRawInfo())
	}
	if m.Links != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("links"))
		info.Content = append(info.Content, m.Links.ToRawInfo())
	}
	if m.Callbacks != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("callbacks"))
		info.Content = append(info.Content, m.Callbacks.ToRawInfo())
	}
	if m.SpecificationExtension != nil {
		for _, item := range m.SpecificationExtension {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of Contact suitable for JSON or YAML export.
func (m *Contact) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.Name != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("name"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Name))
	}
	if m.Url != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("url"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Url))
	}
	if m.Email != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("email"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Email))
	}
	if m.SpecificationExtension != nil {
		for _, item := range m.SpecificationExtension {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of DefaultType suitable for JSON or YAML export.
func (m *DefaultType) ToRawInfo() *yaml.Node {
	// ONE OF WRAPPER
	// DefaultType
	// {Name:number Type:float StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:}
	if v0, ok := m.GetOneof().(*DefaultType_Number); ok {
		return compiler.NewScalarNodeForFloat(v0.Number)
	}
	// {Name:boolean Type:bool StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:}
	if v1, ok := m.GetOneof().(*DefaultType_Boolean); ok {
		return compiler.NewScalarNodeForBool(v1.Boolean)
	}
	// {Name:string Type:string StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:}
	if v2, ok := m.GetOneof().(*DefaultType_String_); ok {
		return compiler.NewScalarNodeForString(v2.String_)
	}
	return compiler.NewNullNode()
}

// ToRawInfo returns a description of Discriminator suitable for JSON or YAML export.
func (m *Discriminator) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	// always include this required field.
	info.Content = append(info.Content, compiler.NewScalarNodeForString("propertyName"))
	info.Content = append(info.Content, compiler.NewScalarNodeForString(m.PropertyName))
	if m.Mapping != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("mapping"))
		info.Content = append(info.Content, m.Mapping.ToRawInfo())
	}
	if m.SpecificationExtension != nil {
		for _, item := range m.SpecificationExtension {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of Document suitable for JSON or YAML export.
func (m *Document) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	// always include this required field.
	info.Content = append(info.Content, compiler.NewScalarNodeForString("openapi"))
	info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Openapi))
	// always include this required field.
	info.Content = append(info.Content, compiler.NewScalarNodeForString("info"))
	info.Content = append(info.Content, m.Info.ToRawInfo())
	if len(m.Servers) != 0 {
		items := compiler.NewSequenceNode()
		for _, item := range m.Servers {
			items.Content = append(items.Content, item.ToRawInfo())
		}
		info.Content = append(info.Content, compiler.NewScalarNodeForString("servers"))
		info.Content = append(info.Content, items)
	}
	// always include this required field.
	info.Content = append(info.Content, compiler.NewScalarNodeForString("paths"))
	info.Content = append(info.Content, m.Paths.ToRawInfo())
	if m.Components != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("components"))
		info.Content = append(info.Content, m.Components.ToRawInfo())
	}
	if len(m.Security) != 0 {
		items := compiler.NewSequenceNode()
		for _, item := range m.Security {
			items.Content = append(items.Content, item.ToRawInfo())
		}
		info.Content = append(info.Content, compiler.NewScalarNodeForString("security"))
		info.Content = append(info.Content, items)
	}
	if len(m.Tags) != 0 {
		items := compiler.NewSequenceNode()
		for _, item := range m.Tags {
			items.Content = append(items.Content, item.ToRawInfo())
		}
		info.Content = append(info.Content, compiler.NewScalarNodeForString("tags"))
		info.Content = append(info.Content, items)
	}
	if m.ExternalDocs != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("externalDocs"))
		info.Content = append(info.Content, m.ExternalDocs.ToRawInfo())
	}
	if m.SpecificationExtension != nil {
		for _, item := range m.SpecificationExtension {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of Encoding suitable for JSON or YAML export.
func (m *Encoding) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.ContentType != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("contentType"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.ContentType))
	}
	if m.Headers != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("headers"))
		info.Content = append(info.Content, m.Headers.ToRawInfo())
	}
	if m.Style != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("style"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Style))
	}
	if m.Explode != false {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("explode"))
		info.Content = append(info.Content, compiler.NewScalarNodeForBool(m.Explode))
	}
	if m.AllowReserved != false {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("allowReserved"))
		info.Content = append(info.Content, compiler.NewScalarNodeForBool(m.AllowReserved))
	}
	if m.SpecificationExtension != nil {
		for _, item := range m.SpecificationExtension {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of Encodings suitable for JSON or YAML export.
func (m *Encodings) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.AdditionalProperties != nil {
		for _, item := range m.AdditionalProperties {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of Example suitable for JSON or YAML export.
func (m *Example) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.Summary != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("summary"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Summary))
	}
	if m.Description != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("description"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Description))
	}
	if m.Value != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("value"))
		info.Content = append(info.Content, m.Value.ToRawInfo())
	}
	if m.ExternalValue != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("externalValue"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.ExternalValue))
	}
	if m.SpecificationExtension != nil {
		for _, item := range m.SpecificationExtension {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of ExampleOrReference suitable for JSON or YAML export.
func (m *ExampleOrReference) ToRawInfo() *yaml.Node {
	// ONE OF WRAPPER
	// ExampleOrReference
	// {Name:example Type:Example StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:}
	v0 := m.GetExample()
	if v0 != nil {
		return v0.ToRawInfo()
	}
	// {Name:reference Type:Reference StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:}
	v1 := m.GetReference()
	if v1 != nil {
		return v1.ToRawInfo()
	}
	return compiler.NewNullNode()
}

// ToRawInfo returns a description of ExamplesOrReferences suitable for JSON or YAML export.
func (m *ExamplesOrReferences) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.AdditionalProperties != nil {
		for _, item := range m.AdditionalProperties {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of Expression suitable for JSON or YAML export.
func (m *Expression) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.AdditionalProperties != nil {
		for _, item := range m.AdditionalProperties {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of ExternalDocs suitable for JSON or YAML export.
func (m *ExternalDocs) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.Description != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("description"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Description))
	}
	// always include this required field.
	info.Content = append(info.Content, compiler.NewScalarNodeForString("url"))
	info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Url))
	if m.SpecificationExtension != nil {
		for _, item := range m.SpecificationExtension {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of Header suitable for JSON or YAML export.
func (m *Header) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.Description != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("description"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Description))
	}
	if m.Required != false {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("required"))
		info.Content = append(info.Content, compiler.NewScalarNodeForBool(m.Required))
	}
	if m.Deprecated != false {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("deprecated"))
		info.Content = append(info.Content, compiler.NewScalarNodeForBool(m.Deprecated))
	}
	if m.AllowEmptyValue != false {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("allowEmptyValue"))
		info.Content = append(info.Content, compiler.NewScalarNodeForBool(m.AllowEmptyValue))
	}
	if m.Style != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("style"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Style))
	}
	if m.Explode != false {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("explode"))
		info.Content = append(info.Content, compiler.NewScalarNodeForBool(m.Explode))
	}
	if m.AllowReserved != false {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("allowReserved"))
		info.Content = append(info.Content, compiler.NewScalarNodeForBool(m.AllowReserved))
	}
	if m.Schema != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("schema"))
		info.Content = append(info.Content, m.Schema.ToRawInfo())
	}
	if m.Example != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("example"))
		info.Content = append(info.Content, m.Example.ToRawInfo())
	}
	if m.Examples != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("examples"))
		info.Content = append(info.Content, m.Examples.ToRawInfo())
	}
	if m.Content != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("content"))
		info.Content = append(info.Content, m.Content.ToRawInfo())
	}
	if m.SpecificationExtension != nil {
		for _, item := range m.SpecificationExtension {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of HeaderOrReference suitable for JSON or YAML export.
func (m *HeaderOrReference) ToRawInfo() *yaml.Node {
	// ONE OF WRAPPER
	// HeaderOrReference
	// {Name:header Type:Header StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:}
	v0 := m.GetHeader()
	if v0 != nil {
		return v0.ToRawInfo()
	}
	// {Name:reference Type:Reference StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:}
	v1 := m.GetReference()
	if v1 != nil {
		return v1.ToRawInfo()
	}
	return compiler.NewNullNode()
}

// ToRawInfo returns a description of HeadersOrReferences suitable for JSON or YAML export.
func (m *HeadersOrReferences) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.AdditionalProperties != nil {
		for _, item := range m.AdditionalProperties {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of Info suitable for JSON or YAML export.
func (m *Info) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	// always include this required field.
	info.Content = append(info.Content, compiler.NewScalarNodeForString("title"))
	info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Title))
	if m.Description != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("description"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Description))
	}
	if m.TermsOfService != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("termsOfService"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.TermsOfService))
	}
	if m.Contact != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("contact"))
		info.Content = append(info.Content, m.Contact.ToRawInfo())
	}
	if m.License != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("license"))
		info.Content = append(info.Content, m.License.ToRawInfo())
	}
	// always include this required field.
	info.Content = append(info.Content, compiler.NewScalarNodeForString("version"))
	info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Version))
	if m.Summary != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("summary"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Summary))
	}
	if m.SpecificationExtension != nil {
		for _, item := range m.SpecificationExtension {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of ItemsItem suitable for JSON or YAML export.
func (m *ItemsItem) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if len(m.SchemaOrReference) != 0 {
		items := compiler.NewSequenceNode()
		for _, item := range m.SchemaOrReference {
			items.Content = append(items.Content, item.ToRawInfo())
		}
		info.Content = append(info.Content, compiler.NewScalarNodeForString("schemaOrReference"))
		info.Content = append(info.Content, items)
	}
	return info
}

// ToRawInfo returns a description of License suitable for JSON or YAML export.
func (m *License) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	// always include this required field.
	info.Content = append(info.Content, compiler.NewScalarNodeForString("name"))
	info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Name))
	if m.Url != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("url"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Url))
	}
	if m.SpecificationExtension != nil {
		for _, item := range m.SpecificationExtension {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of Link suitable for JSON or YAML export.
func (m *Link) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.OperationRef != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("operationRef"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.OperationRef))
	}
	if m.OperationId != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("operationId"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.OperationId))
	}
	if m.Parameters != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("parameters"))
		info.Content = append(info.Content, m.Parameters.ToRawInfo())
	}
	if m.RequestBody != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("requestBody"))
		info.Content = append(info.Content, m.RequestBody.ToRawInfo())
	}
	if m.Description != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("description"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Description))
	}
	if m.Server != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("server"))
		info.Content = append(info.Content, m.Server.ToRawInfo())
	}
	if m.SpecificationExtension != nil {
		for _, item := range m.SpecificationExtension {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of LinkOrReference suitable for JSON or YAML export.
func (m *LinkOrReference) ToRawInfo() *yaml.Node {
	// ONE OF WRAPPER
	// LinkOrReference
	// {Name:link Type:Link StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:}
	v0 := m.GetLink()
	if v0 != nil {
		return v0.ToRawInfo()
	}
	// {Name:reference Type:Reference StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:}
	v1 := m.GetReference()
	if v1 != nil {
		return v1.ToRawInfo()
	}
	return compiler.NewNullNode()
}

// ToRawInfo returns a description of LinksOrReferences suitable for JSON or YAML export.
func (m *LinksOrReferences) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.AdditionalProperties != nil {
		for _, item := range m.AdditionalProperties {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of MediaType suitable for JSON or YAML export.
func (m *MediaType) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.Schema != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("schema"))
		info.Content = append(info.Content, m.Schema.ToRawInfo())
	}
	if m.Example != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("example"))
		info.Content = append(info.Content, m.Example.ToRawInfo())
	}
	if m.Examples != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("examples"))
		info.Content = append(info.Content, m.Examples.ToRawInfo())
	}
	if m.Encoding != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("encoding"))
		info.Content = append(info.Content, m.Encoding.ToRawInfo())
	}
	if m.SpecificationExtension != nil {
		for _, item := range m.SpecificationExtension {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of MediaTypes suitable for JSON or YAML export.
func (m *MediaTypes) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.AdditionalProperties != nil {
		for _, item := range m.AdditionalProperties {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of NamedAny suitable for JSON or YAML export.
func (m *NamedAny) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.Name != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("name"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Name))
	}
	if m.Value != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("value"))
		info.Content = append(info.Content, m.Value.ToRawInfo())
	}
	return info
}

// ToRawInfo returns a description of NamedCallbackOrReference suitable for JSON or YAML export.
func (m *NamedCallbackOrReference) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.Name != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("name"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Name))
	}
	// &{Name:value Type:CallbackOrReference StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:Mapped value}
	return info
}

// ToRawInfo returns a description of NamedEncoding suitable for JSON or YAML export.
func (m *NamedEncoding) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.Name != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("name"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Name))
	}
	// &{Name:value Type:Encoding StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:Mapped value}
	return info
}

// ToRawInfo returns a description of NamedExampleOrReference suitable for JSON or YAML export.
func (m *NamedExampleOrReference) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.Name != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("name"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Name))
	}
	// &{Name:value Type:ExampleOrReference StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:Mapped value}
	return info
}

// ToRawInfo returns a description of NamedHeaderOrReference suitable for JSON or YAML export.
func (m *NamedHeaderOrReference) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.Name != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("name"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Name))
	}
	// &{Name:value Type:HeaderOrReference StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:Mapped value}
	return info
}

// ToRawInfo returns a description of NamedLinkOrReference suitable for JSON or YAML export.
func (m *NamedLinkOrReference) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.Name != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("name"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Name))
	}
	// &{Name:value Type:LinkOrReference StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:Mapped value}
	return info
}

// ToRawInfo returns a description of NamedMediaType suitable for JSON or YAML export.
func (m *NamedMediaType) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.Name != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("name"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Name))
	}
	// &{Name:value Type:MediaType StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:Mapped value}
	return info
}

// ToRawInfo returns a description of NamedParameterOrReference suitable for JSON or YAML export.
func (m *NamedParameterOrReference) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.Name != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("name"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Name))
	}
	// &{Name:value Type:ParameterOrReference StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:Mapped value}
	return info
}

// ToRawInfo returns a description of NamedPathItem suitable for JSON or YAML export.
func (m *NamedPathItem) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.Name != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("name"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Name))
	}
	// &{Name:value Type:PathItem StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:Mapped value}
	return info
}

// ToRawInfo returns a description of NamedRequestBodyOrReference suitable for JSON or YAML export.
func (m *NamedRequestBodyOrReference) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.Name != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("name"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Name))
	}
	// &{Name:value Type:RequestBodyOrReference StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:Mapped value}
	return info
}

// ToRawInfo returns a description of NamedResponseOrReference suitable for JSON or YAML export.
func (m *NamedResponseOrReference) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.Name != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("name"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Name))
	}
	// &{Name:value Type:ResponseOrReference StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:Mapped value}
	return info
}

// ToRawInfo returns a description of NamedSchemaOrReference suitable for JSON or YAML export.
func (m *NamedSchemaOrReference) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.Name != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("name"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Name))
	}
	// &{Name:value Type:SchemaOrReference StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:Mapped value}
	return info
}

// ToRawInfo returns a description of NamedSecuritySchemeOrReference suitable for JSON or YAML export.
func (m *NamedSecuritySchemeOrReference) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.Name != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("name"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Name))
	}
	// &{Name:value Type:SecuritySchemeOrReference StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:Mapped value}
	return info
}

// ToRawInfo returns a description of NamedServerVariable suitable for JSON or YAML export.
func (m *NamedServerVariable) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.Name != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("name"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Name))
	}
	// &{Name:value Type:ServerVariable StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:Mapped value}
	return info
}

// ToRawInfo returns a description of NamedString suitable for JSON or YAML export.
func (m *NamedString) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.Name != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("name"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Name))
	}
	if m.Value != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("value"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Value))
	}
	return info
}

// ToRawInfo returns a description of NamedStringArray suitable for JSON or YAML export.
func (m *NamedStringArray) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.Name != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("name"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Name))
	}
	// &{Name:value Type:StringArray StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:Mapped value}
	return info
}

// ToRawInfo returns a description of OauthFlow suitable for JSON or YAML export.
func (m *OauthFlow) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.AuthorizationUrl != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("authorizationUrl"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.AuthorizationUrl))
	}
	if m.TokenUrl != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("tokenUrl"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.TokenUrl))
	}
	if m.RefreshUrl != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("refreshUrl"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.RefreshUrl))
	}
	if m.Scopes != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("scopes"))
		info.Content = append(info.Content, m.Scopes.ToRawInfo())
	}
	if m.SpecificationExtension != nil {
		for _, item := range m.SpecificationExtension {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of OauthFlows suitable for JSON or YAML export.
func (m *OauthFlows) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.Implicit != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("implicit"))
		info.Content = append(info.Content, m.Implicit.ToRawInfo())
	}
	if m.Password != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("password"))
		info.Content = append(info.Content, m.Password.ToRawInfo())
	}
	if m.ClientCredentials != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("clientCredentials"))
		info.Content = append(info.Content, m.ClientCredentials.ToRawInfo())
	}
	if m.AuthorizationCode != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("authorizationCode"))
		info.Content = append(info.Content, m.AuthorizationCode.ToRawInfo())
	}
	if m.SpecificationExtension != nil {
		for _, item := range m.SpecificationExtension {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of Object suitable for JSON or YAML export.
func (m *Object) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.AdditionalProperties != nil {
		for _, item := range m.AdditionalProperties {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of Operation suitable for JSON or YAML export.
func (m *Operation) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if len(m.Tags) != 0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("tags"))
		info.Content = append(info.Content, compiler.NewSequenceNodeForStringArray(m.Tags))
	}
	if m.Summary != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("summary"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Summary))
	}
	if m.Description != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("description"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Description))
	}
	if m.ExternalDocs != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("externalDocs"))
		info.Content = append(info.Content, m.ExternalDocs.ToRawInfo())
	}
	if m.OperationId != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("operationId"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.OperationId))
	}
	if len(m.Parameters) != 0 {
		items := compiler.NewSequenceNode()
		for _, item := range m.Parameters {
			items.Content = append(items.Content, item.ToRawInfo())
		}
		info.Content = append(info.Content, compiler.NewScalarNodeForString("parameters"))
		info.Content = append(info.Content, items)
	}
	if m.RequestBody != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("requestBody"))
		info.Content = append(info.Content, m.RequestBody.ToRawInfo())
	}
	// always include this required field.
	info.Content = append(info.Content, compiler.NewScalarNodeForString("responses"))
	info.Content = append(info.Content, m.Responses.ToRawInfo())
	if m.Callbacks != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("callbacks"))
		info.Content = append(info.Content, m.Callbacks.ToRawInfo())
	}
	if m.Deprecated != false {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("deprecated"))
		info.Content = append(info.Content, compiler.NewScalarNodeForBool(m.Deprecated))
	}
	if len(m.Security) != 0 {
		items := compiler.NewSequenceNode()
		for _, item := range m.Security {
			items.Content = append(items.Content, item.ToRawInfo())
		}
		info.Content = append(info.Content, compiler.NewScalarNodeForString("security"))
		info.Content = append(info.Content, items)
	}
	if len(m.Servers) != 0 {
		items := compiler.NewSequenceNode()
		for _, item := range m.Servers {
			items.Content = append(items.Content, item.ToRawInfo())
		}
		info.Content = append(info.Content, compiler.NewScalarNodeForString("servers"))
		info.Content = append(info.Content, items)
	}
	if m.SpecificationExtension != nil {
		for _, item := range m.SpecificationExtension {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of Parameter suitable for JSON or YAML export.
func (m *Parameter) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	// always include this required field.
	info.Content = append(info.Content, compiler.NewScalarNodeForString("name"))
	info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Name))
	// always include this required field.
	info.Content = append(info.Content, compiler.NewScalarNodeForString("in"))
	info.Content = append(info.Content, compiler.NewScalarNodeForString(m.In))
	if m.Description != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("description"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Description))
	}
	if m.Required != false {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("required"))
		info.Content = append(info.Content, compiler.NewScalarNodeForBool(m.Required))
	}
	if m.Deprecated != false {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("deprecated"))
		info.Content = append(info.Content, compiler.NewScalarNodeForBool(m.Deprecated))
	}
	if m.AllowEmptyValue != false {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("allowEmptyValue"))
		info.Content = append(info.Content, compiler.NewScalarNodeForBool(m.AllowEmptyValue))
	}
	if m.Style != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("style"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Style))
	}
	if m.Explode != false {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("explode"))
		info.Content = append(info.Content, compiler.NewScalarNodeForBool(m.Explode))
	}
	if m.AllowReserved != false {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("allowReserved"))
		info.Content = append(info.Content, compiler.NewScalarNodeForBool(m.AllowReserved))
	}
	if m.Schema != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("schema"))
		info.Content = append(info.Content, m.Schema.ToRawInfo())
	}
	if m.Example != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("example"))
		info.Content = append(info.Content, m.Example.ToRawInfo())
	}
	if m.Examples != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("examples"))
		info.Content = append(info.Content, m.Examples.ToRawInfo())
	}
	if m.Content != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("content"))
		info.Content = append(info.Content, m.Content.ToRawInfo())
	}
	if m.SpecificationExtension != nil {
		for _, item := range m.SpecificationExtension {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of ParameterOrReference suitable for JSON or YAML export.
func (m *ParameterOrReference) ToRawInfo() *yaml.Node {
	// ONE OF WRAPPER
	// ParameterOrReference
	// {Name:parameter Type:Parameter StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:}
	v0 := m.GetParameter()
	if v0 != nil {
		return v0.ToRawInfo()
	}
	// {Name:reference Type:Reference StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:}
	v1 := m.GetReference()
	if v1 != nil {
		return v1.ToRawInfo()
	}
	return compiler.NewNullNode()
}

// ToRawInfo returns a description of ParametersOrReferences suitable for JSON or YAML export.
func (m *ParametersOrReferences) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.AdditionalProperties != nil {
		for _, item := range m.AdditionalProperties {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of PathItem suitable for JSON or YAML export.
func (m *PathItem) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.XRef != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("$ref"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.XRef))
	}
	if m.Summary != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("summary"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Summary))
	}
	if m.Description != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("description"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Description))
	}
	if m.Get != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("get"))
		info.Content = append(info.Content, m.Get.ToRawInfo())
	}
	if m.Put != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("put"))
		info.Content = append(info.Content, m.Put.ToRawInfo())
	}
	if m.Post != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("post"))
		info.Content = append(info.Content, m.Post.ToRawInfo())
	}
	if m.Delete != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("delete"))
		info.Content = append(info.Content, m.Delete.ToRawInfo())
	}
	if m.Options != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("options"))
		info.Content = append(info.Content, m.Options.ToRawInfo())
	}
	if m.Head != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("head"))
		info.Content = append(info.Content, m.Head.ToRawInfo())
	}
	if m.Patch != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("patch"))
		info.Content = append(info.Content, m.Patch.ToRawInfo())
	}
	if m.Trace != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("trace"))
		info.Content = append(info.Content, m.Trace.ToRawInfo())
	}
	if len(m.Servers) != 0 {
		items := compiler.NewSequenceNode()
		for _, item := range m.Servers {
			items.Content = append(items.Content, item.ToRawInfo())
		}
		info.Content = append(info.Content, compiler.NewScalarNodeForString("servers"))
		info.Content = append(info.Content, items)
	}
	if len(m.Parameters) != 0 {
		items := compiler.NewSequenceNode()
		for _, item := range m.Parameters {
			items.Content = append(items.Content, item.ToRawInfo())
		}
		info.Content = append(info.Content, compiler.NewScalarNodeForString("parameters"))
		info.Content = append(info.Content, items)
	}
	if m.SpecificationExtension != nil {
		for _, item := range m.SpecificationExtension {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of Paths suitable for JSON or YAML export.
func (m *Paths) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.Path != nil {
		for _, item := range m.Path {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	if m.SpecificationExtension != nil {
		for _, item := range m.SpecificationExtension {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of Properties suitable for JSON or YAML export.
func (m *Properties) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.AdditionalProperties != nil {
		for _, item := range m.AdditionalProperties {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of Reference suitable for JSON or YAML export.
func (m *Reference) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	// always include this required field.
	info.Content = append(info.Content, compiler.NewScalarNodeForString("$ref"))
	info.Content = append(info.Content, compiler.NewScalarNodeForString(m.XRef))
	if m.Summary != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("summary"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Summary))
	}
	if m.Description != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("description"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Description))
	}
	return info
}

// ToRawInfo returns a description of RequestBodiesOrReferences suitable for JSON or YAML export.
func (m *RequestBodiesOrReferences) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.AdditionalProperties != nil {
		for _, item := range m.AdditionalProperties {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of RequestBody suitable for JSON or YAML export.
func (m *RequestBody) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.Description != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("description"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Description))
	}
	// always include this required field.
	info.Content = append(info.Content, compiler.NewScalarNodeForString("content"))
	info.Content = append(info.Content, m.Content.ToRawInfo())
	if m.Required != false {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("required"))
		info.Content = append(info.Content, compiler.NewScalarNodeForBool(m.Required))
	}
	if m.SpecificationExtension != nil {
		for _, item := range m.SpecificationExtension {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of RequestBodyOrReference suitable for JSON or YAML export.
func (m *RequestBodyOrReference) ToRawInfo() *yaml.Node {
	// ONE OF WRAPPER
	// RequestBodyOrReference
	// {Name:requestBody Type:RequestBody StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:}
	v0 := m.GetRequestBody()
	if v0 != nil {
		return v0.ToRawInfo()
	}
	// {Name:reference Type:Reference StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:}
	v1 := m.GetReference()
	if v1 != nil {
		return v1.ToRawInfo()
	}
	return compiler.NewNullNode()
}

// ToRawInfo returns a description of Response suitable for JSON or YAML export.
func (m *Response) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	// always include this required field.
	info.Content = append(info.Content, compiler.NewScalarNodeForString("description"))
	info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Description))
	if m.Headers != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("headers"))
		info.Content = append(info.Content, m.Headers.ToRawInfo())
	}
	if m.Content != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("content"))
		info.Content = append(info.Content, m.Content.ToRawInfo())
	}
	if m.Links != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("links"))
		info.Content = append(info.Content, m.Links.ToRawInfo())
	}
	if m.SpecificationExtension != nil {
		for _, item := range m.SpecificationExtension {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of ResponseOrReference suitable for JSON or YAML export.
func (m *ResponseOrReference) ToRawInfo() *yaml.Node {
	// ONE OF WRAPPER
	// ResponseOrReference
	// {Name:response Type:Response StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:}
	v0 := m.GetResponse()
	if v0 != nil {
		return v0.ToRawInfo()
	}
	// {Name:reference Type:Reference StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:}
	v1 := m.GetReference()
	if v1 != nil {
		return v1.ToRawInfo()
	}
	return compiler.NewNullNode()
}

// ToRawInfo returns a description of Responses suitable for JSON or YAML export.
func (m *Responses) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.Default != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("default"))
		info.Content = append(info.Content, m.Default.ToRawInfo())
	}
	if m.ResponseOrReference != nil {
		for _, item := range m.ResponseOrReference {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	if m.SpecificationExtension != nil {
		for _, item := range m.SpecificationExtension {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of ResponsesOrReferences suitable for JSON or YAML export.
func (m *ResponsesOrReferences) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.AdditionalProperties != nil {
		for _, item := range m.AdditionalProperties {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of Schema suitable for JSON or YAML export.
func (m *Schema) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.Nullable != false {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("nullable"))
		info.Content = append(info.Content, compiler.NewScalarNodeForBool(m.Nullable))
	}
	if m.Discriminator != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("discriminator"))
		info.Content = append(info.Content, m.Discriminator.ToRawInfo())
	}
	if m.ReadOnly != false {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("readOnly"))
		info.Content = append(info.Content, compiler.NewScalarNodeForBool(m.ReadOnly))
	}
	if m.WriteOnly != false {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("writeOnly"))
		info.Content = append(info.Content, compiler.NewScalarNodeForBool(m.WriteOnly))
	}
	if m.Xml != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("xml"))
		info.Content = append(info.Content, m.Xml.ToRawInfo())
	}
	if m.ExternalDocs != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("externalDocs"))
		info.Content = append(info.Content, m.ExternalDocs.ToRawInfo())
	}
	if m.Example != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("example"))
		info.Content = append(info.Content, m.Example.ToRawInfo())
	}
	if m.Deprecated != false {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("deprecated"))
		info.Content = append(info.Content, compiler.NewScalarNodeForBool(m.Deprecated))
	}
	if m.Title != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("title"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Title))
	}
	if m.MultipleOf != 0.0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("multipleOf"))
		info.Content = append(info.Content, compiler.NewScalarNodeForFloat(m.MultipleOf))
	}
	if m.Maximum != 0.0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("maximum"))
		info.Content = append(info.Content, compiler.NewScalarNodeForFloat(m.Maximum))
	}
	if m.ExclusiveMaximum != false {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("exclusiveMaximum"))
		info.Content = append(info.Content, compiler.NewScalarNodeForBool(m.ExclusiveMaximum))
	}
	if m.Minimum != 0.0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("minimum"))
		info.Content = append(info.Content, compiler.NewScalarNodeForFloat(m.Minimum))
	}
	if m.ExclusiveMinimum != false {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("exclusiveMinimum"))
		info.Content = append(info.Content, compiler.NewScalarNodeForBool(m.ExclusiveMinimum))
	}
	if m.MaxLength != 0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("maxLength"))
		info.Content = append(info.Content, compiler.NewScalarNodeForInt(m.MaxLength))
	}
	if m.MinLength != 0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("minLength"))
		info.Content = append(info.Content, compiler.NewScalarNodeForInt(m.MinLength))
	}
	if m.Pattern != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("pattern"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Pattern))
	}
	if m.MaxItems != 0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("maxItems"))
		info.Content = append(info.Content, compiler.NewScalarNodeForInt(m.MaxItems))
	}
	if m.MinItems != 0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("minItems"))
		info.Content = append(info.Content, compiler.NewScalarNodeForInt(m.MinItems))
	}
	if m.UniqueItems != false {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("uniqueItems"))
		info.Content = append(info.Content, compiler.NewScalarNodeForBool(m.UniqueItems))
	}
	if m.MaxProperties != 0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("maxProperties"))
		info.Content = append(info.Content, compiler.NewScalarNodeForInt(m.MaxProperties))
	}
	if m.MinProperties != 0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("minProperties"))
		info.Content = append(info.Content, compiler.NewScalarNodeForInt(m.MinProperties))
	}
	if len(m.Required) != 0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("required"))
		info.Content = append(info.Content, compiler.NewSequenceNodeForStringArray(m.Required))
	}
	if len(m.Enum) != 0 {
		items := compiler.NewSequenceNode()
		for _, item := range m.Enum {
			items.Content = append(items.Content, item.ToRawInfo())
		}
		info.Content = append(info.Content, compiler.NewScalarNodeForString("enum"))
		info.Content = append(info.Content, items)
	}
	if m.Type != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("type"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Type))
	}
	if len(m.AllOf) != 0 {
		items := compiler.NewSequenceNode()
		for _, item := range m.AllOf {
			items.Content = append(items.Content, item.ToRawInfo())
		}
		info.Content = append(info.Content, compiler.NewScalarNodeForString("allOf"))
		info.Content = append(info.Content, items)
	}
	if len(m.OneOf) != 0 {
		items := compiler.NewSequenceNode()
		for _, item := range m.OneOf {
			items.Content = append(items.Content, item.ToRawInfo())
		}
		info.Content = append(info.Content, compiler.NewScalarNodeForString("oneOf"))
		info.Content = append(info.Content, items)
	}
	if len(m.AnyOf) != 0 {
		items := compiler.NewSequenceNode()
		for _, item := range m.AnyOf {
			items.Content = append(items.Content, item.ToRawInfo())
		}
		info.Content = append(info.Content, compiler.NewScalarNodeForString("anyOf"))
		info.Content = append(info.Content, items)
	}
	if m.Not != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("not"))
		info.Content = append(info.Content, m.Not.ToRawInfo())
	}
	if m.Items != nil {
		items := compiler.NewSequenceNode()
		for _, item := range m.Items.SchemaOrReference {
			items.Content = append(items.Content, item.ToRawInfo())
		}
		if len(items.Content) == 1 {
			items = items.Content[0]
		}
		info.Content = append(info.Content, compiler.NewScalarNodeForString("items"))
		info.Content = append(info.Content, items)
	}
	if m.Properties != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("properties"))
		info.Content = append(info.Content, m.Properties.ToRawInfo())
	}
	if m.AdditionalProperties != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("additionalProperties"))
		info.Content = append(info.Content, m.AdditionalProperties.ToRawInfo())
	}
	if m.Default != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("default"))
		info.Content = append(info.Content, m.Default.ToRawInfo())
	}
	if m.Description != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("description"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Description))
	}
	if m.Format != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("format"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Format))
	}
	if m.SpecificationExtension != nil {
		for _, item := range m.SpecificationExtension {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of SchemaOrReference suitable for JSON or YAML export.
func (m *SchemaOrReference) ToRawInfo() *yaml.Node {
	// ONE OF WRAPPER
	// SchemaOrReference
	// {Name:schema Type:Schema StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:}
	v0 := m.GetSchema()
	if v0 != nil {
		return v0.ToRawInfo()
	}
	// {Name:reference Type:Reference StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:}
	v1 := m.GetReference()
	if v1 != nil {
		return v1.ToRawInfo()
	}
	return compiler.NewNullNode()
}

// ToRawInfo returns a description of SchemasOrReferences suitable for JSON or YAML export.
func (m *SchemasOrReferences) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.AdditionalProperties != nil {
		for _, item := range m.AdditionalProperties {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of SecurityRequirement suitable for JSON or YAML export.
func (m *SecurityRequirement) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.AdditionalProperties != nil {
		for _, item := range m.AdditionalProperties {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of SecurityScheme suitable for JSON or YAML export.
func (m *SecurityScheme) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	// always include this required field.
	info.Content = append(info.Content, compiler.NewScalarNodeForString("type"))
	info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Type))
	if m.Description != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("description"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Description))
	}
	if m.Name != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("name"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Name))
	}
	if m.In != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("in"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.In))
	}
	if m.Scheme != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("scheme"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Scheme))
	}
	if m.BearerFormat != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("bearerFormat"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.BearerFormat))
	}
	if m.Flows != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("flows"))
		info.Content = append(info.Content, m.Flows.ToRawInfo())
	}
	if m.OpenIdConnectUrl != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("openIdConnectUrl"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.OpenIdConnectUrl))
	}
	if m.SpecificationExtension != nil {
		for _, item := range m.SpecificationExtension {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of SecuritySchemeOrReference suitable for JSON or YAML export.
func (m *SecuritySchemeOrReference) ToRawInfo() *yaml.Node {
	// ONE OF WRAPPER
	// SecuritySchemeOrReference
	// {Name:securityScheme Type:SecurityScheme StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:}
	v0 := m.GetSecurityScheme()
	if v0 != nil {
		return v0.ToRawInfo()
	}
	// {Name:reference Type:Reference StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:}
	v1 := m.GetReference()
	if v1 != nil {
		return v1.ToRawInfo()
	}
	return compiler.NewNullNode()
}

// ToRawInfo returns a description of SecuritySchemesOrReferences suitable for JSON or YAML export.
func (m *SecuritySchemesOrReferences) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.AdditionalProperties != nil {
		for _, item := range m.AdditionalProperties {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of Server suitable for JSON or YAML export.
func (m *Server) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	// always include this required field.
	info.Content = append(info.Content, compiler.NewScalarNodeForString("url"))
	info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Url))
	if m.Description != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("description"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Description))
	}
	if m.Variables != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("variables"))
		info.Content = append(info.Content, m.Variables.ToRawInfo())
	}
	if m.SpecificationExtension != nil {
		for _, item := range m.SpecificationExtension {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of ServerVariable suitable for JSON or YAML export.
func (m *ServerVariable) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if len(m.Enum) != 0 {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("enum"))
		info.Content = append(info.Content, compiler.NewSequenceNodeForStringArray(m.Enum))
	}
	// always include this required field.
	info.Content = append(info.Content, compiler.NewScalarNodeForString("default"))
	info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Default))
	if m.Description != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("description"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Description))
	}
	if m.SpecificationExtension != nil {
		for _, item := range m.SpecificationExtension {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of ServerVariables suitable for JSON or YAML export.
func (m *ServerVariables) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.AdditionalProperties != nil {
		for _, item := range m.AdditionalProperties {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of SpecificationExtension suitable for JSON or YAML export.
func (m *SpecificationExtension) ToRawInfo() *yaml.Node {
	// ONE OF WRAPPER
	// SpecificationExtension
	// {Name:number Type:float StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:}
	if v0, ok := m.GetOneof().(*SpecificationExtension_Number); ok {
		return compiler.NewScalarNodeForFloat(v0.Number)
	}
	// {Name:boolean Type:bool StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:}
	if v1, ok := m.GetOneof().(*SpecificationExtension_Boolean); ok {
		return compiler.NewScalarNodeForBool(v1.Boolean)
	}
	// {Name:string Type:string StringEnumValues:[] MapType: Repeated:false Pattern: Implicit:false Description:}
	if v2, ok := m.GetOneof().(*SpecificationExtension_String_); ok {
		return compiler.NewScalarNodeForString(v2.String_)
	}
	return compiler.NewNullNode()
}

// ToRawInfo returns a description of StringArray suitable for JSON or YAML export.
func (m *StringArray) ToRawInfo() *yaml.Node {
	return compiler.NewSequenceNodeForStringArray(m.Value)
}

// ToRawInfo returns a description of Strings suitable for JSON or YAML export.
func (m *Strings) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	// &{Name:additionalProperties Type:NamedString StringEnumValues:[] MapType:string Repeated:true Pattern: Implicit:true Description:}
	return info
}

// ToRawInfo returns a description of Tag suitable for JSON or YAML export.
func (m *Tag) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	// always include this required field.
	info.Content = append(info.Content, compiler.NewScalarNodeForString("name"))
	info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Name))
	if m.Description != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("description"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Description))
	}
	if m.ExternalDocs != nil {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("externalDocs"))
		info.Content = append(info.Content, m.ExternalDocs.ToRawInfo())
	}
	if m.SpecificationExtension != nil {
		for _, item := range m.SpecificationExtension {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

// ToRawInfo returns a description of Xml suitable for JSON or YAML export.
func (m *Xml) ToRawInfo() *yaml.Node {
	info := compiler.NewMappingNode()
	if m == nil {
		return info
	}
	if m.Name != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("name"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Name))
	}
	if m.Namespace != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("namespace"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Namespace))
	}
	if m.Prefix != "" {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("prefix"))
		info.Content = append(info.Content, compiler.NewScalarNodeForString(m.Prefix))
	}
	if m.Attribute != false {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("attribute"))
		info.Content = append(info.Content, compiler.NewScalarNodeForBool(m.Attribute))
	}
	if m.Wrapped != false {
		info.Content = append(info.Content, compiler.NewScalarNodeForString("wrapped"))
		info.Content = append(info.Content, compiler.NewScalarNodeForBool(m.Wrapped))
	}
	if m.SpecificationExtension != nil {
		for _, item := range m.SpecificationExtension {
			info.Content = append(info.Content, compiler.NewScalarNodeForString(item.Name))
			info.Content = append(info.Content, item.Value.ToRawInfo())
		}
	}
	return info
}

var (
	pattern0 = regexp.MustCompile("^")
	pattern1 = regexp.MustCompile("^x-")
	pattern2 = regexp.MustCompile("^/")
	pattern3 = regexp.MustCompile("^([0-9X]{3})$")
)
