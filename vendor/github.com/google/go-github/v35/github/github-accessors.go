// Copyright 2017 The go-github AUTHORS. All rights reserved.
//
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

// Code generated by gen-accessors; DO NOT EDIT.

package github

import (
	"encoding/json"
	"time"
)

// GetRetryAfter returns the RetryAfter field if it's non-nil, zero value otherwise.
func (a *AbuseRateLimitError) GetRetryAfter() time.Duration {
	if a == nil || a.RetryAfter == nil {
		return 0
	}
	return *a.RetryAfter
}

// GetGithubOwnedAllowed returns the GithubOwnedAllowed field if it's non-nil, zero value otherwise.
func (a *ActionsAllowed) GetGithubOwnedAllowed() bool {
	if a == nil || a.GithubOwnedAllowed == nil {
		return false
	}
	return *a.GithubOwnedAllowed
}

// GetVerifiedAllowed returns the VerifiedAllowed field if it's non-nil, zero value otherwise.
func (a *ActionsAllowed) GetVerifiedAllowed() bool {
	if a == nil || a.VerifiedAllowed == nil {
		return false
	}
	return *a.VerifiedAllowed
}

// GetAllowedActions returns the AllowedActions field if it's non-nil, zero value otherwise.
func (a *ActionsPermissions) GetAllowedActions() string {
	if a == nil || a.AllowedActions == nil {
		return ""
	}
	return *a.AllowedActions
}

// GetEnabledRepositories returns the EnabledRepositories field if it's non-nil, zero value otherwise.
func (a *ActionsPermissions) GetEnabledRepositories() string {
	if a == nil || a.EnabledRepositories == nil {
		return ""
	}
	return *a.EnabledRepositories
}

// GetSelectedActionsURL returns the SelectedActionsURL field if it's non-nil, zero value otherwise.
func (a *ActionsPermissions) GetSelectedActionsURL() string {
	if a == nil || a.SelectedActionsURL == nil {
		return ""
	}
	return *a.SelectedActionsURL
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (a *AdminEnforcement) GetURL() string {
	if a == nil || a.URL == nil {
		return ""
	}
	return *a.URL
}

// GetComments returns the Comments field.
func (a *AdminStats) GetComments() *CommentStats {
	if a == nil {
		return nil
	}
	return a.Comments
}

// GetGists returns the Gists field.
func (a *AdminStats) GetGists() *GistStats {
	if a == nil {
		return nil
	}
	return a.Gists
}

// GetHooks returns the Hooks field.
func (a *AdminStats) GetHooks() *HookStats {
	if a == nil {
		return nil
	}
	return a.Hooks
}

// GetIssues returns the Issues field.
func (a *AdminStats) GetIssues() *IssueStats {
	if a == nil {
		return nil
	}
	return a.Issues
}

// GetMilestones returns the Milestones field.
func (a *AdminStats) GetMilestones() *MilestoneStats {
	if a == nil {
		return nil
	}
	return a.Milestones
}

// GetOrgs returns the Orgs field.
func (a *AdminStats) GetOrgs() *OrgStats {
	if a == nil {
		return nil
	}
	return a.Orgs
}

// GetPages returns the Pages field.
func (a *AdminStats) GetPages() *PageStats {
	if a == nil {
		return nil
	}
	return a.Pages
}

// GetPulls returns the Pulls field.
func (a *AdminStats) GetPulls() *PullStats {
	if a == nil {
		return nil
	}
	return a.Pulls
}

// GetRepos returns the Repos field.
func (a *AdminStats) GetRepos() *RepoStats {
	if a == nil {
		return nil
	}
	return a.Repos
}

// GetUsers returns the Users field.
func (a *AdminStats) GetUsers() *UserStats {
	if a == nil {
		return nil
	}
	return a.Users
}

// GetClosedAt returns the ClosedAt field if it's non-nil, zero value otherwise.
func (a *Alert) GetClosedAt() Timestamp {
	if a == nil || a.ClosedAt == nil {
		return Timestamp{}
	}
	return *a.ClosedAt
}

// GetClosedBy returns the ClosedBy field.
func (a *Alert) GetClosedBy() *User {
	if a == nil {
		return nil
	}
	return a.ClosedBy
}

// GetCreatedAt returns the CreatedAt field if it's non-nil, zero value otherwise.
func (a *Alert) GetCreatedAt() Timestamp {
	if a == nil || a.CreatedAt == nil {
		return Timestamp{}
	}
	return *a.CreatedAt
}

// GetHTMLURL returns the HTMLURL field if it's non-nil, zero value otherwise.
func (a *Alert) GetHTMLURL() string {
	if a == nil || a.HTMLURL == nil {
		return ""
	}
	return *a.HTMLURL
}

// GetOpen returns the Open field if it's non-nil, zero value otherwise.
func (a *Alert) GetOpen() bool {
	if a == nil || a.Open == nil {
		return false
	}
	return *a.Open
}

// GetRuleDescription returns the RuleDescription field if it's non-nil, zero value otherwise.
func (a *Alert) GetRuleDescription() string {
	if a == nil || a.RuleDescription == nil {
		return ""
	}
	return *a.RuleDescription
}

// GetRuleID returns the RuleID field if it's non-nil, zero value otherwise.
func (a *Alert) GetRuleID() string {
	if a == nil || a.RuleID == nil {
		return ""
	}
	return *a.RuleID
}

// GetRuleSeverity returns the RuleSeverity field if it's non-nil, zero value otherwise.
func (a *Alert) GetRuleSeverity() string {
	if a == nil || a.RuleSeverity == nil {
		return ""
	}
	return *a.RuleSeverity
}

// GetTool returns the Tool field if it's non-nil, zero value otherwise.
func (a *Alert) GetTool() string {
	if a == nil || a.Tool == nil {
		return ""
	}
	return *a.Tool
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (a *Alert) GetURL() string {
	if a == nil || a.URL == nil {
		return ""
	}
	return *a.URL
}

// GetVerifiablePasswordAuthentication returns the VerifiablePasswordAuthentication field if it's non-nil, zero value otherwise.
func (a *APIMeta) GetVerifiablePasswordAuthentication() bool {
	if a == nil || a.VerifiablePasswordAuthentication == nil {
		return false
	}
	return *a.VerifiablePasswordAuthentication
}

// GetCreatedAt returns the CreatedAt field if it's non-nil, zero value otherwise.
func (a *App) GetCreatedAt() Timestamp {
	if a == nil || a.CreatedAt == nil {
		return Timestamp{}
	}
	return *a.CreatedAt
}

// GetDescription returns the Description field if it's non-nil, zero value otherwise.
func (a *App) GetDescription() string {
	if a == nil || a.Description == nil {
		return ""
	}
	return *a.Description
}

// GetExternalURL returns the ExternalURL field if it's non-nil, zero value otherwise.
func (a *App) GetExternalURL() string {
	if a == nil || a.ExternalURL == nil {
		return ""
	}
	return *a.ExternalURL
}

// GetHTMLURL returns the HTMLURL field if it's non-nil, zero value otherwise.
func (a *App) GetHTMLURL() string {
	if a == nil || a.HTMLURL == nil {
		return ""
	}
	return *a.HTMLURL
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (a *App) GetID() int64 {
	if a == nil || a.ID == nil {
		return 0
	}
	return *a.ID
}

// GetName returns the Name field if it's non-nil, zero value otherwise.
func (a *App) GetName() string {
	if a == nil || a.Name == nil {
		return ""
	}
	return *a.Name
}

// GetNodeID returns the NodeID field if it's non-nil, zero value otherwise.
func (a *App) GetNodeID() string {
	if a == nil || a.NodeID == nil {
		return ""
	}
	return *a.NodeID
}

// GetOwner returns the Owner field.
func (a *App) GetOwner() *User {
	if a == nil {
		return nil
	}
	return a.Owner
}

// GetPermissions returns the Permissions field.
func (a *App) GetPermissions() *InstallationPermissions {
	if a == nil {
		return nil
	}
	return a.Permissions
}

// GetSlug returns the Slug field if it's non-nil, zero value otherwise.
func (a *App) GetSlug() string {
	if a == nil || a.Slug == nil {
		return ""
	}
	return *a.Slug
}

// GetUpdatedAt returns the UpdatedAt field if it's non-nil, zero value otherwise.
func (a *App) GetUpdatedAt() Timestamp {
	if a == nil || a.UpdatedAt == nil {
		return Timestamp{}
	}
	return *a.UpdatedAt
}

// GetClientID returns the ClientID field if it's non-nil, zero value otherwise.
func (a *AppConfig) GetClientID() string {
	if a == nil || a.ClientID == nil {
		return ""
	}
	return *a.ClientID
}

// GetClientSecret returns the ClientSecret field if it's non-nil, zero value otherwise.
func (a *AppConfig) GetClientSecret() string {
	if a == nil || a.ClientSecret == nil {
		return ""
	}
	return *a.ClientSecret
}

// GetCreatedAt returns the CreatedAt field if it's non-nil, zero value otherwise.
func (a *AppConfig) GetCreatedAt() Timestamp {
	if a == nil || a.CreatedAt == nil {
		return Timestamp{}
	}
	return *a.CreatedAt
}

// GetDescription returns the Description field if it's non-nil, zero value otherwise.
func (a *AppConfig) GetDescription() string {
	if a == nil || a.Description == nil {
		return ""
	}
	return *a.Description
}

// GetExternalURL returns the ExternalURL field if it's non-nil, zero value otherwise.
func (a *AppConfig) GetExternalURL() string {
	if a == nil || a.ExternalURL == nil {
		return ""
	}
	return *a.ExternalURL
}

// GetHTMLURL returns the HTMLURL field if it's non-nil, zero value otherwise.
func (a *AppConfig) GetHTMLURL() string {
	if a == nil || a.HTMLURL == nil {
		return ""
	}
	return *a.HTMLURL
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (a *AppConfig) GetID() int64 {
	if a == nil || a.ID == nil {
		return 0
	}
	return *a.ID
}

// GetName returns the Name field if it's non-nil, zero value otherwise.
func (a *AppConfig) GetName() string {
	if a == nil || a.Name == nil {
		return ""
	}
	return *a.Name
}

// GetNodeID returns the NodeID field if it's non-nil, zero value otherwise.
func (a *AppConfig) GetNodeID() string {
	if a == nil || a.NodeID == nil {
		return ""
	}
	return *a.NodeID
}

// GetOwner returns the Owner field.
func (a *AppConfig) GetOwner() *User {
	if a == nil {
		return nil
	}
	return a.Owner
}

// GetPEM returns the PEM field if it's non-nil, zero value otherwise.
func (a *AppConfig) GetPEM() string {
	if a == nil || a.PEM == nil {
		return ""
	}
	return *a.PEM
}

// GetUpdatedAt returns the UpdatedAt field if it's non-nil, zero value otherwise.
func (a *AppConfig) GetUpdatedAt() Timestamp {
	if a == nil || a.UpdatedAt == nil {
		return Timestamp{}
	}
	return *a.UpdatedAt
}

// GetWebhookSecret returns the WebhookSecret field if it's non-nil, zero value otherwise.
func (a *AppConfig) GetWebhookSecret() string {
	if a == nil || a.WebhookSecret == nil {
		return ""
	}
	return *a.WebhookSecret
}

// GetArchiveDownloadURL returns the ArchiveDownloadURL field if it's non-nil, zero value otherwise.
func (a *Artifact) GetArchiveDownloadURL() string {
	if a == nil || a.ArchiveDownloadURL == nil {
		return ""
	}
	return *a.ArchiveDownloadURL
}

// GetCreatedAt returns the CreatedAt field if it's non-nil, zero value otherwise.
func (a *Artifact) GetCreatedAt() Timestamp {
	if a == nil || a.CreatedAt == nil {
		return Timestamp{}
	}
	return *a.CreatedAt
}

// GetExpired returns the Expired field if it's non-nil, zero value otherwise.
func (a *Artifact) GetExpired() bool {
	if a == nil || a.Expired == nil {
		return false
	}
	return *a.Expired
}

// GetExpiresAt returns the ExpiresAt field if it's non-nil, zero value otherwise.
func (a *Artifact) GetExpiresAt() Timestamp {
	if a == nil || a.ExpiresAt == nil {
		return Timestamp{}
	}
	return *a.ExpiresAt
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (a *Artifact) GetID() int64 {
	if a == nil || a.ID == nil {
		return 0
	}
	return *a.ID
}

// GetName returns the Name field if it's non-nil, zero value otherwise.
func (a *Artifact) GetName() string {
	if a == nil || a.Name == nil {
		return ""
	}
	return *a.Name
}

// GetNodeID returns the NodeID field if it's non-nil, zero value otherwise.
func (a *Artifact) GetNodeID() string {
	if a == nil || a.NodeID == nil {
		return ""
	}
	return *a.NodeID
}

// GetSizeInBytes returns the SizeInBytes field if it's non-nil, zero value otherwise.
func (a *Artifact) GetSizeInBytes() int64 {
	if a == nil || a.SizeInBytes == nil {
		return 0
	}
	return *a.SizeInBytes
}

// GetTotalCount returns the TotalCount field if it's non-nil, zero value otherwise.
func (a *ArtifactList) GetTotalCount() int64 {
	if a == nil || a.TotalCount == nil {
		return 0
	}
	return *a.TotalCount
}

// GetBody returns the Body field if it's non-nil, zero value otherwise.
func (a *Attachment) GetBody() string {
	if a == nil || a.Body == nil {
		return ""
	}
	return *a.Body
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (a *Attachment) GetID() int64 {
	if a == nil || a.ID == nil {
		return 0
	}
	return *a.ID
}

// GetTitle returns the Title field if it's non-nil, zero value otherwise.
func (a *Attachment) GetTitle() string {
	if a == nil || a.Title == nil {
		return ""
	}
	return *a.Title
}

// GetAction returns the Action field if it's non-nil, zero value otherwise.
func (a *AuditEntry) GetAction() string {
	if a == nil || a.Action == nil {
		return ""
	}
	return *a.Action
}

// GetActive returns the Active field if it's non-nil, zero value otherwise.
func (a *AuditEntry) GetActive() bool {
	if a == nil || a.Active == nil {
		return false
	}
	return *a.Active
}

// GetActiveWas returns the ActiveWas field if it's non-nil, zero value otherwise.
func (a *AuditEntry) GetActiveWas() bool {
	if a == nil || a.ActiveWas == nil {
		return false
	}
	return *a.ActiveWas
}

// GetActor returns the Actor field if it's non-nil, zero value otherwise.
func (a *AuditEntry) GetActor() string {
	if a == nil || a.Actor == nil {
		return ""
	}
	return *a.Actor
}

// GetBlockedUser returns the BlockedUser field if it's non-nil, zero value otherwise.
func (a *AuditEntry) GetBlockedUser() string {
	if a == nil || a.BlockedUser == nil {
		return ""
	}
	return *a.BlockedUser
}

// GetBusiness returns the Business field if it's non-nil, zero value otherwise.
func (a *AuditEntry) GetBusiness() string {
	if a == nil || a.Business == nil {
		return ""
	}
	return *a.Business
}

// GetCancelledAt returns the CancelledAt field if it's non-nil, zero value otherwise.
func (a *AuditEntry) GetCancelledAt() Timestamp {
	if a == nil || a.CancelledAt == nil {
		return Timestamp{}
	}
	return *a.CancelledAt
}

// GetCompletedAt returns the CompletedAt field if it's non-nil, zero value otherwise.
func (a *AuditEntry) GetCompletedAt() Timestamp {
	if a == nil || a.CompletedAt == nil {
		return Timestamp{}
	}
	return *a.CompletedAt
}

// GetConclusion returns the Conclusion field if it's non-nil, zero value otherwise.
func (a *AuditEntry) GetConclusion() string {
	if a == nil || a.Conclusion == nil {
		return ""
	}
	return *a.Conclusion
}

// GetConfig returns the Config field.
func (a *AuditEntry) GetConfig() *HookConfig {
	if a == nil {
		return nil
	}
	return a.Config
}

// GetConfigWas returns the ConfigWas field.
func (a *AuditEntry) GetConfigWas() *HookConfig {
	if a == nil {
		return nil
	}
	return a.ConfigWas
}

// GetContentType returns the ContentType field if it's non-nil, zero value otherwise.
func (a *AuditEntry) GetContentType() string {
	if a == nil || a.ContentType == nil {
		return ""
	}
	return *a.ContentType
}

// GetCreatedAt returns the CreatedAt field if it's non-nil, zero value otherwise.
func (a *AuditEntry) GetCreatedAt() Timestamp {
	if a == nil || a.CreatedAt == nil {
		return Timestamp{}
	}
	return *a.CreatedAt
}

// GetDeployKeyFingerprint returns the DeployKeyFingerprint field if it's non-nil, zero value otherwise.
func (a *AuditEntry) GetDeployKeyFingerprint() string {
	if a == nil || a.DeployKeyFingerprint == nil {
		return ""
	}
	return *a.DeployKeyFingerprint
}

// GetDocumentID returns the DocumentID field if it's non-nil, zero value otherwise.
func (a *AuditEntry) GetDocumentID() string {
	if a == nil || a.DocumentID == nil {
		return ""
	}
	return *a.DocumentID
}

// GetEmoji returns the Emoji field if it's non-nil, zero value otherwise.
func (a *AuditEntry) GetEmoji() string {
	if a == nil || a.Emoji == nil {
		return ""
	}
	return *a.Emoji
}

// GetEnvironmentName returns the EnvironmentName field if it's non-nil, zero value otherwise.
func (a *AuditEntry) GetEnvironmentName() string {
	if a == nil || a.EnvironmentName == nil {
		return ""
	}
	return *a.EnvironmentName
}

// GetEvent returns the Event field if it's non-nil, zero value otherwise.
func (a *AuditEntry) GetEvent() string {
	if a == nil || a.Event == nil {
		return ""
	}
	return *a.Event
}

// GetExplanation returns the Explanation field if it's non-nil, zero value otherwise.
func (a *AuditEntry) GetExplanation() string {
	if a == nil || a.Explanation == nil {
		return ""
	}
	return *a.Explanation
}

// GetFingerprint returns the Fingerprint field if it's non-nil, zero value otherwise.
func (a *AuditEntry) GetFingerprint() string {
	if a == nil || a.Fingerprint == nil {
		return ""
	}
	return *a.Fingerprint
}

// GetHeadBranch returns the HeadBranch field if it's non-nil, zero value otherwise.
func (a *AuditEntry) GetHeadBranch() string {
	if a == nil || a.HeadBranch == nil {
		return ""
	}
	return *a.HeadBranch
}

// GetHeadSHA returns the HeadSHA field if it's non-nil, zero value otherwise.
func (a *AuditEntry) GetHeadSHA() string {
	if a == nil || a.HeadSHA == nil {
		return ""
	}
	return *a.HeadSHA
}

// GetHookID returns the HookID field if it's non-nil, zero value otherwise.
func (a *AuditEntry) GetHookID() int64 {
	if a == nil || a.HookID == nil {
		return 0
	}
	return *a.HookID
}

// GetIsHostedRunner returns the IsHostedRunner field if it's non-nil, zero value otherwise.
func (a *AuditEntry) GetIsHostedRunner() bool {
	if a == nil || a.IsHostedRunner == nil {
		return false
	}
	return *a.IsHostedRunner
}

// GetJobName returns the JobName field if it's non-nil, zero value otherwise.
func (a *AuditEntry) GetJobName() string {
	if a == nil || a.JobName == nil {
		return ""
	}
	return *a.JobName
}

// GetLimitedAvailability returns the LimitedAvailability field if it's non-nil, zero value otherwise.
func (a *AuditEntry) GetLimitedAvailability() bool {
	if a == nil || a.LimitedAvailability == nil {
		return false
	}
	return *a.LimitedAvailability
}

// GetMessage returns the Message field if it's non-nil, zero value otherwise.
func (a *AuditEntry) GetMessage() string {
	if a == nil || a.Message == nil {
		return ""
	}
	return *a.Message
}

// GetName returns the Name field if it's non-nil, zero value otherwise.
func (a *AuditEntry) GetName() string {
	if a == nil || a.Name == nil {
		return ""
	}
	return *a.Name
}

// GetOldUser returns the OldUser field if it's non-nil, zero value otherwise.
func (a *AuditEntry) GetOldUser() string {
	if a == nil || a.OldUser == nil {
		return ""
	}
	return *a.OldUser
}

// GetOpenSSHPublicKey returns the OpenSSHPublicKey field if it's non-nil, zero value otherwise.
func (a *AuditEntry) GetOpenSSHPublicKey() string {
	if a == nil || a.OpenSSHPublicKey == nil {
		return ""
	}
	return *a.OpenSSHPublicKey
}

// GetOrg returns the Org field if it's non-nil, zero value otherwise.
func (a *AuditEntry) GetOrg() string {
	if a == nil || a.Org == nil {
		return ""
	}
	return *a.Org
}

// GetPreviousVisibility returns the PreviousVisibility field if it's non-nil, zero value otherwise.
func (a *AuditEntry) GetPreviousVisibility() string {
	if a == nil || a.PreviousVisibility == nil {
		return ""
	}
	return *a.PreviousVisibility
}

// GetReadOnly returns the ReadOnly field if it's non-nil, zero value otherwise.
func (a *AuditEntry) GetReadOnly() string {
	if a == nil || a.ReadOnly == nil {
		return ""
	}
	return *a.ReadOnly
}

// GetRepo returns the Repo field if it's non-nil, zero value otherwise.
func (a *AuditEntry) GetRepo() string {
	if a == nil || a.Repo == nil {
		return ""
	}
	return *a.Repo
}

// GetRepository returns the Repository field if it's non-nil, zero value otherwise.
func (a *AuditEntry) GetRepository() string {
	if a == nil || a.Repository == nil {
		return ""
	}
	return *a.Repository
}

// GetRepositoryPublic returns the RepositoryPublic field if it's non-nil, zero value otherwise.
func (a *AuditEntry) GetRepositoryPublic() bool {
	if a == nil || a.RepositoryPublic == nil {
		return false
	}
	return *a.RepositoryPublic
}

// GetRunnerGroupID returns the RunnerGroupID field if it's non-nil, zero value otherwise.
func (a *AuditEntry) GetRunnerGroupID() string {
	if a == nil || a.RunnerGroupID == nil {
		return ""
	}
	return *a.RunnerGroupID
}

// GetRunnerGroupName returns the RunnerGroupName field if it's non-nil, zero value otherwise.
func (a *AuditEntry) GetRunnerGroupName() string {
	if a == nil || a.RunnerGroupName == nil {
		return ""
	}
	return *a.RunnerGroupName
}

// GetRunnerID returns the RunnerID field if it's non-nil, zero value otherwise.
func (a *AuditEntry) GetRunnerID() string {
	if a == nil || a.RunnerID == nil {
		return ""
	}
	return *a.RunnerID
}

// GetRunnerName returns the RunnerName field if it's non-nil, zero value otherwise.
func (a *AuditEntry) GetRunnerName() string {
	if a == nil || a.RunnerName == nil {
		return ""
	}
	return *a.RunnerName
}

// GetSourceVersion returns the SourceVersion field if it's non-nil, zero value otherwise.
func (a *AuditEntry) GetSourceVersion() string {
	if a == nil || a.SourceVersion == nil {
		return ""
	}
	return *a.SourceVersion
}

// GetStartedAt returns the StartedAt field if it's non-nil, zero value otherwise.
func (a *AuditEntry) GetStartedAt() Timestamp {
	if a == nil || a.StartedAt == nil {
		return Timestamp{}
	}
	return *a.StartedAt
}

// GetTargetLogin returns the TargetLogin field if it's non-nil, zero value otherwise.
func (a *AuditEntry) GetTargetLogin() string {
	if a == nil || a.TargetLogin == nil {
		return ""
	}
	return *a.TargetLogin
}

// GetTargetVersion returns the TargetVersion field if it's non-nil, zero value otherwise.
func (a *AuditEntry) GetTargetVersion() string {
	if a == nil || a.TargetVersion == nil {
		return ""
	}
	return *a.TargetVersion
}

// GetTeam returns the Team field if it's non-nil, zero value otherwise.
func (a *AuditEntry) GetTeam() string {
	if a == nil || a.Team == nil {
		return ""
	}
	return *a.Team
}

// GetTimestamp returns the Timestamp field if it's non-nil, zero value otherwise.
func (a *AuditEntry) GetTimestamp() Timestamp {
	if a == nil || a.Timestamp == nil {
		return Timestamp{}
	}
	return *a.Timestamp
}

// GetTransportProtocol returns the TransportProtocol field if it's non-nil, zero value otherwise.
func (a *AuditEntry) GetTransportProtocol() int {
	if a == nil || a.TransportProtocol == nil {
		return 0
	}
	return *a.TransportProtocol
}

// GetTransportProtocolName returns the TransportProtocolName field if it's non-nil, zero value otherwise.
func (a *AuditEntry) GetTransportProtocolName() string {
	if a == nil || a.TransportProtocolName == nil {
		return ""
	}
	return *a.TransportProtocolName
}

// GetTriggerID returns the TriggerID field if it's non-nil, zero value otherwise.
func (a *AuditEntry) GetTriggerID() int64 {
	if a == nil || a.TriggerID == nil {
		return 0
	}
	return *a.TriggerID
}

// GetUser returns the User field if it's non-nil, zero value otherwise.
func (a *AuditEntry) GetUser() string {
	if a == nil || a.User == nil {
		return ""
	}
	return *a.User
}

// GetVisibility returns the Visibility field if it's non-nil, zero value otherwise.
func (a *AuditEntry) GetVisibility() string {
	if a == nil || a.Visibility == nil {
		return ""
	}
	return *a.Visibility
}

// GetWorkflowID returns the WorkflowID field if it's non-nil, zero value otherwise.
func (a *AuditEntry) GetWorkflowID() int64 {
	if a == nil || a.WorkflowID == nil {
		return 0
	}
	return *a.WorkflowID
}

// GetWorkflowRunID returns the WorkflowRunID field if it's non-nil, zero value otherwise.
func (a *AuditEntry) GetWorkflowRunID() int64 {
	if a == nil || a.WorkflowRunID == nil {
		return 0
	}
	return *a.WorkflowRunID
}

// GetApp returns the App field.
func (a *Authorization) GetApp() *AuthorizationApp {
	if a == nil {
		return nil
	}
	return a.App
}

// GetCreatedAt returns the CreatedAt field if it's non-nil, zero value otherwise.
func (a *Authorization) GetCreatedAt() Timestamp {
	if a == nil || a.CreatedAt == nil {
		return Timestamp{}
	}
	return *a.CreatedAt
}

// GetFingerprint returns the Fingerprint field if it's non-nil, zero value otherwise.
func (a *Authorization) GetFingerprint() string {
	if a == nil || a.Fingerprint == nil {
		return ""
	}
	return *a.Fingerprint
}

// GetHashedToken returns the HashedToken field if it's non-nil, zero value otherwise.
func (a *Authorization) GetHashedToken() string {
	if a == nil || a.HashedToken == nil {
		return ""
	}
	return *a.HashedToken
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (a *Authorization) GetID() int64 {
	if a == nil || a.ID == nil {
		return 0
	}
	return *a.ID
}

// GetNote returns the Note field if it's non-nil, zero value otherwise.
func (a *Authorization) GetNote() string {
	if a == nil || a.Note == nil {
		return ""
	}
	return *a.Note
}

// GetNoteURL returns the NoteURL field if it's non-nil, zero value otherwise.
func (a *Authorization) GetNoteURL() string {
	if a == nil || a.NoteURL == nil {
		return ""
	}
	return *a.NoteURL
}

// GetToken returns the Token field if it's non-nil, zero value otherwise.
func (a *Authorization) GetToken() string {
	if a == nil || a.Token == nil {
		return ""
	}
	return *a.Token
}

// GetTokenLastEight returns the TokenLastEight field if it's non-nil, zero value otherwise.
func (a *Authorization) GetTokenLastEight() string {
	if a == nil || a.TokenLastEight == nil {
		return ""
	}
	return *a.TokenLastEight
}

// GetUpdatedAt returns the UpdatedAt field if it's non-nil, zero value otherwise.
func (a *Authorization) GetUpdatedAt() Timestamp {
	if a == nil || a.UpdatedAt == nil {
		return Timestamp{}
	}
	return *a.UpdatedAt
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (a *Authorization) GetURL() string {
	if a == nil || a.URL == nil {
		return ""
	}
	return *a.URL
}

// GetUser returns the User field.
func (a *Authorization) GetUser() *User {
	if a == nil {
		return nil
	}
	return a.User
}

// GetClientID returns the ClientID field if it's non-nil, zero value otherwise.
func (a *AuthorizationApp) GetClientID() string {
	if a == nil || a.ClientID == nil {
		return ""
	}
	return *a.ClientID
}

// GetName returns the Name field if it's non-nil, zero value otherwise.
func (a *AuthorizationApp) GetName() string {
	if a == nil || a.Name == nil {
		return ""
	}
	return *a.Name
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (a *AuthorizationApp) GetURL() string {
	if a == nil || a.URL == nil {
		return ""
	}
	return *a.URL
}

// GetClientID returns the ClientID field if it's non-nil, zero value otherwise.
func (a *AuthorizationRequest) GetClientID() string {
	if a == nil || a.ClientID == nil {
		return ""
	}
	return *a.ClientID
}

// GetClientSecret returns the ClientSecret field if it's non-nil, zero value otherwise.
func (a *AuthorizationRequest) GetClientSecret() string {
	if a == nil || a.ClientSecret == nil {
		return ""
	}
	return *a.ClientSecret
}

// GetFingerprint returns the Fingerprint field if it's non-nil, zero value otherwise.
func (a *AuthorizationRequest) GetFingerprint() string {
	if a == nil || a.Fingerprint == nil {
		return ""
	}
	return *a.Fingerprint
}

// GetNote returns the Note field if it's non-nil, zero value otherwise.
func (a *AuthorizationRequest) GetNote() string {
	if a == nil || a.Note == nil {
		return ""
	}
	return *a.Note
}

// GetNoteURL returns the NoteURL field if it's non-nil, zero value otherwise.
func (a *AuthorizationRequest) GetNoteURL() string {
	if a == nil || a.NoteURL == nil {
		return ""
	}
	return *a.NoteURL
}

// GetFingerprint returns the Fingerprint field if it's non-nil, zero value otherwise.
func (a *AuthorizationUpdateRequest) GetFingerprint() string {
	if a == nil || a.Fingerprint == nil {
		return ""
	}
	return *a.Fingerprint
}

// GetNote returns the Note field if it's non-nil, zero value otherwise.
func (a *AuthorizationUpdateRequest) GetNote() string {
	if a == nil || a.Note == nil {
		return ""
	}
	return *a.Note
}

// GetNoteURL returns the NoteURL field if it's non-nil, zero value otherwise.
func (a *AuthorizationUpdateRequest) GetNoteURL() string {
	if a == nil || a.NoteURL == nil {
		return ""
	}
	return *a.NoteURL
}

// GetAppID returns the AppID field if it's non-nil, zero value otherwise.
func (a *AutoTriggerCheck) GetAppID() int64 {
	if a == nil || a.AppID == nil {
		return 0
	}
	return *a.AppID
}

// GetSetting returns the Setting field if it's non-nil, zero value otherwise.
func (a *AutoTriggerCheck) GetSetting() bool {
	if a == nil || a.Setting == nil {
		return false
	}
	return *a.Setting
}

// GetContent returns the Content field if it's non-nil, zero value otherwise.
func (b *Blob) GetContent() string {
	if b == nil || b.Content == nil {
		return ""
	}
	return *b.Content
}

// GetEncoding returns the Encoding field if it's non-nil, zero value otherwise.
func (b *Blob) GetEncoding() string {
	if b == nil || b.Encoding == nil {
		return ""
	}
	return *b.Encoding
}

// GetNodeID returns the NodeID field if it's non-nil, zero value otherwise.
func (b *Blob) GetNodeID() string {
	if b == nil || b.NodeID == nil {
		return ""
	}
	return *b.NodeID
}

// GetSHA returns the SHA field if it's non-nil, zero value otherwise.
func (b *Blob) GetSHA() string {
	if b == nil || b.SHA == nil {
		return ""
	}
	return *b.SHA
}

// GetSize returns the Size field if it's non-nil, zero value otherwise.
func (b *Blob) GetSize() int {
	if b == nil || b.Size == nil {
		return 0
	}
	return *b.Size
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (b *Blob) GetURL() string {
	if b == nil || b.URL == nil {
		return ""
	}
	return *b.URL
}

// GetCommit returns the Commit field.
func (b *Branch) GetCommit() *RepositoryCommit {
	if b == nil {
		return nil
	}
	return b.Commit
}

// GetName returns the Name field if it's non-nil, zero value otherwise.
func (b *Branch) GetName() string {
	if b == nil || b.Name == nil {
		return ""
	}
	return *b.Name
}

// GetProtected returns the Protected field if it's non-nil, zero value otherwise.
func (b *Branch) GetProtected() bool {
	if b == nil || b.Protected == nil {
		return false
	}
	return *b.Protected
}

// GetCommit returns the Commit field.
func (b *BranchCommit) GetCommit() *Commit {
	if b == nil {
		return nil
	}
	return b.Commit
}

// GetName returns the Name field if it's non-nil, zero value otherwise.
func (b *BranchCommit) GetName() string {
	if b == nil || b.Name == nil {
		return ""
	}
	return *b.Name
}

// GetProtected returns the Protected field if it's non-nil, zero value otherwise.
func (b *BranchCommit) GetProtected() bool {
	if b == nil || b.Protected == nil {
		return false
	}
	return *b.Protected
}

// GetProtected returns the Protected field if it's non-nil, zero value otherwise.
func (b *BranchListOptions) GetProtected() bool {
	if b == nil || b.Protected == nil {
		return false
	}
	return *b.Protected
}

// GetCustomBranchPolicies returns the CustomBranchPolicies field if it's non-nil, zero value otherwise.
func (b *BranchPolicy) GetCustomBranchPolicies() bool {
	if b == nil || b.CustomBranchPolicies == nil {
		return false
	}
	return *b.CustomBranchPolicies
}

// GetProtectedBranches returns the ProtectedBranches field if it's non-nil, zero value otherwise.
func (b *BranchPolicy) GetProtectedBranches() bool {
	if b == nil || b.ProtectedBranches == nil {
		return false
	}
	return *b.ProtectedBranches
}

// GetApp returns the App field.
func (c *CheckRun) GetApp() *App {
	if c == nil {
		return nil
	}
	return c.App
}

// GetCheckSuite returns the CheckSuite field.
func (c *CheckRun) GetCheckSuite() *CheckSuite {
	if c == nil {
		return nil
	}
	return c.CheckSuite
}

// GetCompletedAt returns the CompletedAt field if it's non-nil, zero value otherwise.
func (c *CheckRun) GetCompletedAt() Timestamp {
	if c == nil || c.CompletedAt == nil {
		return Timestamp{}
	}
	return *c.CompletedAt
}

// GetConclusion returns the Conclusion field if it's non-nil, zero value otherwise.
func (c *CheckRun) GetConclusion() string {
	if c == nil || c.Conclusion == nil {
		return ""
	}
	return *c.Conclusion
}

// GetDetailsURL returns the DetailsURL field if it's non-nil, zero value otherwise.
func (c *CheckRun) GetDetailsURL() string {
	if c == nil || c.DetailsURL == nil {
		return ""
	}
	return *c.DetailsURL
}

// GetExternalID returns the ExternalID field if it's non-nil, zero value otherwise.
func (c *CheckRun) GetExternalID() string {
	if c == nil || c.ExternalID == nil {
		return ""
	}
	return *c.ExternalID
}

// GetHeadSHA returns the HeadSHA field if it's non-nil, zero value otherwise.
func (c *CheckRun) GetHeadSHA() string {
	if c == nil || c.HeadSHA == nil {
		return ""
	}
	return *c.HeadSHA
}

// GetHTMLURL returns the HTMLURL field if it's non-nil, zero value otherwise.
func (c *CheckRun) GetHTMLURL() string {
	if c == nil || c.HTMLURL == nil {
		return ""
	}
	return *c.HTMLURL
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (c *CheckRun) GetID() int64 {
	if c == nil || c.ID == nil {
		return 0
	}
	return *c.ID
}

// GetName returns the Name field if it's non-nil, zero value otherwise.
func (c *CheckRun) GetName() string {
	if c == nil || c.Name == nil {
		return ""
	}
	return *c.Name
}

// GetNodeID returns the NodeID field if it's non-nil, zero value otherwise.
func (c *CheckRun) GetNodeID() string {
	if c == nil || c.NodeID == nil {
		return ""
	}
	return *c.NodeID
}

// GetOutput returns the Output field.
func (c *CheckRun) GetOutput() *CheckRunOutput {
	if c == nil {
		return nil
	}
	return c.Output
}

// GetStartedAt returns the StartedAt field if it's non-nil, zero value otherwise.
func (c *CheckRun) GetStartedAt() Timestamp {
	if c == nil || c.StartedAt == nil {
		return Timestamp{}
	}
	return *c.StartedAt
}

// GetStatus returns the Status field if it's non-nil, zero value otherwise.
func (c *CheckRun) GetStatus() string {
	if c == nil || c.Status == nil {
		return ""
	}
	return *c.Status
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (c *CheckRun) GetURL() string {
	if c == nil || c.URL == nil {
		return ""
	}
	return *c.URL
}

// GetAnnotationLevel returns the AnnotationLevel field if it's non-nil, zero value otherwise.
func (c *CheckRunAnnotation) GetAnnotationLevel() string {
	if c == nil || c.AnnotationLevel == nil {
		return ""
	}
	return *c.AnnotationLevel
}

// GetEndColumn returns the EndColumn field if it's non-nil, zero value otherwise.
func (c *CheckRunAnnotation) GetEndColumn() int {
	if c == nil || c.EndColumn == nil {
		return 0
	}
	return *c.EndColumn
}

// GetEndLine returns the EndLine field if it's non-nil, zero value otherwise.
func (c *CheckRunAnnotation) GetEndLine() int {
	if c == nil || c.EndLine == nil {
		return 0
	}
	return *c.EndLine
}

// GetMessage returns the Message field if it's non-nil, zero value otherwise.
func (c *CheckRunAnnotation) GetMessage() string {
	if c == nil || c.Message == nil {
		return ""
	}
	return *c.Message
}

// GetPath returns the Path field if it's non-nil, zero value otherwise.
func (c *CheckRunAnnotation) GetPath() string {
	if c == nil || c.Path == nil {
		return ""
	}
	return *c.Path
}

// GetRawDetails returns the RawDetails field if it's non-nil, zero value otherwise.
func (c *CheckRunAnnotation) GetRawDetails() string {
	if c == nil || c.RawDetails == nil {
		return ""
	}
	return *c.RawDetails
}

// GetStartColumn returns the StartColumn field if it's non-nil, zero value otherwise.
func (c *CheckRunAnnotation) GetStartColumn() int {
	if c == nil || c.StartColumn == nil {
		return 0
	}
	return *c.StartColumn
}

// GetStartLine returns the StartLine field if it's non-nil, zero value otherwise.
func (c *CheckRunAnnotation) GetStartLine() int {
	if c == nil || c.StartLine == nil {
		return 0
	}
	return *c.StartLine
}

// GetTitle returns the Title field if it's non-nil, zero value otherwise.
func (c *CheckRunAnnotation) GetTitle() string {
	if c == nil || c.Title == nil {
		return ""
	}
	return *c.Title
}

// GetAction returns the Action field if it's non-nil, zero value otherwise.
func (c *CheckRunEvent) GetAction() string {
	if c == nil || c.Action == nil {
		return ""
	}
	return *c.Action
}

// GetCheckRun returns the CheckRun field.
func (c *CheckRunEvent) GetCheckRun() *CheckRun {
	if c == nil {
		return nil
	}
	return c.CheckRun
}

// GetInstallation returns the Installation field.
func (c *CheckRunEvent) GetInstallation() *Installation {
	if c == nil {
		return nil
	}
	return c.Installation
}

// GetOrg returns the Org field.
func (c *CheckRunEvent) GetOrg() *Organization {
	if c == nil {
		return nil
	}
	return c.Org
}

// GetRepo returns the Repo field.
func (c *CheckRunEvent) GetRepo() *Repository {
	if c == nil {
		return nil
	}
	return c.Repo
}

// GetRequestedAction returns the RequestedAction field.
func (c *CheckRunEvent) GetRequestedAction() *RequestedAction {
	if c == nil {
		return nil
	}
	return c.RequestedAction
}

// GetSender returns the Sender field.
func (c *CheckRunEvent) GetSender() *User {
	if c == nil {
		return nil
	}
	return c.Sender
}

// GetAlt returns the Alt field if it's non-nil, zero value otherwise.
func (c *CheckRunImage) GetAlt() string {
	if c == nil || c.Alt == nil {
		return ""
	}
	return *c.Alt
}

// GetCaption returns the Caption field if it's non-nil, zero value otherwise.
func (c *CheckRunImage) GetCaption() string {
	if c == nil || c.Caption == nil {
		return ""
	}
	return *c.Caption
}

// GetImageURL returns the ImageURL field if it's non-nil, zero value otherwise.
func (c *CheckRunImage) GetImageURL() string {
	if c == nil || c.ImageURL == nil {
		return ""
	}
	return *c.ImageURL
}

// GetAnnotationsCount returns the AnnotationsCount field if it's non-nil, zero value otherwise.
func (c *CheckRunOutput) GetAnnotationsCount() int {
	if c == nil || c.AnnotationsCount == nil {
		return 0
	}
	return *c.AnnotationsCount
}

// GetAnnotationsURL returns the AnnotationsURL field if it's non-nil, zero value otherwise.
func (c *CheckRunOutput) GetAnnotationsURL() string {
	if c == nil || c.AnnotationsURL == nil {
		return ""
	}
	return *c.AnnotationsURL
}

// GetSummary returns the Summary field if it's non-nil, zero value otherwise.
func (c *CheckRunOutput) GetSummary() string {
	if c == nil || c.Summary == nil {
		return ""
	}
	return *c.Summary
}

// GetText returns the Text field if it's non-nil, zero value otherwise.
func (c *CheckRunOutput) GetText() string {
	if c == nil || c.Text == nil {
		return ""
	}
	return *c.Text
}

// GetTitle returns the Title field if it's non-nil, zero value otherwise.
func (c *CheckRunOutput) GetTitle() string {
	if c == nil || c.Title == nil {
		return ""
	}
	return *c.Title
}

// GetAfterSHA returns the AfterSHA field if it's non-nil, zero value otherwise.
func (c *CheckSuite) GetAfterSHA() string {
	if c == nil || c.AfterSHA == nil {
		return ""
	}
	return *c.AfterSHA
}

// GetApp returns the App field.
func (c *CheckSuite) GetApp() *App {
	if c == nil {
		return nil
	}
	return c.App
}

// GetBeforeSHA returns the BeforeSHA field if it's non-nil, zero value otherwise.
func (c *CheckSuite) GetBeforeSHA() string {
	if c == nil || c.BeforeSHA == nil {
		return ""
	}
	return *c.BeforeSHA
}

// GetConclusion returns the Conclusion field if it's non-nil, zero value otherwise.
func (c *CheckSuite) GetConclusion() string {
	if c == nil || c.Conclusion == nil {
		return ""
	}
	return *c.Conclusion
}

// GetHeadBranch returns the HeadBranch field if it's non-nil, zero value otherwise.
func (c *CheckSuite) GetHeadBranch() string {
	if c == nil || c.HeadBranch == nil {
		return ""
	}
	return *c.HeadBranch
}

// GetHeadCommit returns the HeadCommit field.
func (c *CheckSuite) GetHeadCommit() *Commit {
	if c == nil {
		return nil
	}
	return c.HeadCommit
}

// GetHeadSHA returns the HeadSHA field if it's non-nil, zero value otherwise.
func (c *CheckSuite) GetHeadSHA() string {
	if c == nil || c.HeadSHA == nil {
		return ""
	}
	return *c.HeadSHA
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (c *CheckSuite) GetID() int64 {
	if c == nil || c.ID == nil {
		return 0
	}
	return *c.ID
}

// GetNodeID returns the NodeID field if it's non-nil, zero value otherwise.
func (c *CheckSuite) GetNodeID() string {
	if c == nil || c.NodeID == nil {
		return ""
	}
	return *c.NodeID
}

// GetRepository returns the Repository field.
func (c *CheckSuite) GetRepository() *Repository {
	if c == nil {
		return nil
	}
	return c.Repository
}

// GetStatus returns the Status field if it's non-nil, zero value otherwise.
func (c *CheckSuite) GetStatus() string {
	if c == nil || c.Status == nil {
		return ""
	}
	return *c.Status
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (c *CheckSuite) GetURL() string {
	if c == nil || c.URL == nil {
		return ""
	}
	return *c.URL
}

// GetAction returns the Action field if it's non-nil, zero value otherwise.
func (c *CheckSuiteEvent) GetAction() string {
	if c == nil || c.Action == nil {
		return ""
	}
	return *c.Action
}

// GetCheckSuite returns the CheckSuite field.
func (c *CheckSuiteEvent) GetCheckSuite() *CheckSuite {
	if c == nil {
		return nil
	}
	return c.CheckSuite
}

// GetInstallation returns the Installation field.
func (c *CheckSuiteEvent) GetInstallation() *Installation {
	if c == nil {
		return nil
	}
	return c.Installation
}

// GetOrg returns the Org field.
func (c *CheckSuiteEvent) GetOrg() *Organization {
	if c == nil {
		return nil
	}
	return c.Org
}

// GetRepo returns the Repo field.
func (c *CheckSuiteEvent) GetRepo() *Repository {
	if c == nil {
		return nil
	}
	return c.Repo
}

// GetSender returns the Sender field.
func (c *CheckSuiteEvent) GetSender() *User {
	if c == nil {
		return nil
	}
	return c.Sender
}

// GetPreferences returns the Preferences field.
func (c *CheckSuitePreferenceResults) GetPreferences() *PreferenceList {
	if c == nil {
		return nil
	}
	return c.Preferences
}

// GetRepository returns the Repository field.
func (c *CheckSuitePreferenceResults) GetRepository() *Repository {
	if c == nil {
		return nil
	}
	return c.Repository
}

// GetBody returns the Body field if it's non-nil, zero value otherwise.
func (c *CodeOfConduct) GetBody() string {
	if c == nil || c.Body == nil {
		return ""
	}
	return *c.Body
}

// GetKey returns the Key field if it's non-nil, zero value otherwise.
func (c *CodeOfConduct) GetKey() string {
	if c == nil || c.Key == nil {
		return ""
	}
	return *c.Key
}

// GetName returns the Name field if it's non-nil, zero value otherwise.
func (c *CodeOfConduct) GetName() string {
	if c == nil || c.Name == nil {
		return ""
	}
	return *c.Name
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (c *CodeOfConduct) GetURL() string {
	if c == nil || c.URL == nil {
		return ""
	}
	return *c.URL
}

// GetHTMLURL returns the HTMLURL field if it's non-nil, zero value otherwise.
func (c *CodeResult) GetHTMLURL() string {
	if c == nil || c.HTMLURL == nil {
		return ""
	}
	return *c.HTMLURL
}

// GetName returns the Name field if it's non-nil, zero value otherwise.
func (c *CodeResult) GetName() string {
	if c == nil || c.Name == nil {
		return ""
	}
	return *c.Name
}

// GetPath returns the Path field if it's non-nil, zero value otherwise.
func (c *CodeResult) GetPath() string {
	if c == nil || c.Path == nil {
		return ""
	}
	return *c.Path
}

// GetRepository returns the Repository field.
func (c *CodeResult) GetRepository() *Repository {
	if c == nil {
		return nil
	}
	return c.Repository
}

// GetSHA returns the SHA field if it's non-nil, zero value otherwise.
func (c *CodeResult) GetSHA() string {
	if c == nil || c.SHA == nil {
		return ""
	}
	return *c.SHA
}

// GetIncompleteResults returns the IncompleteResults field if it's non-nil, zero value otherwise.
func (c *CodeSearchResult) GetIncompleteResults() bool {
	if c == nil || c.IncompleteResults == nil {
		return false
	}
	return *c.IncompleteResults
}

// GetTotal returns the Total field if it's non-nil, zero value otherwise.
func (c *CodeSearchResult) GetTotal() int {
	if c == nil || c.Total == nil {
		return 0
	}
	return *c.Total
}

// GetCreatedAt returns the CreatedAt field if it's non-nil, zero value otherwise.
func (c *CollaboratorInvitation) GetCreatedAt() Timestamp {
	if c == nil || c.CreatedAt == nil {
		return Timestamp{}
	}
	return *c.CreatedAt
}

// GetHTMLURL returns the HTMLURL field if it's non-nil, zero value otherwise.
func (c *CollaboratorInvitation) GetHTMLURL() string {
	if c == nil || c.HTMLURL == nil {
		return ""
	}
	return *c.HTMLURL
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (c *CollaboratorInvitation) GetID() int64 {
	if c == nil || c.ID == nil {
		return 0
	}
	return *c.ID
}

// GetInvitee returns the Invitee field.
func (c *CollaboratorInvitation) GetInvitee() *User {
	if c == nil {
		return nil
	}
	return c.Invitee
}

// GetInviter returns the Inviter field.
func (c *CollaboratorInvitation) GetInviter() *User {
	if c == nil {
		return nil
	}
	return c.Inviter
}

// GetPermissions returns the Permissions field if it's non-nil, zero value otherwise.
func (c *CollaboratorInvitation) GetPermissions() string {
	if c == nil || c.Permissions == nil {
		return ""
	}
	return *c.Permissions
}

// GetRepo returns the Repo field.
func (c *CollaboratorInvitation) GetRepo() *Repository {
	if c == nil {
		return nil
	}
	return c.Repo
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (c *CollaboratorInvitation) GetURL() string {
	if c == nil || c.URL == nil {
		return ""
	}
	return *c.URL
}

// GetCommitURL returns the CommitURL field if it's non-nil, zero value otherwise.
func (c *CombinedStatus) GetCommitURL() string {
	if c == nil || c.CommitURL == nil {
		return ""
	}
	return *c.CommitURL
}

// GetName returns the Name field if it's non-nil, zero value otherwise.
func (c *CombinedStatus) GetName() string {
	if c == nil || c.Name == nil {
		return ""
	}
	return *c.Name
}

// GetRepositoryURL returns the RepositoryURL field if it's non-nil, zero value otherwise.
func (c *CombinedStatus) GetRepositoryURL() string {
	if c == nil || c.RepositoryURL == nil {
		return ""
	}
	return *c.RepositoryURL
}

// GetSHA returns the SHA field if it's non-nil, zero value otherwise.
func (c *CombinedStatus) GetSHA() string {
	if c == nil || c.SHA == nil {
		return ""
	}
	return *c.SHA
}

// GetState returns the State field if it's non-nil, zero value otherwise.
func (c *CombinedStatus) GetState() string {
	if c == nil || c.State == nil {
		return ""
	}
	return *c.State
}

// GetTotalCount returns the TotalCount field if it's non-nil, zero value otherwise.
func (c *CombinedStatus) GetTotalCount() int {
	if c == nil || c.TotalCount == nil {
		return 0
	}
	return *c.TotalCount
}

// GetCreatedAt returns the CreatedAt field if it's non-nil, zero value otherwise.
func (c *Comment) GetCreatedAt() time.Time {
	if c == nil || c.CreatedAt == nil {
		return time.Time{}
	}
	return *c.CreatedAt
}

// GetTotalCommitComments returns the TotalCommitComments field if it's non-nil, zero value otherwise.
func (c *CommentStats) GetTotalCommitComments() int {
	if c == nil || c.TotalCommitComments == nil {
		return 0
	}
	return *c.TotalCommitComments
}

// GetTotalGistComments returns the TotalGistComments field if it's non-nil, zero value otherwise.
func (c *CommentStats) GetTotalGistComments() int {
	if c == nil || c.TotalGistComments == nil {
		return 0
	}
	return *c.TotalGistComments
}

// GetTotalIssueComments returns the TotalIssueComments field if it's non-nil, zero value otherwise.
func (c *CommentStats) GetTotalIssueComments() int {
	if c == nil || c.TotalIssueComments == nil {
		return 0
	}
	return *c.TotalIssueComments
}

// GetTotalPullRequestComments returns the TotalPullRequestComments field if it's non-nil, zero value otherwise.
func (c *CommentStats) GetTotalPullRequestComments() int {
	if c == nil || c.TotalPullRequestComments == nil {
		return 0
	}
	return *c.TotalPullRequestComments
}

// GetAuthor returns the Author field.
func (c *Commit) GetAuthor() *CommitAuthor {
	if c == nil {
		return nil
	}
	return c.Author
}

// GetCommentCount returns the CommentCount field if it's non-nil, zero value otherwise.
func (c *Commit) GetCommentCount() int {
	if c == nil || c.CommentCount == nil {
		return 0
	}
	return *c.CommentCount
}

// GetCommitter returns the Committer field.
func (c *Commit) GetCommitter() *CommitAuthor {
	if c == nil {
		return nil
	}
	return c.Committer
}

// GetHTMLURL returns the HTMLURL field if it's non-nil, zero value otherwise.
func (c *Commit) GetHTMLURL() string {
	if c == nil || c.HTMLURL == nil {
		return ""
	}
	return *c.HTMLURL
}

// GetMessage returns the Message field if it's non-nil, zero value otherwise.
func (c *Commit) GetMessage() string {
	if c == nil || c.Message == nil {
		return ""
	}
	return *c.Message
}

// GetNodeID returns the NodeID field if it's non-nil, zero value otherwise.
func (c *Commit) GetNodeID() string {
	if c == nil || c.NodeID == nil {
		return ""
	}
	return *c.NodeID
}

// GetSHA returns the SHA field if it's non-nil, zero value otherwise.
func (c *Commit) GetSHA() string {
	if c == nil || c.SHA == nil {
		return ""
	}
	return *c.SHA
}

// GetStats returns the Stats field.
func (c *Commit) GetStats() *CommitStats {
	if c == nil {
		return nil
	}
	return c.Stats
}

// GetTree returns the Tree field.
func (c *Commit) GetTree() *Tree {
	if c == nil {
		return nil
	}
	return c.Tree
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (c *Commit) GetURL() string {
	if c == nil || c.URL == nil {
		return ""
	}
	return *c.URL
}

// GetVerification returns the Verification field.
func (c *Commit) GetVerification() *SignatureVerification {
	if c == nil {
		return nil
	}
	return c.Verification
}

// GetDate returns the Date field if it's non-nil, zero value otherwise.
func (c *CommitAuthor) GetDate() time.Time {
	if c == nil || c.Date == nil {
		return time.Time{}
	}
	return *c.Date
}

// GetEmail returns the Email field if it's non-nil, zero value otherwise.
func (c *CommitAuthor) GetEmail() string {
	if c == nil || c.Email == nil {
		return ""
	}
	return *c.Email
}

// GetLogin returns the Login field if it's non-nil, zero value otherwise.
func (c *CommitAuthor) GetLogin() string {
	if c == nil || c.Login == nil {
		return ""
	}
	return *c.Login
}

// GetName returns the Name field if it's non-nil, zero value otherwise.
func (c *CommitAuthor) GetName() string {
	if c == nil || c.Name == nil {
		return ""
	}
	return *c.Name
}

// GetAction returns the Action field if it's non-nil, zero value otherwise.
func (c *CommitCommentEvent) GetAction() string {
	if c == nil || c.Action == nil {
		return ""
	}
	return *c.Action
}

// GetComment returns the Comment field.
func (c *CommitCommentEvent) GetComment() *RepositoryComment {
	if c == nil {
		return nil
	}
	return c.Comment
}

// GetInstallation returns the Installation field.
func (c *CommitCommentEvent) GetInstallation() *Installation {
	if c == nil {
		return nil
	}
	return c.Installation
}

// GetRepo returns the Repo field.
func (c *CommitCommentEvent) GetRepo() *Repository {
	if c == nil {
		return nil
	}
	return c.Repo
}

// GetSender returns the Sender field.
func (c *CommitCommentEvent) GetSender() *User {
	if c == nil {
		return nil
	}
	return c.Sender
}

// GetAdditions returns the Additions field if it's non-nil, zero value otherwise.
func (c *CommitFile) GetAdditions() int {
	if c == nil || c.Additions == nil {
		return 0
	}
	return *c.Additions
}

// GetBlobURL returns the BlobURL field if it's non-nil, zero value otherwise.
func (c *CommitFile) GetBlobURL() string {
	if c == nil || c.BlobURL == nil {
		return ""
	}
	return *c.BlobURL
}

// GetChanges returns the Changes field if it's non-nil, zero value otherwise.
func (c *CommitFile) GetChanges() int {
	if c == nil || c.Changes == nil {
		return 0
	}
	return *c.Changes
}

// GetContentsURL returns the ContentsURL field if it's non-nil, zero value otherwise.
func (c *CommitFile) GetContentsURL() string {
	if c == nil || c.ContentsURL == nil {
		return ""
	}
	return *c.ContentsURL
}

// GetDeletions returns the Deletions field if it's non-nil, zero value otherwise.
func (c *CommitFile) GetDeletions() int {
	if c == nil || c.Deletions == nil {
		return 0
	}
	return *c.Deletions
}

// GetFilename returns the Filename field if it's non-nil, zero value otherwise.
func (c *CommitFile) GetFilename() string {
	if c == nil || c.Filename == nil {
		return ""
	}
	return *c.Filename
}

// GetPatch returns the Patch field if it's non-nil, zero value otherwise.
func (c *CommitFile) GetPatch() string {
	if c == nil || c.Patch == nil {
		return ""
	}
	return *c.Patch
}

// GetPreviousFilename returns the PreviousFilename field if it's non-nil, zero value otherwise.
func (c *CommitFile) GetPreviousFilename() string {
	if c == nil || c.PreviousFilename == nil {
		return ""
	}
	return *c.PreviousFilename
}

// GetRawURL returns the RawURL field if it's non-nil, zero value otherwise.
func (c *CommitFile) GetRawURL() string {
	if c == nil || c.RawURL == nil {
		return ""
	}
	return *c.RawURL
}

// GetSHA returns the SHA field if it's non-nil, zero value otherwise.
func (c *CommitFile) GetSHA() string {
	if c == nil || c.SHA == nil {
		return ""
	}
	return *c.SHA
}

// GetStatus returns the Status field if it's non-nil, zero value otherwise.
func (c *CommitFile) GetStatus() string {
	if c == nil || c.Status == nil {
		return ""
	}
	return *c.Status
}

// GetAuthor returns the Author field.
func (c *CommitResult) GetAuthor() *User {
	if c == nil {
		return nil
	}
	return c.Author
}

// GetCommentsURL returns the CommentsURL field if it's non-nil, zero value otherwise.
func (c *CommitResult) GetCommentsURL() string {
	if c == nil || c.CommentsURL == nil {
		return ""
	}
	return *c.CommentsURL
}

// GetCommit returns the Commit field.
func (c *CommitResult) GetCommit() *Commit {
	if c == nil {
		return nil
	}
	return c.Commit
}

// GetCommitter returns the Committer field.
func (c *CommitResult) GetCommitter() *User {
	if c == nil {
		return nil
	}
	return c.Committer
}

// GetHTMLURL returns the HTMLURL field if it's non-nil, zero value otherwise.
func (c *CommitResult) GetHTMLURL() string {
	if c == nil || c.HTMLURL == nil {
		return ""
	}
	return *c.HTMLURL
}

// GetRepository returns the Repository field.
func (c *CommitResult) GetRepository() *Repository {
	if c == nil {
		return nil
	}
	return c.Repository
}

// GetScore returns the Score field.
func (c *CommitResult) GetScore() *float64 {
	if c == nil {
		return nil
	}
	return c.Score
}

// GetSHA returns the SHA field if it's non-nil, zero value otherwise.
func (c *CommitResult) GetSHA() string {
	if c == nil || c.SHA == nil {
		return ""
	}
	return *c.SHA
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (c *CommitResult) GetURL() string {
	if c == nil || c.URL == nil {
		return ""
	}
	return *c.URL
}

// GetAheadBy returns the AheadBy field if it's non-nil, zero value otherwise.
func (c *CommitsComparison) GetAheadBy() int {
	if c == nil || c.AheadBy == nil {
		return 0
	}
	return *c.AheadBy
}

// GetBaseCommit returns the BaseCommit field.
func (c *CommitsComparison) GetBaseCommit() *RepositoryCommit {
	if c == nil {
		return nil
	}
	return c.BaseCommit
}

// GetBehindBy returns the BehindBy field if it's non-nil, zero value otherwise.
func (c *CommitsComparison) GetBehindBy() int {
	if c == nil || c.BehindBy == nil {
		return 0
	}
	return *c.BehindBy
}

// GetDiffURL returns the DiffURL field if it's non-nil, zero value otherwise.
func (c *CommitsComparison) GetDiffURL() string {
	if c == nil || c.DiffURL == nil {
		return ""
	}
	return *c.DiffURL
}

// GetHTMLURL returns the HTMLURL field if it's non-nil, zero value otherwise.
func (c *CommitsComparison) GetHTMLURL() string {
	if c == nil || c.HTMLURL == nil {
		return ""
	}
	return *c.HTMLURL
}

// GetMergeBaseCommit returns the MergeBaseCommit field.
func (c *CommitsComparison) GetMergeBaseCommit() *RepositoryCommit {
	if c == nil {
		return nil
	}
	return c.MergeBaseCommit
}

// GetPatchURL returns the PatchURL field if it's non-nil, zero value otherwise.
func (c *CommitsComparison) GetPatchURL() string {
	if c == nil || c.PatchURL == nil {
		return ""
	}
	return *c.PatchURL
}

// GetPermalinkURL returns the PermalinkURL field if it's non-nil, zero value otherwise.
func (c *CommitsComparison) GetPermalinkURL() string {
	if c == nil || c.PermalinkURL == nil {
		return ""
	}
	return *c.PermalinkURL
}

// GetStatus returns the Status field if it's non-nil, zero value otherwise.
func (c *CommitsComparison) GetStatus() string {
	if c == nil || c.Status == nil {
		return ""
	}
	return *c.Status
}

// GetTotalCommits returns the TotalCommits field if it's non-nil, zero value otherwise.
func (c *CommitsComparison) GetTotalCommits() int {
	if c == nil || c.TotalCommits == nil {
		return 0
	}
	return *c.TotalCommits
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (c *CommitsComparison) GetURL() string {
	if c == nil || c.URL == nil {
		return ""
	}
	return *c.URL
}

// GetIncompleteResults returns the IncompleteResults field if it's non-nil, zero value otherwise.
func (c *CommitsSearchResult) GetIncompleteResults() bool {
	if c == nil || c.IncompleteResults == nil {
		return false
	}
	return *c.IncompleteResults
}

// GetTotal returns the Total field if it's non-nil, zero value otherwise.
func (c *CommitsSearchResult) GetTotal() int {
	if c == nil || c.Total == nil {
		return 0
	}
	return *c.Total
}

// GetAdditions returns the Additions field if it's non-nil, zero value otherwise.
func (c *CommitStats) GetAdditions() int {
	if c == nil || c.Additions == nil {
		return 0
	}
	return *c.Additions
}

// GetDeletions returns the Deletions field if it's non-nil, zero value otherwise.
func (c *CommitStats) GetDeletions() int {
	if c == nil || c.Deletions == nil {
		return 0
	}
	return *c.Deletions
}

// GetTotal returns the Total field if it's non-nil, zero value otherwise.
func (c *CommitStats) GetTotal() int {
	if c == nil || c.Total == nil {
		return 0
	}
	return *c.Total
}

// GetCodeOfConduct returns the CodeOfConduct field.
func (c *CommunityHealthFiles) GetCodeOfConduct() *Metric {
	if c == nil {
		return nil
	}
	return c.CodeOfConduct
}

// GetContributing returns the Contributing field.
func (c *CommunityHealthFiles) GetContributing() *Metric {
	if c == nil {
		return nil
	}
	return c.Contributing
}

// GetIssueTemplate returns the IssueTemplate field.
func (c *CommunityHealthFiles) GetIssueTemplate() *Metric {
	if c == nil {
		return nil
	}
	return c.IssueTemplate
}

// GetLicense returns the License field.
func (c *CommunityHealthFiles) GetLicense() *Metric {
	if c == nil {
		return nil
	}
	return c.License
}

// GetPullRequestTemplate returns the PullRequestTemplate field.
func (c *CommunityHealthFiles) GetPullRequestTemplate() *Metric {
	if c == nil {
		return nil
	}
	return c.PullRequestTemplate
}

// GetReadme returns the Readme field.
func (c *CommunityHealthFiles) GetReadme() *Metric {
	if c == nil {
		return nil
	}
	return c.Readme
}

// GetFiles returns the Files field.
func (c *CommunityHealthMetrics) GetFiles() *CommunityHealthFiles {
	if c == nil {
		return nil
	}
	return c.Files
}

// GetHealthPercentage returns the HealthPercentage field if it's non-nil, zero value otherwise.
func (c *CommunityHealthMetrics) GetHealthPercentage() int {
	if c == nil || c.HealthPercentage == nil {
		return 0
	}
	return *c.HealthPercentage
}

// GetUpdatedAt returns the UpdatedAt field if it's non-nil, zero value otherwise.
func (c *CommunityHealthMetrics) GetUpdatedAt() time.Time {
	if c == nil || c.UpdatedAt == nil {
		return time.Time{}
	}
	return *c.UpdatedAt
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (c *ContentReference) GetID() int64 {
	if c == nil || c.ID == nil {
		return 0
	}
	return *c.ID
}

// GetNodeID returns the NodeID field if it's non-nil, zero value otherwise.
func (c *ContentReference) GetNodeID() string {
	if c == nil || c.NodeID == nil {
		return ""
	}
	return *c.NodeID
}

// GetReference returns the Reference field if it's non-nil, zero value otherwise.
func (c *ContentReference) GetReference() string {
	if c == nil || c.Reference == nil {
		return ""
	}
	return *c.Reference
}

// GetAction returns the Action field if it's non-nil, zero value otherwise.
func (c *ContentReferenceEvent) GetAction() string {
	if c == nil || c.Action == nil {
		return ""
	}
	return *c.Action
}

// GetContentReference returns the ContentReference field.
func (c *ContentReferenceEvent) GetContentReference() *ContentReference {
	if c == nil {
		return nil
	}
	return c.ContentReference
}

// GetInstallation returns the Installation field.
func (c *ContentReferenceEvent) GetInstallation() *Installation {
	if c == nil {
		return nil
	}
	return c.Installation
}

// GetRepo returns the Repo field.
func (c *ContentReferenceEvent) GetRepo() *Repository {
	if c == nil {
		return nil
	}
	return c.Repo
}

// GetSender returns the Sender field.
func (c *ContentReferenceEvent) GetSender() *User {
	if c == nil {
		return nil
	}
	return c.Sender
}

// GetAvatarURL returns the AvatarURL field if it's non-nil, zero value otherwise.
func (c *Contributor) GetAvatarURL() string {
	if c == nil || c.AvatarURL == nil {
		return ""
	}
	return *c.AvatarURL
}

// GetContributions returns the Contributions field if it's non-nil, zero value otherwise.
func (c *Contributor) GetContributions() int {
	if c == nil || c.Contributions == nil {
		return 0
	}
	return *c.Contributions
}

// GetEmail returns the Email field if it's non-nil, zero value otherwise.
func (c *Contributor) GetEmail() string {
	if c == nil || c.Email == nil {
		return ""
	}
	return *c.Email
}

// GetEventsURL returns the EventsURL field if it's non-nil, zero value otherwise.
func (c *Contributor) GetEventsURL() string {
	if c == nil || c.EventsURL == nil {
		return ""
	}
	return *c.EventsURL
}

// GetFollowersURL returns the FollowersURL field if it's non-nil, zero value otherwise.
func (c *Contributor) GetFollowersURL() string {
	if c == nil || c.FollowersURL == nil {
		return ""
	}
	return *c.FollowersURL
}

// GetFollowingURL returns the FollowingURL field if it's non-nil, zero value otherwise.
func (c *Contributor) GetFollowingURL() string {
	if c == nil || c.FollowingURL == nil {
		return ""
	}
	return *c.FollowingURL
}

// GetGistsURL returns the GistsURL field if it's non-nil, zero value otherwise.
func (c *Contributor) GetGistsURL() string {
	if c == nil || c.GistsURL == nil {
		return ""
	}
	return *c.GistsURL
}

// GetGravatarID returns the GravatarID field if it's non-nil, zero value otherwise.
func (c *Contributor) GetGravatarID() string {
	if c == nil || c.GravatarID == nil {
		return ""
	}
	return *c.GravatarID
}

// GetHTMLURL returns the HTMLURL field if it's non-nil, zero value otherwise.
func (c *Contributor) GetHTMLURL() string {
	if c == nil || c.HTMLURL == nil {
		return ""
	}
	return *c.HTMLURL
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (c *Contributor) GetID() int64 {
	if c == nil || c.ID == nil {
		return 0
	}
	return *c.ID
}

// GetLogin returns the Login field if it's non-nil, zero value otherwise.
func (c *Contributor) GetLogin() string {
	if c == nil || c.Login == nil {
		return ""
	}
	return *c.Login
}

// GetName returns the Name field if it's non-nil, zero value otherwise.
func (c *Contributor) GetName() string {
	if c == nil || c.Name == nil {
		return ""
	}
	return *c.Name
}

// GetNodeID returns the NodeID field if it's non-nil, zero value otherwise.
func (c *Contributor) GetNodeID() string {
	if c == nil || c.NodeID == nil {
		return ""
	}
	return *c.NodeID
}

// GetOrganizationsURL returns the OrganizationsURL field if it's non-nil, zero value otherwise.
func (c *Contributor) GetOrganizationsURL() string {
	if c == nil || c.OrganizationsURL == nil {
		return ""
	}
	return *c.OrganizationsURL
}

// GetReceivedEventsURL returns the ReceivedEventsURL field if it's non-nil, zero value otherwise.
func (c *Contributor) GetReceivedEventsURL() string {
	if c == nil || c.ReceivedEventsURL == nil {
		return ""
	}
	return *c.ReceivedEventsURL
}

// GetReposURL returns the ReposURL field if it's non-nil, zero value otherwise.
func (c *Contributor) GetReposURL() string {
	if c == nil || c.ReposURL == nil {
		return ""
	}
	return *c.ReposURL
}

// GetSiteAdmin returns the SiteAdmin field if it's non-nil, zero value otherwise.
func (c *Contributor) GetSiteAdmin() bool {
	if c == nil || c.SiteAdmin == nil {
		return false
	}
	return *c.SiteAdmin
}

// GetStarredURL returns the StarredURL field if it's non-nil, zero value otherwise.
func (c *Contributor) GetStarredURL() string {
	if c == nil || c.StarredURL == nil {
		return ""
	}
	return *c.StarredURL
}

// GetSubscriptionsURL returns the SubscriptionsURL field if it's non-nil, zero value otherwise.
func (c *Contributor) GetSubscriptionsURL() string {
	if c == nil || c.SubscriptionsURL == nil {
		return ""
	}
	return *c.SubscriptionsURL
}

// GetType returns the Type field if it's non-nil, zero value otherwise.
func (c *Contributor) GetType() string {
	if c == nil || c.Type == nil {
		return ""
	}
	return *c.Type
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (c *Contributor) GetURL() string {
	if c == nil || c.URL == nil {
		return ""
	}
	return *c.URL
}

// GetAuthor returns the Author field.
func (c *ContributorStats) GetAuthor() *Contributor {
	if c == nil {
		return nil
	}
	return c.Author
}

// GetTotal returns the Total field if it's non-nil, zero value otherwise.
func (c *ContributorStats) GetTotal() int {
	if c == nil || c.Total == nil {
		return 0
	}
	return *c.Total
}

// GetCompletedAt returns the CompletedAt field if it's non-nil, zero value otherwise.
func (c *CreateCheckRunOptions) GetCompletedAt() Timestamp {
	if c == nil || c.CompletedAt == nil {
		return Timestamp{}
	}
	return *c.CompletedAt
}

// GetConclusion returns the Conclusion field if it's non-nil, zero value otherwise.
func (c *CreateCheckRunOptions) GetConclusion() string {
	if c == nil || c.Conclusion == nil {
		return ""
	}
	return *c.Conclusion
}

// GetDetailsURL returns the DetailsURL field if it's non-nil, zero value otherwise.
func (c *CreateCheckRunOptions) GetDetailsURL() string {
	if c == nil || c.DetailsURL == nil {
		return ""
	}
	return *c.DetailsURL
}

// GetExternalID returns the ExternalID field if it's non-nil, zero value otherwise.
func (c *CreateCheckRunOptions) GetExternalID() string {
	if c == nil || c.ExternalID == nil {
		return ""
	}
	return *c.ExternalID
}

// GetOutput returns the Output field.
func (c *CreateCheckRunOptions) GetOutput() *CheckRunOutput {
	if c == nil {
		return nil
	}
	return c.Output
}

// GetStartedAt returns the StartedAt field if it's non-nil, zero value otherwise.
func (c *CreateCheckRunOptions) GetStartedAt() Timestamp {
	if c == nil || c.StartedAt == nil {
		return Timestamp{}
	}
	return *c.StartedAt
}

// GetStatus returns the Status field if it's non-nil, zero value otherwise.
func (c *CreateCheckRunOptions) GetStatus() string {
	if c == nil || c.Status == nil {
		return ""
	}
	return *c.Status
}

// GetHeadBranch returns the HeadBranch field if it's non-nil, zero value otherwise.
func (c *CreateCheckSuiteOptions) GetHeadBranch() string {
	if c == nil || c.HeadBranch == nil {
		return ""
	}
	return *c.HeadBranch
}

// GetDescription returns the Description field if it's non-nil, zero value otherwise.
func (c *CreateEvent) GetDescription() string {
	if c == nil || c.Description == nil {
		return ""
	}
	return *c.Description
}

// GetInstallation returns the Installation field.
func (c *CreateEvent) GetInstallation() *Installation {
	if c == nil {
		return nil
	}
	return c.Installation
}

// GetMasterBranch returns the MasterBranch field if it's non-nil, zero value otherwise.
func (c *CreateEvent) GetMasterBranch() string {
	if c == nil || c.MasterBranch == nil {
		return ""
	}
	return *c.MasterBranch
}

// GetPusherType returns the PusherType field if it's non-nil, zero value otherwise.
func (c *CreateEvent) GetPusherType() string {
	if c == nil || c.PusherType == nil {
		return ""
	}
	return *c.PusherType
}

// GetRef returns the Ref field if it's non-nil, zero value otherwise.
func (c *CreateEvent) GetRef() string {
	if c == nil || c.Ref == nil {
		return ""
	}
	return *c.Ref
}

// GetRefType returns the RefType field if it's non-nil, zero value otherwise.
func (c *CreateEvent) GetRefType() string {
	if c == nil || c.RefType == nil {
		return ""
	}
	return *c.RefType
}

// GetRepo returns the Repo field.
func (c *CreateEvent) GetRepo() *Repository {
	if c == nil {
		return nil
	}
	return c.Repo
}

// GetSender returns the Sender field.
func (c *CreateEvent) GetSender() *User {
	if c == nil {
		return nil
	}
	return c.Sender
}

// GetEmail returns the Email field if it's non-nil, zero value otherwise.
func (c *CreateOrgInvitationOptions) GetEmail() string {
	if c == nil || c.Email == nil {
		return ""
	}
	return *c.Email
}

// GetInviteeID returns the InviteeID field if it's non-nil, zero value otherwise.
func (c *CreateOrgInvitationOptions) GetInviteeID() int64 {
	if c == nil || c.InviteeID == nil {
		return 0
	}
	return *c.InviteeID
}

// GetRole returns the Role field if it's non-nil, zero value otherwise.
func (c *CreateOrgInvitationOptions) GetRole() string {
	if c == nil || c.Role == nil {
		return ""
	}
	return *c.Role
}

// GetName returns the Name field if it's non-nil, zero value otherwise.
func (c *CreateRunnerGroupRequest) GetName() string {
	if c == nil || c.Name == nil {
		return ""
	}
	return *c.Name
}

// GetVisibility returns the Visibility field if it's non-nil, zero value otherwise.
func (c *CreateRunnerGroupRequest) GetVisibility() string {
	if c == nil || c.Visibility == nil {
		return ""
	}
	return *c.Visibility
}

// GetDeploymentBranchPolicy returns the DeploymentBranchPolicy field.
func (c *CreateUpdateEnvironment) GetDeploymentBranchPolicy() *BranchPolicy {
	if c == nil {
		return nil
	}
	return c.DeploymentBranchPolicy
}

// GetWaitTimer returns the WaitTimer field if it's non-nil, zero value otherwise.
func (c *CreateUpdateEnvironment) GetWaitTimer() int {
	if c == nil || c.WaitTimer == nil {
		return 0
	}
	return *c.WaitTimer
}

// GetBody returns the Body field if it's non-nil, zero value otherwise.
func (c *CreateUserProjectOptions) GetBody() string {
	if c == nil || c.Body == nil {
		return ""
	}
	return *c.Body
}

// GetInstallation returns the Installation field.
func (d *DeleteEvent) GetInstallation() *Installation {
	if d == nil {
		return nil
	}
	return d.Installation
}

// GetPusherType returns the PusherType field if it's non-nil, zero value otherwise.
func (d *DeleteEvent) GetPusherType() string {
	if d == nil || d.PusherType == nil {
		return ""
	}
	return *d.PusherType
}

// GetRef returns the Ref field if it's non-nil, zero value otherwise.
func (d *DeleteEvent) GetRef() string {
	if d == nil || d.Ref == nil {
		return ""
	}
	return *d.Ref
}

// GetRefType returns the RefType field if it's non-nil, zero value otherwise.
func (d *DeleteEvent) GetRefType() string {
	if d == nil || d.RefType == nil {
		return ""
	}
	return *d.RefType
}

// GetRepo returns the Repo field.
func (d *DeleteEvent) GetRepo() *Repository {
	if d == nil {
		return nil
	}
	return d.Repo
}

// GetSender returns the Sender field.
func (d *DeleteEvent) GetSender() *User {
	if d == nil {
		return nil
	}
	return d.Sender
}

// GetAction returns the Action field if it's non-nil, zero value otherwise.
func (d *DeployKeyEvent) GetAction() string {
	if d == nil || d.Action == nil {
		return ""
	}
	return *d.Action
}

// GetKey returns the Key field.
func (d *DeployKeyEvent) GetKey() *Key {
	if d == nil {
		return nil
	}
	return d.Key
}

// GetCreatedAt returns the CreatedAt field if it's non-nil, zero value otherwise.
func (d *Deployment) GetCreatedAt() Timestamp {
	if d == nil || d.CreatedAt == nil {
		return Timestamp{}
	}
	return *d.CreatedAt
}

// GetCreator returns the Creator field.
func (d *Deployment) GetCreator() *User {
	if d == nil {
		return nil
	}
	return d.Creator
}

// GetDescription returns the Description field if it's non-nil, zero value otherwise.
func (d *Deployment) GetDescription() string {
	if d == nil || d.Description == nil {
		return ""
	}
	return *d.Description
}

// GetEnvironment returns the Environment field if it's non-nil, zero value otherwise.
func (d *Deployment) GetEnvironment() string {
	if d == nil || d.Environment == nil {
		return ""
	}
	return *d.Environment
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (d *Deployment) GetID() int64 {
	if d == nil || d.ID == nil {
		return 0
	}
	return *d.ID
}

// GetNodeID returns the NodeID field if it's non-nil, zero value otherwise.
func (d *Deployment) GetNodeID() string {
	if d == nil || d.NodeID == nil {
		return ""
	}
	return *d.NodeID
}

// GetRef returns the Ref field if it's non-nil, zero value otherwise.
func (d *Deployment) GetRef() string {
	if d == nil || d.Ref == nil {
		return ""
	}
	return *d.Ref
}

// GetRepositoryURL returns the RepositoryURL field if it's non-nil, zero value otherwise.
func (d *Deployment) GetRepositoryURL() string {
	if d == nil || d.RepositoryURL == nil {
		return ""
	}
	return *d.RepositoryURL
}

// GetSHA returns the SHA field if it's non-nil, zero value otherwise.
func (d *Deployment) GetSHA() string {
	if d == nil || d.SHA == nil {
		return ""
	}
	return *d.SHA
}

// GetStatusesURL returns the StatusesURL field if it's non-nil, zero value otherwise.
func (d *Deployment) GetStatusesURL() string {
	if d == nil || d.StatusesURL == nil {
		return ""
	}
	return *d.StatusesURL
}

// GetTask returns the Task field if it's non-nil, zero value otherwise.
func (d *Deployment) GetTask() string {
	if d == nil || d.Task == nil {
		return ""
	}
	return *d.Task
}

// GetUpdatedAt returns the UpdatedAt field if it's non-nil, zero value otherwise.
func (d *Deployment) GetUpdatedAt() Timestamp {
	if d == nil || d.UpdatedAt == nil {
		return Timestamp{}
	}
	return *d.UpdatedAt
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (d *Deployment) GetURL() string {
	if d == nil || d.URL == nil {
		return ""
	}
	return *d.URL
}

// GetDeployment returns the Deployment field.
func (d *DeploymentEvent) GetDeployment() *Deployment {
	if d == nil {
		return nil
	}
	return d.Deployment
}

// GetInstallation returns the Installation field.
func (d *DeploymentEvent) GetInstallation() *Installation {
	if d == nil {
		return nil
	}
	return d.Installation
}

// GetRepo returns the Repo field.
func (d *DeploymentEvent) GetRepo() *Repository {
	if d == nil {
		return nil
	}
	return d.Repo
}

// GetSender returns the Sender field.
func (d *DeploymentEvent) GetSender() *User {
	if d == nil {
		return nil
	}
	return d.Sender
}

// GetAutoMerge returns the AutoMerge field if it's non-nil, zero value otherwise.
func (d *DeploymentRequest) GetAutoMerge() bool {
	if d == nil || d.AutoMerge == nil {
		return false
	}
	return *d.AutoMerge
}

// GetDescription returns the Description field if it's non-nil, zero value otherwise.
func (d *DeploymentRequest) GetDescription() string {
	if d == nil || d.Description == nil {
		return ""
	}
	return *d.Description
}

// GetEnvironment returns the Environment field if it's non-nil, zero value otherwise.
func (d *DeploymentRequest) GetEnvironment() string {
	if d == nil || d.Environment == nil {
		return ""
	}
	return *d.Environment
}

// GetProductionEnvironment returns the ProductionEnvironment field if it's non-nil, zero value otherwise.
func (d *DeploymentRequest) GetProductionEnvironment() bool {
	if d == nil || d.ProductionEnvironment == nil {
		return false
	}
	return *d.ProductionEnvironment
}

// GetRef returns the Ref field if it's non-nil, zero value otherwise.
func (d *DeploymentRequest) GetRef() string {
	if d == nil || d.Ref == nil {
		return ""
	}
	return *d.Ref
}

// GetRequiredContexts returns the RequiredContexts field if it's non-nil, zero value otherwise.
func (d *DeploymentRequest) GetRequiredContexts() []string {
	if d == nil || d.RequiredContexts == nil {
		return nil
	}
	return *d.RequiredContexts
}

// GetTask returns the Task field if it's non-nil, zero value otherwise.
func (d *DeploymentRequest) GetTask() string {
	if d == nil || d.Task == nil {
		return ""
	}
	return *d.Task
}

// GetTransientEnvironment returns the TransientEnvironment field if it's non-nil, zero value otherwise.
func (d *DeploymentRequest) GetTransientEnvironment() bool {
	if d == nil || d.TransientEnvironment == nil {
		return false
	}
	return *d.TransientEnvironment
}

// GetCreatedAt returns the CreatedAt field if it's non-nil, zero value otherwise.
func (d *DeploymentStatus) GetCreatedAt() Timestamp {
	if d == nil || d.CreatedAt == nil {
		return Timestamp{}
	}
	return *d.CreatedAt
}

// GetCreator returns the Creator field.
func (d *DeploymentStatus) GetCreator() *User {
	if d == nil {
		return nil
	}
	return d.Creator
}

// GetDeploymentURL returns the DeploymentURL field if it's non-nil, zero value otherwise.
func (d *DeploymentStatus) GetDeploymentURL() string {
	if d == nil || d.DeploymentURL == nil {
		return ""
	}
	return *d.DeploymentURL
}

// GetDescription returns the Description field if it's non-nil, zero value otherwise.
func (d *DeploymentStatus) GetDescription() string {
	if d == nil || d.Description == nil {
		return ""
	}
	return *d.Description
}

// GetEnvironment returns the Environment field if it's non-nil, zero value otherwise.
func (d *DeploymentStatus) GetEnvironment() string {
	if d == nil || d.Environment == nil {
		return ""
	}
	return *d.Environment
}

// GetEnvironmentURL returns the EnvironmentURL field if it's non-nil, zero value otherwise.
func (d *DeploymentStatus) GetEnvironmentURL() string {
	if d == nil || d.EnvironmentURL == nil {
		return ""
	}
	return *d.EnvironmentURL
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (d *DeploymentStatus) GetID() int64 {
	if d == nil || d.ID == nil {
		return 0
	}
	return *d.ID
}

// GetLogURL returns the LogURL field if it's non-nil, zero value otherwise.
func (d *DeploymentStatus) GetLogURL() string {
	if d == nil || d.LogURL == nil {
		return ""
	}
	return *d.LogURL
}

// GetNodeID returns the NodeID field if it's non-nil, zero value otherwise.
func (d *DeploymentStatus) GetNodeID() string {
	if d == nil || d.NodeID == nil {
		return ""
	}
	return *d.NodeID
}

// GetRepositoryURL returns the RepositoryURL field if it's non-nil, zero value otherwise.
func (d *DeploymentStatus) GetRepositoryURL() string {
	if d == nil || d.RepositoryURL == nil {
		return ""
	}
	return *d.RepositoryURL
}

// GetState returns the State field if it's non-nil, zero value otherwise.
func (d *DeploymentStatus) GetState() string {
	if d == nil || d.State == nil {
		return ""
	}
	return *d.State
}

// GetTargetURL returns the TargetURL field if it's non-nil, zero value otherwise.
func (d *DeploymentStatus) GetTargetURL() string {
	if d == nil || d.TargetURL == nil {
		return ""
	}
	return *d.TargetURL
}

// GetUpdatedAt returns the UpdatedAt field if it's non-nil, zero value otherwise.
func (d *DeploymentStatus) GetUpdatedAt() Timestamp {
	if d == nil || d.UpdatedAt == nil {
		return Timestamp{}
	}
	return *d.UpdatedAt
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (d *DeploymentStatus) GetURL() string {
	if d == nil || d.URL == nil {
		return ""
	}
	return *d.URL
}

// GetDeployment returns the Deployment field.
func (d *DeploymentStatusEvent) GetDeployment() *Deployment {
	if d == nil {
		return nil
	}
	return d.Deployment
}

// GetDeploymentStatus returns the DeploymentStatus field.
func (d *DeploymentStatusEvent) GetDeploymentStatus() *DeploymentStatus {
	if d == nil {
		return nil
	}
	return d.DeploymentStatus
}

// GetInstallation returns the Installation field.
func (d *DeploymentStatusEvent) GetInstallation() *Installation {
	if d == nil {
		return nil
	}
	return d.Installation
}

// GetRepo returns the Repo field.
func (d *DeploymentStatusEvent) GetRepo() *Repository {
	if d == nil {
		return nil
	}
	return d.Repo
}

// GetSender returns the Sender field.
func (d *DeploymentStatusEvent) GetSender() *User {
	if d == nil {
		return nil
	}
	return d.Sender
}

// GetAutoInactive returns the AutoInactive field if it's non-nil, zero value otherwise.
func (d *DeploymentStatusRequest) GetAutoInactive() bool {
	if d == nil || d.AutoInactive == nil {
		return false
	}
	return *d.AutoInactive
}

// GetDescription returns the Description field if it's non-nil, zero value otherwise.
func (d *DeploymentStatusRequest) GetDescription() string {
	if d == nil || d.Description == nil {
		return ""
	}
	return *d.Description
}

// GetEnvironment returns the Environment field if it's non-nil, zero value otherwise.
func (d *DeploymentStatusRequest) GetEnvironment() string {
	if d == nil || d.Environment == nil {
		return ""
	}
	return *d.Environment
}

// GetEnvironmentURL returns the EnvironmentURL field if it's non-nil, zero value otherwise.
func (d *DeploymentStatusRequest) GetEnvironmentURL() string {
	if d == nil || d.EnvironmentURL == nil {
		return ""
	}
	return *d.EnvironmentURL
}

// GetLogURL returns the LogURL field if it's non-nil, zero value otherwise.
func (d *DeploymentStatusRequest) GetLogURL() string {
	if d == nil || d.LogURL == nil {
		return ""
	}
	return *d.LogURL
}

// GetState returns the State field if it's non-nil, zero value otherwise.
func (d *DeploymentStatusRequest) GetState() string {
	if d == nil || d.State == nil {
		return ""
	}
	return *d.State
}

// GetAuthor returns the Author field.
func (d *DiscussionComment) GetAuthor() *User {
	if d == nil {
		return nil
	}
	return d.Author
}

// GetBody returns the Body field if it's non-nil, zero value otherwise.
func (d *DiscussionComment) GetBody() string {
	if d == nil || d.Body == nil {
		return ""
	}
	return *d.Body
}

// GetBodyHTML returns the BodyHTML field if it's non-nil, zero value otherwise.
func (d *DiscussionComment) GetBodyHTML() string {
	if d == nil || d.BodyHTML == nil {
		return ""
	}
	return *d.BodyHTML
}

// GetBodyVersion returns the BodyVersion field if it's non-nil, zero value otherwise.
func (d *DiscussionComment) GetBodyVersion() string {
	if d == nil || d.BodyVersion == nil {
		return ""
	}
	return *d.BodyVersion
}

// GetCreatedAt returns the CreatedAt field if it's non-nil, zero value otherwise.
func (d *DiscussionComment) GetCreatedAt() Timestamp {
	if d == nil || d.CreatedAt == nil {
		return Timestamp{}
	}
	return *d.CreatedAt
}

// GetDiscussionURL returns the DiscussionURL field if it's non-nil, zero value otherwise.
func (d *DiscussionComment) GetDiscussionURL() string {
	if d == nil || d.DiscussionURL == nil {
		return ""
	}
	return *d.DiscussionURL
}

// GetHTMLURL returns the HTMLURL field if it's non-nil, zero value otherwise.
func (d *DiscussionComment) GetHTMLURL() string {
	if d == nil || d.HTMLURL == nil {
		return ""
	}
	return *d.HTMLURL
}

// GetLastEditedAt returns the LastEditedAt field if it's non-nil, zero value otherwise.
func (d *DiscussionComment) GetLastEditedAt() Timestamp {
	if d == nil || d.LastEditedAt == nil {
		return Timestamp{}
	}
	return *d.LastEditedAt
}

// GetNodeID returns the NodeID field if it's non-nil, zero value otherwise.
func (d *DiscussionComment) GetNodeID() string {
	if d == nil || d.NodeID == nil {
		return ""
	}
	return *d.NodeID
}

// GetNumber returns the Number field if it's non-nil, zero value otherwise.
func (d *DiscussionComment) GetNumber() int {
	if d == nil || d.Number == nil {
		return 0
	}
	return *d.Number
}

// GetReactions returns the Reactions field.
func (d *DiscussionComment) GetReactions() *Reactions {
	if d == nil {
		return nil
	}
	return d.Reactions
}

// GetUpdatedAt returns the UpdatedAt field if it's non-nil, zero value otherwise.
func (d *DiscussionComment) GetUpdatedAt() Timestamp {
	if d == nil || d.UpdatedAt == nil {
		return Timestamp{}
	}
	return *d.UpdatedAt
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (d *DiscussionComment) GetURL() string {
	if d == nil || d.URL == nil {
		return ""
	}
	return *d.URL
}

// GetTeams returns the Teams field if it's non-nil, zero value otherwise.
func (d *DismissalRestrictionsRequest) GetTeams() []string {
	if d == nil || d.Teams == nil {
		return nil
	}
	return *d.Teams
}

// GetUsers returns the Users field if it's non-nil, zero value otherwise.
func (d *DismissalRestrictionsRequest) GetUsers() []string {
	if d == nil || d.Users == nil {
		return nil
	}
	return *d.Users
}

// GetDismissalCommitID returns the DismissalCommitID field if it's non-nil, zero value otherwise.
func (d *DismissedReview) GetDismissalCommitID() string {
	if d == nil || d.DismissalCommitID == nil {
		return ""
	}
	return *d.DismissalCommitID
}

// GetDismissalMessage returns the DismissalMessage field if it's non-nil, zero value otherwise.
func (d *DismissedReview) GetDismissalMessage() string {
	if d == nil || d.DismissalMessage == nil {
		return ""
	}
	return *d.DismissalMessage
}

// GetReviewID returns the ReviewID field if it's non-nil, zero value otherwise.
func (d *DismissedReview) GetReviewID() int64 {
	if d == nil || d.ReviewID == nil {
		return 0
	}
	return *d.ReviewID
}

// GetState returns the State field if it's non-nil, zero value otherwise.
func (d *DismissedReview) GetState() string {
	if d == nil || d.State == nil {
		return ""
	}
	return *d.State
}

// GetClientPayload returns the ClientPayload field if it's non-nil, zero value otherwise.
func (d *DispatchRequestOptions) GetClientPayload() json.RawMessage {
	if d == nil || d.ClientPayload == nil {
		return json.RawMessage{}
	}
	return *d.ClientPayload
}

// GetBody returns the Body field if it's non-nil, zero value otherwise.
func (d *DraftReviewComment) GetBody() string {
	if d == nil || d.Body == nil {
		return ""
	}
	return *d.Body
}

// GetLine returns the Line field if it's non-nil, zero value otherwise.
func (d *DraftReviewComment) GetLine() int {
	if d == nil || d.Line == nil {
		return 0
	}
	return *d.Line
}

// GetPath returns the Path field if it's non-nil, zero value otherwise.
func (d *DraftReviewComment) GetPath() string {
	if d == nil || d.Path == nil {
		return ""
	}
	return *d.Path
}

// GetPosition returns the Position field if it's non-nil, zero value otherwise.
func (d *DraftReviewComment) GetPosition() int {
	if d == nil || d.Position == nil {
		return 0
	}
	return *d.Position
}

// GetSide returns the Side field if it's non-nil, zero value otherwise.
func (d *DraftReviewComment) GetSide() string {
	if d == nil || d.Side == nil {
		return ""
	}
	return *d.Side
}

// GetStartLine returns the StartLine field if it's non-nil, zero value otherwise.
func (d *DraftReviewComment) GetStartLine() int {
	if d == nil || d.StartLine == nil {
		return 0
	}
	return *d.StartLine
}

// GetStartSide returns the StartSide field if it's non-nil, zero value otherwise.
func (d *DraftReviewComment) GetStartSide() string {
	if d == nil || d.StartSide == nil {
		return ""
	}
	return *d.StartSide
}

// GetAvatarURL returns the AvatarURL field if it's non-nil, zero value otherwise.
func (e *Enterprise) GetAvatarURL() string {
	if e == nil || e.AvatarURL == nil {
		return ""
	}
	return *e.AvatarURL
}

// GetCreatedAt returns the CreatedAt field if it's non-nil, zero value otherwise.
func (e *Enterprise) GetCreatedAt() Timestamp {
	if e == nil || e.CreatedAt == nil {
		return Timestamp{}
	}
	return *e.CreatedAt
}

// GetDescription returns the Description field if it's non-nil, zero value otherwise.
func (e *Enterprise) GetDescription() string {
	if e == nil || e.Description == nil {
		return ""
	}
	return *e.Description
}

// GetHTMLURL returns the HTMLURL field if it's non-nil, zero value otherwise.
func (e *Enterprise) GetHTMLURL() string {
	if e == nil || e.HTMLURL == nil {
		return ""
	}
	return *e.HTMLURL
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (e *Enterprise) GetID() int {
	if e == nil || e.ID == nil {
		return 0
	}
	return *e.ID
}

// GetName returns the Name field if it's non-nil, zero value otherwise.
func (e *Enterprise) GetName() string {
	if e == nil || e.Name == nil {
		return ""
	}
	return *e.Name
}

// GetNodeID returns the NodeID field if it's non-nil, zero value otherwise.
func (e *Enterprise) GetNodeID() string {
	if e == nil || e.NodeID == nil {
		return ""
	}
	return *e.NodeID
}

// GetSlug returns the Slug field if it's non-nil, zero value otherwise.
func (e *Enterprise) GetSlug() string {
	if e == nil || e.Slug == nil {
		return ""
	}
	return *e.Slug
}

// GetUpdatedAt returns the UpdatedAt field if it's non-nil, zero value otherwise.
func (e *Enterprise) GetUpdatedAt() Timestamp {
	if e == nil || e.UpdatedAt == nil {
		return Timestamp{}
	}
	return *e.UpdatedAt
}

// GetWebsiteURL returns the WebsiteURL field if it's non-nil, zero value otherwise.
func (e *Enterprise) GetWebsiteURL() string {
	if e == nil || e.WebsiteURL == nil {
		return ""
	}
	return *e.WebsiteURL
}

// GetCreatedAt returns the CreatedAt field if it's non-nil, zero value otherwise.
func (e *Environment) GetCreatedAt() Timestamp {
	if e == nil || e.CreatedAt == nil {
		return Timestamp{}
	}
	return *e.CreatedAt
}

// GetDeploymentBranchPolicy returns the DeploymentBranchPolicy field.
func (e *Environment) GetDeploymentBranchPolicy() *BranchPolicy {
	if e == nil {
		return nil
	}
	return e.DeploymentBranchPolicy
}

// GetEnvironmentName returns the EnvironmentName field if it's non-nil, zero value otherwise.
func (e *Environment) GetEnvironmentName() string {
	if e == nil || e.EnvironmentName == nil {
		return ""
	}
	return *e.EnvironmentName
}

// GetHTMLURL returns the HTMLURL field if it's non-nil, zero value otherwise.
func (e *Environment) GetHTMLURL() string {
	if e == nil || e.HTMLURL == nil {
		return ""
	}
	return *e.HTMLURL
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (e *Environment) GetID() int64 {
	if e == nil || e.ID == nil {
		return 0
	}
	return *e.ID
}

// GetName returns the Name field if it's non-nil, zero value otherwise.
func (e *Environment) GetName() string {
	if e == nil || e.Name == nil {
		return ""
	}
	return *e.Name
}

// GetNodeID returns the NodeID field if it's non-nil, zero value otherwise.
func (e *Environment) GetNodeID() string {
	if e == nil || e.NodeID == nil {
		return ""
	}
	return *e.NodeID
}

// GetOwner returns the Owner field if it's non-nil, zero value otherwise.
func (e *Environment) GetOwner() string {
	if e == nil || e.Owner == nil {
		return ""
	}
	return *e.Owner
}

// GetRepo returns the Repo field if it's non-nil, zero value otherwise.
func (e *Environment) GetRepo() string {
	if e == nil || e.Repo == nil {
		return ""
	}
	return *e.Repo
}

// GetUpdatedAt returns the UpdatedAt field if it's non-nil, zero value otherwise.
func (e *Environment) GetUpdatedAt() Timestamp {
	if e == nil || e.UpdatedAt == nil {
		return Timestamp{}
	}
	return *e.UpdatedAt
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (e *Environment) GetURL() string {
	if e == nil || e.URL == nil {
		return ""
	}
	return *e.URL
}

// GetWaitTimer returns the WaitTimer field if it's non-nil, zero value otherwise.
func (e *Environment) GetWaitTimer() int {
	if e == nil || e.WaitTimer == nil {
		return 0
	}
	return *e.WaitTimer
}

// GetTotalCount returns the TotalCount field if it's non-nil, zero value otherwise.
func (e *EnvResponse) GetTotalCount() int {
	if e == nil || e.TotalCount == nil {
		return 0
	}
	return *e.TotalCount
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (e *EnvReviewers) GetID() int64 {
	if e == nil || e.ID == nil {
		return 0
	}
	return *e.ID
}

// GetType returns the Type field if it's non-nil, zero value otherwise.
func (e *EnvReviewers) GetType() string {
	if e == nil || e.Type == nil {
		return ""
	}
	return *e.Type
}

// GetActor returns the Actor field.
func (e *Event) GetActor() *User {
	if e == nil {
		return nil
	}
	return e.Actor
}

// GetCreatedAt returns the CreatedAt field if it's non-nil, zero value otherwise.
func (e *Event) GetCreatedAt() time.Time {
	if e == nil || e.CreatedAt == nil {
		return time.Time{}
	}
	return *e.CreatedAt
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (e *Event) GetID() string {
	if e == nil || e.ID == nil {
		return ""
	}
	return *e.ID
}

// GetOrg returns the Org field.
func (e *Event) GetOrg() *Organization {
	if e == nil {
		return nil
	}
	return e.Org
}

// GetPublic returns the Public field if it's non-nil, zero value otherwise.
func (e *Event) GetPublic() bool {
	if e == nil || e.Public == nil {
		return false
	}
	return *e.Public
}

// GetRawPayload returns the RawPayload field if it's non-nil, zero value otherwise.
func (e *Event) GetRawPayload() json.RawMessage {
	if e == nil || e.RawPayload == nil {
		return json.RawMessage{}
	}
	return *e.RawPayload
}

// GetRepo returns the Repo field.
func (e *Event) GetRepo() *Repository {
	if e == nil {
		return nil
	}
	return e.Repo
}

// GetType returns the Type field if it's non-nil, zero value otherwise.
func (e *Event) GetType() string {
	if e == nil || e.Type == nil {
		return ""
	}
	return *e.Type
}

// GetHRef returns the HRef field if it's non-nil, zero value otherwise.
func (f *FeedLink) GetHRef() string {
	if f == nil || f.HRef == nil {
		return ""
	}
	return *f.HRef
}

// GetType returns the Type field if it's non-nil, zero value otherwise.
func (f *FeedLink) GetType() string {
	if f == nil || f.Type == nil {
		return ""
	}
	return *f.Type
}

// GetCurrentUserActorURL returns the CurrentUserActorURL field if it's non-nil, zero value otherwise.
func (f *Feeds) GetCurrentUserActorURL() string {
	if f == nil || f.CurrentUserActorURL == nil {
		return ""
	}
	return *f.CurrentUserActorURL
}

// GetCurrentUserOrganizationURL returns the CurrentUserOrganizationURL field if it's non-nil, zero value otherwise.
func (f *Feeds) GetCurrentUserOrganizationURL() string {
	if f == nil || f.CurrentUserOrganizationURL == nil {
		return ""
	}
	return *f.CurrentUserOrganizationURL
}

// GetCurrentUserPublicURL returns the CurrentUserPublicURL field if it's non-nil, zero value otherwise.
func (f *Feeds) GetCurrentUserPublicURL() string {
	if f == nil || f.CurrentUserPublicURL == nil {
		return ""
	}
	return *f.CurrentUserPublicURL
}

// GetCurrentUserURL returns the CurrentUserURL field if it's non-nil, zero value otherwise.
func (f *Feeds) GetCurrentUserURL() string {
	if f == nil || f.CurrentUserURL == nil {
		return ""
	}
	return *f.CurrentUserURL
}

// GetTimelineURL returns the TimelineURL field if it's non-nil, zero value otherwise.
func (f *Feeds) GetTimelineURL() string {
	if f == nil || f.TimelineURL == nil {
		return ""
	}
	return *f.TimelineURL
}

// GetUserURL returns the UserURL field if it's non-nil, zero value otherwise.
func (f *Feeds) GetUserURL() string {
	if f == nil || f.UserURL == nil {
		return ""
	}
	return *f.UserURL
}

// GetForkee returns the Forkee field.
func (f *ForkEvent) GetForkee() *Repository {
	if f == nil {
		return nil
	}
	return f.Forkee
}

// GetInstallation returns the Installation field.
func (f *ForkEvent) GetInstallation() *Installation {
	if f == nil {
		return nil
	}
	return f.Installation
}

// GetRepo returns the Repo field.
func (f *ForkEvent) GetRepo() *Repository {
	if f == nil {
		return nil
	}
	return f.Repo
}

// GetSender returns the Sender field.
func (f *ForkEvent) GetSender() *User {
	if f == nil {
		return nil
	}
	return f.Sender
}

// GetInclude returns the Include field if it's non-nil, zero value otherwise.
func (g *GetAuditLogOptions) GetInclude() string {
	if g == nil || g.Include == nil {
		return ""
	}
	return *g.Include
}

// GetOrder returns the Order field if it's non-nil, zero value otherwise.
func (g *GetAuditLogOptions) GetOrder() string {
	if g == nil || g.Order == nil {
		return ""
	}
	return *g.Order
}

// GetPhrase returns the Phrase field if it's non-nil, zero value otherwise.
func (g *GetAuditLogOptions) GetPhrase() string {
	if g == nil || g.Phrase == nil {
		return ""
	}
	return *g.Phrase
}

// GetComments returns the Comments field if it's non-nil, zero value otherwise.
func (g *Gist) GetComments() int {
	if g == nil || g.Comments == nil {
		return 0
	}
	return *g.Comments
}

// GetCreatedAt returns the CreatedAt field if it's non-nil, zero value otherwise.
func (g *Gist) GetCreatedAt() time.Time {
	if g == nil || g.CreatedAt == nil {
		return time.Time{}
	}
	return *g.CreatedAt
}

// GetDescription returns the Description field if it's non-nil, zero value otherwise.
func (g *Gist) GetDescription() string {
	if g == nil || g.Description == nil {
		return ""
	}
	return *g.Description
}

// GetFiles returns the Files map if it's non-nil, an empty map otherwise.
func (g *Gist) GetFiles() map[GistFilename]GistFile {
	if g == nil || g.Files == nil {
		return map[GistFilename]GistFile{}
	}
	return g.Files
}

// GetGitPullURL returns the GitPullURL field if it's non-nil, zero value otherwise.
func (g *Gist) GetGitPullURL() string {
	if g == nil || g.GitPullURL == nil {
		return ""
	}
	return *g.GitPullURL
}

// GetGitPushURL returns the GitPushURL field if it's non-nil, zero value otherwise.
func (g *Gist) GetGitPushURL() string {
	if g == nil || g.GitPushURL == nil {
		return ""
	}
	return *g.GitPushURL
}

// GetHTMLURL returns the HTMLURL field if it's non-nil, zero value otherwise.
func (g *Gist) GetHTMLURL() string {
	if g == nil || g.HTMLURL == nil {
		return ""
	}
	return *g.HTMLURL
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (g *Gist) GetID() string {
	if g == nil || g.ID == nil {
		return ""
	}
	return *g.ID
}

// GetNodeID returns the NodeID field if it's non-nil, zero value otherwise.
func (g *Gist) GetNodeID() string {
	if g == nil || g.NodeID == nil {
		return ""
	}
	return *g.NodeID
}

// GetOwner returns the Owner field.
func (g *Gist) GetOwner() *User {
	if g == nil {
		return nil
	}
	return g.Owner
}

// GetPublic returns the Public field if it's non-nil, zero value otherwise.
func (g *Gist) GetPublic() bool {
	if g == nil || g.Public == nil {
		return false
	}
	return *g.Public
}

// GetUpdatedAt returns the UpdatedAt field if it's non-nil, zero value otherwise.
func (g *Gist) GetUpdatedAt() time.Time {
	if g == nil || g.UpdatedAt == nil {
		return time.Time{}
	}
	return *g.UpdatedAt
}

// GetBody returns the Body field if it's non-nil, zero value otherwise.
func (g *GistComment) GetBody() string {
	if g == nil || g.Body == nil {
		return ""
	}
	return *g.Body
}

// GetCreatedAt returns the CreatedAt field if it's non-nil, zero value otherwise.
func (g *GistComment) GetCreatedAt() time.Time {
	if g == nil || g.CreatedAt == nil {
		return time.Time{}
	}
	return *g.CreatedAt
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (g *GistComment) GetID() int64 {
	if g == nil || g.ID == nil {
		return 0
	}
	return *g.ID
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (g *GistComment) GetURL() string {
	if g == nil || g.URL == nil {
		return ""
	}
	return *g.URL
}

// GetUser returns the User field.
func (g *GistComment) GetUser() *User {
	if g == nil {
		return nil
	}
	return g.User
}

// GetChangeStatus returns the ChangeStatus field.
func (g *GistCommit) GetChangeStatus() *CommitStats {
	if g == nil {
		return nil
	}
	return g.ChangeStatus
}

// GetCommittedAt returns the CommittedAt field if it's non-nil, zero value otherwise.
func (g *GistCommit) GetCommittedAt() Timestamp {
	if g == nil || g.CommittedAt == nil {
		return Timestamp{}
	}
	return *g.CommittedAt
}

// GetNodeID returns the NodeID field if it's non-nil, zero value otherwise.
func (g *GistCommit) GetNodeID() string {
	if g == nil || g.NodeID == nil {
		return ""
	}
	return *g.NodeID
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (g *GistCommit) GetURL() string {
	if g == nil || g.URL == nil {
		return ""
	}
	return *g.URL
}

// GetUser returns the User field.
func (g *GistCommit) GetUser() *User {
	if g == nil {
		return nil
	}
	return g.User
}

// GetVersion returns the Version field if it's non-nil, zero value otherwise.
func (g *GistCommit) GetVersion() string {
	if g == nil || g.Version == nil {
		return ""
	}
	return *g.Version
}

// GetContent returns the Content field if it's non-nil, zero value otherwise.
func (g *GistFile) GetContent() string {
	if g == nil || g.Content == nil {
		return ""
	}
	return *g.Content
}

// GetFilename returns the Filename field if it's non-nil, zero value otherwise.
func (g *GistFile) GetFilename() string {
	if g == nil || g.Filename == nil {
		return ""
	}
	return *g.Filename
}

// GetLanguage returns the Language field if it's non-nil, zero value otherwise.
func (g *GistFile) GetLanguage() string {
	if g == nil || g.Language == nil {
		return ""
	}
	return *g.Language
}

// GetRawURL returns the RawURL field if it's non-nil, zero value otherwise.
func (g *GistFile) GetRawURL() string {
	if g == nil || g.RawURL == nil {
		return ""
	}
	return *g.RawURL
}

// GetSize returns the Size field if it's non-nil, zero value otherwise.
func (g *GistFile) GetSize() int {
	if g == nil || g.Size == nil {
		return 0
	}
	return *g.Size
}

// GetType returns the Type field if it's non-nil, zero value otherwise.
func (g *GistFile) GetType() string {
	if g == nil || g.Type == nil {
		return ""
	}
	return *g.Type
}

// GetCreatedAt returns the CreatedAt field if it's non-nil, zero value otherwise.
func (g *GistFork) GetCreatedAt() Timestamp {
	if g == nil || g.CreatedAt == nil {
		return Timestamp{}
	}
	return *g.CreatedAt
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (g *GistFork) GetID() string {
	if g == nil || g.ID == nil {
		return ""
	}
	return *g.ID
}

// GetNodeID returns the NodeID field if it's non-nil, zero value otherwise.
func (g *GistFork) GetNodeID() string {
	if g == nil || g.NodeID == nil {
		return ""
	}
	return *g.NodeID
}

// GetUpdatedAt returns the UpdatedAt field if it's non-nil, zero value otherwise.
func (g *GistFork) GetUpdatedAt() Timestamp {
	if g == nil || g.UpdatedAt == nil {
		return Timestamp{}
	}
	return *g.UpdatedAt
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (g *GistFork) GetURL() string {
	if g == nil || g.URL == nil {
		return ""
	}
	return *g.URL
}

// GetUser returns the User field.
func (g *GistFork) GetUser() *User {
	if g == nil {
		return nil
	}
	return g.User
}

// GetPrivateGists returns the PrivateGists field if it's non-nil, zero value otherwise.
func (g *GistStats) GetPrivateGists() int {
	if g == nil || g.PrivateGists == nil {
		return 0
	}
	return *g.PrivateGists
}

// GetPublicGists returns the PublicGists field if it's non-nil, zero value otherwise.
func (g *GistStats) GetPublicGists() int {
	if g == nil || g.PublicGists == nil {
		return 0
	}
	return *g.PublicGists
}

// GetTotalGists returns the TotalGists field if it's non-nil, zero value otherwise.
func (g *GistStats) GetTotalGists() int {
	if g == nil || g.TotalGists == nil {
		return 0
	}
	return *g.TotalGists
}

// GetAction returns the Action field if it's non-nil, zero value otherwise.
func (g *GitHubAppAuthorizationEvent) GetAction() string {
	if g == nil || g.Action == nil {
		return ""
	}
	return *g.Action
}

// GetSender returns the Sender field.
func (g *GitHubAppAuthorizationEvent) GetSender() *User {
	if g == nil {
		return nil
	}
	return g.Sender
}

// GetName returns the Name field if it's non-nil, zero value otherwise.
func (g *Gitignore) GetName() string {
	if g == nil || g.Name == nil {
		return ""
	}
	return *g.Name
}

// GetSource returns the Source field if it's non-nil, zero value otherwise.
func (g *Gitignore) GetSource() string {
	if g == nil || g.Source == nil {
		return ""
	}
	return *g.Source
}

// GetSHA returns the SHA field if it's non-nil, zero value otherwise.
func (g *GitObject) GetSHA() string {
	if g == nil || g.SHA == nil {
		return ""
	}
	return *g.SHA
}

// GetType returns the Type field if it's non-nil, zero value otherwise.
func (g *GitObject) GetType() string {
	if g == nil || g.Type == nil {
		return ""
	}
	return *g.Type
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (g *GitObject) GetURL() string {
	if g == nil || g.URL == nil {
		return ""
	}
	return *g.URL
}

// GetInstallation returns the Installation field.
func (g *GollumEvent) GetInstallation() *Installation {
	if g == nil {
		return nil
	}
	return g.Installation
}

// GetRepo returns the Repo field.
func (g *GollumEvent) GetRepo() *Repository {
	if g == nil {
		return nil
	}
	return g.Repo
}

// GetSender returns the Sender field.
func (g *GollumEvent) GetSender() *User {
	if g == nil {
		return nil
	}
	return g.Sender
}

// GetEmail returns the Email field if it's non-nil, zero value otherwise.
func (g *GPGEmail) GetEmail() string {
	if g == nil || g.Email == nil {
		return ""
	}
	return *g.Email
}

// GetVerified returns the Verified field if it's non-nil, zero value otherwise.
func (g *GPGEmail) GetVerified() bool {
	if g == nil || g.Verified == nil {
		return false
	}
	return *g.Verified
}

// GetCanCertify returns the CanCertify field if it's non-nil, zero value otherwise.
func (g *GPGKey) GetCanCertify() bool {
	if g == nil || g.CanCertify == nil {
		return false
	}
	return *g.CanCertify
}

// GetCanEncryptComms returns the CanEncryptComms field if it's non-nil, zero value otherwise.
func (g *GPGKey) GetCanEncryptComms() bool {
	if g == nil || g.CanEncryptComms == nil {
		return false
	}
	return *g.CanEncryptComms
}

// GetCanEncryptStorage returns the CanEncryptStorage field if it's non-nil, zero value otherwise.
func (g *GPGKey) GetCanEncryptStorage() bool {
	if g == nil || g.CanEncryptStorage == nil {
		return false
	}
	return *g.CanEncryptStorage
}

// GetCanSign returns the CanSign field if it's non-nil, zero value otherwise.
func (g *GPGKey) GetCanSign() bool {
	if g == nil || g.CanSign == nil {
		return false
	}
	return *g.CanSign
}

// GetCreatedAt returns the CreatedAt field if it's non-nil, zero value otherwise.
func (g *GPGKey) GetCreatedAt() time.Time {
	if g == nil || g.CreatedAt == nil {
		return time.Time{}
	}
	return *g.CreatedAt
}

// GetExpiresAt returns the ExpiresAt field if it's non-nil, zero value otherwise.
func (g *GPGKey) GetExpiresAt() time.Time {
	if g == nil || g.ExpiresAt == nil {
		return time.Time{}
	}
	return *g.ExpiresAt
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (g *GPGKey) GetID() int64 {
	if g == nil || g.ID == nil {
		return 0
	}
	return *g.ID
}

// GetKeyID returns the KeyID field if it's non-nil, zero value otherwise.
func (g *GPGKey) GetKeyID() string {
	if g == nil || g.KeyID == nil {
		return ""
	}
	return *g.KeyID
}

// GetPrimaryKeyID returns the PrimaryKeyID field if it's non-nil, zero value otherwise.
func (g *GPGKey) GetPrimaryKeyID() int64 {
	if g == nil || g.PrimaryKeyID == nil {
		return 0
	}
	return *g.PrimaryKeyID
}

// GetPublicKey returns the PublicKey field if it's non-nil, zero value otherwise.
func (g *GPGKey) GetPublicKey() string {
	if g == nil || g.PublicKey == nil {
		return ""
	}
	return *g.PublicKey
}

// GetApp returns the App field.
func (g *Grant) GetApp() *AuthorizationApp {
	if g == nil {
		return nil
	}
	return g.App
}

// GetCreatedAt returns the CreatedAt field if it's non-nil, zero value otherwise.
func (g *Grant) GetCreatedAt() Timestamp {
	if g == nil || g.CreatedAt == nil {
		return Timestamp{}
	}
	return *g.CreatedAt
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (g *Grant) GetID() int64 {
	if g == nil || g.ID == nil {
		return 0
	}
	return *g.ID
}

// GetUpdatedAt returns the UpdatedAt field if it's non-nil, zero value otherwise.
func (g *Grant) GetUpdatedAt() Timestamp {
	if g == nil || g.UpdatedAt == nil {
		return Timestamp{}
	}
	return *g.UpdatedAt
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (g *Grant) GetURL() string {
	if g == nil || g.URL == nil {
		return ""
	}
	return *g.URL
}

// GetAuthor returns the Author field.
func (h *HeadCommit) GetAuthor() *CommitAuthor {
	if h == nil {
		return nil
	}
	return h.Author
}

// GetCommitter returns the Committer field.
func (h *HeadCommit) GetCommitter() *CommitAuthor {
	if h == nil {
		return nil
	}
	return h.Committer
}

// GetDistinct returns the Distinct field if it's non-nil, zero value otherwise.
func (h *HeadCommit) GetDistinct() bool {
	if h == nil || h.Distinct == nil {
		return false
	}
	return *h.Distinct
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (h *HeadCommit) GetID() string {
	if h == nil || h.ID == nil {
		return ""
	}
	return *h.ID
}

// GetMessage returns the Message field if it's non-nil, zero value otherwise.
func (h *HeadCommit) GetMessage() string {
	if h == nil || h.Message == nil {
		return ""
	}
	return *h.Message
}

// GetSHA returns the SHA field if it's non-nil, zero value otherwise.
func (h *HeadCommit) GetSHA() string {
	if h == nil || h.SHA == nil {
		return ""
	}
	return *h.SHA
}

// GetTimestamp returns the Timestamp field if it's non-nil, zero value otherwise.
func (h *HeadCommit) GetTimestamp() Timestamp {
	if h == nil || h.Timestamp == nil {
		return Timestamp{}
	}
	return *h.Timestamp
}

// GetTreeID returns the TreeID field if it's non-nil, zero value otherwise.
func (h *HeadCommit) GetTreeID() string {
	if h == nil || h.TreeID == nil {
		return ""
	}
	return *h.TreeID
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (h *HeadCommit) GetURL() string {
	if h == nil || h.URL == nil {
		return ""
	}
	return *h.URL
}

// GetActive returns the Active field if it's non-nil, zero value otherwise.
func (h *Hook) GetActive() bool {
	if h == nil || h.Active == nil {
		return false
	}
	return *h.Active
}

// GetCreatedAt returns the CreatedAt field if it's non-nil, zero value otherwise.
func (h *Hook) GetCreatedAt() time.Time {
	if h == nil || h.CreatedAt == nil {
		return time.Time{}
	}
	return *h.CreatedAt
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (h *Hook) GetID() int64 {
	if h == nil || h.ID == nil {
		return 0
	}
	return *h.ID
}

// GetName returns the Name field if it's non-nil, zero value otherwise.
func (h *Hook) GetName() string {
	if h == nil || h.Name == nil {
		return ""
	}
	return *h.Name
}

// GetPingURL returns the PingURL field if it's non-nil, zero value otherwise.
func (h *Hook) GetPingURL() string {
	if h == nil || h.PingURL == nil {
		return ""
	}
	return *h.PingURL
}

// GetTestURL returns the TestURL field if it's non-nil, zero value otherwise.
func (h *Hook) GetTestURL() string {
	if h == nil || h.TestURL == nil {
		return ""
	}
	return *h.TestURL
}

// GetType returns the Type field if it's non-nil, zero value otherwise.
func (h *Hook) GetType() string {
	if h == nil || h.Type == nil {
		return ""
	}
	return *h.Type
}

// GetUpdatedAt returns the UpdatedAt field if it's non-nil, zero value otherwise.
func (h *Hook) GetUpdatedAt() time.Time {
	if h == nil || h.UpdatedAt == nil {
		return time.Time{}
	}
	return *h.UpdatedAt
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (h *Hook) GetURL() string {
	if h == nil || h.URL == nil {
		return ""
	}
	return *h.URL
}

// GetContentType returns the ContentType field if it's non-nil, zero value otherwise.
func (h *HookConfig) GetContentType() string {
	if h == nil || h.ContentType == nil {
		return ""
	}
	return *h.ContentType
}

// GetInsecureSSL returns the InsecureSSL field if it's non-nil, zero value otherwise.
func (h *HookConfig) GetInsecureSSL() string {
	if h == nil || h.InsecureSSL == nil {
		return ""
	}
	return *h.InsecureSSL
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (h *HookConfig) GetURL() string {
	if h == nil || h.URL == nil {
		return ""
	}
	return *h.URL
}

// GetActiveHooks returns the ActiveHooks field if it's non-nil, zero value otherwise.
func (h *HookStats) GetActiveHooks() int {
	if h == nil || h.ActiveHooks == nil {
		return 0
	}
	return *h.ActiveHooks
}

// GetInactiveHooks returns the InactiveHooks field if it's non-nil, zero value otherwise.
func (h *HookStats) GetInactiveHooks() int {
	if h == nil || h.InactiveHooks == nil {
		return 0
	}
	return *h.InactiveHooks
}

// GetTotalHooks returns the TotalHooks field if it's non-nil, zero value otherwise.
func (h *HookStats) GetTotalHooks() int {
	if h == nil || h.TotalHooks == nil {
		return 0
	}
	return *h.TotalHooks
}

// GetGroupDescription returns the GroupDescription field if it's non-nil, zero value otherwise.
func (i *IDPGroup) GetGroupDescription() string {
	if i == nil || i.GroupDescription == nil {
		return ""
	}
	return *i.GroupDescription
}

// GetGroupID returns the GroupID field if it's non-nil, zero value otherwise.
func (i *IDPGroup) GetGroupID() string {
	if i == nil || i.GroupID == nil {
		return ""
	}
	return *i.GroupID
}

// GetGroupName returns the GroupName field if it's non-nil, zero value otherwise.
func (i *IDPGroup) GetGroupName() string {
	if i == nil || i.GroupName == nil {
		return ""
	}
	return *i.GroupName
}

// GetAuthorsCount returns the AuthorsCount field if it's non-nil, zero value otherwise.
func (i *Import) GetAuthorsCount() int {
	if i == nil || i.AuthorsCount == nil {
		return 0
	}
	return *i.AuthorsCount
}

// GetAuthorsURL returns the AuthorsURL field if it's non-nil, zero value otherwise.
func (i *Import) GetAuthorsURL() string {
	if i == nil || i.AuthorsURL == nil {
		return ""
	}
	return *i.AuthorsURL
}

// GetCommitCount returns the CommitCount field if it's non-nil, zero value otherwise.
func (i *Import) GetCommitCount() int {
	if i == nil || i.CommitCount == nil {
		return 0
	}
	return *i.CommitCount
}

// GetFailedStep returns the FailedStep field if it's non-nil, zero value otherwise.
func (i *Import) GetFailedStep() string {
	if i == nil || i.FailedStep == nil {
		return ""
	}
	return *i.FailedStep
}

// GetHasLargeFiles returns the HasLargeFiles field if it's non-nil, zero value otherwise.
func (i *Import) GetHasLargeFiles() bool {
	if i == nil || i.HasLargeFiles == nil {
		return false
	}
	return *i.HasLargeFiles
}

// GetHTMLURL returns the HTMLURL field if it's non-nil, zero value otherwise.
func (i *Import) GetHTMLURL() string {
	if i == nil || i.HTMLURL == nil {
		return ""
	}
	return *i.HTMLURL
}

// GetHumanName returns the HumanName field if it's non-nil, zero value otherwise.
func (i *Import) GetHumanName() string {
	if i == nil || i.HumanName == nil {
		return ""
	}
	return *i.HumanName
}

// GetLargeFilesCount returns the LargeFilesCount field if it's non-nil, zero value otherwise.
func (i *Import) GetLargeFilesCount() int {
	if i == nil || i.LargeFilesCount == nil {
		return 0
	}
	return *i.LargeFilesCount
}

// GetLargeFilesSize returns the LargeFilesSize field if it's non-nil, zero value otherwise.
func (i *Import) GetLargeFilesSize() int {
	if i == nil || i.LargeFilesSize == nil {
		return 0
	}
	return *i.LargeFilesSize
}

// GetMessage returns the Message field if it's non-nil, zero value otherwise.
func (i *Import) GetMessage() string {
	if i == nil || i.Message == nil {
		return ""
	}
	return *i.Message
}

// GetPercent returns the Percent field if it's non-nil, zero value otherwise.
func (i *Import) GetPercent() int {
	if i == nil || i.Percent == nil {
		return 0
	}
	return *i.Percent
}

// GetPushPercent returns the PushPercent field if it's non-nil, zero value otherwise.
func (i *Import) GetPushPercent() int {
	if i == nil || i.PushPercent == nil {
		return 0
	}
	return *i.PushPercent
}

// GetRepositoryURL returns the RepositoryURL field if it's non-nil, zero value otherwise.
func (i *Import) GetRepositoryURL() string {
	if i == nil || i.RepositoryURL == nil {
		return ""
	}
	return *i.RepositoryURL
}

// GetStatus returns the Status field if it's non-nil, zero value otherwise.
func (i *Import) GetStatus() string {
	if i == nil || i.Status == nil {
		return ""
	}
	return *i.Status
}

// GetStatusText returns the StatusText field if it's non-nil, zero value otherwise.
func (i *Import) GetStatusText() string {
	if i == nil || i.StatusText == nil {
		return ""
	}
	return *i.StatusText
}

// GetTFVCProject returns the TFVCProject field if it's non-nil, zero value otherwise.
func (i *Import) GetTFVCProject() string {
	if i == nil || i.TFVCProject == nil {
		return ""
	}
	return *i.TFVCProject
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (i *Import) GetURL() string {
	if i == nil || i.URL == nil {
		return ""
	}
	return *i.URL
}

// GetUseLFS returns the UseLFS field if it's non-nil, zero value otherwise.
func (i *Import) GetUseLFS() string {
	if i == nil || i.UseLFS == nil {
		return ""
	}
	return *i.UseLFS
}

// GetVCS returns the VCS field if it's non-nil, zero value otherwise.
func (i *Import) GetVCS() string {
	if i == nil || i.VCS == nil {
		return ""
	}
	return *i.VCS
}

// GetVCSPassword returns the VCSPassword field if it's non-nil, zero value otherwise.
func (i *Import) GetVCSPassword() string {
	if i == nil || i.VCSPassword == nil {
		return ""
	}
	return *i.VCSPassword
}

// GetVCSURL returns the VCSURL field if it's non-nil, zero value otherwise.
func (i *Import) GetVCSURL() string {
	if i == nil || i.VCSURL == nil {
		return ""
	}
	return *i.VCSURL
}

// GetVCSUsername returns the VCSUsername field if it's non-nil, zero value otherwise.
func (i *Import) GetVCSUsername() string {
	if i == nil || i.VCSUsername == nil {
		return ""
	}
	return *i.VCSUsername
}

// GetAccessTokensURL returns the AccessTokensURL field if it's non-nil, zero value otherwise.
func (i *Installation) GetAccessTokensURL() string {
	if i == nil || i.AccessTokensURL == nil {
		return ""
	}
	return *i.AccessTokensURL
}

// GetAccount returns the Account field.
func (i *Installation) GetAccount() *User {
	if i == nil {
		return nil
	}
	return i.Account
}

// GetAppID returns the AppID field if it's non-nil, zero value otherwise.
func (i *Installation) GetAppID() int64 {
	if i == nil || i.AppID == nil {
		return 0
	}
	return *i.AppID
}

// GetAppSlug returns the AppSlug field if it's non-nil, zero value otherwise.
func (i *Installation) GetAppSlug() string {
	if i == nil || i.AppSlug == nil {
		return ""
	}
	return *i.AppSlug
}

// GetCreatedAt returns the CreatedAt field if it's non-nil, zero value otherwise.
func (i *Installation) GetCreatedAt() Timestamp {
	if i == nil || i.CreatedAt == nil {
		return Timestamp{}
	}
	return *i.CreatedAt
}

// GetHasMultipleSingleFiles returns the HasMultipleSingleFiles field if it's non-nil, zero value otherwise.
func (i *Installation) GetHasMultipleSingleFiles() bool {
	if i == nil || i.HasMultipleSingleFiles == nil {
		return false
	}
	return *i.HasMultipleSingleFiles
}

// GetHTMLURL returns the HTMLURL field if it's non-nil, zero value otherwise.
func (i *Installation) GetHTMLURL() string {
	if i == nil || i.HTMLURL == nil {
		return ""
	}
	return *i.HTMLURL
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (i *Installation) GetID() int64 {
	if i == nil || i.ID == nil {
		return 0
	}
	return *i.ID
}

// GetNodeID returns the NodeID field if it's non-nil, zero value otherwise.
func (i *Installation) GetNodeID() string {
	if i == nil || i.NodeID == nil {
		return ""
	}
	return *i.NodeID
}

// GetPermissions returns the Permissions field.
func (i *Installation) GetPermissions() *InstallationPermissions {
	if i == nil {
		return nil
	}
	return i.Permissions
}

// GetRepositoriesURL returns the RepositoriesURL field if it's non-nil, zero value otherwise.
func (i *Installation) GetRepositoriesURL() string {
	if i == nil || i.RepositoriesURL == nil {
		return ""
	}
	return *i.RepositoriesURL
}

// GetRepositorySelection returns the RepositorySelection field if it's non-nil, zero value otherwise.
func (i *Installation) GetRepositorySelection() string {
	if i == nil || i.RepositorySelection == nil {
		return ""
	}
	return *i.RepositorySelection
}

// GetSingleFileName returns the SingleFileName field if it's non-nil, zero value otherwise.
func (i *Installation) GetSingleFileName() string {
	if i == nil || i.SingleFileName == nil {
		return ""
	}
	return *i.SingleFileName
}

// GetSuspendedAt returns the SuspendedAt field if it's non-nil, zero value otherwise.
func (i *Installation) GetSuspendedAt() Timestamp {
	if i == nil || i.SuspendedAt == nil {
		return Timestamp{}
	}
	return *i.SuspendedAt
}

// GetSuspendedBy returns the SuspendedBy field.
func (i *Installation) GetSuspendedBy() *User {
	if i == nil {
		return nil
	}
	return i.SuspendedBy
}

// GetTargetID returns the TargetID field if it's non-nil, zero value otherwise.
func (i *Installation) GetTargetID() int64 {
	if i == nil || i.TargetID == nil {
		return 0
	}
	return *i.TargetID
}

// GetTargetType returns the TargetType field if it's non-nil, zero value otherwise.
func (i *Installation) GetTargetType() string {
	if i == nil || i.TargetType == nil {
		return ""
	}
	return *i.TargetType
}

// GetUpdatedAt returns the UpdatedAt field if it's non-nil, zero value otherwise.
func (i *Installation) GetUpdatedAt() Timestamp {
	if i == nil || i.UpdatedAt == nil {
		return Timestamp{}
	}
	return *i.UpdatedAt
}

// GetAction returns the Action field if it's non-nil, zero value otherwise.
func (i *InstallationEvent) GetAction() string {
	if i == nil || i.Action == nil {
		return ""
	}
	return *i.Action
}

// GetInstallation returns the Installation field.
func (i *InstallationEvent) GetInstallation() *Installation {
	if i == nil {
		return nil
	}
	return i.Installation
}

// GetSender returns the Sender field.
func (i *InstallationEvent) GetSender() *User {
	if i == nil {
		return nil
	}
	return i.Sender
}

// GetActions returns the Actions field if it's non-nil, zero value otherwise.
func (i *InstallationPermissions) GetActions() string {
	if i == nil || i.Actions == nil {
		return ""
	}
	return *i.Actions
}

// GetAdministration returns the Administration field if it's non-nil, zero value otherwise.
func (i *InstallationPermissions) GetAdministration() string {
	if i == nil || i.Administration == nil {
		return ""
	}
	return *i.Administration
}

// GetBlocking returns the Blocking field if it's non-nil, zero value otherwise.
func (i *InstallationPermissions) GetBlocking() string {
	if i == nil || i.Blocking == nil {
		return ""
	}
	return *i.Blocking
}

// GetChecks returns the Checks field if it's non-nil, zero value otherwise.
func (i *InstallationPermissions) GetChecks() string {
	if i == nil || i.Checks == nil {
		return ""
	}
	return *i.Checks
}

// GetContentReferences returns the ContentReferences field if it's non-nil, zero value otherwise.
func (i *InstallationPermissions) GetContentReferences() string {
	if i == nil || i.ContentReferences == nil {
		return ""
	}
	return *i.ContentReferences
}

// GetContents returns the Contents field if it's non-nil, zero value otherwise.
func (i *InstallationPermissions) GetContents() string {
	if i == nil || i.Contents == nil {
		return ""
	}
	return *i.Contents
}

// GetDeployments returns the Deployments field if it's non-nil, zero value otherwise.
func (i *InstallationPermissions) GetDeployments() string {
	if i == nil || i.Deployments == nil {
		return ""
	}
	return *i.Deployments
}

// GetEmails returns the Emails field if it's non-nil, zero value otherwise.
func (i *InstallationPermissions) GetEmails() string {
	if i == nil || i.Emails == nil {
		return ""
	}
	return *i.Emails
}

// GetEnvironments returns the Environments field if it's non-nil, zero value otherwise.
func (i *InstallationPermissions) GetEnvironments() string {
	if i == nil || i.Environments == nil {
		return ""
	}
	return *i.Environments
}

// GetFollowers returns the Followers field if it's non-nil, zero value otherwise.
func (i *InstallationPermissions) GetFollowers() string {
	if i == nil || i.Followers == nil {
		return ""
	}
	return *i.Followers
}

// GetIssues returns the Issues field if it's non-nil, zero value otherwise.
func (i *InstallationPermissions) GetIssues() string {
	if i == nil || i.Issues == nil {
		return ""
	}
	return *i.Issues
}

// GetMembers returns the Members field if it's non-nil, zero value otherwise.
func (i *InstallationPermissions) GetMembers() string {
	if i == nil || i.Members == nil {
		return ""
	}
	return *i.Members
}

// GetMetadata returns the Metadata field if it's non-nil, zero value otherwise.
func (i *InstallationPermissions) GetMetadata() string {
	if i == nil || i.Metadata == nil {
		return ""
	}
	return *i.Metadata
}

// GetOrganizationAdministration returns the OrganizationAdministration field if it's non-nil, zero value otherwise.
func (i *InstallationPermissions) GetOrganizationAdministration() string {
	if i == nil || i.OrganizationAdministration == nil {
		return ""
	}
	return *i.OrganizationAdministration
}

// GetOrganizationHooks returns the OrganizationHooks field if it's non-nil, zero value otherwise.
func (i *InstallationPermissions) GetOrganizationHooks() string {
	if i == nil || i.OrganizationHooks == nil {
		return ""
	}
	return *i.OrganizationHooks
}

// GetOrganizationPlan returns the OrganizationPlan field if it's non-nil, zero value otherwise.
func (i *InstallationPermissions) GetOrganizationPlan() string {
	if i == nil || i.OrganizationPlan == nil {
		return ""
	}
	return *i.OrganizationPlan
}

// GetOrganizationPreReceiveHooks returns the OrganizationPreReceiveHooks field if it's non-nil, zero value otherwise.
func (i *InstallationPermissions) GetOrganizationPreReceiveHooks() string {
	if i == nil || i.OrganizationPreReceiveHooks == nil {
		return ""
	}
	return *i.OrganizationPreReceiveHooks
}

// GetOrganizationProjects returns the OrganizationProjects field if it's non-nil, zero value otherwise.
func (i *InstallationPermissions) GetOrganizationProjects() string {
	if i == nil || i.OrganizationProjects == nil {
		return ""
	}
	return *i.OrganizationProjects
}

// GetOrganizationSecrets returns the OrganizationSecrets field if it's non-nil, zero value otherwise.
func (i *InstallationPermissions) GetOrganizationSecrets() string {
	if i == nil || i.OrganizationSecrets == nil {
		return ""
	}
	return *i.OrganizationSecrets
}

// GetOrganizationSelfHostedRunners returns the OrganizationSelfHostedRunners field if it's non-nil, zero value otherwise.
func (i *InstallationPermissions) GetOrganizationSelfHostedRunners() string {
	if i == nil || i.OrganizationSelfHostedRunners == nil {
		return ""
	}
	return *i.OrganizationSelfHostedRunners
}

// GetOrganizationUserBlocking returns the OrganizationUserBlocking field if it's non-nil, zero value otherwise.
func (i *InstallationPermissions) GetOrganizationUserBlocking() string {
	if i == nil || i.OrganizationUserBlocking == nil {
		return ""
	}
	return *i.OrganizationUserBlocking
}

// GetPackages returns the Packages field if it's non-nil, zero value otherwise.
func (i *InstallationPermissions) GetPackages() string {
	if i == nil || i.Packages == nil {
		return ""
	}
	return *i.Packages
}

// GetPages returns the Pages field if it's non-nil, zero value otherwise.
func (i *InstallationPermissions) GetPages() string {
	if i == nil || i.Pages == nil {
		return ""
	}
	return *i.Pages
}

// GetPullRequests returns the PullRequests field if it's non-nil, zero value otherwise.
func (i *InstallationPermissions) GetPullRequests() string {
	if i == nil || i.PullRequests == nil {
		return ""
	}
	return *i.PullRequests
}

// GetRepositoryHooks returns the RepositoryHooks field if it's non-nil, zero value otherwise.
func (i *InstallationPermissions) GetRepositoryHooks() string {
	if i == nil || i.RepositoryHooks == nil {
		return ""
	}
	return *i.RepositoryHooks
}

// GetRepositoryPreReceiveHooks returns the RepositoryPreReceiveHooks field if it's non-nil, zero value otherwise.
func (i *InstallationPermissions) GetRepositoryPreReceiveHooks() string {
	if i == nil || i.RepositoryPreReceiveHooks == nil {
		return ""
	}
	return *i.RepositoryPreReceiveHooks
}

// GetRepositoryProjects returns the RepositoryProjects field if it's non-nil, zero value otherwise.
func (i *InstallationPermissions) GetRepositoryProjects() string {
	if i == nil || i.RepositoryProjects == nil {
		return ""
	}
	return *i.RepositoryProjects
}

// GetSecrets returns the Secrets field if it's non-nil, zero value otherwise.
func (i *InstallationPermissions) GetSecrets() string {
	if i == nil || i.Secrets == nil {
		return ""
	}
	return *i.Secrets
}

// GetSecretScanningAlerts returns the SecretScanningAlerts field if it's non-nil, zero value otherwise.
func (i *InstallationPermissions) GetSecretScanningAlerts() string {
	if i == nil || i.SecretScanningAlerts == nil {
		return ""
	}
	return *i.SecretScanningAlerts
}

// GetSecurityEvents returns the SecurityEvents field if it's non-nil, zero value otherwise.
func (i *InstallationPermissions) GetSecurityEvents() string {
	if i == nil || i.SecurityEvents == nil {
		return ""
	}
	return *i.SecurityEvents
}

// GetSingleFile returns the SingleFile field if it's non-nil, zero value otherwise.
func (i *InstallationPermissions) GetSingleFile() string {
	if i == nil || i.SingleFile == nil {
		return ""
	}
	return *i.SingleFile
}

// GetStatuses returns the Statuses field if it's non-nil, zero value otherwise.
func (i *InstallationPermissions) GetStatuses() string {
	if i == nil || i.Statuses == nil {
		return ""
	}
	return *i.Statuses
}

// GetTeamDiscussions returns the TeamDiscussions field if it's non-nil, zero value otherwise.
func (i *InstallationPermissions) GetTeamDiscussions() string {
	if i == nil || i.TeamDiscussions == nil {
		return ""
	}
	return *i.TeamDiscussions
}

// GetVulnerabilityAlerts returns the VulnerabilityAlerts field if it's non-nil, zero value otherwise.
func (i *InstallationPermissions) GetVulnerabilityAlerts() string {
	if i == nil || i.VulnerabilityAlerts == nil {
		return ""
	}
	return *i.VulnerabilityAlerts
}

// GetWorkflows returns the Workflows field if it's non-nil, zero value otherwise.
func (i *InstallationPermissions) GetWorkflows() string {
	if i == nil || i.Workflows == nil {
		return ""
	}
	return *i.Workflows
}

// GetAction returns the Action field if it's non-nil, zero value otherwise.
func (i *InstallationRepositoriesEvent) GetAction() string {
	if i == nil || i.Action == nil {
		return ""
	}
	return *i.Action
}

// GetInstallation returns the Installation field.
func (i *InstallationRepositoriesEvent) GetInstallation() *Installation {
	if i == nil {
		return nil
	}
	return i.Installation
}

// GetRepositorySelection returns the RepositorySelection field if it's non-nil, zero value otherwise.
func (i *InstallationRepositoriesEvent) GetRepositorySelection() string {
	if i == nil || i.RepositorySelection == nil {
		return ""
	}
	return *i.RepositorySelection
}

// GetSender returns the Sender field.
func (i *InstallationRepositoriesEvent) GetSender() *User {
	if i == nil {
		return nil
	}
	return i.Sender
}

// GetExpiresAt returns the ExpiresAt field if it's non-nil, zero value otherwise.
func (i *InstallationToken) GetExpiresAt() time.Time {
	if i == nil || i.ExpiresAt == nil {
		return time.Time{}
	}
	return *i.ExpiresAt
}

// GetPermissions returns the Permissions field.
func (i *InstallationToken) GetPermissions() *InstallationPermissions {
	if i == nil {
		return nil
	}
	return i.Permissions
}

// GetToken returns the Token field if it's non-nil, zero value otherwise.
func (i *InstallationToken) GetToken() string {
	if i == nil || i.Token == nil {
		return ""
	}
	return *i.Token
}

// GetPermissions returns the Permissions field.
func (i *InstallationTokenOptions) GetPermissions() *InstallationPermissions {
	if i == nil {
		return nil
	}
	return i.Permissions
}

// GetExpiresAt returns the ExpiresAt field if it's non-nil, zero value otherwise.
func (i *InteractionRestriction) GetExpiresAt() Timestamp {
	if i == nil || i.ExpiresAt == nil {
		return Timestamp{}
	}
	return *i.ExpiresAt
}

// GetLimit returns the Limit field if it's non-nil, zero value otherwise.
func (i *InteractionRestriction) GetLimit() string {
	if i == nil || i.Limit == nil {
		return ""
	}
	return *i.Limit
}

// GetOrigin returns the Origin field if it's non-nil, zero value otherwise.
func (i *InteractionRestriction) GetOrigin() string {
	if i == nil || i.Origin == nil {
		return ""
	}
	return *i.Origin
}

// GetCreatedAt returns the CreatedAt field if it's non-nil, zero value otherwise.
func (i *Invitation) GetCreatedAt() time.Time {
	if i == nil || i.CreatedAt == nil {
		return time.Time{}
	}
	return *i.CreatedAt
}

// GetEmail returns the Email field if it's non-nil, zero value otherwise.
func (i *Invitation) GetEmail() string {
	if i == nil || i.Email == nil {
		return ""
	}
	return *i.Email
}

// GetFailedAt returns the FailedAt field if it's non-nil, zero value otherwise.
func (i *Invitation) GetFailedAt() Timestamp {
	if i == nil || i.FailedAt == nil {
		return Timestamp{}
	}
	return *i.FailedAt
}

// GetFailedReason returns the FailedReason field if it's non-nil, zero value otherwise.
func (i *Invitation) GetFailedReason() string {
	if i == nil || i.FailedReason == nil {
		return ""
	}
	return *i.FailedReason
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (i *Invitation) GetID() int64 {
	if i == nil || i.ID == nil {
		return 0
	}
	return *i.ID
}

// GetInvitationTeamURL returns the InvitationTeamURL field if it's non-nil, zero value otherwise.
func (i *Invitation) GetInvitationTeamURL() string {
	if i == nil || i.InvitationTeamURL == nil {
		return ""
	}
	return *i.InvitationTeamURL
}

// GetInviter returns the Inviter field.
func (i *Invitation) GetInviter() *User {
	if i == nil {
		return nil
	}
	return i.Inviter
}

// GetLogin returns the Login field if it's non-nil, zero value otherwise.
func (i *Invitation) GetLogin() string {
	if i == nil || i.Login == nil {
		return ""
	}
	return *i.Login
}

// GetNodeID returns the NodeID field if it's non-nil, zero value otherwise.
func (i *Invitation) GetNodeID() string {
	if i == nil || i.NodeID == nil {
		return ""
	}
	return *i.NodeID
}

// GetRole returns the Role field if it's non-nil, zero value otherwise.
func (i *Invitation) GetRole() string {
	if i == nil || i.Role == nil {
		return ""
	}
	return *i.Role
}

// GetTeamCount returns the TeamCount field if it's non-nil, zero value otherwise.
func (i *Invitation) GetTeamCount() int {
	if i == nil || i.TeamCount == nil {
		return 0
	}
	return *i.TeamCount
}

// GetActiveLockReason returns the ActiveLockReason field if it's non-nil, zero value otherwise.
func (i *Issue) GetActiveLockReason() string {
	if i == nil || i.ActiveLockReason == nil {
		return ""
	}
	return *i.ActiveLockReason
}

// GetAssignee returns the Assignee field.
func (i *Issue) GetAssignee() *User {
	if i == nil {
		return nil
	}
	return i.Assignee
}

// GetAuthorAssociation returns the AuthorAssociation field if it's non-nil, zero value otherwise.
func (i *Issue) GetAuthorAssociation() string {
	if i == nil || i.AuthorAssociation == nil {
		return ""
	}
	return *i.AuthorAssociation
}

// GetBody returns the Body field if it's non-nil, zero value otherwise.
func (i *Issue) GetBody() string {
	if i == nil || i.Body == nil {
		return ""
	}
	return *i.Body
}

// GetClosedAt returns the ClosedAt field if it's non-nil, zero value otherwise.
func (i *Issue) GetClosedAt() time.Time {
	if i == nil || i.ClosedAt == nil {
		return time.Time{}
	}
	return *i.ClosedAt
}

// GetClosedBy returns the ClosedBy field.
func (i *Issue) GetClosedBy() *User {
	if i == nil {
		return nil
	}
	return i.ClosedBy
}

// GetComments returns the Comments field if it's non-nil, zero value otherwise.
func (i *Issue) GetComments() int {
	if i == nil || i.Comments == nil {
		return 0
	}
	return *i.Comments
}

// GetCommentsURL returns the CommentsURL field if it's non-nil, zero value otherwise.
func (i *Issue) GetCommentsURL() string {
	if i == nil || i.CommentsURL == nil {
		return ""
	}
	return *i.CommentsURL
}

// GetCreatedAt returns the CreatedAt field if it's non-nil, zero value otherwise.
func (i *Issue) GetCreatedAt() time.Time {
	if i == nil || i.CreatedAt == nil {
		return time.Time{}
	}
	return *i.CreatedAt
}

// GetEventsURL returns the EventsURL field if it's non-nil, zero value otherwise.
func (i *Issue) GetEventsURL() string {
	if i == nil || i.EventsURL == nil {
		return ""
	}
	return *i.EventsURL
}

// GetHTMLURL returns the HTMLURL field if it's non-nil, zero value otherwise.
func (i *Issue) GetHTMLURL() string {
	if i == nil || i.HTMLURL == nil {
		return ""
	}
	return *i.HTMLURL
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (i *Issue) GetID() int64 {
	if i == nil || i.ID == nil {
		return 0
	}
	return *i.ID
}

// GetLabelsURL returns the LabelsURL field if it's non-nil, zero value otherwise.
func (i *Issue) GetLabelsURL() string {
	if i == nil || i.LabelsURL == nil {
		return ""
	}
	return *i.LabelsURL
}

// GetLocked returns the Locked field if it's non-nil, zero value otherwise.
func (i *Issue) GetLocked() bool {
	if i == nil || i.Locked == nil {
		return false
	}
	return *i.Locked
}

// GetMilestone returns the Milestone field.
func (i *Issue) GetMilestone() *Milestone {
	if i == nil {
		return nil
	}
	return i.Milestone
}

// GetNodeID returns the NodeID field if it's non-nil, zero value otherwise.
func (i *Issue) GetNodeID() string {
	if i == nil || i.NodeID == nil {
		return ""
	}
	return *i.NodeID
}

// GetNumber returns the Number field if it's non-nil, zero value otherwise.
func (i *Issue) GetNumber() int {
	if i == nil || i.Number == nil {
		return 0
	}
	return *i.Number
}

// GetPullRequestLinks returns the PullRequestLinks field.
func (i *Issue) GetPullRequestLinks() *PullRequestLinks {
	if i == nil {
		return nil
	}
	return i.PullRequestLinks
}

// GetReactions returns the Reactions field.
func (i *Issue) GetReactions() *Reactions {
	if i == nil {
		return nil
	}
	return i.Reactions
}

// GetRepository returns the Repository field.
func (i *Issue) GetRepository() *Repository {
	if i == nil {
		return nil
	}
	return i.Repository
}

// GetRepositoryURL returns the RepositoryURL field if it's non-nil, zero value otherwise.
func (i *Issue) GetRepositoryURL() string {
	if i == nil || i.RepositoryURL == nil {
		return ""
	}
	return *i.RepositoryURL
}

// GetState returns the State field if it's non-nil, zero value otherwise.
func (i *Issue) GetState() string {
	if i == nil || i.State == nil {
		return ""
	}
	return *i.State
}

// GetTitle returns the Title field if it's non-nil, zero value otherwise.
func (i *Issue) GetTitle() string {
	if i == nil || i.Title == nil {
		return ""
	}
	return *i.Title
}

// GetUpdatedAt returns the UpdatedAt field if it's non-nil, zero value otherwise.
func (i *Issue) GetUpdatedAt() time.Time {
	if i == nil || i.UpdatedAt == nil {
		return time.Time{}
	}
	return *i.UpdatedAt
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (i *Issue) GetURL() string {
	if i == nil || i.URL == nil {
		return ""
	}
	return *i.URL
}

// GetUser returns the User field.
func (i *Issue) GetUser() *User {
	if i == nil {
		return nil
	}
	return i.User
}

// GetAuthorAssociation returns the AuthorAssociation field if it's non-nil, zero value otherwise.
func (i *IssueComment) GetAuthorAssociation() string {
	if i == nil || i.AuthorAssociation == nil {
		return ""
	}
	return *i.AuthorAssociation
}

// GetBody returns the Body field if it's non-nil, zero value otherwise.
func (i *IssueComment) GetBody() string {
	if i == nil || i.Body == nil {
		return ""
	}
	return *i.Body
}

// GetCreatedAt returns the CreatedAt field if it's non-nil, zero value otherwise.
func (i *IssueComment) GetCreatedAt() time.Time {
	if i == nil || i.CreatedAt == nil {
		return time.Time{}
	}
	return *i.CreatedAt
}

// GetHTMLURL returns the HTMLURL field if it's non-nil, zero value otherwise.
func (i *IssueComment) GetHTMLURL() string {
	if i == nil || i.HTMLURL == nil {
		return ""
	}
	return *i.HTMLURL
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (i *IssueComment) GetID() int64 {
	if i == nil || i.ID == nil {
		return 0
	}
	return *i.ID
}

// GetIssueURL returns the IssueURL field if it's non-nil, zero value otherwise.
func (i *IssueComment) GetIssueURL() string {
	if i == nil || i.IssueURL == nil {
		return ""
	}
	return *i.IssueURL
}

// GetNodeID returns the NodeID field if it's non-nil, zero value otherwise.
func (i *IssueComment) GetNodeID() string {
	if i == nil || i.NodeID == nil {
		return ""
	}
	return *i.NodeID
}

// GetReactions returns the Reactions field.
func (i *IssueComment) GetReactions() *Reactions {
	if i == nil {
		return nil
	}
	return i.Reactions
}

// GetUpdatedAt returns the UpdatedAt field if it's non-nil, zero value otherwise.
func (i *IssueComment) GetUpdatedAt() time.Time {
	if i == nil || i.UpdatedAt == nil {
		return time.Time{}
	}
	return *i.UpdatedAt
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (i *IssueComment) GetURL() string {
	if i == nil || i.URL == nil {
		return ""
	}
	return *i.URL
}

// GetUser returns the User field.
func (i *IssueComment) GetUser() *User {
	if i == nil {
		return nil
	}
	return i.User
}

// GetAction returns the Action field if it's non-nil, zero value otherwise.
func (i *IssueCommentEvent) GetAction() string {
	if i == nil || i.Action == nil {
		return ""
	}
	return *i.Action
}

// GetChanges returns the Changes field.
func (i *IssueCommentEvent) GetChanges() *EditChange {
	if i == nil {
		return nil
	}
	return i.Changes
}

// GetComment returns the Comment field.
func (i *IssueCommentEvent) GetComment() *IssueComment {
	if i == nil {
		return nil
	}
	return i.Comment
}

// GetInstallation returns the Installation field.
func (i *IssueCommentEvent) GetInstallation() *Installation {
	if i == nil {
		return nil
	}
	return i.Installation
}

// GetIssue returns the Issue field.
func (i *IssueCommentEvent) GetIssue() *Issue {
	if i == nil {
		return nil
	}
	return i.Issue
}

// GetRepo returns the Repo field.
func (i *IssueCommentEvent) GetRepo() *Repository {
	if i == nil {
		return nil
	}
	return i.Repo
}

// GetSender returns the Sender field.
func (i *IssueCommentEvent) GetSender() *User {
	if i == nil {
		return nil
	}
	return i.Sender
}

// GetActor returns the Actor field.
func (i *IssueEvent) GetActor() *User {
	if i == nil {
		return nil
	}
	return i.Actor
}

// GetAssignee returns the Assignee field.
func (i *IssueEvent) GetAssignee() *User {
	if i == nil {
		return nil
	}
	return i.Assignee
}

// GetAssigner returns the Assigner field.
func (i *IssueEvent) GetAssigner() *User {
	if i == nil {
		return nil
	}
	return i.Assigner
}

// GetCommitID returns the CommitID field if it's non-nil, zero value otherwise.
func (i *IssueEvent) GetCommitID() string {
	if i == nil || i.CommitID == nil {
		return ""
	}
	return *i.CommitID
}

// GetCreatedAt returns the CreatedAt field if it's non-nil, zero value otherwise.
func (i *IssueEvent) GetCreatedAt() time.Time {
	if i == nil || i.CreatedAt == nil {
		return time.Time{}
	}
	return *i.CreatedAt
}

// GetDismissedReview returns the DismissedReview field.
func (i *IssueEvent) GetDismissedReview() *DismissedReview {
	if i == nil {
		return nil
	}
	return i.DismissedReview
}

// GetEvent returns the Event field if it's non-nil, zero value otherwise.
func (i *IssueEvent) GetEvent() string {
	if i == nil || i.Event == nil {
		return ""
	}
	return *i.Event
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (i *IssueEvent) GetID() int64 {
	if i == nil || i.ID == nil {
		return 0
	}
	return *i.ID
}

// GetIssue returns the Issue field.
func (i *IssueEvent) GetIssue() *Issue {
	if i == nil {
		return nil
	}
	return i.Issue
}

// GetLabel returns the Label field.
func (i *IssueEvent) GetLabel() *Label {
	if i == nil {
		return nil
	}
	return i.Label
}

// GetLockReason returns the LockReason field if it's non-nil, zero value otherwise.
func (i *IssueEvent) GetLockReason() string {
	if i == nil || i.LockReason == nil {
		return ""
	}
	return *i.LockReason
}

// GetMilestone returns the Milestone field.
func (i *IssueEvent) GetMilestone() *Milestone {
	if i == nil {
		return nil
	}
	return i.Milestone
}

// GetProjectCard returns the ProjectCard field.
func (i *IssueEvent) GetProjectCard() *ProjectCard {
	if i == nil {
		return nil
	}
	return i.ProjectCard
}

// GetRename returns the Rename field.
func (i *IssueEvent) GetRename() *Rename {
	if i == nil {
		return nil
	}
	return i.Rename
}

// GetRequestedReviewer returns the RequestedReviewer field.
func (i *IssueEvent) GetRequestedReviewer() *User {
	if i == nil {
		return nil
	}
	return i.RequestedReviewer
}

// GetReviewRequester returns the ReviewRequester field.
func (i *IssueEvent) GetReviewRequester() *User {
	if i == nil {
		return nil
	}
	return i.ReviewRequester
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (i *IssueEvent) GetURL() string {
	if i == nil || i.URL == nil {
		return ""
	}
	return *i.URL
}

// GetAssignee returns the Assignee field if it's non-nil, zero value otherwise.
func (i *IssueImport) GetAssignee() string {
	if i == nil || i.Assignee == nil {
		return ""
	}
	return *i.Assignee
}

// GetClosed returns the Closed field if it's non-nil, zero value otherwise.
func (i *IssueImport) GetClosed() bool {
	if i == nil || i.Closed == nil {
		return false
	}
	return *i.Closed
}

// GetClosedAt returns the ClosedAt field if it's non-nil, zero value otherwise.
func (i *IssueImport) GetClosedAt() time.Time {
	if i == nil || i.ClosedAt == nil {
		return time.Time{}
	}
	return *i.ClosedAt
}

// GetCreatedAt returns the CreatedAt field if it's non-nil, zero value otherwise.
func (i *IssueImport) GetCreatedAt() time.Time {
	if i == nil || i.CreatedAt == nil {
		return time.Time{}
	}
	return *i.CreatedAt
}

// GetMilestone returns the Milestone field if it's non-nil, zero value otherwise.
func (i *IssueImport) GetMilestone() int {
	if i == nil || i.Milestone == nil {
		return 0
	}
	return *i.Milestone
}

// GetUpdatedAt returns the UpdatedAt field if it's non-nil, zero value otherwise.
func (i *IssueImport) GetUpdatedAt() time.Time {
	if i == nil || i.UpdatedAt == nil {
		return time.Time{}
	}
	return *i.UpdatedAt
}

// GetCode returns the Code field if it's non-nil, zero value otherwise.
func (i *IssueImportError) GetCode() string {
	if i == nil || i.Code == nil {
		return ""
	}
	return *i.Code
}

// GetField returns the Field field if it's non-nil, zero value otherwise.
func (i *IssueImportError) GetField() string {
	if i == nil || i.Field == nil {
		return ""
	}
	return *i.Field
}

// GetLocation returns the Location field if it's non-nil, zero value otherwise.
func (i *IssueImportError) GetLocation() string {
	if i == nil || i.Location == nil {
		return ""
	}
	return *i.Location
}

// GetResource returns the Resource field if it's non-nil, zero value otherwise.
func (i *IssueImportError) GetResource() string {
	if i == nil || i.Resource == nil {
		return ""
	}
	return *i.Resource
}

// GetValue returns the Value field if it's non-nil, zero value otherwise.
func (i *IssueImportError) GetValue() string {
	if i == nil || i.Value == nil {
		return ""
	}
	return *i.Value
}

// GetCreatedAt returns the CreatedAt field if it's non-nil, zero value otherwise.
func (i *IssueImportResponse) GetCreatedAt() time.Time {
	if i == nil || i.CreatedAt == nil {
		return time.Time{}
	}
	return *i.CreatedAt
}

// GetDocumentationURL returns the DocumentationURL field if it's non-nil, zero value otherwise.
func (i *IssueImportResponse) GetDocumentationURL() string {
	if i == nil || i.DocumentationURL == nil {
		return ""
	}
	return *i.DocumentationURL
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (i *IssueImportResponse) GetID() int {
	if i == nil || i.ID == nil {
		return 0
	}
	return *i.ID
}

// GetImportIssuesURL returns the ImportIssuesURL field if it's non-nil, zero value otherwise.
func (i *IssueImportResponse) GetImportIssuesURL() string {
	if i == nil || i.ImportIssuesURL == nil {
		return ""
	}
	return *i.ImportIssuesURL
}

// GetMessage returns the Message field if it's non-nil, zero value otherwise.
func (i *IssueImportResponse) GetMessage() string {
	if i == nil || i.Message == nil {
		return ""
	}
	return *i.Message
}

// GetRepositoryURL returns the RepositoryURL field if it's non-nil, zero value otherwise.
func (i *IssueImportResponse) GetRepositoryURL() string {
	if i == nil || i.RepositoryURL == nil {
		return ""
	}
	return *i.RepositoryURL
}

// GetStatus returns the Status field if it's non-nil, zero value otherwise.
func (i *IssueImportResponse) GetStatus() string {
	if i == nil || i.Status == nil {
		return ""
	}
	return *i.Status
}

// GetUpdatedAt returns the UpdatedAt field if it's non-nil, zero value otherwise.
func (i *IssueImportResponse) GetUpdatedAt() time.Time {
	if i == nil || i.UpdatedAt == nil {
		return time.Time{}
	}
	return *i.UpdatedAt
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (i *IssueImportResponse) GetURL() string {
	if i == nil || i.URL == nil {
		return ""
	}
	return *i.URL
}

// GetDirection returns the Direction field if it's non-nil, zero value otherwise.
func (i *IssueListCommentsOptions) GetDirection() string {
	if i == nil || i.Direction == nil {
		return ""
	}
	return *i.Direction
}

// GetSince returns the Since field if it's non-nil, zero value otherwise.
func (i *IssueListCommentsOptions) GetSince() time.Time {
	if i == nil || i.Since == nil {
		return time.Time{}
	}
	return *i.Since
}

// GetSort returns the Sort field if it's non-nil, zero value otherwise.
func (i *IssueListCommentsOptions) GetSort() string {
	if i == nil || i.Sort == nil {
		return ""
	}
	return *i.Sort
}

// GetAssignee returns the Assignee field if it's non-nil, zero value otherwise.
func (i *IssueRequest) GetAssignee() string {
	if i == nil || i.Assignee == nil {
		return ""
	}
	return *i.Assignee
}

// GetAssignees returns the Assignees field if it's non-nil, zero value otherwise.
func (i *IssueRequest) GetAssignees() []string {
	if i == nil || i.Assignees == nil {
		return nil
	}
	return *i.Assignees
}

// GetBody returns the Body field if it's non-nil, zero value otherwise.
func (i *IssueRequest) GetBody() string {
	if i == nil || i.Body == nil {
		return ""
	}
	return *i.Body
}

// GetLabels returns the Labels field if it's non-nil, zero value otherwise.
func (i *IssueRequest) GetLabels() []string {
	if i == nil || i.Labels == nil {
		return nil
	}
	return *i.Labels
}

// GetMilestone returns the Milestone field if it's non-nil, zero value otherwise.
func (i *IssueRequest) GetMilestone() int {
	if i == nil || i.Milestone == nil {
		return 0
	}
	return *i.Milestone
}

// GetState returns the State field if it's non-nil, zero value otherwise.
func (i *IssueRequest) GetState() string {
	if i == nil || i.State == nil {
		return ""
	}
	return *i.State
}

// GetTitle returns the Title field if it's non-nil, zero value otherwise.
func (i *IssueRequest) GetTitle() string {
	if i == nil || i.Title == nil {
		return ""
	}
	return *i.Title
}

// GetAction returns the Action field if it's non-nil, zero value otherwise.
func (i *IssuesEvent) GetAction() string {
	if i == nil || i.Action == nil {
		return ""
	}
	return *i.Action
}

// GetAssignee returns the Assignee field.
func (i *IssuesEvent) GetAssignee() *User {
	if i == nil {
		return nil
	}
	return i.Assignee
}

// GetChanges returns the Changes field.
func (i *IssuesEvent) GetChanges() *EditChange {
	if i == nil {
		return nil
	}
	return i.Changes
}

// GetInstallation returns the Installation field.
func (i *IssuesEvent) GetInstallation() *Installation {
	if i == nil {
		return nil
	}
	return i.Installation
}

// GetIssue returns the Issue field.
func (i *IssuesEvent) GetIssue() *Issue {
	if i == nil {
		return nil
	}
	return i.Issue
}

// GetLabel returns the Label field.
func (i *IssuesEvent) GetLabel() *Label {
	if i == nil {
		return nil
	}
	return i.Label
}

// GetRepo returns the Repo field.
func (i *IssuesEvent) GetRepo() *Repository {
	if i == nil {
		return nil
	}
	return i.Repo
}

// GetSender returns the Sender field.
func (i *IssuesEvent) GetSender() *User {
	if i == nil {
		return nil
	}
	return i.Sender
}

// GetIncompleteResults returns the IncompleteResults field if it's non-nil, zero value otherwise.
func (i *IssuesSearchResult) GetIncompleteResults() bool {
	if i == nil || i.IncompleteResults == nil {
		return false
	}
	return *i.IncompleteResults
}

// GetTotal returns the Total field if it's non-nil, zero value otherwise.
func (i *IssuesSearchResult) GetTotal() int {
	if i == nil || i.Total == nil {
		return 0
	}
	return *i.Total
}

// GetClosedIssues returns the ClosedIssues field if it's non-nil, zero value otherwise.
func (i *IssueStats) GetClosedIssues() int {
	if i == nil || i.ClosedIssues == nil {
		return 0
	}
	return *i.ClosedIssues
}

// GetOpenIssues returns the OpenIssues field if it's non-nil, zero value otherwise.
func (i *IssueStats) GetOpenIssues() int {
	if i == nil || i.OpenIssues == nil {
		return 0
	}
	return *i.OpenIssues
}

// GetTotalIssues returns the TotalIssues field if it's non-nil, zero value otherwise.
func (i *IssueStats) GetTotalIssues() int {
	if i == nil || i.TotalIssues == nil {
		return 0
	}
	return *i.TotalIssues
}

// GetTotalCount returns the TotalCount field if it's non-nil, zero value otherwise.
func (j *Jobs) GetTotalCount() int {
	if j == nil || j.TotalCount == nil {
		return 0
	}
	return *j.TotalCount
}

// GetCreatedAt returns the CreatedAt field if it's non-nil, zero value otherwise.
func (k *Key) GetCreatedAt() Timestamp {
	if k == nil || k.CreatedAt == nil {
		return Timestamp{}
	}
	return *k.CreatedAt
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (k *Key) GetID() int64 {
	if k == nil || k.ID == nil {
		return 0
	}
	return *k.ID
}

// GetKey returns the Key field if it's non-nil, zero value otherwise.
func (k *Key) GetKey() string {
	if k == nil || k.Key == nil {
		return ""
	}
	return *k.Key
}

// GetReadOnly returns the ReadOnly field if it's non-nil, zero value otherwise.
func (k *Key) GetReadOnly() bool {
	if k == nil || k.ReadOnly == nil {
		return false
	}
	return *k.ReadOnly
}

// GetTitle returns the Title field if it's non-nil, zero value otherwise.
func (k *Key) GetTitle() string {
	if k == nil || k.Title == nil {
		return ""
	}
	return *k.Title
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (k *Key) GetURL() string {
	if k == nil || k.URL == nil {
		return ""
	}
	return *k.URL
}

// GetVerified returns the Verified field if it's non-nil, zero value otherwise.
func (k *Key) GetVerified() bool {
	if k == nil || k.Verified == nil {
		return false
	}
	return *k.Verified
}

// GetColor returns the Color field if it's non-nil, zero value otherwise.
func (l *Label) GetColor() string {
	if l == nil || l.Color == nil {
		return ""
	}
	return *l.Color
}

// GetDefault returns the Default field if it's non-nil, zero value otherwise.
func (l *Label) GetDefault() bool {
	if l == nil || l.Default == nil {
		return false
	}
	return *l.Default
}

// GetDescription returns the Description field if it's non-nil, zero value otherwise.
func (l *Label) GetDescription() string {
	if l == nil || l.Description == nil {
		return ""
	}
	return *l.Description
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (l *Label) GetID() int64 {
	if l == nil || l.ID == nil {
		return 0
	}
	return *l.ID
}

// GetName returns the Name field if it's non-nil, zero value otherwise.
func (l *Label) GetName() string {
	if l == nil || l.Name == nil {
		return ""
	}
	return *l.Name
}

// GetNodeID returns the NodeID field if it's non-nil, zero value otherwise.
func (l *Label) GetNodeID() string {
	if l == nil || l.NodeID == nil {
		return ""
	}
	return *l.NodeID
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (l *Label) GetURL() string {
	if l == nil || l.URL == nil {
		return ""
	}
	return *l.URL
}

// GetAction returns the Action field if it's non-nil, zero value otherwise.
func (l *LabelEvent) GetAction() string {
	if l == nil || l.Action == nil {
		return ""
	}
	return *l.Action
}

// GetChanges returns the Changes field.
func (l *LabelEvent) GetChanges() *EditChange {
	if l == nil {
		return nil
	}
	return l.Changes
}

// GetInstallation returns the Installation field.
func (l *LabelEvent) GetInstallation() *Installation {
	if l == nil {
		return nil
	}
	return l.Installation
}

// GetLabel returns the Label field.
func (l *LabelEvent) GetLabel() *Label {
	if l == nil {
		return nil
	}
	return l.Label
}

// GetOrg returns the Org field.
func (l *LabelEvent) GetOrg() *Organization {
	if l == nil {
		return nil
	}
	return l.Org
}

// GetRepo returns the Repo field.
func (l *LabelEvent) GetRepo() *Repository {
	if l == nil {
		return nil
	}
	return l.Repo
}

// GetColor returns the Color field if it's non-nil, zero value otherwise.
func (l *LabelResult) GetColor() string {
	if l == nil || l.Color == nil {
		return ""
	}
	return *l.Color
}

// GetDefault returns the Default field if it's non-nil, zero value otherwise.
func (l *LabelResult) GetDefault() bool {
	if l == nil || l.Default == nil {
		return false
	}
	return *l.Default
}

// GetDescription returns the Description field if it's non-nil, zero value otherwise.
func (l *LabelResult) GetDescription() string {
	if l == nil || l.Description == nil {
		return ""
	}
	return *l.Description
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (l *LabelResult) GetID() int64 {
	if l == nil || l.ID == nil {
		return 0
	}
	return *l.ID
}

// GetName returns the Name field if it's non-nil, zero value otherwise.
func (l *LabelResult) GetName() string {
	if l == nil || l.Name == nil {
		return ""
	}
	return *l.Name
}

// GetScore returns the Score field.
func (l *LabelResult) GetScore() *float64 {
	if l == nil {
		return nil
	}
	return l.Score
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (l *LabelResult) GetURL() string {
	if l == nil || l.URL == nil {
		return ""
	}
	return *l.URL
}

// GetIncompleteResults returns the IncompleteResults field if it's non-nil, zero value otherwise.
func (l *LabelsSearchResult) GetIncompleteResults() bool {
	if l == nil || l.IncompleteResults == nil {
		return false
	}
	return *l.IncompleteResults
}

// GetTotal returns the Total field if it's non-nil, zero value otherwise.
func (l *LabelsSearchResult) GetTotal() int {
	if l == nil || l.Total == nil {
		return 0
	}
	return *l.Total
}

// GetOID returns the OID field if it's non-nil, zero value otherwise.
func (l *LargeFile) GetOID() string {
	if l == nil || l.OID == nil {
		return ""
	}
	return *l.OID
}

// GetPath returns the Path field if it's non-nil, zero value otherwise.
func (l *LargeFile) GetPath() string {
	if l == nil || l.Path == nil {
		return ""
	}
	return *l.Path
}

// GetRefName returns the RefName field if it's non-nil, zero value otherwise.
func (l *LargeFile) GetRefName() string {
	if l == nil || l.RefName == nil {
		return ""
	}
	return *l.RefName
}

// GetSize returns the Size field if it's non-nil, zero value otherwise.
func (l *LargeFile) GetSize() int {
	if l == nil || l.Size == nil {
		return 0
	}
	return *l.Size
}

// GetBody returns the Body field if it's non-nil, zero value otherwise.
func (l *License) GetBody() string {
	if l == nil || l.Body == nil {
		return ""
	}
	return *l.Body
}

// GetConditions returns the Conditions field if it's non-nil, zero value otherwise.
func (l *License) GetConditions() []string {
	if l == nil || l.Conditions == nil {
		return nil
	}
	return *l.Conditions
}

// GetDescription returns the Description field if it's non-nil, zero value otherwise.
func (l *License) GetDescription() string {
	if l == nil || l.Description == nil {
		return ""
	}
	return *l.Description
}

// GetFeatured returns the Featured field if it's non-nil, zero value otherwise.
func (l *License) GetFeatured() bool {
	if l == nil || l.Featured == nil {
		return false
	}
	return *l.Featured
}

// GetHTMLURL returns the HTMLURL field if it's non-nil, zero value otherwise.
func (l *License) GetHTMLURL() string {
	if l == nil || l.HTMLURL == nil {
		return ""
	}
	return *l.HTMLURL
}

// GetImplementation returns the Implementation field if it's non-nil, zero value otherwise.
func (l *License) GetImplementation() string {
	if l == nil || l.Implementation == nil {
		return ""
	}
	return *l.Implementation
}

// GetKey returns the Key field if it's non-nil, zero value otherwise.
func (l *License) GetKey() string {
	if l == nil || l.Key == nil {
		return ""
	}
	return *l.Key
}

// GetLimitations returns the Limitations field if it's non-nil, zero value otherwise.
func (l *License) GetLimitations() []string {
	if l == nil || l.Limitations == nil {
		return nil
	}
	return *l.Limitations
}

// GetName returns the Name field if it's non-nil, zero value otherwise.
func (l *License) GetName() string {
	if l == nil || l.Name == nil {
		return ""
	}
	return *l.Name
}

// GetPermissions returns the Permissions field if it's non-nil, zero value otherwise.
func (l *License) GetPermissions() []string {
	if l == nil || l.Permissions == nil {
		return nil
	}
	return *l.Permissions
}

// GetSPDXID returns the SPDXID field if it's non-nil, zero value otherwise.
func (l *License) GetSPDXID() string {
	if l == nil || l.SPDXID == nil {
		return ""
	}
	return *l.SPDXID
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (l *License) GetURL() string {
	if l == nil || l.URL == nil {
		return ""
	}
	return *l.URL
}

// GetCheckName returns the CheckName field if it's non-nil, zero value otherwise.
func (l *ListCheckRunsOptions) GetCheckName() string {
	if l == nil || l.CheckName == nil {
		return ""
	}
	return *l.CheckName
}

// GetFilter returns the Filter field if it's non-nil, zero value otherwise.
func (l *ListCheckRunsOptions) GetFilter() string {
	if l == nil || l.Filter == nil {
		return ""
	}
	return *l.Filter
}

// GetStatus returns the Status field if it's non-nil, zero value otherwise.
func (l *ListCheckRunsOptions) GetStatus() string {
	if l == nil || l.Status == nil {
		return ""
	}
	return *l.Status
}

// GetTotal returns the Total field if it's non-nil, zero value otherwise.
func (l *ListCheckRunsResults) GetTotal() int {
	if l == nil || l.Total == nil {
		return 0
	}
	return *l.Total
}

// GetAppID returns the AppID field if it's non-nil, zero value otherwise.
func (l *ListCheckSuiteOptions) GetAppID() int {
	if l == nil || l.AppID == nil {
		return 0
	}
	return *l.AppID
}

// GetCheckName returns the CheckName field if it's non-nil, zero value otherwise.
func (l *ListCheckSuiteOptions) GetCheckName() string {
	if l == nil || l.CheckName == nil {
		return ""
	}
	return *l.CheckName
}

// GetTotal returns the Total field if it's non-nil, zero value otherwise.
func (l *ListCheckSuiteResults) GetTotal() int {
	if l == nil || l.Total == nil {
		return 0
	}
	return *l.Total
}

// GetAffiliation returns the Affiliation field if it's non-nil, zero value otherwise.
func (l *ListCollaboratorOptions) GetAffiliation() string {
	if l == nil || l.Affiliation == nil {
		return ""
	}
	return *l.Affiliation
}

// GetTotalCount returns the TotalCount field if it's non-nil, zero value otherwise.
func (l *ListRepositories) GetTotalCount() int {
	if l == nil || l.TotalCount == nil {
		return 0
	}
	return *l.TotalCount
}

// GetEffectiveDate returns the EffectiveDate field if it's non-nil, zero value otherwise.
func (m *MarketplacePendingChange) GetEffectiveDate() Timestamp {
	if m == nil || m.EffectiveDate == nil {
		return Timestamp{}
	}
	return *m.EffectiveDate
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (m *MarketplacePendingChange) GetID() int64 {
	if m == nil || m.ID == nil {
		return 0
	}
	return *m.ID
}

// GetPlan returns the Plan field.
func (m *MarketplacePendingChange) GetPlan() *MarketplacePlan {
	if m == nil {
		return nil
	}
	return m.Plan
}

// GetUnitCount returns the UnitCount field if it's non-nil, zero value otherwise.
func (m *MarketplacePendingChange) GetUnitCount() int {
	if m == nil || m.UnitCount == nil {
		return 0
	}
	return *m.UnitCount
}

// GetAccountsURL returns the AccountsURL field if it's non-nil, zero value otherwise.
func (m *MarketplacePlan) GetAccountsURL() string {
	if m == nil || m.AccountsURL == nil {
		return ""
	}
	return *m.AccountsURL
}

// GetBullets returns the Bullets field if it's non-nil, zero value otherwise.
func (m *MarketplacePlan) GetBullets() []string {
	if m == nil || m.Bullets == nil {
		return nil
	}
	return *m.Bullets
}

// GetDescription returns the Description field if it's non-nil, zero value otherwise.
func (m *MarketplacePlan) GetDescription() string {
	if m == nil || m.Description == nil {
		return ""
	}
	return *m.Description
}

// GetHasFreeTrial returns the HasFreeTrial field if it's non-nil, zero value otherwise.
func (m *MarketplacePlan) GetHasFreeTrial() bool {
	if m == nil || m.HasFreeTrial == nil {
		return false
	}
	return *m.HasFreeTrial
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (m *MarketplacePlan) GetID() int64 {
	if m == nil || m.ID == nil {
		return 0
	}
	return *m.ID
}

// GetMonthlyPriceInCents returns the MonthlyPriceInCents field if it's non-nil, zero value otherwise.
func (m *MarketplacePlan) GetMonthlyPriceInCents() int {
	if m == nil || m.MonthlyPriceInCents == nil {
		return 0
	}
	return *m.MonthlyPriceInCents
}

// GetName returns the Name field if it's non-nil, zero value otherwise.
func (m *MarketplacePlan) GetName() string {
	if m == nil || m.Name == nil {
		return ""
	}
	return *m.Name
}

// GetNumber returns the Number field if it's non-nil, zero value otherwise.
func (m *MarketplacePlan) GetNumber() int {
	if m == nil || m.Number == nil {
		return 0
	}
	return *m.Number
}

// GetPriceModel returns the PriceModel field if it's non-nil, zero value otherwise.
func (m *MarketplacePlan) GetPriceModel() string {
	if m == nil || m.PriceModel == nil {
		return ""
	}
	return *m.PriceModel
}

// GetState returns the State field if it's non-nil, zero value otherwise.
func (m *MarketplacePlan) GetState() string {
	if m == nil || m.State == nil {
		return ""
	}
	return *m.State
}

// GetUnitName returns the UnitName field if it's non-nil, zero value otherwise.
func (m *MarketplacePlan) GetUnitName() string {
	if m == nil || m.UnitName == nil {
		return ""
	}
	return *m.UnitName
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (m *MarketplacePlan) GetURL() string {
	if m == nil || m.URL == nil {
		return ""
	}
	return *m.URL
}

// GetYearlyPriceInCents returns the YearlyPriceInCents field if it's non-nil, zero value otherwise.
func (m *MarketplacePlan) GetYearlyPriceInCents() int {
	if m == nil || m.YearlyPriceInCents == nil {
		return 0
	}
	return *m.YearlyPriceInCents
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (m *MarketplacePlanAccount) GetID() int64 {
	if m == nil || m.ID == nil {
		return 0
	}
	return *m.ID
}

// GetLogin returns the Login field if it's non-nil, zero value otherwise.
func (m *MarketplacePlanAccount) GetLogin() string {
	if m == nil || m.Login == nil {
		return ""
	}
	return *m.Login
}

// GetMarketplacePendingChange returns the MarketplacePendingChange field.
func (m *MarketplacePlanAccount) GetMarketplacePendingChange() *MarketplacePendingChange {
	if m == nil {
		return nil
	}
	return m.MarketplacePendingChange
}

// GetMarketplacePurchase returns the MarketplacePurchase field.
func (m *MarketplacePlanAccount) GetMarketplacePurchase() *MarketplacePurchase {
	if m == nil {
		return nil
	}
	return m.MarketplacePurchase
}

// GetOrganizationBillingEmail returns the OrganizationBillingEmail field if it's non-nil, zero value otherwise.
func (m *MarketplacePlanAccount) GetOrganizationBillingEmail() string {
	if m == nil || m.OrganizationBillingEmail == nil {
		return ""
	}
	return *m.OrganizationBillingEmail
}

// GetType returns the Type field if it's non-nil, zero value otherwise.
func (m *MarketplacePlanAccount) GetType() string {
	if m == nil || m.Type == nil {
		return ""
	}
	return *m.Type
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (m *MarketplacePlanAccount) GetURL() string {
	if m == nil || m.URL == nil {
		return ""
	}
	return *m.URL
}

// GetBillingCycle returns the BillingCycle field if it's non-nil, zero value otherwise.
func (m *MarketplacePurchase) GetBillingCycle() string {
	if m == nil || m.BillingCycle == nil {
		return ""
	}
	return *m.BillingCycle
}

// GetFreeTrialEndsOn returns the FreeTrialEndsOn field if it's non-nil, zero value otherwise.
func (m *MarketplacePurchase) GetFreeTrialEndsOn() Timestamp {
	if m == nil || m.FreeTrialEndsOn == nil {
		return Timestamp{}
	}
	return *m.FreeTrialEndsOn
}

// GetNextBillingDate returns the NextBillingDate field if it's non-nil, zero value otherwise.
func (m *MarketplacePurchase) GetNextBillingDate() Timestamp {
	if m == nil || m.NextBillingDate == nil {
		return Timestamp{}
	}
	return *m.NextBillingDate
}

// GetOnFreeTrial returns the OnFreeTrial field if it's non-nil, zero value otherwise.
func (m *MarketplacePurchase) GetOnFreeTrial() bool {
	if m == nil || m.OnFreeTrial == nil {
		return false
	}
	return *m.OnFreeTrial
}

// GetPlan returns the Plan field.
func (m *MarketplacePurchase) GetPlan() *MarketplacePlan {
	if m == nil {
		return nil
	}
	return m.Plan
}

// GetUnitCount returns the UnitCount field if it's non-nil, zero value otherwise.
func (m *MarketplacePurchase) GetUnitCount() int {
	if m == nil || m.UnitCount == nil {
		return 0
	}
	return *m.UnitCount
}

// GetUpdatedAt returns the UpdatedAt field if it's non-nil, zero value otherwise.
func (m *MarketplacePurchase) GetUpdatedAt() Timestamp {
	if m == nil || m.UpdatedAt == nil {
		return Timestamp{}
	}
	return *m.UpdatedAt
}

// GetAction returns the Action field if it's non-nil, zero value otherwise.
func (m *MarketplacePurchaseEvent) GetAction() string {
	if m == nil || m.Action == nil {
		return ""
	}
	return *m.Action
}

// GetEffectiveDate returns the EffectiveDate field if it's non-nil, zero value otherwise.
func (m *MarketplacePurchaseEvent) GetEffectiveDate() Timestamp {
	if m == nil || m.EffectiveDate == nil {
		return Timestamp{}
	}
	return *m.EffectiveDate
}

// GetInstallation returns the Installation field.
func (m *MarketplacePurchaseEvent) GetInstallation() *Installation {
	if m == nil {
		return nil
	}
	return m.Installation
}

// GetMarketplacePurchase returns the MarketplacePurchase field.
func (m *MarketplacePurchaseEvent) GetMarketplacePurchase() *MarketplacePurchase {
	if m == nil {
		return nil
	}
	return m.MarketplacePurchase
}

// GetPreviousMarketplacePurchase returns the PreviousMarketplacePurchase field.
func (m *MarketplacePurchaseEvent) GetPreviousMarketplacePurchase() *MarketplacePurchase {
	if m == nil {
		return nil
	}
	return m.PreviousMarketplacePurchase
}

// GetSender returns the Sender field.
func (m *MarketplacePurchaseEvent) GetSender() *User {
	if m == nil {
		return nil
	}
	return m.Sender
}

// GetText returns the Text field if it's non-nil, zero value otherwise.
func (m *Match) GetText() string {
	if m == nil || m.Text == nil {
		return ""
	}
	return *m.Text
}

// GetAction returns the Action field if it's non-nil, zero value otherwise.
func (m *MemberEvent) GetAction() string {
	if m == nil || m.Action == nil {
		return ""
	}
	return *m.Action
}

// GetInstallation returns the Installation field.
func (m *MemberEvent) GetInstallation() *Installation {
	if m == nil {
		return nil
	}
	return m.Installation
}

// GetMember returns the Member field.
func (m *MemberEvent) GetMember() *User {
	if m == nil {
		return nil
	}
	return m.Member
}

// GetRepo returns the Repo field.
func (m *MemberEvent) GetRepo() *Repository {
	if m == nil {
		return nil
	}
	return m.Repo
}

// GetSender returns the Sender field.
func (m *MemberEvent) GetSender() *User {
	if m == nil {
		return nil
	}
	return m.Sender
}

// GetOrganization returns the Organization field.
func (m *Membership) GetOrganization() *Organization {
	if m == nil {
		return nil
	}
	return m.Organization
}

// GetOrganizationURL returns the OrganizationURL field if it's non-nil, zero value otherwise.
func (m *Membership) GetOrganizationURL() string {
	if m == nil || m.OrganizationURL == nil {
		return ""
	}
	return *m.OrganizationURL
}

// GetRole returns the Role field if it's non-nil, zero value otherwise.
func (m *Membership) GetRole() string {
	if m == nil || m.Role == nil {
		return ""
	}
	return *m.Role
}

// GetState returns the State field if it's non-nil, zero value otherwise.
func (m *Membership) GetState() string {
	if m == nil || m.State == nil {
		return ""
	}
	return *m.State
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (m *Membership) GetURL() string {
	if m == nil || m.URL == nil {
		return ""
	}
	return *m.URL
}

// GetUser returns the User field.
func (m *Membership) GetUser() *User {
	if m == nil {
		return nil
	}
	return m.User
}

// GetAction returns the Action field if it's non-nil, zero value otherwise.
func (m *MembershipEvent) GetAction() string {
	if m == nil || m.Action == nil {
		return ""
	}
	return *m.Action
}

// GetInstallation returns the Installation field.
func (m *MembershipEvent) GetInstallation() *Installation {
	if m == nil {
		return nil
	}
	return m.Installation
}

// GetMember returns the Member field.
func (m *MembershipEvent) GetMember() *User {
	if m == nil {
		return nil
	}
	return m.Member
}

// GetOrg returns the Org field.
func (m *MembershipEvent) GetOrg() *Organization {
	if m == nil {
		return nil
	}
	return m.Org
}

// GetScope returns the Scope field if it's non-nil, zero value otherwise.
func (m *MembershipEvent) GetScope() string {
	if m == nil || m.Scope == nil {
		return ""
	}
	return *m.Scope
}

// GetSender returns the Sender field.
func (m *MembershipEvent) GetSender() *User {
	if m == nil {
		return nil
	}
	return m.Sender
}

// GetTeam returns the Team field.
func (m *MembershipEvent) GetTeam() *Team {
	if m == nil {
		return nil
	}
	return m.Team
}

// GetAction returns the Action field if it's non-nil, zero value otherwise.
func (m *MetaEvent) GetAction() string {
	if m == nil || m.Action == nil {
		return ""
	}
	return *m.Action
}

// GetHook returns the Hook field.
func (m *MetaEvent) GetHook() *Hook {
	if m == nil {
		return nil
	}
	return m.Hook
}

// GetHookID returns the HookID field if it's non-nil, zero value otherwise.
func (m *MetaEvent) GetHookID() int64 {
	if m == nil || m.HookID == nil {
		return 0
	}
	return *m.HookID
}

// GetHTMLURL returns the HTMLURL field if it's non-nil, zero value otherwise.
func (m *Metric) GetHTMLURL() string {
	if m == nil || m.HTMLURL == nil {
		return ""
	}
	return *m.HTMLURL
}

// GetKey returns the Key field if it's non-nil, zero value otherwise.
func (m *Metric) GetKey() string {
	if m == nil || m.Key == nil {
		return ""
	}
	return *m.Key
}

// GetName returns the Name field if it's non-nil, zero value otherwise.
func (m *Metric) GetName() string {
	if m == nil || m.Name == nil {
		return ""
	}
	return *m.Name
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (m *Metric) GetURL() string {
	if m == nil || m.URL == nil {
		return ""
	}
	return *m.URL
}

// GetCreatedAt returns the CreatedAt field if it's non-nil, zero value otherwise.
func (m *Migration) GetCreatedAt() string {
	if m == nil || m.CreatedAt == nil {
		return ""
	}
	return *m.CreatedAt
}

// GetExcludeAttachments returns the ExcludeAttachments field if it's non-nil, zero value otherwise.
func (m *Migration) GetExcludeAttachments() bool {
	if m == nil || m.ExcludeAttachments == nil {
		return false
	}
	return *m.ExcludeAttachments
}

// GetGUID returns the GUID field if it's non-nil, zero value otherwise.
func (m *Migration) GetGUID() string {
	if m == nil || m.GUID == nil {
		return ""
	}
	return *m.GUID
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (m *Migration) GetID() int64 {
	if m == nil || m.ID == nil {
		return 0
	}
	return *m.ID
}

// GetLockRepositories returns the LockRepositories field if it's non-nil, zero value otherwise.
func (m *Migration) GetLockRepositories() bool {
	if m == nil || m.LockRepositories == nil {
		return false
	}
	return *m.LockRepositories
}

// GetState returns the State field if it's non-nil, zero value otherwise.
func (m *Migration) GetState() string {
	if m == nil || m.State == nil {
		return ""
	}
	return *m.State
}

// GetUpdatedAt returns the UpdatedAt field if it's non-nil, zero value otherwise.
func (m *Migration) GetUpdatedAt() string {
	if m == nil || m.UpdatedAt == nil {
		return ""
	}
	return *m.UpdatedAt
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (m *Migration) GetURL() string {
	if m == nil || m.URL == nil {
		return ""
	}
	return *m.URL
}

// GetClosedAt returns the ClosedAt field if it's non-nil, zero value otherwise.
func (m *Milestone) GetClosedAt() time.Time {
	if m == nil || m.ClosedAt == nil {
		return time.Time{}
	}
	return *m.ClosedAt
}

// GetClosedIssues returns the ClosedIssues field if it's non-nil, zero value otherwise.
func (m *Milestone) GetClosedIssues() int {
	if m == nil || m.ClosedIssues == nil {
		return 0
	}
	return *m.ClosedIssues
}

// GetCreatedAt returns the CreatedAt field if it's non-nil, zero value otherwise.
func (m *Milestone) GetCreatedAt() time.Time {
	if m == nil || m.CreatedAt == nil {
		return time.Time{}
	}
	return *m.CreatedAt
}

// GetCreator returns the Creator field.
func (m *Milestone) GetCreator() *User {
	if m == nil {
		return nil
	}
	return m.Creator
}

// GetDescription returns the Description field if it's non-nil, zero value otherwise.
func (m *Milestone) GetDescription() string {
	if m == nil || m.Description == nil {
		return ""
	}
	return *m.Description
}

// GetDueOn returns the DueOn field if it's non-nil, zero value otherwise.
func (m *Milestone) GetDueOn() time.Time {
	if m == nil || m.DueOn == nil {
		return time.Time{}
	}
	return *m.DueOn
}

// GetHTMLURL returns the HTMLURL field if it's non-nil, zero value otherwise.
func (m *Milestone) GetHTMLURL() string {
	if m == nil || m.HTMLURL == nil {
		return ""
	}
	return *m.HTMLURL
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (m *Milestone) GetID() int64 {
	if m == nil || m.ID == nil {
		return 0
	}
	return *m.ID
}

// GetLabelsURL returns the LabelsURL field if it's non-nil, zero value otherwise.
func (m *Milestone) GetLabelsURL() string {
	if m == nil || m.LabelsURL == nil {
		return ""
	}
	return *m.LabelsURL
}

// GetNodeID returns the NodeID field if it's non-nil, zero value otherwise.
func (m *Milestone) GetNodeID() string {
	if m == nil || m.NodeID == nil {
		return ""
	}
	return *m.NodeID
}

// GetNumber returns the Number field if it's non-nil, zero value otherwise.
func (m *Milestone) GetNumber() int {
	if m == nil || m.Number == nil {
		return 0
	}
	return *m.Number
}

// GetOpenIssues returns the OpenIssues field if it's non-nil, zero value otherwise.
func (m *Milestone) GetOpenIssues() int {
	if m == nil || m.OpenIssues == nil {
		return 0
	}
	return *m.OpenIssues
}

// GetState returns the State field if it's non-nil, zero value otherwise.
func (m *Milestone) GetState() string {
	if m == nil || m.State == nil {
		return ""
	}
	return *m.State
}

// GetTitle returns the Title field if it's non-nil, zero value otherwise.
func (m *Milestone) GetTitle() string {
	if m == nil || m.Title == nil {
		return ""
	}
	return *m.Title
}

// GetUpdatedAt returns the UpdatedAt field if it's non-nil, zero value otherwise.
func (m *Milestone) GetUpdatedAt() time.Time {
	if m == nil || m.UpdatedAt == nil {
		return time.Time{}
	}
	return *m.UpdatedAt
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (m *Milestone) GetURL() string {
	if m == nil || m.URL == nil {
		return ""
	}
	return *m.URL
}

// GetAction returns the Action field if it's non-nil, zero value otherwise.
func (m *MilestoneEvent) GetAction() string {
	if m == nil || m.Action == nil {
		return ""
	}
	return *m.Action
}

// GetChanges returns the Changes field.
func (m *MilestoneEvent) GetChanges() *EditChange {
	if m == nil {
		return nil
	}
	return m.Changes
}

// GetInstallation returns the Installation field.
func (m *MilestoneEvent) GetInstallation() *Installation {
	if m == nil {
		return nil
	}
	return m.Installation
}

// GetMilestone returns the Milestone field.
func (m *MilestoneEvent) GetMilestone() *Milestone {
	if m == nil {
		return nil
	}
	return m.Milestone
}

// GetOrg returns the Org field.
func (m *MilestoneEvent) GetOrg() *Organization {
	if m == nil {
		return nil
	}
	return m.Org
}

// GetRepo returns the Repo field.
func (m *MilestoneEvent) GetRepo() *Repository {
	if m == nil {
		return nil
	}
	return m.Repo
}

// GetSender returns the Sender field.
func (m *MilestoneEvent) GetSender() *User {
	if m == nil {
		return nil
	}
	return m.Sender
}

// GetClosedMilestones returns the ClosedMilestones field if it's non-nil, zero value otherwise.
func (m *MilestoneStats) GetClosedMilestones() int {
	if m == nil || m.ClosedMilestones == nil {
		return 0
	}
	return *m.ClosedMilestones
}

// GetOpenMilestones returns the OpenMilestones field if it's non-nil, zero value otherwise.
func (m *MilestoneStats) GetOpenMilestones() int {
	if m == nil || m.OpenMilestones == nil {
		return 0
	}
	return *m.OpenMilestones
}

// GetTotalMilestones returns the TotalMilestones field if it's non-nil, zero value otherwise.
func (m *MilestoneStats) GetTotalMilestones() int {
	if m == nil || m.TotalMilestones == nil {
		return 0
	}
	return *m.TotalMilestones
}

// GetBase returns the Base field if it's non-nil, zero value otherwise.
func (n *NewPullRequest) GetBase() string {
	if n == nil || n.Base == nil {
		return ""
	}
	return *n.Base
}

// GetBody returns the Body field if it's non-nil, zero value otherwise.
func (n *NewPullRequest) GetBody() string {
	if n == nil || n.Body == nil {
		return ""
	}
	return *n.Body
}

// GetDraft returns the Draft field if it's non-nil, zero value otherwise.
func (n *NewPullRequest) GetDraft() bool {
	if n == nil || n.Draft == nil {
		return false
	}
	return *n.Draft
}

// GetHead returns the Head field if it's non-nil, zero value otherwise.
func (n *NewPullRequest) GetHead() string {
	if n == nil || n.Head == nil {
		return ""
	}
	return *n.Head
}

// GetIssue returns the Issue field if it's non-nil, zero value otherwise.
func (n *NewPullRequest) GetIssue() int {
	if n == nil || n.Issue == nil {
		return 0
	}
	return *n.Issue
}

// GetMaintainerCanModify returns the MaintainerCanModify field if it's non-nil, zero value otherwise.
func (n *NewPullRequest) GetMaintainerCanModify() bool {
	if n == nil || n.MaintainerCanModify == nil {
		return false
	}
	return *n.MaintainerCanModify
}

// GetTitle returns the Title field if it's non-nil, zero value otherwise.
func (n *NewPullRequest) GetTitle() string {
	if n == nil || n.Title == nil {
		return ""
	}
	return *n.Title
}

// GetDescription returns the Description field if it's non-nil, zero value otherwise.
func (n *NewTeam) GetDescription() string {
	if n == nil || n.Description == nil {
		return ""
	}
	return *n.Description
}

// GetLDAPDN returns the LDAPDN field if it's non-nil, zero value otherwise.
func (n *NewTeam) GetLDAPDN() string {
	if n == nil || n.LDAPDN == nil {
		return ""
	}
	return *n.LDAPDN
}

// GetParentTeamID returns the ParentTeamID field if it's non-nil, zero value otherwise.
func (n *NewTeam) GetParentTeamID() int64 {
	if n == nil || n.ParentTeamID == nil {
		return 0
	}
	return *n.ParentTeamID
}

// GetPermission returns the Permission field if it's non-nil, zero value otherwise.
func (n *NewTeam) GetPermission() string {
	if n == nil || n.Permission == nil {
		return ""
	}
	return *n.Permission
}

// GetPrivacy returns the Privacy field if it's non-nil, zero value otherwise.
func (n *NewTeam) GetPrivacy() string {
	if n == nil || n.Privacy == nil {
		return ""
	}
	return *n.Privacy
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (n *Notification) GetID() string {
	if n == nil || n.ID == nil {
		return ""
	}
	return *n.ID
}

// GetLastReadAt returns the LastReadAt field if it's non-nil, zero value otherwise.
func (n *Notification) GetLastReadAt() time.Time {
	if n == nil || n.LastReadAt == nil {
		return time.Time{}
	}
	return *n.LastReadAt
}

// GetReason returns the Reason field if it's non-nil, zero value otherwise.
func (n *Notification) GetReason() string {
	if n == nil || n.Reason == nil {
		return ""
	}
	return *n.Reason
}

// GetRepository returns the Repository field.
func (n *Notification) GetRepository() *Repository {
	if n == nil {
		return nil
	}
	return n.Repository
}

// GetSubject returns the Subject field.
func (n *Notification) GetSubject() *NotificationSubject {
	if n == nil {
		return nil
	}
	return n.Subject
}

// GetUnread returns the Unread field if it's non-nil, zero value otherwise.
func (n *Notification) GetUnread() bool {
	if n == nil || n.Unread == nil {
		return false
	}
	return *n.Unread
}

// GetUpdatedAt returns the UpdatedAt field if it's non-nil, zero value otherwise.
func (n *Notification) GetUpdatedAt() time.Time {
	if n == nil || n.UpdatedAt == nil {
		return time.Time{}
	}
	return *n.UpdatedAt
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (n *Notification) GetURL() string {
	if n == nil || n.URL == nil {
		return ""
	}
	return *n.URL
}

// GetLatestCommentURL returns the LatestCommentURL field if it's non-nil, zero value otherwise.
func (n *NotificationSubject) GetLatestCommentURL() string {
	if n == nil || n.LatestCommentURL == nil {
		return ""
	}
	return *n.LatestCommentURL
}

// GetTitle returns the Title field if it's non-nil, zero value otherwise.
func (n *NotificationSubject) GetTitle() string {
	if n == nil || n.Title == nil {
		return ""
	}
	return *n.Title
}

// GetType returns the Type field if it's non-nil, zero value otherwise.
func (n *NotificationSubject) GetType() string {
	if n == nil || n.Type == nil {
		return ""
	}
	return *n.Type
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (n *NotificationSubject) GetURL() string {
	if n == nil || n.URL == nil {
		return ""
	}
	return *n.URL
}

// GetClientID returns the ClientID field if it's non-nil, zero value otherwise.
func (o *OAuthAPP) GetClientID() string {
	if o == nil || o.ClientID == nil {
		return ""
	}
	return *o.ClientID
}

// GetName returns the Name field if it's non-nil, zero value otherwise.
func (o *OAuthAPP) GetName() string {
	if o == nil || o.Name == nil {
		return ""
	}
	return *o.Name
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (o *OAuthAPP) GetURL() string {
	if o == nil || o.URL == nil {
		return ""
	}
	return *o.URL
}

// GetAvatarURL returns the AvatarURL field if it's non-nil, zero value otherwise.
func (o *Organization) GetAvatarURL() string {
	if o == nil || o.AvatarURL == nil {
		return ""
	}
	return *o.AvatarURL
}

// GetBillingEmail returns the BillingEmail field if it's non-nil, zero value otherwise.
func (o *Organization) GetBillingEmail() string {
	if o == nil || o.BillingEmail == nil {
		return ""
	}
	return *o.BillingEmail
}

// GetBlog returns the Blog field if it's non-nil, zero value otherwise.
func (o *Organization) GetBlog() string {
	if o == nil || o.Blog == nil {
		return ""
	}
	return *o.Blog
}

// GetCollaborators returns the Collaborators field if it's non-nil, zero value otherwise.
func (o *Organization) GetCollaborators() int {
	if o == nil || o.Collaborators == nil {
		return 0
	}
	return *o.Collaborators
}

// GetCompany returns the Company field if it's non-nil, zero value otherwise.
func (o *Organization) GetCompany() string {
	if o == nil || o.Company == nil {
		return ""
	}
	return *o.Company
}

// GetCreatedAt returns the CreatedAt field if it's non-nil, zero value otherwise.
func (o *Organization) GetCreatedAt() time.Time {
	if o == nil || o.CreatedAt == nil {
		return time.Time{}
	}
	return *o.CreatedAt
}

// GetDefaultRepoPermission returns the DefaultRepoPermission field if it's non-nil, zero value otherwise.
func (o *Organization) GetDefaultRepoPermission() string {
	if o == nil || o.DefaultRepoPermission == nil {
		return ""
	}
	return *o.DefaultRepoPermission
}

// GetDefaultRepoSettings returns the DefaultRepoSettings field if it's non-nil, zero value otherwise.
func (o *Organization) GetDefaultRepoSettings() string {
	if o == nil || o.DefaultRepoSettings == nil {
		return ""
	}
	return *o.DefaultRepoSettings
}

// GetDescription returns the Description field if it's non-nil, zero value otherwise.
func (o *Organization) GetDescription() string {
	if o == nil || o.Description == nil {
		return ""
	}
	return *o.Description
}

// GetDiskUsage returns the DiskUsage field if it's non-nil, zero value otherwise.
func (o *Organization) GetDiskUsage() int {
	if o == nil || o.DiskUsage == nil {
		return 0
	}
	return *o.DiskUsage
}

// GetEmail returns the Email field if it's non-nil, zero value otherwise.
func (o *Organization) GetEmail() string {
	if o == nil || o.Email == nil {
		return ""
	}
	return *o.Email
}

// GetEventsURL returns the EventsURL field if it's non-nil, zero value otherwise.
func (o *Organization) GetEventsURL() string {
	if o == nil || o.EventsURL == nil {
		return ""
	}
	return *o.EventsURL
}

// GetFollowers returns the Followers field if it's non-nil, zero value otherwise.
func (o *Organization) GetFollowers() int {
	if o == nil || o.Followers == nil {
		return 0
	}
	return *o.Followers
}

// GetFollowing returns the Following field if it's non-nil, zero value otherwise.
func (o *Organization) GetFollowing() int {
	if o == nil || o.Following == nil {
		return 0
	}
	return *o.Following
}

// GetHasOrganizationProjects returns the HasOrganizationProjects field if it's non-nil, zero value otherwise.
func (o *Organization) GetHasOrganizationProjects() bool {
	if o == nil || o.HasOrganizationProjects == nil {
		return false
	}
	return *o.HasOrganizationProjects
}

// GetHasRepositoryProjects returns the HasRepositoryProjects field if it's non-nil, zero value otherwise.
func (o *Organization) GetHasRepositoryProjects() bool {
	if o == nil || o.HasRepositoryProjects == nil {
		return false
	}
	return *o.HasRepositoryProjects
}

// GetHooksURL returns the HooksURL field if it's non-nil, zero value otherwise.
func (o *Organization) GetHooksURL() string {
	if o == nil || o.HooksURL == nil {
		return ""
	}
	return *o.HooksURL
}

// GetHTMLURL returns the HTMLURL field if it's non-nil, zero value otherwise.
func (o *Organization) GetHTMLURL() string {
	if o == nil || o.HTMLURL == nil {
		return ""
	}
	return *o.HTMLURL
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (o *Organization) GetID() int64 {
	if o == nil || o.ID == nil {
		return 0
	}
	return *o.ID
}

// GetIssuesURL returns the IssuesURL field if it's non-nil, zero value otherwise.
func (o *Organization) GetIssuesURL() string {
	if o == nil || o.IssuesURL == nil {
		return ""
	}
	return *o.IssuesURL
}

// GetIsVerified returns the IsVerified field if it's non-nil, zero value otherwise.
func (o *Organization) GetIsVerified() bool {
	if o == nil || o.IsVerified == nil {
		return false
	}
	return *o.IsVerified
}

// GetLocation returns the Location field if it's non-nil, zero value otherwise.
func (o *Organization) GetLocation() string {
	if o == nil || o.Location == nil {
		return ""
	}
	return *o.Location
}

// GetLogin returns the Login field if it's non-nil, zero value otherwise.
func (o *Organization) GetLogin() string {
	if o == nil || o.Login == nil {
		return ""
	}
	return *o.Login
}

// GetMembersAllowedRepositoryCreationType returns the MembersAllowedRepositoryCreationType field if it's non-nil, zero value otherwise.
func (o *Organization) GetMembersAllowedRepositoryCreationType() string {
	if o == nil || o.MembersAllowedRepositoryCreationType == nil {
		return ""
	}
	return *o.MembersAllowedRepositoryCreationType
}

// GetMembersCanCreateInternalRepos returns the MembersCanCreateInternalRepos field if it's non-nil, zero value otherwise.
func (o *Organization) GetMembersCanCreateInternalRepos() bool {
	if o == nil || o.MembersCanCreateInternalRepos == nil {
		return false
	}
	return *o.MembersCanCreateInternalRepos
}

// GetMembersCanCreatePages returns the MembersCanCreatePages field if it's non-nil, zero value otherwise.
func (o *Organization) GetMembersCanCreatePages() bool {
	if o == nil || o.MembersCanCreatePages == nil {
		return false
	}
	return *o.MembersCanCreatePages
}

// GetMembersCanCreatePrivatePages returns the MembersCanCreatePrivatePages field if it's non-nil, zero value otherwise.
func (o *Organization) GetMembersCanCreatePrivatePages() bool {
	if o == nil || o.MembersCanCreatePrivatePages == nil {
		return false
	}
	return *o.MembersCanCreatePrivatePages
}

// GetMembersCanCreatePrivateRepos returns the MembersCanCreatePrivateRepos field if it's non-nil, zero value otherwise.
func (o *Organization) GetMembersCanCreatePrivateRepos() bool {
	if o == nil || o.MembersCanCreatePrivateRepos == nil {
		return false
	}
	return *o.MembersCanCreatePrivateRepos
}

// GetMembersCanCreatePublicPages returns the MembersCanCreatePublicPages field if it's non-nil, zero value otherwise.
func (o *Organization) GetMembersCanCreatePublicPages() bool {
	if o == nil || o.MembersCanCreatePublicPages == nil {
		return false
	}
	return *o.MembersCanCreatePublicPages
}

// GetMembersCanCreatePublicRepos returns the MembersCanCreatePublicRepos field if it's non-nil, zero value otherwise.
func (o *Organization) GetMembersCanCreatePublicRepos() bool {
	if o == nil || o.MembersCanCreatePublicRepos == nil {
		return false
	}
	return *o.MembersCanCreatePublicRepos
}

// GetMembersCanCreateRepos returns the MembersCanCreateRepos field if it's non-nil, zero value otherwise.
func (o *Organization) GetMembersCanCreateRepos() bool {
	if o == nil || o.MembersCanCreateRepos == nil {
		return false
	}
	return *o.MembersCanCreateRepos
}

// GetMembersURL returns the MembersURL field if it's non-nil, zero value otherwise.
func (o *Organization) GetMembersURL() string {
	if o == nil || o.MembersURL == nil {
		return ""
	}
	return *o.MembersURL
}

// GetName returns the Name field if it's non-nil, zero value otherwise.
func (o *Organization) GetName() string {
	if o == nil || o.Name == nil {
		return ""
	}
	return *o.Name
}

// GetNodeID returns the NodeID field if it's non-nil, zero value otherwise.
func (o *Organization) GetNodeID() string {
	if o == nil || o.NodeID == nil {
		return ""
	}
	return *o.NodeID
}

// GetOwnedPrivateRepos returns the OwnedPrivateRepos field if it's non-nil, zero value otherwise.
func (o *Organization) GetOwnedPrivateRepos() int {
	if o == nil || o.OwnedPrivateRepos == nil {
		return 0
	}
	return *o.OwnedPrivateRepos
}

// GetPlan returns the Plan field.
func (o *Organization) GetPlan() *Plan {
	if o == nil {
		return nil
	}
	return o.Plan
}

// GetPrivateGists returns the PrivateGists field if it's non-nil, zero value otherwise.
func (o *Organization) GetPrivateGists() int {
	if o == nil || o.PrivateGists == nil {
		return 0
	}
	return *o.PrivateGists
}

// GetPublicGists returns the PublicGists field if it's non-nil, zero value otherwise.
func (o *Organization) GetPublicGists() int {
	if o == nil || o.PublicGists == nil {
		return 0
	}
	return *o.PublicGists
}

// GetPublicMembersURL returns the PublicMembersURL field if it's non-nil, zero value otherwise.
func (o *Organization) GetPublicMembersURL() string {
	if o == nil || o.PublicMembersURL == nil {
		return ""
	}
	return *o.PublicMembersURL
}

// GetPublicRepos returns the PublicRepos field if it's non-nil, zero value otherwise.
func (o *Organization) GetPublicRepos() int {
	if o == nil || o.PublicRepos == nil {
		return 0
	}
	return *o.PublicRepos
}

// GetReposURL returns the ReposURL field if it's non-nil, zero value otherwise.
func (o *Organization) GetReposURL() string {
	if o == nil || o.ReposURL == nil {
		return ""
	}
	return *o.ReposURL
}

// GetTotalPrivateRepos returns the TotalPrivateRepos field if it's non-nil, zero value otherwise.
func (o *Organization) GetTotalPrivateRepos() int {
	if o == nil || o.TotalPrivateRepos == nil {
		return 0
	}
	return *o.TotalPrivateRepos
}

// GetTwitterUsername returns the TwitterUsername field if it's non-nil, zero value otherwise.
func (o *Organization) GetTwitterUsername() string {
	if o == nil || o.TwitterUsername == nil {
		return ""
	}
	return *o.TwitterUsername
}

// GetTwoFactorRequirementEnabled returns the TwoFactorRequirementEnabled field if it's non-nil, zero value otherwise.
func (o *Organization) GetTwoFactorRequirementEnabled() bool {
	if o == nil || o.TwoFactorRequirementEnabled == nil {
		return false
	}
	return *o.TwoFactorRequirementEnabled
}

// GetType returns the Type field if it's non-nil, zero value otherwise.
func (o *Organization) GetType() string {
	if o == nil || o.Type == nil {
		return ""
	}
	return *o.Type
}

// GetUpdatedAt returns the UpdatedAt field if it's non-nil, zero value otherwise.
func (o *Organization) GetUpdatedAt() time.Time {
	if o == nil || o.UpdatedAt == nil {
		return time.Time{}
	}
	return *o.UpdatedAt
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (o *Organization) GetURL() string {
	if o == nil || o.URL == nil {
		return ""
	}
	return *o.URL
}

// GetAction returns the Action field if it's non-nil, zero value otherwise.
func (o *OrganizationEvent) GetAction() string {
	if o == nil || o.Action == nil {
		return ""
	}
	return *o.Action
}

// GetInstallation returns the Installation field.
func (o *OrganizationEvent) GetInstallation() *Installation {
	if o == nil {
		return nil
	}
	return o.Installation
}

// GetInvitation returns the Invitation field.
func (o *OrganizationEvent) GetInvitation() *Invitation {
	if o == nil {
		return nil
	}
	return o.Invitation
}

// GetMembership returns the Membership field.
func (o *OrganizationEvent) GetMembership() *Membership {
	if o == nil {
		return nil
	}
	return o.Membership
}

// GetOrganization returns the Organization field.
func (o *OrganizationEvent) GetOrganization() *Organization {
	if o == nil {
		return nil
	}
	return o.Organization
}

// GetSender returns the Sender field.
func (o *OrganizationEvent) GetSender() *User {
	if o == nil {
		return nil
	}
	return o.Sender
}

// GetTotalCount returns the TotalCount field if it's non-nil, zero value otherwise.
func (o *OrganizationInstallations) GetTotalCount() int {
	if o == nil || o.TotalCount == nil {
		return 0
	}
	return *o.TotalCount
}

// GetAction returns the Action field if it's non-nil, zero value otherwise.
func (o *OrgBlockEvent) GetAction() string {
	if o == nil || o.Action == nil {
		return ""
	}
	return *o.Action
}

// GetBlockedUser returns the BlockedUser field.
func (o *OrgBlockEvent) GetBlockedUser() *User {
	if o == nil {
		return nil
	}
	return o.BlockedUser
}

// GetInstallation returns the Installation field.
func (o *OrgBlockEvent) GetInstallation() *Installation {
	if o == nil {
		return nil
	}
	return o.Installation
}

// GetOrganization returns the Organization field.
func (o *OrgBlockEvent) GetOrganization() *Organization {
	if o == nil {
		return nil
	}
	return o.Organization
}

// GetSender returns the Sender field.
func (o *OrgBlockEvent) GetSender() *User {
	if o == nil {
		return nil
	}
	return o.Sender
}

// GetDisabledOrgs returns the DisabledOrgs field if it's non-nil, zero value otherwise.
func (o *OrgStats) GetDisabledOrgs() int {
	if o == nil || o.DisabledOrgs == nil {
		return 0
	}
	return *o.DisabledOrgs
}

// GetTotalOrgs returns the TotalOrgs field if it's non-nil, zero value otherwise.
func (o *OrgStats) GetTotalOrgs() int {
	if o == nil || o.TotalOrgs == nil {
		return 0
	}
	return *o.TotalOrgs
}

// GetTotalTeamMembers returns the TotalTeamMembers field if it's non-nil, zero value otherwise.
func (o *OrgStats) GetTotalTeamMembers() int {
	if o == nil || o.TotalTeamMembers == nil {
		return 0
	}
	return *o.TotalTeamMembers
}

// GetTotalTeams returns the TotalTeams field if it's non-nil, zero value otherwise.
func (o *OrgStats) GetTotalTeams() int {
	if o == nil || o.TotalTeams == nil {
		return 0
	}
	return *o.TotalTeams
}

// GetCreatedAt returns the CreatedAt field if it's non-nil, zero value otherwise.
func (p *Package) GetCreatedAt() Timestamp {
	if p == nil || p.CreatedAt == nil {
		return Timestamp{}
	}
	return *p.CreatedAt
}

// GetHTMLURL returns the HTMLURL field if it's non-nil, zero value otherwise.
func (p *Package) GetHTMLURL() string {
	if p == nil || p.HTMLURL == nil {
		return ""
	}
	return *p.HTMLURL
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (p *Package) GetID() int64 {
	if p == nil || p.ID == nil {
		return 0
	}
	return *p.ID
}

// GetName returns the Name field if it's non-nil, zero value otherwise.
func (p *Package) GetName() string {
	if p == nil || p.Name == nil {
		return ""
	}
	return *p.Name
}

// GetOwner returns the Owner field.
func (p *Package) GetOwner() *User {
	if p == nil {
		return nil
	}
	return p.Owner
}

// GetPackageType returns the PackageType field if it's non-nil, zero value otherwise.
func (p *Package) GetPackageType() string {
	if p == nil || p.PackageType == nil {
		return ""
	}
	return *p.PackageType
}

// GetPackageVersion returns the PackageVersion field.
func (p *Package) GetPackageVersion() *PackageVersion {
	if p == nil {
		return nil
	}
	return p.PackageVersion
}

// GetRegistry returns the Registry field.
func (p *Package) GetRegistry() *PackageRegistry {
	if p == nil {
		return nil
	}
	return p.Registry
}

// GetUpdatedAt returns the UpdatedAt field if it's non-nil, zero value otherwise.
func (p *Package) GetUpdatedAt() Timestamp {
	if p == nil || p.UpdatedAt == nil {
		return Timestamp{}
	}
	return *p.UpdatedAt
}

// GetAction returns the Action field if it's non-nil, zero value otherwise.
func (p *PackageEvent) GetAction() string {
	if p == nil || p.Action == nil {
		return ""
	}
	return *p.Action
}

// GetOrg returns the Org field.
func (p *PackageEvent) GetOrg() *Organization {
	if p == nil {
		return nil
	}
	return p.Org
}

// GetPackage returns the Package field.
func (p *PackageEvent) GetPackage() *Package {
	if p == nil {
		return nil
	}
	return p.Package
}

// GetRepo returns the Repo field.
func (p *PackageEvent) GetRepo() *Repository {
	if p == nil {
		return nil
	}
	return p.Repo
}

// GetSender returns the Sender field.
func (p *PackageEvent) GetSender() *User {
	if p == nil {
		return nil
	}
	return p.Sender
}

// GetAuthor returns the Author field.
func (p *PackageFile) GetAuthor() *User {
	if p == nil {
		return nil
	}
	return p.Author
}

// GetContentType returns the ContentType field if it's non-nil, zero value otherwise.
func (p *PackageFile) GetContentType() string {
	if p == nil || p.ContentType == nil {
		return ""
	}
	return *p.ContentType
}

// GetCreatedAt returns the CreatedAt field if it's non-nil, zero value otherwise.
func (p *PackageFile) GetCreatedAt() Timestamp {
	if p == nil || p.CreatedAt == nil {
		return Timestamp{}
	}
	return *p.CreatedAt
}

// GetDownloadURL returns the DownloadURL field if it's non-nil, zero value otherwise.
func (p *PackageFile) GetDownloadURL() string {
	if p == nil || p.DownloadURL == nil {
		return ""
	}
	return *p.DownloadURL
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (p *PackageFile) GetID() int64 {
	if p == nil || p.ID == nil {
		return 0
	}
	return *p.ID
}

// GetMD5 returns the MD5 field if it's non-nil, zero value otherwise.
func (p *PackageFile) GetMD5() string {
	if p == nil || p.MD5 == nil {
		return ""
	}
	return *p.MD5
}

// GetName returns the Name field if it's non-nil, zero value otherwise.
func (p *PackageFile) GetName() string {
	if p == nil || p.Name == nil {
		return ""
	}
	return *p.Name
}

// GetSHA1 returns the SHA1 field if it's non-nil, zero value otherwise.
func (p *PackageFile) GetSHA1() string {
	if p == nil || p.SHA1 == nil {
		return ""
	}
	return *p.SHA1
}

// GetSHA256 returns the SHA256 field if it's non-nil, zero value otherwise.
func (p *PackageFile) GetSHA256() string {
	if p == nil || p.SHA256 == nil {
		return ""
	}
	return *p.SHA256
}

// GetSize returns the Size field if it's non-nil, zero value otherwise.
func (p *PackageFile) GetSize() int64 {
	if p == nil || p.Size == nil {
		return 0
	}
	return *p.Size
}

// GetState returns the State field if it's non-nil, zero value otherwise.
func (p *PackageFile) GetState() string {
	if p == nil || p.State == nil {
		return ""
	}
	return *p.State
}

// GetUpdatedAt returns the UpdatedAt field if it's non-nil, zero value otherwise.
func (p *PackageFile) GetUpdatedAt() Timestamp {
	if p == nil || p.UpdatedAt == nil {
		return Timestamp{}
	}
	return *p.UpdatedAt
}

// GetAboutURL returns the AboutURL field if it's non-nil, zero value otherwise.
func (p *PackageRegistry) GetAboutURL() string {
	if p == nil || p.AboutURL == nil {
		return ""
	}
	return *p.AboutURL
}

// GetName returns the Name field if it's non-nil, zero value otherwise.
func (p *PackageRegistry) GetName() string {
	if p == nil || p.Name == nil {
		return ""
	}
	return *p.Name
}

// GetType returns the Type field if it's non-nil, zero value otherwise.
func (p *PackageRegistry) GetType() string {
	if p == nil || p.Type == nil {
		return ""
	}
	return *p.Type
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (p *PackageRegistry) GetURL() string {
	if p == nil || p.URL == nil {
		return ""
	}
	return *p.URL
}

// GetVendor returns the Vendor field if it's non-nil, zero value otherwise.
func (p *PackageRegistry) GetVendor() string {
	if p == nil || p.Vendor == nil {
		return ""
	}
	return *p.Vendor
}

// GetAuthor returns the Author field.
func (p *PackageRelease) GetAuthor() *User {
	if p == nil {
		return nil
	}
	return p.Author
}

// GetCreatedAt returns the CreatedAt field if it's non-nil, zero value otherwise.
func (p *PackageRelease) GetCreatedAt() Timestamp {
	if p == nil || p.CreatedAt == nil {
		return Timestamp{}
	}
	return *p.CreatedAt
}

// GetDraft returns the Draft field if it's non-nil, zero value otherwise.
func (p *PackageRelease) GetDraft() bool {
	if p == nil || p.Draft == nil {
		return false
	}
	return *p.Draft
}

// GetHTMLURL returns the HTMLURL field if it's non-nil, zero value otherwise.
func (p *PackageRelease) GetHTMLURL() string {
	if p == nil || p.HTMLURL == nil {
		return ""
	}
	return *p.HTMLURL
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (p *PackageRelease) GetID() int64 {
	if p == nil || p.ID == nil {
		return 0
	}
	return *p.ID
}

// GetName returns the Name field if it's non-nil, zero value otherwise.
func (p *PackageRelease) GetName() string {
	if p == nil || p.Name == nil {
		return ""
	}
	return *p.Name
}

// GetPrerelease returns the Prerelease field if it's non-nil, zero value otherwise.
func (p *PackageRelease) GetPrerelease() bool {
	if p == nil || p.Prerelease == nil {
		return false
	}
	return *p.Prerelease
}

// GetPublishedAt returns the PublishedAt field if it's non-nil, zero value otherwise.
func (p *PackageRelease) GetPublishedAt() Timestamp {
	if p == nil || p.PublishedAt == nil {
		return Timestamp{}
	}
	return *p.PublishedAt
}

// GetTagName returns the TagName field if it's non-nil, zero value otherwise.
func (p *PackageRelease) GetTagName() string {
	if p == nil || p.TagName == nil {
		return ""
	}
	return *p.TagName
}

// GetTargetCommitish returns the TargetCommitish field if it's non-nil, zero value otherwise.
func (p *PackageRelease) GetTargetCommitish() string {
	if p == nil || p.TargetCommitish == nil {
		return ""
	}
	return *p.TargetCommitish
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (p *PackageRelease) GetURL() string {
	if p == nil || p.URL == nil {
		return ""
	}
	return *p.URL
}

// GetAuthor returns the Author field.
func (p *PackageVersion) GetAuthor() *User {
	if p == nil {
		return nil
	}
	return p.Author
}

// GetBody returns the Body field if it's non-nil, zero value otherwise.
func (p *PackageVersion) GetBody() string {
	if p == nil || p.Body == nil {
		return ""
	}
	return *p.Body
}

// GetBodyHTML returns the BodyHTML field if it's non-nil, zero value otherwise.
func (p *PackageVersion) GetBodyHTML() string {
	if p == nil || p.BodyHTML == nil {
		return ""
	}
	return *p.BodyHTML
}

// GetCreatedAt returns the CreatedAt field if it's non-nil, zero value otherwise.
func (p *PackageVersion) GetCreatedAt() Timestamp {
	if p == nil || p.CreatedAt == nil {
		return Timestamp{}
	}
	return *p.CreatedAt
}

// GetDraft returns the Draft field if it's non-nil, zero value otherwise.
func (p *PackageVersion) GetDraft() bool {
	if p == nil || p.Draft == nil {
		return false
	}
	return *p.Draft
}

// GetHTMLURL returns the HTMLURL field if it's non-nil, zero value otherwise.
func (p *PackageVersion) GetHTMLURL() string {
	if p == nil || p.HTMLURL == nil {
		return ""
	}
	return *p.HTMLURL
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (p *PackageVersion) GetID() int64 {
	if p == nil || p.ID == nil {
		return 0
	}
	return *p.ID
}

// GetInstallationCommand returns the InstallationCommand field if it's non-nil, zero value otherwise.
func (p *PackageVersion) GetInstallationCommand() string {
	if p == nil || p.InstallationCommand == nil {
		return ""
	}
	return *p.InstallationCommand
}

// GetManifest returns the Manifest field if it's non-nil, zero value otherwise.
func (p *PackageVersion) GetManifest() string {
	if p == nil || p.Manifest == nil {
		return ""
	}
	return *p.Manifest
}

// GetPrerelease returns the Prerelease field if it's non-nil, zero value otherwise.
func (p *PackageVersion) GetPrerelease() bool {
	if p == nil || p.Prerelease == nil {
		return false
	}
	return *p.Prerelease
}

// GetRelease returns the Release field.
func (p *PackageVersion) GetRelease() *PackageRelease {
	if p == nil {
		return nil
	}
	return p.Release
}

// GetSummary returns the Summary field if it's non-nil, zero value otherwise.
func (p *PackageVersion) GetSummary() string {
	if p == nil || p.Summary == nil {
		return ""
	}
	return *p.Summary
}

// GetTagName returns the TagName field if it's non-nil, zero value otherwise.
func (p *PackageVersion) GetTagName() string {
	if p == nil || p.TagName == nil {
		return ""
	}
	return *p.TagName
}

// GetTargetCommitish returns the TargetCommitish field if it's non-nil, zero value otherwise.
func (p *PackageVersion) GetTargetCommitish() string {
	if p == nil || p.TargetCommitish == nil {
		return ""
	}
	return *p.TargetCommitish
}

// GetTargetOID returns the TargetOID field if it's non-nil, zero value otherwise.
func (p *PackageVersion) GetTargetOID() string {
	if p == nil || p.TargetOID == nil {
		return ""
	}
	return *p.TargetOID
}

// GetUpdatedAt returns the UpdatedAt field if it's non-nil, zero value otherwise.
func (p *PackageVersion) GetUpdatedAt() Timestamp {
	if p == nil || p.UpdatedAt == nil {
		return Timestamp{}
	}
	return *p.UpdatedAt
}

// GetVersion returns the Version field if it's non-nil, zero value otherwise.
func (p *PackageVersion) GetVersion() string {
	if p == nil || p.Version == nil {
		return ""
	}
	return *p.Version
}

// GetAction returns the Action field if it's non-nil, zero value otherwise.
func (p *Page) GetAction() string {
	if p == nil || p.Action == nil {
		return ""
	}
	return *p.Action
}

// GetHTMLURL returns the HTMLURL field if it's non-nil, zero value otherwise.
func (p *Page) GetHTMLURL() string {
	if p == nil || p.HTMLURL == nil {
		return ""
	}
	return *p.HTMLURL
}

// GetPageName returns the PageName field if it's non-nil, zero value otherwise.
func (p *Page) GetPageName() string {
	if p == nil || p.PageName == nil {
		return ""
	}
	return *p.PageName
}

// GetSHA returns the SHA field if it's non-nil, zero value otherwise.
func (p *Page) GetSHA() string {
	if p == nil || p.SHA == nil {
		return ""
	}
	return *p.SHA
}

// GetSummary returns the Summary field if it's non-nil, zero value otherwise.
func (p *Page) GetSummary() string {
	if p == nil || p.Summary == nil {
		return ""
	}
	return *p.Summary
}

// GetTitle returns the Title field if it's non-nil, zero value otherwise.
func (p *Page) GetTitle() string {
	if p == nil || p.Title == nil {
		return ""
	}
	return *p.Title
}

// GetBuild returns the Build field.
func (p *PageBuildEvent) GetBuild() *PagesBuild {
	if p == nil {
		return nil
	}
	return p.Build
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (p *PageBuildEvent) GetID() int64 {
	if p == nil || p.ID == nil {
		return 0
	}
	return *p.ID
}

// GetInstallation returns the Installation field.
func (p *PageBuildEvent) GetInstallation() *Installation {
	if p == nil {
		return nil
	}
	return p.Installation
}

// GetRepo returns the Repo field.
func (p *PageBuildEvent) GetRepo() *Repository {
	if p == nil {
		return nil
	}
	return p.Repo
}

// GetSender returns the Sender field.
func (p *PageBuildEvent) GetSender() *User {
	if p == nil {
		return nil
	}
	return p.Sender
}

// GetCNAME returns the CNAME field if it's non-nil, zero value otherwise.
func (p *Pages) GetCNAME() string {
	if p == nil || p.CNAME == nil {
		return ""
	}
	return *p.CNAME
}

// GetCustom404 returns the Custom404 field if it's non-nil, zero value otherwise.
func (p *Pages) GetCustom404() bool {
	if p == nil || p.Custom404 == nil {
		return false
	}
	return *p.Custom404
}

// GetHTMLURL returns the HTMLURL field if it's non-nil, zero value otherwise.
func (p *Pages) GetHTMLURL() string {
	if p == nil || p.HTMLURL == nil {
		return ""
	}
	return *p.HTMLURL
}

// GetSource returns the Source field.
func (p *Pages) GetSource() *PagesSource {
	if p == nil {
		return nil
	}
	return p.Source
}

// GetStatus returns the Status field if it's non-nil, zero value otherwise.
func (p *Pages) GetStatus() string {
	if p == nil || p.Status == nil {
		return ""
	}
	return *p.Status
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (p *Pages) GetURL() string {
	if p == nil || p.URL == nil {
		return ""
	}
	return *p.URL
}

// GetCommit returns the Commit field if it's non-nil, zero value otherwise.
func (p *PagesBuild) GetCommit() string {
	if p == nil || p.Commit == nil {
		return ""
	}
	return *p.Commit
}

// GetCreatedAt returns the CreatedAt field if it's non-nil, zero value otherwise.
func (p *PagesBuild) GetCreatedAt() Timestamp {
	if p == nil || p.CreatedAt == nil {
		return Timestamp{}
	}
	return *p.CreatedAt
}

// GetDuration returns the Duration field if it's non-nil, zero value otherwise.
func (p *PagesBuild) GetDuration() int {
	if p == nil || p.Duration == nil {
		return 0
	}
	return *p.Duration
}

// GetError returns the Error field.
func (p *PagesBuild) GetError() *PagesError {
	if p == nil {
		return nil
	}
	return p.Error
}

// GetPusher returns the Pusher field.
func (p *PagesBuild) GetPusher() *User {
	if p == nil {
		return nil
	}
	return p.Pusher
}

// GetStatus returns the Status field if it's non-nil, zero value otherwise.
func (p *PagesBuild) GetStatus() string {
	if p == nil || p.Status == nil {
		return ""
	}
	return *p.Status
}

// GetUpdatedAt returns the UpdatedAt field if it's non-nil, zero value otherwise.
func (p *PagesBuild) GetUpdatedAt() Timestamp {
	if p == nil || p.UpdatedAt == nil {
		return Timestamp{}
	}
	return *p.UpdatedAt
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (p *PagesBuild) GetURL() string {
	if p == nil || p.URL == nil {
		return ""
	}
	return *p.URL
}

// GetMessage returns the Message field if it's non-nil, zero value otherwise.
func (p *PagesError) GetMessage() string {
	if p == nil || p.Message == nil {
		return ""
	}
	return *p.Message
}

// GetBranch returns the Branch field if it's non-nil, zero value otherwise.
func (p *PagesSource) GetBranch() string {
	if p == nil || p.Branch == nil {
		return ""
	}
	return *p.Branch
}

// GetPath returns the Path field if it's non-nil, zero value otherwise.
func (p *PagesSource) GetPath() string {
	if p == nil || p.Path == nil {
		return ""
	}
	return *p.Path
}

// GetTotalPages returns the TotalPages field if it's non-nil, zero value otherwise.
func (p *PageStats) GetTotalPages() int {
	if p == nil || p.TotalPages == nil {
		return 0
	}
	return *p.TotalPages
}

// GetCNAME returns the CNAME field if it's non-nil, zero value otherwise.
func (p *PagesUpdate) GetCNAME() string {
	if p == nil || p.CNAME == nil {
		return ""
	}
	return *p.CNAME
}

// GetSource returns the Source field if it's non-nil, zero value otherwise.
func (p *PagesUpdate) GetSource() string {
	if p == nil || p.Source == nil {
		return ""
	}
	return *p.Source
}

// GetHook returns the Hook field.
func (p *PingEvent) GetHook() *Hook {
	if p == nil {
		return nil
	}
	return p.Hook
}

// GetHookID returns the HookID field if it's non-nil, zero value otherwise.
func (p *PingEvent) GetHookID() int64 {
	if p == nil || p.HookID == nil {
		return 0
	}
	return *p.HookID
}

// GetInstallation returns the Installation field.
func (p *PingEvent) GetInstallation() *Installation {
	if p == nil {
		return nil
	}
	return p.Installation
}

// GetZen returns the Zen field if it's non-nil, zero value otherwise.
func (p *PingEvent) GetZen() string {
	if p == nil || p.Zen == nil {
		return ""
	}
	return *p.Zen
}

// GetCollaborators returns the Collaborators field if it's non-nil, zero value otherwise.
func (p *Plan) GetCollaborators() int {
	if p == nil || p.Collaborators == nil {
		return 0
	}
	return *p.Collaborators
}

// GetFilledSeats returns the FilledSeats field if it's non-nil, zero value otherwise.
func (p *Plan) GetFilledSeats() int {
	if p == nil || p.FilledSeats == nil {
		return 0
	}
	return *p.FilledSeats
}

// GetName returns the Name field if it's non-nil, zero value otherwise.
func (p *Plan) GetName() string {
	if p == nil || p.Name == nil {
		return ""
	}
	return *p.Name
}

// GetPrivateRepos returns the PrivateRepos field if it's non-nil, zero value otherwise.
func (p *Plan) GetPrivateRepos() int {
	if p == nil || p.PrivateRepos == nil {
		return 0
	}
	return *p.PrivateRepos
}

// GetSeats returns the Seats field if it's non-nil, zero value otherwise.
func (p *Plan) GetSeats() int {
	if p == nil || p.Seats == nil {
		return 0
	}
	return *p.Seats
}

// GetSpace returns the Space field if it's non-nil, zero value otherwise.
func (p *Plan) GetSpace() int {
	if p == nil || p.Space == nil {
		return 0
	}
	return *p.Space
}

// GetConfigURL returns the ConfigURL field if it's non-nil, zero value otherwise.
func (p *PreReceiveHook) GetConfigURL() string {
	if p == nil || p.ConfigURL == nil {
		return ""
	}
	return *p.ConfigURL
}

// GetEnforcement returns the Enforcement field if it's non-nil, zero value otherwise.
func (p *PreReceiveHook) GetEnforcement() string {
	if p == nil || p.Enforcement == nil {
		return ""
	}
	return *p.Enforcement
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (p *PreReceiveHook) GetID() int64 {
	if p == nil || p.ID == nil {
		return 0
	}
	return *p.ID
}

// GetName returns the Name field if it's non-nil, zero value otherwise.
func (p *PreReceiveHook) GetName() string {
	if p == nil || p.Name == nil {
		return ""
	}
	return *p.Name
}

// GetHRef returns the HRef field if it's non-nil, zero value otherwise.
func (p *PRLink) GetHRef() string {
	if p == nil || p.HRef == nil {
		return ""
	}
	return *p.HRef
}

// GetComments returns the Comments field.
func (p *PRLinks) GetComments() *PRLink {
	if p == nil {
		return nil
	}
	return p.Comments
}

// GetCommits returns the Commits field.
func (p *PRLinks) GetCommits() *PRLink {
	if p == nil {
		return nil
	}
	return p.Commits
}

// GetHTML returns the HTML field.
func (p *PRLinks) GetHTML() *PRLink {
	if p == nil {
		return nil
	}
	return p.HTML
}

// GetIssue returns the Issue field.
func (p *PRLinks) GetIssue() *PRLink {
	if p == nil {
		return nil
	}
	return p.Issue
}

// GetReviewComment returns the ReviewComment field.
func (p *PRLinks) GetReviewComment() *PRLink {
	if p == nil {
		return nil
	}
	return p.ReviewComment
}

// GetReviewComments returns the ReviewComments field.
func (p *PRLinks) GetReviewComments() *PRLink {
	if p == nil {
		return nil
	}
	return p.ReviewComments
}

// GetSelf returns the Self field.
func (p *PRLinks) GetSelf() *PRLink {
	if p == nil {
		return nil
	}
	return p.Self
}

// GetStatuses returns the Statuses field.
func (p *PRLinks) GetStatuses() *PRLink {
	if p == nil {
		return nil
	}
	return p.Statuses
}

// GetBody returns the Body field if it's non-nil, zero value otherwise.
func (p *Project) GetBody() string {
	if p == nil || p.Body == nil {
		return ""
	}
	return *p.Body
}

// GetColumnsURL returns the ColumnsURL field if it's non-nil, zero value otherwise.
func (p *Project) GetColumnsURL() string {
	if p == nil || p.ColumnsURL == nil {
		return ""
	}
	return *p.ColumnsURL
}

// GetCreatedAt returns the CreatedAt field if it's non-nil, zero value otherwise.
func (p *Project) GetCreatedAt() Timestamp {
	if p == nil || p.CreatedAt == nil {
		return Timestamp{}
	}
	return *p.CreatedAt
}

// GetCreator returns the Creator field.
func (p *Project) GetCreator() *User {
	if p == nil {
		return nil
	}
	return p.Creator
}

// GetHTMLURL returns the HTMLURL field if it's non-nil, zero value otherwise.
func (p *Project) GetHTMLURL() string {
	if p == nil || p.HTMLURL == nil {
		return ""
	}
	return *p.HTMLURL
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (p *Project) GetID() int64 {
	if p == nil || p.ID == nil {
		return 0
	}
	return *p.ID
}

// GetName returns the Name field if it's non-nil, zero value otherwise.
func (p *Project) GetName() string {
	if p == nil || p.Name == nil {
		return ""
	}
	return *p.Name
}

// GetNodeID returns the NodeID field if it's non-nil, zero value otherwise.
func (p *Project) GetNodeID() string {
	if p == nil || p.NodeID == nil {
		return ""
	}
	return *p.NodeID
}

// GetNumber returns the Number field if it's non-nil, zero value otherwise.
func (p *Project) GetNumber() int {
	if p == nil || p.Number == nil {
		return 0
	}
	return *p.Number
}

// GetOwnerURL returns the OwnerURL field if it's non-nil, zero value otherwise.
func (p *Project) GetOwnerURL() string {
	if p == nil || p.OwnerURL == nil {
		return ""
	}
	return *p.OwnerURL
}

// GetState returns the State field if it's non-nil, zero value otherwise.
func (p *Project) GetState() string {
	if p == nil || p.State == nil {
		return ""
	}
	return *p.State
}

// GetUpdatedAt returns the UpdatedAt field if it's non-nil, zero value otherwise.
func (p *Project) GetUpdatedAt() Timestamp {
	if p == nil || p.UpdatedAt == nil {
		return Timestamp{}
	}
	return *p.UpdatedAt
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (p *Project) GetURL() string {
	if p == nil || p.URL == nil {
		return ""
	}
	return *p.URL
}

// GetArchived returns the Archived field if it's non-nil, zero value otherwise.
func (p *ProjectCard) GetArchived() bool {
	if p == nil || p.Archived == nil {
		return false
	}
	return *p.Archived
}

// GetColumnID returns the ColumnID field if it's non-nil, zero value otherwise.
func (p *ProjectCard) GetColumnID() int64 {
	if p == nil || p.ColumnID == nil {
		return 0
	}
	return *p.ColumnID
}

// GetColumnName returns the ColumnName field if it's non-nil, zero value otherwise.
func (p *ProjectCard) GetColumnName() string {
	if p == nil || p.ColumnName == nil {
		return ""
	}
	return *p.ColumnName
}

// GetColumnURL returns the ColumnURL field if it's non-nil, zero value otherwise.
func (p *ProjectCard) GetColumnURL() string {
	if p == nil || p.ColumnURL == nil {
		return ""
	}
	return *p.ColumnURL
}

// GetContentURL returns the ContentURL field if it's non-nil, zero value otherwise.
func (p *ProjectCard) GetContentURL() string {
	if p == nil || p.ContentURL == nil {
		return ""
	}
	return *p.ContentURL
}

// GetCreatedAt returns the CreatedAt field if it's non-nil, zero value otherwise.
func (p *ProjectCard) GetCreatedAt() Timestamp {
	if p == nil || p.CreatedAt == nil {
		return Timestamp{}
	}
	return *p.CreatedAt
}

// GetCreator returns the Creator field.
func (p *ProjectCard) GetCreator() *User {
	if p == nil {
		return nil
	}
	return p.Creator
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (p *ProjectCard) GetID() int64 {
	if p == nil || p.ID == nil {
		return 0
	}
	return *p.ID
}

// GetNodeID returns the NodeID field if it's non-nil, zero value otherwise.
func (p *ProjectCard) GetNodeID() string {
	if p == nil || p.NodeID == nil {
		return ""
	}
	return *p.NodeID
}

// GetNote returns the Note field if it's non-nil, zero value otherwise.
func (p *ProjectCard) GetNote() string {
	if p == nil || p.Note == nil {
		return ""
	}
	return *p.Note
}

// GetPreviousColumnName returns the PreviousColumnName field if it's non-nil, zero value otherwise.
func (p *ProjectCard) GetPreviousColumnName() string {
	if p == nil || p.PreviousColumnName == nil {
		return ""
	}
	return *p.PreviousColumnName
}

// GetProjectID returns the ProjectID field if it's non-nil, zero value otherwise.
func (p *ProjectCard) GetProjectID() int64 {
	if p == nil || p.ProjectID == nil {
		return 0
	}
	return *p.ProjectID
}

// GetProjectURL returns the ProjectURL field if it's non-nil, zero value otherwise.
func (p *ProjectCard) GetProjectURL() string {
	if p == nil || p.ProjectURL == nil {
		return ""
	}
	return *p.ProjectURL
}

// GetUpdatedAt returns the UpdatedAt field if it's non-nil, zero value otherwise.
func (p *ProjectCard) GetUpdatedAt() Timestamp {
	if p == nil || p.UpdatedAt == nil {
		return Timestamp{}
	}
	return *p.UpdatedAt
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (p *ProjectCard) GetURL() string {
	if p == nil || p.URL == nil {
		return ""
	}
	return *p.URL
}

// GetAction returns the Action field if it's non-nil, zero value otherwise.
func (p *ProjectCardEvent) GetAction() string {
	if p == nil || p.Action == nil {
		return ""
	}
	return *p.Action
}

// GetAfterID returns the AfterID field if it's non-nil, zero value otherwise.
func (p *ProjectCardEvent) GetAfterID() int64 {
	if p == nil || p.AfterID == nil {
		return 0
	}
	return *p.AfterID
}

// GetChanges returns the Changes field.
func (p *ProjectCardEvent) GetChanges() *ProjectCardChange {
	if p == nil {
		return nil
	}
	return p.Changes
}

// GetInstallation returns the Installation field.
func (p *ProjectCardEvent) GetInstallation() *Installation {
	if p == nil {
		return nil
	}
	return p.Installation
}

// GetOrg returns the Org field.
func (p *ProjectCardEvent) GetOrg() *Organization {
	if p == nil {
		return nil
	}
	return p.Org
}

// GetProjectCard returns the ProjectCard field.
func (p *ProjectCardEvent) GetProjectCard() *ProjectCard {
	if p == nil {
		return nil
	}
	return p.ProjectCard
}

// GetRepo returns the Repo field.
func (p *ProjectCardEvent) GetRepo() *Repository {
	if p == nil {
		return nil
	}
	return p.Repo
}

// GetSender returns the Sender field.
func (p *ProjectCardEvent) GetSender() *User {
	if p == nil {
		return nil
	}
	return p.Sender
}

// GetArchivedState returns the ArchivedState field if it's non-nil, zero value otherwise.
func (p *ProjectCardListOptions) GetArchivedState() string {
	if p == nil || p.ArchivedState == nil {
		return ""
	}
	return *p.ArchivedState
}

// GetArchived returns the Archived field if it's non-nil, zero value otherwise.
func (p *ProjectCardOptions) GetArchived() bool {
	if p == nil || p.Archived == nil {
		return false
	}
	return *p.Archived
}

// GetPermission returns the Permission field if it's non-nil, zero value otherwise.
func (p *ProjectCollaboratorOptions) GetPermission() string {
	if p == nil || p.Permission == nil {
		return ""
	}
	return *p.Permission
}

// GetCardsURL returns the CardsURL field if it's non-nil, zero value otherwise.
func (p *ProjectColumn) GetCardsURL() string {
	if p == nil || p.CardsURL == nil {
		return ""
	}
	return *p.CardsURL
}

// GetCreatedAt returns the CreatedAt field if it's non-nil, zero value otherwise.
func (p *ProjectColumn) GetCreatedAt() Timestamp {
	if p == nil || p.CreatedAt == nil {
		return Timestamp{}
	}
	return *p.CreatedAt
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (p *ProjectColumn) GetID() int64 {
	if p == nil || p.ID == nil {
		return 0
	}
	return *p.ID
}

// GetName returns the Name field if it's non-nil, zero value otherwise.
func (p *ProjectColumn) GetName() string {
	if p == nil || p.Name == nil {
		return ""
	}
	return *p.Name
}

// GetNodeID returns the NodeID field if it's non-nil, zero value otherwise.
func (p *ProjectColumn) GetNodeID() string {
	if p == nil || p.NodeID == nil {
		return ""
	}
	return *p.NodeID
}

// GetProjectURL returns the ProjectURL field if it's non-nil, zero value otherwise.
func (p *ProjectColumn) GetProjectURL() string {
	if p == nil || p.ProjectURL == nil {
		return ""
	}
	return *p.ProjectURL
}

// GetUpdatedAt returns the UpdatedAt field if it's non-nil, zero value otherwise.
func (p *ProjectColumn) GetUpdatedAt() Timestamp {
	if p == nil || p.UpdatedAt == nil {
		return Timestamp{}
	}
	return *p.UpdatedAt
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (p *ProjectColumn) GetURL() string {
	if p == nil || p.URL == nil {
		return ""
	}
	return *p.URL
}

// GetAction returns the Action field if it's non-nil, zero value otherwise.
func (p *ProjectColumnEvent) GetAction() string {
	if p == nil || p.Action == nil {
		return ""
	}
	return *p.Action
}

// GetAfterID returns the AfterID field if it's non-nil, zero value otherwise.
func (p *ProjectColumnEvent) GetAfterID() int64 {
	if p == nil || p.AfterID == nil {
		return 0
	}
	return *p.AfterID
}

// GetChanges returns the Changes field.
func (p *ProjectColumnEvent) GetChanges() *ProjectColumnChange {
	if p == nil {
		return nil
	}
	return p.Changes
}

// GetInstallation returns the Installation field.
func (p *ProjectColumnEvent) GetInstallation() *Installation {
	if p == nil {
		return nil
	}
	return p.Installation
}

// GetOrg returns the Org field.
func (p *ProjectColumnEvent) GetOrg() *Organization {
	if p == nil {
		return nil
	}
	return p.Org
}

// GetProjectColumn returns the ProjectColumn field.
func (p *ProjectColumnEvent) GetProjectColumn() *ProjectColumn {
	if p == nil {
		return nil
	}
	return p.ProjectColumn
}

// GetRepo returns the Repo field.
func (p *ProjectColumnEvent) GetRepo() *Repository {
	if p == nil {
		return nil
	}
	return p.Repo
}

// GetSender returns the Sender field.
func (p *ProjectColumnEvent) GetSender() *User {
	if p == nil {
		return nil
	}
	return p.Sender
}

// GetAction returns the Action field if it's non-nil, zero value otherwise.
func (p *ProjectEvent) GetAction() string {
	if p == nil || p.Action == nil {
		return ""
	}
	return *p.Action
}

// GetChanges returns the Changes field.
func (p *ProjectEvent) GetChanges() *ProjectChange {
	if p == nil {
		return nil
	}
	return p.Changes
}

// GetInstallation returns the Installation field.
func (p *ProjectEvent) GetInstallation() *Installation {
	if p == nil {
		return nil
	}
	return p.Installation
}

// GetOrg returns the Org field.
func (p *ProjectEvent) GetOrg() *Organization {
	if p == nil {
		return nil
	}
	return p.Org
}

// GetProject returns the Project field.
func (p *ProjectEvent) GetProject() *Project {
	if p == nil {
		return nil
	}
	return p.Project
}

// GetRepo returns the Repo field.
func (p *ProjectEvent) GetRepo() *Repository {
	if p == nil {
		return nil
	}
	return p.Repo
}

// GetSender returns the Sender field.
func (p *ProjectEvent) GetSender() *User {
	if p == nil {
		return nil
	}
	return p.Sender
}

// GetBody returns the Body field if it's non-nil, zero value otherwise.
func (p *ProjectOptions) GetBody() string {
	if p == nil || p.Body == nil {
		return ""
	}
	return *p.Body
}

// GetName returns the Name field if it's non-nil, zero value otherwise.
func (p *ProjectOptions) GetName() string {
	if p == nil || p.Name == nil {
		return ""
	}
	return *p.Name
}

// GetOrganizationPermission returns the OrganizationPermission field if it's non-nil, zero value otherwise.
func (p *ProjectOptions) GetOrganizationPermission() string {
	if p == nil || p.OrganizationPermission == nil {
		return ""
	}
	return *p.OrganizationPermission
}

// GetPublic returns the Public field if it's non-nil, zero value otherwise.
func (p *ProjectOptions) GetPublic() bool {
	if p == nil || p.Public == nil {
		return false
	}
	return *p.Public
}

// GetState returns the State field if it's non-nil, zero value otherwise.
func (p *ProjectOptions) GetState() string {
	if p == nil || p.State == nil {
		return ""
	}
	return *p.State
}

// GetPermission returns the Permission field if it's non-nil, zero value otherwise.
func (p *ProjectPermissionLevel) GetPermission() string {
	if p == nil || p.Permission == nil {
		return ""
	}
	return *p.Permission
}

// GetUser returns the User field.
func (p *ProjectPermissionLevel) GetUser() *User {
	if p == nil {
		return nil
	}
	return p.User
}

// GetAllowDeletions returns the AllowDeletions field.
func (p *Protection) GetAllowDeletions() *AllowDeletions {
	if p == nil {
		return nil
	}
	return p.AllowDeletions
}

// GetAllowForcePushes returns the AllowForcePushes field.
func (p *Protection) GetAllowForcePushes() *AllowForcePushes {
	if p == nil {
		return nil
	}
	return p.AllowForcePushes
}

// GetEnforceAdmins returns the EnforceAdmins field.
func (p *Protection) GetEnforceAdmins() *AdminEnforcement {
	if p == nil {
		return nil
	}
	return p.EnforceAdmins
}

// GetRequiredPullRequestReviews returns the RequiredPullRequestReviews field.
func (p *Protection) GetRequiredPullRequestReviews() *PullRequestReviewsEnforcement {
	if p == nil {
		return nil
	}
	return p.RequiredPullRequestReviews
}

// GetRequiredStatusChecks returns the RequiredStatusChecks field.
func (p *Protection) GetRequiredStatusChecks() *RequiredStatusChecks {
	if p == nil {
		return nil
	}
	return p.RequiredStatusChecks
}

// GetRequireLinearHistory returns the RequireLinearHistory field.
func (p *Protection) GetRequireLinearHistory() *RequireLinearHistory {
	if p == nil {
		return nil
	}
	return p.RequireLinearHistory
}

// GetRestrictions returns the Restrictions field.
func (p *Protection) GetRestrictions() *BranchRestrictions {
	if p == nil {
		return nil
	}
	return p.Restrictions
}

// GetAllowDeletions returns the AllowDeletions field if it's non-nil, zero value otherwise.
func (p *ProtectionRequest) GetAllowDeletions() bool {
	if p == nil || p.AllowDeletions == nil {
		return false
	}
	return *p.AllowDeletions
}

// GetAllowForcePushes returns the AllowForcePushes field if it's non-nil, zero value otherwise.
func (p *ProtectionRequest) GetAllowForcePushes() bool {
	if p == nil || p.AllowForcePushes == nil {
		return false
	}
	return *p.AllowForcePushes
}

// GetRequiredPullRequestReviews returns the RequiredPullRequestReviews field.
func (p *ProtectionRequest) GetRequiredPullRequestReviews() *PullRequestReviewsEnforcementRequest {
	if p == nil {
		return nil
	}
	return p.RequiredPullRequestReviews
}

// GetRequiredStatusChecks returns the RequiredStatusChecks field.
func (p *ProtectionRequest) GetRequiredStatusChecks() *RequiredStatusChecks {
	if p == nil {
		return nil
	}
	return p.RequiredStatusChecks
}

// GetRequireLinearHistory returns the RequireLinearHistory field if it's non-nil, zero value otherwise.
func (p *ProtectionRequest) GetRequireLinearHistory() bool {
	if p == nil || p.RequireLinearHistory == nil {
		return false
	}
	return *p.RequireLinearHistory
}

// GetRestrictions returns the Restrictions field.
func (p *ProtectionRequest) GetRestrictions() *BranchRestrictionsRequest {
	if p == nil {
		return nil
	}
	return p.Restrictions
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (p *ProtectionRule) GetID() int64 {
	if p == nil || p.ID == nil {
		return 0
	}
	return *p.ID
}

// GetNodeID returns the NodeID field if it's non-nil, zero value otherwise.
func (p *ProtectionRule) GetNodeID() string {
	if p == nil || p.NodeID == nil {
		return ""
	}
	return *p.NodeID
}

// GetType returns the Type field if it's non-nil, zero value otherwise.
func (p *ProtectionRule) GetType() string {
	if p == nil || p.Type == nil {
		return ""
	}
	return *p.Type
}

// GetWaitTimer returns the WaitTimer field if it's non-nil, zero value otherwise.
func (p *ProtectionRule) GetWaitTimer() int {
	if p == nil || p.WaitTimer == nil {
		return 0
	}
	return *p.WaitTimer
}

// GetInstallation returns the Installation field.
func (p *PublicEvent) GetInstallation() *Installation {
	if p == nil {
		return nil
	}
	return p.Installation
}

// GetRepo returns the Repo field.
func (p *PublicEvent) GetRepo() *Repository {
	if p == nil {
		return nil
	}
	return p.Repo
}

// GetSender returns the Sender field.
func (p *PublicEvent) GetSender() *User {
	if p == nil {
		return nil
	}
	return p.Sender
}

// GetKey returns the Key field if it's non-nil, zero value otherwise.
func (p *PublicKey) GetKey() string {
	if p == nil || p.Key == nil {
		return ""
	}
	return *p.Key
}

// GetKeyID returns the KeyID field if it's non-nil, zero value otherwise.
func (p *PublicKey) GetKeyID() string {
	if p == nil || p.KeyID == nil {
		return ""
	}
	return *p.KeyID
}

// GetActiveLockReason returns the ActiveLockReason field if it's non-nil, zero value otherwise.
func (p *PullRequest) GetActiveLockReason() string {
	if p == nil || p.ActiveLockReason == nil {
		return ""
	}
	return *p.ActiveLockReason
}

// GetAdditions returns the Additions field if it's non-nil, zero value otherwise.
func (p *PullRequest) GetAdditions() int {
	if p == nil || p.Additions == nil {
		return 0
	}
	return *p.Additions
}

// GetAssignee returns the Assignee field.
func (p *PullRequest) GetAssignee() *User {
	if p == nil {
		return nil
	}
	return p.Assignee
}

// GetAuthorAssociation returns the AuthorAssociation field if it's non-nil, zero value otherwise.
func (p *PullRequest) GetAuthorAssociation() string {
	if p == nil || p.AuthorAssociation == nil {
		return ""
	}
	return *p.AuthorAssociation
}

// GetBase returns the Base field.
func (p *PullRequest) GetBase() *PullRequestBranch {
	if p == nil {
		return nil
	}
	return p.Base
}

// GetBody returns the Body field if it's non-nil, zero value otherwise.
func (p *PullRequest) GetBody() string {
	if p == nil || p.Body == nil {
		return ""
	}
	return *p.Body
}

// GetChangedFiles returns the ChangedFiles field if it's non-nil, zero value otherwise.
func (p *PullRequest) GetChangedFiles() int {
	if p == nil || p.ChangedFiles == nil {
		return 0
	}
	return *p.ChangedFiles
}

// GetClosedAt returns the ClosedAt field if it's non-nil, zero value otherwise.
func (p *PullRequest) GetClosedAt() time.Time {
	if p == nil || p.ClosedAt == nil {
		return time.Time{}
	}
	return *p.ClosedAt
}

// GetComments returns the Comments field if it's non-nil, zero value otherwise.
func (p *PullRequest) GetComments() int {
	if p == nil || p.Comments == nil {
		return 0
	}
	return *p.Comments
}

// GetCommentsURL returns the CommentsURL field if it's non-nil, zero value otherwise.
func (p *PullRequest) GetCommentsURL() string {
	if p == nil || p.CommentsURL == nil {
		return ""
	}
	return *p.CommentsURL
}

// GetCommits returns the Commits field if it's non-nil, zero value otherwise.
func (p *PullRequest) GetCommits() int {
	if p == nil || p.Commits == nil {
		return 0
	}
	return *p.Commits
}

// GetCommitsURL returns the CommitsURL field if it's non-nil, zero value otherwise.
func (p *PullRequest) GetCommitsURL() string {
	if p == nil || p.CommitsURL == nil {
		return ""
	}
	return *p.CommitsURL
}

// GetCreatedAt returns the CreatedAt field if it's non-nil, zero value otherwise.
func (p *PullRequest) GetCreatedAt() time.Time {
	if p == nil || p.CreatedAt == nil {
		return time.Time{}
	}
	return *p.CreatedAt
}

// GetDeletions returns the Deletions field if it's non-nil, zero value otherwise.
func (p *PullRequest) GetDeletions() int {
	if p == nil || p.Deletions == nil {
		return 0
	}
	return *p.Deletions
}

// GetDiffURL returns the DiffURL field if it's non-nil, zero value otherwise.
func (p *PullRequest) GetDiffURL() string {
	if p == nil || p.DiffURL == nil {
		return ""
	}
	return *p.DiffURL
}

// GetDraft returns the Draft field if it's non-nil, zero value otherwise.
func (p *PullRequest) GetDraft() bool {
	if p == nil || p.Draft == nil {
		return false
	}
	return *p.Draft
}

// GetHead returns the Head field.
func (p *PullRequest) GetHead() *PullRequestBranch {
	if p == nil {
		return nil
	}
	return p.Head
}

// GetHTMLURL returns the HTMLURL field if it's non-nil, zero value otherwise.
func (p *PullRequest) GetHTMLURL() string {
	if p == nil || p.HTMLURL == nil {
		return ""
	}
	return *p.HTMLURL
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (p *PullRequest) GetID() int64 {
	if p == nil || p.ID == nil {
		return 0
	}
	return *p.ID
}

// GetIssueURL returns the IssueURL field if it's non-nil, zero value otherwise.
func (p *PullRequest) GetIssueURL() string {
	if p == nil || p.IssueURL == nil {
		return ""
	}
	return *p.IssueURL
}

// GetLinks returns the Links field.
func (p *PullRequest) GetLinks() *PRLinks {
	if p == nil {
		return nil
	}
	return p.Links
}

// GetLocked returns the Locked field if it's non-nil, zero value otherwise.
func (p *PullRequest) GetLocked() bool {
	if p == nil || p.Locked == nil {
		return false
	}
	return *p.Locked
}

// GetMaintainerCanModify returns the MaintainerCanModify field if it's non-nil, zero value otherwise.
func (p *PullRequest) GetMaintainerCanModify() bool {
	if p == nil || p.MaintainerCanModify == nil {
		return false
	}
	return *p.MaintainerCanModify
}

// GetMergeable returns the Mergeable field if it's non-nil, zero value otherwise.
func (p *PullRequest) GetMergeable() bool {
	if p == nil || p.Mergeable == nil {
		return false
	}
	return *p.Mergeable
}

// GetMergeableState returns the MergeableState field if it's non-nil, zero value otherwise.
func (p *PullRequest) GetMergeableState() string {
	if p == nil || p.MergeableState == nil {
		return ""
	}
	return *p.MergeableState
}

// GetMergeCommitSHA returns the MergeCommitSHA field if it's non-nil, zero value otherwise.
func (p *PullRequest) GetMergeCommitSHA() string {
	if p == nil || p.MergeCommitSHA == nil {
		return ""
	}
	return *p.MergeCommitSHA
}

// GetMerged returns the Merged field if it's non-nil, zero value otherwise.
func (p *PullRequest) GetMerged() bool {
	if p == nil || p.Merged == nil {
		return false
	}
	return *p.Merged
}

// GetMergedAt returns the MergedAt field if it's non-nil, zero value otherwise.
func (p *PullRequest) GetMergedAt() time.Time {
	if p == nil || p.MergedAt == nil {
		return time.Time{}
	}
	return *p.MergedAt
}

// GetMergedBy returns the MergedBy field.
func (p *PullRequest) GetMergedBy() *User {
	if p == nil {
		return nil
	}
	return p.MergedBy
}

// GetMilestone returns the Milestone field.
func (p *PullRequest) GetMilestone() *Milestone {
	if p == nil {
		return nil
	}
	return p.Milestone
}

// GetNodeID returns the NodeID field if it's non-nil, zero value otherwise.
func (p *PullRequest) GetNodeID() string {
	if p == nil || p.NodeID == nil {
		return ""
	}
	return *p.NodeID
}

// GetNumber returns the Number field if it's non-nil, zero value otherwise.
func (p *PullRequest) GetNumber() int {
	if p == nil || p.Number == nil {
		return 0
	}
	return *p.Number
}

// GetPatchURL returns the PatchURL field if it's non-nil, zero value otherwise.
func (p *PullRequest) GetPatchURL() string {
	if p == nil || p.PatchURL == nil {
		return ""
	}
	return *p.PatchURL
}

// GetRebaseable returns the Rebaseable field if it's non-nil, zero value otherwise.
func (p *PullRequest) GetRebaseable() bool {
	if p == nil || p.Rebaseable == nil {
		return false
	}
	return *p.Rebaseable
}

// GetReviewComments returns the ReviewComments field if it's non-nil, zero value otherwise.
func (p *PullRequest) GetReviewComments() int {
	if p == nil || p.ReviewComments == nil {
		return 0
	}
	return *p.ReviewComments
}

// GetReviewCommentsURL returns the ReviewCommentsURL field if it's non-nil, zero value otherwise.
func (p *PullRequest) GetReviewCommentsURL() string {
	if p == nil || p.ReviewCommentsURL == nil {
		return ""
	}
	return *p.ReviewCommentsURL
}

// GetReviewCommentURL returns the ReviewCommentURL field if it's non-nil, zero value otherwise.
func (p *PullRequest) GetReviewCommentURL() string {
	if p == nil || p.ReviewCommentURL == nil {
		return ""
	}
	return *p.ReviewCommentURL
}

// GetState returns the State field if it's non-nil, zero value otherwise.
func (p *PullRequest) GetState() string {
	if p == nil || p.State == nil {
		return ""
	}
	return *p.State
}

// GetStatusesURL returns the StatusesURL field if it's non-nil, zero value otherwise.
func (p *PullRequest) GetStatusesURL() string {
	if p == nil || p.StatusesURL == nil {
		return ""
	}
	return *p.StatusesURL
}

// GetTitle returns the Title field if it's non-nil, zero value otherwise.
func (p *PullRequest) GetTitle() string {
	if p == nil || p.Title == nil {
		return ""
	}
	return *p.Title
}

// GetUpdatedAt returns the UpdatedAt field if it's non-nil, zero value otherwise.
func (p *PullRequest) GetUpdatedAt() time.Time {
	if p == nil || p.UpdatedAt == nil {
		return time.Time{}
	}
	return *p.UpdatedAt
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (p *PullRequest) GetURL() string {
	if p == nil || p.URL == nil {
		return ""
	}
	return *p.URL
}

// GetUser returns the User field.
func (p *PullRequest) GetUser() *User {
	if p == nil {
		return nil
	}
	return p.User
}

// GetLabel returns the Label field if it's non-nil, zero value otherwise.
func (p *PullRequestBranch) GetLabel() string {
	if p == nil || p.Label == nil {
		return ""
	}
	return *p.Label
}

// GetRef returns the Ref field if it's non-nil, zero value otherwise.
func (p *PullRequestBranch) GetRef() string {
	if p == nil || p.Ref == nil {
		return ""
	}
	return *p.Ref
}

// GetRepo returns the Repo field.
func (p *PullRequestBranch) GetRepo() *Repository {
	if p == nil {
		return nil
	}
	return p.Repo
}

// GetSHA returns the SHA field if it's non-nil, zero value otherwise.
func (p *PullRequestBranch) GetSHA() string {
	if p == nil || p.SHA == nil {
		return ""
	}
	return *p.SHA
}

// GetUser returns the User field.
func (p *PullRequestBranch) GetUser() *User {
	if p == nil {
		return nil
	}
	return p.User
}

// GetExpectedHeadSHA returns the ExpectedHeadSHA field if it's non-nil, zero value otherwise.
func (p *PullRequestBranchUpdateOptions) GetExpectedHeadSHA() string {
	if p == nil || p.ExpectedHeadSHA == nil {
		return ""
	}
	return *p.ExpectedHeadSHA
}

// GetMessage returns the Message field if it's non-nil, zero value otherwise.
func (p *PullRequestBranchUpdateResponse) GetMessage() string {
	if p == nil || p.Message == nil {
		return ""
	}
	return *p.Message
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (p *PullRequestBranchUpdateResponse) GetURL() string {
	if p == nil || p.URL == nil {
		return ""
	}
	return *p.URL
}

// GetAuthorAssociation returns the AuthorAssociation field if it's non-nil, zero value otherwise.
func (p *PullRequestComment) GetAuthorAssociation() string {
	if p == nil || p.AuthorAssociation == nil {
		return ""
	}
	return *p.AuthorAssociation
}

// GetBody returns the Body field if it's non-nil, zero value otherwise.
func (p *PullRequestComment) GetBody() string {
	if p == nil || p.Body == nil {
		return ""
	}
	return *p.Body
}

// GetCommitID returns the CommitID field if it's non-nil, zero value otherwise.
func (p *PullRequestComment) GetCommitID() string {
	if p == nil || p.CommitID == nil {
		return ""
	}
	return *p.CommitID
}

// GetCreatedAt returns the CreatedAt field if it's non-nil, zero value otherwise.
func (p *PullRequestComment) GetCreatedAt() time.Time {
	if p == nil || p.CreatedAt == nil {
		return time.Time{}
	}
	return *p.CreatedAt
}

// GetDiffHunk returns the DiffHunk field if it's non-nil, zero value otherwise.
func (p *PullRequestComment) GetDiffHunk() string {
	if p == nil || p.DiffHunk == nil {
		return ""
	}
	return *p.DiffHunk
}

// GetHTMLURL returns the HTMLURL field if it's non-nil, zero value otherwise.
func (p *PullRequestComment) GetHTMLURL() string {
	if p == nil || p.HTMLURL == nil {
		return ""
	}
	return *p.HTMLURL
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (p *PullRequestComment) GetID() int64 {
	if p == nil || p.ID == nil {
		return 0
	}
	return *p.ID
}

// GetInReplyTo returns the InReplyTo field if it's non-nil, zero value otherwise.
func (p *PullRequestComment) GetInReplyTo() int64 {
	if p == nil || p.InReplyTo == nil {
		return 0
	}
	return *p.InReplyTo
}

// GetLine returns the Line field if it's non-nil, zero value otherwise.
func (p *PullRequestComment) GetLine() int {
	if p == nil || p.Line == nil {
		return 0
	}
	return *p.Line
}

// GetNodeID returns the NodeID field if it's non-nil, zero value otherwise.
func (p *PullRequestComment) GetNodeID() string {
	if p == nil || p.NodeID == nil {
		return ""
	}
	return *p.NodeID
}

// GetOriginalCommitID returns the OriginalCommitID field if it's non-nil, zero value otherwise.
func (p *PullRequestComment) GetOriginalCommitID() string {
	if p == nil || p.OriginalCommitID == nil {
		return ""
	}
	return *p.OriginalCommitID
}

// GetOriginalLine returns the OriginalLine field if it's non-nil, zero value otherwise.
func (p *PullRequestComment) GetOriginalLine() int {
	if p == nil || p.OriginalLine == nil {
		return 0
	}
	return *p.OriginalLine
}

// GetOriginalPosition returns the OriginalPosition field if it's non-nil, zero value otherwise.
func (p *PullRequestComment) GetOriginalPosition() int {
	if p == nil || p.OriginalPosition == nil {
		return 0
	}
	return *p.OriginalPosition
}

// GetOriginalStartLine returns the OriginalStartLine field if it's non-nil, zero value otherwise.
func (p *PullRequestComment) GetOriginalStartLine() int {
	if p == nil || p.OriginalStartLine == nil {
		return 0
	}
	return *p.OriginalStartLine
}

// GetPath returns the Path field if it's non-nil, zero value otherwise.
func (p *PullRequestComment) GetPath() string {
	if p == nil || p.Path == nil {
		return ""
	}
	return *p.Path
}

// GetPosition returns the Position field if it's non-nil, zero value otherwise.
func (p *PullRequestComment) GetPosition() int {
	if p == nil || p.Position == nil {
		return 0
	}
	return *p.Position
}

// GetPullRequestReviewID returns the PullRequestReviewID field if it's non-nil, zero value otherwise.
func (p *PullRequestComment) GetPullRequestReviewID() int64 {
	if p == nil || p.PullRequestReviewID == nil {
		return 0
	}
	return *p.PullRequestReviewID
}

// GetPullRequestURL returns the PullRequestURL field if it's non-nil, zero value otherwise.
func (p *PullRequestComment) GetPullRequestURL() string {
	if p == nil || p.PullRequestURL == nil {
		return ""
	}
	return *p.PullRequestURL
}

// GetReactions returns the Reactions field.
func (p *PullRequestComment) GetReactions() *Reactions {
	if p == nil {
		return nil
	}
	return p.Reactions
}

// GetSide returns the Side field if it's non-nil, zero value otherwise.
func (p *PullRequestComment) GetSide() string {
	if p == nil || p.Side == nil {
		return ""
	}
	return *p.Side
}

// GetStartLine returns the StartLine field if it's non-nil, zero value otherwise.
func (p *PullRequestComment) GetStartLine() int {
	if p == nil || p.StartLine == nil {
		return 0
	}
	return *p.StartLine
}

// GetStartSide returns the StartSide field if it's non-nil, zero value otherwise.
func (p *PullRequestComment) GetStartSide() string {
	if p == nil || p.StartSide == nil {
		return ""
	}
	return *p.StartSide
}

// GetUpdatedAt returns the UpdatedAt field if it's non-nil, zero value otherwise.
func (p *PullRequestComment) GetUpdatedAt() time.Time {
	if p == nil || p.UpdatedAt == nil {
		return time.Time{}
	}
	return *p.UpdatedAt
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (p *PullRequestComment) GetURL() string {
	if p == nil || p.URL == nil {
		return ""
	}
	return *p.URL
}

// GetUser returns the User field.
func (p *PullRequestComment) GetUser() *User {
	if p == nil {
		return nil
	}
	return p.User
}

// GetAction returns the Action field if it's non-nil, zero value otherwise.
func (p *PullRequestEvent) GetAction() string {
	if p == nil || p.Action == nil {
		return ""
	}
	return *p.Action
}

// GetAfter returns the After field if it's non-nil, zero value otherwise.
func (p *PullRequestEvent) GetAfter() string {
	if p == nil || p.After == nil {
		return ""
	}
	return *p.After
}

// GetAssignee returns the Assignee field.
func (p *PullRequestEvent) GetAssignee() *User {
	if p == nil {
		return nil
	}
	return p.Assignee
}

// GetBefore returns the Before field if it's non-nil, zero value otherwise.
func (p *PullRequestEvent) GetBefore() string {
	if p == nil || p.Before == nil {
		return ""
	}
	return *p.Before
}

// GetChanges returns the Changes field.
func (p *PullRequestEvent) GetChanges() *EditChange {
	if p == nil {
		return nil
	}
	return p.Changes
}

// GetInstallation returns the Installation field.
func (p *PullRequestEvent) GetInstallation() *Installation {
	if p == nil {
		return nil
	}
	return p.Installation
}

// GetLabel returns the Label field.
func (p *PullRequestEvent) GetLabel() *Label {
	if p == nil {
		return nil
	}
	return p.Label
}

// GetNumber returns the Number field if it's non-nil, zero value otherwise.
func (p *PullRequestEvent) GetNumber() int {
	if p == nil || p.Number == nil {
		return 0
	}
	return *p.Number
}

// GetOrganization returns the Organization field.
func (p *PullRequestEvent) GetOrganization() *Organization {
	if p == nil {
		return nil
	}
	return p.Organization
}

// GetPullRequest returns the PullRequest field.
func (p *PullRequestEvent) GetPullRequest() *PullRequest {
	if p == nil {
		return nil
	}
	return p.PullRequest
}

// GetRepo returns the Repo field.
func (p *PullRequestEvent) GetRepo() *Repository {
	if p == nil {
		return nil
	}
	return p.Repo
}

// GetRequestedReviewer returns the RequestedReviewer field.
func (p *PullRequestEvent) GetRequestedReviewer() *User {
	if p == nil {
		return nil
	}
	return p.RequestedReviewer
}

// GetRequestedTeam returns the RequestedTeam field.
func (p *PullRequestEvent) GetRequestedTeam() *Team {
	if p == nil {
		return nil
	}
	return p.RequestedTeam
}

// GetSender returns the Sender field.
func (p *PullRequestEvent) GetSender() *User {
	if p == nil {
		return nil
	}
	return p.Sender
}

// GetDiffURL returns the DiffURL field if it's non-nil, zero value otherwise.
func (p *PullRequestLinks) GetDiffURL() string {
	if p == nil || p.DiffURL == nil {
		return ""
	}
	return *p.DiffURL
}

// GetHTMLURL returns the HTMLURL field if it's non-nil, zero value otherwise.
func (p *PullRequestLinks) GetHTMLURL() string {
	if p == nil || p.HTMLURL == nil {
		return ""
	}
	return *p.HTMLURL
}

// GetPatchURL returns the PatchURL field if it's non-nil, zero value otherwise.
func (p *PullRequestLinks) GetPatchURL() string {
	if p == nil || p.PatchURL == nil {
		return ""
	}
	return *p.PatchURL
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (p *PullRequestLinks) GetURL() string {
	if p == nil || p.URL == nil {
		return ""
	}
	return *p.URL
}

// GetMerged returns the Merged field if it's non-nil, zero value otherwise.
func (p *PullRequestMergeResult) GetMerged() bool {
	if p == nil || p.Merged == nil {
		return false
	}
	return *p.Merged
}

// GetMessage returns the Message field if it's non-nil, zero value otherwise.
func (p *PullRequestMergeResult) GetMessage() string {
	if p == nil || p.Message == nil {
		return ""
	}
	return *p.Message
}

// GetSHA returns the SHA field if it's non-nil, zero value otherwise.
func (p *PullRequestMergeResult) GetSHA() string {
	if p == nil || p.SHA == nil {
		return ""
	}
	return *p.SHA
}

// GetAuthorAssociation returns the AuthorAssociation field if it's non-nil, zero value otherwise.
func (p *PullRequestReview) GetAuthorAssociation() string {
	if p == nil || p.AuthorAssociation == nil {
		return ""
	}
	return *p.AuthorAssociation
}

// GetBody returns the Body field if it's non-nil, zero value otherwise.
func (p *PullRequestReview) GetBody() string {
	if p == nil || p.Body == nil {
		return ""
	}
	return *p.Body
}

// GetCommitID returns the CommitID field if it's non-nil, zero value otherwise.
func (p *PullRequestReview) GetCommitID() string {
	if p == nil || p.CommitID == nil {
		return ""
	}
	return *p.CommitID
}

// GetHTMLURL returns the HTMLURL field if it's non-nil, zero value otherwise.
func (p *PullRequestReview) GetHTMLURL() string {
	if p == nil || p.HTMLURL == nil {
		return ""
	}
	return *p.HTMLURL
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (p *PullRequestReview) GetID() int64 {
	if p == nil || p.ID == nil {
		return 0
	}
	return *p.ID
}

// GetNodeID returns the NodeID field if it's non-nil, zero value otherwise.
func (p *PullRequestReview) GetNodeID() string {
	if p == nil || p.NodeID == nil {
		return ""
	}
	return *p.NodeID
}

// GetPullRequestURL returns the PullRequestURL field if it's non-nil, zero value otherwise.
func (p *PullRequestReview) GetPullRequestURL() string {
	if p == nil || p.PullRequestURL == nil {
		return ""
	}
	return *p.PullRequestURL
}

// GetState returns the State field if it's non-nil, zero value otherwise.
func (p *PullRequestReview) GetState() string {
	if p == nil || p.State == nil {
		return ""
	}
	return *p.State
}

// GetSubmittedAt returns the SubmittedAt field if it's non-nil, zero value otherwise.
func (p *PullRequestReview) GetSubmittedAt() time.Time {
	if p == nil || p.SubmittedAt == nil {
		return time.Time{}
	}
	return *p.SubmittedAt
}

// GetUser returns the User field.
func (p *PullRequestReview) GetUser() *User {
	if p == nil {
		return nil
	}
	return p.User
}

// GetAction returns the Action field if it's non-nil, zero value otherwise.
func (p *PullRequestReviewCommentEvent) GetAction() string {
	if p == nil || p.Action == nil {
		return ""
	}
	return *p.Action
}

// GetChanges returns the Changes field.
func (p *PullRequestReviewCommentEvent) GetChanges() *EditChange {
	if p == nil {
		return nil
	}
	return p.Changes
}

// GetComment returns the Comment field.
func (p *PullRequestReviewCommentEvent) GetComment() *PullRequestComment {
	if p == nil {
		return nil
	}
	return p.Comment
}

// GetInstallation returns the Installation field.
func (p *PullRequestReviewCommentEvent) GetInstallation() *Installation {
	if p == nil {
		return nil
	}
	return p.Installation
}

// GetPullRequest returns the PullRequest field.
func (p *PullRequestReviewCommentEvent) GetPullRequest() *PullRequest {
	if p == nil {
		return nil
	}
	return p.PullRequest
}

// GetRepo returns the Repo field.
func (p *PullRequestReviewCommentEvent) GetRepo() *Repository {
	if p == nil {
		return nil
	}
	return p.Repo
}

// GetSender returns the Sender field.
func (p *PullRequestReviewCommentEvent) GetSender() *User {
	if p == nil {
		return nil
	}
	return p.Sender
}

// GetMessage returns the Message field if it's non-nil, zero value otherwise.
func (p *PullRequestReviewDismissalRequest) GetMessage() string {
	if p == nil || p.Message == nil {
		return ""
	}
	return *p.Message
}

// GetAction returns the Action field if it's non-nil, zero value otherwise.
func (p *PullRequestReviewEvent) GetAction() string {
	if p == nil || p.Action == nil {
		return ""
	}
	return *p.Action
}

// GetInstallation returns the Installation field.
func (p *PullRequestReviewEvent) GetInstallation() *Installation {
	if p == nil {
		return nil
	}
	return p.Installation
}

// GetOrganization returns the Organization field.
func (p *PullRequestReviewEvent) GetOrganization() *Organization {
	if p == nil {
		return nil
	}
	return p.Organization
}

// GetPullRequest returns the PullRequest field.
func (p *PullRequestReviewEvent) GetPullRequest() *PullRequest {
	if p == nil {
		return nil
	}
	return p.PullRequest
}

// GetRepo returns the Repo field.
func (p *PullRequestReviewEvent) GetRepo() *Repository {
	if p == nil {
		return nil
	}
	return p.Repo
}

// GetReview returns the Review field.
func (p *PullRequestReviewEvent) GetReview() *PullRequestReview {
	if p == nil {
		return nil
	}
	return p.Review
}

// GetSender returns the Sender field.
func (p *PullRequestReviewEvent) GetSender() *User {
	if p == nil {
		return nil
	}
	return p.Sender
}

// GetBody returns the Body field if it's non-nil, zero value otherwise.
func (p *PullRequestReviewRequest) GetBody() string {
	if p == nil || p.Body == nil {
		return ""
	}
	return *p.Body
}

// GetCommitID returns the CommitID field if it's non-nil, zero value otherwise.
func (p *PullRequestReviewRequest) GetCommitID() string {
	if p == nil || p.CommitID == nil {
		return ""
	}
	return *p.CommitID
}

// GetEvent returns the Event field if it's non-nil, zero value otherwise.
func (p *PullRequestReviewRequest) GetEvent() string {
	if p == nil || p.Event == nil {
		return ""
	}
	return *p.Event
}

// GetNodeID returns the NodeID field if it's non-nil, zero value otherwise.
func (p *PullRequestReviewRequest) GetNodeID() string {
	if p == nil || p.NodeID == nil {
		return ""
	}
	return *p.NodeID
}

// GetDismissalRestrictions returns the DismissalRestrictions field.
func (p *PullRequestReviewsEnforcement) GetDismissalRestrictions() *DismissalRestrictions {
	if p == nil {
		return nil
	}
	return p.DismissalRestrictions
}

// GetDismissalRestrictionsRequest returns the DismissalRestrictionsRequest field.
func (p *PullRequestReviewsEnforcementRequest) GetDismissalRestrictionsRequest() *DismissalRestrictionsRequest {
	if p == nil {
		return nil
	}
	return p.DismissalRestrictionsRequest
}

// GetDismissalRestrictionsRequest returns the DismissalRestrictionsRequest field.
func (p *PullRequestReviewsEnforcementUpdate) GetDismissalRestrictionsRequest() *DismissalRestrictionsRequest {
	if p == nil {
		return nil
	}
	return p.DismissalRestrictionsRequest
}

// GetDismissStaleReviews returns the DismissStaleReviews field if it's non-nil, zero value otherwise.
func (p *PullRequestReviewsEnforcementUpdate) GetDismissStaleReviews() bool {
	if p == nil || p.DismissStaleReviews == nil {
		return false
	}
	return *p.DismissStaleReviews
}

// GetAction returns the Action field if it's non-nil, zero value otherwise.
func (p *PullRequestTargetEvent) GetAction() string {
	if p == nil || p.Action == nil {
		return ""
	}
	return *p.Action
}

// GetAfter returns the After field if it's non-nil, zero value otherwise.
func (p *PullRequestTargetEvent) GetAfter() string {
	if p == nil || p.After == nil {
		return ""
	}
	return *p.After
}

// GetAssignee returns the Assignee field.
func (p *PullRequestTargetEvent) GetAssignee() *User {
	if p == nil {
		return nil
	}
	return p.Assignee
}

// GetBefore returns the Before field if it's non-nil, zero value otherwise.
func (p *PullRequestTargetEvent) GetBefore() string {
	if p == nil || p.Before == nil {
		return ""
	}
	return *p.Before
}

// GetChanges returns the Changes field.
func (p *PullRequestTargetEvent) GetChanges() *EditChange {
	if p == nil {
		return nil
	}
	return p.Changes
}

// GetInstallation returns the Installation field.
func (p *PullRequestTargetEvent) GetInstallation() *Installation {
	if p == nil {
		return nil
	}
	return p.Installation
}

// GetLabel returns the Label field.
func (p *PullRequestTargetEvent) GetLabel() *Label {
	if p == nil {
		return nil
	}
	return p.Label
}

// GetNumber returns the Number field if it's non-nil, zero value otherwise.
func (p *PullRequestTargetEvent) GetNumber() int {
	if p == nil || p.Number == nil {
		return 0
	}
	return *p.Number
}

// GetOrganization returns the Organization field.
func (p *PullRequestTargetEvent) GetOrganization() *Organization {
	if p == nil {
		return nil
	}
	return p.Organization
}

// GetPullRequest returns the PullRequest field.
func (p *PullRequestTargetEvent) GetPullRequest() *PullRequest {
	if p == nil {
		return nil
	}
	return p.PullRequest
}

// GetRepo returns the Repo field.
func (p *PullRequestTargetEvent) GetRepo() *Repository {
	if p == nil {
		return nil
	}
	return p.Repo
}

// GetRequestedReviewer returns the RequestedReviewer field.
func (p *PullRequestTargetEvent) GetRequestedReviewer() *User {
	if p == nil {
		return nil
	}
	return p.RequestedReviewer
}

// GetRequestedTeam returns the RequestedTeam field.
func (p *PullRequestTargetEvent) GetRequestedTeam() *Team {
	if p == nil {
		return nil
	}
	return p.RequestedTeam
}

// GetSender returns the Sender field.
func (p *PullRequestTargetEvent) GetSender() *User {
	if p == nil {
		return nil
	}
	return p.Sender
}

// GetMergablePulls returns the MergablePulls field if it's non-nil, zero value otherwise.
func (p *PullStats) GetMergablePulls() int {
	if p == nil || p.MergablePulls == nil {
		return 0
	}
	return *p.MergablePulls
}

// GetMergedPulls returns the MergedPulls field if it's non-nil, zero value otherwise.
func (p *PullStats) GetMergedPulls() int {
	if p == nil || p.MergedPulls == nil {
		return 0
	}
	return *p.MergedPulls
}

// GetTotalPulls returns the TotalPulls field if it's non-nil, zero value otherwise.
func (p *PullStats) GetTotalPulls() int {
	if p == nil || p.TotalPulls == nil {
		return 0
	}
	return *p.TotalPulls
}

// GetUnmergablePulls returns the UnmergablePulls field if it's non-nil, zero value otherwise.
func (p *PullStats) GetUnmergablePulls() int {
	if p == nil || p.UnmergablePulls == nil {
		return 0
	}
	return *p.UnmergablePulls
}

// GetCommits returns the Commits field if it's non-nil, zero value otherwise.
func (p *PunchCard) GetCommits() int {
	if p == nil || p.Commits == nil {
		return 0
	}
	return *p.Commits
}

// GetDay returns the Day field if it's non-nil, zero value otherwise.
func (p *PunchCard) GetDay() int {
	if p == nil || p.Day == nil {
		return 0
	}
	return *p.Day
}

// GetHour returns the Hour field if it's non-nil, zero value otherwise.
func (p *PunchCard) GetHour() int {
	if p == nil || p.Hour == nil {
		return 0
	}
	return *p.Hour
}

// GetAfter returns the After field if it's non-nil, zero value otherwise.
func (p *PushEvent) GetAfter() string {
	if p == nil || p.After == nil {
		return ""
	}
	return *p.After
}

// GetBaseRef returns the BaseRef field if it's non-nil, zero value otherwise.
func (p *PushEvent) GetBaseRef() string {
	if p == nil || p.BaseRef == nil {
		return ""
	}
	return *p.BaseRef
}

// GetBefore returns the Before field if it's non-nil, zero value otherwise.
func (p *PushEvent) GetBefore() string {
	if p == nil || p.Before == nil {
		return ""
	}
	return *p.Before
}

// GetCompare returns the Compare field if it's non-nil, zero value otherwise.
func (p *PushEvent) GetCompare() string {
	if p == nil || p.Compare == nil {
		return ""
	}
	return *p.Compare
}

// GetCreated returns the Created field if it's non-nil, zero value otherwise.
func (p *PushEvent) GetCreated() bool {
	if p == nil || p.Created == nil {
		return false
	}
	return *p.Created
}

// GetDeleted returns the Deleted field if it's non-nil, zero value otherwise.
func (p *PushEvent) GetDeleted() bool {
	if p == nil || p.Deleted == nil {
		return false
	}
	return *p.Deleted
}

// GetDistinctSize returns the DistinctSize field if it's non-nil, zero value otherwise.
func (p *PushEvent) GetDistinctSize() int {
	if p == nil || p.DistinctSize == nil {
		return 0
	}
	return *p.DistinctSize
}

// GetForced returns the Forced field if it's non-nil, zero value otherwise.
func (p *PushEvent) GetForced() bool {
	if p == nil || p.Forced == nil {
		return false
	}
	return *p.Forced
}

// GetHead returns the Head field if it's non-nil, zero value otherwise.
func (p *PushEvent) GetHead() string {
	if p == nil || p.Head == nil {
		return ""
	}
	return *p.Head
}

// GetHeadCommit returns the HeadCommit field.
func (p *PushEvent) GetHeadCommit() *HeadCommit {
	if p == nil {
		return nil
	}
	return p.HeadCommit
}

// GetInstallation returns the Installation field.
func (p *PushEvent) GetInstallation() *Installation {
	if p == nil {
		return nil
	}
	return p.Installation
}

// GetPusher returns the Pusher field.
func (p *PushEvent) GetPusher() *User {
	if p == nil {
		return nil
	}
	return p.Pusher
}

// GetPushID returns the PushID field if it's non-nil, zero value otherwise.
func (p *PushEvent) GetPushID() int64 {
	if p == nil || p.PushID == nil {
		return 0
	}
	return *p.PushID
}

// GetRef returns the Ref field if it's non-nil, zero value otherwise.
func (p *PushEvent) GetRef() string {
	if p == nil || p.Ref == nil {
		return ""
	}
	return *p.Ref
}

// GetRepo returns the Repo field.
func (p *PushEvent) GetRepo() *PushEventRepository {
	if p == nil {
		return nil
	}
	return p.Repo
}

// GetSender returns the Sender field.
func (p *PushEvent) GetSender() *User {
	if p == nil {
		return nil
	}
	return p.Sender
}

// GetSize returns the Size field if it's non-nil, zero value otherwise.
func (p *PushEvent) GetSize() int {
	if p == nil || p.Size == nil {
		return 0
	}
	return *p.Size
}

// GetEmail returns the Email field if it's non-nil, zero value otherwise.
func (p *PushEventRepoOwner) GetEmail() string {
	if p == nil || p.Email == nil {
		return ""
	}
	return *p.Email
}

// GetName returns the Name field if it's non-nil, zero value otherwise.
func (p *PushEventRepoOwner) GetName() string {
	if p == nil || p.Name == nil {
		return ""
	}
	return *p.Name
}

// GetArchived returns the Archived field if it's non-nil, zero value otherwise.
func (p *PushEventRepository) GetArchived() bool {
	if p == nil || p.Archived == nil {
		return false
	}
	return *p.Archived
}

// GetArchiveURL returns the ArchiveURL field if it's non-nil, zero value otherwise.
func (p *PushEventRepository) GetArchiveURL() string {
	if p == nil || p.ArchiveURL == nil {
		return ""
	}
	return *p.ArchiveURL
}

// GetCloneURL returns the CloneURL field if it's non-nil, zero value otherwise.
func (p *PushEventRepository) GetCloneURL() string {
	if p == nil || p.CloneURL == nil {
		return ""
	}
	return *p.CloneURL
}

// GetCreatedAt returns the CreatedAt field if it's non-nil, zero value otherwise.
func (p *PushEventRepository) GetCreatedAt() Timestamp {
	if p == nil || p.CreatedAt == nil {
		return Timestamp{}
	}
	return *p.CreatedAt
}

// GetDefaultBranch returns the DefaultBranch field if it's non-nil, zero value otherwise.
func (p *PushEventRepository) GetDefaultBranch() string {
	if p == nil || p.DefaultBranch == nil {
		return ""
	}
	return *p.DefaultBranch
}

// GetDescription returns the Description field if it's non-nil, zero value otherwise.
func (p *PushEventRepository) GetDescription() string {
	if p == nil || p.Description == nil {
		return ""
	}
	return *p.Description
}

// GetDisabled returns the Disabled field if it's non-nil, zero value otherwise.
func (p *PushEventRepository) GetDisabled() bool {
	if p == nil || p.Disabled == nil {
		return false
	}
	return *p.Disabled
}

// GetFork returns the Fork field if it's non-nil, zero value otherwise.
func (p *PushEventRepository) GetFork() bool {
	if p == nil || p.Fork == nil {
		return false
	}
	return *p.Fork
}

// GetForksCount returns the ForksCount field if it's non-nil, zero value otherwise.
func (p *PushEventRepository) GetForksCount() int {
	if p == nil || p.ForksCount == nil {
		return 0
	}
	return *p.ForksCount
}

// GetFullName returns the FullName field if it's non-nil, zero value otherwise.
func (p *PushEventRepository) GetFullName() string {
	if p == nil || p.FullName == nil {
		return ""
	}
	return *p.FullName
}

// GetGitURL returns the GitURL field if it's non-nil, zero value otherwise.
func (p *PushEventRepository) GetGitURL() string {
	if p == nil || p.GitURL == nil {
		return ""
	}
	return *p.GitURL
}

// GetHasDownloads returns the HasDownloads field if it's non-nil, zero value otherwise.
func (p *PushEventRepository) GetHasDownloads() bool {
	if p == nil || p.HasDownloads == nil {
		return false
	}
	return *p.HasDownloads
}

// GetHasIssues returns the HasIssues field if it's non-nil, zero value otherwise.
func (p *PushEventRepository) GetHasIssues() bool {
	if p == nil || p.HasIssues == nil {
		return false
	}
	return *p.HasIssues
}

// GetHasPages returns the HasPages field if it's non-nil, zero value otherwise.
func (p *PushEventRepository) GetHasPages() bool {
	if p == nil || p.HasPages == nil {
		return false
	}
	return *p.HasPages
}

// GetHasWiki returns the HasWiki field if it's non-nil, zero value otherwise.
func (p *PushEventRepository) GetHasWiki() bool {
	if p == nil || p.HasWiki == nil {
		return false
	}
	return *p.HasWiki
}

// GetHomepage returns the Homepage field if it's non-nil, zero value otherwise.
func (p *PushEventRepository) GetHomepage() string {
	if p == nil || p.Homepage == nil {
		return ""
	}
	return *p.Homepage
}

// GetHTMLURL returns the HTMLURL field if it's non-nil, zero value otherwise.
func (p *PushEventRepository) GetHTMLURL() string {
	if p == nil || p.HTMLURL == nil {
		return ""
	}
	return *p.HTMLURL
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (p *PushEventRepository) GetID() int64 {
	if p == nil || p.ID == nil {
		return 0
	}
	return *p.ID
}

// GetLanguage returns the Language field if it's non-nil, zero value otherwise.
func (p *PushEventRepository) GetLanguage() string {
	if p == nil || p.Language == nil {
		return ""
	}
	return *p.Language
}

// GetMasterBranch returns the MasterBranch field if it's non-nil, zero value otherwise.
func (p *PushEventRepository) GetMasterBranch() string {
	if p == nil || p.MasterBranch == nil {
		return ""
	}
	return *p.MasterBranch
}

// GetName returns the Name field if it's non-nil, zero value otherwise.
func (p *PushEventRepository) GetName() string {
	if p == nil || p.Name == nil {
		return ""
	}
	return *p.Name
}

// GetNodeID returns the NodeID field if it's non-nil, zero value otherwise.
func (p *PushEventRepository) GetNodeID() string {
	if p == nil || p.NodeID == nil {
		return ""
	}
	return *p.NodeID
}

// GetOpenIssuesCount returns the OpenIssuesCount field if it's non-nil, zero value otherwise.
func (p *PushEventRepository) GetOpenIssuesCount() int {
	if p == nil || p.OpenIssuesCount == nil {
		return 0
	}
	return *p.OpenIssuesCount
}

// GetOrganization returns the Organization field if it's non-nil, zero value otherwise.
func (p *PushEventRepository) GetOrganization() string {
	if p == nil || p.Organization == nil {
		return ""
	}
	return *p.Organization
}

// GetOwner returns the Owner field.
func (p *PushEventRepository) GetOwner() *User {
	if p == nil {
		return nil
	}
	return p.Owner
}

// GetPrivate returns the Private field if it's non-nil, zero value otherwise.
func (p *PushEventRepository) GetPrivate() bool {
	if p == nil || p.Private == nil {
		return false
	}
	return *p.Private
}

// GetPullsURL returns the PullsURL field if it's non-nil, zero value otherwise.
func (p *PushEventRepository) GetPullsURL() string {
	if p == nil || p.PullsURL == nil {
		return ""
	}
	return *p.PullsURL
}

// GetPushedAt returns the PushedAt field if it's non-nil, zero value otherwise.
func (p *PushEventRepository) GetPushedAt() Timestamp {
	if p == nil || p.PushedAt == nil {
		return Timestamp{}
	}
	return *p.PushedAt
}

// GetSize returns the Size field if it's non-nil, zero value otherwise.
func (p *PushEventRepository) GetSize() int {
	if p == nil || p.Size == nil {
		return 0
	}
	return *p.Size
}

// GetSSHURL returns the SSHURL field if it's non-nil, zero value otherwise.
func (p *PushEventRepository) GetSSHURL() string {
	if p == nil || p.SSHURL == nil {
		return ""
	}
	return *p.SSHURL
}

// GetStargazersCount returns the StargazersCount field if it's non-nil, zero value otherwise.
func (p *PushEventRepository) GetStargazersCount() int {
	if p == nil || p.StargazersCount == nil {
		return 0
	}
	return *p.StargazersCount
}

// GetStatusesURL returns the StatusesURL field if it's non-nil, zero value otherwise.
func (p *PushEventRepository) GetStatusesURL() string {
	if p == nil || p.StatusesURL == nil {
		return ""
	}
	return *p.StatusesURL
}

// GetSVNURL returns the SVNURL field if it's non-nil, zero value otherwise.
func (p *PushEventRepository) GetSVNURL() string {
	if p == nil || p.SVNURL == nil {
		return ""
	}
	return *p.SVNURL
}

// GetUpdatedAt returns the UpdatedAt field if it's non-nil, zero value otherwise.
func (p *PushEventRepository) GetUpdatedAt() Timestamp {
	if p == nil || p.UpdatedAt == nil {
		return Timestamp{}
	}
	return *p.UpdatedAt
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (p *PushEventRepository) GetURL() string {
	if p == nil || p.URL == nil {
		return ""
	}
	return *p.URL
}

// GetWatchersCount returns the WatchersCount field if it's non-nil, zero value otherwise.
func (p *PushEventRepository) GetWatchersCount() int {
	if p == nil || p.WatchersCount == nil {
		return 0
	}
	return *p.WatchersCount
}

// GetCore returns the Core field.
func (r *RateLimits) GetCore() *Rate {
	if r == nil {
		return nil
	}
	return r.Core
}

// GetSearch returns the Search field.
func (r *RateLimits) GetSearch() *Rate {
	if r == nil {
		return nil
	}
	return r.Search
}

// GetContent returns the Content field if it's non-nil, zero value otherwise.
func (r *Reaction) GetContent() string {
	if r == nil || r.Content == nil {
		return ""
	}
	return *r.Content
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (r *Reaction) GetID() int64 {
	if r == nil || r.ID == nil {
		return 0
	}
	return *r.ID
}

// GetNodeID returns the NodeID field if it's non-nil, zero value otherwise.
func (r *Reaction) GetNodeID() string {
	if r == nil || r.NodeID == nil {
		return ""
	}
	return *r.NodeID
}

// GetUser returns the User field.
func (r *Reaction) GetUser() *User {
	if r == nil {
		return nil
	}
	return r.User
}

// GetConfused returns the Confused field if it's non-nil, zero value otherwise.
func (r *Reactions) GetConfused() int {
	if r == nil || r.Confused == nil {
		return 0
	}
	return *r.Confused
}

// GetEyes returns the Eyes field if it's non-nil, zero value otherwise.
func (r *Reactions) GetEyes() int {
	if r == nil || r.Eyes == nil {
		return 0
	}
	return *r.Eyes
}

// GetHeart returns the Heart field if it's non-nil, zero value otherwise.
func (r *Reactions) GetHeart() int {
	if r == nil || r.Heart == nil {
		return 0
	}
	return *r.Heart
}

// GetHooray returns the Hooray field if it's non-nil, zero value otherwise.
func (r *Reactions) GetHooray() int {
	if r == nil || r.Hooray == nil {
		return 0
	}
	return *r.Hooray
}

// GetLaugh returns the Laugh field if it's non-nil, zero value otherwise.
func (r *Reactions) GetLaugh() int {
	if r == nil || r.Laugh == nil {
		return 0
	}
	return *r.Laugh
}

// GetMinusOne returns the MinusOne field if it's non-nil, zero value otherwise.
func (r *Reactions) GetMinusOne() int {
	if r == nil || r.MinusOne == nil {
		return 0
	}
	return *r.MinusOne
}

// GetPlusOne returns the PlusOne field if it's non-nil, zero value otherwise.
func (r *Reactions) GetPlusOne() int {
	if r == nil || r.PlusOne == nil {
		return 0
	}
	return *r.PlusOne
}

// GetRocket returns the Rocket field if it's non-nil, zero value otherwise.
func (r *Reactions) GetRocket() int {
	if r == nil || r.Rocket == nil {
		return 0
	}
	return *r.Rocket
}

// GetTotalCount returns the TotalCount field if it's non-nil, zero value otherwise.
func (r *Reactions) GetTotalCount() int {
	if r == nil || r.TotalCount == nil {
		return 0
	}
	return *r.TotalCount
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (r *Reactions) GetURL() string {
	if r == nil || r.URL == nil {
		return ""
	}
	return *r.URL
}

// GetNodeID returns the NodeID field if it's non-nil, zero value otherwise.
func (r *Reference) GetNodeID() string {
	if r == nil || r.NodeID == nil {
		return ""
	}
	return *r.NodeID
}

// GetObject returns the Object field.
func (r *Reference) GetObject() *GitObject {
	if r == nil {
		return nil
	}
	return r.Object
}

// GetRef returns the Ref field if it's non-nil, zero value otherwise.
func (r *Reference) GetRef() string {
	if r == nil || r.Ref == nil {
		return ""
	}
	return *r.Ref
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (r *Reference) GetURL() string {
	if r == nil || r.URL == nil {
		return ""
	}
	return *r.URL
}

// GetExpiresAt returns the ExpiresAt field if it's non-nil, zero value otherwise.
func (r *RegistrationToken) GetExpiresAt() Timestamp {
	if r == nil || r.ExpiresAt == nil {
		return Timestamp{}
	}
	return *r.ExpiresAt
}

// GetToken returns the Token field if it's non-nil, zero value otherwise.
func (r *RegistrationToken) GetToken() string {
	if r == nil || r.Token == nil {
		return ""
	}
	return *r.Token
}

// GetBrowserDownloadURL returns the BrowserDownloadURL field if it's non-nil, zero value otherwise.
func (r *ReleaseAsset) GetBrowserDownloadURL() string {
	if r == nil || r.BrowserDownloadURL == nil {
		return ""
	}
	return *r.BrowserDownloadURL
}

// GetContentType returns the ContentType field if it's non-nil, zero value otherwise.
func (r *ReleaseAsset) GetContentType() string {
	if r == nil || r.ContentType == nil {
		return ""
	}
	return *r.ContentType
}

// GetCreatedAt returns the CreatedAt field if it's non-nil, zero value otherwise.
func (r *ReleaseAsset) GetCreatedAt() Timestamp {
	if r == nil || r.CreatedAt == nil {
		return Timestamp{}
	}
	return *r.CreatedAt
}

// GetDownloadCount returns the DownloadCount field if it's non-nil, zero value otherwise.
func (r *ReleaseAsset) GetDownloadCount() int {
	if r == nil || r.DownloadCount == nil {
		return 0
	}
	return *r.DownloadCount
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (r *ReleaseAsset) GetID() int64 {
	if r == nil || r.ID == nil {
		return 0
	}
	return *r.ID
}

// GetLabel returns the Label field if it's non-nil, zero value otherwise.
func (r *ReleaseAsset) GetLabel() string {
	if r == nil || r.Label == nil {
		return ""
	}
	return *r.Label
}

// GetName returns the Name field if it's non-nil, zero value otherwise.
func (r *ReleaseAsset) GetName() string {
	if r == nil || r.Name == nil {
		return ""
	}
	return *r.Name
}

// GetNodeID returns the NodeID field if it's non-nil, zero value otherwise.
func (r *ReleaseAsset) GetNodeID() string {
	if r == nil || r.NodeID == nil {
		return ""
	}
	return *r.NodeID
}

// GetSize returns the Size field if it's non-nil, zero value otherwise.
func (r *ReleaseAsset) GetSize() int {
	if r == nil || r.Size == nil {
		return 0
	}
	return *r.Size
}

// GetState returns the State field if it's non-nil, zero value otherwise.
func (r *ReleaseAsset) GetState() string {
	if r == nil || r.State == nil {
		return ""
	}
	return *r.State
}

// GetUpdatedAt returns the UpdatedAt field if it's non-nil, zero value otherwise.
func (r *ReleaseAsset) GetUpdatedAt() Timestamp {
	if r == nil || r.UpdatedAt == nil {
		return Timestamp{}
	}
	return *r.UpdatedAt
}

// GetUploader returns the Uploader field.
func (r *ReleaseAsset) GetUploader() *User {
	if r == nil {
		return nil
	}
	return r.Uploader
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (r *ReleaseAsset) GetURL() string {
	if r == nil || r.URL == nil {
		return ""
	}
	return *r.URL
}

// GetAction returns the Action field if it's non-nil, zero value otherwise.
func (r *ReleaseEvent) GetAction() string {
	if r == nil || r.Action == nil {
		return ""
	}
	return *r.Action
}

// GetInstallation returns the Installation field.
func (r *ReleaseEvent) GetInstallation() *Installation {
	if r == nil {
		return nil
	}
	return r.Installation
}

// GetRelease returns the Release field.
func (r *ReleaseEvent) GetRelease() *RepositoryRelease {
	if r == nil {
		return nil
	}
	return r.Release
}

// GetRepo returns the Repo field.
func (r *ReleaseEvent) GetRepo() *Repository {
	if r == nil {
		return nil
	}
	return r.Repo
}

// GetSender returns the Sender field.
func (r *ReleaseEvent) GetSender() *User {
	if r == nil {
		return nil
	}
	return r.Sender
}

// GetExpiresAt returns the ExpiresAt field if it's non-nil, zero value otherwise.
func (r *RemoveToken) GetExpiresAt() Timestamp {
	if r == nil || r.ExpiresAt == nil {
		return Timestamp{}
	}
	return *r.ExpiresAt
}

// GetToken returns the Token field if it's non-nil, zero value otherwise.
func (r *RemoveToken) GetToken() string {
	if r == nil || r.Token == nil {
		return ""
	}
	return *r.Token
}

// GetFrom returns the From field if it's non-nil, zero value otherwise.
func (r *Rename) GetFrom() string {
	if r == nil || r.From == nil {
		return ""
	}
	return *r.From
}

// GetTo returns the To field if it's non-nil, zero value otherwise.
func (r *Rename) GetTo() string {
	if r == nil || r.To == nil {
		return ""
	}
	return *r.To
}

// GetMessage returns the Message field if it's non-nil, zero value otherwise.
func (r *RenameOrgResponse) GetMessage() string {
	if r == nil || r.Message == nil {
		return ""
	}
	return *r.Message
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (r *RenameOrgResponse) GetURL() string {
	if r == nil || r.URL == nil {
		return ""
	}
	return *r.URL
}

// GetIncompleteResults returns the IncompleteResults field if it's non-nil, zero value otherwise.
func (r *RepositoriesSearchResult) GetIncompleteResults() bool {
	if r == nil || r.IncompleteResults == nil {
		return false
	}
	return *r.IncompleteResults
}

// GetTotal returns the Total field if it's non-nil, zero value otherwise.
func (r *RepositoriesSearchResult) GetTotal() int {
	if r == nil || r.Total == nil {
		return 0
	}
	return *r.Total
}

// GetAllowMergeCommit returns the AllowMergeCommit field if it's non-nil, zero value otherwise.
func (r *Repository) GetAllowMergeCommit() bool {
	if r == nil || r.AllowMergeCommit == nil {
		return false
	}
	return *r.AllowMergeCommit
}

// GetAllowRebaseMerge returns the AllowRebaseMerge field if it's non-nil, zero value otherwise.
func (r *Repository) GetAllowRebaseMerge() bool {
	if r == nil || r.AllowRebaseMerge == nil {
		return false
	}
	return *r.AllowRebaseMerge
}

// GetAllowSquashMerge returns the AllowSquashMerge field if it's non-nil, zero value otherwise.
func (r *Repository) GetAllowSquashMerge() bool {
	if r == nil || r.AllowSquashMerge == nil {
		return false
	}
	return *r.AllowSquashMerge
}

// GetArchived returns the Archived field if it's non-nil, zero value otherwise.
func (r *Repository) GetArchived() bool {
	if r == nil || r.Archived == nil {
		return false
	}
	return *r.Archived
}

// GetArchiveURL returns the ArchiveURL field if it's non-nil, zero value otherwise.
func (r *Repository) GetArchiveURL() string {
	if r == nil || r.ArchiveURL == nil {
		return ""
	}
	return *r.ArchiveURL
}

// GetAssigneesURL returns the AssigneesURL field if it's non-nil, zero value otherwise.
func (r *Repository) GetAssigneesURL() string {
	if r == nil || r.AssigneesURL == nil {
		return ""
	}
	return *r.AssigneesURL
}

// GetAutoInit returns the AutoInit field if it's non-nil, zero value otherwise.
func (r *Repository) GetAutoInit() bool {
	if r == nil || r.AutoInit == nil {
		return false
	}
	return *r.AutoInit
}

// GetBlobsURL returns the BlobsURL field if it's non-nil, zero value otherwise.
func (r *Repository) GetBlobsURL() string {
	if r == nil || r.BlobsURL == nil {
		return ""
	}
	return *r.BlobsURL
}

// GetBranchesURL returns the BranchesURL field if it's non-nil, zero value otherwise.
func (r *Repository) GetBranchesURL() string {
	if r == nil || r.BranchesURL == nil {
		return ""
	}
	return *r.BranchesURL
}

// GetCloneURL returns the CloneURL field if it's non-nil, zero value otherwise.
func (r *Repository) GetCloneURL() string {
	if r == nil || r.CloneURL == nil {
		return ""
	}
	return *r.CloneURL
}

// GetCodeOfConduct returns the CodeOfConduct field.
func (r *Repository) GetCodeOfConduct() *CodeOfConduct {
	if r == nil {
		return nil
	}
	return r.CodeOfConduct
}

// GetCollaboratorsURL returns the CollaboratorsURL field if it's non-nil, zero value otherwise.
func (r *Repository) GetCollaboratorsURL() string {
	if r == nil || r.CollaboratorsURL == nil {
		return ""
	}
	return *r.CollaboratorsURL
}

// GetCommentsURL returns the CommentsURL field if it's non-nil, zero value otherwise.
func (r *Repository) GetCommentsURL() string {
	if r == nil || r.CommentsURL == nil {
		return ""
	}
	return *r.CommentsURL
}

// GetCommitsURL returns the CommitsURL field if it's non-nil, zero value otherwise.
func (r *Repository) GetCommitsURL() string {
	if r == nil || r.CommitsURL == nil {
		return ""
	}
	return *r.CommitsURL
}

// GetCompareURL returns the CompareURL field if it's non-nil, zero value otherwise.
func (r *Repository) GetCompareURL() string {
	if r == nil || r.CompareURL == nil {
		return ""
	}
	return *r.CompareURL
}

// GetContentsURL returns the ContentsURL field if it's non-nil, zero value otherwise.
func (r *Repository) GetContentsURL() string {
	if r == nil || r.ContentsURL == nil {
		return ""
	}
	return *r.ContentsURL
}

// GetContributorsURL returns the ContributorsURL field if it's non-nil, zero value otherwise.
func (r *Repository) GetContributorsURL() string {
	if r == nil || r.ContributorsURL == nil {
		return ""
	}
	return *r.ContributorsURL
}

// GetCreatedAt returns the CreatedAt field if it's non-nil, zero value otherwise.
func (r *Repository) GetCreatedAt() Timestamp {
	if r == nil || r.CreatedAt == nil {
		return Timestamp{}
	}
	return *r.CreatedAt
}

// GetDefaultBranch returns the DefaultBranch field if it's non-nil, zero value otherwise.
func (r *Repository) GetDefaultBranch() string {
	if r == nil || r.DefaultBranch == nil {
		return ""
	}
	return *r.DefaultBranch
}

// GetDeleteBranchOnMerge returns the DeleteBranchOnMerge field if it's non-nil, zero value otherwise.
func (r *Repository) GetDeleteBranchOnMerge() bool {
	if r == nil || r.DeleteBranchOnMerge == nil {
		return false
	}
	return *r.DeleteBranchOnMerge
}

// GetDeploymentsURL returns the DeploymentsURL field if it's non-nil, zero value otherwise.
func (r *Repository) GetDeploymentsURL() string {
	if r == nil || r.DeploymentsURL == nil {
		return ""
	}
	return *r.DeploymentsURL
}

// GetDescription returns the Description field if it's non-nil, zero value otherwise.
func (r *Repository) GetDescription() string {
	if r == nil || r.Description == nil {
		return ""
	}
	return *r.Description
}

// GetDisabled returns the Disabled field if it's non-nil, zero value otherwise.
func (r *Repository) GetDisabled() bool {
	if r == nil || r.Disabled == nil {
		return false
	}
	return *r.Disabled
}

// GetDownloadsURL returns the DownloadsURL field if it's non-nil, zero value otherwise.
func (r *Repository) GetDownloadsURL() string {
	if r == nil || r.DownloadsURL == nil {
		return ""
	}
	return *r.DownloadsURL
}

// GetEventsURL returns the EventsURL field if it's non-nil, zero value otherwise.
func (r *Repository) GetEventsURL() string {
	if r == nil || r.EventsURL == nil {
		return ""
	}
	return *r.EventsURL
}

// GetFork returns the Fork field if it's non-nil, zero value otherwise.
func (r *Repository) GetFork() bool {
	if r == nil || r.Fork == nil {
		return false
	}
	return *r.Fork
}

// GetForksCount returns the ForksCount field if it's non-nil, zero value otherwise.
func (r *Repository) GetForksCount() int {
	if r == nil || r.ForksCount == nil {
		return 0
	}
	return *r.ForksCount
}

// GetForksURL returns the ForksURL field if it's non-nil, zero value otherwise.
func (r *Repository) GetForksURL() string {
	if r == nil || r.ForksURL == nil {
		return ""
	}
	return *r.ForksURL
}

// GetFullName returns the FullName field if it's non-nil, zero value otherwise.
func (r *Repository) GetFullName() string {
	if r == nil || r.FullName == nil {
		return ""
	}
	return *r.FullName
}

// GetGitCommitsURL returns the GitCommitsURL field if it's non-nil, zero value otherwise.
func (r *Repository) GetGitCommitsURL() string {
	if r == nil || r.GitCommitsURL == nil {
		return ""
	}
	return *r.GitCommitsURL
}

// GetGitignoreTemplate returns the GitignoreTemplate field if it's non-nil, zero value otherwise.
func (r *Repository) GetGitignoreTemplate() string {
	if r == nil || r.GitignoreTemplate == nil {
		return ""
	}
	return *r.GitignoreTemplate
}

// GetGitRefsURL returns the GitRefsURL field if it's non-nil, zero value otherwise.
func (r *Repository) GetGitRefsURL() string {
	if r == nil || r.GitRefsURL == nil {
		return ""
	}
	return *r.GitRefsURL
}

// GetGitTagsURL returns the GitTagsURL field if it's non-nil, zero value otherwise.
func (r *Repository) GetGitTagsURL() string {
	if r == nil || r.GitTagsURL == nil {
		return ""
	}
	return *r.GitTagsURL
}

// GetGitURL returns the GitURL field if it's non-nil, zero value otherwise.
func (r *Repository) GetGitURL() string {
	if r == nil || r.GitURL == nil {
		return ""
	}
	return *r.GitURL
}

// GetHasDownloads returns the HasDownloads field if it's non-nil, zero value otherwise.
func (r *Repository) GetHasDownloads() bool {
	if r == nil || r.HasDownloads == nil {
		return false
	}
	return *r.HasDownloads
}

// GetHasIssues returns the HasIssues field if it's non-nil, zero value otherwise.
func (r *Repository) GetHasIssues() bool {
	if r == nil || r.HasIssues == nil {
		return false
	}
	return *r.HasIssues
}

// GetHasPages returns the HasPages field if it's non-nil, zero value otherwise.
func (r *Repository) GetHasPages() bool {
	if r == nil || r.HasPages == nil {
		return false
	}
	return *r.HasPages
}

// GetHasProjects returns the HasProjects field if it's non-nil, zero value otherwise.
func (r *Repository) GetHasProjects() bool {
	if r == nil || r.HasProjects == nil {
		return false
	}
	return *r.HasProjects
}

// GetHasWiki returns the HasWiki field if it's non-nil, zero value otherwise.
func (r *Repository) GetHasWiki() bool {
	if r == nil || r.HasWiki == nil {
		return false
	}
	return *r.HasWiki
}

// GetHomepage returns the Homepage field if it's non-nil, zero value otherwise.
func (r *Repository) GetHomepage() string {
	if r == nil || r.Homepage == nil {
		return ""
	}
	return *r.Homepage
}

// GetHooksURL returns the HooksURL field if it's non-nil, zero value otherwise.
func (r *Repository) GetHooksURL() string {
	if r == nil || r.HooksURL == nil {
		return ""
	}
	return *r.HooksURL
}

// GetHTMLURL returns the HTMLURL field if it's non-nil, zero value otherwise.
func (r *Repository) GetHTMLURL() string {
	if r == nil || r.HTMLURL == nil {
		return ""
	}
	return *r.HTMLURL
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (r *Repository) GetID() int64 {
	if r == nil || r.ID == nil {
		return 0
	}
	return *r.ID
}

// GetIssueCommentURL returns the IssueCommentURL field if it's non-nil, zero value otherwise.
func (r *Repository) GetIssueCommentURL() string {
	if r == nil || r.IssueCommentURL == nil {
		return ""
	}
	return *r.IssueCommentURL
}

// GetIssueEventsURL returns the IssueEventsURL field if it's non-nil, zero value otherwise.
func (r *Repository) GetIssueEventsURL() string {
	if r == nil || r.IssueEventsURL == nil {
		return ""
	}
	return *r.IssueEventsURL
}

// GetIssuesURL returns the IssuesURL field if it's non-nil, zero value otherwise.
func (r *Repository) GetIssuesURL() string {
	if r == nil || r.IssuesURL == nil {
		return ""
	}
	return *r.IssuesURL
}

// GetIsTemplate returns the IsTemplate field if it's non-nil, zero value otherwise.
func (r *Repository) GetIsTemplate() bool {
	if r == nil || r.IsTemplate == nil {
		return false
	}
	return *r.IsTemplate
}

// GetKeysURL returns the KeysURL field if it's non-nil, zero value otherwise.
func (r *Repository) GetKeysURL() string {
	if r == nil || r.KeysURL == nil {
		return ""
	}
	return *r.KeysURL
}

// GetLabelsURL returns the LabelsURL field if it's non-nil, zero value otherwise.
func (r *Repository) GetLabelsURL() string {
	if r == nil || r.LabelsURL == nil {
		return ""
	}
	return *r.LabelsURL
}

// GetLanguage returns the Language field if it's non-nil, zero value otherwise.
func (r *Repository) GetLanguage() string {
	if r == nil || r.Language == nil {
		return ""
	}
	return *r.Language
}

// GetLanguagesURL returns the LanguagesURL field if it's non-nil, zero value otherwise.
func (r *Repository) GetLanguagesURL() string {
	if r == nil || r.LanguagesURL == nil {
		return ""
	}
	return *r.LanguagesURL
}

// GetLicense returns the License field.
func (r *Repository) GetLicense() *License {
	if r == nil {
		return nil
	}
	return r.License
}

// GetLicenseTemplate returns the LicenseTemplate field if it's non-nil, zero value otherwise.
func (r *Repository) GetLicenseTemplate() string {
	if r == nil || r.LicenseTemplate == nil {
		return ""
	}
	return *r.LicenseTemplate
}

// GetMasterBranch returns the MasterBranch field if it's non-nil, zero value otherwise.
func (r *Repository) GetMasterBranch() string {
	if r == nil || r.MasterBranch == nil {
		return ""
	}
	return *r.MasterBranch
}

// GetMergesURL returns the MergesURL field if it's non-nil, zero value otherwise.
func (r *Repository) GetMergesURL() string {
	if r == nil || r.MergesURL == nil {
		return ""
	}
	return *r.MergesURL
}

// GetMilestonesURL returns the MilestonesURL field if it's non-nil, zero value otherwise.
func (r *Repository) GetMilestonesURL() string {
	if r == nil || r.MilestonesURL == nil {
		return ""
	}
	return *r.MilestonesURL
}

// GetMirrorURL returns the MirrorURL field if it's non-nil, zero value otherwise.
func (r *Repository) GetMirrorURL() string {
	if r == nil || r.MirrorURL == nil {
		return ""
	}
	return *r.MirrorURL
}

// GetName returns the Name field if it's non-nil, zero value otherwise.
func (r *Repository) GetName() string {
	if r == nil || r.Name == nil {
		return ""
	}
	return *r.Name
}

// GetNetworkCount returns the NetworkCount field if it's non-nil, zero value otherwise.
func (r *Repository) GetNetworkCount() int {
	if r == nil || r.NetworkCount == nil {
		return 0
	}
	return *r.NetworkCount
}

// GetNodeID returns the NodeID field if it's non-nil, zero value otherwise.
func (r *Repository) GetNodeID() string {
	if r == nil || r.NodeID == nil {
		return ""
	}
	return *r.NodeID
}

// GetNotificationsURL returns the NotificationsURL field if it's non-nil, zero value otherwise.
func (r *Repository) GetNotificationsURL() string {
	if r == nil || r.NotificationsURL == nil {
		return ""
	}
	return *r.NotificationsURL
}

// GetOpenIssuesCount returns the OpenIssuesCount field if it's non-nil, zero value otherwise.
func (r *Repository) GetOpenIssuesCount() int {
	if r == nil || r.OpenIssuesCount == nil {
		return 0
	}
	return *r.OpenIssuesCount
}

// GetOrganization returns the Organization field.
func (r *Repository) GetOrganization() *Organization {
	if r == nil {
		return nil
	}
	return r.Organization
}

// GetOwner returns the Owner field.
func (r *Repository) GetOwner() *User {
	if r == nil {
		return nil
	}
	return r.Owner
}

// GetParent returns the Parent field.
func (r *Repository) GetParent() *Repository {
	if r == nil {
		return nil
	}
	return r.Parent
}

// GetPermissions returns the Permissions map if it's non-nil, an empty map otherwise.
func (r *Repository) GetPermissions() map[string]bool {
	if r == nil || r.Permissions == nil {
		return map[string]bool{}
	}
	return r.Permissions
}

// GetPrivate returns the Private field if it's non-nil, zero value otherwise.
func (r *Repository) GetPrivate() bool {
	if r == nil || r.Private == nil {
		return false
	}
	return *r.Private
}

// GetPullsURL returns the PullsURL field if it's non-nil, zero value otherwise.
func (r *Repository) GetPullsURL() string {
	if r == nil || r.PullsURL == nil {
		return ""
	}
	return *r.PullsURL
}

// GetPushedAt returns the PushedAt field if it's non-nil, zero value otherwise.
func (r *Repository) GetPushedAt() Timestamp {
	if r == nil || r.PushedAt == nil {
		return Timestamp{}
	}
	return *r.PushedAt
}

// GetReleasesURL returns the ReleasesURL field if it's non-nil, zero value otherwise.
func (r *Repository) GetReleasesURL() string {
	if r == nil || r.ReleasesURL == nil {
		return ""
	}
	return *r.ReleasesURL
}

// GetSize returns the Size field if it's non-nil, zero value otherwise.
func (r *Repository) GetSize() int {
	if r == nil || r.Size == nil {
		return 0
	}
	return *r.Size
}

// GetSource returns the Source field.
func (r *Repository) GetSource() *Repository {
	if r == nil {
		return nil
	}
	return r.Source
}

// GetSSHURL returns the SSHURL field if it's non-nil, zero value otherwise.
func (r *Repository) GetSSHURL() string {
	if r == nil || r.SSHURL == nil {
		return ""
	}
	return *r.SSHURL
}

// GetStargazersCount returns the StargazersCount field if it's non-nil, zero value otherwise.
func (r *Repository) GetStargazersCount() int {
	if r == nil || r.StargazersCount == nil {
		return 0
	}
	return *r.StargazersCount
}

// GetStargazersURL returns the StargazersURL field if it's non-nil, zero value otherwise.
func (r *Repository) GetStargazersURL() string {
	if r == nil || r.StargazersURL == nil {
		return ""
	}
	return *r.StargazersURL
}

// GetStatusesURL returns the StatusesURL field if it's non-nil, zero value otherwise.
func (r *Repository) GetStatusesURL() string {
	if r == nil || r.StatusesURL == nil {
		return ""
	}
	return *r.StatusesURL
}

// GetSubscribersCount returns the SubscribersCount field if it's non-nil, zero value otherwise.
func (r *Repository) GetSubscribersCount() int {
	if r == nil || r.SubscribersCount == nil {
		return 0
	}
	return *r.SubscribersCount
}

// GetSubscribersURL returns the SubscribersURL field if it's non-nil, zero value otherwise.
func (r *Repository) GetSubscribersURL() string {
	if r == nil || r.SubscribersURL == nil {
		return ""
	}
	return *r.SubscribersURL
}

// GetSubscriptionURL returns the SubscriptionURL field if it's non-nil, zero value otherwise.
func (r *Repository) GetSubscriptionURL() string {
	if r == nil || r.SubscriptionURL == nil {
		return ""
	}
	return *r.SubscriptionURL
}

// GetSVNURL returns the SVNURL field if it's non-nil, zero value otherwise.
func (r *Repository) GetSVNURL() string {
	if r == nil || r.SVNURL == nil {
		return ""
	}
	return *r.SVNURL
}

// GetTagsURL returns the TagsURL field if it's non-nil, zero value otherwise.
func (r *Repository) GetTagsURL() string {
	if r == nil || r.TagsURL == nil {
		return ""
	}
	return *r.TagsURL
}

// GetTeamID returns the TeamID field if it's non-nil, zero value otherwise.
func (r *Repository) GetTeamID() int64 {
	if r == nil || r.TeamID == nil {
		return 0
	}
	return *r.TeamID
}

// GetTeamsURL returns the TeamsURL field if it's non-nil, zero value otherwise.
func (r *Repository) GetTeamsURL() string {
	if r == nil || r.TeamsURL == nil {
		return ""
	}
	return *r.TeamsURL
}

// GetTemplateRepository returns the TemplateRepository field.
func (r *Repository) GetTemplateRepository() *Repository {
	if r == nil {
		return nil
	}
	return r.TemplateRepository
}

// GetTreesURL returns the TreesURL field if it's non-nil, zero value otherwise.
func (r *Repository) GetTreesURL() string {
	if r == nil || r.TreesURL == nil {
		return ""
	}
	return *r.TreesURL
}

// GetUpdatedAt returns the UpdatedAt field if it's non-nil, zero value otherwise.
func (r *Repository) GetUpdatedAt() Timestamp {
	if r == nil || r.UpdatedAt == nil {
		return Timestamp{}
	}
	return *r.UpdatedAt
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (r *Repository) GetURL() string {
	if r == nil || r.URL == nil {
		return ""
	}
	return *r.URL
}

// GetVisibility returns the Visibility field if it's non-nil, zero value otherwise.
func (r *Repository) GetVisibility() string {
	if r == nil || r.Visibility == nil {
		return ""
	}
	return *r.Visibility
}

// GetWatchersCount returns the WatchersCount field if it's non-nil, zero value otherwise.
func (r *Repository) GetWatchersCount() int {
	if r == nil || r.WatchersCount == nil {
		return 0
	}
	return *r.WatchersCount
}

// GetBody returns the Body field if it's non-nil, zero value otherwise.
func (r *RepositoryComment) GetBody() string {
	if r == nil || r.Body == nil {
		return ""
	}
	return *r.Body
}

// GetCommitID returns the CommitID field if it's non-nil, zero value otherwise.
func (r *RepositoryComment) GetCommitID() string {
	if r == nil || r.CommitID == nil {
		return ""
	}
	return *r.CommitID
}

// GetCreatedAt returns the CreatedAt field if it's non-nil, zero value otherwise.
func (r *RepositoryComment) GetCreatedAt() time.Time {
	if r == nil || r.CreatedAt == nil {
		return time.Time{}
	}
	return *r.CreatedAt
}

// GetHTMLURL returns the HTMLURL field if it's non-nil, zero value otherwise.
func (r *RepositoryComment) GetHTMLURL() string {
	if r == nil || r.HTMLURL == nil {
		return ""
	}
	return *r.HTMLURL
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (r *RepositoryComment) GetID() int64 {
	if r == nil || r.ID == nil {
		return 0
	}
	return *r.ID
}

// GetNodeID returns the NodeID field if it's non-nil, zero value otherwise.
func (r *RepositoryComment) GetNodeID() string {
	if r == nil || r.NodeID == nil {
		return ""
	}
	return *r.NodeID
}

// GetPath returns the Path field if it's non-nil, zero value otherwise.
func (r *RepositoryComment) GetPath() string {
	if r == nil || r.Path == nil {
		return ""
	}
	return *r.Path
}

// GetPosition returns the Position field if it's non-nil, zero value otherwise.
func (r *RepositoryComment) GetPosition() int {
	if r == nil || r.Position == nil {
		return 0
	}
	return *r.Position
}

// GetReactions returns the Reactions field.
func (r *RepositoryComment) GetReactions() *Reactions {
	if r == nil {
		return nil
	}
	return r.Reactions
}

// GetUpdatedAt returns the UpdatedAt field if it's non-nil, zero value otherwise.
func (r *RepositoryComment) GetUpdatedAt() time.Time {
	if r == nil || r.UpdatedAt == nil {
		return time.Time{}
	}
	return *r.UpdatedAt
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (r *RepositoryComment) GetURL() string {
	if r == nil || r.URL == nil {
		return ""
	}
	return *r.URL
}

// GetUser returns the User field.
func (r *RepositoryComment) GetUser() *User {
	if r == nil {
		return nil
	}
	return r.User
}

// GetAuthor returns the Author field.
func (r *RepositoryCommit) GetAuthor() *User {
	if r == nil {
		return nil
	}
	return r.Author
}

// GetCommentsURL returns the CommentsURL field if it's non-nil, zero value otherwise.
func (r *RepositoryCommit) GetCommentsURL() string {
	if r == nil || r.CommentsURL == nil {
		return ""
	}
	return *r.CommentsURL
}

// GetCommit returns the Commit field.
func (r *RepositoryCommit) GetCommit() *Commit {
	if r == nil {
		return nil
	}
	return r.Commit
}

// GetCommitter returns the Committer field.
func (r *RepositoryCommit) GetCommitter() *User {
	if r == nil {
		return nil
	}
	return r.Committer
}

// GetHTMLURL returns the HTMLURL field if it's non-nil, zero value otherwise.
func (r *RepositoryCommit) GetHTMLURL() string {
	if r == nil || r.HTMLURL == nil {
		return ""
	}
	return *r.HTMLURL
}

// GetNodeID returns the NodeID field if it's non-nil, zero value otherwise.
func (r *RepositoryCommit) GetNodeID() string {
	if r == nil || r.NodeID == nil {
		return ""
	}
	return *r.NodeID
}

// GetSHA returns the SHA field if it's non-nil, zero value otherwise.
func (r *RepositoryCommit) GetSHA() string {
	if r == nil || r.SHA == nil {
		return ""
	}
	return *r.SHA
}

// GetStats returns the Stats field.
func (r *RepositoryCommit) GetStats() *CommitStats {
	if r == nil {
		return nil
	}
	return r.Stats
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (r *RepositoryCommit) GetURL() string {
	if r == nil || r.URL == nil {
		return ""
	}
	return *r.URL
}

// GetDownloadURL returns the DownloadURL field if it's non-nil, zero value otherwise.
func (r *RepositoryContent) GetDownloadURL() string {
	if r == nil || r.DownloadURL == nil {
		return ""
	}
	return *r.DownloadURL
}

// GetEncoding returns the Encoding field if it's non-nil, zero value otherwise.
func (r *RepositoryContent) GetEncoding() string {
	if r == nil || r.Encoding == nil {
		return ""
	}
	return *r.Encoding
}

// GetGitURL returns the GitURL field if it's non-nil, zero value otherwise.
func (r *RepositoryContent) GetGitURL() string {
	if r == nil || r.GitURL == nil {
		return ""
	}
	return *r.GitURL
}

// GetHTMLURL returns the HTMLURL field if it's non-nil, zero value otherwise.
func (r *RepositoryContent) GetHTMLURL() string {
	if r == nil || r.HTMLURL == nil {
		return ""
	}
	return *r.HTMLURL
}

// GetName returns the Name field if it's non-nil, zero value otherwise.
func (r *RepositoryContent) GetName() string {
	if r == nil || r.Name == nil {
		return ""
	}
	return *r.Name
}

// GetPath returns the Path field if it's non-nil, zero value otherwise.
func (r *RepositoryContent) GetPath() string {
	if r == nil || r.Path == nil {
		return ""
	}
	return *r.Path
}

// GetSHA returns the SHA field if it's non-nil, zero value otherwise.
func (r *RepositoryContent) GetSHA() string {
	if r == nil || r.SHA == nil {
		return ""
	}
	return *r.SHA
}

// GetSize returns the Size field if it's non-nil, zero value otherwise.
func (r *RepositoryContent) GetSize() int {
	if r == nil || r.Size == nil {
		return 0
	}
	return *r.Size
}

// GetTarget returns the Target field if it's non-nil, zero value otherwise.
func (r *RepositoryContent) GetTarget() string {
	if r == nil || r.Target == nil {
		return ""
	}
	return *r.Target
}

// GetType returns the Type field if it's non-nil, zero value otherwise.
func (r *RepositoryContent) GetType() string {
	if r == nil || r.Type == nil {
		return ""
	}
	return *r.Type
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (r *RepositoryContent) GetURL() string {
	if r == nil || r.URL == nil {
		return ""
	}
	return *r.URL
}

// GetAuthor returns the Author field.
func (r *RepositoryContentFileOptions) GetAuthor() *CommitAuthor {
	if r == nil {
		return nil
	}
	return r.Author
}

// GetBranch returns the Branch field if it's non-nil, zero value otherwise.
func (r *RepositoryContentFileOptions) GetBranch() string {
	if r == nil || r.Branch == nil {
		return ""
	}
	return *r.Branch
}

// GetCommitter returns the Committer field.
func (r *RepositoryContentFileOptions) GetCommitter() *CommitAuthor {
	if r == nil {
		return nil
	}
	return r.Committer
}

// GetMessage returns the Message field if it's non-nil, zero value otherwise.
func (r *RepositoryContentFileOptions) GetMessage() string {
	if r == nil || r.Message == nil {
		return ""
	}
	return *r.Message
}

// GetSHA returns the SHA field if it's non-nil, zero value otherwise.
func (r *RepositoryContentFileOptions) GetSHA() string {
	if r == nil || r.SHA == nil {
		return ""
	}
	return *r.SHA
}

// GetContent returns the Content field.
func (r *RepositoryContentResponse) GetContent() *RepositoryContent {
	if r == nil {
		return nil
	}
	return r.Content
}

// GetAction returns the Action field if it's non-nil, zero value otherwise.
func (r *RepositoryDispatchEvent) GetAction() string {
	if r == nil || r.Action == nil {
		return ""
	}
	return *r.Action
}

// GetBranch returns the Branch field if it's non-nil, zero value otherwise.
func (r *RepositoryDispatchEvent) GetBranch() string {
	if r == nil || r.Branch == nil {
		return ""
	}
	return *r.Branch
}

// GetInstallation returns the Installation field.
func (r *RepositoryDispatchEvent) GetInstallation() *Installation {
	if r == nil {
		return nil
	}
	return r.Installation
}

// GetOrg returns the Org field.
func (r *RepositoryDispatchEvent) GetOrg() *Organization {
	if r == nil {
		return nil
	}
	return r.Org
}

// GetRepo returns the Repo field.
func (r *RepositoryDispatchEvent) GetRepo() *Repository {
	if r == nil {
		return nil
	}
	return r.Repo
}

// GetSender returns the Sender field.
func (r *RepositoryDispatchEvent) GetSender() *User {
	if r == nil {
		return nil
	}
	return r.Sender
}

// GetAction returns the Action field if it's non-nil, zero value otherwise.
func (r *RepositoryEvent) GetAction() string {
	if r == nil || r.Action == nil {
		return ""
	}
	return *r.Action
}

// GetInstallation returns the Installation field.
func (r *RepositoryEvent) GetInstallation() *Installation {
	if r == nil {
		return nil
	}
	return r.Installation
}

// GetOrg returns the Org field.
func (r *RepositoryEvent) GetOrg() *Organization {
	if r == nil {
		return nil
	}
	return r.Org
}

// GetRepo returns the Repo field.
func (r *RepositoryEvent) GetRepo() *Repository {
	if r == nil {
		return nil
	}
	return r.Repo
}

// GetSender returns the Sender field.
func (r *RepositoryEvent) GetSender() *User {
	if r == nil {
		return nil
	}
	return r.Sender
}

// GetCreatedAt returns the CreatedAt field if it's non-nil, zero value otherwise.
func (r *RepositoryInvitation) GetCreatedAt() Timestamp {
	if r == nil || r.CreatedAt == nil {
		return Timestamp{}
	}
	return *r.CreatedAt
}

// GetHTMLURL returns the HTMLURL field if it's non-nil, zero value otherwise.
func (r *RepositoryInvitation) GetHTMLURL() string {
	if r == nil || r.HTMLURL == nil {
		return ""
	}
	return *r.HTMLURL
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (r *RepositoryInvitation) GetID() int64 {
	if r == nil || r.ID == nil {
		return 0
	}
	return *r.ID
}

// GetInvitee returns the Invitee field.
func (r *RepositoryInvitation) GetInvitee() *User {
	if r == nil {
		return nil
	}
	return r.Invitee
}

// GetInviter returns the Inviter field.
func (r *RepositoryInvitation) GetInviter() *User {
	if r == nil {
		return nil
	}
	return r.Inviter
}

// GetPermissions returns the Permissions field if it's non-nil, zero value otherwise.
func (r *RepositoryInvitation) GetPermissions() string {
	if r == nil || r.Permissions == nil {
		return ""
	}
	return *r.Permissions
}

// GetRepo returns the Repo field.
func (r *RepositoryInvitation) GetRepo() *Repository {
	if r == nil {
		return nil
	}
	return r.Repo
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (r *RepositoryInvitation) GetURL() string {
	if r == nil || r.URL == nil {
		return ""
	}
	return *r.URL
}

// GetContent returns the Content field if it's non-nil, zero value otherwise.
func (r *RepositoryLicense) GetContent() string {
	if r == nil || r.Content == nil {
		return ""
	}
	return *r.Content
}

// GetDownloadURL returns the DownloadURL field if it's non-nil, zero value otherwise.
func (r *RepositoryLicense) GetDownloadURL() string {
	if r == nil || r.DownloadURL == nil {
		return ""
	}
	return *r.DownloadURL
}

// GetEncoding returns the Encoding field if it's non-nil, zero value otherwise.
func (r *RepositoryLicense) GetEncoding() string {
	if r == nil || r.Encoding == nil {
		return ""
	}
	return *r.Encoding
}

// GetGitURL returns the GitURL field if it's non-nil, zero value otherwise.
func (r *RepositoryLicense) GetGitURL() string {
	if r == nil || r.GitURL == nil {
		return ""
	}
	return *r.GitURL
}

// GetHTMLURL returns the HTMLURL field if it's non-nil, zero value otherwise.
func (r *RepositoryLicense) GetHTMLURL() string {
	if r == nil || r.HTMLURL == nil {
		return ""
	}
	return *r.HTMLURL
}

// GetLicense returns the License field.
func (r *RepositoryLicense) GetLicense() *License {
	if r == nil {
		return nil
	}
	return r.License
}

// GetName returns the Name field if it's non-nil, zero value otherwise.
func (r *RepositoryLicense) GetName() string {
	if r == nil || r.Name == nil {
		return ""
	}
	return *r.Name
}

// GetPath returns the Path field if it's non-nil, zero value otherwise.
func (r *RepositoryLicense) GetPath() string {
	if r == nil || r.Path == nil {
		return ""
	}
	return *r.Path
}

// GetSHA returns the SHA field if it's non-nil, zero value otherwise.
func (r *RepositoryLicense) GetSHA() string {
	if r == nil || r.SHA == nil {
		return ""
	}
	return *r.SHA
}

// GetSize returns the Size field if it's non-nil, zero value otherwise.
func (r *RepositoryLicense) GetSize() int {
	if r == nil || r.Size == nil {
		return 0
	}
	return *r.Size
}

// GetType returns the Type field if it's non-nil, zero value otherwise.
func (r *RepositoryLicense) GetType() string {
	if r == nil || r.Type == nil {
		return ""
	}
	return *r.Type
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (r *RepositoryLicense) GetURL() string {
	if r == nil || r.URL == nil {
		return ""
	}
	return *r.URL
}

// GetBase returns the Base field if it's non-nil, zero value otherwise.
func (r *RepositoryMergeRequest) GetBase() string {
	if r == nil || r.Base == nil {
		return ""
	}
	return *r.Base
}

// GetCommitMessage returns the CommitMessage field if it's non-nil, zero value otherwise.
func (r *RepositoryMergeRequest) GetCommitMessage() string {
	if r == nil || r.CommitMessage == nil {
		return ""
	}
	return *r.CommitMessage
}

// GetHead returns the Head field if it's non-nil, zero value otherwise.
func (r *RepositoryMergeRequest) GetHead() string {
	if r == nil || r.Head == nil {
		return ""
	}
	return *r.Head
}

// GetPermission returns the Permission field if it's non-nil, zero value otherwise.
func (r *RepositoryPermissionLevel) GetPermission() string {
	if r == nil || r.Permission == nil {
		return ""
	}
	return *r.Permission
}

// GetUser returns the User field.
func (r *RepositoryPermissionLevel) GetUser() *User {
	if r == nil {
		return nil
	}
	return r.User
}

// GetAssetsURL returns the AssetsURL field if it's non-nil, zero value otherwise.
func (r *RepositoryRelease) GetAssetsURL() string {
	if r == nil || r.AssetsURL == nil {
		return ""
	}
	return *r.AssetsURL
}

// GetAuthor returns the Author field.
func (r *RepositoryRelease) GetAuthor() *User {
	if r == nil {
		return nil
	}
	return r.Author
}

// GetBody returns the Body field if it's non-nil, zero value otherwise.
func (r *RepositoryRelease) GetBody() string {
	if r == nil || r.Body == nil {
		return ""
	}
	return *r.Body
}

// GetCreatedAt returns the CreatedAt field if it's non-nil, zero value otherwise.
func (r *RepositoryRelease) GetCreatedAt() Timestamp {
	if r == nil || r.CreatedAt == nil {
		return Timestamp{}
	}
	return *r.CreatedAt
}

// GetDiscussionCategoryName returns the DiscussionCategoryName field if it's non-nil, zero value otherwise.
func (r *RepositoryRelease) GetDiscussionCategoryName() string {
	if r == nil || r.DiscussionCategoryName == nil {
		return ""
	}
	return *r.DiscussionCategoryName
}

// GetDraft returns the Draft field if it's non-nil, zero value otherwise.
func (r *RepositoryRelease) GetDraft() bool {
	if r == nil || r.Draft == nil {
		return false
	}
	return *r.Draft
}

// GetHTMLURL returns the HTMLURL field if it's non-nil, zero value otherwise.
func (r *RepositoryRelease) GetHTMLURL() string {
	if r == nil || r.HTMLURL == nil {
		return ""
	}
	return *r.HTMLURL
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (r *RepositoryRelease) GetID() int64 {
	if r == nil || r.ID == nil {
		return 0
	}
	return *r.ID
}

// GetName returns the Name field if it's non-nil, zero value otherwise.
func (r *RepositoryRelease) GetName() string {
	if r == nil || r.Name == nil {
		return ""
	}
	return *r.Name
}

// GetNodeID returns the NodeID field if it's non-nil, zero value otherwise.
func (r *RepositoryRelease) GetNodeID() string {
	if r == nil || r.NodeID == nil {
		return ""
	}
	return *r.NodeID
}

// GetPrerelease returns the Prerelease field if it's non-nil, zero value otherwise.
func (r *RepositoryRelease) GetPrerelease() bool {
	if r == nil || r.Prerelease == nil {
		return false
	}
	return *r.Prerelease
}

// GetPublishedAt returns the PublishedAt field if it's non-nil, zero value otherwise.
func (r *RepositoryRelease) GetPublishedAt() Timestamp {
	if r == nil || r.PublishedAt == nil {
		return Timestamp{}
	}
	return *r.PublishedAt
}

// GetTagName returns the TagName field if it's non-nil, zero value otherwise.
func (r *RepositoryRelease) GetTagName() string {
	if r == nil || r.TagName == nil {
		return ""
	}
	return *r.TagName
}

// GetTarballURL returns the TarballURL field if it's non-nil, zero value otherwise.
func (r *RepositoryRelease) GetTarballURL() string {
	if r == nil || r.TarballURL == nil {
		return ""
	}
	return *r.TarballURL
}

// GetTargetCommitish returns the TargetCommitish field if it's non-nil, zero value otherwise.
func (r *RepositoryRelease) GetTargetCommitish() string {
	if r == nil || r.TargetCommitish == nil {
		return ""
	}
	return *r.TargetCommitish
}

// GetUploadURL returns the UploadURL field if it's non-nil, zero value otherwise.
func (r *RepositoryRelease) GetUploadURL() string {
	if r == nil || r.UploadURL == nil {
		return ""
	}
	return *r.UploadURL
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (r *RepositoryRelease) GetURL() string {
	if r == nil || r.URL == nil {
		return ""
	}
	return *r.URL
}

// GetZipballURL returns the ZipballURL field if it's non-nil, zero value otherwise.
func (r *RepositoryRelease) GetZipballURL() string {
	if r == nil || r.ZipballURL == nil {
		return ""
	}
	return *r.ZipballURL
}

// GetCommit returns the Commit field.
func (r *RepositoryTag) GetCommit() *Commit {
	if r == nil {
		return nil
	}
	return r.Commit
}

// GetName returns the Name field if it's non-nil, zero value otherwise.
func (r *RepositoryTag) GetName() string {
	if r == nil || r.Name == nil {
		return ""
	}
	return *r.Name
}

// GetTarballURL returns the TarballURL field if it's non-nil, zero value otherwise.
func (r *RepositoryTag) GetTarballURL() string {
	if r == nil || r.TarballURL == nil {
		return ""
	}
	return *r.TarballURL
}

// GetZipballURL returns the ZipballURL field if it's non-nil, zero value otherwise.
func (r *RepositoryTag) GetZipballURL() string {
	if r == nil || r.ZipballURL == nil {
		return ""
	}
	return *r.ZipballURL
}

// GetAction returns the Action field if it's non-nil, zero value otherwise.
func (r *RepositoryVulnerabilityAlertEvent) GetAction() string {
	if r == nil || r.Action == nil {
		return ""
	}
	return *r.Action
}

// GetRepository returns the Repository field.
func (r *RepositoryVulnerabilityAlertEvent) GetRepository() *Repository {
	if r == nil {
		return nil
	}
	return r.Repository
}

// GetForkRepos returns the ForkRepos field if it's non-nil, zero value otherwise.
func (r *RepoStats) GetForkRepos() int {
	if r == nil || r.ForkRepos == nil {
		return 0
	}
	return *r.ForkRepos
}

// GetOrgRepos returns the OrgRepos field if it's non-nil, zero value otherwise.
func (r *RepoStats) GetOrgRepos() int {
	if r == nil || r.OrgRepos == nil {
		return 0
	}
	return *r.OrgRepos
}

// GetRootRepos returns the RootRepos field if it's non-nil, zero value otherwise.
func (r *RepoStats) GetRootRepos() int {
	if r == nil || r.RootRepos == nil {
		return 0
	}
	return *r.RootRepos
}

// GetTotalPushes returns the TotalPushes field if it's non-nil, zero value otherwise.
func (r *RepoStats) GetTotalPushes() int {
	if r == nil || r.TotalPushes == nil {
		return 0
	}
	return *r.TotalPushes
}

// GetTotalRepos returns the TotalRepos field if it's non-nil, zero value otherwise.
func (r *RepoStats) GetTotalRepos() int {
	if r == nil || r.TotalRepos == nil {
		return 0
	}
	return *r.TotalRepos
}

// GetTotalWikis returns the TotalWikis field if it's non-nil, zero value otherwise.
func (r *RepoStats) GetTotalWikis() int {
	if r == nil || r.TotalWikis == nil {
		return 0
	}
	return *r.TotalWikis
}

// GetContext returns the Context field if it's non-nil, zero value otherwise.
func (r *RepoStatus) GetContext() string {
	if r == nil || r.Context == nil {
		return ""
	}
	return *r.Context
}

// GetCreatedAt returns the CreatedAt field if it's non-nil, zero value otherwise.
func (r *RepoStatus) GetCreatedAt() time.Time {
	if r == nil || r.CreatedAt == nil {
		return time.Time{}
	}
	return *r.CreatedAt
}

// GetCreator returns the Creator field.
func (r *RepoStatus) GetCreator() *User {
	if r == nil {
		return nil
	}
	return r.Creator
}

// GetDescription returns the Description field if it's non-nil, zero value otherwise.
func (r *RepoStatus) GetDescription() string {
	if r == nil || r.Description == nil {
		return ""
	}
	return *r.Description
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (r *RepoStatus) GetID() int64 {
	if r == nil || r.ID == nil {
		return 0
	}
	return *r.ID
}

// GetNodeID returns the NodeID field if it's non-nil, zero value otherwise.
func (r *RepoStatus) GetNodeID() string {
	if r == nil || r.NodeID == nil {
		return ""
	}
	return *r.NodeID
}

// GetState returns the State field if it's non-nil, zero value otherwise.
func (r *RepoStatus) GetState() string {
	if r == nil || r.State == nil {
		return ""
	}
	return *r.State
}

// GetTargetURL returns the TargetURL field if it's non-nil, zero value otherwise.
func (r *RepoStatus) GetTargetURL() string {
	if r == nil || r.TargetURL == nil {
		return ""
	}
	return *r.TargetURL
}

// GetUpdatedAt returns the UpdatedAt field if it's non-nil, zero value otherwise.
func (r *RepoStatus) GetUpdatedAt() time.Time {
	if r == nil || r.UpdatedAt == nil {
		return time.Time{}
	}
	return *r.UpdatedAt
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (r *RepoStatus) GetURL() string {
	if r == nil || r.URL == nil {
		return ""
	}
	return *r.URL
}

// GetType returns the Type field if it's non-nil, zero value otherwise.
func (r *RequiredReviewer) GetType() string {
	if r == nil || r.Type == nil {
		return ""
	}
	return *r.Type
}

// GetStrict returns the Strict field if it's non-nil, zero value otherwise.
func (r *RequiredStatusChecksRequest) GetStrict() bool {
	if r == nil || r.Strict == nil {
		return false
	}
	return *r.Strict
}

// GetNodeID returns the NodeID field if it's non-nil, zero value otherwise.
func (r *ReviewersRequest) GetNodeID() string {
	if r == nil || r.NodeID == nil {
		return ""
	}
	return *r.NodeID
}

// GetBusy returns the Busy field if it's non-nil, zero value otherwise.
func (r *Runner) GetBusy() bool {
	if r == nil || r.Busy == nil {
		return false
	}
	return *r.Busy
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (r *Runner) GetID() int64 {
	if r == nil || r.ID == nil {
		return 0
	}
	return *r.ID
}

// GetName returns the Name field if it's non-nil, zero value otherwise.
func (r *Runner) GetName() string {
	if r == nil || r.Name == nil {
		return ""
	}
	return *r.Name
}

// GetOS returns the OS field if it's non-nil, zero value otherwise.
func (r *Runner) GetOS() string {
	if r == nil || r.OS == nil {
		return ""
	}
	return *r.OS
}

// GetStatus returns the Status field if it's non-nil, zero value otherwise.
func (r *Runner) GetStatus() string {
	if r == nil || r.Status == nil {
		return ""
	}
	return *r.Status
}

// GetArchitecture returns the Architecture field if it's non-nil, zero value otherwise.
func (r *RunnerApplicationDownload) GetArchitecture() string {
	if r == nil || r.Architecture == nil {
		return ""
	}
	return *r.Architecture
}

// GetDownloadURL returns the DownloadURL field if it's non-nil, zero value otherwise.
func (r *RunnerApplicationDownload) GetDownloadURL() string {
	if r == nil || r.DownloadURL == nil {
		return ""
	}
	return *r.DownloadURL
}

// GetFilename returns the Filename field if it's non-nil, zero value otherwise.
func (r *RunnerApplicationDownload) GetFilename() string {
	if r == nil || r.Filename == nil {
		return ""
	}
	return *r.Filename
}

// GetOS returns the OS field if it's non-nil, zero value otherwise.
func (r *RunnerApplicationDownload) GetOS() string {
	if r == nil || r.OS == nil {
		return ""
	}
	return *r.OS
}

// GetSHA256Checksum returns the SHA256Checksum field if it's non-nil, zero value otherwise.
func (r *RunnerApplicationDownload) GetSHA256Checksum() string {
	if r == nil || r.SHA256Checksum == nil {
		return ""
	}
	return *r.SHA256Checksum
}

// GetTempDownloadToken returns the TempDownloadToken field if it's non-nil, zero value otherwise.
func (r *RunnerApplicationDownload) GetTempDownloadToken() string {
	if r == nil || r.TempDownloadToken == nil {
		return ""
	}
	return *r.TempDownloadToken
}

// GetAllowsPublicRepositories returns the AllowsPublicRepositories field if it's non-nil, zero value otherwise.
func (r *RunnerGroup) GetAllowsPublicRepositories() bool {
	if r == nil || r.AllowsPublicRepositories == nil {
		return false
	}
	return *r.AllowsPublicRepositories
}

// GetDefault returns the Default field if it's non-nil, zero value otherwise.
func (r *RunnerGroup) GetDefault() bool {
	if r == nil || r.Default == nil {
		return false
	}
	return *r.Default
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (r *RunnerGroup) GetID() int64 {
	if r == nil || r.ID == nil {
		return 0
	}
	return *r.ID
}

// GetInherited returns the Inherited field if it's non-nil, zero value otherwise.
func (r *RunnerGroup) GetInherited() bool {
	if r == nil || r.Inherited == nil {
		return false
	}
	return *r.Inherited
}

// GetName returns the Name field if it's non-nil, zero value otherwise.
func (r *RunnerGroup) GetName() string {
	if r == nil || r.Name == nil {
		return ""
	}
	return *r.Name
}

// GetRunnersURL returns the RunnersURL field if it's non-nil, zero value otherwise.
func (r *RunnerGroup) GetRunnersURL() string {
	if r == nil || r.RunnersURL == nil {
		return ""
	}
	return *r.RunnersURL
}

// GetSelectedRepositoriesURL returns the SelectedRepositoriesURL field if it's non-nil, zero value otherwise.
func (r *RunnerGroup) GetSelectedRepositoriesURL() string {
	if r == nil || r.SelectedRepositoriesURL == nil {
		return ""
	}
	return *r.SelectedRepositoriesURL
}

// GetVisibility returns the Visibility field if it's non-nil, zero value otherwise.
func (r *RunnerGroup) GetVisibility() string {
	if r == nil || r.Visibility == nil {
		return ""
	}
	return *r.Visibility
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (r *RunnerLabels) GetID() int64 {
	if r == nil || r.ID == nil {
		return 0
	}
	return *r.ID
}

// GetName returns the Name field if it's non-nil, zero value otherwise.
func (r *RunnerLabels) GetName() string {
	if r == nil || r.Name == nil {
		return ""
	}
	return *r.Name
}

// GetType returns the Type field if it's non-nil, zero value otherwise.
func (r *RunnerLabels) GetType() string {
	if r == nil || r.Type == nil {
		return ""
	}
	return *r.Type
}

// GetTotalCount returns the TotalCount field if it's non-nil, zero value otherwise.
func (s *SelectedReposList) GetTotalCount() int {
	if s == nil || s.TotalCount == nil {
		return 0
	}
	return *s.TotalCount
}

// GetName returns the Name field if it's non-nil, zero value otherwise.
func (s *ServiceHook) GetName() string {
	if s == nil || s.Name == nil {
		return ""
	}
	return *s.Name
}

// GetEnabled returns the Enabled field if it's non-nil, zero value otherwise.
func (s *SignaturesProtectedBranch) GetEnabled() bool {
	if s == nil || s.Enabled == nil {
		return false
	}
	return *s.Enabled
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (s *SignaturesProtectedBranch) GetURL() string {
	if s == nil || s.URL == nil {
		return ""
	}
	return *s.URL
}

// GetPayload returns the Payload field if it's non-nil, zero value otherwise.
func (s *SignatureVerification) GetPayload() string {
	if s == nil || s.Payload == nil {
		return ""
	}
	return *s.Payload
}

// GetReason returns the Reason field if it's non-nil, zero value otherwise.
func (s *SignatureVerification) GetReason() string {
	if s == nil || s.Reason == nil {
		return ""
	}
	return *s.Reason
}

// GetSignature returns the Signature field if it's non-nil, zero value otherwise.
func (s *SignatureVerification) GetSignature() string {
	if s == nil || s.Signature == nil {
		return ""
	}
	return *s.Signature
}

// GetVerified returns the Verified field if it's non-nil, zero value otherwise.
func (s *SignatureVerification) GetVerified() bool {
	if s == nil || s.Verified == nil {
		return false
	}
	return *s.Verified
}

// GetActor returns the Actor field.
func (s *Source) GetActor() *User {
	if s == nil {
		return nil
	}
	return s.Actor
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (s *Source) GetID() int64 {
	if s == nil || s.ID == nil {
		return 0
	}
	return *s.ID
}

// GetIssue returns the Issue field.
func (s *Source) GetIssue() *Issue {
	if s == nil {
		return nil
	}
	return s.Issue
}

// GetType returns the Type field if it's non-nil, zero value otherwise.
func (s *Source) GetType() string {
	if s == nil || s.Type == nil {
		return ""
	}
	return *s.Type
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (s *Source) GetURL() string {
	if s == nil || s.URL == nil {
		return ""
	}
	return *s.URL
}

// GetEmail returns the Email field if it's non-nil, zero value otherwise.
func (s *SourceImportAuthor) GetEmail() string {
	if s == nil || s.Email == nil {
		return ""
	}
	return *s.Email
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (s *SourceImportAuthor) GetID() int64 {
	if s == nil || s.ID == nil {
		return 0
	}
	return *s.ID
}

// GetImportURL returns the ImportURL field if it's non-nil, zero value otherwise.
func (s *SourceImportAuthor) GetImportURL() string {
	if s == nil || s.ImportURL == nil {
		return ""
	}
	return *s.ImportURL
}

// GetName returns the Name field if it's non-nil, zero value otherwise.
func (s *SourceImportAuthor) GetName() string {
	if s == nil || s.Name == nil {
		return ""
	}
	return *s.Name
}

// GetRemoteID returns the RemoteID field if it's non-nil, zero value otherwise.
func (s *SourceImportAuthor) GetRemoteID() string {
	if s == nil || s.RemoteID == nil {
		return ""
	}
	return *s.RemoteID
}

// GetRemoteName returns the RemoteName field if it's non-nil, zero value otherwise.
func (s *SourceImportAuthor) GetRemoteName() string {
	if s == nil || s.RemoteName == nil {
		return ""
	}
	return *s.RemoteName
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (s *SourceImportAuthor) GetURL() string {
	if s == nil || s.URL == nil {
		return ""
	}
	return *s.URL
}

// GetAction returns the Action field if it's non-nil, zero value otherwise.
func (s *StarEvent) GetAction() string {
	if s == nil || s.Action == nil {
		return ""
	}
	return *s.Action
}

// GetOrg returns the Org field.
func (s *StarEvent) GetOrg() *Organization {
	if s == nil {
		return nil
	}
	return s.Org
}

// GetRepo returns the Repo field.
func (s *StarEvent) GetRepo() *Repository {
	if s == nil {
		return nil
	}
	return s.Repo
}

// GetSender returns the Sender field.
func (s *StarEvent) GetSender() *User {
	if s == nil {
		return nil
	}
	return s.Sender
}

// GetStarredAt returns the StarredAt field if it's non-nil, zero value otherwise.
func (s *StarEvent) GetStarredAt() Timestamp {
	if s == nil || s.StarredAt == nil {
		return Timestamp{}
	}
	return *s.StarredAt
}

// GetStarredAt returns the StarredAt field if it's non-nil, zero value otherwise.
func (s *Stargazer) GetStarredAt() Timestamp {
	if s == nil || s.StarredAt == nil {
		return Timestamp{}
	}
	return *s.StarredAt
}

// GetUser returns the User field.
func (s *Stargazer) GetUser() *User {
	if s == nil {
		return nil
	}
	return s.User
}

// GetRepository returns the Repository field.
func (s *StarredRepository) GetRepository() *Repository {
	if s == nil {
		return nil
	}
	return s.Repository
}

// GetStarredAt returns the StarredAt field if it's non-nil, zero value otherwise.
func (s *StarredRepository) GetStarredAt() Timestamp {
	if s == nil || s.StarredAt == nil {
		return Timestamp{}
	}
	return *s.StarredAt
}

// GetCommit returns the Commit field.
func (s *StatusEvent) GetCommit() *RepositoryCommit {
	if s == nil {
		return nil
	}
	return s.Commit
}

// GetContext returns the Context field if it's non-nil, zero value otherwise.
func (s *StatusEvent) GetContext() string {
	if s == nil || s.Context == nil {
		return ""
	}
	return *s.Context
}

// GetCreatedAt returns the CreatedAt field if it's non-nil, zero value otherwise.
func (s *StatusEvent) GetCreatedAt() Timestamp {
	if s == nil || s.CreatedAt == nil {
		return Timestamp{}
	}
	return *s.CreatedAt
}

// GetDescription returns the Description field if it's non-nil, zero value otherwise.
func (s *StatusEvent) GetDescription() string {
	if s == nil || s.Description == nil {
		return ""
	}
	return *s.Description
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (s *StatusEvent) GetID() int64 {
	if s == nil || s.ID == nil {
		return 0
	}
	return *s.ID
}

// GetInstallation returns the Installation field.
func (s *StatusEvent) GetInstallation() *Installation {
	if s == nil {
		return nil
	}
	return s.Installation
}

// GetName returns the Name field if it's non-nil, zero value otherwise.
func (s *StatusEvent) GetName() string {
	if s == nil || s.Name == nil {
		return ""
	}
	return *s.Name
}

// GetRepo returns the Repo field.
func (s *StatusEvent) GetRepo() *Repository {
	if s == nil {
		return nil
	}
	return s.Repo
}

// GetSender returns the Sender field.
func (s *StatusEvent) GetSender() *User {
	if s == nil {
		return nil
	}
	return s.Sender
}

// GetSHA returns the SHA field if it's non-nil, zero value otherwise.
func (s *StatusEvent) GetSHA() string {
	if s == nil || s.SHA == nil {
		return ""
	}
	return *s.SHA
}

// GetState returns the State field if it's non-nil, zero value otherwise.
func (s *StatusEvent) GetState() string {
	if s == nil || s.State == nil {
		return ""
	}
	return *s.State
}

// GetTargetURL returns the TargetURL field if it's non-nil, zero value otherwise.
func (s *StatusEvent) GetTargetURL() string {
	if s == nil || s.TargetURL == nil {
		return ""
	}
	return *s.TargetURL
}

// GetUpdatedAt returns the UpdatedAt field if it's non-nil, zero value otherwise.
func (s *StatusEvent) GetUpdatedAt() Timestamp {
	if s == nil || s.UpdatedAt == nil {
		return Timestamp{}
	}
	return *s.UpdatedAt
}

// GetCreatedAt returns the CreatedAt field if it's non-nil, zero value otherwise.
func (s *Subscription) GetCreatedAt() Timestamp {
	if s == nil || s.CreatedAt == nil {
		return Timestamp{}
	}
	return *s.CreatedAt
}

// GetIgnored returns the Ignored field if it's non-nil, zero value otherwise.
func (s *Subscription) GetIgnored() bool {
	if s == nil || s.Ignored == nil {
		return false
	}
	return *s.Ignored
}

// GetReason returns the Reason field if it's non-nil, zero value otherwise.
func (s *Subscription) GetReason() string {
	if s == nil || s.Reason == nil {
		return ""
	}
	return *s.Reason
}

// GetRepositoryURL returns the RepositoryURL field if it's non-nil, zero value otherwise.
func (s *Subscription) GetRepositoryURL() string {
	if s == nil || s.RepositoryURL == nil {
		return ""
	}
	return *s.RepositoryURL
}

// GetSubscribed returns the Subscribed field if it's non-nil, zero value otherwise.
func (s *Subscription) GetSubscribed() bool {
	if s == nil || s.Subscribed == nil {
		return false
	}
	return *s.Subscribed
}

// GetThreadURL returns the ThreadURL field if it's non-nil, zero value otherwise.
func (s *Subscription) GetThreadURL() string {
	if s == nil || s.ThreadURL == nil {
		return ""
	}
	return *s.ThreadURL
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (s *Subscription) GetURL() string {
	if s == nil || s.URL == nil {
		return ""
	}
	return *s.URL
}

// GetMessage returns the Message field if it's non-nil, zero value otherwise.
func (t *Tag) GetMessage() string {
	if t == nil || t.Message == nil {
		return ""
	}
	return *t.Message
}

// GetNodeID returns the NodeID field if it's non-nil, zero value otherwise.
func (t *Tag) GetNodeID() string {
	if t == nil || t.NodeID == nil {
		return ""
	}
	return *t.NodeID
}

// GetObject returns the Object field.
func (t *Tag) GetObject() *GitObject {
	if t == nil {
		return nil
	}
	return t.Object
}

// GetSHA returns the SHA field if it's non-nil, zero value otherwise.
func (t *Tag) GetSHA() string {
	if t == nil || t.SHA == nil {
		return ""
	}
	return *t.SHA
}

// GetTag returns the Tag field if it's non-nil, zero value otherwise.
func (t *Tag) GetTag() string {
	if t == nil || t.Tag == nil {
		return ""
	}
	return *t.Tag
}

// GetTagger returns the Tagger field.
func (t *Tag) GetTagger() *CommitAuthor {
	if t == nil {
		return nil
	}
	return t.Tagger
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (t *Tag) GetURL() string {
	if t == nil || t.URL == nil {
		return ""
	}
	return *t.URL
}

// GetVerification returns the Verification field.
func (t *Tag) GetVerification() *SignatureVerification {
	if t == nil {
		return nil
	}
	return t.Verification
}

// GetCompletedAt returns the CompletedAt field if it's non-nil, zero value otherwise.
func (t *TaskStep) GetCompletedAt() Timestamp {
	if t == nil || t.CompletedAt == nil {
		return Timestamp{}
	}
	return *t.CompletedAt
}

// GetConclusion returns the Conclusion field if it's non-nil, zero value otherwise.
func (t *TaskStep) GetConclusion() string {
	if t == nil || t.Conclusion == nil {
		return ""
	}
	return *t.Conclusion
}

// GetName returns the Name field if it's non-nil, zero value otherwise.
func (t *TaskStep) GetName() string {
	if t == nil || t.Name == nil {
		return ""
	}
	return *t.Name
}

// GetNumber returns the Number field if it's non-nil, zero value otherwise.
func (t *TaskStep) GetNumber() int64 {
	if t == nil || t.Number == nil {
		return 0
	}
	return *t.Number
}

// GetStartedAt returns the StartedAt field if it's non-nil, zero value otherwise.
func (t *TaskStep) GetStartedAt() Timestamp {
	if t == nil || t.StartedAt == nil {
		return Timestamp{}
	}
	return *t.StartedAt
}

// GetStatus returns the Status field if it's non-nil, zero value otherwise.
func (t *TaskStep) GetStatus() string {
	if t == nil || t.Status == nil {
		return ""
	}
	return *t.Status
}

// GetDescription returns the Description field if it's non-nil, zero value otherwise.
func (t *Team) GetDescription() string {
	if t == nil || t.Description == nil {
		return ""
	}
	return *t.Description
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (t *Team) GetID() int64 {
	if t == nil || t.ID == nil {
		return 0
	}
	return *t.ID
}

// GetLDAPDN returns the LDAPDN field if it's non-nil, zero value otherwise.
func (t *Team) GetLDAPDN() string {
	if t == nil || t.LDAPDN == nil {
		return ""
	}
	return *t.LDAPDN
}

// GetMembersCount returns the MembersCount field if it's non-nil, zero value otherwise.
func (t *Team) GetMembersCount() int {
	if t == nil || t.MembersCount == nil {
		return 0
	}
	return *t.MembersCount
}

// GetMembersURL returns the MembersURL field if it's non-nil, zero value otherwise.
func (t *Team) GetMembersURL() string {
	if t == nil || t.MembersURL == nil {
		return ""
	}
	return *t.MembersURL
}

// GetName returns the Name field if it's non-nil, zero value otherwise.
func (t *Team) GetName() string {
	if t == nil || t.Name == nil {
		return ""
	}
	return *t.Name
}

// GetNodeID returns the NodeID field if it's non-nil, zero value otherwise.
func (t *Team) GetNodeID() string {
	if t == nil || t.NodeID == nil {
		return ""
	}
	return *t.NodeID
}

// GetOrganization returns the Organization field.
func (t *Team) GetOrganization() *Organization {
	if t == nil {
		return nil
	}
	return t.Organization
}

// GetParent returns the Parent field.
func (t *Team) GetParent() *Team {
	if t == nil {
		return nil
	}
	return t.Parent
}

// GetPermission returns the Permission field if it's non-nil, zero value otherwise.
func (t *Team) GetPermission() string {
	if t == nil || t.Permission == nil {
		return ""
	}
	return *t.Permission
}

// GetPermissions returns the Permissions map if it's non-nil, an empty map otherwise.
func (t *Team) GetPermissions() map[string]bool {
	if t == nil || t.Permissions == nil {
		return map[string]bool{}
	}
	return t.Permissions
}

// GetPrivacy returns the Privacy field if it's non-nil, zero value otherwise.
func (t *Team) GetPrivacy() string {
	if t == nil || t.Privacy == nil {
		return ""
	}
	return *t.Privacy
}

// GetReposCount returns the ReposCount field if it's non-nil, zero value otherwise.
func (t *Team) GetReposCount() int {
	if t == nil || t.ReposCount == nil {
		return 0
	}
	return *t.ReposCount
}

// GetRepositoriesURL returns the RepositoriesURL field if it's non-nil, zero value otherwise.
func (t *Team) GetRepositoriesURL() string {
	if t == nil || t.RepositoriesURL == nil {
		return ""
	}
	return *t.RepositoriesURL
}

// GetSlug returns the Slug field if it's non-nil, zero value otherwise.
func (t *Team) GetSlug() string {
	if t == nil || t.Slug == nil {
		return ""
	}
	return *t.Slug
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (t *Team) GetURL() string {
	if t == nil || t.URL == nil {
		return ""
	}
	return *t.URL
}

// GetInstallation returns the Installation field.
func (t *TeamAddEvent) GetInstallation() *Installation {
	if t == nil {
		return nil
	}
	return t.Installation
}

// GetOrg returns the Org field.
func (t *TeamAddEvent) GetOrg() *Organization {
	if t == nil {
		return nil
	}
	return t.Org
}

// GetRepo returns the Repo field.
func (t *TeamAddEvent) GetRepo() *Repository {
	if t == nil {
		return nil
	}
	return t.Repo
}

// GetSender returns the Sender field.
func (t *TeamAddEvent) GetSender() *User {
	if t == nil {
		return nil
	}
	return t.Sender
}

// GetTeam returns the Team field.
func (t *TeamAddEvent) GetTeam() *Team {
	if t == nil {
		return nil
	}
	return t.Team
}

// GetAuthor returns the Author field.
func (t *TeamDiscussion) GetAuthor() *User {
	if t == nil {
		return nil
	}
	return t.Author
}

// GetBody returns the Body field if it's non-nil, zero value otherwise.
func (t *TeamDiscussion) GetBody() string {
	if t == nil || t.Body == nil {
		return ""
	}
	return *t.Body
}

// GetBodyHTML returns the BodyHTML field if it's non-nil, zero value otherwise.
func (t *TeamDiscussion) GetBodyHTML() string {
	if t == nil || t.BodyHTML == nil {
		return ""
	}
	return *t.BodyHTML
}

// GetBodyVersion returns the BodyVersion field if it's non-nil, zero value otherwise.
func (t *TeamDiscussion) GetBodyVersion() string {
	if t == nil || t.BodyVersion == nil {
		return ""
	}
	return *t.BodyVersion
}

// GetCommentsCount returns the CommentsCount field if it's non-nil, zero value otherwise.
func (t *TeamDiscussion) GetCommentsCount() int {
	if t == nil || t.CommentsCount == nil {
		return 0
	}
	return *t.CommentsCount
}

// GetCommentsURL returns the CommentsURL field if it's non-nil, zero value otherwise.
func (t *TeamDiscussion) GetCommentsURL() string {
	if t == nil || t.CommentsURL == nil {
		return ""
	}
	return *t.CommentsURL
}

// GetCreatedAt returns the CreatedAt field if it's non-nil, zero value otherwise.
func (t *TeamDiscussion) GetCreatedAt() Timestamp {
	if t == nil || t.CreatedAt == nil {
		return Timestamp{}
	}
	return *t.CreatedAt
}

// GetHTMLURL returns the HTMLURL field if it's non-nil, zero value otherwise.
func (t *TeamDiscussion) GetHTMLURL() string {
	if t == nil || t.HTMLURL == nil {
		return ""
	}
	return *t.HTMLURL
}

// GetLastEditedAt returns the LastEditedAt field if it's non-nil, zero value otherwise.
func (t *TeamDiscussion) GetLastEditedAt() Timestamp {
	if t == nil || t.LastEditedAt == nil {
		return Timestamp{}
	}
	return *t.LastEditedAt
}

// GetNodeID returns the NodeID field if it's non-nil, zero value otherwise.
func (t *TeamDiscussion) GetNodeID() string {
	if t == nil || t.NodeID == nil {
		return ""
	}
	return *t.NodeID
}

// GetNumber returns the Number field if it's non-nil, zero value otherwise.
func (t *TeamDiscussion) GetNumber() int {
	if t == nil || t.Number == nil {
		return 0
	}
	return *t.Number
}

// GetPinned returns the Pinned field if it's non-nil, zero value otherwise.
func (t *TeamDiscussion) GetPinned() bool {
	if t == nil || t.Pinned == nil {
		return false
	}
	return *t.Pinned
}

// GetPrivate returns the Private field if it's non-nil, zero value otherwise.
func (t *TeamDiscussion) GetPrivate() bool {
	if t == nil || t.Private == nil {
		return false
	}
	return *t.Private
}

// GetReactions returns the Reactions field.
func (t *TeamDiscussion) GetReactions() *Reactions {
	if t == nil {
		return nil
	}
	return t.Reactions
}

// GetTeamURL returns the TeamURL field if it's non-nil, zero value otherwise.
func (t *TeamDiscussion) GetTeamURL() string {
	if t == nil || t.TeamURL == nil {
		return ""
	}
	return *t.TeamURL
}

// GetTitle returns the Title field if it's non-nil, zero value otherwise.
func (t *TeamDiscussion) GetTitle() string {
	if t == nil || t.Title == nil {
		return ""
	}
	return *t.Title
}

// GetUpdatedAt returns the UpdatedAt field if it's non-nil, zero value otherwise.
func (t *TeamDiscussion) GetUpdatedAt() Timestamp {
	if t == nil || t.UpdatedAt == nil {
		return Timestamp{}
	}
	return *t.UpdatedAt
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (t *TeamDiscussion) GetURL() string {
	if t == nil || t.URL == nil {
		return ""
	}
	return *t.URL
}

// GetAction returns the Action field if it's non-nil, zero value otherwise.
func (t *TeamEvent) GetAction() string {
	if t == nil || t.Action == nil {
		return ""
	}
	return *t.Action
}

// GetChanges returns the Changes field.
func (t *TeamEvent) GetChanges() *TeamChange {
	if t == nil {
		return nil
	}
	return t.Changes
}

// GetInstallation returns the Installation field.
func (t *TeamEvent) GetInstallation() *Installation {
	if t == nil {
		return nil
	}
	return t.Installation
}

// GetOrg returns the Org field.
func (t *TeamEvent) GetOrg() *Organization {
	if t == nil {
		return nil
	}
	return t.Org
}

// GetRepo returns the Repo field.
func (t *TeamEvent) GetRepo() *Repository {
	if t == nil {
		return nil
	}
	return t.Repo
}

// GetSender returns the Sender field.
func (t *TeamEvent) GetSender() *User {
	if t == nil {
		return nil
	}
	return t.Sender
}

// GetTeam returns the Team field.
func (t *TeamEvent) GetTeam() *Team {
	if t == nil {
		return nil
	}
	return t.Team
}

// GetDescription returns the Description field if it's non-nil, zero value otherwise.
func (t *TeamLDAPMapping) GetDescription() string {
	if t == nil || t.Description == nil {
		return ""
	}
	return *t.Description
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (t *TeamLDAPMapping) GetID() int64 {
	if t == nil || t.ID == nil {
		return 0
	}
	return *t.ID
}

// GetLDAPDN returns the LDAPDN field if it's non-nil, zero value otherwise.
func (t *TeamLDAPMapping) GetLDAPDN() string {
	if t == nil || t.LDAPDN == nil {
		return ""
	}
	return *t.LDAPDN
}

// GetMembersURL returns the MembersURL field if it's non-nil, zero value otherwise.
func (t *TeamLDAPMapping) GetMembersURL() string {
	if t == nil || t.MembersURL == nil {
		return ""
	}
	return *t.MembersURL
}

// GetName returns the Name field if it's non-nil, zero value otherwise.
func (t *TeamLDAPMapping) GetName() string {
	if t == nil || t.Name == nil {
		return ""
	}
	return *t.Name
}

// GetPermission returns the Permission field if it's non-nil, zero value otherwise.
func (t *TeamLDAPMapping) GetPermission() string {
	if t == nil || t.Permission == nil {
		return ""
	}
	return *t.Permission
}

// GetPrivacy returns the Privacy field if it's non-nil, zero value otherwise.
func (t *TeamLDAPMapping) GetPrivacy() string {
	if t == nil || t.Privacy == nil {
		return ""
	}
	return *t.Privacy
}

// GetRepositoriesURL returns the RepositoriesURL field if it's non-nil, zero value otherwise.
func (t *TeamLDAPMapping) GetRepositoriesURL() string {
	if t == nil || t.RepositoriesURL == nil {
		return ""
	}
	return *t.RepositoriesURL
}

// GetSlug returns the Slug field if it's non-nil, zero value otherwise.
func (t *TeamLDAPMapping) GetSlug() string {
	if t == nil || t.Slug == nil {
		return ""
	}
	return *t.Slug
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (t *TeamLDAPMapping) GetURL() string {
	if t == nil || t.URL == nil {
		return ""
	}
	return *t.URL
}

// GetPermission returns the Permission field if it's non-nil, zero value otherwise.
func (t *TeamProjectOptions) GetPermission() string {
	if t == nil || t.Permission == nil {
		return ""
	}
	return *t.Permission
}

// GetDescription returns the Description field if it's non-nil, zero value otherwise.
func (t *TemplateRepoRequest) GetDescription() string {
	if t == nil || t.Description == nil {
		return ""
	}
	return *t.Description
}

// GetName returns the Name field if it's non-nil, zero value otherwise.
func (t *TemplateRepoRequest) GetName() string {
	if t == nil || t.Name == nil {
		return ""
	}
	return *t.Name
}

// GetOwner returns the Owner field if it's non-nil, zero value otherwise.
func (t *TemplateRepoRequest) GetOwner() string {
	if t == nil || t.Owner == nil {
		return ""
	}
	return *t.Owner
}

// GetPrivate returns the Private field if it's non-nil, zero value otherwise.
func (t *TemplateRepoRequest) GetPrivate() bool {
	if t == nil || t.Private == nil {
		return false
	}
	return *t.Private
}

// GetFragment returns the Fragment field if it's non-nil, zero value otherwise.
func (t *TextMatch) GetFragment() string {
	if t == nil || t.Fragment == nil {
		return ""
	}
	return *t.Fragment
}

// GetObjectType returns the ObjectType field if it's non-nil, zero value otherwise.
func (t *TextMatch) GetObjectType() string {
	if t == nil || t.ObjectType == nil {
		return ""
	}
	return *t.ObjectType
}

// GetObjectURL returns the ObjectURL field if it's non-nil, zero value otherwise.
func (t *TextMatch) GetObjectURL() string {
	if t == nil || t.ObjectURL == nil {
		return ""
	}
	return *t.ObjectURL
}

// GetProperty returns the Property field if it's non-nil, zero value otherwise.
func (t *TextMatch) GetProperty() string {
	if t == nil || t.Property == nil {
		return ""
	}
	return *t.Property
}

// GetActor returns the Actor field.
func (t *Timeline) GetActor() *User {
	if t == nil {
		return nil
	}
	return t.Actor
}

// GetAssignee returns the Assignee field.
func (t *Timeline) GetAssignee() *User {
	if t == nil {
		return nil
	}
	return t.Assignee
}

// GetCommitID returns the CommitID field if it's non-nil, zero value otherwise.
func (t *Timeline) GetCommitID() string {
	if t == nil || t.CommitID == nil {
		return ""
	}
	return *t.CommitID
}

// GetCommitURL returns the CommitURL field if it's non-nil, zero value otherwise.
func (t *Timeline) GetCommitURL() string {
	if t == nil || t.CommitURL == nil {
		return ""
	}
	return *t.CommitURL
}

// GetCreatedAt returns the CreatedAt field if it's non-nil, zero value otherwise.
func (t *Timeline) GetCreatedAt() time.Time {
	if t == nil || t.CreatedAt == nil {
		return time.Time{}
	}
	return *t.CreatedAt
}

// GetEvent returns the Event field if it's non-nil, zero value otherwise.
func (t *Timeline) GetEvent() string {
	if t == nil || t.Event == nil {
		return ""
	}
	return *t.Event
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (t *Timeline) GetID() int64 {
	if t == nil || t.ID == nil {
		return 0
	}
	return *t.ID
}

// GetLabel returns the Label field.
func (t *Timeline) GetLabel() *Label {
	if t == nil {
		return nil
	}
	return t.Label
}

// GetMilestone returns the Milestone field.
func (t *Timeline) GetMilestone() *Milestone {
	if t == nil {
		return nil
	}
	return t.Milestone
}

// GetProjectCard returns the ProjectCard field.
func (t *Timeline) GetProjectCard() *ProjectCard {
	if t == nil {
		return nil
	}
	return t.ProjectCard
}

// GetRename returns the Rename field.
func (t *Timeline) GetRename() *Rename {
	if t == nil {
		return nil
	}
	return t.Rename
}

// GetSource returns the Source field.
func (t *Timeline) GetSource() *Source {
	if t == nil {
		return nil
	}
	return t.Source
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (t *Timeline) GetURL() string {
	if t == nil || t.URL == nil {
		return ""
	}
	return *t.URL
}

// GetCreatedAt returns the CreatedAt field if it's non-nil, zero value otherwise.
func (t *TopicResult) GetCreatedAt() Timestamp {
	if t == nil || t.CreatedAt == nil {
		return Timestamp{}
	}
	return *t.CreatedAt
}

// GetCreatedBy returns the CreatedBy field if it's non-nil, zero value otherwise.
func (t *TopicResult) GetCreatedBy() string {
	if t == nil || t.CreatedBy == nil {
		return ""
	}
	return *t.CreatedBy
}

// GetCurated returns the Curated field if it's non-nil, zero value otherwise.
func (t *TopicResult) GetCurated() bool {
	if t == nil || t.Curated == nil {
		return false
	}
	return *t.Curated
}

// GetDescription returns the Description field if it's non-nil, zero value otherwise.
func (t *TopicResult) GetDescription() string {
	if t == nil || t.Description == nil {
		return ""
	}
	return *t.Description
}

// GetDisplayName returns the DisplayName field if it's non-nil, zero value otherwise.
func (t *TopicResult) GetDisplayName() string {
	if t == nil || t.DisplayName == nil {
		return ""
	}
	return *t.DisplayName
}

// GetFeatured returns the Featured field if it's non-nil, zero value otherwise.
func (t *TopicResult) GetFeatured() bool {
	if t == nil || t.Featured == nil {
		return false
	}
	return *t.Featured
}

// GetName returns the Name field if it's non-nil, zero value otherwise.
func (t *TopicResult) GetName() string {
	if t == nil || t.Name == nil {
		return ""
	}
	return *t.Name
}

// GetScore returns the Score field.
func (t *TopicResult) GetScore() *float64 {
	if t == nil {
		return nil
	}
	return t.Score
}

// GetShortDescription returns the ShortDescription field if it's non-nil, zero value otherwise.
func (t *TopicResult) GetShortDescription() string {
	if t == nil || t.ShortDescription == nil {
		return ""
	}
	return *t.ShortDescription
}

// GetUpdatedAt returns the UpdatedAt field if it's non-nil, zero value otherwise.
func (t *TopicResult) GetUpdatedAt() string {
	if t == nil || t.UpdatedAt == nil {
		return ""
	}
	return *t.UpdatedAt
}

// GetIncompleteResults returns the IncompleteResults field if it's non-nil, zero value otherwise.
func (t *TopicsSearchResult) GetIncompleteResults() bool {
	if t == nil || t.IncompleteResults == nil {
		return false
	}
	return *t.IncompleteResults
}

// GetTotal returns the Total field if it's non-nil, zero value otherwise.
func (t *TopicsSearchResult) GetTotal() int {
	if t == nil || t.Total == nil {
		return 0
	}
	return *t.Total
}

// GetCount returns the Count field if it's non-nil, zero value otherwise.
func (t *TrafficClones) GetCount() int {
	if t == nil || t.Count == nil {
		return 0
	}
	return *t.Count
}

// GetUniques returns the Uniques field if it's non-nil, zero value otherwise.
func (t *TrafficClones) GetUniques() int {
	if t == nil || t.Uniques == nil {
		return 0
	}
	return *t.Uniques
}

// GetCount returns the Count field if it's non-nil, zero value otherwise.
func (t *TrafficData) GetCount() int {
	if t == nil || t.Count == nil {
		return 0
	}
	return *t.Count
}

// GetTimestamp returns the Timestamp field if it's non-nil, zero value otherwise.
func (t *TrafficData) GetTimestamp() Timestamp {
	if t == nil || t.Timestamp == nil {
		return Timestamp{}
	}
	return *t.Timestamp
}

// GetUniques returns the Uniques field if it's non-nil, zero value otherwise.
func (t *TrafficData) GetUniques() int {
	if t == nil || t.Uniques == nil {
		return 0
	}
	return *t.Uniques
}

// GetCount returns the Count field if it's non-nil, zero value otherwise.
func (t *TrafficPath) GetCount() int {
	if t == nil || t.Count == nil {
		return 0
	}
	return *t.Count
}

// GetPath returns the Path field if it's non-nil, zero value otherwise.
func (t *TrafficPath) GetPath() string {
	if t == nil || t.Path == nil {
		return ""
	}
	return *t.Path
}

// GetTitle returns the Title field if it's non-nil, zero value otherwise.
func (t *TrafficPath) GetTitle() string {
	if t == nil || t.Title == nil {
		return ""
	}
	return *t.Title
}

// GetUniques returns the Uniques field if it's non-nil, zero value otherwise.
func (t *TrafficPath) GetUniques() int {
	if t == nil || t.Uniques == nil {
		return 0
	}
	return *t.Uniques
}

// GetCount returns the Count field if it's non-nil, zero value otherwise.
func (t *TrafficReferrer) GetCount() int {
	if t == nil || t.Count == nil {
		return 0
	}
	return *t.Count
}

// GetReferrer returns the Referrer field if it's non-nil, zero value otherwise.
func (t *TrafficReferrer) GetReferrer() string {
	if t == nil || t.Referrer == nil {
		return ""
	}
	return *t.Referrer
}

// GetUniques returns the Uniques field if it's non-nil, zero value otherwise.
func (t *TrafficReferrer) GetUniques() int {
	if t == nil || t.Uniques == nil {
		return 0
	}
	return *t.Uniques
}

// GetCount returns the Count field if it's non-nil, zero value otherwise.
func (t *TrafficViews) GetCount() int {
	if t == nil || t.Count == nil {
		return 0
	}
	return *t.Count
}

// GetUniques returns the Uniques field if it's non-nil, zero value otherwise.
func (t *TrafficViews) GetUniques() int {
	if t == nil || t.Uniques == nil {
		return 0
	}
	return *t.Uniques
}

// GetSHA returns the SHA field if it's non-nil, zero value otherwise.
func (t *Tree) GetSHA() string {
	if t == nil || t.SHA == nil {
		return ""
	}
	return *t.SHA
}

// GetTruncated returns the Truncated field if it's non-nil, zero value otherwise.
func (t *Tree) GetTruncated() bool {
	if t == nil || t.Truncated == nil {
		return false
	}
	return *t.Truncated
}

// GetContent returns the Content field if it's non-nil, zero value otherwise.
func (t *TreeEntry) GetContent() string {
	if t == nil || t.Content == nil {
		return ""
	}
	return *t.Content
}

// GetMode returns the Mode field if it's non-nil, zero value otherwise.
func (t *TreeEntry) GetMode() string {
	if t == nil || t.Mode == nil {
		return ""
	}
	return *t.Mode
}

// GetPath returns the Path field if it's non-nil, zero value otherwise.
func (t *TreeEntry) GetPath() string {
	if t == nil || t.Path == nil {
		return ""
	}
	return *t.Path
}

// GetSHA returns the SHA field if it's non-nil, zero value otherwise.
func (t *TreeEntry) GetSHA() string {
	if t == nil || t.SHA == nil {
		return ""
	}
	return *t.SHA
}

// GetSize returns the Size field if it's non-nil, zero value otherwise.
func (t *TreeEntry) GetSize() int {
	if t == nil || t.Size == nil {
		return 0
	}
	return *t.Size
}

// GetType returns the Type field if it's non-nil, zero value otherwise.
func (t *TreeEntry) GetType() string {
	if t == nil || t.Type == nil {
		return ""
	}
	return *t.Type
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (t *TreeEntry) GetURL() string {
	if t == nil || t.URL == nil {
		return ""
	}
	return *t.URL
}

// GetCompletedAt returns the CompletedAt field if it's non-nil, zero value otherwise.
func (u *UpdateCheckRunOptions) GetCompletedAt() Timestamp {
	if u == nil || u.CompletedAt == nil {
		return Timestamp{}
	}
	return *u.CompletedAt
}

// GetConclusion returns the Conclusion field if it's non-nil, zero value otherwise.
func (u *UpdateCheckRunOptions) GetConclusion() string {
	if u == nil || u.Conclusion == nil {
		return ""
	}
	return *u.Conclusion
}

// GetDetailsURL returns the DetailsURL field if it's non-nil, zero value otherwise.
func (u *UpdateCheckRunOptions) GetDetailsURL() string {
	if u == nil || u.DetailsURL == nil {
		return ""
	}
	return *u.DetailsURL
}

// GetExternalID returns the ExternalID field if it's non-nil, zero value otherwise.
func (u *UpdateCheckRunOptions) GetExternalID() string {
	if u == nil || u.ExternalID == nil {
		return ""
	}
	return *u.ExternalID
}

// GetOutput returns the Output field.
func (u *UpdateCheckRunOptions) GetOutput() *CheckRunOutput {
	if u == nil {
		return nil
	}
	return u.Output
}

// GetStatus returns the Status field if it's non-nil, zero value otherwise.
func (u *UpdateCheckRunOptions) GetStatus() string {
	if u == nil || u.Status == nil {
		return ""
	}
	return *u.Status
}

// GetName returns the Name field if it's non-nil, zero value otherwise.
func (u *UpdateRunnerGroupRequest) GetName() string {
	if u == nil || u.Name == nil {
		return ""
	}
	return *u.Name
}

// GetVisibility returns the Visibility field if it's non-nil, zero value otherwise.
func (u *UpdateRunnerGroupRequest) GetVisibility() string {
	if u == nil || u.Visibility == nil {
		return ""
	}
	return *u.Visibility
}

// GetAvatarURL returns the AvatarURL field if it's non-nil, zero value otherwise.
func (u *User) GetAvatarURL() string {
	if u == nil || u.AvatarURL == nil {
		return ""
	}
	return *u.AvatarURL
}

// GetBio returns the Bio field if it's non-nil, zero value otherwise.
func (u *User) GetBio() string {
	if u == nil || u.Bio == nil {
		return ""
	}
	return *u.Bio
}

// GetBlog returns the Blog field if it's non-nil, zero value otherwise.
func (u *User) GetBlog() string {
	if u == nil || u.Blog == nil {
		return ""
	}
	return *u.Blog
}

// GetCollaborators returns the Collaborators field if it's non-nil, zero value otherwise.
func (u *User) GetCollaborators() int {
	if u == nil || u.Collaborators == nil {
		return 0
	}
	return *u.Collaborators
}

// GetCompany returns the Company field if it's non-nil, zero value otherwise.
func (u *User) GetCompany() string {
	if u == nil || u.Company == nil {
		return ""
	}
	return *u.Company
}

// GetCreatedAt returns the CreatedAt field if it's non-nil, zero value otherwise.
func (u *User) GetCreatedAt() Timestamp {
	if u == nil || u.CreatedAt == nil {
		return Timestamp{}
	}
	return *u.CreatedAt
}

// GetDiskUsage returns the DiskUsage field if it's non-nil, zero value otherwise.
func (u *User) GetDiskUsage() int {
	if u == nil || u.DiskUsage == nil {
		return 0
	}
	return *u.DiskUsage
}

// GetEmail returns the Email field if it's non-nil, zero value otherwise.
func (u *User) GetEmail() string {
	if u == nil || u.Email == nil {
		return ""
	}
	return *u.Email
}

// GetEventsURL returns the EventsURL field if it's non-nil, zero value otherwise.
func (u *User) GetEventsURL() string {
	if u == nil || u.EventsURL == nil {
		return ""
	}
	return *u.EventsURL
}

// GetFollowers returns the Followers field if it's non-nil, zero value otherwise.
func (u *User) GetFollowers() int {
	if u == nil || u.Followers == nil {
		return 0
	}
	return *u.Followers
}

// GetFollowersURL returns the FollowersURL field if it's non-nil, zero value otherwise.
func (u *User) GetFollowersURL() string {
	if u == nil || u.FollowersURL == nil {
		return ""
	}
	return *u.FollowersURL
}

// GetFollowing returns the Following field if it's non-nil, zero value otherwise.
func (u *User) GetFollowing() int {
	if u == nil || u.Following == nil {
		return 0
	}
	return *u.Following
}

// GetFollowingURL returns the FollowingURL field if it's non-nil, zero value otherwise.
func (u *User) GetFollowingURL() string {
	if u == nil || u.FollowingURL == nil {
		return ""
	}
	return *u.FollowingURL
}

// GetGistsURL returns the GistsURL field if it's non-nil, zero value otherwise.
func (u *User) GetGistsURL() string {
	if u == nil || u.GistsURL == nil {
		return ""
	}
	return *u.GistsURL
}

// GetGravatarID returns the GravatarID field if it's non-nil, zero value otherwise.
func (u *User) GetGravatarID() string {
	if u == nil || u.GravatarID == nil {
		return ""
	}
	return *u.GravatarID
}

// GetHireable returns the Hireable field if it's non-nil, zero value otherwise.
func (u *User) GetHireable() bool {
	if u == nil || u.Hireable == nil {
		return false
	}
	return *u.Hireable
}

// GetHTMLURL returns the HTMLURL field if it's non-nil, zero value otherwise.
func (u *User) GetHTMLURL() string {
	if u == nil || u.HTMLURL == nil {
		return ""
	}
	return *u.HTMLURL
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (u *User) GetID() int64 {
	if u == nil || u.ID == nil {
		return 0
	}
	return *u.ID
}

// GetLdapDn returns the LdapDn field if it's non-nil, zero value otherwise.
func (u *User) GetLdapDn() string {
	if u == nil || u.LdapDn == nil {
		return ""
	}
	return *u.LdapDn
}

// GetLocation returns the Location field if it's non-nil, zero value otherwise.
func (u *User) GetLocation() string {
	if u == nil || u.Location == nil {
		return ""
	}
	return *u.Location
}

// GetLogin returns the Login field if it's non-nil, zero value otherwise.
func (u *User) GetLogin() string {
	if u == nil || u.Login == nil {
		return ""
	}
	return *u.Login
}

// GetName returns the Name field if it's non-nil, zero value otherwise.
func (u *User) GetName() string {
	if u == nil || u.Name == nil {
		return ""
	}
	return *u.Name
}

// GetNodeID returns the NodeID field if it's non-nil, zero value otherwise.
func (u *User) GetNodeID() string {
	if u == nil || u.NodeID == nil {
		return ""
	}
	return *u.NodeID
}

// GetOrganizationsURL returns the OrganizationsURL field if it's non-nil, zero value otherwise.
func (u *User) GetOrganizationsURL() string {
	if u == nil || u.OrganizationsURL == nil {
		return ""
	}
	return *u.OrganizationsURL
}

// GetOwnedPrivateRepos returns the OwnedPrivateRepos field if it's non-nil, zero value otherwise.
func (u *User) GetOwnedPrivateRepos() int {
	if u == nil || u.OwnedPrivateRepos == nil {
		return 0
	}
	return *u.OwnedPrivateRepos
}

// GetPermissions returns the Permissions map if it's non-nil, an empty map otherwise.
func (u *User) GetPermissions() map[string]bool {
	if u == nil || u.Permissions == nil {
		return map[string]bool{}
	}
	return u.Permissions
}

// GetPlan returns the Plan field.
func (u *User) GetPlan() *Plan {
	if u == nil {
		return nil
	}
	return u.Plan
}

// GetPrivateGists returns the PrivateGists field if it's non-nil, zero value otherwise.
func (u *User) GetPrivateGists() int {
	if u == nil || u.PrivateGists == nil {
		return 0
	}
	return *u.PrivateGists
}

// GetPublicGists returns the PublicGists field if it's non-nil, zero value otherwise.
func (u *User) GetPublicGists() int {
	if u == nil || u.PublicGists == nil {
		return 0
	}
	return *u.PublicGists
}

// GetPublicRepos returns the PublicRepos field if it's non-nil, zero value otherwise.
func (u *User) GetPublicRepos() int {
	if u == nil || u.PublicRepos == nil {
		return 0
	}
	return *u.PublicRepos
}

// GetReceivedEventsURL returns the ReceivedEventsURL field if it's non-nil, zero value otherwise.
func (u *User) GetReceivedEventsURL() string {
	if u == nil || u.ReceivedEventsURL == nil {
		return ""
	}
	return *u.ReceivedEventsURL
}

// GetReposURL returns the ReposURL field if it's non-nil, zero value otherwise.
func (u *User) GetReposURL() string {
	if u == nil || u.ReposURL == nil {
		return ""
	}
	return *u.ReposURL
}

// GetSiteAdmin returns the SiteAdmin field if it's non-nil, zero value otherwise.
func (u *User) GetSiteAdmin() bool {
	if u == nil || u.SiteAdmin == nil {
		return false
	}
	return *u.SiteAdmin
}

// GetStarredURL returns the StarredURL field if it's non-nil, zero value otherwise.
func (u *User) GetStarredURL() string {
	if u == nil || u.StarredURL == nil {
		return ""
	}
	return *u.StarredURL
}

// GetSubscriptionsURL returns the SubscriptionsURL field if it's non-nil, zero value otherwise.
func (u *User) GetSubscriptionsURL() string {
	if u == nil || u.SubscriptionsURL == nil {
		return ""
	}
	return *u.SubscriptionsURL
}

// GetSuspendedAt returns the SuspendedAt field if it's non-nil, zero value otherwise.
func (u *User) GetSuspendedAt() Timestamp {
	if u == nil || u.SuspendedAt == nil {
		return Timestamp{}
	}
	return *u.SuspendedAt
}

// GetTotalPrivateRepos returns the TotalPrivateRepos field if it's non-nil, zero value otherwise.
func (u *User) GetTotalPrivateRepos() int {
	if u == nil || u.TotalPrivateRepos == nil {
		return 0
	}
	return *u.TotalPrivateRepos
}

// GetTwitterUsername returns the TwitterUsername field if it's non-nil, zero value otherwise.
func (u *User) GetTwitterUsername() string {
	if u == nil || u.TwitterUsername == nil {
		return ""
	}
	return *u.TwitterUsername
}

// GetTwoFactorAuthentication returns the TwoFactorAuthentication field if it's non-nil, zero value otherwise.
func (u *User) GetTwoFactorAuthentication() bool {
	if u == nil || u.TwoFactorAuthentication == nil {
		return false
	}
	return *u.TwoFactorAuthentication
}

// GetType returns the Type field if it's non-nil, zero value otherwise.
func (u *User) GetType() string {
	if u == nil || u.Type == nil {
		return ""
	}
	return *u.Type
}

// GetUpdatedAt returns the UpdatedAt field if it's non-nil, zero value otherwise.
func (u *User) GetUpdatedAt() Timestamp {
	if u == nil || u.UpdatedAt == nil {
		return Timestamp{}
	}
	return *u.UpdatedAt
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (u *User) GetURL() string {
	if u == nil || u.URL == nil {
		return ""
	}
	return *u.URL
}

// GetApp returns the App field.
func (u *UserAuthorization) GetApp() *OAuthAPP {
	if u == nil {
		return nil
	}
	return u.App
}

// GetCreatedAt returns the CreatedAt field if it's non-nil, zero value otherwise.
func (u *UserAuthorization) GetCreatedAt() Timestamp {
	if u == nil || u.CreatedAt == nil {
		return Timestamp{}
	}
	return *u.CreatedAt
}

// GetFingerprint returns the Fingerprint field if it's non-nil, zero value otherwise.
func (u *UserAuthorization) GetFingerprint() string {
	if u == nil || u.Fingerprint == nil {
		return ""
	}
	return *u.Fingerprint
}

// GetHashedToken returns the HashedToken field if it's non-nil, zero value otherwise.
func (u *UserAuthorization) GetHashedToken() string {
	if u == nil || u.HashedToken == nil {
		return ""
	}
	return *u.HashedToken
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (u *UserAuthorization) GetID() int64 {
	if u == nil || u.ID == nil {
		return 0
	}
	return *u.ID
}

// GetNote returns the Note field if it's non-nil, zero value otherwise.
func (u *UserAuthorization) GetNote() string {
	if u == nil || u.Note == nil {
		return ""
	}
	return *u.Note
}

// GetNoteURL returns the NoteURL field if it's non-nil, zero value otherwise.
func (u *UserAuthorization) GetNoteURL() string {
	if u == nil || u.NoteURL == nil {
		return ""
	}
	return *u.NoteURL
}

// GetToken returns the Token field if it's non-nil, zero value otherwise.
func (u *UserAuthorization) GetToken() string {
	if u == nil || u.Token == nil {
		return ""
	}
	return *u.Token
}

// GetTokenLastEight returns the TokenLastEight field if it's non-nil, zero value otherwise.
func (u *UserAuthorization) GetTokenLastEight() string {
	if u == nil || u.TokenLastEight == nil {
		return ""
	}
	return *u.TokenLastEight
}

// GetUpdatedAt returns the UpdatedAt field if it's non-nil, zero value otherwise.
func (u *UserAuthorization) GetUpdatedAt() Timestamp {
	if u == nil || u.UpdatedAt == nil {
		return Timestamp{}
	}
	return *u.UpdatedAt
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (u *UserAuthorization) GetURL() string {
	if u == nil || u.URL == nil {
		return ""
	}
	return *u.URL
}

// GetMessage returns the Message field if it's non-nil, zero value otherwise.
func (u *UserContext) GetMessage() string {
	if u == nil || u.Message == nil {
		return ""
	}
	return *u.Message
}

// GetOcticon returns the Octicon field if it's non-nil, zero value otherwise.
func (u *UserContext) GetOcticon() string {
	if u == nil || u.Octicon == nil {
		return ""
	}
	return *u.Octicon
}

// GetEmail returns the Email field if it's non-nil, zero value otherwise.
func (u *UserEmail) GetEmail() string {
	if u == nil || u.Email == nil {
		return ""
	}
	return *u.Email
}

// GetPrimary returns the Primary field if it's non-nil, zero value otherwise.
func (u *UserEmail) GetPrimary() bool {
	if u == nil || u.Primary == nil {
		return false
	}
	return *u.Primary
}

// GetVerified returns the Verified field if it's non-nil, zero value otherwise.
func (u *UserEmail) GetVerified() bool {
	if u == nil || u.Verified == nil {
		return false
	}
	return *u.Verified
}

// GetVisibility returns the Visibility field if it's non-nil, zero value otherwise.
func (u *UserEmail) GetVisibility() string {
	if u == nil || u.Visibility == nil {
		return ""
	}
	return *u.Visibility
}

// GetAction returns the Action field if it's non-nil, zero value otherwise.
func (u *UserEvent) GetAction() string {
	if u == nil || u.Action == nil {
		return ""
	}
	return *u.Action
}

// GetEnterprise returns the Enterprise field.
func (u *UserEvent) GetEnterprise() *Enterprise {
	if u == nil {
		return nil
	}
	return u.Enterprise
}

// GetSender returns the Sender field.
func (u *UserEvent) GetSender() *User {
	if u == nil {
		return nil
	}
	return u.Sender
}

// GetUser returns the User field.
func (u *UserEvent) GetUser() *User {
	if u == nil {
		return nil
	}
	return u.User
}

// GetAvatarURL returns the AvatarURL field if it's non-nil, zero value otherwise.
func (u *UserLDAPMapping) GetAvatarURL() string {
	if u == nil || u.AvatarURL == nil {
		return ""
	}
	return *u.AvatarURL
}

// GetEventsURL returns the EventsURL field if it's non-nil, zero value otherwise.
func (u *UserLDAPMapping) GetEventsURL() string {
	if u == nil || u.EventsURL == nil {
		return ""
	}
	return *u.EventsURL
}

// GetFollowersURL returns the FollowersURL field if it's non-nil, zero value otherwise.
func (u *UserLDAPMapping) GetFollowersURL() string {
	if u == nil || u.FollowersURL == nil {
		return ""
	}
	return *u.FollowersURL
}

// GetFollowingURL returns the FollowingURL field if it's non-nil, zero value otherwise.
func (u *UserLDAPMapping) GetFollowingURL() string {
	if u == nil || u.FollowingURL == nil {
		return ""
	}
	return *u.FollowingURL
}

// GetGistsURL returns the GistsURL field if it's non-nil, zero value otherwise.
func (u *UserLDAPMapping) GetGistsURL() string {
	if u == nil || u.GistsURL == nil {
		return ""
	}
	return *u.GistsURL
}

// GetGravatarID returns the GravatarID field if it's non-nil, zero value otherwise.
func (u *UserLDAPMapping) GetGravatarID() string {
	if u == nil || u.GravatarID == nil {
		return ""
	}
	return *u.GravatarID
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (u *UserLDAPMapping) GetID() int64 {
	if u == nil || u.ID == nil {
		return 0
	}
	return *u.ID
}

// GetLDAPDN returns the LDAPDN field if it's non-nil, zero value otherwise.
func (u *UserLDAPMapping) GetLDAPDN() string {
	if u == nil || u.LDAPDN == nil {
		return ""
	}
	return *u.LDAPDN
}

// GetLogin returns the Login field if it's non-nil, zero value otherwise.
func (u *UserLDAPMapping) GetLogin() string {
	if u == nil || u.Login == nil {
		return ""
	}
	return *u.Login
}

// GetOrganizationsURL returns the OrganizationsURL field if it's non-nil, zero value otherwise.
func (u *UserLDAPMapping) GetOrganizationsURL() string {
	if u == nil || u.OrganizationsURL == nil {
		return ""
	}
	return *u.OrganizationsURL
}

// GetReceivedEventsURL returns the ReceivedEventsURL field if it's non-nil, zero value otherwise.
func (u *UserLDAPMapping) GetReceivedEventsURL() string {
	if u == nil || u.ReceivedEventsURL == nil {
		return ""
	}
	return *u.ReceivedEventsURL
}

// GetReposURL returns the ReposURL field if it's non-nil, zero value otherwise.
func (u *UserLDAPMapping) GetReposURL() string {
	if u == nil || u.ReposURL == nil {
		return ""
	}
	return *u.ReposURL
}

// GetSiteAdmin returns the SiteAdmin field if it's non-nil, zero value otherwise.
func (u *UserLDAPMapping) GetSiteAdmin() bool {
	if u == nil || u.SiteAdmin == nil {
		return false
	}
	return *u.SiteAdmin
}

// GetStarredURL returns the StarredURL field if it's non-nil, zero value otherwise.
func (u *UserLDAPMapping) GetStarredURL() string {
	if u == nil || u.StarredURL == nil {
		return ""
	}
	return *u.StarredURL
}

// GetSubscriptionsURL returns the SubscriptionsURL field if it's non-nil, zero value otherwise.
func (u *UserLDAPMapping) GetSubscriptionsURL() string {
	if u == nil || u.SubscriptionsURL == nil {
		return ""
	}
	return *u.SubscriptionsURL
}

// GetType returns the Type field if it's non-nil, zero value otherwise.
func (u *UserLDAPMapping) GetType() string {
	if u == nil || u.Type == nil {
		return ""
	}
	return *u.Type
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (u *UserLDAPMapping) GetURL() string {
	if u == nil || u.URL == nil {
		return ""
	}
	return *u.URL
}

// GetCreatedAt returns the CreatedAt field if it's non-nil, zero value otherwise.
func (u *UserMigration) GetCreatedAt() string {
	if u == nil || u.CreatedAt == nil {
		return ""
	}
	return *u.CreatedAt
}

// GetExcludeAttachments returns the ExcludeAttachments field if it's non-nil, zero value otherwise.
func (u *UserMigration) GetExcludeAttachments() bool {
	if u == nil || u.ExcludeAttachments == nil {
		return false
	}
	return *u.ExcludeAttachments
}

// GetGUID returns the GUID field if it's non-nil, zero value otherwise.
func (u *UserMigration) GetGUID() string {
	if u == nil || u.GUID == nil {
		return ""
	}
	return *u.GUID
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (u *UserMigration) GetID() int64 {
	if u == nil || u.ID == nil {
		return 0
	}
	return *u.ID
}

// GetLockRepositories returns the LockRepositories field if it's non-nil, zero value otherwise.
func (u *UserMigration) GetLockRepositories() bool {
	if u == nil || u.LockRepositories == nil {
		return false
	}
	return *u.LockRepositories
}

// GetState returns the State field if it's non-nil, zero value otherwise.
func (u *UserMigration) GetState() string {
	if u == nil || u.State == nil {
		return ""
	}
	return *u.State
}

// GetUpdatedAt returns the UpdatedAt field if it's non-nil, zero value otherwise.
func (u *UserMigration) GetUpdatedAt() string {
	if u == nil || u.UpdatedAt == nil {
		return ""
	}
	return *u.UpdatedAt
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (u *UserMigration) GetURL() string {
	if u == nil || u.URL == nil {
		return ""
	}
	return *u.URL
}

// GetIncompleteResults returns the IncompleteResults field if it's non-nil, zero value otherwise.
func (u *UsersSearchResult) GetIncompleteResults() bool {
	if u == nil || u.IncompleteResults == nil {
		return false
	}
	return *u.IncompleteResults
}

// GetTotal returns the Total field if it's non-nil, zero value otherwise.
func (u *UsersSearchResult) GetTotal() int {
	if u == nil || u.Total == nil {
		return 0
	}
	return *u.Total
}

// GetAdminUsers returns the AdminUsers field if it's non-nil, zero value otherwise.
func (u *UserStats) GetAdminUsers() int {
	if u == nil || u.AdminUsers == nil {
		return 0
	}
	return *u.AdminUsers
}

// GetSuspendedUsers returns the SuspendedUsers field if it's non-nil, zero value otherwise.
func (u *UserStats) GetSuspendedUsers() int {
	if u == nil || u.SuspendedUsers == nil {
		return 0
	}
	return *u.SuspendedUsers
}

// GetTotalUsers returns the TotalUsers field if it's non-nil, zero value otherwise.
func (u *UserStats) GetTotalUsers() int {
	if u == nil || u.TotalUsers == nil {
		return 0
	}
	return *u.TotalUsers
}

// GetReason returns the Reason field if it's non-nil, zero value otherwise.
func (u *UserSuspendOptions) GetReason() string {
	if u == nil || u.Reason == nil {
		return ""
	}
	return *u.Reason
}

// GetAction returns the Action field if it's non-nil, zero value otherwise.
func (w *WatchEvent) GetAction() string {
	if w == nil || w.Action == nil {
		return ""
	}
	return *w.Action
}

// GetInstallation returns the Installation field.
func (w *WatchEvent) GetInstallation() *Installation {
	if w == nil {
		return nil
	}
	return w.Installation
}

// GetRepo returns the Repo field.
func (w *WatchEvent) GetRepo() *Repository {
	if w == nil {
		return nil
	}
	return w.Repo
}

// GetSender returns the Sender field.
func (w *WatchEvent) GetSender() *User {
	if w == nil {
		return nil
	}
	return w.Sender
}

// GetEmail returns the Email field if it's non-nil, zero value otherwise.
func (w *WebHookAuthor) GetEmail() string {
	if w == nil || w.Email == nil {
		return ""
	}
	return *w.Email
}

// GetName returns the Name field if it's non-nil, zero value otherwise.
func (w *WebHookAuthor) GetName() string {
	if w == nil || w.Name == nil {
		return ""
	}
	return *w.Name
}

// GetUsername returns the Username field if it's non-nil, zero value otherwise.
func (w *WebHookAuthor) GetUsername() string {
	if w == nil || w.Username == nil {
		return ""
	}
	return *w.Username
}

// GetAuthor returns the Author field.
func (w *WebHookCommit) GetAuthor() *WebHookAuthor {
	if w == nil {
		return nil
	}
	return w.Author
}

// GetCommitter returns the Committer field.
func (w *WebHookCommit) GetCommitter() *WebHookAuthor {
	if w == nil {
		return nil
	}
	return w.Committer
}

// GetDistinct returns the Distinct field if it's non-nil, zero value otherwise.
func (w *WebHookCommit) GetDistinct() bool {
	if w == nil || w.Distinct == nil {
		return false
	}
	return *w.Distinct
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (w *WebHookCommit) GetID() string {
	if w == nil || w.ID == nil {
		return ""
	}
	return *w.ID
}

// GetMessage returns the Message field if it's non-nil, zero value otherwise.
func (w *WebHookCommit) GetMessage() string {
	if w == nil || w.Message == nil {
		return ""
	}
	return *w.Message
}

// GetTimestamp returns the Timestamp field if it's non-nil, zero value otherwise.
func (w *WebHookCommit) GetTimestamp() time.Time {
	if w == nil || w.Timestamp == nil {
		return time.Time{}
	}
	return *w.Timestamp
}

// GetAction returns the Action field if it's non-nil, zero value otherwise.
func (w *WebHookPayload) GetAction() string {
	if w == nil || w.Action == nil {
		return ""
	}
	return *w.Action
}

// GetAfter returns the After field if it's non-nil, zero value otherwise.
func (w *WebHookPayload) GetAfter() string {
	if w == nil || w.After == nil {
		return ""
	}
	return *w.After
}

// GetBefore returns the Before field if it's non-nil, zero value otherwise.
func (w *WebHookPayload) GetBefore() string {
	if w == nil || w.Before == nil {
		return ""
	}
	return *w.Before
}

// GetCompare returns the Compare field if it's non-nil, zero value otherwise.
func (w *WebHookPayload) GetCompare() string {
	if w == nil || w.Compare == nil {
		return ""
	}
	return *w.Compare
}

// GetCreated returns the Created field if it's non-nil, zero value otherwise.
func (w *WebHookPayload) GetCreated() bool {
	if w == nil || w.Created == nil {
		return false
	}
	return *w.Created
}

// GetDeleted returns the Deleted field if it's non-nil, zero value otherwise.
func (w *WebHookPayload) GetDeleted() bool {
	if w == nil || w.Deleted == nil {
		return false
	}
	return *w.Deleted
}

// GetForced returns the Forced field if it's non-nil, zero value otherwise.
func (w *WebHookPayload) GetForced() bool {
	if w == nil || w.Forced == nil {
		return false
	}
	return *w.Forced
}

// GetHeadCommit returns the HeadCommit field.
func (w *WebHookPayload) GetHeadCommit() *WebHookCommit {
	if w == nil {
		return nil
	}
	return w.HeadCommit
}

// GetInstallation returns the Installation field.
func (w *WebHookPayload) GetInstallation() *Installation {
	if w == nil {
		return nil
	}
	return w.Installation
}

// GetOrganization returns the Organization field.
func (w *WebHookPayload) GetOrganization() *Organization {
	if w == nil {
		return nil
	}
	return w.Organization
}

// GetPusher returns the Pusher field.
func (w *WebHookPayload) GetPusher() *User {
	if w == nil {
		return nil
	}
	return w.Pusher
}

// GetRef returns the Ref field if it's non-nil, zero value otherwise.
func (w *WebHookPayload) GetRef() string {
	if w == nil || w.Ref == nil {
		return ""
	}
	return *w.Ref
}

// GetRepo returns the Repo field.
func (w *WebHookPayload) GetRepo() *Repository {
	if w == nil {
		return nil
	}
	return w.Repo
}

// GetSender returns the Sender field.
func (w *WebHookPayload) GetSender() *User {
	if w == nil {
		return nil
	}
	return w.Sender
}

// GetTotal returns the Total field if it's non-nil, zero value otherwise.
func (w *WeeklyCommitActivity) GetTotal() int {
	if w == nil || w.Total == nil {
		return 0
	}
	return *w.Total
}

// GetWeek returns the Week field if it's non-nil, zero value otherwise.
func (w *WeeklyCommitActivity) GetWeek() Timestamp {
	if w == nil || w.Week == nil {
		return Timestamp{}
	}
	return *w.Week
}

// GetAdditions returns the Additions field if it's non-nil, zero value otherwise.
func (w *WeeklyStats) GetAdditions() int {
	if w == nil || w.Additions == nil {
		return 0
	}
	return *w.Additions
}

// GetCommits returns the Commits field if it's non-nil, zero value otherwise.
func (w *WeeklyStats) GetCommits() int {
	if w == nil || w.Commits == nil {
		return 0
	}
	return *w.Commits
}

// GetDeletions returns the Deletions field if it's non-nil, zero value otherwise.
func (w *WeeklyStats) GetDeletions() int {
	if w == nil || w.Deletions == nil {
		return 0
	}
	return *w.Deletions
}

// GetWeek returns the Week field if it's non-nil, zero value otherwise.
func (w *WeeklyStats) GetWeek() Timestamp {
	if w == nil || w.Week == nil {
		return Timestamp{}
	}
	return *w.Week
}

// GetBadgeURL returns the BadgeURL field if it's non-nil, zero value otherwise.
func (w *Workflow) GetBadgeURL() string {
	if w == nil || w.BadgeURL == nil {
		return ""
	}
	return *w.BadgeURL
}

// GetCreatedAt returns the CreatedAt field if it's non-nil, zero value otherwise.
func (w *Workflow) GetCreatedAt() Timestamp {
	if w == nil || w.CreatedAt == nil {
		return Timestamp{}
	}
	return *w.CreatedAt
}

// GetHTMLURL returns the HTMLURL field if it's non-nil, zero value otherwise.
func (w *Workflow) GetHTMLURL() string {
	if w == nil || w.HTMLURL == nil {
		return ""
	}
	return *w.HTMLURL
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (w *Workflow) GetID() int64 {
	if w == nil || w.ID == nil {
		return 0
	}
	return *w.ID
}

// GetName returns the Name field if it's non-nil, zero value otherwise.
func (w *Workflow) GetName() string {
	if w == nil || w.Name == nil {
		return ""
	}
	return *w.Name
}

// GetNodeID returns the NodeID field if it's non-nil, zero value otherwise.
func (w *Workflow) GetNodeID() string {
	if w == nil || w.NodeID == nil {
		return ""
	}
	return *w.NodeID
}

// GetPath returns the Path field if it's non-nil, zero value otherwise.
func (w *Workflow) GetPath() string {
	if w == nil || w.Path == nil {
		return ""
	}
	return *w.Path
}

// GetState returns the State field if it's non-nil, zero value otherwise.
func (w *Workflow) GetState() string {
	if w == nil || w.State == nil {
		return ""
	}
	return *w.State
}

// GetUpdatedAt returns the UpdatedAt field if it's non-nil, zero value otherwise.
func (w *Workflow) GetUpdatedAt() Timestamp {
	if w == nil || w.UpdatedAt == nil {
		return Timestamp{}
	}
	return *w.UpdatedAt
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (w *Workflow) GetURL() string {
	if w == nil || w.URL == nil {
		return ""
	}
	return *w.URL
}

// GetTotalMS returns the TotalMS field if it's non-nil, zero value otherwise.
func (w *WorkflowBill) GetTotalMS() int64 {
	if w == nil || w.TotalMS == nil {
		return 0
	}
	return *w.TotalMS
}

// GetOrg returns the Org field.
func (w *WorkflowDispatchEvent) GetOrg() *Organization {
	if w == nil {
		return nil
	}
	return w.Org
}

// GetRef returns the Ref field if it's non-nil, zero value otherwise.
func (w *WorkflowDispatchEvent) GetRef() string {
	if w == nil || w.Ref == nil {
		return ""
	}
	return *w.Ref
}

// GetRepo returns the Repo field.
func (w *WorkflowDispatchEvent) GetRepo() *Repository {
	if w == nil {
		return nil
	}
	return w.Repo
}

// GetSender returns the Sender field.
func (w *WorkflowDispatchEvent) GetSender() *User {
	if w == nil {
		return nil
	}
	return w.Sender
}

// GetWorkflow returns the Workflow field if it's non-nil, zero value otherwise.
func (w *WorkflowDispatchEvent) GetWorkflow() string {
	if w == nil || w.Workflow == nil {
		return ""
	}
	return *w.Workflow
}

// GetMacOS returns the MacOS field.
func (w *WorkflowEnvironment) GetMacOS() *WorkflowBill {
	if w == nil {
		return nil
	}
	return w.MacOS
}

// GetUbuntu returns the Ubuntu field.
func (w *WorkflowEnvironment) GetUbuntu() *WorkflowBill {
	if w == nil {
		return nil
	}
	return w.Ubuntu
}

// GetWindows returns the Windows field.
func (w *WorkflowEnvironment) GetWindows() *WorkflowBill {
	if w == nil {
		return nil
	}
	return w.Windows
}

// GetCheckRunURL returns the CheckRunURL field if it's non-nil, zero value otherwise.
func (w *WorkflowJob) GetCheckRunURL() string {
	if w == nil || w.CheckRunURL == nil {
		return ""
	}
	return *w.CheckRunURL
}

// GetCompletedAt returns the CompletedAt field if it's non-nil, zero value otherwise.
func (w *WorkflowJob) GetCompletedAt() Timestamp {
	if w == nil || w.CompletedAt == nil {
		return Timestamp{}
	}
	return *w.CompletedAt
}

// GetConclusion returns the Conclusion field if it's non-nil, zero value otherwise.
func (w *WorkflowJob) GetConclusion() string {
	if w == nil || w.Conclusion == nil {
		return ""
	}
	return *w.Conclusion
}

// GetHeadSHA returns the HeadSHA field if it's non-nil, zero value otherwise.
func (w *WorkflowJob) GetHeadSHA() string {
	if w == nil || w.HeadSHA == nil {
		return ""
	}
	return *w.HeadSHA
}

// GetHTMLURL returns the HTMLURL field if it's non-nil, zero value otherwise.
func (w *WorkflowJob) GetHTMLURL() string {
	if w == nil || w.HTMLURL == nil {
		return ""
	}
	return *w.HTMLURL
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (w *WorkflowJob) GetID() int64 {
	if w == nil || w.ID == nil {
		return 0
	}
	return *w.ID
}

// GetName returns the Name field if it's non-nil, zero value otherwise.
func (w *WorkflowJob) GetName() string {
	if w == nil || w.Name == nil {
		return ""
	}
	return *w.Name
}

// GetNodeID returns the NodeID field if it's non-nil, zero value otherwise.
func (w *WorkflowJob) GetNodeID() string {
	if w == nil || w.NodeID == nil {
		return ""
	}
	return *w.NodeID
}

// GetRunID returns the RunID field if it's non-nil, zero value otherwise.
func (w *WorkflowJob) GetRunID() int64 {
	if w == nil || w.RunID == nil {
		return 0
	}
	return *w.RunID
}

// GetRunURL returns the RunURL field if it's non-nil, zero value otherwise.
func (w *WorkflowJob) GetRunURL() string {
	if w == nil || w.RunURL == nil {
		return ""
	}
	return *w.RunURL
}

// GetStartedAt returns the StartedAt field if it's non-nil, zero value otherwise.
func (w *WorkflowJob) GetStartedAt() Timestamp {
	if w == nil || w.StartedAt == nil {
		return Timestamp{}
	}
	return *w.StartedAt
}

// GetStatus returns the Status field if it's non-nil, zero value otherwise.
func (w *WorkflowJob) GetStatus() string {
	if w == nil || w.Status == nil {
		return ""
	}
	return *w.Status
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (w *WorkflowJob) GetURL() string {
	if w == nil || w.URL == nil {
		return ""
	}
	return *w.URL
}

// GetArtifactsURL returns the ArtifactsURL field if it's non-nil, zero value otherwise.
func (w *WorkflowRun) GetArtifactsURL() string {
	if w == nil || w.ArtifactsURL == nil {
		return ""
	}
	return *w.ArtifactsURL
}

// GetCancelURL returns the CancelURL field if it's non-nil, zero value otherwise.
func (w *WorkflowRun) GetCancelURL() string {
	if w == nil || w.CancelURL == nil {
		return ""
	}
	return *w.CancelURL
}

// GetCheckSuiteURL returns the CheckSuiteURL field if it's non-nil, zero value otherwise.
func (w *WorkflowRun) GetCheckSuiteURL() string {
	if w == nil || w.CheckSuiteURL == nil {
		return ""
	}
	return *w.CheckSuiteURL
}

// GetConclusion returns the Conclusion field if it's non-nil, zero value otherwise.
func (w *WorkflowRun) GetConclusion() string {
	if w == nil || w.Conclusion == nil {
		return ""
	}
	return *w.Conclusion
}

// GetCreatedAt returns the CreatedAt field if it's non-nil, zero value otherwise.
func (w *WorkflowRun) GetCreatedAt() Timestamp {
	if w == nil || w.CreatedAt == nil {
		return Timestamp{}
	}
	return *w.CreatedAt
}

// GetEvent returns the Event field if it's non-nil, zero value otherwise.
func (w *WorkflowRun) GetEvent() string {
	if w == nil || w.Event == nil {
		return ""
	}
	return *w.Event
}

// GetHeadBranch returns the HeadBranch field if it's non-nil, zero value otherwise.
func (w *WorkflowRun) GetHeadBranch() string {
	if w == nil || w.HeadBranch == nil {
		return ""
	}
	return *w.HeadBranch
}

// GetHeadCommit returns the HeadCommit field.
func (w *WorkflowRun) GetHeadCommit() *HeadCommit {
	if w == nil {
		return nil
	}
	return w.HeadCommit
}

// GetHeadRepository returns the HeadRepository field.
func (w *WorkflowRun) GetHeadRepository() *Repository {
	if w == nil {
		return nil
	}
	return w.HeadRepository
}

// GetHeadSHA returns the HeadSHA field if it's non-nil, zero value otherwise.
func (w *WorkflowRun) GetHeadSHA() string {
	if w == nil || w.HeadSHA == nil {
		return ""
	}
	return *w.HeadSHA
}

// GetHTMLURL returns the HTMLURL field if it's non-nil, zero value otherwise.
func (w *WorkflowRun) GetHTMLURL() string {
	if w == nil || w.HTMLURL == nil {
		return ""
	}
	return *w.HTMLURL
}

// GetID returns the ID field if it's non-nil, zero value otherwise.
func (w *WorkflowRun) GetID() int64 {
	if w == nil || w.ID == nil {
		return 0
	}
	return *w.ID
}

// GetJobsURL returns the JobsURL field if it's non-nil, zero value otherwise.
func (w *WorkflowRun) GetJobsURL() string {
	if w == nil || w.JobsURL == nil {
		return ""
	}
	return *w.JobsURL
}

// GetLogsURL returns the LogsURL field if it's non-nil, zero value otherwise.
func (w *WorkflowRun) GetLogsURL() string {
	if w == nil || w.LogsURL == nil {
		return ""
	}
	return *w.LogsURL
}

// GetName returns the Name field if it's non-nil, zero value otherwise.
func (w *WorkflowRun) GetName() string {
	if w == nil || w.Name == nil {
		return ""
	}
	return *w.Name
}

// GetNodeID returns the NodeID field if it's non-nil, zero value otherwise.
func (w *WorkflowRun) GetNodeID() string {
	if w == nil || w.NodeID == nil {
		return ""
	}
	return *w.NodeID
}

// GetRepository returns the Repository field.
func (w *WorkflowRun) GetRepository() *Repository {
	if w == nil {
		return nil
	}
	return w.Repository
}

// GetRerunURL returns the RerunURL field if it's non-nil, zero value otherwise.
func (w *WorkflowRun) GetRerunURL() string {
	if w == nil || w.RerunURL == nil {
		return ""
	}
	return *w.RerunURL
}

// GetRunNumber returns the RunNumber field if it's non-nil, zero value otherwise.
func (w *WorkflowRun) GetRunNumber() int {
	if w == nil || w.RunNumber == nil {
		return 0
	}
	return *w.RunNumber
}

// GetStatus returns the Status field if it's non-nil, zero value otherwise.
func (w *WorkflowRun) GetStatus() string {
	if w == nil || w.Status == nil {
		return ""
	}
	return *w.Status
}

// GetUpdatedAt returns the UpdatedAt field if it's non-nil, zero value otherwise.
func (w *WorkflowRun) GetUpdatedAt() Timestamp {
	if w == nil || w.UpdatedAt == nil {
		return Timestamp{}
	}
	return *w.UpdatedAt
}

// GetURL returns the URL field if it's non-nil, zero value otherwise.
func (w *WorkflowRun) GetURL() string {
	if w == nil || w.URL == nil {
		return ""
	}
	return *w.URL
}

// GetWorkflowID returns the WorkflowID field if it's non-nil, zero value otherwise.
func (w *WorkflowRun) GetWorkflowID() int64 {
	if w == nil || w.WorkflowID == nil {
		return 0
	}
	return *w.WorkflowID
}

// GetWorkflowURL returns the WorkflowURL field if it's non-nil, zero value otherwise.
func (w *WorkflowRun) GetWorkflowURL() string {
	if w == nil || w.WorkflowURL == nil {
		return ""
	}
	return *w.WorkflowURL
}

// GetJobs returns the Jobs field if it's non-nil, zero value otherwise.
func (w *WorkflowRunBill) GetJobs() int {
	if w == nil || w.Jobs == nil {
		return 0
	}
	return *w.Jobs
}

// GetTotalMS returns the TotalMS field if it's non-nil, zero value otherwise.
func (w *WorkflowRunBill) GetTotalMS() int64 {
	if w == nil || w.TotalMS == nil {
		return 0
	}
	return *w.TotalMS
}

// GetMacOS returns the MacOS field.
func (w *WorkflowRunEnvironment) GetMacOS() *WorkflowRunBill {
	if w == nil {
		return nil
	}
	return w.MacOS
}

// GetUbuntu returns the Ubuntu field.
func (w *WorkflowRunEnvironment) GetUbuntu() *WorkflowRunBill {
	if w == nil {
		return nil
	}
	return w.Ubuntu
}

// GetWindows returns the Windows field.
func (w *WorkflowRunEnvironment) GetWindows() *WorkflowRunBill {
	if w == nil {
		return nil
	}
	return w.Windows
}

// GetAction returns the Action field if it's non-nil, zero value otherwise.
func (w *WorkflowRunEvent) GetAction() string {
	if w == nil || w.Action == nil {
		return ""
	}
	return *w.Action
}

// GetOrg returns the Org field.
func (w *WorkflowRunEvent) GetOrg() *Organization {
	if w == nil {
		return nil
	}
	return w.Org
}

// GetRepo returns the Repo field.
func (w *WorkflowRunEvent) GetRepo() *Repository {
	if w == nil {
		return nil
	}
	return w.Repo
}

// GetSender returns the Sender field.
func (w *WorkflowRunEvent) GetSender() *User {
	if w == nil {
		return nil
	}
	return w.Sender
}

// GetWorkflow returns the Workflow field.
func (w *WorkflowRunEvent) GetWorkflow() *Workflow {
	if w == nil {
		return nil
	}
	return w.Workflow
}

// GetWorkflowRun returns the WorkflowRun field.
func (w *WorkflowRunEvent) GetWorkflowRun() *WorkflowRun {
	if w == nil {
		return nil
	}
	return w.WorkflowRun
}

// GetTotalCount returns the TotalCount field if it's non-nil, zero value otherwise.
func (w *WorkflowRuns) GetTotalCount() int {
	if w == nil || w.TotalCount == nil {
		return 0
	}
	return *w.TotalCount
}

// GetBillable returns the Billable field.
func (w *WorkflowRunUsage) GetBillable() *WorkflowRunEnvironment {
	if w == nil {
		return nil
	}
	return w.Billable
}

// GetRunDurationMS returns the RunDurationMS field if it's non-nil, zero value otherwise.
func (w *WorkflowRunUsage) GetRunDurationMS() int64 {
	if w == nil || w.RunDurationMS == nil {
		return 0
	}
	return *w.RunDurationMS
}

// GetTotalCount returns the TotalCount field if it's non-nil, zero value otherwise.
func (w *Workflows) GetTotalCount() int {
	if w == nil || w.TotalCount == nil {
		return 0
	}
	return *w.TotalCount
}

// GetBillable returns the Billable field.
func (w *WorkflowUsage) GetBillable() *WorkflowEnvironment {
	if w == nil {
		return nil
	}
	return w.Billable
}
