// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v4.22.2
// source: validate/validate.proto

package validate

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	descriptorpb "google.golang.org/protobuf/types/descriptorpb"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// WellKnownRegex contain some well-known patterns.
type KnownRegex int32

const (
	KnownRegex_UNKNOWN KnownRegex = 0
	// HTTP header name as defined by RFC 7230.
	KnownRegex_HTTP_HEADER_NAME KnownRegex = 1
	// HTTP header value as defined by RFC 7230.
	KnownRegex_HTTP_HEADER_VALUE KnownRegex = 2
)

// Enum value maps for KnownRegex.
var (
	KnownRegex_name = map[int32]string{
		0: "UNKNOWN",
		1: "HTTP_HEADER_NAME",
		2: "HTTP_HEADER_VALUE",
	}
	KnownRegex_value = map[string]int32{
		"UNKNOWN":           0,
		"HTTP_HEADER_NAME":  1,
		"HTTP_HEADER_VALUE": 2,
	}
)

func (x KnownRegex) Enum() *KnownRegex {
	p := new(KnownRegex)
	*p = x
	return p
}

func (x KnownRegex) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (KnownRegex) Descriptor() protoreflect.EnumDescriptor {
	return file_validate_validate_proto_enumTypes[0].Descriptor()
}

func (KnownRegex) Type() protoreflect.EnumType {
	return &file_validate_validate_proto_enumTypes[0]
}

func (x KnownRegex) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *KnownRegex) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = KnownRegex(num)
	return nil
}

// Deprecated: Use KnownRegex.Descriptor instead.
func (KnownRegex) EnumDescriptor() ([]byte, []int) {
	return file_validate_validate_proto_rawDescGZIP(), []int{0}
}

// FieldRules encapsulates the rules for each type of field. Depending on the
// field, the correct set should be used to ensure proper validations.
type FieldRules struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Message *MessageRules `protobuf:"bytes,17,opt,name=message" json:"message,omitempty"`
	// Types that are assignable to Type:
	//
	//	*FieldRules_Float
	//	*FieldRules_Double
	//	*FieldRules_Int32
	//	*FieldRules_Int64
	//	*FieldRules_Uint32
	//	*FieldRules_Uint64
	//	*FieldRules_Sint32
	//	*FieldRules_Sint64
	//	*FieldRules_Fixed32
	//	*FieldRules_Fixed64
	//	*FieldRules_Sfixed32
	//	*FieldRules_Sfixed64
	//	*FieldRules_Bool
	//	*FieldRules_String_
	//	*FieldRules_Bytes
	//	*FieldRules_Enum
	//	*FieldRules_Repeated
	//	*FieldRules_Map
	//	*FieldRules_Any
	//	*FieldRules_Duration
	//	*FieldRules_Timestamp
	Type isFieldRules_Type `protobuf_oneof:"type"`
}

func (x *FieldRules) Reset() {
	*x = FieldRules{}
	if protoimpl.UnsafeEnabled {
		mi := &file_validate_validate_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FieldRules) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FieldRules) ProtoMessage() {}

func (x *FieldRules) ProtoReflect() protoreflect.Message {
	mi := &file_validate_validate_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FieldRules.ProtoReflect.Descriptor instead.
func (*FieldRules) Descriptor() ([]byte, []int) {
	return file_validate_validate_proto_rawDescGZIP(), []int{0}
}

func (x *FieldRules) GetMessage() *MessageRules {
	if x != nil {
		return x.Message
	}
	return nil
}

func (m *FieldRules) GetType() isFieldRules_Type {
	if m != nil {
		return m.Type
	}
	return nil
}

func (x *FieldRules) GetFloat() *FloatRules {
	if x, ok := x.GetType().(*FieldRules_Float); ok {
		return x.Float
	}
	return nil
}

func (x *FieldRules) GetDouble() *DoubleRules {
	if x, ok := x.GetType().(*FieldRules_Double); ok {
		return x.Double
	}
	return nil
}

func (x *FieldRules) GetInt32() *Int32Rules {
	if x, ok := x.GetType().(*FieldRules_Int32); ok {
		return x.Int32
	}
	return nil
}

func (x *FieldRules) GetInt64() *Int64Rules {
	if x, ok := x.GetType().(*FieldRules_Int64); ok {
		return x.Int64
	}
	return nil
}

func (x *FieldRules) GetUint32() *UInt32Rules {
	if x, ok := x.GetType().(*FieldRules_Uint32); ok {
		return x.Uint32
	}
	return nil
}

func (x *FieldRules) GetUint64() *UInt64Rules {
	if x, ok := x.GetType().(*FieldRules_Uint64); ok {
		return x.Uint64
	}
	return nil
}

func (x *FieldRules) GetSint32() *SInt32Rules {
	if x, ok := x.GetType().(*FieldRules_Sint32); ok {
		return x.Sint32
	}
	return nil
}

func (x *FieldRules) GetSint64() *SInt64Rules {
	if x, ok := x.GetType().(*FieldRules_Sint64); ok {
		return x.Sint64
	}
	return nil
}

func (x *FieldRules) GetFixed32() *Fixed32Rules {
	if x, ok := x.GetType().(*FieldRules_Fixed32); ok {
		return x.Fixed32
	}
	return nil
}

func (x *FieldRules) GetFixed64() *Fixed64Rules {
	if x, ok := x.GetType().(*FieldRules_Fixed64); ok {
		return x.Fixed64
	}
	return nil
}

func (x *FieldRules) GetSfixed32() *SFixed32Rules {
	if x, ok := x.GetType().(*FieldRules_Sfixed32); ok {
		return x.Sfixed32
	}
	return nil
}

func (x *FieldRules) GetSfixed64() *SFixed64Rules {
	if x, ok := x.GetType().(*FieldRules_Sfixed64); ok {
		return x.Sfixed64
	}
	return nil
}

func (x *FieldRules) GetBool() *BoolRules {
	if x, ok := x.GetType().(*FieldRules_Bool); ok {
		return x.Bool
	}
	return nil
}

func (x *FieldRules) GetString_() *StringRules {
	if x, ok := x.GetType().(*FieldRules_String_); ok {
		return x.String_
	}
	return nil
}

func (x *FieldRules) GetBytes() *BytesRules {
	if x, ok := x.GetType().(*FieldRules_Bytes); ok {
		return x.Bytes
	}
	return nil
}

func (x *FieldRules) GetEnum() *EnumRules {
	if x, ok := x.GetType().(*FieldRules_Enum); ok {
		return x.Enum
	}
	return nil
}

func (x *FieldRules) GetRepeated() *RepeatedRules {
	if x, ok := x.GetType().(*FieldRules_Repeated); ok {
		return x.Repeated
	}
	return nil
}

func (x *FieldRules) GetMap() *MapRules {
	if x, ok := x.GetType().(*FieldRules_Map); ok {
		return x.Map
	}
	return nil
}

func (x *FieldRules) GetAny() *AnyRules {
	if x, ok := x.GetType().(*FieldRules_Any); ok {
		return x.Any
	}
	return nil
}

func (x *FieldRules) GetDuration() *DurationRules {
	if x, ok := x.GetType().(*FieldRules_Duration); ok {
		return x.Duration
	}
	return nil
}

func (x *FieldRules) GetTimestamp() *TimestampRules {
	if x, ok := x.GetType().(*FieldRules_Timestamp); ok {
		return x.Timestamp
	}
	return nil
}

type isFieldRules_Type interface {
	isFieldRules_Type()
}

type FieldRules_Float struct {
	// Scalar Field Types
	Float *FloatRules `protobuf:"bytes,1,opt,name=float,oneof"`
}

type FieldRules_Double struct {
	Double *DoubleRules `protobuf:"bytes,2,opt,name=double,oneof"`
}

type FieldRules_Int32 struct {
	Int32 *Int32Rules `protobuf:"bytes,3,opt,name=int32,oneof"`
}

type FieldRules_Int64 struct {
	Int64 *Int64Rules `protobuf:"bytes,4,opt,name=int64,oneof"`
}

type FieldRules_Uint32 struct {
	Uint32 *UInt32Rules `protobuf:"bytes,5,opt,name=uint32,oneof"`
}

type FieldRules_Uint64 struct {
	Uint64 *UInt64Rules `protobuf:"bytes,6,opt,name=uint64,oneof"`
}

type FieldRules_Sint32 struct {
	Sint32 *SInt32Rules `protobuf:"bytes,7,opt,name=sint32,oneof"`
}

type FieldRules_Sint64 struct {
	Sint64 *SInt64Rules `protobuf:"bytes,8,opt,name=sint64,oneof"`
}

type FieldRules_Fixed32 struct {
	Fixed32 *Fixed32Rules `protobuf:"bytes,9,opt,name=fixed32,oneof"`
}

type FieldRules_Fixed64 struct {
	Fixed64 *Fixed64Rules `protobuf:"bytes,10,opt,name=fixed64,oneof"`
}

type FieldRules_Sfixed32 struct {
	Sfixed32 *SFixed32Rules `protobuf:"bytes,11,opt,name=sfixed32,oneof"`
}

type FieldRules_Sfixed64 struct {
	Sfixed64 *SFixed64Rules `protobuf:"bytes,12,opt,name=sfixed64,oneof"`
}

type FieldRules_Bool struct {
	Bool *BoolRules `protobuf:"bytes,13,opt,name=bool,oneof"`
}

type FieldRules_String_ struct {
	String_ *StringRules `protobuf:"bytes,14,opt,name=string,oneof"`
}

type FieldRules_Bytes struct {
	Bytes *BytesRules `protobuf:"bytes,15,opt,name=bytes,oneof"`
}

type FieldRules_Enum struct {
	// Complex Field Types
	Enum *EnumRules `protobuf:"bytes,16,opt,name=enum,oneof"`
}

type FieldRules_Repeated struct {
	Repeated *RepeatedRules `protobuf:"bytes,18,opt,name=repeated,oneof"`
}

type FieldRules_Map struct {
	Map *MapRules `protobuf:"bytes,19,opt,name=map,oneof"`
}

type FieldRules_Any struct {
	// Well-Known Field Types
	Any *AnyRules `protobuf:"bytes,20,opt,name=any,oneof"`
}

type FieldRules_Duration struct {
	Duration *DurationRules `protobuf:"bytes,21,opt,name=duration,oneof"`
}

type FieldRules_Timestamp struct {
	Timestamp *TimestampRules `protobuf:"bytes,22,opt,name=timestamp,oneof"`
}

func (*FieldRules_Float) isFieldRules_Type() {}

func (*FieldRules_Double) isFieldRules_Type() {}

func (*FieldRules_Int32) isFieldRules_Type() {}

func (*FieldRules_Int64) isFieldRules_Type() {}

func (*FieldRules_Uint32) isFieldRules_Type() {}

func (*FieldRules_Uint64) isFieldRules_Type() {}

func (*FieldRules_Sint32) isFieldRules_Type() {}

func (*FieldRules_Sint64) isFieldRules_Type() {}

func (*FieldRules_Fixed32) isFieldRules_Type() {}

func (*FieldRules_Fixed64) isFieldRules_Type() {}

func (*FieldRules_Sfixed32) isFieldRules_Type() {}

func (*FieldRules_Sfixed64) isFieldRules_Type() {}

func (*FieldRules_Bool) isFieldRules_Type() {}

func (*FieldRules_String_) isFieldRules_Type() {}

func (*FieldRules_Bytes) isFieldRules_Type() {}

func (*FieldRules_Enum) isFieldRules_Type() {}

func (*FieldRules_Repeated) isFieldRules_Type() {}

func (*FieldRules_Map) isFieldRules_Type() {}

func (*FieldRules_Any) isFieldRules_Type() {}

func (*FieldRules_Duration) isFieldRules_Type() {}

func (*FieldRules_Timestamp) isFieldRules_Type() {}

// FloatRules describes the constraints applied to `float` values
type FloatRules struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Const specifies that this field must be exactly the specified value
	Const *float32 `protobuf:"fixed32,1,opt,name=const" json:"const,omitempty"`
	// Lt specifies that this field must be less than the specified value,
	// exclusive
	Lt *float32 `protobuf:"fixed32,2,opt,name=lt" json:"lt,omitempty"`
	// Lte specifies that this field must be less than or equal to the
	// specified value, inclusive
	Lte *float32 `protobuf:"fixed32,3,opt,name=lte" json:"lte,omitempty"`
	// Gt specifies that this field must be greater than the specified value,
	// exclusive. If the value of Gt is larger than a specified Lt or Lte, the
	// range is reversed.
	Gt *float32 `protobuf:"fixed32,4,opt,name=gt" json:"gt,omitempty"`
	// Gte specifies that this field must be greater than or equal to the
	// specified value, inclusive. If the value of Gte is larger than a
	// specified Lt or Lte, the range is reversed.
	Gte *float32 `protobuf:"fixed32,5,opt,name=gte" json:"gte,omitempty"`
	// In specifies that this field must be equal to one of the specified
	// values
	In []float32 `protobuf:"fixed32,6,rep,name=in" json:"in,omitempty"`
	// NotIn specifies that this field cannot be equal to one of the specified
	// values
	NotIn []float32 `protobuf:"fixed32,7,rep,name=not_in,json=notIn" json:"not_in,omitempty"`
	// IgnoreEmpty specifies that the validation rules of this field should be
	// evaluated only if the field is not empty
	IgnoreEmpty *bool `protobuf:"varint,8,opt,name=ignore_empty,json=ignoreEmpty" json:"ignore_empty,omitempty"`
}

func (x *FloatRules) Reset() {
	*x = FloatRules{}
	if protoimpl.UnsafeEnabled {
		mi := &file_validate_validate_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FloatRules) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FloatRules) ProtoMessage() {}

func (x *FloatRules) ProtoReflect() protoreflect.Message {
	mi := &file_validate_validate_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FloatRules.ProtoReflect.Descriptor instead.
func (*FloatRules) Descriptor() ([]byte, []int) {
	return file_validate_validate_proto_rawDescGZIP(), []int{1}
}

func (x *FloatRules) GetConst() float32 {
	if x != nil && x.Const != nil {
		return *x.Const
	}
	return 0
}

func (x *FloatRules) GetLt() float32 {
	if x != nil && x.Lt != nil {
		return *x.Lt
	}
	return 0
}

func (x *FloatRules) GetLte() float32 {
	if x != nil && x.Lte != nil {
		return *x.Lte
	}
	return 0
}

func (x *FloatRules) GetGt() float32 {
	if x != nil && x.Gt != nil {
		return *x.Gt
	}
	return 0
}

func (x *FloatRules) GetGte() float32 {
	if x != nil && x.Gte != nil {
		return *x.Gte
	}
	return 0
}

func (x *FloatRules) GetIn() []float32 {
	if x != nil {
		return x.In
	}
	return nil
}

func (x *FloatRules) GetNotIn() []float32 {
	if x != nil {
		return x.NotIn
	}
	return nil
}

func (x *FloatRules) GetIgnoreEmpty() bool {
	if x != nil && x.IgnoreEmpty != nil {
		return *x.IgnoreEmpty
	}
	return false
}

// DoubleRules describes the constraints applied to `double` values
type DoubleRules struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Const specifies that this field must be exactly the specified value
	Const *float64 `protobuf:"fixed64,1,opt,name=const" json:"const,omitempty"`
	// Lt specifies that this field must be less than the specified value,
	// exclusive
	Lt *float64 `protobuf:"fixed64,2,opt,name=lt" json:"lt,omitempty"`
	// Lte specifies that this field must be less than or equal to the
	// specified value, inclusive
	Lte *float64 `protobuf:"fixed64,3,opt,name=lte" json:"lte,omitempty"`
	// Gt specifies that this field must be greater than the specified value,
	// exclusive. If the value of Gt is larger than a specified Lt or Lte, the
	// range is reversed.
	Gt *float64 `protobuf:"fixed64,4,opt,name=gt" json:"gt,omitempty"`
	// Gte specifies that this field must be greater than or equal to the
	// specified value, inclusive. If the value of Gte is larger than a
	// specified Lt or Lte, the range is reversed.
	Gte *float64 `protobuf:"fixed64,5,opt,name=gte" json:"gte,omitempty"`
	// In specifies that this field must be equal to one of the specified
	// values
	In []float64 `protobuf:"fixed64,6,rep,name=in" json:"in,omitempty"`
	// NotIn specifies that this field cannot be equal to one of the specified
	// values
	NotIn []float64 `protobuf:"fixed64,7,rep,name=not_in,json=notIn" json:"not_in,omitempty"`
	// IgnoreEmpty specifies that the validation rules of this field should be
	// evaluated only if the field is not empty
	IgnoreEmpty *bool `protobuf:"varint,8,opt,name=ignore_empty,json=ignoreEmpty" json:"ignore_empty,omitempty"`
}

func (x *DoubleRules) Reset() {
	*x = DoubleRules{}
	if protoimpl.UnsafeEnabled {
		mi := &file_validate_validate_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DoubleRules) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoubleRules) ProtoMessage() {}

func (x *DoubleRules) ProtoReflect() protoreflect.Message {
	mi := &file_validate_validate_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoubleRules.ProtoReflect.Descriptor instead.
func (*DoubleRules) Descriptor() ([]byte, []int) {
	return file_validate_validate_proto_rawDescGZIP(), []int{2}
}

func (x *DoubleRules) GetConst() float64 {
	if x != nil && x.Const != nil {
		return *x.Const
	}
	return 0
}

func (x *DoubleRules) GetLt() float64 {
	if x != nil && x.Lt != nil {
		return *x.Lt
	}
	return 0
}

func (x *DoubleRules) GetLte() float64 {
	if x != nil && x.Lte != nil {
		return *x.Lte
	}
	return 0
}

func (x *DoubleRules) GetGt() float64 {
	if x != nil && x.Gt != nil {
		return *x.Gt
	}
	return 0
}

func (x *DoubleRules) GetGte() float64 {
	if x != nil && x.Gte != nil {
		return *x.Gte
	}
	return 0
}

func (x *DoubleRules) GetIn() []float64 {
	if x != nil {
		return x.In
	}
	return nil
}

func (x *DoubleRules) GetNotIn() []float64 {
	if x != nil {
		return x.NotIn
	}
	return nil
}

func (x *DoubleRules) GetIgnoreEmpty() bool {
	if x != nil && x.IgnoreEmpty != nil {
		return *x.IgnoreEmpty
	}
	return false
}

// Int32Rules describes the constraints applied to `int32` values
type Int32Rules struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Const specifies that this field must be exactly the specified value
	Const *int32 `protobuf:"varint,1,opt,name=const" json:"const,omitempty"`
	// Lt specifies that this field must be less than the specified value,
	// exclusive
	Lt *int32 `protobuf:"varint,2,opt,name=lt" json:"lt,omitempty"`
	// Lte specifies that this field must be less than or equal to the
	// specified value, inclusive
	Lte *int32 `protobuf:"varint,3,opt,name=lte" json:"lte,omitempty"`
	// Gt specifies that this field must be greater than the specified value,
	// exclusive. If the value of Gt is larger than a specified Lt or Lte, the
	// range is reversed.
	Gt *int32 `protobuf:"varint,4,opt,name=gt" json:"gt,omitempty"`
	// Gte specifies that this field must be greater than or equal to the
	// specified value, inclusive. If the value of Gte is larger than a
	// specified Lt or Lte, the range is reversed.
	Gte *int32 `protobuf:"varint,5,opt,name=gte" json:"gte,omitempty"`
	// In specifies that this field must be equal to one of the specified
	// values
	In []int32 `protobuf:"varint,6,rep,name=in" json:"in,omitempty"`
	// NotIn specifies that this field cannot be equal to one of the specified
	// values
	NotIn []int32 `protobuf:"varint,7,rep,name=not_in,json=notIn" json:"not_in,omitempty"`
	// IgnoreEmpty specifies that the validation rules of this field should be
	// evaluated only if the field is not empty
	IgnoreEmpty *bool `protobuf:"varint,8,opt,name=ignore_empty,json=ignoreEmpty" json:"ignore_empty,omitempty"`
}

func (x *Int32Rules) Reset() {
	*x = Int32Rules{}
	if protoimpl.UnsafeEnabled {
		mi := &file_validate_validate_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Int32Rules) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Int32Rules) ProtoMessage() {}

func (x *Int32Rules) ProtoReflect() protoreflect.Message {
	mi := &file_validate_validate_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Int32Rules.ProtoReflect.Descriptor instead.
func (*Int32Rules) Descriptor() ([]byte, []int) {
	return file_validate_validate_proto_rawDescGZIP(), []int{3}
}

func (x *Int32Rules) GetConst() int32 {
	if x != nil && x.Const != nil {
		return *x.Const
	}
	return 0
}

func (x *Int32Rules) GetLt() int32 {
	if x != nil && x.Lt != nil {
		return *x.Lt
	}
	return 0
}

func (x *Int32Rules) GetLte() int32 {
	if x != nil && x.Lte != nil {
		return *x.Lte
	}
	return 0
}

func (x *Int32Rules) GetGt() int32 {
	if x != nil && x.Gt != nil {
		return *x.Gt
	}
	return 0
}

func (x *Int32Rules) GetGte() int32 {
	if x != nil && x.Gte != nil {
		return *x.Gte
	}
	return 0
}

func (x *Int32Rules) GetIn() []int32 {
	if x != nil {
		return x.In
	}
	return nil
}

func (x *Int32Rules) GetNotIn() []int32 {
	if x != nil {
		return x.NotIn
	}
	return nil
}

func (x *Int32Rules) GetIgnoreEmpty() bool {
	if x != nil && x.IgnoreEmpty != nil {
		return *x.IgnoreEmpty
	}
	return false
}

// Int64Rules describes the constraints applied to `int64` values
type Int64Rules struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Const specifies that this field must be exactly the specified value
	Const *int64 `protobuf:"varint,1,opt,name=const" json:"const,omitempty"`
	// Lt specifies that this field must be less than the specified value,
	// exclusive
	Lt *int64 `protobuf:"varint,2,opt,name=lt" json:"lt,omitempty"`
	// Lte specifies that this field must be less than or equal to the
	// specified value, inclusive
	Lte *int64 `protobuf:"varint,3,opt,name=lte" json:"lte,omitempty"`
	// Gt specifies that this field must be greater than the specified value,
	// exclusive. If the value of Gt is larger than a specified Lt or Lte, the
	// range is reversed.
	Gt *int64 `protobuf:"varint,4,opt,name=gt" json:"gt,omitempty"`
	// Gte specifies that this field must be greater than or equal to the
	// specified value, inclusive. If the value of Gte is larger than a
	// specified Lt or Lte, the range is reversed.
	Gte *int64 `protobuf:"varint,5,opt,name=gte" json:"gte,omitempty"`
	// In specifies that this field must be equal to one of the specified
	// values
	In []int64 `protobuf:"varint,6,rep,name=in" json:"in,omitempty"`
	// NotIn specifies that this field cannot be equal to one of the specified
	// values
	NotIn []int64 `protobuf:"varint,7,rep,name=not_in,json=notIn" json:"not_in,omitempty"`
	// IgnoreEmpty specifies that the validation rules of this field should be
	// evaluated only if the field is not empty
	IgnoreEmpty *bool `protobuf:"varint,8,opt,name=ignore_empty,json=ignoreEmpty" json:"ignore_empty,omitempty"`
}

func (x *Int64Rules) Reset() {
	*x = Int64Rules{}
	if protoimpl.UnsafeEnabled {
		mi := &file_validate_validate_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Int64Rules) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Int64Rules) ProtoMessage() {}

func (x *Int64Rules) ProtoReflect() protoreflect.Message {
	mi := &file_validate_validate_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Int64Rules.ProtoReflect.Descriptor instead.
func (*Int64Rules) Descriptor() ([]byte, []int) {
	return file_validate_validate_proto_rawDescGZIP(), []int{4}
}

func (x *Int64Rules) GetConst() int64 {
	if x != nil && x.Const != nil {
		return *x.Const
	}
	return 0
}

func (x *Int64Rules) GetLt() int64 {
	if x != nil && x.Lt != nil {
		return *x.Lt
	}
	return 0
}

func (x *Int64Rules) GetLte() int64 {
	if x != nil && x.Lte != nil {
		return *x.Lte
	}
	return 0
}

func (x *Int64Rules) GetGt() int64 {
	if x != nil && x.Gt != nil {
		return *x.Gt
	}
	return 0
}

func (x *Int64Rules) GetGte() int64 {
	if x != nil && x.Gte != nil {
		return *x.Gte
	}
	return 0
}

func (x *Int64Rules) GetIn() []int64 {
	if x != nil {
		return x.In
	}
	return nil
}

func (x *Int64Rules) GetNotIn() []int64 {
	if x != nil {
		return x.NotIn
	}
	return nil
}

func (x *Int64Rules) GetIgnoreEmpty() bool {
	if x != nil && x.IgnoreEmpty != nil {
		return *x.IgnoreEmpty
	}
	return false
}

// UInt32Rules describes the constraints applied to `uint32` values
type UInt32Rules struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Const specifies that this field must be exactly the specified value
	Const *uint32 `protobuf:"varint,1,opt,name=const" json:"const,omitempty"`
	// Lt specifies that this field must be less than the specified value,
	// exclusive
	Lt *uint32 `protobuf:"varint,2,opt,name=lt" json:"lt,omitempty"`
	// Lte specifies that this field must be less than or equal to the
	// specified value, inclusive
	Lte *uint32 `protobuf:"varint,3,opt,name=lte" json:"lte,omitempty"`
	// Gt specifies that this field must be greater than the specified value,
	// exclusive. If the value of Gt is larger than a specified Lt or Lte, the
	// range is reversed.
	Gt *uint32 `protobuf:"varint,4,opt,name=gt" json:"gt,omitempty"`
	// Gte specifies that this field must be greater than or equal to the
	// specified value, inclusive. If the value of Gte is larger than a
	// specified Lt or Lte, the range is reversed.
	Gte *uint32 `protobuf:"varint,5,opt,name=gte" json:"gte,omitempty"`
	// In specifies that this field must be equal to one of the specified
	// values
	In []uint32 `protobuf:"varint,6,rep,name=in" json:"in,omitempty"`
	// NotIn specifies that this field cannot be equal to one of the specified
	// values
	NotIn []uint32 `protobuf:"varint,7,rep,name=not_in,json=notIn" json:"not_in,omitempty"`
	// IgnoreEmpty specifies that the validation rules of this field should be
	// evaluated only if the field is not empty
	IgnoreEmpty *bool `protobuf:"varint,8,opt,name=ignore_empty,json=ignoreEmpty" json:"ignore_empty,omitempty"`
}

func (x *UInt32Rules) Reset() {
	*x = UInt32Rules{}
	if protoimpl.UnsafeEnabled {
		mi := &file_validate_validate_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UInt32Rules) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UInt32Rules) ProtoMessage() {}

func (x *UInt32Rules) ProtoReflect() protoreflect.Message {
	mi := &file_validate_validate_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UInt32Rules.ProtoReflect.Descriptor instead.
func (*UInt32Rules) Descriptor() ([]byte, []int) {
	return file_validate_validate_proto_rawDescGZIP(), []int{5}
}

func (x *UInt32Rules) GetConst() uint32 {
	if x != nil && x.Const != nil {
		return *x.Const
	}
	return 0
}

func (x *UInt32Rules) GetLt() uint32 {
	if x != nil && x.Lt != nil {
		return *x.Lt
	}
	return 0
}

func (x *UInt32Rules) GetLte() uint32 {
	if x != nil && x.Lte != nil {
		return *x.Lte
	}
	return 0
}

func (x *UInt32Rules) GetGt() uint32 {
	if x != nil && x.Gt != nil {
		return *x.Gt
	}
	return 0
}

func (x *UInt32Rules) GetGte() uint32 {
	if x != nil && x.Gte != nil {
		return *x.Gte
	}
	return 0
}

func (x *UInt32Rules) GetIn() []uint32 {
	if x != nil {
		return x.In
	}
	return nil
}

func (x *UInt32Rules) GetNotIn() []uint32 {
	if x != nil {
		return x.NotIn
	}
	return nil
}

func (x *UInt32Rules) GetIgnoreEmpty() bool {
	if x != nil && x.IgnoreEmpty != nil {
		return *x.IgnoreEmpty
	}
	return false
}

// UInt64Rules describes the constraints applied to `uint64` values
type UInt64Rules struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Const specifies that this field must be exactly the specified value
	Const *uint64 `protobuf:"varint,1,opt,name=const" json:"const,omitempty"`
	// Lt specifies that this field must be less than the specified value,
	// exclusive
	Lt *uint64 `protobuf:"varint,2,opt,name=lt" json:"lt,omitempty"`
	// Lte specifies that this field must be less than or equal to the
	// specified value, inclusive
	Lte *uint64 `protobuf:"varint,3,opt,name=lte" json:"lte,omitempty"`
	// Gt specifies that this field must be greater than the specified value,
	// exclusive. If the value of Gt is larger than a specified Lt or Lte, the
	// range is reversed.
	Gt *uint64 `protobuf:"varint,4,opt,name=gt" json:"gt,omitempty"`
	// Gte specifies that this field must be greater than or equal to the
	// specified value, inclusive. If the value of Gte is larger than a
	// specified Lt or Lte, the range is reversed.
	Gte *uint64 `protobuf:"varint,5,opt,name=gte" json:"gte,omitempty"`
	// In specifies that this field must be equal to one of the specified
	// values
	In []uint64 `protobuf:"varint,6,rep,name=in" json:"in,omitempty"`
	// NotIn specifies that this field cannot be equal to one of the specified
	// values
	NotIn []uint64 `protobuf:"varint,7,rep,name=not_in,json=notIn" json:"not_in,omitempty"`
	// IgnoreEmpty specifies that the validation rules of this field should be
	// evaluated only if the field is not empty
	IgnoreEmpty *bool `protobuf:"varint,8,opt,name=ignore_empty,json=ignoreEmpty" json:"ignore_empty,omitempty"`
}

func (x *UInt64Rules) Reset() {
	*x = UInt64Rules{}
	if protoimpl.UnsafeEnabled {
		mi := &file_validate_validate_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UInt64Rules) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UInt64Rules) ProtoMessage() {}

func (x *UInt64Rules) ProtoReflect() protoreflect.Message {
	mi := &file_validate_validate_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UInt64Rules.ProtoReflect.Descriptor instead.
func (*UInt64Rules) Descriptor() ([]byte, []int) {
	return file_validate_validate_proto_rawDescGZIP(), []int{6}
}

func (x *UInt64Rules) GetConst() uint64 {
	if x != nil && x.Const != nil {
		return *x.Const
	}
	return 0
}

func (x *UInt64Rules) GetLt() uint64 {
	if x != nil && x.Lt != nil {
		return *x.Lt
	}
	return 0
}

func (x *UInt64Rules) GetLte() uint64 {
	if x != nil && x.Lte != nil {
		return *x.Lte
	}
	return 0
}

func (x *UInt64Rules) GetGt() uint64 {
	if x != nil && x.Gt != nil {
		return *x.Gt
	}
	return 0
}

func (x *UInt64Rules) GetGte() uint64 {
	if x != nil && x.Gte != nil {
		return *x.Gte
	}
	return 0
}

func (x *UInt64Rules) GetIn() []uint64 {
	if x != nil {
		return x.In
	}
	return nil
}

func (x *UInt64Rules) GetNotIn() []uint64 {
	if x != nil {
		return x.NotIn
	}
	return nil
}

func (x *UInt64Rules) GetIgnoreEmpty() bool {
	if x != nil && x.IgnoreEmpty != nil {
		return *x.IgnoreEmpty
	}
	return false
}

// SInt32Rules describes the constraints applied to `sint32` values
type SInt32Rules struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Const specifies that this field must be exactly the specified value
	Const *int32 `protobuf:"zigzag32,1,opt,name=const" json:"const,omitempty"`
	// Lt specifies that this field must be less than the specified value,
	// exclusive
	Lt *int32 `protobuf:"zigzag32,2,opt,name=lt" json:"lt,omitempty"`
	// Lte specifies that this field must be less than or equal to the
	// specified value, inclusive
	Lte *int32 `protobuf:"zigzag32,3,opt,name=lte" json:"lte,omitempty"`
	// Gt specifies that this field must be greater than the specified value,
	// exclusive. If the value of Gt is larger than a specified Lt or Lte, the
	// range is reversed.
	Gt *int32 `protobuf:"zigzag32,4,opt,name=gt" json:"gt,omitempty"`
	// Gte specifies that this field must be greater than or equal to the
	// specified value, inclusive. If the value of Gte is larger than a
	// specified Lt or Lte, the range is reversed.
	Gte *int32 `protobuf:"zigzag32,5,opt,name=gte" json:"gte,omitempty"`
	// In specifies that this field must be equal to one of the specified
	// values
	In []int32 `protobuf:"zigzag32,6,rep,name=in" json:"in,omitempty"`
	// NotIn specifies that this field cannot be equal to one of the specified
	// values
	NotIn []int32 `protobuf:"zigzag32,7,rep,name=not_in,json=notIn" json:"not_in,omitempty"`
	// IgnoreEmpty specifies that the validation rules of this field should be
	// evaluated only if the field is not empty
	IgnoreEmpty *bool `protobuf:"varint,8,opt,name=ignore_empty,json=ignoreEmpty" json:"ignore_empty,omitempty"`
}

func (x *SInt32Rules) Reset() {
	*x = SInt32Rules{}
	if protoimpl.UnsafeEnabled {
		mi := &file_validate_validate_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SInt32Rules) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SInt32Rules) ProtoMessage() {}

func (x *SInt32Rules) ProtoReflect() protoreflect.Message {
	mi := &file_validate_validate_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SInt32Rules.ProtoReflect.Descriptor instead.
func (*SInt32Rules) Descriptor() ([]byte, []int) {
	return file_validate_validate_proto_rawDescGZIP(), []int{7}
}

func (x *SInt32Rules) GetConst() int32 {
	if x != nil && x.Const != nil {
		return *x.Const
	}
	return 0
}

func (x *SInt32Rules) GetLt() int32 {
	if x != nil && x.Lt != nil {
		return *x.Lt
	}
	return 0
}

func (x *SInt32Rules) GetLte() int32 {
	if x != nil && x.Lte != nil {
		return *x.Lte
	}
	return 0
}

func (x *SInt32Rules) GetGt() int32 {
	if x != nil && x.Gt != nil {
		return *x.Gt
	}
	return 0
}

func (x *SInt32Rules) GetGte() int32 {
	if x != nil && x.Gte != nil {
		return *x.Gte
	}
	return 0
}

func (x *SInt32Rules) GetIn() []int32 {
	if x != nil {
		return x.In
	}
	return nil
}

func (x *SInt32Rules) GetNotIn() []int32 {
	if x != nil {
		return x.NotIn
	}
	return nil
}

func (x *SInt32Rules) GetIgnoreEmpty() bool {
	if x != nil && x.IgnoreEmpty != nil {
		return *x.IgnoreEmpty
	}
	return false
}

// SInt64Rules describes the constraints applied to `sint64` values
type SInt64Rules struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Const specifies that this field must be exactly the specified value
	Const *int64 `protobuf:"zigzag64,1,opt,name=const" json:"const,omitempty"`
	// Lt specifies that this field must be less than the specified value,
	// exclusive
	Lt *int64 `protobuf:"zigzag64,2,opt,name=lt" json:"lt,omitempty"`
	// Lte specifies that this field must be less than or equal to the
	// specified value, inclusive
	Lte *int64 `protobuf:"zigzag64,3,opt,name=lte" json:"lte,omitempty"`
	// Gt specifies that this field must be greater than the specified value,
	// exclusive. If the value of Gt is larger than a specified Lt or Lte, the
	// range is reversed.
	Gt *int64 `protobuf:"zigzag64,4,opt,name=gt" json:"gt,omitempty"`
	// Gte specifies that this field must be greater than or equal to the
	// specified value, inclusive. If the value of Gte is larger than a
	// specified Lt or Lte, the range is reversed.
	Gte *int64 `protobuf:"zigzag64,5,opt,name=gte" json:"gte,omitempty"`
	// In specifies that this field must be equal to one of the specified
	// values
	In []int64 `protobuf:"zigzag64,6,rep,name=in" json:"in,omitempty"`
	// NotIn specifies that this field cannot be equal to one of the specified
	// values
	NotIn []int64 `protobuf:"zigzag64,7,rep,name=not_in,json=notIn" json:"not_in,omitempty"`
	// IgnoreEmpty specifies that the validation rules of this field should be
	// evaluated only if the field is not empty
	IgnoreEmpty *bool `protobuf:"varint,8,opt,name=ignore_empty,json=ignoreEmpty" json:"ignore_empty,omitempty"`
}

func (x *SInt64Rules) Reset() {
	*x = SInt64Rules{}
	if protoimpl.UnsafeEnabled {
		mi := &file_validate_validate_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SInt64Rules) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SInt64Rules) ProtoMessage() {}

func (x *SInt64Rules) ProtoReflect() protoreflect.Message {
	mi := &file_validate_validate_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SInt64Rules.ProtoReflect.Descriptor instead.
func (*SInt64Rules) Descriptor() ([]byte, []int) {
	return file_validate_validate_proto_rawDescGZIP(), []int{8}
}

func (x *SInt64Rules) GetConst() int64 {
	if x != nil && x.Const != nil {
		return *x.Const
	}
	return 0
}

func (x *SInt64Rules) GetLt() int64 {
	if x != nil && x.Lt != nil {
		return *x.Lt
	}
	return 0
}

func (x *SInt64Rules) GetLte() int64 {
	if x != nil && x.Lte != nil {
		return *x.Lte
	}
	return 0
}

func (x *SInt64Rules) GetGt() int64 {
	if x != nil && x.Gt != nil {
		return *x.Gt
	}
	return 0
}

func (x *SInt64Rules) GetGte() int64 {
	if x != nil && x.Gte != nil {
		return *x.Gte
	}
	return 0
}

func (x *SInt64Rules) GetIn() []int64 {
	if x != nil {
		return x.In
	}
	return nil
}

func (x *SInt64Rules) GetNotIn() []int64 {
	if x != nil {
		return x.NotIn
	}
	return nil
}

func (x *SInt64Rules) GetIgnoreEmpty() bool {
	if x != nil && x.IgnoreEmpty != nil {
		return *x.IgnoreEmpty
	}
	return false
}

// Fixed32Rules describes the constraints applied to `fixed32` values
type Fixed32Rules struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Const specifies that this field must be exactly the specified value
	Const *uint32 `protobuf:"fixed32,1,opt,name=const" json:"const,omitempty"`
	// Lt specifies that this field must be less than the specified value,
	// exclusive
	Lt *uint32 `protobuf:"fixed32,2,opt,name=lt" json:"lt,omitempty"`
	// Lte specifies that this field must be less than or equal to the
	// specified value, inclusive
	Lte *uint32 `protobuf:"fixed32,3,opt,name=lte" json:"lte,omitempty"`
	// Gt specifies that this field must be greater than the specified value,
	// exclusive. If the value of Gt is larger than a specified Lt or Lte, the
	// range is reversed.
	Gt *uint32 `protobuf:"fixed32,4,opt,name=gt" json:"gt,omitempty"`
	// Gte specifies that this field must be greater than or equal to the
	// specified value, inclusive. If the value of Gte is larger than a
	// specified Lt or Lte, the range is reversed.
	Gte *uint32 `protobuf:"fixed32,5,opt,name=gte" json:"gte,omitempty"`
	// In specifies that this field must be equal to one of the specified
	// values
	In []uint32 `protobuf:"fixed32,6,rep,name=in" json:"in,omitempty"`
	// NotIn specifies that this field cannot be equal to one of the specified
	// values
	NotIn []uint32 `protobuf:"fixed32,7,rep,name=not_in,json=notIn" json:"not_in,omitempty"`
	// IgnoreEmpty specifies that the validation rules of this field should be
	// evaluated only if the field is not empty
	IgnoreEmpty *bool `protobuf:"varint,8,opt,name=ignore_empty,json=ignoreEmpty" json:"ignore_empty,omitempty"`
}

func (x *Fixed32Rules) Reset() {
	*x = Fixed32Rules{}
	if protoimpl.UnsafeEnabled {
		mi := &file_validate_validate_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Fixed32Rules) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Fixed32Rules) ProtoMessage() {}

func (x *Fixed32Rules) ProtoReflect() protoreflect.Message {
	mi := &file_validate_validate_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Fixed32Rules.ProtoReflect.Descriptor instead.
func (*Fixed32Rules) Descriptor() ([]byte, []int) {
	return file_validate_validate_proto_rawDescGZIP(), []int{9}
}

func (x *Fixed32Rules) GetConst() uint32 {
	if x != nil && x.Const != nil {
		return *x.Const
	}
	return 0
}

func (x *Fixed32Rules) GetLt() uint32 {
	if x != nil && x.Lt != nil {
		return *x.Lt
	}
	return 0
}

func (x *Fixed32Rules) GetLte() uint32 {
	if x != nil && x.Lte != nil {
		return *x.Lte
	}
	return 0
}

func (x *Fixed32Rules) GetGt() uint32 {
	if x != nil && x.Gt != nil {
		return *x.Gt
	}
	return 0
}

func (x *Fixed32Rules) GetGte() uint32 {
	if x != nil && x.Gte != nil {
		return *x.Gte
	}
	return 0
}

func (x *Fixed32Rules) GetIn() []uint32 {
	if x != nil {
		return x.In
	}
	return nil
}

func (x *Fixed32Rules) GetNotIn() []uint32 {
	if x != nil {
		return x.NotIn
	}
	return nil
}

func (x *Fixed32Rules) GetIgnoreEmpty() bool {
	if x != nil && x.IgnoreEmpty != nil {
		return *x.IgnoreEmpty
	}
	return false
}

// Fixed64Rules describes the constraints applied to `fixed64` values
type Fixed64Rules struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Const specifies that this field must be exactly the specified value
	Const *uint64 `protobuf:"fixed64,1,opt,name=const" json:"const,omitempty"`
	// Lt specifies that this field must be less than the specified value,
	// exclusive
	Lt *uint64 `protobuf:"fixed64,2,opt,name=lt" json:"lt,omitempty"`
	// Lte specifies that this field must be less than or equal to the
	// specified value, inclusive
	Lte *uint64 `protobuf:"fixed64,3,opt,name=lte" json:"lte,omitempty"`
	// Gt specifies that this field must be greater than the specified value,
	// exclusive. If the value of Gt is larger than a specified Lt or Lte, the
	// range is reversed.
	Gt *uint64 `protobuf:"fixed64,4,opt,name=gt" json:"gt,omitempty"`
	// Gte specifies that this field must be greater than or equal to the
	// specified value, inclusive. If the value of Gte is larger than a
	// specified Lt or Lte, the range is reversed.
	Gte *uint64 `protobuf:"fixed64,5,opt,name=gte" json:"gte,omitempty"`
	// In specifies that this field must be equal to one of the specified
	// values
	In []uint64 `protobuf:"fixed64,6,rep,name=in" json:"in,omitempty"`
	// NotIn specifies that this field cannot be equal to one of the specified
	// values
	NotIn []uint64 `protobuf:"fixed64,7,rep,name=not_in,json=notIn" json:"not_in,omitempty"`
	// IgnoreEmpty specifies that the validation rules of this field should be
	// evaluated only if the field is not empty
	IgnoreEmpty *bool `protobuf:"varint,8,opt,name=ignore_empty,json=ignoreEmpty" json:"ignore_empty,omitempty"`
}

func (x *Fixed64Rules) Reset() {
	*x = Fixed64Rules{}
	if protoimpl.UnsafeEnabled {
		mi := &file_validate_validate_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Fixed64Rules) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Fixed64Rules) ProtoMessage() {}

func (x *Fixed64Rules) ProtoReflect() protoreflect.Message {
	mi := &file_validate_validate_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Fixed64Rules.ProtoReflect.Descriptor instead.
func (*Fixed64Rules) Descriptor() ([]byte, []int) {
	return file_validate_validate_proto_rawDescGZIP(), []int{10}
}

func (x *Fixed64Rules) GetConst() uint64 {
	if x != nil && x.Const != nil {
		return *x.Const
	}
	return 0
}

func (x *Fixed64Rules) GetLt() uint64 {
	if x != nil && x.Lt != nil {
		return *x.Lt
	}
	return 0
}

func (x *Fixed64Rules) GetLte() uint64 {
	if x != nil && x.Lte != nil {
		return *x.Lte
	}
	return 0
}

func (x *Fixed64Rules) GetGt() uint64 {
	if x != nil && x.Gt != nil {
		return *x.Gt
	}
	return 0
}

func (x *Fixed64Rules) GetGte() uint64 {
	if x != nil && x.Gte != nil {
		return *x.Gte
	}
	return 0
}

func (x *Fixed64Rules) GetIn() []uint64 {
	if x != nil {
		return x.In
	}
	return nil
}

func (x *Fixed64Rules) GetNotIn() []uint64 {
	if x != nil {
		return x.NotIn
	}
	return nil
}

func (x *Fixed64Rules) GetIgnoreEmpty() bool {
	if x != nil && x.IgnoreEmpty != nil {
		return *x.IgnoreEmpty
	}
	return false
}

// SFixed32Rules describes the constraints applied to `sfixed32` values
type SFixed32Rules struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Const specifies that this field must be exactly the specified value
	Const *int32 `protobuf:"fixed32,1,opt,name=const" json:"const,omitempty"`
	// Lt specifies that this field must be less than the specified value,
	// exclusive
	Lt *int32 `protobuf:"fixed32,2,opt,name=lt" json:"lt,omitempty"`
	// Lte specifies that this field must be less than or equal to the
	// specified value, inclusive
	Lte *int32 `protobuf:"fixed32,3,opt,name=lte" json:"lte,omitempty"`
	// Gt specifies that this field must be greater than the specified value,
	// exclusive. If the value of Gt is larger than a specified Lt or Lte, the
	// range is reversed.
	Gt *int32 `protobuf:"fixed32,4,opt,name=gt" json:"gt,omitempty"`
	// Gte specifies that this field must be greater than or equal to the
	// specified value, inclusive. If the value of Gte is larger than a
	// specified Lt or Lte, the range is reversed.
	Gte *int32 `protobuf:"fixed32,5,opt,name=gte" json:"gte,omitempty"`
	// In specifies that this field must be equal to one of the specified
	// values
	In []int32 `protobuf:"fixed32,6,rep,name=in" json:"in,omitempty"`
	// NotIn specifies that this field cannot be equal to one of the specified
	// values
	NotIn []int32 `protobuf:"fixed32,7,rep,name=not_in,json=notIn" json:"not_in,omitempty"`
	// IgnoreEmpty specifies that the validation rules of this field should be
	// evaluated only if the field is not empty
	IgnoreEmpty *bool `protobuf:"varint,8,opt,name=ignore_empty,json=ignoreEmpty" json:"ignore_empty,omitempty"`
}

func (x *SFixed32Rules) Reset() {
	*x = SFixed32Rules{}
	if protoimpl.UnsafeEnabled {
		mi := &file_validate_validate_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SFixed32Rules) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SFixed32Rules) ProtoMessage() {}

func (x *SFixed32Rules) ProtoReflect() protoreflect.Message {
	mi := &file_validate_validate_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SFixed32Rules.ProtoReflect.Descriptor instead.
func (*SFixed32Rules) Descriptor() ([]byte, []int) {
	return file_validate_validate_proto_rawDescGZIP(), []int{11}
}

func (x *SFixed32Rules) GetConst() int32 {
	if x != nil && x.Const != nil {
		return *x.Const
	}
	return 0
}

func (x *SFixed32Rules) GetLt() int32 {
	if x != nil && x.Lt != nil {
		return *x.Lt
	}
	return 0
}

func (x *SFixed32Rules) GetLte() int32 {
	if x != nil && x.Lte != nil {
		return *x.Lte
	}
	return 0
}

func (x *SFixed32Rules) GetGt() int32 {
	if x != nil && x.Gt != nil {
		return *x.Gt
	}
	return 0
}

func (x *SFixed32Rules) GetGte() int32 {
	if x != nil && x.Gte != nil {
		return *x.Gte
	}
	return 0
}

func (x *SFixed32Rules) GetIn() []int32 {
	if x != nil {
		return x.In
	}
	return nil
}

func (x *SFixed32Rules) GetNotIn() []int32 {
	if x != nil {
		return x.NotIn
	}
	return nil
}

func (x *SFixed32Rules) GetIgnoreEmpty() bool {
	if x != nil && x.IgnoreEmpty != nil {
		return *x.IgnoreEmpty
	}
	return false
}

// SFixed64Rules describes the constraints applied to `sfixed64` values
type SFixed64Rules struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Const specifies that this field must be exactly the specified value
	Const *int64 `protobuf:"fixed64,1,opt,name=const" json:"const,omitempty"`
	// Lt specifies that this field must be less than the specified value,
	// exclusive
	Lt *int64 `protobuf:"fixed64,2,opt,name=lt" json:"lt,omitempty"`
	// Lte specifies that this field must be less than or equal to the
	// specified value, inclusive
	Lte *int64 `protobuf:"fixed64,3,opt,name=lte" json:"lte,omitempty"`
	// Gt specifies that this field must be greater than the specified value,
	// exclusive. If the value of Gt is larger than a specified Lt or Lte, the
	// range is reversed.
	Gt *int64 `protobuf:"fixed64,4,opt,name=gt" json:"gt,omitempty"`
	// Gte specifies that this field must be greater than or equal to the
	// specified value, inclusive. If the value of Gte is larger than a
	// specified Lt or Lte, the range is reversed.
	Gte *int64 `protobuf:"fixed64,5,opt,name=gte" json:"gte,omitempty"`
	// In specifies that this field must be equal to one of the specified
	// values
	In []int64 `protobuf:"fixed64,6,rep,name=in" json:"in,omitempty"`
	// NotIn specifies that this field cannot be equal to one of the specified
	// values
	NotIn []int64 `protobuf:"fixed64,7,rep,name=not_in,json=notIn" json:"not_in,omitempty"`
	// IgnoreEmpty specifies that the validation rules of this field should be
	// evaluated only if the field is not empty
	IgnoreEmpty *bool `protobuf:"varint,8,opt,name=ignore_empty,json=ignoreEmpty" json:"ignore_empty,omitempty"`
}

func (x *SFixed64Rules) Reset() {
	*x = SFixed64Rules{}
	if protoimpl.UnsafeEnabled {
		mi := &file_validate_validate_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SFixed64Rules) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SFixed64Rules) ProtoMessage() {}

func (x *SFixed64Rules) ProtoReflect() protoreflect.Message {
	mi := &file_validate_validate_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SFixed64Rules.ProtoReflect.Descriptor instead.
func (*SFixed64Rules) Descriptor() ([]byte, []int) {
	return file_validate_validate_proto_rawDescGZIP(), []int{12}
}

func (x *SFixed64Rules) GetConst() int64 {
	if x != nil && x.Const != nil {
		return *x.Const
	}
	return 0
}

func (x *SFixed64Rules) GetLt() int64 {
	if x != nil && x.Lt != nil {
		return *x.Lt
	}
	return 0
}

func (x *SFixed64Rules) GetLte() int64 {
	if x != nil && x.Lte != nil {
		return *x.Lte
	}
	return 0
}

func (x *SFixed64Rules) GetGt() int64 {
	if x != nil && x.Gt != nil {
		return *x.Gt
	}
	return 0
}

func (x *SFixed64Rules) GetGte() int64 {
	if x != nil && x.Gte != nil {
		return *x.Gte
	}
	return 0
}

func (x *SFixed64Rules) GetIn() []int64 {
	if x != nil {
		return x.In
	}
	return nil
}

func (x *SFixed64Rules) GetNotIn() []int64 {
	if x != nil {
		return x.NotIn
	}
	return nil
}

func (x *SFixed64Rules) GetIgnoreEmpty() bool {
	if x != nil && x.IgnoreEmpty != nil {
		return *x.IgnoreEmpty
	}
	return false
}

// BoolRules describes the constraints applied to `bool` values
type BoolRules struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Const specifies that this field must be exactly the specified value
	Const *bool `protobuf:"varint,1,opt,name=const" json:"const,omitempty"`
}

func (x *BoolRules) Reset() {
	*x = BoolRules{}
	if protoimpl.UnsafeEnabled {
		mi := &file_validate_validate_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BoolRules) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BoolRules) ProtoMessage() {}

func (x *BoolRules) ProtoReflect() protoreflect.Message {
	mi := &file_validate_validate_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BoolRules.ProtoReflect.Descriptor instead.
func (*BoolRules) Descriptor() ([]byte, []int) {
	return file_validate_validate_proto_rawDescGZIP(), []int{13}
}

func (x *BoolRules) GetConst() bool {
	if x != nil && x.Const != nil {
		return *x.Const
	}
	return false
}

// StringRules describe the constraints applied to `string` values
type StringRules struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Const specifies that this field must be exactly the specified value
	Const *string `protobuf:"bytes,1,opt,name=const" json:"const,omitempty"`
	// Len specifies that this field must be the specified number of
	// characters (Unicode code points). Note that the number of
	// characters may differ from the number of bytes in the string.
	Len *uint64 `protobuf:"varint,19,opt,name=len" json:"len,omitempty"`
	// MinLen specifies that this field must be the specified number of
	// characters (Unicode code points) at a minimum. Note that the number of
	// characters may differ from the number of bytes in the string.
	MinLen *uint64 `protobuf:"varint,2,opt,name=min_len,json=minLen" json:"min_len,omitempty"`
	// MaxLen specifies that this field must be the specified number of
	// characters (Unicode code points) at a maximum. Note that the number of
	// characters may differ from the number of bytes in the string.
	MaxLen *uint64 `protobuf:"varint,3,opt,name=max_len,json=maxLen" json:"max_len,omitempty"`
	// LenBytes specifies that this field must be the specified number of bytes
	LenBytes *uint64 `protobuf:"varint,20,opt,name=len_bytes,json=lenBytes" json:"len_bytes,omitempty"`
	// MinBytes specifies that this field must be the specified number of bytes
	// at a minimum
	MinBytes *uint64 `protobuf:"varint,4,opt,name=min_bytes,json=minBytes" json:"min_bytes,omitempty"`
	// MaxBytes specifies that this field must be the specified number of bytes
	// at a maximum
	MaxBytes *uint64 `protobuf:"varint,5,opt,name=max_bytes,json=maxBytes" json:"max_bytes,omitempty"`
	// Pattern specifes that this field must match against the specified
	// regular expression (RE2 syntax). The included expression should elide
	// any delimiters.
	Pattern *string `protobuf:"bytes,6,opt,name=pattern" json:"pattern,omitempty"`
	// Prefix specifies that this field must have the specified substring at
	// the beginning of the string.
	Prefix *string `protobuf:"bytes,7,opt,name=prefix" json:"prefix,omitempty"`
	// Suffix specifies that this field must have the specified substring at
	// the end of the string.
	Suffix *string `protobuf:"bytes,8,opt,name=suffix" json:"suffix,omitempty"`
	// Contains specifies that this field must have the specified substring
	// anywhere in the string.
	Contains *string `protobuf:"bytes,9,opt,name=contains" json:"contains,omitempty"`
	// NotContains specifies that this field cannot have the specified substring
	// anywhere in the string.
	NotContains *string `protobuf:"bytes,23,opt,name=not_contains,json=notContains" json:"not_contains,omitempty"`
	// In specifies that this field must be equal to one of the specified
	// values
	In []string `protobuf:"bytes,10,rep,name=in" json:"in,omitempty"`
	// NotIn specifies that this field cannot be equal to one of the specified
	// values
	NotIn []string `protobuf:"bytes,11,rep,name=not_in,json=notIn" json:"not_in,omitempty"`
	// WellKnown rules provide advanced constraints against common string
	// patterns
	//
	// Types that are assignable to WellKnown:
	//
	//	*StringRules_Email
	//	*StringRules_Hostname
	//	*StringRules_Ip
	//	*StringRules_Ipv4
	//	*StringRules_Ipv6
	//	*StringRules_Uri
	//	*StringRules_UriRef
	//	*StringRules_Address
	//	*StringRules_Uuid
	//	*StringRules_WellKnownRegex
	WellKnown isStringRules_WellKnown `protobuf_oneof:"well_known"`
	// This applies to regexes HTTP_HEADER_NAME and HTTP_HEADER_VALUE to enable
	// strict header validation.
	// By default, this is true, and HTTP header validations are RFC-compliant.
	// Setting to false will enable a looser validations that only disallows
	// \r\n\0 characters, which can be used to bypass header matching rules.
	Strict *bool `protobuf:"varint,25,opt,name=strict,def=1" json:"strict,omitempty"`
	// IgnoreEmpty specifies that the validation rules of this field should be
	// evaluated only if the field is not empty
	IgnoreEmpty *bool `protobuf:"varint,26,opt,name=ignore_empty,json=ignoreEmpty" json:"ignore_empty,omitempty"`
}

// Default values for StringRules fields.
const (
	Default_StringRules_Strict = bool(true)
)

func (x *StringRules) Reset() {
	*x = StringRules{}
	if protoimpl.UnsafeEnabled {
		mi := &file_validate_validate_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringRules) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringRules) ProtoMessage() {}

func (x *StringRules) ProtoReflect() protoreflect.Message {
	mi := &file_validate_validate_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringRules.ProtoReflect.Descriptor instead.
func (*StringRules) Descriptor() ([]byte, []int) {
	return file_validate_validate_proto_rawDescGZIP(), []int{14}
}

func (x *StringRules) GetConst() string {
	if x != nil && x.Const != nil {
		return *x.Const
	}
	return ""
}

func (x *StringRules) GetLen() uint64 {
	if x != nil && x.Len != nil {
		return *x.Len
	}
	return 0
}

func (x *StringRules) GetMinLen() uint64 {
	if x != nil && x.MinLen != nil {
		return *x.MinLen
	}
	return 0
}

func (x *StringRules) GetMaxLen() uint64 {
	if x != nil && x.MaxLen != nil {
		return *x.MaxLen
	}
	return 0
}

func (x *StringRules) GetLenBytes() uint64 {
	if x != nil && x.LenBytes != nil {
		return *x.LenBytes
	}
	return 0
}

func (x *StringRules) GetMinBytes() uint64 {
	if x != nil && x.MinBytes != nil {
		return *x.MinBytes
	}
	return 0
}

func (x *StringRules) GetMaxBytes() uint64 {
	if x != nil && x.MaxBytes != nil {
		return *x.MaxBytes
	}
	return 0
}

func (x *StringRules) GetPattern() string {
	if x != nil && x.Pattern != nil {
		return *x.Pattern
	}
	return ""
}

func (x *StringRules) GetPrefix() string {
	if x != nil && x.Prefix != nil {
		return *x.Prefix
	}
	return ""
}

func (x *StringRules) GetSuffix() string {
	if x != nil && x.Suffix != nil {
		return *x.Suffix
	}
	return ""
}

func (x *StringRules) GetContains() string {
	if x != nil && x.Contains != nil {
		return *x.Contains
	}
	return ""
}

func (x *StringRules) GetNotContains() string {
	if x != nil && x.NotContains != nil {
		return *x.NotContains
	}
	return ""
}

func (x *StringRules) GetIn() []string {
	if x != nil {
		return x.In
	}
	return nil
}

func (x *StringRules) GetNotIn() []string {
	if x != nil {
		return x.NotIn
	}
	return nil
}

func (m *StringRules) GetWellKnown() isStringRules_WellKnown {
	if m != nil {
		return m.WellKnown
	}
	return nil
}

func (x *StringRules) GetEmail() bool {
	if x, ok := x.GetWellKnown().(*StringRules_Email); ok {
		return x.Email
	}
	return false
}

func (x *StringRules) GetHostname() bool {
	if x, ok := x.GetWellKnown().(*StringRules_Hostname); ok {
		return x.Hostname
	}
	return false
}

func (x *StringRules) GetIp() bool {
	if x, ok := x.GetWellKnown().(*StringRules_Ip); ok {
		return x.Ip
	}
	return false
}

func (x *StringRules) GetIpv4() bool {
	if x, ok := x.GetWellKnown().(*StringRules_Ipv4); ok {
		return x.Ipv4
	}
	return false
}

func (x *StringRules) GetIpv6() bool {
	if x, ok := x.GetWellKnown().(*StringRules_Ipv6); ok {
		return x.Ipv6
	}
	return false
}

func (x *StringRules) GetUri() bool {
	if x, ok := x.GetWellKnown().(*StringRules_Uri); ok {
		return x.Uri
	}
	return false
}

func (x *StringRules) GetUriRef() bool {
	if x, ok := x.GetWellKnown().(*StringRules_UriRef); ok {
		return x.UriRef
	}
	return false
}

func (x *StringRules) GetAddress() bool {
	if x, ok := x.GetWellKnown().(*StringRules_Address); ok {
		return x.Address
	}
	return false
}

func (x *StringRules) GetUuid() bool {
	if x, ok := x.GetWellKnown().(*StringRules_Uuid); ok {
		return x.Uuid
	}
	return false
}

func (x *StringRules) GetWellKnownRegex() KnownRegex {
	if x, ok := x.GetWellKnown().(*StringRules_WellKnownRegex); ok {
		return x.WellKnownRegex
	}
	return KnownRegex_UNKNOWN
}

func (x *StringRules) GetStrict() bool {
	if x != nil && x.Strict != nil {
		return *x.Strict
	}
	return Default_StringRules_Strict
}

func (x *StringRules) GetIgnoreEmpty() bool {
	if x != nil && x.IgnoreEmpty != nil {
		return *x.IgnoreEmpty
	}
	return false
}

type isStringRules_WellKnown interface {
	isStringRules_WellKnown()
}

type StringRules_Email struct {
	// Email specifies that the field must be a valid email address as
	// defined by RFC 5322
	Email bool `protobuf:"varint,12,opt,name=email,oneof"`
}

type StringRules_Hostname struct {
	// Hostname specifies that the field must be a valid hostname as
	// defined by RFC 1034. This constraint does not support
	// internationalized domain names (IDNs).
	Hostname bool `protobuf:"varint,13,opt,name=hostname,oneof"`
}

type StringRules_Ip struct {
	// Ip specifies that the field must be a valid IP (v4 or v6) address.
	// Valid IPv6 addresses should not include surrounding square brackets.
	Ip bool `protobuf:"varint,14,opt,name=ip,oneof"`
}

type StringRules_Ipv4 struct {
	// Ipv4 specifies that the field must be a valid IPv4 address.
	Ipv4 bool `protobuf:"varint,15,opt,name=ipv4,oneof"`
}

type StringRules_Ipv6 struct {
	// Ipv6 specifies that the field must be a valid IPv6 address. Valid
	// IPv6 addresses should not include surrounding square brackets.
	Ipv6 bool `protobuf:"varint,16,opt,name=ipv6,oneof"`
}

type StringRules_Uri struct {
	// Uri specifies that the field must be a valid, absolute URI as defined
	// by RFC 3986
	Uri bool `protobuf:"varint,17,opt,name=uri,oneof"`
}

type StringRules_UriRef struct {
	// UriRef specifies that the field must be a valid URI as defined by RFC
	// 3986 and may be relative or absolute.
	UriRef bool `protobuf:"varint,18,opt,name=uri_ref,json=uriRef,oneof"`
}

type StringRules_Address struct {
	// Address specifies that the field must be either a valid hostname as
	// defined by RFC 1034 (which does not support internationalized domain
	// names or IDNs), or it can be a valid IP (v4 or v6).
	Address bool `protobuf:"varint,21,opt,name=address,oneof"`
}

type StringRules_Uuid struct {
	// Uuid specifies that the field must be a valid UUID as defined by
	// RFC 4122
	Uuid bool `protobuf:"varint,22,opt,name=uuid,oneof"`
}

type StringRules_WellKnownRegex struct {
	// WellKnownRegex specifies a common well known pattern defined as a regex.
	WellKnownRegex KnownRegex `protobuf:"varint,24,opt,name=well_known_regex,json=wellKnownRegex,enum=validate.KnownRegex,oneof"`
}

func (*StringRules_Email) isStringRules_WellKnown() {}

func (*StringRules_Hostname) isStringRules_WellKnown() {}

func (*StringRules_Ip) isStringRules_WellKnown() {}

func (*StringRules_Ipv4) isStringRules_WellKnown() {}

func (*StringRules_Ipv6) isStringRules_WellKnown() {}

func (*StringRules_Uri) isStringRules_WellKnown() {}

func (*StringRules_UriRef) isStringRules_WellKnown() {}

func (*StringRules_Address) isStringRules_WellKnown() {}

func (*StringRules_Uuid) isStringRules_WellKnown() {}

func (*StringRules_WellKnownRegex) isStringRules_WellKnown() {}

// BytesRules describe the constraints applied to `bytes` values
type BytesRules struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Const specifies that this field must be exactly the specified value
	Const []byte `protobuf:"bytes,1,opt,name=const" json:"const,omitempty"`
	// Len specifies that this field must be the specified number of bytes
	Len *uint64 `protobuf:"varint,13,opt,name=len" json:"len,omitempty"`
	// MinLen specifies that this field must be the specified number of bytes
	// at a minimum
	MinLen *uint64 `protobuf:"varint,2,opt,name=min_len,json=minLen" json:"min_len,omitempty"`
	// MaxLen specifies that this field must be the specified number of bytes
	// at a maximum
	MaxLen *uint64 `protobuf:"varint,3,opt,name=max_len,json=maxLen" json:"max_len,omitempty"`
	// Pattern specifes that this field must match against the specified
	// regular expression (RE2 syntax). The included expression should elide
	// any delimiters.
	Pattern *string `protobuf:"bytes,4,opt,name=pattern" json:"pattern,omitempty"`
	// Prefix specifies that this field must have the specified bytes at the
	// beginning of the string.
	Prefix []byte `protobuf:"bytes,5,opt,name=prefix" json:"prefix,omitempty"`
	// Suffix specifies that this field must have the specified bytes at the
	// end of the string.
	Suffix []byte `protobuf:"bytes,6,opt,name=suffix" json:"suffix,omitempty"`
	// Contains specifies that this field must have the specified bytes
	// anywhere in the string.
	Contains []byte `protobuf:"bytes,7,opt,name=contains" json:"contains,omitempty"`
	// In specifies that this field must be equal to one of the specified
	// values
	In [][]byte `protobuf:"bytes,8,rep,name=in" json:"in,omitempty"`
	// NotIn specifies that this field cannot be equal to one of the specified
	// values
	NotIn [][]byte `protobuf:"bytes,9,rep,name=not_in,json=notIn" json:"not_in,omitempty"`
	// WellKnown rules provide advanced constraints against common byte
	// patterns
	//
	// Types that are assignable to WellKnown:
	//
	//	*BytesRules_Ip
	//	*BytesRules_Ipv4
	//	*BytesRules_Ipv6
	WellKnown isBytesRules_WellKnown `protobuf_oneof:"well_known"`
	// IgnoreEmpty specifies that the validation rules of this field should be
	// evaluated only if the field is not empty
	IgnoreEmpty *bool `protobuf:"varint,14,opt,name=ignore_empty,json=ignoreEmpty" json:"ignore_empty,omitempty"`
}

func (x *BytesRules) Reset() {
	*x = BytesRules{}
	if protoimpl.UnsafeEnabled {
		mi := &file_validate_validate_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BytesRules) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BytesRules) ProtoMessage() {}

func (x *BytesRules) ProtoReflect() protoreflect.Message {
	mi := &file_validate_validate_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BytesRules.ProtoReflect.Descriptor instead.
func (*BytesRules) Descriptor() ([]byte, []int) {
	return file_validate_validate_proto_rawDescGZIP(), []int{15}
}

func (x *BytesRules) GetConst() []byte {
	if x != nil {
		return x.Const
	}
	return nil
}

func (x *BytesRules) GetLen() uint64 {
	if x != nil && x.Len != nil {
		return *x.Len
	}
	return 0
}

func (x *BytesRules) GetMinLen() uint64 {
	if x != nil && x.MinLen != nil {
		return *x.MinLen
	}
	return 0
}

func (x *BytesRules) GetMaxLen() uint64 {
	if x != nil && x.MaxLen != nil {
		return *x.MaxLen
	}
	return 0
}

func (x *BytesRules) GetPattern() string {
	if x != nil && x.Pattern != nil {
		return *x.Pattern
	}
	return ""
}

func (x *BytesRules) GetPrefix() []byte {
	if x != nil {
		return x.Prefix
	}
	return nil
}

func (x *BytesRules) GetSuffix() []byte {
	if x != nil {
		return x.Suffix
	}
	return nil
}

func (x *BytesRules) GetContains() []byte {
	if x != nil {
		return x.Contains
	}
	return nil
}

func (x *BytesRules) GetIn() [][]byte {
	if x != nil {
		return x.In
	}
	return nil
}

func (x *BytesRules) GetNotIn() [][]byte {
	if x != nil {
		return x.NotIn
	}
	return nil
}

func (m *BytesRules) GetWellKnown() isBytesRules_WellKnown {
	if m != nil {
		return m.WellKnown
	}
	return nil
}

func (x *BytesRules) GetIp() bool {
	if x, ok := x.GetWellKnown().(*BytesRules_Ip); ok {
		return x.Ip
	}
	return false
}

func (x *BytesRules) GetIpv4() bool {
	if x, ok := x.GetWellKnown().(*BytesRules_Ipv4); ok {
		return x.Ipv4
	}
	return false
}

func (x *BytesRules) GetIpv6() bool {
	if x, ok := x.GetWellKnown().(*BytesRules_Ipv6); ok {
		return x.Ipv6
	}
	return false
}

func (x *BytesRules) GetIgnoreEmpty() bool {
	if x != nil && x.IgnoreEmpty != nil {
		return *x.IgnoreEmpty
	}
	return false
}

type isBytesRules_WellKnown interface {
	isBytesRules_WellKnown()
}

type BytesRules_Ip struct {
	// Ip specifies that the field must be a valid IP (v4 or v6) address in
	// byte format
	Ip bool `protobuf:"varint,10,opt,name=ip,oneof"`
}

type BytesRules_Ipv4 struct {
	// Ipv4 specifies that the field must be a valid IPv4 address in byte
	// format
	Ipv4 bool `protobuf:"varint,11,opt,name=ipv4,oneof"`
}

type BytesRules_Ipv6 struct {
	// Ipv6 specifies that the field must be a valid IPv6 address in byte
	// format
	Ipv6 bool `protobuf:"varint,12,opt,name=ipv6,oneof"`
}

func (*BytesRules_Ip) isBytesRules_WellKnown() {}

func (*BytesRules_Ipv4) isBytesRules_WellKnown() {}

func (*BytesRules_Ipv6) isBytesRules_WellKnown() {}

// EnumRules describe the constraints applied to enum values
type EnumRules struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Const specifies that this field must be exactly the specified value
	Const *int32 `protobuf:"varint,1,opt,name=const" json:"const,omitempty"`
	// DefinedOnly specifies that this field must be only one of the defined
	// values for this enum, failing on any undefined value.
	DefinedOnly *bool `protobuf:"varint,2,opt,name=defined_only,json=definedOnly" json:"defined_only,omitempty"`
	// In specifies that this field must be equal to one of the specified
	// values
	In []int32 `protobuf:"varint,3,rep,name=in" json:"in,omitempty"`
	// NotIn specifies that this field cannot be equal to one of the specified
	// values
	NotIn []int32 `protobuf:"varint,4,rep,name=not_in,json=notIn" json:"not_in,omitempty"`
}

func (x *EnumRules) Reset() {
	*x = EnumRules{}
	if protoimpl.UnsafeEnabled {
		mi := &file_validate_validate_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnumRules) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnumRules) ProtoMessage() {}

func (x *EnumRules) ProtoReflect() protoreflect.Message {
	mi := &file_validate_validate_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnumRules.ProtoReflect.Descriptor instead.
func (*EnumRules) Descriptor() ([]byte, []int) {
	return file_validate_validate_proto_rawDescGZIP(), []int{16}
}

func (x *EnumRules) GetConst() int32 {
	if x != nil && x.Const != nil {
		return *x.Const
	}
	return 0
}

func (x *EnumRules) GetDefinedOnly() bool {
	if x != nil && x.DefinedOnly != nil {
		return *x.DefinedOnly
	}
	return false
}

func (x *EnumRules) GetIn() []int32 {
	if x != nil {
		return x.In
	}
	return nil
}

func (x *EnumRules) GetNotIn() []int32 {
	if x != nil {
		return x.NotIn
	}
	return nil
}

// MessageRules describe the constraints applied to embedded message values.
// For message-type fields, validation is performed recursively.
type MessageRules struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Skip specifies that the validation rules of this field should not be
	// evaluated
	Skip *bool `protobuf:"varint,1,opt,name=skip" json:"skip,omitempty"`
	// Required specifies that this field must be set
	Required *bool `protobuf:"varint,2,opt,name=required" json:"required,omitempty"`
}

func (x *MessageRules) Reset() {
	*x = MessageRules{}
	if protoimpl.UnsafeEnabled {
		mi := &file_validate_validate_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MessageRules) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageRules) ProtoMessage() {}

func (x *MessageRules) ProtoReflect() protoreflect.Message {
	mi := &file_validate_validate_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageRules.ProtoReflect.Descriptor instead.
func (*MessageRules) Descriptor() ([]byte, []int) {
	return file_validate_validate_proto_rawDescGZIP(), []int{17}
}

func (x *MessageRules) GetSkip() bool {
	if x != nil && x.Skip != nil {
		return *x.Skip
	}
	return false
}

func (x *MessageRules) GetRequired() bool {
	if x != nil && x.Required != nil {
		return *x.Required
	}
	return false
}

// RepeatedRules describe the constraints applied to `repeated` values
type RepeatedRules struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// MinItems specifies that this field must have the specified number of
	// items at a minimum
	MinItems *uint64 `protobuf:"varint,1,opt,name=min_items,json=minItems" json:"min_items,omitempty"`
	// MaxItems specifies that this field must have the specified number of
	// items at a maximum
	MaxItems *uint64 `protobuf:"varint,2,opt,name=max_items,json=maxItems" json:"max_items,omitempty"`
	// Unique specifies that all elements in this field must be unique. This
	// contraint is only applicable to scalar and enum types (messages are not
	// supported).
	Unique *bool `protobuf:"varint,3,opt,name=unique" json:"unique,omitempty"`
	// Items specifies the contraints to be applied to each item in the field.
	// Repeated message fields will still execute validation against each item
	// unless skip is specified here.
	Items *FieldRules `protobuf:"bytes,4,opt,name=items" json:"items,omitempty"`
	// IgnoreEmpty specifies that the validation rules of this field should be
	// evaluated only if the field is not empty
	IgnoreEmpty *bool `protobuf:"varint,5,opt,name=ignore_empty,json=ignoreEmpty" json:"ignore_empty,omitempty"`
}

func (x *RepeatedRules) Reset() {
	*x = RepeatedRules{}
	if protoimpl.UnsafeEnabled {
		mi := &file_validate_validate_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RepeatedRules) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RepeatedRules) ProtoMessage() {}

func (x *RepeatedRules) ProtoReflect() protoreflect.Message {
	mi := &file_validate_validate_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RepeatedRules.ProtoReflect.Descriptor instead.
func (*RepeatedRules) Descriptor() ([]byte, []int) {
	return file_validate_validate_proto_rawDescGZIP(), []int{18}
}

func (x *RepeatedRules) GetMinItems() uint64 {
	if x != nil && x.MinItems != nil {
		return *x.MinItems
	}
	return 0
}

func (x *RepeatedRules) GetMaxItems() uint64 {
	if x != nil && x.MaxItems != nil {
		return *x.MaxItems
	}
	return 0
}

func (x *RepeatedRules) GetUnique() bool {
	if x != nil && x.Unique != nil {
		return *x.Unique
	}
	return false
}

func (x *RepeatedRules) GetItems() *FieldRules {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *RepeatedRules) GetIgnoreEmpty() bool {
	if x != nil && x.IgnoreEmpty != nil {
		return *x.IgnoreEmpty
	}
	return false
}

// MapRules describe the constraints applied to `map` values
type MapRules struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// MinPairs specifies that this field must have the specified number of
	// KVs at a minimum
	MinPairs *uint64 `protobuf:"varint,1,opt,name=min_pairs,json=minPairs" json:"min_pairs,omitempty"`
	// MaxPairs specifies that this field must have the specified number of
	// KVs at a maximum
	MaxPairs *uint64 `protobuf:"varint,2,opt,name=max_pairs,json=maxPairs" json:"max_pairs,omitempty"`
	// NoSparse specifies values in this field cannot be unset. This only
	// applies to map's with message value types.
	NoSparse *bool `protobuf:"varint,3,opt,name=no_sparse,json=noSparse" json:"no_sparse,omitempty"`
	// Keys specifies the constraints to be applied to each key in the field.
	Keys *FieldRules `protobuf:"bytes,4,opt,name=keys" json:"keys,omitempty"`
	// Values specifies the constraints to be applied to the value of each key
	// in the field. Message values will still have their validations evaluated
	// unless skip is specified here.
	Values *FieldRules `protobuf:"bytes,5,opt,name=values" json:"values,omitempty"`
	// IgnoreEmpty specifies that the validation rules of this field should be
	// evaluated only if the field is not empty
	IgnoreEmpty *bool `protobuf:"varint,6,opt,name=ignore_empty,json=ignoreEmpty" json:"ignore_empty,omitempty"`
}

func (x *MapRules) Reset() {
	*x = MapRules{}
	if protoimpl.UnsafeEnabled {
		mi := &file_validate_validate_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MapRules) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MapRules) ProtoMessage() {}

func (x *MapRules) ProtoReflect() protoreflect.Message {
	mi := &file_validate_validate_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MapRules.ProtoReflect.Descriptor instead.
func (*MapRules) Descriptor() ([]byte, []int) {
	return file_validate_validate_proto_rawDescGZIP(), []int{19}
}

func (x *MapRules) GetMinPairs() uint64 {
	if x != nil && x.MinPairs != nil {
		return *x.MinPairs
	}
	return 0
}

func (x *MapRules) GetMaxPairs() uint64 {
	if x != nil && x.MaxPairs != nil {
		return *x.MaxPairs
	}
	return 0
}

func (x *MapRules) GetNoSparse() bool {
	if x != nil && x.NoSparse != nil {
		return *x.NoSparse
	}
	return false
}

func (x *MapRules) GetKeys() *FieldRules {
	if x != nil {
		return x.Keys
	}
	return nil
}

func (x *MapRules) GetValues() *FieldRules {
	if x != nil {
		return x.Values
	}
	return nil
}

func (x *MapRules) GetIgnoreEmpty() bool {
	if x != nil && x.IgnoreEmpty != nil {
		return *x.IgnoreEmpty
	}
	return false
}

// AnyRules describe constraints applied exclusively to the
// `google.protobuf.Any` well-known type
type AnyRules struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required specifies that this field must be set
	Required *bool `protobuf:"varint,1,opt,name=required" json:"required,omitempty"`
	// In specifies that this field's `type_url` must be equal to one of the
	// specified values.
	In []string `protobuf:"bytes,2,rep,name=in" json:"in,omitempty"`
	// NotIn specifies that this field's `type_url` must not be equal to any of
	// the specified values.
	NotIn []string `protobuf:"bytes,3,rep,name=not_in,json=notIn" json:"not_in,omitempty"`
}

func (x *AnyRules) Reset() {
	*x = AnyRules{}
	if protoimpl.UnsafeEnabled {
		mi := &file_validate_validate_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AnyRules) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnyRules) ProtoMessage() {}

func (x *AnyRules) ProtoReflect() protoreflect.Message {
	mi := &file_validate_validate_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnyRules.ProtoReflect.Descriptor instead.
func (*AnyRules) Descriptor() ([]byte, []int) {
	return file_validate_validate_proto_rawDescGZIP(), []int{20}
}

func (x *AnyRules) GetRequired() bool {
	if x != nil && x.Required != nil {
		return *x.Required
	}
	return false
}

func (x *AnyRules) GetIn() []string {
	if x != nil {
		return x.In
	}
	return nil
}

func (x *AnyRules) GetNotIn() []string {
	if x != nil {
		return x.NotIn
	}
	return nil
}

// DurationRules describe the constraints applied exclusively to the
// `google.protobuf.Duration` well-known type
type DurationRules struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required specifies that this field must be set
	Required *bool `protobuf:"varint,1,opt,name=required" json:"required,omitempty"`
	// Const specifies that this field must be exactly the specified value
	Const *durationpb.Duration `protobuf:"bytes,2,opt,name=const" json:"const,omitempty"`
	// Lt specifies that this field must be less than the specified value,
	// exclusive
	Lt *durationpb.Duration `protobuf:"bytes,3,opt,name=lt" json:"lt,omitempty"`
	// Lt specifies that this field must be less than the specified value,
	// inclusive
	Lte *durationpb.Duration `protobuf:"bytes,4,opt,name=lte" json:"lte,omitempty"`
	// Gt specifies that this field must be greater than the specified value,
	// exclusive
	Gt *durationpb.Duration `protobuf:"bytes,5,opt,name=gt" json:"gt,omitempty"`
	// Gte specifies that this field must be greater than the specified value,
	// inclusive
	Gte *durationpb.Duration `protobuf:"bytes,6,opt,name=gte" json:"gte,omitempty"`
	// In specifies that this field must be equal to one of the specified
	// values
	In []*durationpb.Duration `protobuf:"bytes,7,rep,name=in" json:"in,omitempty"`
	// NotIn specifies that this field cannot be equal to one of the specified
	// values
	NotIn []*durationpb.Duration `protobuf:"bytes,8,rep,name=not_in,json=notIn" json:"not_in,omitempty"`
}

func (x *DurationRules) Reset() {
	*x = DurationRules{}
	if protoimpl.UnsafeEnabled {
		mi := &file_validate_validate_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DurationRules) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DurationRules) ProtoMessage() {}

func (x *DurationRules) ProtoReflect() protoreflect.Message {
	mi := &file_validate_validate_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DurationRules.ProtoReflect.Descriptor instead.
func (*DurationRules) Descriptor() ([]byte, []int) {
	return file_validate_validate_proto_rawDescGZIP(), []int{21}
}

func (x *DurationRules) GetRequired() bool {
	if x != nil && x.Required != nil {
		return *x.Required
	}
	return false
}

func (x *DurationRules) GetConst() *durationpb.Duration {
	if x != nil {
		return x.Const
	}
	return nil
}

func (x *DurationRules) GetLt() *durationpb.Duration {
	if x != nil {
		return x.Lt
	}
	return nil
}

func (x *DurationRules) GetLte() *durationpb.Duration {
	if x != nil {
		return x.Lte
	}
	return nil
}

func (x *DurationRules) GetGt() *durationpb.Duration {
	if x != nil {
		return x.Gt
	}
	return nil
}

func (x *DurationRules) GetGte() *durationpb.Duration {
	if x != nil {
		return x.Gte
	}
	return nil
}

func (x *DurationRules) GetIn() []*durationpb.Duration {
	if x != nil {
		return x.In
	}
	return nil
}

func (x *DurationRules) GetNotIn() []*durationpb.Duration {
	if x != nil {
		return x.NotIn
	}
	return nil
}

// TimestampRules describe the constraints applied exclusively to the
// `google.protobuf.Timestamp` well-known type
type TimestampRules struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required specifies that this field must be set
	Required *bool `protobuf:"varint,1,opt,name=required" json:"required,omitempty"`
	// Const specifies that this field must be exactly the specified value
	Const *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=const" json:"const,omitempty"`
	// Lt specifies that this field must be less than the specified value,
	// exclusive
	Lt *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=lt" json:"lt,omitempty"`
	// Lte specifies that this field must be less than the specified value,
	// inclusive
	Lte *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=lte" json:"lte,omitempty"`
	// Gt specifies that this field must be greater than the specified value,
	// exclusive
	Gt *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=gt" json:"gt,omitempty"`
	// Gte specifies that this field must be greater than the specified value,
	// inclusive
	Gte *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=gte" json:"gte,omitempty"`
	// LtNow specifies that this must be less than the current time. LtNow
	// can only be used with the Within rule.
	LtNow *bool `protobuf:"varint,7,opt,name=lt_now,json=ltNow" json:"lt_now,omitempty"`
	// GtNow specifies that this must be greater than the current time. GtNow
	// can only be used with the Within rule.
	GtNow *bool `protobuf:"varint,8,opt,name=gt_now,json=gtNow" json:"gt_now,omitempty"`
	// Within specifies that this field must be within this duration of the
	// current time. This constraint can be used alone or with the LtNow and
	// GtNow rules.
	Within *durationpb.Duration `protobuf:"bytes,9,opt,name=within" json:"within,omitempty"`
}

func (x *TimestampRules) Reset() {
	*x = TimestampRules{}
	if protoimpl.UnsafeEnabled {
		mi := &file_validate_validate_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TimestampRules) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimestampRules) ProtoMessage() {}

func (x *TimestampRules) ProtoReflect() protoreflect.Message {
	mi := &file_validate_validate_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimestampRules.ProtoReflect.Descriptor instead.
func (*TimestampRules) Descriptor() ([]byte, []int) {
	return file_validate_validate_proto_rawDescGZIP(), []int{22}
}

func (x *TimestampRules) GetRequired() bool {
	if x != nil && x.Required != nil {
		return *x.Required
	}
	return false
}

func (x *TimestampRules) GetConst() *timestamppb.Timestamp {
	if x != nil {
		return x.Const
	}
	return nil
}

func (x *TimestampRules) GetLt() *timestamppb.Timestamp {
	if x != nil {
		return x.Lt
	}
	return nil
}

func (x *TimestampRules) GetLte() *timestamppb.Timestamp {
	if x != nil {
		return x.Lte
	}
	return nil
}

func (x *TimestampRules) GetGt() *timestamppb.Timestamp {
	if x != nil {
		return x.Gt
	}
	return nil
}

func (x *TimestampRules) GetGte() *timestamppb.Timestamp {
	if x != nil {
		return x.Gte
	}
	return nil
}

func (x *TimestampRules) GetLtNow() bool {
	if x != nil && x.LtNow != nil {
		return *x.LtNow
	}
	return false
}

func (x *TimestampRules) GetGtNow() bool {
	if x != nil && x.GtNow != nil {
		return *x.GtNow
	}
	return false
}

func (x *TimestampRules) GetWithin() *durationpb.Duration {
	if x != nil {
		return x.Within
	}
	return nil
}

var file_validate_validate_proto_extTypes = []protoimpl.ExtensionInfo{
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*bool)(nil),
		Field:         1071,
		Name:          "validate.disabled",
		Tag:           "varint,1071,opt,name=disabled",
		Filename:      "validate/validate.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*bool)(nil),
		Field:         1072,
		Name:          "validate.ignored",
		Tag:           "varint,1072,opt,name=ignored",
		Filename:      "validate/validate.proto",
	},
	{
		ExtendedType:  (*descriptorpb.OneofOptions)(nil),
		ExtensionType: (*bool)(nil),
		Field:         1071,
		Name:          "validate.required",
		Tag:           "varint,1071,opt,name=required",
		Filename:      "validate/validate.proto",
	},
	{
		ExtendedType:  (*descriptorpb.FieldOptions)(nil),
		ExtensionType: (*FieldRules)(nil),
		Field:         1071,
		Name:          "validate.rules",
		Tag:           "bytes,1071,opt,name=rules",
		Filename:      "validate/validate.proto",
	},
}

// Extension fields to descriptorpb.MessageOptions.
var (
	// Disabled nullifies any validation rules for this message, including any
	// message fields associated with it that do support validation.
	//
	// optional bool disabled = 1071;
	E_Disabled = &file_validate_validate_proto_extTypes[0]
	// Ignore skips generation of validation methods for this message.
	//
	// optional bool ignored = 1072;
	E_Ignored = &file_validate_validate_proto_extTypes[1]
)

// Extension fields to descriptorpb.OneofOptions.
var (
	// Required ensures that exactly one the field options in a oneof is set;
	// validation fails if no fields in the oneof are set.
	//
	// optional bool required = 1071;
	E_Required = &file_validate_validate_proto_extTypes[2]
)

// Extension fields to descriptorpb.FieldOptions.
var (
	// Rules specify the validations to be performed on this field. By default,
	// no validation is performed against a field.
	//
	// optional validate.FieldRules rules = 1071;
	E_Rules = &file_validate_validate_proto_extTypes[3]
)

var File_validate_validate_proto protoreflect.FileDescriptor

var file_validate_validate_proto_rawDesc = []byte{
	0x0a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x1a, 0x20, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x6f, 0x72, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xc8, 0x08, 0x0a, 0x0a, 0x46, 0x69, 0x65, 0x6c, 0x64,
	0x52, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x30, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x52, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x2c, 0x0a, 0x05, 0x66, 0x6c, 0x6f, 0x61, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2e, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x48, 0x00, 0x52, 0x05,
	0x66, 0x6c, 0x6f, 0x61, 0x74, 0x12, 0x2f, 0x0a, 0x06, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2e, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x48, 0x00, 0x52, 0x06,
	0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x12, 0x2c, 0x0a, 0x05, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2e, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x48, 0x00, 0x52, 0x05, 0x69,
	0x6e, 0x74, 0x33, 0x32, 0x12, 0x2c, 0x0a, 0x05, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x49,
	0x6e, 0x74, 0x36, 0x34, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x48, 0x00, 0x52, 0x05, 0x69, 0x6e, 0x74,
	0x36, 0x34, 0x12, 0x2f, 0x0a, 0x06, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x15, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x55, 0x49,
	0x6e, 0x74, 0x33, 0x32, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x48, 0x00, 0x52, 0x06, 0x75, 0x69, 0x6e,
	0x74, 0x33, 0x32, 0x12, 0x2f, 0x0a, 0x06, 0x75, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x55,
	0x49, 0x6e, 0x74, 0x36, 0x34, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x48, 0x00, 0x52, 0x06, 0x75, 0x69,
	0x6e, 0x74, 0x36, 0x34, 0x12, 0x2f, 0x0a, 0x06, 0x73, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e,
	0x53, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x48, 0x00, 0x52, 0x06, 0x73,
	0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x2f, 0x0a, 0x06, 0x73, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2e, 0x53, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x48, 0x00, 0x52, 0x06,
	0x73, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x32, 0x0a, 0x07, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33,
	0x32, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2e, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x48,
	0x00, 0x52, 0x07, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x12, 0x32, 0x0a, 0x07, 0x66, 0x69,
	0x78, 0x65, 0x64, 0x36, 0x34, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x52, 0x75,
	0x6c, 0x65, 0x73, 0x48, 0x00, 0x52, 0x07, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x35,
	0x0a, 0x08, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x17, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x53, 0x46, 0x69, 0x78,
	0x65, 0x64, 0x33, 0x32, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x48, 0x00, 0x52, 0x08, 0x73, 0x66, 0x69,
	0x78, 0x65, 0x64, 0x33, 0x32, 0x12, 0x35, 0x0a, 0x08, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36,
	0x34, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2e, 0x53, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x52, 0x75, 0x6c, 0x65, 0x73,
	0x48, 0x00, 0x52, 0x08, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x29, 0x0a, 0x04,
	0x62, 0x6f, 0x6f, 0x6c, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x48,
	0x00, 0x52, 0x04, 0x62, 0x6f, 0x6f, 0x6c, 0x12, 0x2f, 0x0a, 0x06, 0x73, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x48, 0x00,
	0x52, 0x06, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x2c, 0x0a, 0x05, 0x62, 0x79, 0x74, 0x65,
	0x73, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2e, 0x42, 0x79, 0x74, 0x65, 0x73, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x48, 0x00, 0x52,
	0x05, 0x62, 0x79, 0x74, 0x65, 0x73, 0x12, 0x29, 0x0a, 0x04, 0x65, 0x6e, 0x75, 0x6d, 0x18, 0x10,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e,
	0x45, 0x6e, 0x75, 0x6d, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x48, 0x00, 0x52, 0x04, 0x65, 0x6e, 0x75,
	0x6d, 0x12, 0x35, 0x0a, 0x08, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x18, 0x12, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x52,
	0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x48, 0x00, 0x52, 0x08,
	0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x12, 0x26, 0x0a, 0x03, 0x6d, 0x61, 0x70, 0x18,
	0x13, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2e, 0x4d, 0x61, 0x70, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x48, 0x00, 0x52, 0x03, 0x6d, 0x61, 0x70,
	0x12, 0x26, 0x0a, 0x03, 0x61, 0x6e, 0x79, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x41, 0x6e, 0x79, 0x52, 0x75, 0x6c, 0x65,
	0x73, 0x48, 0x00, 0x52, 0x03, 0x61, 0x6e, 0x79, 0x12, 0x35, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x75,
	0x6c, 0x65, 0x73, 0x48, 0x00, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x38, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x16, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x48, 0x00, 0x52, 0x09,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x06, 0x0a, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x22, 0xb0, 0x01, 0x0a, 0x0a, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x73,
	0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x05, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x6c, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x02, 0x6c, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x74, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x03, 0x6c, 0x74, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x67, 0x74, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x02, 0x67, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x67, 0x74, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x02, 0x52, 0x03, 0x67, 0x74, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x6e,
	0x18, 0x06, 0x20, 0x03, 0x28, 0x02, 0x52, 0x02, 0x69, 0x6e, 0x12, 0x15, 0x0a, 0x06, 0x6e, 0x6f,
	0x74, 0x5f, 0x69, 0x6e, 0x18, 0x07, 0x20, 0x03, 0x28, 0x02, 0x52, 0x05, 0x6e, 0x6f, 0x74, 0x49,
	0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x5f, 0x65, 0x6d, 0x70, 0x74,
	0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x22, 0xb1, 0x01, 0x0a, 0x0b, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x52,
	0x75, 0x6c, 0x65, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x05, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x6c, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x02, 0x6c, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x74,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x6c, 0x74, 0x65, 0x12, 0x0e, 0x0a, 0x02,
	0x67, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52, 0x02, 0x67, 0x74, 0x12, 0x10, 0x0a, 0x03,
	0x67, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x67, 0x74, 0x65, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x6e, 0x18, 0x06, 0x20, 0x03, 0x28, 0x01, 0x52, 0x02, 0x69, 0x6e, 0x12, 0x15,
	0x0a, 0x06, 0x6e, 0x6f, 0x74, 0x5f, 0x69, 0x6e, 0x18, 0x07, 0x20, 0x03, 0x28, 0x01, 0x52, 0x05,
	0x6e, 0x6f, 0x74, 0x49, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x5f,
	0x65, 0x6d, 0x70, 0x74, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x67, 0x6e,
	0x6f, 0x72, 0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0xb0, 0x01, 0x0a, 0x0a, 0x49, 0x6e, 0x74,
	0x33, 0x32, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x6e, 0x73, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x12, 0x0e, 0x0a,
	0x02, 0x6c, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x6c, 0x74, 0x12, 0x10, 0x0a,
	0x03, 0x6c, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6c, 0x74, 0x65, 0x12,
	0x0e, 0x0a, 0x02, 0x67, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x67, 0x74, 0x12,
	0x10, 0x0a, 0x03, 0x67, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x67, 0x74,
	0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x6e, 0x18, 0x06, 0x20, 0x03, 0x28, 0x05, 0x52, 0x02, 0x69,
	0x6e, 0x12, 0x15, 0x0a, 0x06, 0x6e, 0x6f, 0x74, 0x5f, 0x69, 0x6e, 0x18, 0x07, 0x20, 0x03, 0x28,
	0x05, 0x52, 0x05, 0x6e, 0x6f, 0x74, 0x49, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x67, 0x6e, 0x6f,
	0x72, 0x65, 0x5f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b,
	0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0xb0, 0x01, 0x0a, 0x0a,
	0x49, 0x6e, 0x74, 0x36, 0x34, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f,
	0x6e, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x63, 0x6f, 0x6e, 0x73, 0x74,
	0x12, 0x0e, 0x0a, 0x02, 0x6c, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x6c, 0x74,
	0x12, 0x10, 0x0a, 0x03, 0x6c, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6c,
	0x74, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x67, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02,
	0x67, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x67, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x03, 0x67, 0x74, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x6e, 0x18, 0x06, 0x20, 0x03, 0x28, 0x03,
	0x52, 0x02, 0x69, 0x6e, 0x12, 0x15, 0x0a, 0x06, 0x6e, 0x6f, 0x74, 0x5f, 0x69, 0x6e, 0x18, 0x07,
	0x20, 0x03, 0x28, 0x03, 0x52, 0x05, 0x6e, 0x6f, 0x74, 0x49, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x69,
	0x67, 0x6e, 0x6f, 0x72, 0x65, 0x5f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0b, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0xb1,
	0x01, 0x0a, 0x0b, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x14,
	0x0a, 0x05, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x63,
	0x6f, 0x6e, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x6c, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x02, 0x6c, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x03, 0x6c, 0x74, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x67, 0x74, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x02, 0x67, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x67, 0x74, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x03, 0x67, 0x74, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x6e, 0x18, 0x06,
	0x20, 0x03, 0x28, 0x0d, 0x52, 0x02, 0x69, 0x6e, 0x12, 0x15, 0x0a, 0x06, 0x6e, 0x6f, 0x74, 0x5f,
	0x69, 0x6e, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x05, 0x6e, 0x6f, 0x74, 0x49, 0x6e, 0x12,
	0x21, 0x0a, 0x0c, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x5f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x22, 0xb1, 0x01, 0x0a, 0x0b, 0x55, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x52, 0x75, 0x6c,
	0x65, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x05, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x6c, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x6c, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x74, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x6c, 0x74, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x67, 0x74,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x67, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x67, 0x74,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x67, 0x74, 0x65, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x6e, 0x18, 0x06, 0x20, 0x03, 0x28, 0x04, 0x52, 0x02, 0x69, 0x6e, 0x12, 0x15, 0x0a, 0x06,
	0x6e, 0x6f, 0x74, 0x5f, 0x69, 0x6e, 0x18, 0x07, 0x20, 0x03, 0x28, 0x04, 0x52, 0x05, 0x6e, 0x6f,
	0x74, 0x49, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x5f, 0x65, 0x6d,
	0x70, 0x74, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x67, 0x6e, 0x6f, 0x72,
	0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0xb1, 0x01, 0x0a, 0x0b, 0x53, 0x49, 0x6e, 0x74, 0x33,
	0x32, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x11, 0x52, 0x05, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02,
	0x6c, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x11, 0x52, 0x02, 0x6c, 0x74, 0x12, 0x10, 0x0a, 0x03,
	0x6c, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x11, 0x52, 0x03, 0x6c, 0x74, 0x65, 0x12, 0x0e,
	0x0a, 0x02, 0x67, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x11, 0x52, 0x02, 0x67, 0x74, 0x12, 0x10,
	0x0a, 0x03, 0x67, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x11, 0x52, 0x03, 0x67, 0x74, 0x65,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x6e, 0x18, 0x06, 0x20, 0x03, 0x28, 0x11, 0x52, 0x02, 0x69, 0x6e,
	0x12, 0x15, 0x0a, 0x06, 0x6e, 0x6f, 0x74, 0x5f, 0x69, 0x6e, 0x18, 0x07, 0x20, 0x03, 0x28, 0x11,
	0x52, 0x05, 0x6e, 0x6f, 0x74, 0x49, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x67, 0x6e, 0x6f, 0x72,
	0x65, 0x5f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69,
	0x67, 0x6e, 0x6f, 0x72, 0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0xb1, 0x01, 0x0a, 0x0b, 0x53,
	0x49, 0x6e, 0x74, 0x36, 0x34, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f,
	0x6e, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x12, 0x52, 0x05, 0x63, 0x6f, 0x6e, 0x73, 0x74,
	0x12, 0x0e, 0x0a, 0x02, 0x6c, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x12, 0x52, 0x02, 0x6c, 0x74,
	0x12, 0x10, 0x0a, 0x03, 0x6c, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x12, 0x52, 0x03, 0x6c,
	0x74, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x67, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x12, 0x52, 0x02,
	0x67, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x67, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x12, 0x52,
	0x03, 0x67, 0x74, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x6e, 0x18, 0x06, 0x20, 0x03, 0x28, 0x12,
	0x52, 0x02, 0x69, 0x6e, 0x12, 0x15, 0x0a, 0x06, 0x6e, 0x6f, 0x74, 0x5f, 0x69, 0x6e, 0x18, 0x07,
	0x20, 0x03, 0x28, 0x12, 0x52, 0x05, 0x6e, 0x6f, 0x74, 0x49, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x69,
	0x67, 0x6e, 0x6f, 0x72, 0x65, 0x5f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0b, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0xb2,
	0x01, 0x0a, 0x0c, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x12,
	0x14, 0x0a, 0x05, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x07, 0x52, 0x05,
	0x63, 0x6f, 0x6e, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x6c, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x07, 0x52, 0x02, 0x6c, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x07, 0x52, 0x03, 0x6c, 0x74, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x67, 0x74, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x07, 0x52, 0x02, 0x67, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x67, 0x74, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x07, 0x52, 0x03, 0x67, 0x74, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x6e, 0x18,
	0x06, 0x20, 0x03, 0x28, 0x07, 0x52, 0x02, 0x69, 0x6e, 0x12, 0x15, 0x0a, 0x06, 0x6e, 0x6f, 0x74,
	0x5f, 0x69, 0x6e, 0x18, 0x07, 0x20, 0x03, 0x28, 0x07, 0x52, 0x05, 0x6e, 0x6f, 0x74, 0x49, 0x6e,
	0x12, 0x21, 0x0a, 0x0c, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x5f, 0x65, 0x6d, 0x70, 0x74, 0x79,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x22, 0xb2, 0x01, 0x0a, 0x0c, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x52,
	0x75, 0x6c, 0x65, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x06, 0x52, 0x05, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x6c, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x06, 0x52, 0x02, 0x6c, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x74,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x06, 0x52, 0x03, 0x6c, 0x74, 0x65, 0x12, 0x0e, 0x0a, 0x02,
	0x67, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x06, 0x52, 0x02, 0x67, 0x74, 0x12, 0x10, 0x0a, 0x03,
	0x67, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x06, 0x52, 0x03, 0x67, 0x74, 0x65, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x6e, 0x18, 0x06, 0x20, 0x03, 0x28, 0x06, 0x52, 0x02, 0x69, 0x6e, 0x12, 0x15,
	0x0a, 0x06, 0x6e, 0x6f, 0x74, 0x5f, 0x69, 0x6e, 0x18, 0x07, 0x20, 0x03, 0x28, 0x06, 0x52, 0x05,
	0x6e, 0x6f, 0x74, 0x49, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x5f,
	0x65, 0x6d, 0x70, 0x74, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x67, 0x6e,
	0x6f, 0x72, 0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0xb3, 0x01, 0x0a, 0x0d, 0x53, 0x46, 0x69,
	0x78, 0x65, 0x64, 0x33, 0x32, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f,
	0x6e, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0f, 0x52, 0x05, 0x63, 0x6f, 0x6e, 0x73, 0x74,
	0x12, 0x0e, 0x0a, 0x02, 0x6c, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0f, 0x52, 0x02, 0x6c, 0x74,
	0x12, 0x10, 0x0a, 0x03, 0x6c, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0f, 0x52, 0x03, 0x6c,
	0x74, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x67, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0f, 0x52, 0x02,
	0x67, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x67, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0f, 0x52,
	0x03, 0x67, 0x74, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x6e, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0f,
	0x52, 0x02, 0x69, 0x6e, 0x12, 0x15, 0x0a, 0x06, 0x6e, 0x6f, 0x74, 0x5f, 0x69, 0x6e, 0x18, 0x07,
	0x20, 0x03, 0x28, 0x0f, 0x52, 0x05, 0x6e, 0x6f, 0x74, 0x49, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x69,
	0x67, 0x6e, 0x6f, 0x72, 0x65, 0x5f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0b, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0xb3,
	0x01, 0x0a, 0x0d, 0x53, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x52, 0x75, 0x6c, 0x65, 0x73,
	0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x10, 0x52,
	0x05, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x6c, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x10, 0x52, 0x02, 0x6c, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x74, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x10, 0x52, 0x03, 0x6c, 0x74, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x67, 0x74, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x10, 0x52, 0x02, 0x67, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x67, 0x74, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x10, 0x52, 0x03, 0x67, 0x74, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x6e,
	0x18, 0x06, 0x20, 0x03, 0x28, 0x10, 0x52, 0x02, 0x69, 0x6e, 0x12, 0x15, 0x0a, 0x06, 0x6e, 0x6f,
	0x74, 0x5f, 0x69, 0x6e, 0x18, 0x07, 0x20, 0x03, 0x28, 0x10, 0x52, 0x05, 0x6e, 0x6f, 0x74, 0x49,
	0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x5f, 0x65, 0x6d, 0x70, 0x74,
	0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x22, 0x21, 0x0a, 0x09, 0x42, 0x6f, 0x6f, 0x6c, 0x52, 0x75, 0x6c, 0x65,
	0x73, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x05, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x22, 0xd4, 0x05, 0x0a, 0x0b, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x6e, 0x73, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x12, 0x10, 0x0a,
	0x03, 0x6c, 0x65, 0x6e, 0x18, 0x13, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x6c, 0x65, 0x6e, 0x12,
	0x17, 0x0a, 0x07, 0x6d, 0x69, 0x6e, 0x5f, 0x6c, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x06, 0x6d, 0x69, 0x6e, 0x4c, 0x65, 0x6e, 0x12, 0x17, 0x0a, 0x07, 0x6d, 0x61, 0x78, 0x5f,
	0x6c, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x6d, 0x61, 0x78, 0x4c, 0x65,
	0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x6c, 0x65, 0x6e, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18, 0x14,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x6c, 0x65, 0x6e, 0x42, 0x79, 0x74, 0x65, 0x73, 0x12, 0x1b,
	0x0a, 0x09, 0x6d, 0x69, 0x6e, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x08, 0x6d, 0x69, 0x6e, 0x42, 0x79, 0x74, 0x65, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x6d,
	0x61, 0x78, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08,
	0x6d, 0x61, 0x78, 0x42, 0x79, 0x74, 0x65, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x61, 0x74, 0x74,
	0x65, 0x72, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x61, 0x74, 0x74, 0x65,
	0x72, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x72, 0x65, 0x66, 0x69, 0x78, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x70, 0x72, 0x65, 0x66, 0x69, 0x78, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x75,
	0x66, 0x66, 0x69, 0x78, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x75, 0x66, 0x66,
	0x69, 0x78, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x73, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x73, 0x12, 0x21,
	0x0a, 0x0c, 0x6e, 0x6f, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x73, 0x18, 0x17,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6e, 0x6f, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e,
	0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x6e, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x09, 0x52, 0x02, 0x69,
	0x6e, 0x12, 0x15, 0x0a, 0x06, 0x6e, 0x6f, 0x74, 0x5f, 0x69, 0x6e, 0x18, 0x0b, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x05, 0x6e, 0x6f, 0x74, 0x49, 0x6e, 0x12, 0x16, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69,
	0x6c, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x12, 0x1c, 0x0a, 0x08, 0x68, 0x6f, 0x73, 0x74, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x08, 0x48, 0x00, 0x52, 0x08, 0x68, 0x6f, 0x73, 0x74, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x10,
	0x0a, 0x02, 0x69, 0x70, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x02, 0x69, 0x70,
	0x12, 0x14, 0x0a, 0x04, 0x69, 0x70, 0x76, 0x34, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00,
	0x52, 0x04, 0x69, 0x70, 0x76, 0x34, 0x12, 0x14, 0x0a, 0x04, 0x69, 0x70, 0x76, 0x36, 0x18, 0x10,
	0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x04, 0x69, 0x70, 0x76, 0x36, 0x12, 0x12, 0x0a, 0x03,
	0x75, 0x72, 0x69, 0x18, 0x11, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x03, 0x75, 0x72, 0x69,
	0x12, 0x19, 0x0a, 0x07, 0x75, 0x72, 0x69, 0x5f, 0x72, 0x65, 0x66, 0x18, 0x12, 0x20, 0x01, 0x28,
	0x08, 0x48, 0x00, 0x52, 0x06, 0x75, 0x72, 0x69, 0x52, 0x65, 0x66, 0x12, 0x1a, 0x0a, 0x07, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x15, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x07,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x14, 0x0a, 0x04, 0x75, 0x75, 0x69, 0x64, 0x18,
	0x16, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x04, 0x75, 0x75, 0x69, 0x64, 0x12, 0x40, 0x0a,
	0x10, 0x77, 0x65, 0x6c, 0x6c, 0x5f, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x5f, 0x72, 0x65, 0x67, 0x65,
	0x78, 0x18, 0x18, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2e, 0x4b, 0x6e, 0x6f, 0x77, 0x6e, 0x52, 0x65, 0x67, 0x65, 0x78, 0x48, 0x00, 0x52,
	0x0e, 0x77, 0x65, 0x6c, 0x6c, 0x4b, 0x6e, 0x6f, 0x77, 0x6e, 0x52, 0x65, 0x67, 0x65, 0x78, 0x12,
	0x1c, 0x0a, 0x06, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x18, 0x19, 0x20, 0x01, 0x28, 0x08, 0x3a,
	0x04, 0x74, 0x72, 0x75, 0x65, 0x52, 0x06, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x12, 0x21, 0x0a,
	0x0c, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x5f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x18, 0x1a, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x45, 0x6d, 0x70, 0x74, 0x79,
	0x42, 0x0c, 0x0a, 0x0a, 0x77, 0x65, 0x6c, 0x6c, 0x5f, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x22, 0xe2,
	0x02, 0x0a, 0x0a, 0x42, 0x79, 0x74, 0x65, 0x73, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x14, 0x0a,
	0x05, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x05, 0x63, 0x6f,
	0x6e, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x65, 0x6e, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x03, 0x6c, 0x65, 0x6e, 0x12, 0x17, 0x0a, 0x07, 0x6d, 0x69, 0x6e, 0x5f, 0x6c, 0x65, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x6d, 0x69, 0x6e, 0x4c, 0x65, 0x6e, 0x12, 0x17,
	0x0a, 0x07, 0x6d, 0x61, 0x78, 0x5f, 0x6c, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x06, 0x6d, 0x61, 0x78, 0x4c, 0x65, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x61, 0x74, 0x74, 0x65,
	0x72, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x61, 0x74, 0x74, 0x65, 0x72,
	0x6e, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x72, 0x65, 0x66, 0x69, 0x78, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0c, 0x52, 0x06, 0x70, 0x72, 0x65, 0x66, 0x69, 0x78, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x75, 0x66,
	0x66, 0x69, 0x78, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x06, 0x73, 0x75, 0x66, 0x66, 0x69,
	0x78, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x73, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0c, 0x52, 0x08, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x73, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x6e, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0c, 0x52, 0x02, 0x69, 0x6e, 0x12, 0x15, 0x0a,
	0x06, 0x6e, 0x6f, 0x74, 0x5f, 0x69, 0x6e, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0c, 0x52, 0x05, 0x6e,
	0x6f, 0x74, 0x49, 0x6e, 0x12, 0x10, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08,
	0x48, 0x00, 0x52, 0x02, 0x69, 0x70, 0x12, 0x14, 0x0a, 0x04, 0x69, 0x70, 0x76, 0x34, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x04, 0x69, 0x70, 0x76, 0x34, 0x12, 0x14, 0x0a, 0x04,
	0x69, 0x70, 0x76, 0x36, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x04, 0x69, 0x70,
	0x76, 0x36, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x5f, 0x65, 0x6d, 0x70,
	0x74, 0x79, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x42, 0x0c, 0x0a, 0x0a, 0x77, 0x65, 0x6c, 0x6c, 0x5f, 0x6b, 0x6e,
	0x6f, 0x77, 0x6e, 0x22, 0x6b, 0x0a, 0x09, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x75, 0x6c, 0x65, 0x73,
	0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x05, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65,
	0x64, 0x5f, 0x6f, 0x6e, 0x6c, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x64, 0x65,
	0x66, 0x69, 0x6e, 0x65, 0x64, 0x4f, 0x6e, 0x6c, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x6e, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x05, 0x52, 0x02, 0x69, 0x6e, 0x12, 0x15, 0x0a, 0x06, 0x6e, 0x6f, 0x74,
	0x5f, 0x69, 0x6e, 0x18, 0x04, 0x20, 0x03, 0x28, 0x05, 0x52, 0x05, 0x6e, 0x6f, 0x74, 0x49, 0x6e,
	0x22, 0x3e, 0x0a, 0x0c, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x73,
	0x12, 0x12, 0x0a, 0x04, 0x73, 0x6b, 0x69, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x04,
	0x73, 0x6b, 0x69, 0x70, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64,
	0x22, 0xb0, 0x01, 0x0a, 0x0d, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x52, 0x75, 0x6c,
	0x65, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x69, 0x6e, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x6d, 0x69, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12,
	0x1b, 0x0a, 0x09, 0x6d, 0x61, 0x78, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x08, 0x6d, 0x61, 0x78, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x16, 0x0a, 0x06,
	0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x75, 0x6e,
	0x69, 0x71, 0x75, 0x65, 0x12, 0x2a, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x46,
	0x69, 0x65, 0x6c, 0x64, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73,
	0x12, 0x21, 0x0a, 0x0c, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x5f, 0x65, 0x6d, 0x70, 0x74, 0x79,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x22, 0xdc, 0x01, 0x0a, 0x08, 0x4d, 0x61, 0x70, 0x52, 0x75, 0x6c, 0x65, 0x73,
	0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x69, 0x6e, 0x5f, 0x70, 0x61, 0x69, 0x72, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x08, 0x6d, 0x69, 0x6e, 0x50, 0x61, 0x69, 0x72, 0x73, 0x12, 0x1b, 0x0a,
	0x09, 0x6d, 0x61, 0x78, 0x5f, 0x70, 0x61, 0x69, 0x72, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x08, 0x6d, 0x61, 0x78, 0x50, 0x61, 0x69, 0x72, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x6e, 0x6f,
	0x5f, 0x73, 0x70, 0x61, 0x72, 0x73, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x6e,
	0x6f, 0x53, 0x70, 0x61, 0x72, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x6b, 0x65, 0x79, 0x73, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x52, 0x04, 0x6b, 0x65, 0x79,
	0x73, 0x12, 0x2c, 0x0a, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x14, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x46, 0x69, 0x65,
	0x6c, 0x64, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x52, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x12,
	0x21, 0x0a, 0x0c, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x5f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x22, 0x4d, 0x0a, 0x08, 0x41, 0x6e, 0x79, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x1a,
	0x0a, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x6e,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x02, 0x69, 0x6e, 0x12, 0x15, 0x0a, 0x06, 0x6e, 0x6f,
	0x74, 0x5f, 0x69, 0x6e, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x6e, 0x6f, 0x74, 0x49,
	0x6e, 0x22, 0xe9, 0x02, 0x0a, 0x0d, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x75,
	0x6c, 0x65, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x12,
	0x2f, 0x0a, 0x05, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x05, 0x63, 0x6f, 0x6e, 0x73, 0x74,
	0x12, 0x29, 0x0a, 0x02, 0x6c, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x02, 0x6c, 0x74, 0x12, 0x2b, 0x0a, 0x03, 0x6c,
	0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x03, 0x6c, 0x74, 0x65, 0x12, 0x29, 0x0a, 0x02, 0x67, 0x74, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x02, 0x67, 0x74, 0x12, 0x2b, 0x0a, 0x03, 0x67, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x03, 0x67, 0x74, 0x65,
	0x12, 0x29, 0x0a, 0x02, 0x69, 0x6e, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x02, 0x69, 0x6e, 0x12, 0x30, 0x0a, 0x06, 0x6e,
	0x6f, 0x74, 0x5f, 0x69, 0x6e, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x05, 0x6e, 0x6f, 0x74, 0x49, 0x6e, 0x22, 0xf3, 0x02,
	0x0a, 0x0e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x75, 0x6c, 0x65, 0x73,
	0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x12, 0x30, 0x0a, 0x05,
	0x63, 0x6f, 0x6e, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x05, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x12, 0x2a,
	0x0a, 0x02, 0x6c, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x02, 0x6c, 0x74, 0x12, 0x2c, 0x0a, 0x03, 0x6c, 0x74,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x03, 0x6c, 0x74, 0x65, 0x12, 0x2a, 0x0a, 0x02, 0x67, 0x74, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x02, 0x67, 0x74, 0x12, 0x2c, 0x0a, 0x03, 0x67, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x03, 0x67,
	0x74, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x6c, 0x74, 0x5f, 0x6e, 0x6f, 0x77, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x05, 0x6c, 0x74, 0x4e, 0x6f, 0x77, 0x12, 0x15, 0x0a, 0x06, 0x67, 0x74, 0x5f,
	0x6e, 0x6f, 0x77, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x67, 0x74, 0x4e, 0x6f, 0x77,
	0x12, 0x31, 0x0a, 0x06, 0x77, 0x69, 0x74, 0x68, 0x69, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x06, 0x77, 0x69, 0x74,
	0x68, 0x69, 0x6e, 0x2a, 0x46, 0x0a, 0x0a, 0x4b, 0x6e, 0x6f, 0x77, 0x6e, 0x52, 0x65, 0x67, 0x65,
	0x78, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x14,
	0x0a, 0x10, 0x48, 0x54, 0x54, 0x50, 0x5f, 0x48, 0x45, 0x41, 0x44, 0x45, 0x52, 0x5f, 0x4e, 0x41,
	0x4d, 0x45, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11, 0x48, 0x54, 0x54, 0x50, 0x5f, 0x48, 0x45, 0x41,
	0x44, 0x45, 0x52, 0x5f, 0x56, 0x41, 0x4c, 0x55, 0x45, 0x10, 0x02, 0x3a, 0x3c, 0x0a, 0x08, 0x64,
	0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xaf, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x08, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x3a, 0x3a, 0x0a, 0x07, 0x69, 0x67, 0x6e,
	0x6f, 0x72, 0x65, 0x64, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xb0, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x69, 0x67,
	0x6e, 0x6f, 0x72, 0x65, 0x64, 0x3a, 0x3a, 0x0a, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x12, 0x1d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x4f, 0x6e, 0x65, 0x6f, 0x66, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x18, 0xaf, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x3a, 0x4a, 0x0a, 0x05, 0x72, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x1d, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x46, 0x69, 0x65,
	0x6c, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xaf, 0x08, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x14, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x46, 0x69, 0x65, 0x6c,
	0x64, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x52, 0x05, 0x72, 0x75, 0x6c, 0x65, 0x73, 0x42, 0x50, 0x0a,
	0x1a, 0x69, 0x6f, 0x2e, 0x65, 0x6e, 0x76, 0x6f, 0x79, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x2e, 0x70,
	0x67, 0x76, 0x2e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x5a, 0x32, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x6e, 0x76, 0x6f, 0x79, 0x70, 0x72, 0x6f,
	0x78, 0x79, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2d, 0x67, 0x65, 0x6e, 0x2d, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
}

var (
	file_validate_validate_proto_rawDescOnce sync.Once
	file_validate_validate_proto_rawDescData = file_validate_validate_proto_rawDesc
)

func file_validate_validate_proto_rawDescGZIP() []byte {
	file_validate_validate_proto_rawDescOnce.Do(func() {
		file_validate_validate_proto_rawDescData = protoimpl.X.CompressGZIP(file_validate_validate_proto_rawDescData)
	})
	return file_validate_validate_proto_rawDescData
}

var file_validate_validate_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_validate_validate_proto_msgTypes = make([]protoimpl.MessageInfo, 23)
var file_validate_validate_proto_goTypes = []interface{}{
	(KnownRegex)(0),                     // 0: validate.KnownRegex
	(*FieldRules)(nil),                  // 1: validate.FieldRules
	(*FloatRules)(nil),                  // 2: validate.FloatRules
	(*DoubleRules)(nil),                 // 3: validate.DoubleRules
	(*Int32Rules)(nil),                  // 4: validate.Int32Rules
	(*Int64Rules)(nil),                  // 5: validate.Int64Rules
	(*UInt32Rules)(nil),                 // 6: validate.UInt32Rules
	(*UInt64Rules)(nil),                 // 7: validate.UInt64Rules
	(*SInt32Rules)(nil),                 // 8: validate.SInt32Rules
	(*SInt64Rules)(nil),                 // 9: validate.SInt64Rules
	(*Fixed32Rules)(nil),                // 10: validate.Fixed32Rules
	(*Fixed64Rules)(nil),                // 11: validate.Fixed64Rules
	(*SFixed32Rules)(nil),               // 12: validate.SFixed32Rules
	(*SFixed64Rules)(nil),               // 13: validate.SFixed64Rules
	(*BoolRules)(nil),                   // 14: validate.BoolRules
	(*StringRules)(nil),                 // 15: validate.StringRules
	(*BytesRules)(nil),                  // 16: validate.BytesRules
	(*EnumRules)(nil),                   // 17: validate.EnumRules
	(*MessageRules)(nil),                // 18: validate.MessageRules
	(*RepeatedRules)(nil),               // 19: validate.RepeatedRules
	(*MapRules)(nil),                    // 20: validate.MapRules
	(*AnyRules)(nil),                    // 21: validate.AnyRules
	(*DurationRules)(nil),               // 22: validate.DurationRules
	(*TimestampRules)(nil),              // 23: validate.TimestampRules
	(*durationpb.Duration)(nil),         // 24: google.protobuf.Duration
	(*timestamppb.Timestamp)(nil),       // 25: google.protobuf.Timestamp
	(*descriptorpb.MessageOptions)(nil), // 26: google.protobuf.MessageOptions
	(*descriptorpb.OneofOptions)(nil),   // 27: google.protobuf.OneofOptions
	(*descriptorpb.FieldOptions)(nil),   // 28: google.protobuf.FieldOptions
}
var file_validate_validate_proto_depIdxs = []int32{
	18, // 0: validate.FieldRules.message:type_name -> validate.MessageRules
	2,  // 1: validate.FieldRules.float:type_name -> validate.FloatRules
	3,  // 2: validate.FieldRules.double:type_name -> validate.DoubleRules
	4,  // 3: validate.FieldRules.int32:type_name -> validate.Int32Rules
	5,  // 4: validate.FieldRules.int64:type_name -> validate.Int64Rules
	6,  // 5: validate.FieldRules.uint32:type_name -> validate.UInt32Rules
	7,  // 6: validate.FieldRules.uint64:type_name -> validate.UInt64Rules
	8,  // 7: validate.FieldRules.sint32:type_name -> validate.SInt32Rules
	9,  // 8: validate.FieldRules.sint64:type_name -> validate.SInt64Rules
	10, // 9: validate.FieldRules.fixed32:type_name -> validate.Fixed32Rules
	11, // 10: validate.FieldRules.fixed64:type_name -> validate.Fixed64Rules
	12, // 11: validate.FieldRules.sfixed32:type_name -> validate.SFixed32Rules
	13, // 12: validate.FieldRules.sfixed64:type_name -> validate.SFixed64Rules
	14, // 13: validate.FieldRules.bool:type_name -> validate.BoolRules
	15, // 14: validate.FieldRules.string:type_name -> validate.StringRules
	16, // 15: validate.FieldRules.bytes:type_name -> validate.BytesRules
	17, // 16: validate.FieldRules.enum:type_name -> validate.EnumRules
	19, // 17: validate.FieldRules.repeated:type_name -> validate.RepeatedRules
	20, // 18: validate.FieldRules.map:type_name -> validate.MapRules
	21, // 19: validate.FieldRules.any:type_name -> validate.AnyRules
	22, // 20: validate.FieldRules.duration:type_name -> validate.DurationRules
	23, // 21: validate.FieldRules.timestamp:type_name -> validate.TimestampRules
	0,  // 22: validate.StringRules.well_known_regex:type_name -> validate.KnownRegex
	1,  // 23: validate.RepeatedRules.items:type_name -> validate.FieldRules
	1,  // 24: validate.MapRules.keys:type_name -> validate.FieldRules
	1,  // 25: validate.MapRules.values:type_name -> validate.FieldRules
	24, // 26: validate.DurationRules.const:type_name -> google.protobuf.Duration
	24, // 27: validate.DurationRules.lt:type_name -> google.protobuf.Duration
	24, // 28: validate.DurationRules.lte:type_name -> google.protobuf.Duration
	24, // 29: validate.DurationRules.gt:type_name -> google.protobuf.Duration
	24, // 30: validate.DurationRules.gte:type_name -> google.protobuf.Duration
	24, // 31: validate.DurationRules.in:type_name -> google.protobuf.Duration
	24, // 32: validate.DurationRules.not_in:type_name -> google.protobuf.Duration
	25, // 33: validate.TimestampRules.const:type_name -> google.protobuf.Timestamp
	25, // 34: validate.TimestampRules.lt:type_name -> google.protobuf.Timestamp
	25, // 35: validate.TimestampRules.lte:type_name -> google.protobuf.Timestamp
	25, // 36: validate.TimestampRules.gt:type_name -> google.protobuf.Timestamp
	25, // 37: validate.TimestampRules.gte:type_name -> google.protobuf.Timestamp
	24, // 38: validate.TimestampRules.within:type_name -> google.protobuf.Duration
	26, // 39: validate.disabled:extendee -> google.protobuf.MessageOptions
	26, // 40: validate.ignored:extendee -> google.protobuf.MessageOptions
	27, // 41: validate.required:extendee -> google.protobuf.OneofOptions
	28, // 42: validate.rules:extendee -> google.protobuf.FieldOptions
	1,  // 43: validate.rules:type_name -> validate.FieldRules
	44, // [44:44] is the sub-list for method output_type
	44, // [44:44] is the sub-list for method input_type
	43, // [43:44] is the sub-list for extension type_name
	39, // [39:43] is the sub-list for extension extendee
	0,  // [0:39] is the sub-list for field type_name
}

func init() { file_validate_validate_proto_init() }
func file_validate_validate_proto_init() {
	if File_validate_validate_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_validate_validate_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FieldRules); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_validate_validate_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FloatRules); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_validate_validate_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DoubleRules); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_validate_validate_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Int32Rules); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_validate_validate_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Int64Rules); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_validate_validate_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UInt32Rules); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_validate_validate_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UInt64Rules); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_validate_validate_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SInt32Rules); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_validate_validate_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SInt64Rules); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_validate_validate_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Fixed32Rules); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_validate_validate_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Fixed64Rules); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_validate_validate_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SFixed32Rules); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_validate_validate_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SFixed64Rules); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_validate_validate_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BoolRules); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_validate_validate_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StringRules); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_validate_validate_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BytesRules); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_validate_validate_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EnumRules); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_validate_validate_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MessageRules); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_validate_validate_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RepeatedRules); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_validate_validate_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MapRules); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_validate_validate_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AnyRules); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_validate_validate_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DurationRules); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_validate_validate_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TimestampRules); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_validate_validate_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*FieldRules_Float)(nil),
		(*FieldRules_Double)(nil),
		(*FieldRules_Int32)(nil),
		(*FieldRules_Int64)(nil),
		(*FieldRules_Uint32)(nil),
		(*FieldRules_Uint64)(nil),
		(*FieldRules_Sint32)(nil),
		(*FieldRules_Sint64)(nil),
		(*FieldRules_Fixed32)(nil),
		(*FieldRules_Fixed64)(nil),
		(*FieldRules_Sfixed32)(nil),
		(*FieldRules_Sfixed64)(nil),
		(*FieldRules_Bool)(nil),
		(*FieldRules_String_)(nil),
		(*FieldRules_Bytes)(nil),
		(*FieldRules_Enum)(nil),
		(*FieldRules_Repeated)(nil),
		(*FieldRules_Map)(nil),
		(*FieldRules_Any)(nil),
		(*FieldRules_Duration)(nil),
		(*FieldRules_Timestamp)(nil),
	}
	file_validate_validate_proto_msgTypes[14].OneofWrappers = []interface{}{
		(*StringRules_Email)(nil),
		(*StringRules_Hostname)(nil),
		(*StringRules_Ip)(nil),
		(*StringRules_Ipv4)(nil),
		(*StringRules_Ipv6)(nil),
		(*StringRules_Uri)(nil),
		(*StringRules_UriRef)(nil),
		(*StringRules_Address)(nil),
		(*StringRules_Uuid)(nil),
		(*StringRules_WellKnownRegex)(nil),
	}
	file_validate_validate_proto_msgTypes[15].OneofWrappers = []interface{}{
		(*BytesRules_Ip)(nil),
		(*BytesRules_Ipv4)(nil),
		(*BytesRules_Ipv6)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_validate_validate_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   23,
			NumExtensions: 4,
			NumServices:   0,
		},
		GoTypes:           file_validate_validate_proto_goTypes,
		DependencyIndexes: file_validate_validate_proto_depIdxs,
		EnumInfos:         file_validate_validate_proto_enumTypes,
		MessageInfos:      file_validate_validate_proto_msgTypes,
		ExtensionInfos:    file_validate_validate_proto_extTypes,
	}.Build()
	File_validate_validate_proto = out.File
	file_validate_validate_proto_rawDesc = nil
	file_validate_validate_proto_goTypes = nil
	file_validate_validate_proto_depIdxs = nil
}
