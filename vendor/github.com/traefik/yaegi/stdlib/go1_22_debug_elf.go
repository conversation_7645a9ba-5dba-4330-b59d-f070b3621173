// Code generated by 'yaegi extract debug/elf'. DO NOT EDIT.

//go:build go1.22
// +build go1.22

package stdlib

import (
	"debug/elf"
	"go/constant"
	"go/token"
	"reflect"
)

func init() {
	Symbols["debug/elf/elf"] = map[string]reflect.Value{
		// function, constant and variable definitions
		"ARM_MAGIC_TRAMP_NUMBER":             reflect.ValueOf(constant.MakeFromLiteral("1543503875", token.INT, 0)),
		"COMPRESS_HIOS":                      reflect.ValueOf(elf.COMPRESS_HIOS),
		"COMPRESS_HIPROC":                    reflect.ValueOf(elf.COMPRESS_HIPROC),
		"COMPRESS_LOOS":                      reflect.ValueOf(elf.COMPRESS_LOOS),
		"COMPRESS_LOPROC":                    reflect.ValueOf(elf.COMPRESS_LOPROC),
		"COMPRESS_ZLIB":                      reflect.ValueOf(elf.COMPRESS_ZLIB),
		"COMPRESS_ZSTD":                      reflect.ValueOf(elf.COMPRESS_ZSTD),
		"DF_1_CONFALT":                       reflect.ValueOf(elf.DF_1_CONFALT),
		"DF_1_DIRECT":                        reflect.ValueOf(elf.DF_1_DIRECT),
		"DF_1_DISPRELDNE":                    reflect.ValueOf(elf.DF_1_DISPRELDNE),
		"DF_1_DISPRELPND":                    reflect.ValueOf(elf.DF_1_DISPRELPND),
		"DF_1_EDITED":                        reflect.ValueOf(elf.DF_1_EDITED),
		"DF_1_ENDFILTEE":                     reflect.ValueOf(elf.DF_1_ENDFILTEE),
		"DF_1_GLOBAL":                        reflect.ValueOf(elf.DF_1_GLOBAL),
		"DF_1_GLOBAUDIT":                     reflect.ValueOf(elf.DF_1_GLOBAUDIT),
		"DF_1_GROUP":                         reflect.ValueOf(elf.DF_1_GROUP),
		"DF_1_IGNMULDEF":                     reflect.ValueOf(elf.DF_1_IGNMULDEF),
		"DF_1_INITFIRST":                     reflect.ValueOf(elf.DF_1_INITFIRST),
		"DF_1_INTERPOSE":                     reflect.ValueOf(elf.DF_1_INTERPOSE),
		"DF_1_KMOD":                          reflect.ValueOf(elf.DF_1_KMOD),
		"DF_1_LOADFLTR":                      reflect.ValueOf(elf.DF_1_LOADFLTR),
		"DF_1_NOCOMMON":                      reflect.ValueOf(elf.DF_1_NOCOMMON),
		"DF_1_NODEFLIB":                      reflect.ValueOf(elf.DF_1_NODEFLIB),
		"DF_1_NODELETE":                      reflect.ValueOf(elf.DF_1_NODELETE),
		"DF_1_NODIRECT":                      reflect.ValueOf(elf.DF_1_NODIRECT),
		"DF_1_NODUMP":                        reflect.ValueOf(elf.DF_1_NODUMP),
		"DF_1_NOHDR":                         reflect.ValueOf(elf.DF_1_NOHDR),
		"DF_1_NOKSYMS":                       reflect.ValueOf(elf.DF_1_NOKSYMS),
		"DF_1_NOOPEN":                        reflect.ValueOf(elf.DF_1_NOOPEN),
		"DF_1_NORELOC":                       reflect.ValueOf(elf.DF_1_NORELOC),
		"DF_1_NOW":                           reflect.ValueOf(elf.DF_1_NOW),
		"DF_1_ORIGIN":                        reflect.ValueOf(elf.DF_1_ORIGIN),
		"DF_1_PIE":                           reflect.ValueOf(elf.DF_1_PIE),
		"DF_1_SINGLETON":                     reflect.ValueOf(elf.DF_1_SINGLETON),
		"DF_1_STUB":                          reflect.ValueOf(elf.DF_1_STUB),
		"DF_1_SYMINTPOSE":                    reflect.ValueOf(elf.DF_1_SYMINTPOSE),
		"DF_1_TRANS":                         reflect.ValueOf(elf.DF_1_TRANS),
		"DF_1_WEAKFILTER":                    reflect.ValueOf(elf.DF_1_WEAKFILTER),
		"DF_BIND_NOW":                        reflect.ValueOf(elf.DF_BIND_NOW),
		"DF_ORIGIN":                          reflect.ValueOf(elf.DF_ORIGIN),
		"DF_STATIC_TLS":                      reflect.ValueOf(elf.DF_STATIC_TLS),
		"DF_SYMBOLIC":                        reflect.ValueOf(elf.DF_SYMBOLIC),
		"DF_TEXTREL":                         reflect.ValueOf(elf.DF_TEXTREL),
		"DT_ADDRRNGHI":                       reflect.ValueOf(elf.DT_ADDRRNGHI),
		"DT_ADDRRNGLO":                       reflect.ValueOf(elf.DT_ADDRRNGLO),
		"DT_AUDIT":                           reflect.ValueOf(elf.DT_AUDIT),
		"DT_AUXILIARY":                       reflect.ValueOf(elf.DT_AUXILIARY),
		"DT_BIND_NOW":                        reflect.ValueOf(elf.DT_BIND_NOW),
		"DT_CHECKSUM":                        reflect.ValueOf(elf.DT_CHECKSUM),
		"DT_CONFIG":                          reflect.ValueOf(elf.DT_CONFIG),
		"DT_DEBUG":                           reflect.ValueOf(elf.DT_DEBUG),
		"DT_DEPAUDIT":                        reflect.ValueOf(elf.DT_DEPAUDIT),
		"DT_ENCODING":                        reflect.ValueOf(elf.DT_ENCODING),
		"DT_FEATURE":                         reflect.ValueOf(elf.DT_FEATURE),
		"DT_FILTER":                          reflect.ValueOf(elf.DT_FILTER),
		"DT_FINI":                            reflect.ValueOf(elf.DT_FINI),
		"DT_FINI_ARRAY":                      reflect.ValueOf(elf.DT_FINI_ARRAY),
		"DT_FINI_ARRAYSZ":                    reflect.ValueOf(elf.DT_FINI_ARRAYSZ),
		"DT_FLAGS":                           reflect.ValueOf(elf.DT_FLAGS),
		"DT_FLAGS_1":                         reflect.ValueOf(elf.DT_FLAGS_1),
		"DT_GNU_CONFLICT":                    reflect.ValueOf(elf.DT_GNU_CONFLICT),
		"DT_GNU_CONFLICTSZ":                  reflect.ValueOf(elf.DT_GNU_CONFLICTSZ),
		"DT_GNU_HASH":                        reflect.ValueOf(elf.DT_GNU_HASH),
		"DT_GNU_LIBLIST":                     reflect.ValueOf(elf.DT_GNU_LIBLIST),
		"DT_GNU_LIBLISTSZ":                   reflect.ValueOf(elf.DT_GNU_LIBLISTSZ),
		"DT_GNU_PRELINKED":                   reflect.ValueOf(elf.DT_GNU_PRELINKED),
		"DT_HASH":                            reflect.ValueOf(elf.DT_HASH),
		"DT_HIOS":                            reflect.ValueOf(elf.DT_HIOS),
		"DT_HIPROC":                          reflect.ValueOf(elf.DT_HIPROC),
		"DT_INIT":                            reflect.ValueOf(elf.DT_INIT),
		"DT_INIT_ARRAY":                      reflect.ValueOf(elf.DT_INIT_ARRAY),
		"DT_INIT_ARRAYSZ":                    reflect.ValueOf(elf.DT_INIT_ARRAYSZ),
		"DT_JMPREL":                          reflect.ValueOf(elf.DT_JMPREL),
		"DT_LOOS":                            reflect.ValueOf(elf.DT_LOOS),
		"DT_LOPROC":                          reflect.ValueOf(elf.DT_LOPROC),
		"DT_MIPS_AUX_DYNAMIC":                reflect.ValueOf(elf.DT_MIPS_AUX_DYNAMIC),
		"DT_MIPS_BASE_ADDRESS":               reflect.ValueOf(elf.DT_MIPS_BASE_ADDRESS),
		"DT_MIPS_COMPACT_SIZE":               reflect.ValueOf(elf.DT_MIPS_COMPACT_SIZE),
		"DT_MIPS_CONFLICT":                   reflect.ValueOf(elf.DT_MIPS_CONFLICT),
		"DT_MIPS_CONFLICTNO":                 reflect.ValueOf(elf.DT_MIPS_CONFLICTNO),
		"DT_MIPS_CXX_FLAGS":                  reflect.ValueOf(elf.DT_MIPS_CXX_FLAGS),
		"DT_MIPS_DELTA_CLASS":                reflect.ValueOf(elf.DT_MIPS_DELTA_CLASS),
		"DT_MIPS_DELTA_CLASSSYM":             reflect.ValueOf(elf.DT_MIPS_DELTA_CLASSSYM),
		"DT_MIPS_DELTA_CLASSSYM_NO":          reflect.ValueOf(elf.DT_MIPS_DELTA_CLASSSYM_NO),
		"DT_MIPS_DELTA_CLASS_NO":             reflect.ValueOf(elf.DT_MIPS_DELTA_CLASS_NO),
		"DT_MIPS_DELTA_INSTANCE":             reflect.ValueOf(elf.DT_MIPS_DELTA_INSTANCE),
		"DT_MIPS_DELTA_INSTANCE_NO":          reflect.ValueOf(elf.DT_MIPS_DELTA_INSTANCE_NO),
		"DT_MIPS_DELTA_RELOC":                reflect.ValueOf(elf.DT_MIPS_DELTA_RELOC),
		"DT_MIPS_DELTA_RELOC_NO":             reflect.ValueOf(elf.DT_MIPS_DELTA_RELOC_NO),
		"DT_MIPS_DELTA_SYM":                  reflect.ValueOf(elf.DT_MIPS_DELTA_SYM),
		"DT_MIPS_DELTA_SYM_NO":               reflect.ValueOf(elf.DT_MIPS_DELTA_SYM_NO),
		"DT_MIPS_DYNSTR_ALIGN":               reflect.ValueOf(elf.DT_MIPS_DYNSTR_ALIGN),
		"DT_MIPS_FLAGS":                      reflect.ValueOf(elf.DT_MIPS_FLAGS),
		"DT_MIPS_GOTSYM":                     reflect.ValueOf(elf.DT_MIPS_GOTSYM),
		"DT_MIPS_GP_VALUE":                   reflect.ValueOf(elf.DT_MIPS_GP_VALUE),
		"DT_MIPS_HIDDEN_GOTIDX":              reflect.ValueOf(elf.DT_MIPS_HIDDEN_GOTIDX),
		"DT_MIPS_HIPAGENO":                   reflect.ValueOf(elf.DT_MIPS_HIPAGENO),
		"DT_MIPS_ICHECKSUM":                  reflect.ValueOf(elf.DT_MIPS_ICHECKSUM),
		"DT_MIPS_INTERFACE":                  reflect.ValueOf(elf.DT_MIPS_INTERFACE),
		"DT_MIPS_INTERFACE_SIZE":             reflect.ValueOf(elf.DT_MIPS_INTERFACE_SIZE),
		"DT_MIPS_IVERSION":                   reflect.ValueOf(elf.DT_MIPS_IVERSION),
		"DT_MIPS_LIBLIST":                    reflect.ValueOf(elf.DT_MIPS_LIBLIST),
		"DT_MIPS_LIBLISTNO":                  reflect.ValueOf(elf.DT_MIPS_LIBLISTNO),
		"DT_MIPS_LOCALPAGE_GOTIDX":           reflect.ValueOf(elf.DT_MIPS_LOCALPAGE_GOTIDX),
		"DT_MIPS_LOCAL_GOTIDX":               reflect.ValueOf(elf.DT_MIPS_LOCAL_GOTIDX),
		"DT_MIPS_LOCAL_GOTNO":                reflect.ValueOf(elf.DT_MIPS_LOCAL_GOTNO),
		"DT_MIPS_MSYM":                       reflect.ValueOf(elf.DT_MIPS_MSYM),
		"DT_MIPS_OPTIONS":                    reflect.ValueOf(elf.DT_MIPS_OPTIONS),
		"DT_MIPS_PERF_SUFFIX":                reflect.ValueOf(elf.DT_MIPS_PERF_SUFFIX),
		"DT_MIPS_PIXIE_INIT":                 reflect.ValueOf(elf.DT_MIPS_PIXIE_INIT),
		"DT_MIPS_PLTGOT":                     reflect.ValueOf(elf.DT_MIPS_PLTGOT),
		"DT_MIPS_PROTECTED_GOTIDX":           reflect.ValueOf(elf.DT_MIPS_PROTECTED_GOTIDX),
		"DT_MIPS_RLD_MAP":                    reflect.ValueOf(elf.DT_MIPS_RLD_MAP),
		"DT_MIPS_RLD_MAP_REL":                reflect.ValueOf(elf.DT_MIPS_RLD_MAP_REL),
		"DT_MIPS_RLD_TEXT_RESOLVE_ADDR":      reflect.ValueOf(elf.DT_MIPS_RLD_TEXT_RESOLVE_ADDR),
		"DT_MIPS_RLD_VERSION":                reflect.ValueOf(elf.DT_MIPS_RLD_VERSION),
		"DT_MIPS_RWPLT":                      reflect.ValueOf(elf.DT_MIPS_RWPLT),
		"DT_MIPS_SYMBOL_LIB":                 reflect.ValueOf(elf.DT_MIPS_SYMBOL_LIB),
		"DT_MIPS_SYMTABNO":                   reflect.ValueOf(elf.DT_MIPS_SYMTABNO),
		"DT_MIPS_TIME_STAMP":                 reflect.ValueOf(elf.DT_MIPS_TIME_STAMP),
		"DT_MIPS_UNREFEXTNO":                 reflect.ValueOf(elf.DT_MIPS_UNREFEXTNO),
		"DT_MOVEENT":                         reflect.ValueOf(elf.DT_MOVEENT),
		"DT_MOVESZ":                          reflect.ValueOf(elf.DT_MOVESZ),
		"DT_MOVETAB":                         reflect.ValueOf(elf.DT_MOVETAB),
		"DT_NEEDED":                          reflect.ValueOf(elf.DT_NEEDED),
		"DT_NULL":                            reflect.ValueOf(elf.DT_NULL),
		"DT_PLTGOT":                          reflect.ValueOf(elf.DT_PLTGOT),
		"DT_PLTPAD":                          reflect.ValueOf(elf.DT_PLTPAD),
		"DT_PLTPADSZ":                        reflect.ValueOf(elf.DT_PLTPADSZ),
		"DT_PLTREL":                          reflect.ValueOf(elf.DT_PLTREL),
		"DT_PLTRELSZ":                        reflect.ValueOf(elf.DT_PLTRELSZ),
		"DT_POSFLAG_1":                       reflect.ValueOf(elf.DT_POSFLAG_1),
		"DT_PPC64_GLINK":                     reflect.ValueOf(elf.DT_PPC64_GLINK),
		"DT_PPC64_OPD":                       reflect.ValueOf(elf.DT_PPC64_OPD),
		"DT_PPC64_OPDSZ":                     reflect.ValueOf(elf.DT_PPC64_OPDSZ),
		"DT_PPC64_OPT":                       reflect.ValueOf(elf.DT_PPC64_OPT),
		"DT_PPC_GOT":                         reflect.ValueOf(elf.DT_PPC_GOT),
		"DT_PPC_OPT":                         reflect.ValueOf(elf.DT_PPC_OPT),
		"DT_PREINIT_ARRAY":                   reflect.ValueOf(elf.DT_PREINIT_ARRAY),
		"DT_PREINIT_ARRAYSZ":                 reflect.ValueOf(elf.DT_PREINIT_ARRAYSZ),
		"DT_REL":                             reflect.ValueOf(elf.DT_REL),
		"DT_RELA":                            reflect.ValueOf(elf.DT_RELA),
		"DT_RELACOUNT":                       reflect.ValueOf(elf.DT_RELACOUNT),
		"DT_RELAENT":                         reflect.ValueOf(elf.DT_RELAENT),
		"DT_RELASZ":                          reflect.ValueOf(elf.DT_RELASZ),
		"DT_RELCOUNT":                        reflect.ValueOf(elf.DT_RELCOUNT),
		"DT_RELENT":                          reflect.ValueOf(elf.DT_RELENT),
		"DT_RELSZ":                           reflect.ValueOf(elf.DT_RELSZ),
		"DT_RPATH":                           reflect.ValueOf(elf.DT_RPATH),
		"DT_RUNPATH":                         reflect.ValueOf(elf.DT_RUNPATH),
		"DT_SONAME":                          reflect.ValueOf(elf.DT_SONAME),
		"DT_SPARC_REGISTER":                  reflect.ValueOf(elf.DT_SPARC_REGISTER),
		"DT_STRSZ":                           reflect.ValueOf(elf.DT_STRSZ),
		"DT_STRTAB":                          reflect.ValueOf(elf.DT_STRTAB),
		"DT_SYMBOLIC":                        reflect.ValueOf(elf.DT_SYMBOLIC),
		"DT_SYMENT":                          reflect.ValueOf(elf.DT_SYMENT),
		"DT_SYMINENT":                        reflect.ValueOf(elf.DT_SYMINENT),
		"DT_SYMINFO":                         reflect.ValueOf(elf.DT_SYMINFO),
		"DT_SYMINSZ":                         reflect.ValueOf(elf.DT_SYMINSZ),
		"DT_SYMTAB":                          reflect.ValueOf(elf.DT_SYMTAB),
		"DT_SYMTAB_SHNDX":                    reflect.ValueOf(elf.DT_SYMTAB_SHNDX),
		"DT_TEXTREL":                         reflect.ValueOf(elf.DT_TEXTREL),
		"DT_TLSDESC_GOT":                     reflect.ValueOf(elf.DT_TLSDESC_GOT),
		"DT_TLSDESC_PLT":                     reflect.ValueOf(elf.DT_TLSDESC_PLT),
		"DT_USED":                            reflect.ValueOf(elf.DT_USED),
		"DT_VALRNGHI":                        reflect.ValueOf(elf.DT_VALRNGHI),
		"DT_VALRNGLO":                        reflect.ValueOf(elf.DT_VALRNGLO),
		"DT_VERDEF":                          reflect.ValueOf(elf.DT_VERDEF),
		"DT_VERDEFNUM":                       reflect.ValueOf(elf.DT_VERDEFNUM),
		"DT_VERNEED":                         reflect.ValueOf(elf.DT_VERNEED),
		"DT_VERNEEDNUM":                      reflect.ValueOf(elf.DT_VERNEEDNUM),
		"DT_VERSYM":                          reflect.ValueOf(elf.DT_VERSYM),
		"EI_ABIVERSION":                      reflect.ValueOf(constant.MakeFromLiteral("8", token.INT, 0)),
		"EI_CLASS":                           reflect.ValueOf(constant.MakeFromLiteral("4", token.INT, 0)),
		"EI_DATA":                            reflect.ValueOf(constant.MakeFromLiteral("5", token.INT, 0)),
		"EI_NIDENT":                          reflect.ValueOf(constant.MakeFromLiteral("16", token.INT, 0)),
		"EI_OSABI":                           reflect.ValueOf(constant.MakeFromLiteral("7", token.INT, 0)),
		"EI_PAD":                             reflect.ValueOf(constant.MakeFromLiteral("9", token.INT, 0)),
		"EI_VERSION":                         reflect.ValueOf(constant.MakeFromLiteral("6", token.INT, 0)),
		"ELFCLASS32":                         reflect.ValueOf(elf.ELFCLASS32),
		"ELFCLASS64":                         reflect.ValueOf(elf.ELFCLASS64),
		"ELFCLASSNONE":                       reflect.ValueOf(elf.ELFCLASSNONE),
		"ELFDATA2LSB":                        reflect.ValueOf(elf.ELFDATA2LSB),
		"ELFDATA2MSB":                        reflect.ValueOf(elf.ELFDATA2MSB),
		"ELFDATANONE":                        reflect.ValueOf(elf.ELFDATANONE),
		"ELFMAG":                             reflect.ValueOf(constant.MakeFromLiteral("\"\\x7fELF\"", token.STRING, 0)),
		"ELFOSABI_86OPEN":                    reflect.ValueOf(elf.ELFOSABI_86OPEN),
		"ELFOSABI_AIX":                       reflect.ValueOf(elf.ELFOSABI_AIX),
		"ELFOSABI_ARM":                       reflect.ValueOf(elf.ELFOSABI_ARM),
		"ELFOSABI_AROS":                      reflect.ValueOf(elf.ELFOSABI_AROS),
		"ELFOSABI_CLOUDABI":                  reflect.ValueOf(elf.ELFOSABI_CLOUDABI),
		"ELFOSABI_FENIXOS":                   reflect.ValueOf(elf.ELFOSABI_FENIXOS),
		"ELFOSABI_FREEBSD":                   reflect.ValueOf(elf.ELFOSABI_FREEBSD),
		"ELFOSABI_HPUX":                      reflect.ValueOf(elf.ELFOSABI_HPUX),
		"ELFOSABI_HURD":                      reflect.ValueOf(elf.ELFOSABI_HURD),
		"ELFOSABI_IRIX":                      reflect.ValueOf(elf.ELFOSABI_IRIX),
		"ELFOSABI_LINUX":                     reflect.ValueOf(elf.ELFOSABI_LINUX),
		"ELFOSABI_MODESTO":                   reflect.ValueOf(elf.ELFOSABI_MODESTO),
		"ELFOSABI_NETBSD":                    reflect.ValueOf(elf.ELFOSABI_NETBSD),
		"ELFOSABI_NONE":                      reflect.ValueOf(elf.ELFOSABI_NONE),
		"ELFOSABI_NSK":                       reflect.ValueOf(elf.ELFOSABI_NSK),
		"ELFOSABI_OPENBSD":                   reflect.ValueOf(elf.ELFOSABI_OPENBSD),
		"ELFOSABI_OPENVMS":                   reflect.ValueOf(elf.ELFOSABI_OPENVMS),
		"ELFOSABI_SOLARIS":                   reflect.ValueOf(elf.ELFOSABI_SOLARIS),
		"ELFOSABI_STANDALONE":                reflect.ValueOf(elf.ELFOSABI_STANDALONE),
		"ELFOSABI_TRU64":                     reflect.ValueOf(elf.ELFOSABI_TRU64),
		"EM_386":                             reflect.ValueOf(elf.EM_386),
		"EM_486":                             reflect.ValueOf(elf.EM_486),
		"EM_56800EX":                         reflect.ValueOf(elf.EM_56800EX),
		"EM_68HC05":                          reflect.ValueOf(elf.EM_68HC05),
		"EM_68HC08":                          reflect.ValueOf(elf.EM_68HC08),
		"EM_68HC11":                          reflect.ValueOf(elf.EM_68HC11),
		"EM_68HC12":                          reflect.ValueOf(elf.EM_68HC12),
		"EM_68HC16":                          reflect.ValueOf(elf.EM_68HC16),
		"EM_68K":                             reflect.ValueOf(elf.EM_68K),
		"EM_78KOR":                           reflect.ValueOf(elf.EM_78KOR),
		"EM_8051":                            reflect.ValueOf(elf.EM_8051),
		"EM_860":                             reflect.ValueOf(elf.EM_860),
		"EM_88K":                             reflect.ValueOf(elf.EM_88K),
		"EM_960":                             reflect.ValueOf(elf.EM_960),
		"EM_AARCH64":                         reflect.ValueOf(elf.EM_AARCH64),
		"EM_ALPHA":                           reflect.ValueOf(elf.EM_ALPHA),
		"EM_ALPHA_STD":                       reflect.ValueOf(elf.EM_ALPHA_STD),
		"EM_ALTERA_NIOS2":                    reflect.ValueOf(elf.EM_ALTERA_NIOS2),
		"EM_AMDGPU":                          reflect.ValueOf(elf.EM_AMDGPU),
		"EM_ARC":                             reflect.ValueOf(elf.EM_ARC),
		"EM_ARCA":                            reflect.ValueOf(elf.EM_ARCA),
		"EM_ARC_COMPACT":                     reflect.ValueOf(elf.EM_ARC_COMPACT),
		"EM_ARC_COMPACT2":                    reflect.ValueOf(elf.EM_ARC_COMPACT2),
		"EM_ARM":                             reflect.ValueOf(elf.EM_ARM),
		"EM_AVR":                             reflect.ValueOf(elf.EM_AVR),
		"EM_AVR32":                           reflect.ValueOf(elf.EM_AVR32),
		"EM_BA1":                             reflect.ValueOf(elf.EM_BA1),
		"EM_BA2":                             reflect.ValueOf(elf.EM_BA2),
		"EM_BLACKFIN":                        reflect.ValueOf(elf.EM_BLACKFIN),
		"EM_BPF":                             reflect.ValueOf(elf.EM_BPF),
		"EM_C166":                            reflect.ValueOf(elf.EM_C166),
		"EM_CDP":                             reflect.ValueOf(elf.EM_CDP),
		"EM_CE":                              reflect.ValueOf(elf.EM_CE),
		"EM_CLOUDSHIELD":                     reflect.ValueOf(elf.EM_CLOUDSHIELD),
		"EM_COGE":                            reflect.ValueOf(elf.EM_COGE),
		"EM_COLDFIRE":                        reflect.ValueOf(elf.EM_COLDFIRE),
		"EM_COOL":                            reflect.ValueOf(elf.EM_COOL),
		"EM_COREA_1ST":                       reflect.ValueOf(elf.EM_COREA_1ST),
		"EM_COREA_2ND":                       reflect.ValueOf(elf.EM_COREA_2ND),
		"EM_CR":                              reflect.ValueOf(elf.EM_CR),
		"EM_CR16":                            reflect.ValueOf(elf.EM_CR16),
		"EM_CRAYNV2":                         reflect.ValueOf(elf.EM_CRAYNV2),
		"EM_CRIS":                            reflect.ValueOf(elf.EM_CRIS),
		"EM_CRX":                             reflect.ValueOf(elf.EM_CRX),
		"EM_CSR_KALIMBA":                     reflect.ValueOf(elf.EM_CSR_KALIMBA),
		"EM_CUDA":                            reflect.ValueOf(elf.EM_CUDA),
		"EM_CYPRESS_M8C":                     reflect.ValueOf(elf.EM_CYPRESS_M8C),
		"EM_D10V":                            reflect.ValueOf(elf.EM_D10V),
		"EM_D30V":                            reflect.ValueOf(elf.EM_D30V),
		"EM_DSP24":                           reflect.ValueOf(elf.EM_DSP24),
		"EM_DSPIC30F":                        reflect.ValueOf(elf.EM_DSPIC30F),
		"EM_DXP":                             reflect.ValueOf(elf.EM_DXP),
		"EM_ECOG1":                           reflect.ValueOf(elf.EM_ECOG1),
		"EM_ECOG16":                          reflect.ValueOf(elf.EM_ECOG16),
		"EM_ECOG1X":                          reflect.ValueOf(elf.EM_ECOG1X),
		"EM_ECOG2":                           reflect.ValueOf(elf.EM_ECOG2),
		"EM_ETPU":                            reflect.ValueOf(elf.EM_ETPU),
		"EM_EXCESS":                          reflect.ValueOf(elf.EM_EXCESS),
		"EM_F2MC16":                          reflect.ValueOf(elf.EM_F2MC16),
		"EM_FIREPATH":                        reflect.ValueOf(elf.EM_FIREPATH),
		"EM_FR20":                            reflect.ValueOf(elf.EM_FR20),
		"EM_FR30":                            reflect.ValueOf(elf.EM_FR30),
		"EM_FT32":                            reflect.ValueOf(elf.EM_FT32),
		"EM_FX66":                            reflect.ValueOf(elf.EM_FX66),
		"EM_H8S":                             reflect.ValueOf(elf.EM_H8S),
		"EM_H8_300":                          reflect.ValueOf(elf.EM_H8_300),
		"EM_H8_300H":                         reflect.ValueOf(elf.EM_H8_300H),
		"EM_H8_500":                          reflect.ValueOf(elf.EM_H8_500),
		"EM_HUANY":                           reflect.ValueOf(elf.EM_HUANY),
		"EM_IA_64":                           reflect.ValueOf(elf.EM_IA_64),
		"EM_INTEL205":                        reflect.ValueOf(elf.EM_INTEL205),
		"EM_INTEL206":                        reflect.ValueOf(elf.EM_INTEL206),
		"EM_INTEL207":                        reflect.ValueOf(elf.EM_INTEL207),
		"EM_INTEL208":                        reflect.ValueOf(elf.EM_INTEL208),
		"EM_INTEL209":                        reflect.ValueOf(elf.EM_INTEL209),
		"EM_IP2K":                            reflect.ValueOf(elf.EM_IP2K),
		"EM_JAVELIN":                         reflect.ValueOf(elf.EM_JAVELIN),
		"EM_K10M":                            reflect.ValueOf(elf.EM_K10M),
		"EM_KM32":                            reflect.ValueOf(elf.EM_KM32),
		"EM_KMX16":                           reflect.ValueOf(elf.EM_KMX16),
		"EM_KMX32":                           reflect.ValueOf(elf.EM_KMX32),
		"EM_KMX8":                            reflect.ValueOf(elf.EM_KMX8),
		"EM_KVARC":                           reflect.ValueOf(elf.EM_KVARC),
		"EM_L10M":                            reflect.ValueOf(elf.EM_L10M),
		"EM_LANAI":                           reflect.ValueOf(elf.EM_LANAI),
		"EM_LATTICEMICO32":                   reflect.ValueOf(elf.EM_LATTICEMICO32),
		"EM_LOONGARCH":                       reflect.ValueOf(elf.EM_LOONGARCH),
		"EM_M16C":                            reflect.ValueOf(elf.EM_M16C),
		"EM_M32":                             reflect.ValueOf(elf.EM_M32),
		"EM_M32C":                            reflect.ValueOf(elf.EM_M32C),
		"EM_M32R":                            reflect.ValueOf(elf.EM_M32R),
		"EM_MANIK":                           reflect.ValueOf(elf.EM_MANIK),
		"EM_MAX":                             reflect.ValueOf(elf.EM_MAX),
		"EM_MAXQ30":                          reflect.ValueOf(elf.EM_MAXQ30),
		"EM_MCHP_PIC":                        reflect.ValueOf(elf.EM_MCHP_PIC),
		"EM_MCST_ELBRUS":                     reflect.ValueOf(elf.EM_MCST_ELBRUS),
		"EM_ME16":                            reflect.ValueOf(elf.EM_ME16),
		"EM_METAG":                           reflect.ValueOf(elf.EM_METAG),
		"EM_MICROBLAZE":                      reflect.ValueOf(elf.EM_MICROBLAZE),
		"EM_MIPS":                            reflect.ValueOf(elf.EM_MIPS),
		"EM_MIPS_RS3_LE":                     reflect.ValueOf(elf.EM_MIPS_RS3_LE),
		"EM_MIPS_RS4_BE":                     reflect.ValueOf(elf.EM_MIPS_RS4_BE),
		"EM_MIPS_X":                          reflect.ValueOf(elf.EM_MIPS_X),
		"EM_MMA":                             reflect.ValueOf(elf.EM_MMA),
		"EM_MMDSP_PLUS":                      reflect.ValueOf(elf.EM_MMDSP_PLUS),
		"EM_MMIX":                            reflect.ValueOf(elf.EM_MMIX),
		"EM_MN10200":                         reflect.ValueOf(elf.EM_MN10200),
		"EM_MN10300":                         reflect.ValueOf(elf.EM_MN10300),
		"EM_MOXIE":                           reflect.ValueOf(elf.EM_MOXIE),
		"EM_MSP430":                          reflect.ValueOf(elf.EM_MSP430),
		"EM_NCPU":                            reflect.ValueOf(elf.EM_NCPU),
		"EM_NDR1":                            reflect.ValueOf(elf.EM_NDR1),
		"EM_NDS32":                           reflect.ValueOf(elf.EM_NDS32),
		"EM_NONE":                            reflect.ValueOf(elf.EM_NONE),
		"EM_NORC":                            reflect.ValueOf(elf.EM_NORC),
		"EM_NS32K":                           reflect.ValueOf(elf.EM_NS32K),
		"EM_OPEN8":                           reflect.ValueOf(elf.EM_OPEN8),
		"EM_OPENRISC":                        reflect.ValueOf(elf.EM_OPENRISC),
		"EM_PARISC":                          reflect.ValueOf(elf.EM_PARISC),
		"EM_PCP":                             reflect.ValueOf(elf.EM_PCP),
		"EM_PDP10":                           reflect.ValueOf(elf.EM_PDP10),
		"EM_PDP11":                           reflect.ValueOf(elf.EM_PDP11),
		"EM_PDSP":                            reflect.ValueOf(elf.EM_PDSP),
		"EM_PJ":                              reflect.ValueOf(elf.EM_PJ),
		"EM_PPC":                             reflect.ValueOf(elf.EM_PPC),
		"EM_PPC64":                           reflect.ValueOf(elf.EM_PPC64),
		"EM_PRISM":                           reflect.ValueOf(elf.EM_PRISM),
		"EM_QDSP6":                           reflect.ValueOf(elf.EM_QDSP6),
		"EM_R32C":                            reflect.ValueOf(elf.EM_R32C),
		"EM_RCE":                             reflect.ValueOf(elf.EM_RCE),
		"EM_RH32":                            reflect.ValueOf(elf.EM_RH32),
		"EM_RISCV":                           reflect.ValueOf(elf.EM_RISCV),
		"EM_RL78":                            reflect.ValueOf(elf.EM_RL78),
		"EM_RS08":                            reflect.ValueOf(elf.EM_RS08),
		"EM_RX":                              reflect.ValueOf(elf.EM_RX),
		"EM_S370":                            reflect.ValueOf(elf.EM_S370),
		"EM_S390":                            reflect.ValueOf(elf.EM_S390),
		"EM_SCORE7":                          reflect.ValueOf(elf.EM_SCORE7),
		"EM_SEP":                             reflect.ValueOf(elf.EM_SEP),
		"EM_SE_C17":                          reflect.ValueOf(elf.EM_SE_C17),
		"EM_SE_C33":                          reflect.ValueOf(elf.EM_SE_C33),
		"EM_SH":                              reflect.ValueOf(elf.EM_SH),
		"EM_SHARC":                           reflect.ValueOf(elf.EM_SHARC),
		"EM_SLE9X":                           reflect.ValueOf(elf.EM_SLE9X),
		"EM_SNP1K":                           reflect.ValueOf(elf.EM_SNP1K),
		"EM_SPARC":                           reflect.ValueOf(elf.EM_SPARC),
		"EM_SPARC32PLUS":                     reflect.ValueOf(elf.EM_SPARC32PLUS),
		"EM_SPARCV9":                         reflect.ValueOf(elf.EM_SPARCV9),
		"EM_ST100":                           reflect.ValueOf(elf.EM_ST100),
		"EM_ST19":                            reflect.ValueOf(elf.EM_ST19),
		"EM_ST200":                           reflect.ValueOf(elf.EM_ST200),
		"EM_ST7":                             reflect.ValueOf(elf.EM_ST7),
		"EM_ST9PLUS":                         reflect.ValueOf(elf.EM_ST9PLUS),
		"EM_STARCORE":                        reflect.ValueOf(elf.EM_STARCORE),
		"EM_STM8":                            reflect.ValueOf(elf.EM_STM8),
		"EM_STXP7X":                          reflect.ValueOf(elf.EM_STXP7X),
		"EM_SVX":                             reflect.ValueOf(elf.EM_SVX),
		"EM_TILE64":                          reflect.ValueOf(elf.EM_TILE64),
		"EM_TILEGX":                          reflect.ValueOf(elf.EM_TILEGX),
		"EM_TILEPRO":                         reflect.ValueOf(elf.EM_TILEPRO),
		"EM_TINYJ":                           reflect.ValueOf(elf.EM_TINYJ),
		"EM_TI_ARP32":                        reflect.ValueOf(elf.EM_TI_ARP32),
		"EM_TI_C2000":                        reflect.ValueOf(elf.EM_TI_C2000),
		"EM_TI_C5500":                        reflect.ValueOf(elf.EM_TI_C5500),
		"EM_TI_C6000":                        reflect.ValueOf(elf.EM_TI_C6000),
		"EM_TI_PRU":                          reflect.ValueOf(elf.EM_TI_PRU),
		"EM_TMM_GPP":                         reflect.ValueOf(elf.EM_TMM_GPP),
		"EM_TPC":                             reflect.ValueOf(elf.EM_TPC),
		"EM_TRICORE":                         reflect.ValueOf(elf.EM_TRICORE),
		"EM_TRIMEDIA":                        reflect.ValueOf(elf.EM_TRIMEDIA),
		"EM_TSK3000":                         reflect.ValueOf(elf.EM_TSK3000),
		"EM_UNICORE":                         reflect.ValueOf(elf.EM_UNICORE),
		"EM_V800":                            reflect.ValueOf(elf.EM_V800),
		"EM_V850":                            reflect.ValueOf(elf.EM_V850),
		"EM_VAX":                             reflect.ValueOf(elf.EM_VAX),
		"EM_VIDEOCORE":                       reflect.ValueOf(elf.EM_VIDEOCORE),
		"EM_VIDEOCORE3":                      reflect.ValueOf(elf.EM_VIDEOCORE3),
		"EM_VIDEOCORE5":                      reflect.ValueOf(elf.EM_VIDEOCORE5),
		"EM_VISIUM":                          reflect.ValueOf(elf.EM_VISIUM),
		"EM_VPP500":                          reflect.ValueOf(elf.EM_VPP500),
		"EM_X86_64":                          reflect.ValueOf(elf.EM_X86_64),
		"EM_XCORE":                           reflect.ValueOf(elf.EM_XCORE),
		"EM_XGATE":                           reflect.ValueOf(elf.EM_XGATE),
		"EM_XIMO16":                          reflect.ValueOf(elf.EM_XIMO16),
		"EM_XTENSA":                          reflect.ValueOf(elf.EM_XTENSA),
		"EM_Z80":                             reflect.ValueOf(elf.EM_Z80),
		"EM_ZSP":                             reflect.ValueOf(elf.EM_ZSP),
		"ET_CORE":                            reflect.ValueOf(elf.ET_CORE),
		"ET_DYN":                             reflect.ValueOf(elf.ET_DYN),
		"ET_EXEC":                            reflect.ValueOf(elf.ET_EXEC),
		"ET_HIOS":                            reflect.ValueOf(elf.ET_HIOS),
		"ET_HIPROC":                          reflect.ValueOf(elf.ET_HIPROC),
		"ET_LOOS":                            reflect.ValueOf(elf.ET_LOOS),
		"ET_LOPROC":                          reflect.ValueOf(elf.ET_LOPROC),
		"ET_NONE":                            reflect.ValueOf(elf.ET_NONE),
		"ET_REL":                             reflect.ValueOf(elf.ET_REL),
		"EV_CURRENT":                         reflect.ValueOf(elf.EV_CURRENT),
		"EV_NONE":                            reflect.ValueOf(elf.EV_NONE),
		"ErrNoSymbols":                       reflect.ValueOf(&elf.ErrNoSymbols).Elem(),
		"NT_FPREGSET":                        reflect.ValueOf(elf.NT_FPREGSET),
		"NT_PRPSINFO":                        reflect.ValueOf(elf.NT_PRPSINFO),
		"NT_PRSTATUS":                        reflect.ValueOf(elf.NT_PRSTATUS),
		"NewFile":                            reflect.ValueOf(elf.NewFile),
		"Open":                               reflect.ValueOf(elf.Open),
		"PF_MASKOS":                          reflect.ValueOf(elf.PF_MASKOS),
		"PF_MASKPROC":                        reflect.ValueOf(elf.PF_MASKPROC),
		"PF_R":                               reflect.ValueOf(elf.PF_R),
		"PF_W":                               reflect.ValueOf(elf.PF_W),
		"PF_X":                               reflect.ValueOf(elf.PF_X),
		"PT_AARCH64_ARCHEXT":                 reflect.ValueOf(elf.PT_AARCH64_ARCHEXT),
		"PT_AARCH64_UNWIND":                  reflect.ValueOf(elf.PT_AARCH64_UNWIND),
		"PT_ARM_ARCHEXT":                     reflect.ValueOf(elf.PT_ARM_ARCHEXT),
		"PT_ARM_EXIDX":                       reflect.ValueOf(elf.PT_ARM_EXIDX),
		"PT_DYNAMIC":                         reflect.ValueOf(elf.PT_DYNAMIC),
		"PT_GNU_EH_FRAME":                    reflect.ValueOf(elf.PT_GNU_EH_FRAME),
		"PT_GNU_MBIND_HI":                    reflect.ValueOf(elf.PT_GNU_MBIND_HI),
		"PT_GNU_MBIND_LO":                    reflect.ValueOf(elf.PT_GNU_MBIND_LO),
		"PT_GNU_PROPERTY":                    reflect.ValueOf(elf.PT_GNU_PROPERTY),
		"PT_GNU_RELRO":                       reflect.ValueOf(elf.PT_GNU_RELRO),
		"PT_GNU_STACK":                       reflect.ValueOf(elf.PT_GNU_STACK),
		"PT_HIOS":                            reflect.ValueOf(elf.PT_HIOS),
		"PT_HIPROC":                          reflect.ValueOf(elf.PT_HIPROC),
		"PT_INTERP":                          reflect.ValueOf(elf.PT_INTERP),
		"PT_LOAD":                            reflect.ValueOf(elf.PT_LOAD),
		"PT_LOOS":                            reflect.ValueOf(elf.PT_LOOS),
		"PT_LOPROC":                          reflect.ValueOf(elf.PT_LOPROC),
		"PT_MIPS_ABIFLAGS":                   reflect.ValueOf(elf.PT_MIPS_ABIFLAGS),
		"PT_MIPS_OPTIONS":                    reflect.ValueOf(elf.PT_MIPS_OPTIONS),
		"PT_MIPS_REGINFO":                    reflect.ValueOf(elf.PT_MIPS_REGINFO),
		"PT_MIPS_RTPROC":                     reflect.ValueOf(elf.PT_MIPS_RTPROC),
		"PT_NOTE":                            reflect.ValueOf(elf.PT_NOTE),
		"PT_NULL":                            reflect.ValueOf(elf.PT_NULL),
		"PT_OPENBSD_BOOTDATA":                reflect.ValueOf(elf.PT_OPENBSD_BOOTDATA),
		"PT_OPENBSD_RANDOMIZE":               reflect.ValueOf(elf.PT_OPENBSD_RANDOMIZE),
		"PT_OPENBSD_WXNEEDED":                reflect.ValueOf(elf.PT_OPENBSD_WXNEEDED),
		"PT_PAX_FLAGS":                       reflect.ValueOf(elf.PT_PAX_FLAGS),
		"PT_PHDR":                            reflect.ValueOf(elf.PT_PHDR),
		"PT_S390_PGSTE":                      reflect.ValueOf(elf.PT_S390_PGSTE),
		"PT_SHLIB":                           reflect.ValueOf(elf.PT_SHLIB),
		"PT_SUNWSTACK":                       reflect.ValueOf(elf.PT_SUNWSTACK),
		"PT_SUNW_EH_FRAME":                   reflect.ValueOf(elf.PT_SUNW_EH_FRAME),
		"PT_TLS":                             reflect.ValueOf(elf.PT_TLS),
		"R_386_16":                           reflect.ValueOf(elf.R_386_16),
		"R_386_32":                           reflect.ValueOf(elf.R_386_32),
		"R_386_32PLT":                        reflect.ValueOf(elf.R_386_32PLT),
		"R_386_8":                            reflect.ValueOf(elf.R_386_8),
		"R_386_COPY":                         reflect.ValueOf(elf.R_386_COPY),
		"R_386_GLOB_DAT":                     reflect.ValueOf(elf.R_386_GLOB_DAT),
		"R_386_GOT32":                        reflect.ValueOf(elf.R_386_GOT32),
		"R_386_GOT32X":                       reflect.ValueOf(elf.R_386_GOT32X),
		"R_386_GOTOFF":                       reflect.ValueOf(elf.R_386_GOTOFF),
		"R_386_GOTPC":                        reflect.ValueOf(elf.R_386_GOTPC),
		"R_386_IRELATIVE":                    reflect.ValueOf(elf.R_386_IRELATIVE),
		"R_386_JMP_SLOT":                     reflect.ValueOf(elf.R_386_JMP_SLOT),
		"R_386_NONE":                         reflect.ValueOf(elf.R_386_NONE),
		"R_386_PC16":                         reflect.ValueOf(elf.R_386_PC16),
		"R_386_PC32":                         reflect.ValueOf(elf.R_386_PC32),
		"R_386_PC8":                          reflect.ValueOf(elf.R_386_PC8),
		"R_386_PLT32":                        reflect.ValueOf(elf.R_386_PLT32),
		"R_386_RELATIVE":                     reflect.ValueOf(elf.R_386_RELATIVE),
		"R_386_SIZE32":                       reflect.ValueOf(elf.R_386_SIZE32),
		"R_386_TLS_DESC":                     reflect.ValueOf(elf.R_386_TLS_DESC),
		"R_386_TLS_DESC_CALL":                reflect.ValueOf(elf.R_386_TLS_DESC_CALL),
		"R_386_TLS_DTPMOD32":                 reflect.ValueOf(elf.R_386_TLS_DTPMOD32),
		"R_386_TLS_DTPOFF32":                 reflect.ValueOf(elf.R_386_TLS_DTPOFF32),
		"R_386_TLS_GD":                       reflect.ValueOf(elf.R_386_TLS_GD),
		"R_386_TLS_GD_32":                    reflect.ValueOf(elf.R_386_TLS_GD_32),
		"R_386_TLS_GD_CALL":                  reflect.ValueOf(elf.R_386_TLS_GD_CALL),
		"R_386_TLS_GD_POP":                   reflect.ValueOf(elf.R_386_TLS_GD_POP),
		"R_386_TLS_GD_PUSH":                  reflect.ValueOf(elf.R_386_TLS_GD_PUSH),
		"R_386_TLS_GOTDESC":                  reflect.ValueOf(elf.R_386_TLS_GOTDESC),
		"R_386_TLS_GOTIE":                    reflect.ValueOf(elf.R_386_TLS_GOTIE),
		"R_386_TLS_IE":                       reflect.ValueOf(elf.R_386_TLS_IE),
		"R_386_TLS_IE_32":                    reflect.ValueOf(elf.R_386_TLS_IE_32),
		"R_386_TLS_LDM":                      reflect.ValueOf(elf.R_386_TLS_LDM),
		"R_386_TLS_LDM_32":                   reflect.ValueOf(elf.R_386_TLS_LDM_32),
		"R_386_TLS_LDM_CALL":                 reflect.ValueOf(elf.R_386_TLS_LDM_CALL),
		"R_386_TLS_LDM_POP":                  reflect.ValueOf(elf.R_386_TLS_LDM_POP),
		"R_386_TLS_LDM_PUSH":                 reflect.ValueOf(elf.R_386_TLS_LDM_PUSH),
		"R_386_TLS_LDO_32":                   reflect.ValueOf(elf.R_386_TLS_LDO_32),
		"R_386_TLS_LE":                       reflect.ValueOf(elf.R_386_TLS_LE),
		"R_386_TLS_LE_32":                    reflect.ValueOf(elf.R_386_TLS_LE_32),
		"R_386_TLS_TPOFF":                    reflect.ValueOf(elf.R_386_TLS_TPOFF),
		"R_386_TLS_TPOFF32":                  reflect.ValueOf(elf.R_386_TLS_TPOFF32),
		"R_390_12":                           reflect.ValueOf(elf.R_390_12),
		"R_390_16":                           reflect.ValueOf(elf.R_390_16),
		"R_390_20":                           reflect.ValueOf(elf.R_390_20),
		"R_390_32":                           reflect.ValueOf(elf.R_390_32),
		"R_390_64":                           reflect.ValueOf(elf.R_390_64),
		"R_390_8":                            reflect.ValueOf(elf.R_390_8),
		"R_390_COPY":                         reflect.ValueOf(elf.R_390_COPY),
		"R_390_GLOB_DAT":                     reflect.ValueOf(elf.R_390_GLOB_DAT),
		"R_390_GOT12":                        reflect.ValueOf(elf.R_390_GOT12),
		"R_390_GOT16":                        reflect.ValueOf(elf.R_390_GOT16),
		"R_390_GOT20":                        reflect.ValueOf(elf.R_390_GOT20),
		"R_390_GOT32":                        reflect.ValueOf(elf.R_390_GOT32),
		"R_390_GOT64":                        reflect.ValueOf(elf.R_390_GOT64),
		"R_390_GOTENT":                       reflect.ValueOf(elf.R_390_GOTENT),
		"R_390_GOTOFF":                       reflect.ValueOf(elf.R_390_GOTOFF),
		"R_390_GOTOFF16":                     reflect.ValueOf(elf.R_390_GOTOFF16),
		"R_390_GOTOFF64":                     reflect.ValueOf(elf.R_390_GOTOFF64),
		"R_390_GOTPC":                        reflect.ValueOf(elf.R_390_GOTPC),
		"R_390_GOTPCDBL":                     reflect.ValueOf(elf.R_390_GOTPCDBL),
		"R_390_GOTPLT12":                     reflect.ValueOf(elf.R_390_GOTPLT12),
		"R_390_GOTPLT16":                     reflect.ValueOf(elf.R_390_GOTPLT16),
		"R_390_GOTPLT20":                     reflect.ValueOf(elf.R_390_GOTPLT20),
		"R_390_GOTPLT32":                     reflect.ValueOf(elf.R_390_GOTPLT32),
		"R_390_GOTPLT64":                     reflect.ValueOf(elf.R_390_GOTPLT64),
		"R_390_GOTPLTENT":                    reflect.ValueOf(elf.R_390_GOTPLTENT),
		"R_390_GOTPLTOFF16":                  reflect.ValueOf(elf.R_390_GOTPLTOFF16),
		"R_390_GOTPLTOFF32":                  reflect.ValueOf(elf.R_390_GOTPLTOFF32),
		"R_390_GOTPLTOFF64":                  reflect.ValueOf(elf.R_390_GOTPLTOFF64),
		"R_390_JMP_SLOT":                     reflect.ValueOf(elf.R_390_JMP_SLOT),
		"R_390_NONE":                         reflect.ValueOf(elf.R_390_NONE),
		"R_390_PC16":                         reflect.ValueOf(elf.R_390_PC16),
		"R_390_PC16DBL":                      reflect.ValueOf(elf.R_390_PC16DBL),
		"R_390_PC32":                         reflect.ValueOf(elf.R_390_PC32),
		"R_390_PC32DBL":                      reflect.ValueOf(elf.R_390_PC32DBL),
		"R_390_PC64":                         reflect.ValueOf(elf.R_390_PC64),
		"R_390_PLT16DBL":                     reflect.ValueOf(elf.R_390_PLT16DBL),
		"R_390_PLT32":                        reflect.ValueOf(elf.R_390_PLT32),
		"R_390_PLT32DBL":                     reflect.ValueOf(elf.R_390_PLT32DBL),
		"R_390_PLT64":                        reflect.ValueOf(elf.R_390_PLT64),
		"R_390_RELATIVE":                     reflect.ValueOf(elf.R_390_RELATIVE),
		"R_390_TLS_DTPMOD":                   reflect.ValueOf(elf.R_390_TLS_DTPMOD),
		"R_390_TLS_DTPOFF":                   reflect.ValueOf(elf.R_390_TLS_DTPOFF),
		"R_390_TLS_GD32":                     reflect.ValueOf(elf.R_390_TLS_GD32),
		"R_390_TLS_GD64":                     reflect.ValueOf(elf.R_390_TLS_GD64),
		"R_390_TLS_GDCALL":                   reflect.ValueOf(elf.R_390_TLS_GDCALL),
		"R_390_TLS_GOTIE12":                  reflect.ValueOf(elf.R_390_TLS_GOTIE12),
		"R_390_TLS_GOTIE20":                  reflect.ValueOf(elf.R_390_TLS_GOTIE20),
		"R_390_TLS_GOTIE32":                  reflect.ValueOf(elf.R_390_TLS_GOTIE32),
		"R_390_TLS_GOTIE64":                  reflect.ValueOf(elf.R_390_TLS_GOTIE64),
		"R_390_TLS_IE32":                     reflect.ValueOf(elf.R_390_TLS_IE32),
		"R_390_TLS_IE64":                     reflect.ValueOf(elf.R_390_TLS_IE64),
		"R_390_TLS_IEENT":                    reflect.ValueOf(elf.R_390_TLS_IEENT),
		"R_390_TLS_LDCALL":                   reflect.ValueOf(elf.R_390_TLS_LDCALL),
		"R_390_TLS_LDM32":                    reflect.ValueOf(elf.R_390_TLS_LDM32),
		"R_390_TLS_LDM64":                    reflect.ValueOf(elf.R_390_TLS_LDM64),
		"R_390_TLS_LDO32":                    reflect.ValueOf(elf.R_390_TLS_LDO32),
		"R_390_TLS_LDO64":                    reflect.ValueOf(elf.R_390_TLS_LDO64),
		"R_390_TLS_LE32":                     reflect.ValueOf(elf.R_390_TLS_LE32),
		"R_390_TLS_LE64":                     reflect.ValueOf(elf.R_390_TLS_LE64),
		"R_390_TLS_LOAD":                     reflect.ValueOf(elf.R_390_TLS_LOAD),
		"R_390_TLS_TPOFF":                    reflect.ValueOf(elf.R_390_TLS_TPOFF),
		"R_AARCH64_ABS16":                    reflect.ValueOf(elf.R_AARCH64_ABS16),
		"R_AARCH64_ABS32":                    reflect.ValueOf(elf.R_AARCH64_ABS32),
		"R_AARCH64_ABS64":                    reflect.ValueOf(elf.R_AARCH64_ABS64),
		"R_AARCH64_ADD_ABS_LO12_NC":          reflect.ValueOf(elf.R_AARCH64_ADD_ABS_LO12_NC),
		"R_AARCH64_ADR_GOT_PAGE":             reflect.ValueOf(elf.R_AARCH64_ADR_GOT_PAGE),
		"R_AARCH64_ADR_PREL_LO21":            reflect.ValueOf(elf.R_AARCH64_ADR_PREL_LO21),
		"R_AARCH64_ADR_PREL_PG_HI21":         reflect.ValueOf(elf.R_AARCH64_ADR_PREL_PG_HI21),
		"R_AARCH64_ADR_PREL_PG_HI21_NC":      reflect.ValueOf(elf.R_AARCH64_ADR_PREL_PG_HI21_NC),
		"R_AARCH64_CALL26":                   reflect.ValueOf(elf.R_AARCH64_CALL26),
		"R_AARCH64_CONDBR19":                 reflect.ValueOf(elf.R_AARCH64_CONDBR19),
		"R_AARCH64_COPY":                     reflect.ValueOf(elf.R_AARCH64_COPY),
		"R_AARCH64_GLOB_DAT":                 reflect.ValueOf(elf.R_AARCH64_GLOB_DAT),
		"R_AARCH64_GOT_LD_PREL19":            reflect.ValueOf(elf.R_AARCH64_GOT_LD_PREL19),
		"R_AARCH64_IRELATIVE":                reflect.ValueOf(elf.R_AARCH64_IRELATIVE),
		"R_AARCH64_JUMP26":                   reflect.ValueOf(elf.R_AARCH64_JUMP26),
		"R_AARCH64_JUMP_SLOT":                reflect.ValueOf(elf.R_AARCH64_JUMP_SLOT),
		"R_AARCH64_LD64_GOTOFF_LO15":         reflect.ValueOf(elf.R_AARCH64_LD64_GOTOFF_LO15),
		"R_AARCH64_LD64_GOTPAGE_LO15":        reflect.ValueOf(elf.R_AARCH64_LD64_GOTPAGE_LO15),
		"R_AARCH64_LD64_GOT_LO12_NC":         reflect.ValueOf(elf.R_AARCH64_LD64_GOT_LO12_NC),
		"R_AARCH64_LDST128_ABS_LO12_NC":      reflect.ValueOf(elf.R_AARCH64_LDST128_ABS_LO12_NC),
		"R_AARCH64_LDST16_ABS_LO12_NC":       reflect.ValueOf(elf.R_AARCH64_LDST16_ABS_LO12_NC),
		"R_AARCH64_LDST32_ABS_LO12_NC":       reflect.ValueOf(elf.R_AARCH64_LDST32_ABS_LO12_NC),
		"R_AARCH64_LDST64_ABS_LO12_NC":       reflect.ValueOf(elf.R_AARCH64_LDST64_ABS_LO12_NC),
		"R_AARCH64_LDST8_ABS_LO12_NC":        reflect.ValueOf(elf.R_AARCH64_LDST8_ABS_LO12_NC),
		"R_AARCH64_LD_PREL_LO19":             reflect.ValueOf(elf.R_AARCH64_LD_PREL_LO19),
		"R_AARCH64_MOVW_SABS_G0":             reflect.ValueOf(elf.R_AARCH64_MOVW_SABS_G0),
		"R_AARCH64_MOVW_SABS_G1":             reflect.ValueOf(elf.R_AARCH64_MOVW_SABS_G1),
		"R_AARCH64_MOVW_SABS_G2":             reflect.ValueOf(elf.R_AARCH64_MOVW_SABS_G2),
		"R_AARCH64_MOVW_UABS_G0":             reflect.ValueOf(elf.R_AARCH64_MOVW_UABS_G0),
		"R_AARCH64_MOVW_UABS_G0_NC":          reflect.ValueOf(elf.R_AARCH64_MOVW_UABS_G0_NC),
		"R_AARCH64_MOVW_UABS_G1":             reflect.ValueOf(elf.R_AARCH64_MOVW_UABS_G1),
		"R_AARCH64_MOVW_UABS_G1_NC":          reflect.ValueOf(elf.R_AARCH64_MOVW_UABS_G1_NC),
		"R_AARCH64_MOVW_UABS_G2":             reflect.ValueOf(elf.R_AARCH64_MOVW_UABS_G2),
		"R_AARCH64_MOVW_UABS_G2_NC":          reflect.ValueOf(elf.R_AARCH64_MOVW_UABS_G2_NC),
		"R_AARCH64_MOVW_UABS_G3":             reflect.ValueOf(elf.R_AARCH64_MOVW_UABS_G3),
		"R_AARCH64_NONE":                     reflect.ValueOf(elf.R_AARCH64_NONE),
		"R_AARCH64_NULL":                     reflect.ValueOf(elf.R_AARCH64_NULL),
		"R_AARCH64_P32_ABS16":                reflect.ValueOf(elf.R_AARCH64_P32_ABS16),
		"R_AARCH64_P32_ABS32":                reflect.ValueOf(elf.R_AARCH64_P32_ABS32),
		"R_AARCH64_P32_ADD_ABS_LO12_NC":      reflect.ValueOf(elf.R_AARCH64_P32_ADD_ABS_LO12_NC),
		"R_AARCH64_P32_ADR_GOT_PAGE":         reflect.ValueOf(elf.R_AARCH64_P32_ADR_GOT_PAGE),
		"R_AARCH64_P32_ADR_PREL_LO21":        reflect.ValueOf(elf.R_AARCH64_P32_ADR_PREL_LO21),
		"R_AARCH64_P32_ADR_PREL_PG_HI21":     reflect.ValueOf(elf.R_AARCH64_P32_ADR_PREL_PG_HI21),
		"R_AARCH64_P32_CALL26":               reflect.ValueOf(elf.R_AARCH64_P32_CALL26),
		"R_AARCH64_P32_CONDBR19":             reflect.ValueOf(elf.R_AARCH64_P32_CONDBR19),
		"R_AARCH64_P32_COPY":                 reflect.ValueOf(elf.R_AARCH64_P32_COPY),
		"R_AARCH64_P32_GLOB_DAT":             reflect.ValueOf(elf.R_AARCH64_P32_GLOB_DAT),
		"R_AARCH64_P32_GOT_LD_PREL19":        reflect.ValueOf(elf.R_AARCH64_P32_GOT_LD_PREL19),
		"R_AARCH64_P32_IRELATIVE":            reflect.ValueOf(elf.R_AARCH64_P32_IRELATIVE),
		"R_AARCH64_P32_JUMP26":               reflect.ValueOf(elf.R_AARCH64_P32_JUMP26),
		"R_AARCH64_P32_JUMP_SLOT":            reflect.ValueOf(elf.R_AARCH64_P32_JUMP_SLOT),
		"R_AARCH64_P32_LD32_GOT_LO12_NC":     reflect.ValueOf(elf.R_AARCH64_P32_LD32_GOT_LO12_NC),
		"R_AARCH64_P32_LDST128_ABS_LO12_NC":  reflect.ValueOf(elf.R_AARCH64_P32_LDST128_ABS_LO12_NC),
		"R_AARCH64_P32_LDST16_ABS_LO12_NC":   reflect.ValueOf(elf.R_AARCH64_P32_LDST16_ABS_LO12_NC),
		"R_AARCH64_P32_LDST32_ABS_LO12_NC":   reflect.ValueOf(elf.R_AARCH64_P32_LDST32_ABS_LO12_NC),
		"R_AARCH64_P32_LDST64_ABS_LO12_NC":   reflect.ValueOf(elf.R_AARCH64_P32_LDST64_ABS_LO12_NC),
		"R_AARCH64_P32_LDST8_ABS_LO12_NC":    reflect.ValueOf(elf.R_AARCH64_P32_LDST8_ABS_LO12_NC),
		"R_AARCH64_P32_LD_PREL_LO19":         reflect.ValueOf(elf.R_AARCH64_P32_LD_PREL_LO19),
		"R_AARCH64_P32_MOVW_SABS_G0":         reflect.ValueOf(elf.R_AARCH64_P32_MOVW_SABS_G0),
		"R_AARCH64_P32_MOVW_UABS_G0":         reflect.ValueOf(elf.R_AARCH64_P32_MOVW_UABS_G0),
		"R_AARCH64_P32_MOVW_UABS_G0_NC":      reflect.ValueOf(elf.R_AARCH64_P32_MOVW_UABS_G0_NC),
		"R_AARCH64_P32_MOVW_UABS_G1":         reflect.ValueOf(elf.R_AARCH64_P32_MOVW_UABS_G1),
		"R_AARCH64_P32_PREL16":               reflect.ValueOf(elf.R_AARCH64_P32_PREL16),
		"R_AARCH64_P32_PREL32":               reflect.ValueOf(elf.R_AARCH64_P32_PREL32),
		"R_AARCH64_P32_RELATIVE":             reflect.ValueOf(elf.R_AARCH64_P32_RELATIVE),
		"R_AARCH64_P32_TLSDESC":              reflect.ValueOf(elf.R_AARCH64_P32_TLSDESC),
		"R_AARCH64_P32_TLSDESC_ADD_LO12_NC":  reflect.ValueOf(elf.R_AARCH64_P32_TLSDESC_ADD_LO12_NC),
		"R_AARCH64_P32_TLSDESC_ADR_PAGE21":   reflect.ValueOf(elf.R_AARCH64_P32_TLSDESC_ADR_PAGE21),
		"R_AARCH64_P32_TLSDESC_ADR_PREL21":   reflect.ValueOf(elf.R_AARCH64_P32_TLSDESC_ADR_PREL21),
		"R_AARCH64_P32_TLSDESC_CALL":         reflect.ValueOf(elf.R_AARCH64_P32_TLSDESC_CALL),
		"R_AARCH64_P32_TLSDESC_LD32_LO12_NC": reflect.ValueOf(elf.R_AARCH64_P32_TLSDESC_LD32_LO12_NC),
		"R_AARCH64_P32_TLSDESC_LD_PREL19":    reflect.ValueOf(elf.R_AARCH64_P32_TLSDESC_LD_PREL19),
		"R_AARCH64_P32_TLSGD_ADD_LO12_NC":    reflect.ValueOf(elf.R_AARCH64_P32_TLSGD_ADD_LO12_NC),
		"R_AARCH64_P32_TLSGD_ADR_PAGE21":     reflect.ValueOf(elf.R_AARCH64_P32_TLSGD_ADR_PAGE21),
		"R_AARCH64_P32_TLSIE_ADR_GOTTPREL_PAGE21":   reflect.ValueOf(elf.R_AARCH64_P32_TLSIE_ADR_GOTTPREL_PAGE21),
		"R_AARCH64_P32_TLSIE_LD32_GOTTPREL_LO12_NC": reflect.ValueOf(elf.R_AARCH64_P32_TLSIE_LD32_GOTTPREL_LO12_NC),
		"R_AARCH64_P32_TLSIE_LD_GOTTPREL_PREL19":    reflect.ValueOf(elf.R_AARCH64_P32_TLSIE_LD_GOTTPREL_PREL19),
		"R_AARCH64_P32_TLSLE_ADD_TPREL_HI12":        reflect.ValueOf(elf.R_AARCH64_P32_TLSLE_ADD_TPREL_HI12),
		"R_AARCH64_P32_TLSLE_ADD_TPREL_LO12":        reflect.ValueOf(elf.R_AARCH64_P32_TLSLE_ADD_TPREL_LO12),
		"R_AARCH64_P32_TLSLE_ADD_TPREL_LO12_NC":     reflect.ValueOf(elf.R_AARCH64_P32_TLSLE_ADD_TPREL_LO12_NC),
		"R_AARCH64_P32_TLSLE_MOVW_TPREL_G0":         reflect.ValueOf(elf.R_AARCH64_P32_TLSLE_MOVW_TPREL_G0),
		"R_AARCH64_P32_TLSLE_MOVW_TPREL_G0_NC":      reflect.ValueOf(elf.R_AARCH64_P32_TLSLE_MOVW_TPREL_G0_NC),
		"R_AARCH64_P32_TLSLE_MOVW_TPREL_G1":         reflect.ValueOf(elf.R_AARCH64_P32_TLSLE_MOVW_TPREL_G1),
		"R_AARCH64_P32_TLS_DTPMOD":                  reflect.ValueOf(elf.R_AARCH64_P32_TLS_DTPMOD),
		"R_AARCH64_P32_TLS_DTPREL":                  reflect.ValueOf(elf.R_AARCH64_P32_TLS_DTPREL),
		"R_AARCH64_P32_TLS_TPREL":                   reflect.ValueOf(elf.R_AARCH64_P32_TLS_TPREL),
		"R_AARCH64_P32_TSTBR14":                     reflect.ValueOf(elf.R_AARCH64_P32_TSTBR14),
		"R_AARCH64_PREL16":                          reflect.ValueOf(elf.R_AARCH64_PREL16),
		"R_AARCH64_PREL32":                          reflect.ValueOf(elf.R_AARCH64_PREL32),
		"R_AARCH64_PREL64":                          reflect.ValueOf(elf.R_AARCH64_PREL64),
		"R_AARCH64_RELATIVE":                        reflect.ValueOf(elf.R_AARCH64_RELATIVE),
		"R_AARCH64_TLSDESC":                         reflect.ValueOf(elf.R_AARCH64_TLSDESC),
		"R_AARCH64_TLSDESC_ADD":                     reflect.ValueOf(elf.R_AARCH64_TLSDESC_ADD),
		"R_AARCH64_TLSDESC_ADD_LO12_NC":             reflect.ValueOf(elf.R_AARCH64_TLSDESC_ADD_LO12_NC),
		"R_AARCH64_TLSDESC_ADR_PAGE21":              reflect.ValueOf(elf.R_AARCH64_TLSDESC_ADR_PAGE21),
		"R_AARCH64_TLSDESC_ADR_PREL21":              reflect.ValueOf(elf.R_AARCH64_TLSDESC_ADR_PREL21),
		"R_AARCH64_TLSDESC_CALL":                    reflect.ValueOf(elf.R_AARCH64_TLSDESC_CALL),
		"R_AARCH64_TLSDESC_LD64_LO12_NC":            reflect.ValueOf(elf.R_AARCH64_TLSDESC_LD64_LO12_NC),
		"R_AARCH64_TLSDESC_LDR":                     reflect.ValueOf(elf.R_AARCH64_TLSDESC_LDR),
		"R_AARCH64_TLSDESC_LD_PREL19":               reflect.ValueOf(elf.R_AARCH64_TLSDESC_LD_PREL19),
		"R_AARCH64_TLSDESC_OFF_G0_NC":               reflect.ValueOf(elf.R_AARCH64_TLSDESC_OFF_G0_NC),
		"R_AARCH64_TLSDESC_OFF_G1":                  reflect.ValueOf(elf.R_AARCH64_TLSDESC_OFF_G1),
		"R_AARCH64_TLSGD_ADD_LO12_NC":               reflect.ValueOf(elf.R_AARCH64_TLSGD_ADD_LO12_NC),
		"R_AARCH64_TLSGD_ADR_PAGE21":                reflect.ValueOf(elf.R_AARCH64_TLSGD_ADR_PAGE21),
		"R_AARCH64_TLSGD_ADR_PREL21":                reflect.ValueOf(elf.R_AARCH64_TLSGD_ADR_PREL21),
		"R_AARCH64_TLSGD_MOVW_G0_NC":                reflect.ValueOf(elf.R_AARCH64_TLSGD_MOVW_G0_NC),
		"R_AARCH64_TLSGD_MOVW_G1":                   reflect.ValueOf(elf.R_AARCH64_TLSGD_MOVW_G1),
		"R_AARCH64_TLSIE_ADR_GOTTPREL_PAGE21":       reflect.ValueOf(elf.R_AARCH64_TLSIE_ADR_GOTTPREL_PAGE21),
		"R_AARCH64_TLSIE_LD64_GOTTPREL_LO12_NC":     reflect.ValueOf(elf.R_AARCH64_TLSIE_LD64_GOTTPREL_LO12_NC),
		"R_AARCH64_TLSIE_LD_GOTTPREL_PREL19":        reflect.ValueOf(elf.R_AARCH64_TLSIE_LD_GOTTPREL_PREL19),
		"R_AARCH64_TLSIE_MOVW_GOTTPREL_G0_NC":       reflect.ValueOf(elf.R_AARCH64_TLSIE_MOVW_GOTTPREL_G0_NC),
		"R_AARCH64_TLSIE_MOVW_GOTTPREL_G1":          reflect.ValueOf(elf.R_AARCH64_TLSIE_MOVW_GOTTPREL_G1),
		"R_AARCH64_TLSLD_ADR_PAGE21":                reflect.ValueOf(elf.R_AARCH64_TLSLD_ADR_PAGE21),
		"R_AARCH64_TLSLD_ADR_PREL21":                reflect.ValueOf(elf.R_AARCH64_TLSLD_ADR_PREL21),
		"R_AARCH64_TLSLD_LDST128_DTPREL_LO12":       reflect.ValueOf(elf.R_AARCH64_TLSLD_LDST128_DTPREL_LO12),
		"R_AARCH64_TLSLD_LDST128_DTPREL_LO12_NC":    reflect.ValueOf(elf.R_AARCH64_TLSLD_LDST128_DTPREL_LO12_NC),
		"R_AARCH64_TLSLE_ADD_TPREL_HI12":            reflect.ValueOf(elf.R_AARCH64_TLSLE_ADD_TPREL_HI12),
		"R_AARCH64_TLSLE_ADD_TPREL_LO12":            reflect.ValueOf(elf.R_AARCH64_TLSLE_ADD_TPREL_LO12),
		"R_AARCH64_TLSLE_ADD_TPREL_LO12_NC":         reflect.ValueOf(elf.R_AARCH64_TLSLE_ADD_TPREL_LO12_NC),
		"R_AARCH64_TLSLE_LDST128_TPREL_LO12":        reflect.ValueOf(elf.R_AARCH64_TLSLE_LDST128_TPREL_LO12),
		"R_AARCH64_TLSLE_LDST128_TPREL_LO12_NC":     reflect.ValueOf(elf.R_AARCH64_TLSLE_LDST128_TPREL_LO12_NC),
		"R_AARCH64_TLSLE_MOVW_TPREL_G0":             reflect.ValueOf(elf.R_AARCH64_TLSLE_MOVW_TPREL_G0),
		"R_AARCH64_TLSLE_MOVW_TPREL_G0_NC":          reflect.ValueOf(elf.R_AARCH64_TLSLE_MOVW_TPREL_G0_NC),
		"R_AARCH64_TLSLE_MOVW_TPREL_G1":             reflect.ValueOf(elf.R_AARCH64_TLSLE_MOVW_TPREL_G1),
		"R_AARCH64_TLSLE_MOVW_TPREL_G1_NC":          reflect.ValueOf(elf.R_AARCH64_TLSLE_MOVW_TPREL_G1_NC),
		"R_AARCH64_TLSLE_MOVW_TPREL_G2":             reflect.ValueOf(elf.R_AARCH64_TLSLE_MOVW_TPREL_G2),
		"R_AARCH64_TLS_DTPMOD64":                    reflect.ValueOf(elf.R_AARCH64_TLS_DTPMOD64),
		"R_AARCH64_TLS_DTPREL64":                    reflect.ValueOf(elf.R_AARCH64_TLS_DTPREL64),
		"R_AARCH64_TLS_TPREL64":                     reflect.ValueOf(elf.R_AARCH64_TLS_TPREL64),
		"R_AARCH64_TSTBR14":                         reflect.ValueOf(elf.R_AARCH64_TSTBR14),
		"R_ALPHA_BRADDR":                            reflect.ValueOf(elf.R_ALPHA_BRADDR),
		"R_ALPHA_COPY":                              reflect.ValueOf(elf.R_ALPHA_COPY),
		"R_ALPHA_GLOB_DAT":                          reflect.ValueOf(elf.R_ALPHA_GLOB_DAT),
		"R_ALPHA_GPDISP":                            reflect.ValueOf(elf.R_ALPHA_GPDISP),
		"R_ALPHA_GPREL32":                           reflect.ValueOf(elf.R_ALPHA_GPREL32),
		"R_ALPHA_GPRELHIGH":                         reflect.ValueOf(elf.R_ALPHA_GPRELHIGH),
		"R_ALPHA_GPRELLOW":                          reflect.ValueOf(elf.R_ALPHA_GPRELLOW),
		"R_ALPHA_GPVALUE":                           reflect.ValueOf(elf.R_ALPHA_GPVALUE),
		"R_ALPHA_HINT":                              reflect.ValueOf(elf.R_ALPHA_HINT),
		"R_ALPHA_IMMED_BR_HI32":                     reflect.ValueOf(elf.R_ALPHA_IMMED_BR_HI32),
		"R_ALPHA_IMMED_GP_16":                       reflect.ValueOf(elf.R_ALPHA_IMMED_GP_16),
		"R_ALPHA_IMMED_GP_HI32":                     reflect.ValueOf(elf.R_ALPHA_IMMED_GP_HI32),
		"R_ALPHA_IMMED_LO32":                        reflect.ValueOf(elf.R_ALPHA_IMMED_LO32),
		"R_ALPHA_IMMED_SCN_HI32":                    reflect.ValueOf(elf.R_ALPHA_IMMED_SCN_HI32),
		"R_ALPHA_JMP_SLOT":                          reflect.ValueOf(elf.R_ALPHA_JMP_SLOT),
		"R_ALPHA_LITERAL":                           reflect.ValueOf(elf.R_ALPHA_LITERAL),
		"R_ALPHA_LITUSE":                            reflect.ValueOf(elf.R_ALPHA_LITUSE),
		"R_ALPHA_NONE":                              reflect.ValueOf(elf.R_ALPHA_NONE),
		"R_ALPHA_OP_PRSHIFT":                        reflect.ValueOf(elf.R_ALPHA_OP_PRSHIFT),
		"R_ALPHA_OP_PSUB":                           reflect.ValueOf(elf.R_ALPHA_OP_PSUB),
		"R_ALPHA_OP_PUSH":                           reflect.ValueOf(elf.R_ALPHA_OP_PUSH),
		"R_ALPHA_OP_STORE":                          reflect.ValueOf(elf.R_ALPHA_OP_STORE),
		"R_ALPHA_REFLONG":                           reflect.ValueOf(elf.R_ALPHA_REFLONG),
		"R_ALPHA_REFQUAD":                           reflect.ValueOf(elf.R_ALPHA_REFQUAD),
		"R_ALPHA_RELATIVE":                          reflect.ValueOf(elf.R_ALPHA_RELATIVE),
		"R_ALPHA_SREL16":                            reflect.ValueOf(elf.R_ALPHA_SREL16),
		"R_ALPHA_SREL32":                            reflect.ValueOf(elf.R_ALPHA_SREL32),
		"R_ALPHA_SREL64":                            reflect.ValueOf(elf.R_ALPHA_SREL64),
		"R_ARM_ABS12":                               reflect.ValueOf(elf.R_ARM_ABS12),
		"R_ARM_ABS16":                               reflect.ValueOf(elf.R_ARM_ABS16),
		"R_ARM_ABS32":                               reflect.ValueOf(elf.R_ARM_ABS32),
		"R_ARM_ABS32_NOI":                           reflect.ValueOf(elf.R_ARM_ABS32_NOI),
		"R_ARM_ABS8":                                reflect.ValueOf(elf.R_ARM_ABS8),
		"R_ARM_ALU_PCREL_15_8":                      reflect.ValueOf(elf.R_ARM_ALU_PCREL_15_8),
		"R_ARM_ALU_PCREL_23_15":                     reflect.ValueOf(elf.R_ARM_ALU_PCREL_23_15),
		"R_ARM_ALU_PCREL_7_0":                       reflect.ValueOf(elf.R_ARM_ALU_PCREL_7_0),
		"R_ARM_ALU_PC_G0":                           reflect.ValueOf(elf.R_ARM_ALU_PC_G0),
		"R_ARM_ALU_PC_G0_NC":                        reflect.ValueOf(elf.R_ARM_ALU_PC_G0_NC),
		"R_ARM_ALU_PC_G1":                           reflect.ValueOf(elf.R_ARM_ALU_PC_G1),
		"R_ARM_ALU_PC_G1_NC":                        reflect.ValueOf(elf.R_ARM_ALU_PC_G1_NC),
		"R_ARM_ALU_PC_G2":                           reflect.ValueOf(elf.R_ARM_ALU_PC_G2),
		"R_ARM_ALU_SBREL_19_12_NC":                  reflect.ValueOf(elf.R_ARM_ALU_SBREL_19_12_NC),
		"R_ARM_ALU_SBREL_27_20_CK":                  reflect.ValueOf(elf.R_ARM_ALU_SBREL_27_20_CK),
		"R_ARM_ALU_SB_G0":                           reflect.ValueOf(elf.R_ARM_ALU_SB_G0),
		"R_ARM_ALU_SB_G0_NC":                        reflect.ValueOf(elf.R_ARM_ALU_SB_G0_NC),
		"R_ARM_ALU_SB_G1":                           reflect.ValueOf(elf.R_ARM_ALU_SB_G1),
		"R_ARM_ALU_SB_G1_NC":                        reflect.ValueOf(elf.R_ARM_ALU_SB_G1_NC),
		"R_ARM_ALU_SB_G2":                           reflect.ValueOf(elf.R_ARM_ALU_SB_G2),
		"R_ARM_AMP_VCALL9":                          reflect.ValueOf(elf.R_ARM_AMP_VCALL9),
		"R_ARM_BASE_ABS":                            reflect.ValueOf(elf.R_ARM_BASE_ABS),
		"R_ARM_CALL":                                reflect.ValueOf(elf.R_ARM_CALL),
		"R_ARM_COPY":                                reflect.ValueOf(elf.R_ARM_COPY),
		"R_ARM_GLOB_DAT":                            reflect.ValueOf(elf.R_ARM_GLOB_DAT),
		"R_ARM_GNU_VTENTRY":                         reflect.ValueOf(elf.R_ARM_GNU_VTENTRY),
		"R_ARM_GNU_VTINHERIT":                       reflect.ValueOf(elf.R_ARM_GNU_VTINHERIT),
		"R_ARM_GOT32":                               reflect.ValueOf(elf.R_ARM_GOT32),
		"R_ARM_GOTOFF":                              reflect.ValueOf(elf.R_ARM_GOTOFF),
		"R_ARM_GOTOFF12":                            reflect.ValueOf(elf.R_ARM_GOTOFF12),
		"R_ARM_GOTPC":                               reflect.ValueOf(elf.R_ARM_GOTPC),
		"R_ARM_GOTRELAX":                            reflect.ValueOf(elf.R_ARM_GOTRELAX),
		"R_ARM_GOT_ABS":                             reflect.ValueOf(elf.R_ARM_GOT_ABS),
		"R_ARM_GOT_BREL12":                          reflect.ValueOf(elf.R_ARM_GOT_BREL12),
		"R_ARM_GOT_PREL":                            reflect.ValueOf(elf.R_ARM_GOT_PREL),
		"R_ARM_IRELATIVE":                           reflect.ValueOf(elf.R_ARM_IRELATIVE),
		"R_ARM_JUMP24":                              reflect.ValueOf(elf.R_ARM_JUMP24),
		"R_ARM_JUMP_SLOT":                           reflect.ValueOf(elf.R_ARM_JUMP_SLOT),
		"R_ARM_LDC_PC_G0":                           reflect.ValueOf(elf.R_ARM_LDC_PC_G0),
		"R_ARM_LDC_PC_G1":                           reflect.ValueOf(elf.R_ARM_LDC_PC_G1),
		"R_ARM_LDC_PC_G2":                           reflect.ValueOf(elf.R_ARM_LDC_PC_G2),
		"R_ARM_LDC_SB_G0":                           reflect.ValueOf(elf.R_ARM_LDC_SB_G0),
		"R_ARM_LDC_SB_G1":                           reflect.ValueOf(elf.R_ARM_LDC_SB_G1),
		"R_ARM_LDC_SB_G2":                           reflect.ValueOf(elf.R_ARM_LDC_SB_G2),
		"R_ARM_LDRS_PC_G0":                          reflect.ValueOf(elf.R_ARM_LDRS_PC_G0),
		"R_ARM_LDRS_PC_G1":                          reflect.ValueOf(elf.R_ARM_LDRS_PC_G1),
		"R_ARM_LDRS_PC_G2":                          reflect.ValueOf(elf.R_ARM_LDRS_PC_G2),
		"R_ARM_LDRS_SB_G0":                          reflect.ValueOf(elf.R_ARM_LDRS_SB_G0),
		"R_ARM_LDRS_SB_G1":                          reflect.ValueOf(elf.R_ARM_LDRS_SB_G1),
		"R_ARM_LDRS_SB_G2":                          reflect.ValueOf(elf.R_ARM_LDRS_SB_G2),
		"R_ARM_LDR_PC_G1":                           reflect.ValueOf(elf.R_ARM_LDR_PC_G1),
		"R_ARM_LDR_PC_G2":                           reflect.ValueOf(elf.R_ARM_LDR_PC_G2),
		"R_ARM_LDR_SBREL_11_10_NC":                  reflect.ValueOf(elf.R_ARM_LDR_SBREL_11_10_NC),
		"R_ARM_LDR_SB_G0":                           reflect.ValueOf(elf.R_ARM_LDR_SB_G0),
		"R_ARM_LDR_SB_G1":                           reflect.ValueOf(elf.R_ARM_LDR_SB_G1),
		"R_ARM_LDR_SB_G2":                           reflect.ValueOf(elf.R_ARM_LDR_SB_G2),
		"R_ARM_ME_TOO":                              reflect.ValueOf(elf.R_ARM_ME_TOO),
		"R_ARM_MOVT_ABS":                            reflect.ValueOf(elf.R_ARM_MOVT_ABS),
		"R_ARM_MOVT_BREL":                           reflect.ValueOf(elf.R_ARM_MOVT_BREL),
		"R_ARM_MOVT_PREL":                           reflect.ValueOf(elf.R_ARM_MOVT_PREL),
		"R_ARM_MOVW_ABS_NC":                         reflect.ValueOf(elf.R_ARM_MOVW_ABS_NC),
		"R_ARM_MOVW_BREL":                           reflect.ValueOf(elf.R_ARM_MOVW_BREL),
		"R_ARM_MOVW_BREL_NC":                        reflect.ValueOf(elf.R_ARM_MOVW_BREL_NC),
		"R_ARM_MOVW_PREL_NC":                        reflect.ValueOf(elf.R_ARM_MOVW_PREL_NC),
		"R_ARM_NONE":                                reflect.ValueOf(elf.R_ARM_NONE),
		"R_ARM_PC13":                                reflect.ValueOf(elf.R_ARM_PC13),
		"R_ARM_PC24":                                reflect.ValueOf(elf.R_ARM_PC24),
		"R_ARM_PLT32":                               reflect.ValueOf(elf.R_ARM_PLT32),
		"R_ARM_PLT32_ABS":                           reflect.ValueOf(elf.R_ARM_PLT32_ABS),
		"R_ARM_PREL31":                              reflect.ValueOf(elf.R_ARM_PREL31),
		"R_ARM_PRIVATE_0":                           reflect.ValueOf(elf.R_ARM_PRIVATE_0),
		"R_ARM_PRIVATE_1":                           reflect.ValueOf(elf.R_ARM_PRIVATE_1),
		"R_ARM_PRIVATE_10":                          reflect.ValueOf(elf.R_ARM_PRIVATE_10),
		"R_ARM_PRIVATE_11":                          reflect.ValueOf(elf.R_ARM_PRIVATE_11),
		"R_ARM_PRIVATE_12":                          reflect.ValueOf(elf.R_ARM_PRIVATE_12),
		"R_ARM_PRIVATE_13":                          reflect.ValueOf(elf.R_ARM_PRIVATE_13),
		"R_ARM_PRIVATE_14":                          reflect.ValueOf(elf.R_ARM_PRIVATE_14),
		"R_ARM_PRIVATE_15":                          reflect.ValueOf(elf.R_ARM_PRIVATE_15),
		"R_ARM_PRIVATE_2":                           reflect.ValueOf(elf.R_ARM_PRIVATE_2),
		"R_ARM_PRIVATE_3":                           reflect.ValueOf(elf.R_ARM_PRIVATE_3),
		"R_ARM_PRIVATE_4":                           reflect.ValueOf(elf.R_ARM_PRIVATE_4),
		"R_ARM_PRIVATE_5":                           reflect.ValueOf(elf.R_ARM_PRIVATE_5),
		"R_ARM_PRIVATE_6":                           reflect.ValueOf(elf.R_ARM_PRIVATE_6),
		"R_ARM_PRIVATE_7":                           reflect.ValueOf(elf.R_ARM_PRIVATE_7),
		"R_ARM_PRIVATE_8":                           reflect.ValueOf(elf.R_ARM_PRIVATE_8),
		"R_ARM_PRIVATE_9":                           reflect.ValueOf(elf.R_ARM_PRIVATE_9),
		"R_ARM_RABS32":                              reflect.ValueOf(elf.R_ARM_RABS32),
		"R_ARM_RBASE":                               reflect.ValueOf(elf.R_ARM_RBASE),
		"R_ARM_REL32":                               reflect.ValueOf(elf.R_ARM_REL32),
		"R_ARM_REL32_NOI":                           reflect.ValueOf(elf.R_ARM_REL32_NOI),
		"R_ARM_RELATIVE":                            reflect.ValueOf(elf.R_ARM_RELATIVE),
		"R_ARM_RPC24":                               reflect.ValueOf(elf.R_ARM_RPC24),
		"R_ARM_RREL32":                              reflect.ValueOf(elf.R_ARM_RREL32),
		"R_ARM_RSBREL32":                            reflect.ValueOf(elf.R_ARM_RSBREL32),
		"R_ARM_RXPC25":                              reflect.ValueOf(elf.R_ARM_RXPC25),
		"R_ARM_SBREL31":                             reflect.ValueOf(elf.R_ARM_SBREL31),
		"R_ARM_SBREL32":                             reflect.ValueOf(elf.R_ARM_SBREL32),
		"R_ARM_SWI24":                               reflect.ValueOf(elf.R_ARM_SWI24),
		"R_ARM_TARGET1":                             reflect.ValueOf(elf.R_ARM_TARGET1),
		"R_ARM_TARGET2":                             reflect.ValueOf(elf.R_ARM_TARGET2),
		"R_ARM_THM_ABS5":                            reflect.ValueOf(elf.R_ARM_THM_ABS5),
		"R_ARM_THM_ALU_ABS_G0_NC":                   reflect.ValueOf(elf.R_ARM_THM_ALU_ABS_G0_NC),
		"R_ARM_THM_ALU_ABS_G1_NC":                   reflect.ValueOf(elf.R_ARM_THM_ALU_ABS_G1_NC),
		"R_ARM_THM_ALU_ABS_G2_NC":                   reflect.ValueOf(elf.R_ARM_THM_ALU_ABS_G2_NC),
		"R_ARM_THM_ALU_ABS_G3":                      reflect.ValueOf(elf.R_ARM_THM_ALU_ABS_G3),
		"R_ARM_THM_ALU_PREL_11_0":                   reflect.ValueOf(elf.R_ARM_THM_ALU_PREL_11_0),
		"R_ARM_THM_GOT_BREL12":                      reflect.ValueOf(elf.R_ARM_THM_GOT_BREL12),
		"R_ARM_THM_JUMP11":                          reflect.ValueOf(elf.R_ARM_THM_JUMP11),
		"R_ARM_THM_JUMP19":                          reflect.ValueOf(elf.R_ARM_THM_JUMP19),
		"R_ARM_THM_JUMP24":                          reflect.ValueOf(elf.R_ARM_THM_JUMP24),
		"R_ARM_THM_JUMP6":                           reflect.ValueOf(elf.R_ARM_THM_JUMP6),
		"R_ARM_THM_JUMP8":                           reflect.ValueOf(elf.R_ARM_THM_JUMP8),
		"R_ARM_THM_MOVT_ABS":                        reflect.ValueOf(elf.R_ARM_THM_MOVT_ABS),
		"R_ARM_THM_MOVT_BREL":                       reflect.ValueOf(elf.R_ARM_THM_MOVT_BREL),
		"R_ARM_THM_MOVT_PREL":                       reflect.ValueOf(elf.R_ARM_THM_MOVT_PREL),
		"R_ARM_THM_MOVW_ABS_NC":                     reflect.ValueOf(elf.R_ARM_THM_MOVW_ABS_NC),
		"R_ARM_THM_MOVW_BREL":                       reflect.ValueOf(elf.R_ARM_THM_MOVW_BREL),
		"R_ARM_THM_MOVW_BREL_NC":                    reflect.ValueOf(elf.R_ARM_THM_MOVW_BREL_NC),
		"R_ARM_THM_MOVW_PREL_NC":                    reflect.ValueOf(elf.R_ARM_THM_MOVW_PREL_NC),
		"R_ARM_THM_PC12":                            reflect.ValueOf(elf.R_ARM_THM_PC12),
		"R_ARM_THM_PC22":                            reflect.ValueOf(elf.R_ARM_THM_PC22),
		"R_ARM_THM_PC8":                             reflect.ValueOf(elf.R_ARM_THM_PC8),
		"R_ARM_THM_RPC22":                           reflect.ValueOf(elf.R_ARM_THM_RPC22),
		"R_ARM_THM_SWI8":                            reflect.ValueOf(elf.R_ARM_THM_SWI8),
		"R_ARM_THM_TLS_CALL":                        reflect.ValueOf(elf.R_ARM_THM_TLS_CALL),
		"R_ARM_THM_TLS_DESCSEQ16":                   reflect.ValueOf(elf.R_ARM_THM_TLS_DESCSEQ16),
		"R_ARM_THM_TLS_DESCSEQ32":                   reflect.ValueOf(elf.R_ARM_THM_TLS_DESCSEQ32),
		"R_ARM_THM_XPC22":                           reflect.ValueOf(elf.R_ARM_THM_XPC22),
		"R_ARM_TLS_CALL":                            reflect.ValueOf(elf.R_ARM_TLS_CALL),
		"R_ARM_TLS_DESCSEQ":                         reflect.ValueOf(elf.R_ARM_TLS_DESCSEQ),
		"R_ARM_TLS_DTPMOD32":                        reflect.ValueOf(elf.R_ARM_TLS_DTPMOD32),
		"R_ARM_TLS_DTPOFF32":                        reflect.ValueOf(elf.R_ARM_TLS_DTPOFF32),
		"R_ARM_TLS_GD32":                            reflect.ValueOf(elf.R_ARM_TLS_GD32),
		"R_ARM_TLS_GOTDESC":                         reflect.ValueOf(elf.R_ARM_TLS_GOTDESC),
		"R_ARM_TLS_IE12GP":                          reflect.ValueOf(elf.R_ARM_TLS_IE12GP),
		"R_ARM_TLS_IE32":                            reflect.ValueOf(elf.R_ARM_TLS_IE32),
		"R_ARM_TLS_LDM32":                           reflect.ValueOf(elf.R_ARM_TLS_LDM32),
		"R_ARM_TLS_LDO12":                           reflect.ValueOf(elf.R_ARM_TLS_LDO12),
		"R_ARM_TLS_LDO32":                           reflect.ValueOf(elf.R_ARM_TLS_LDO32),
		"R_ARM_TLS_LE12":                            reflect.ValueOf(elf.R_ARM_TLS_LE12),
		"R_ARM_TLS_LE32":                            reflect.ValueOf(elf.R_ARM_TLS_LE32),
		"R_ARM_TLS_TPOFF32":                         reflect.ValueOf(elf.R_ARM_TLS_TPOFF32),
		"R_ARM_V4BX":                                reflect.ValueOf(elf.R_ARM_V4BX),
		"R_ARM_XPC25":                               reflect.ValueOf(elf.R_ARM_XPC25),
		"R_INFO":                                    reflect.ValueOf(elf.R_INFO),
		"R_INFO32":                                  reflect.ValueOf(elf.R_INFO32),
		"R_LARCH_32":                                reflect.ValueOf(elf.R_LARCH_32),
		"R_LARCH_32_PCREL":                          reflect.ValueOf(elf.R_LARCH_32_PCREL),
		"R_LARCH_64":                                reflect.ValueOf(elf.R_LARCH_64),
		"R_LARCH_64_PCREL":                          reflect.ValueOf(elf.R_LARCH_64_PCREL),
		"R_LARCH_ABS64_HI12":                        reflect.ValueOf(elf.R_LARCH_ABS64_HI12),
		"R_LARCH_ABS64_LO20":                        reflect.ValueOf(elf.R_LARCH_ABS64_LO20),
		"R_LARCH_ABS_HI20":                          reflect.ValueOf(elf.R_LARCH_ABS_HI20),
		"R_LARCH_ABS_LO12":                          reflect.ValueOf(elf.R_LARCH_ABS_LO12),
		"R_LARCH_ADD16":                             reflect.ValueOf(elf.R_LARCH_ADD16),
		"R_LARCH_ADD24":                             reflect.ValueOf(elf.R_LARCH_ADD24),
		"R_LARCH_ADD32":                             reflect.ValueOf(elf.R_LARCH_ADD32),
		"R_LARCH_ADD6":                              reflect.ValueOf(elf.R_LARCH_ADD6),
		"R_LARCH_ADD64":                             reflect.ValueOf(elf.R_LARCH_ADD64),
		"R_LARCH_ADD8":                              reflect.ValueOf(elf.R_LARCH_ADD8),
		"R_LARCH_ADD_ULEB128":                       reflect.ValueOf(elf.R_LARCH_ADD_ULEB128),
		"R_LARCH_ALIGN":                             reflect.ValueOf(elf.R_LARCH_ALIGN),
		"R_LARCH_B16":                               reflect.ValueOf(elf.R_LARCH_B16),
		"R_LARCH_B21":                               reflect.ValueOf(elf.R_LARCH_B21),
		"R_LARCH_B26":                               reflect.ValueOf(elf.R_LARCH_B26),
		"R_LARCH_CFA":                               reflect.ValueOf(elf.R_LARCH_CFA),
		"R_LARCH_COPY":                              reflect.ValueOf(elf.R_LARCH_COPY),
		"R_LARCH_DELETE":                            reflect.ValueOf(elf.R_LARCH_DELETE),
		"R_LARCH_GNU_VTENTRY":                       reflect.ValueOf(elf.R_LARCH_GNU_VTENTRY),
		"R_LARCH_GNU_VTINHERIT":                     reflect.ValueOf(elf.R_LARCH_GNU_VTINHERIT),
		"R_LARCH_GOT64_HI12":                        reflect.ValueOf(elf.R_LARCH_GOT64_HI12),
		"R_LARCH_GOT64_LO20":                        reflect.ValueOf(elf.R_LARCH_GOT64_LO20),
		"R_LARCH_GOT64_PC_HI12":                     reflect.ValueOf(elf.R_LARCH_GOT64_PC_HI12),
		"R_LARCH_GOT64_PC_LO20":                     reflect.ValueOf(elf.R_LARCH_GOT64_PC_LO20),
		"R_LARCH_GOT_HI20":                          reflect.ValueOf(elf.R_LARCH_GOT_HI20),
		"R_LARCH_GOT_LO12":                          reflect.ValueOf(elf.R_LARCH_GOT_LO12),
		"R_LARCH_GOT_PC_HI20":                       reflect.ValueOf(elf.R_LARCH_GOT_PC_HI20),
		"R_LARCH_GOT_PC_LO12":                       reflect.ValueOf(elf.R_LARCH_GOT_PC_LO12),
		"R_LARCH_IRELATIVE":                         reflect.ValueOf(elf.R_LARCH_IRELATIVE),
		"R_LARCH_JUMP_SLOT":                         reflect.ValueOf(elf.R_LARCH_JUMP_SLOT),
		"R_LARCH_MARK_LA":                           reflect.ValueOf(elf.R_LARCH_MARK_LA),
		"R_LARCH_MARK_PCREL":                        reflect.ValueOf(elf.R_LARCH_MARK_PCREL),
		"R_LARCH_NONE":                              reflect.ValueOf(elf.R_LARCH_NONE),
		"R_LARCH_PCALA64_HI12":                      reflect.ValueOf(elf.R_LARCH_PCALA64_HI12),
		"R_LARCH_PCALA64_LO20":                      reflect.ValueOf(elf.R_LARCH_PCALA64_LO20),
		"R_LARCH_PCALA_HI20":                        reflect.ValueOf(elf.R_LARCH_PCALA_HI20),
		"R_LARCH_PCALA_LO12":                        reflect.ValueOf(elf.R_LARCH_PCALA_LO12),
		"R_LARCH_PCREL20_S2":                        reflect.ValueOf(elf.R_LARCH_PCREL20_S2),
		"R_LARCH_RELATIVE":                          reflect.ValueOf(elf.R_LARCH_RELATIVE),
		"R_LARCH_RELAX":                             reflect.ValueOf(elf.R_LARCH_RELAX),
		"R_LARCH_SOP_ADD":                           reflect.ValueOf(elf.R_LARCH_SOP_ADD),
		"R_LARCH_SOP_AND":                           reflect.ValueOf(elf.R_LARCH_SOP_AND),
		"R_LARCH_SOP_ASSERT":                        reflect.ValueOf(elf.R_LARCH_SOP_ASSERT),
		"R_LARCH_SOP_IF_ELSE":                       reflect.ValueOf(elf.R_LARCH_SOP_IF_ELSE),
		"R_LARCH_SOP_NOT":                           reflect.ValueOf(elf.R_LARCH_SOP_NOT),
		"R_LARCH_SOP_POP_32_S_0_10_10_16_S2":        reflect.ValueOf(elf.R_LARCH_SOP_POP_32_S_0_10_10_16_S2),
		"R_LARCH_SOP_POP_32_S_0_5_10_16_S2":         reflect.ValueOf(elf.R_LARCH_SOP_POP_32_S_0_5_10_16_S2),
		"R_LARCH_SOP_POP_32_S_10_12":                reflect.ValueOf(elf.R_LARCH_SOP_POP_32_S_10_12),
		"R_LARCH_SOP_POP_32_S_10_16":                reflect.ValueOf(elf.R_LARCH_SOP_POP_32_S_10_16),
		"R_LARCH_SOP_POP_32_S_10_16_S2":             reflect.ValueOf(elf.R_LARCH_SOP_POP_32_S_10_16_S2),
		"R_LARCH_SOP_POP_32_S_10_5":                 reflect.ValueOf(elf.R_LARCH_SOP_POP_32_S_10_5),
		"R_LARCH_SOP_POP_32_S_5_20":                 reflect.ValueOf(elf.R_LARCH_SOP_POP_32_S_5_20),
		"R_LARCH_SOP_POP_32_U":                      reflect.ValueOf(elf.R_LARCH_SOP_POP_32_U),
		"R_LARCH_SOP_POP_32_U_10_12":                reflect.ValueOf(elf.R_LARCH_SOP_POP_32_U_10_12),
		"R_LARCH_SOP_PUSH_ABSOLUTE":                 reflect.ValueOf(elf.R_LARCH_SOP_PUSH_ABSOLUTE),
		"R_LARCH_SOP_PUSH_DUP":                      reflect.ValueOf(elf.R_LARCH_SOP_PUSH_DUP),
		"R_LARCH_SOP_PUSH_GPREL":                    reflect.ValueOf(elf.R_LARCH_SOP_PUSH_GPREL),
		"R_LARCH_SOP_PUSH_PCREL":                    reflect.ValueOf(elf.R_LARCH_SOP_PUSH_PCREL),
		"R_LARCH_SOP_PUSH_PLT_PCREL":                reflect.ValueOf(elf.R_LARCH_SOP_PUSH_PLT_PCREL),
		"R_LARCH_SOP_PUSH_TLS_GD":                   reflect.ValueOf(elf.R_LARCH_SOP_PUSH_TLS_GD),
		"R_LARCH_SOP_PUSH_TLS_GOT":                  reflect.ValueOf(elf.R_LARCH_SOP_PUSH_TLS_GOT),
		"R_LARCH_SOP_PUSH_TLS_TPREL":                reflect.ValueOf(elf.R_LARCH_SOP_PUSH_TLS_TPREL),
		"R_LARCH_SOP_SL":                            reflect.ValueOf(elf.R_LARCH_SOP_SL),
		"R_LARCH_SOP_SR":                            reflect.ValueOf(elf.R_LARCH_SOP_SR),
		"R_LARCH_SOP_SUB":                           reflect.ValueOf(elf.R_LARCH_SOP_SUB),
		"R_LARCH_SUB16":                             reflect.ValueOf(elf.R_LARCH_SUB16),
		"R_LARCH_SUB24":                             reflect.ValueOf(elf.R_LARCH_SUB24),
		"R_LARCH_SUB32":                             reflect.ValueOf(elf.R_LARCH_SUB32),
		"R_LARCH_SUB6":                              reflect.ValueOf(elf.R_LARCH_SUB6),
		"R_LARCH_SUB64":                             reflect.ValueOf(elf.R_LARCH_SUB64),
		"R_LARCH_SUB8":                              reflect.ValueOf(elf.R_LARCH_SUB8),
		"R_LARCH_SUB_ULEB128":                       reflect.ValueOf(elf.R_LARCH_SUB_ULEB128),
		"R_LARCH_TLS_DTPMOD32":                      reflect.ValueOf(elf.R_LARCH_TLS_DTPMOD32),
		"R_LARCH_TLS_DTPMOD64":                      reflect.ValueOf(elf.R_LARCH_TLS_DTPMOD64),
		"R_LARCH_TLS_DTPREL32":                      reflect.ValueOf(elf.R_LARCH_TLS_DTPREL32),
		"R_LARCH_TLS_DTPREL64":                      reflect.ValueOf(elf.R_LARCH_TLS_DTPREL64),
		"R_LARCH_TLS_GD_HI20":                       reflect.ValueOf(elf.R_LARCH_TLS_GD_HI20),
		"R_LARCH_TLS_GD_PC_HI20":                    reflect.ValueOf(elf.R_LARCH_TLS_GD_PC_HI20),
		"R_LARCH_TLS_IE64_HI12":                     reflect.ValueOf(elf.R_LARCH_TLS_IE64_HI12),
		"R_LARCH_TLS_IE64_LO20":                     reflect.ValueOf(elf.R_LARCH_TLS_IE64_LO20),
		"R_LARCH_TLS_IE64_PC_HI12":                  reflect.ValueOf(elf.R_LARCH_TLS_IE64_PC_HI12),
		"R_LARCH_TLS_IE64_PC_LO20":                  reflect.ValueOf(elf.R_LARCH_TLS_IE64_PC_LO20),
		"R_LARCH_TLS_IE_HI20":                       reflect.ValueOf(elf.R_LARCH_TLS_IE_HI20),
		"R_LARCH_TLS_IE_LO12":                       reflect.ValueOf(elf.R_LARCH_TLS_IE_LO12),
		"R_LARCH_TLS_IE_PC_HI20":                    reflect.ValueOf(elf.R_LARCH_TLS_IE_PC_HI20),
		"R_LARCH_TLS_IE_PC_LO12":                    reflect.ValueOf(elf.R_LARCH_TLS_IE_PC_LO12),
		"R_LARCH_TLS_LD_HI20":                       reflect.ValueOf(elf.R_LARCH_TLS_LD_HI20),
		"R_LARCH_TLS_LD_PC_HI20":                    reflect.ValueOf(elf.R_LARCH_TLS_LD_PC_HI20),
		"R_LARCH_TLS_LE64_HI12":                     reflect.ValueOf(elf.R_LARCH_TLS_LE64_HI12),
		"R_LARCH_TLS_LE64_LO20":                     reflect.ValueOf(elf.R_LARCH_TLS_LE64_LO20),
		"R_LARCH_TLS_LE_HI20":                       reflect.ValueOf(elf.R_LARCH_TLS_LE_HI20),
		"R_LARCH_TLS_LE_LO12":                       reflect.ValueOf(elf.R_LARCH_TLS_LE_LO12),
		"R_LARCH_TLS_TPREL32":                       reflect.ValueOf(elf.R_LARCH_TLS_TPREL32),
		"R_LARCH_TLS_TPREL64":                       reflect.ValueOf(elf.R_LARCH_TLS_TPREL64),
		"R_MIPS_16":                                 reflect.ValueOf(elf.R_MIPS_16),
		"R_MIPS_26":                                 reflect.ValueOf(elf.R_MIPS_26),
		"R_MIPS_32":                                 reflect.ValueOf(elf.R_MIPS_32),
		"R_MIPS_64":                                 reflect.ValueOf(elf.R_MIPS_64),
		"R_MIPS_ADD_IMMEDIATE":                      reflect.ValueOf(elf.R_MIPS_ADD_IMMEDIATE),
		"R_MIPS_CALL16":                             reflect.ValueOf(elf.R_MIPS_CALL16),
		"R_MIPS_CALL_HI16":                          reflect.ValueOf(elf.R_MIPS_CALL_HI16),
		"R_MIPS_CALL_LO16":                          reflect.ValueOf(elf.R_MIPS_CALL_LO16),
		"R_MIPS_DELETE":                             reflect.ValueOf(elf.R_MIPS_DELETE),
		"R_MIPS_GOT16":                              reflect.ValueOf(elf.R_MIPS_GOT16),
		"R_MIPS_GOT_DISP":                           reflect.ValueOf(elf.R_MIPS_GOT_DISP),
		"R_MIPS_GOT_HI16":                           reflect.ValueOf(elf.R_MIPS_GOT_HI16),
		"R_MIPS_GOT_LO16":                           reflect.ValueOf(elf.R_MIPS_GOT_LO16),
		"R_MIPS_GOT_OFST":                           reflect.ValueOf(elf.R_MIPS_GOT_OFST),
		"R_MIPS_GOT_PAGE":                           reflect.ValueOf(elf.R_MIPS_GOT_PAGE),
		"R_MIPS_GPREL16":                            reflect.ValueOf(elf.R_MIPS_GPREL16),
		"R_MIPS_GPREL32":                            reflect.ValueOf(elf.R_MIPS_GPREL32),
		"R_MIPS_HI16":                               reflect.ValueOf(elf.R_MIPS_HI16),
		"R_MIPS_HIGHER":                             reflect.ValueOf(elf.R_MIPS_HIGHER),
		"R_MIPS_HIGHEST":                            reflect.ValueOf(elf.R_MIPS_HIGHEST),
		"R_MIPS_INSERT_A":                           reflect.ValueOf(elf.R_MIPS_INSERT_A),
		"R_MIPS_INSERT_B":                           reflect.ValueOf(elf.R_MIPS_INSERT_B),
		"R_MIPS_JALR":                               reflect.ValueOf(elf.R_MIPS_JALR),
		"R_MIPS_LITERAL":                            reflect.ValueOf(elf.R_MIPS_LITERAL),
		"R_MIPS_LO16":                               reflect.ValueOf(elf.R_MIPS_LO16),
		"R_MIPS_NONE":                               reflect.ValueOf(elf.R_MIPS_NONE),
		"R_MIPS_PC16":                               reflect.ValueOf(elf.R_MIPS_PC16),
		"R_MIPS_PC32":                               reflect.ValueOf(elf.R_MIPS_PC32),
		"R_MIPS_PJUMP":                              reflect.ValueOf(elf.R_MIPS_PJUMP),
		"R_MIPS_REL16":                              reflect.ValueOf(elf.R_MIPS_REL16),
		"R_MIPS_REL32":                              reflect.ValueOf(elf.R_MIPS_REL32),
		"R_MIPS_RELGOT":                             reflect.ValueOf(elf.R_MIPS_RELGOT),
		"R_MIPS_SCN_DISP":                           reflect.ValueOf(elf.R_MIPS_SCN_DISP),
		"R_MIPS_SHIFT5":                             reflect.ValueOf(elf.R_MIPS_SHIFT5),
		"R_MIPS_SHIFT6":                             reflect.ValueOf(elf.R_MIPS_SHIFT6),
		"R_MIPS_SUB":                                reflect.ValueOf(elf.R_MIPS_SUB),
		"R_MIPS_TLS_DTPMOD32":                       reflect.ValueOf(elf.R_MIPS_TLS_DTPMOD32),
		"R_MIPS_TLS_DTPMOD64":                       reflect.ValueOf(elf.R_MIPS_TLS_DTPMOD64),
		"R_MIPS_TLS_DTPREL32":                       reflect.ValueOf(elf.R_MIPS_TLS_DTPREL32),
		"R_MIPS_TLS_DTPREL64":                       reflect.ValueOf(elf.R_MIPS_TLS_DTPREL64),
		"R_MIPS_TLS_DTPREL_HI16":                    reflect.ValueOf(elf.R_MIPS_TLS_DTPREL_HI16),
		"R_MIPS_TLS_DTPREL_LO16":                    reflect.ValueOf(elf.R_MIPS_TLS_DTPREL_LO16),
		"R_MIPS_TLS_GD":                             reflect.ValueOf(elf.R_MIPS_TLS_GD),
		"R_MIPS_TLS_GOTTPREL":                       reflect.ValueOf(elf.R_MIPS_TLS_GOTTPREL),
		"R_MIPS_TLS_LDM":                            reflect.ValueOf(elf.R_MIPS_TLS_LDM),
		"R_MIPS_TLS_TPREL32":                        reflect.ValueOf(elf.R_MIPS_TLS_TPREL32),
		"R_MIPS_TLS_TPREL64":                        reflect.ValueOf(elf.R_MIPS_TLS_TPREL64),
		"R_MIPS_TLS_TPREL_HI16":                     reflect.ValueOf(elf.R_MIPS_TLS_TPREL_HI16),
		"R_MIPS_TLS_TPREL_LO16":                     reflect.ValueOf(elf.R_MIPS_TLS_TPREL_LO16),
		"R_PPC64_ADDR14":                            reflect.ValueOf(elf.R_PPC64_ADDR14),
		"R_PPC64_ADDR14_BRNTAKEN":                   reflect.ValueOf(elf.R_PPC64_ADDR14_BRNTAKEN),
		"R_PPC64_ADDR14_BRTAKEN":                    reflect.ValueOf(elf.R_PPC64_ADDR14_BRTAKEN),
		"R_PPC64_ADDR16":                            reflect.ValueOf(elf.R_PPC64_ADDR16),
		"R_PPC64_ADDR16_DS":                         reflect.ValueOf(elf.R_PPC64_ADDR16_DS),
		"R_PPC64_ADDR16_HA":                         reflect.ValueOf(elf.R_PPC64_ADDR16_HA),
		"R_PPC64_ADDR16_HI":                         reflect.ValueOf(elf.R_PPC64_ADDR16_HI),
		"R_PPC64_ADDR16_HIGH":                       reflect.ValueOf(elf.R_PPC64_ADDR16_HIGH),
		"R_PPC64_ADDR16_HIGHA":                      reflect.ValueOf(elf.R_PPC64_ADDR16_HIGHA),
		"R_PPC64_ADDR16_HIGHER":                     reflect.ValueOf(elf.R_PPC64_ADDR16_HIGHER),
		"R_PPC64_ADDR16_HIGHER34":                   reflect.ValueOf(elf.R_PPC64_ADDR16_HIGHER34),
		"R_PPC64_ADDR16_HIGHERA":                    reflect.ValueOf(elf.R_PPC64_ADDR16_HIGHERA),
		"R_PPC64_ADDR16_HIGHERA34":                  reflect.ValueOf(elf.R_PPC64_ADDR16_HIGHERA34),
		"R_PPC64_ADDR16_HIGHEST":                    reflect.ValueOf(elf.R_PPC64_ADDR16_HIGHEST),
		"R_PPC64_ADDR16_HIGHEST34":                  reflect.ValueOf(elf.R_PPC64_ADDR16_HIGHEST34),
		"R_PPC64_ADDR16_HIGHESTA":                   reflect.ValueOf(elf.R_PPC64_ADDR16_HIGHESTA),
		"R_PPC64_ADDR16_HIGHESTA34":                 reflect.ValueOf(elf.R_PPC64_ADDR16_HIGHESTA34),
		"R_PPC64_ADDR16_LO":                         reflect.ValueOf(elf.R_PPC64_ADDR16_LO),
		"R_PPC64_ADDR16_LO_DS":                      reflect.ValueOf(elf.R_PPC64_ADDR16_LO_DS),
		"R_PPC64_ADDR24":                            reflect.ValueOf(elf.R_PPC64_ADDR24),
		"R_PPC64_ADDR32":                            reflect.ValueOf(elf.R_PPC64_ADDR32),
		"R_PPC64_ADDR64":                            reflect.ValueOf(elf.R_PPC64_ADDR64),
		"R_PPC64_ADDR64_LOCAL":                      reflect.ValueOf(elf.R_PPC64_ADDR64_LOCAL),
		"R_PPC64_COPY":                              reflect.ValueOf(elf.R_PPC64_COPY),
		"R_PPC64_D28":                               reflect.ValueOf(elf.R_PPC64_D28),
		"R_PPC64_D34":                               reflect.ValueOf(elf.R_PPC64_D34),
		"R_PPC64_D34_HA30":                          reflect.ValueOf(elf.R_PPC64_D34_HA30),
		"R_PPC64_D34_HI30":                          reflect.ValueOf(elf.R_PPC64_D34_HI30),
		"R_PPC64_D34_LO":                            reflect.ValueOf(elf.R_PPC64_D34_LO),
		"R_PPC64_DTPMOD64":                          reflect.ValueOf(elf.R_PPC64_DTPMOD64),
		"R_PPC64_DTPREL16":                          reflect.ValueOf(elf.R_PPC64_DTPREL16),
		"R_PPC64_DTPREL16_DS":                       reflect.ValueOf(elf.R_PPC64_DTPREL16_DS),
		"R_PPC64_DTPREL16_HA":                       reflect.ValueOf(elf.R_PPC64_DTPREL16_HA),
		"R_PPC64_DTPREL16_HI":                       reflect.ValueOf(elf.R_PPC64_DTPREL16_HI),
		"R_PPC64_DTPREL16_HIGH":                     reflect.ValueOf(elf.R_PPC64_DTPREL16_HIGH),
		"R_PPC64_DTPREL16_HIGHA":                    reflect.ValueOf(elf.R_PPC64_DTPREL16_HIGHA),
		"R_PPC64_DTPREL16_HIGHER":                   reflect.ValueOf(elf.R_PPC64_DTPREL16_HIGHER),
		"R_PPC64_DTPREL16_HIGHERA":                  reflect.ValueOf(elf.R_PPC64_DTPREL16_HIGHERA),
		"R_PPC64_DTPREL16_HIGHEST":                  reflect.ValueOf(elf.R_PPC64_DTPREL16_HIGHEST),
		"R_PPC64_DTPREL16_HIGHESTA":                 reflect.ValueOf(elf.R_PPC64_DTPREL16_HIGHESTA),
		"R_PPC64_DTPREL16_LO":                       reflect.ValueOf(elf.R_PPC64_DTPREL16_LO),
		"R_PPC64_DTPREL16_LO_DS":                    reflect.ValueOf(elf.R_PPC64_DTPREL16_LO_DS),
		"R_PPC64_DTPREL34":                          reflect.ValueOf(elf.R_PPC64_DTPREL34),
		"R_PPC64_DTPREL64":                          reflect.ValueOf(elf.R_PPC64_DTPREL64),
		"R_PPC64_ENTRY":                             reflect.ValueOf(elf.R_PPC64_ENTRY),
		"R_PPC64_GLOB_DAT":                          reflect.ValueOf(elf.R_PPC64_GLOB_DAT),
		"R_PPC64_GNU_VTENTRY":                       reflect.ValueOf(elf.R_PPC64_GNU_VTENTRY),
		"R_PPC64_GNU_VTINHERIT":                     reflect.ValueOf(elf.R_PPC64_GNU_VTINHERIT),
		"R_PPC64_GOT16":                             reflect.ValueOf(elf.R_PPC64_GOT16),
		"R_PPC64_GOT16_DS":                          reflect.ValueOf(elf.R_PPC64_GOT16_DS),
		"R_PPC64_GOT16_HA":                          reflect.ValueOf(elf.R_PPC64_GOT16_HA),
		"R_PPC64_GOT16_HI":                          reflect.ValueOf(elf.R_PPC64_GOT16_HI),
		"R_PPC64_GOT16_LO":                          reflect.ValueOf(elf.R_PPC64_GOT16_LO),
		"R_PPC64_GOT16_LO_DS":                       reflect.ValueOf(elf.R_PPC64_GOT16_LO_DS),
		"R_PPC64_GOT_DTPREL16_DS":                   reflect.ValueOf(elf.R_PPC64_GOT_DTPREL16_DS),
		"R_PPC64_GOT_DTPREL16_HA":                   reflect.ValueOf(elf.R_PPC64_GOT_DTPREL16_HA),
		"R_PPC64_GOT_DTPREL16_HI":                   reflect.ValueOf(elf.R_PPC64_GOT_DTPREL16_HI),
		"R_PPC64_GOT_DTPREL16_LO_DS":                reflect.ValueOf(elf.R_PPC64_GOT_DTPREL16_LO_DS),
		"R_PPC64_GOT_DTPREL_PCREL34":                reflect.ValueOf(elf.R_PPC64_GOT_DTPREL_PCREL34),
		"R_PPC64_GOT_PCREL34":                       reflect.ValueOf(elf.R_PPC64_GOT_PCREL34),
		"R_PPC64_GOT_TLSGD16":                       reflect.ValueOf(elf.R_PPC64_GOT_TLSGD16),
		"R_PPC64_GOT_TLSGD16_HA":                    reflect.ValueOf(elf.R_PPC64_GOT_TLSGD16_HA),
		"R_PPC64_GOT_TLSGD16_HI":                    reflect.ValueOf(elf.R_PPC64_GOT_TLSGD16_HI),
		"R_PPC64_GOT_TLSGD16_LO":                    reflect.ValueOf(elf.R_PPC64_GOT_TLSGD16_LO),
		"R_PPC64_GOT_TLSGD_PCREL34":                 reflect.ValueOf(elf.R_PPC64_GOT_TLSGD_PCREL34),
		"R_PPC64_GOT_TLSLD16":                       reflect.ValueOf(elf.R_PPC64_GOT_TLSLD16),
		"R_PPC64_GOT_TLSLD16_HA":                    reflect.ValueOf(elf.R_PPC64_GOT_TLSLD16_HA),
		"R_PPC64_GOT_TLSLD16_HI":                    reflect.ValueOf(elf.R_PPC64_GOT_TLSLD16_HI),
		"R_PPC64_GOT_TLSLD16_LO":                    reflect.ValueOf(elf.R_PPC64_GOT_TLSLD16_LO),
		"R_PPC64_GOT_TLSLD_PCREL34":                 reflect.ValueOf(elf.R_PPC64_GOT_TLSLD_PCREL34),
		"R_PPC64_GOT_TPREL16_DS":                    reflect.ValueOf(elf.R_PPC64_GOT_TPREL16_DS),
		"R_PPC64_GOT_TPREL16_HA":                    reflect.ValueOf(elf.R_PPC64_GOT_TPREL16_HA),
		"R_PPC64_GOT_TPREL16_HI":                    reflect.ValueOf(elf.R_PPC64_GOT_TPREL16_HI),
		"R_PPC64_GOT_TPREL16_LO_DS":                 reflect.ValueOf(elf.R_PPC64_GOT_TPREL16_LO_DS),
		"R_PPC64_GOT_TPREL_PCREL34":                 reflect.ValueOf(elf.R_PPC64_GOT_TPREL_PCREL34),
		"R_PPC64_IRELATIVE":                         reflect.ValueOf(elf.R_PPC64_IRELATIVE),
		"R_PPC64_JMP_IREL":                          reflect.ValueOf(elf.R_PPC64_JMP_IREL),
		"R_PPC64_JMP_SLOT":                          reflect.ValueOf(elf.R_PPC64_JMP_SLOT),
		"R_PPC64_NONE":                              reflect.ValueOf(elf.R_PPC64_NONE),
		"R_PPC64_PCREL28":                           reflect.ValueOf(elf.R_PPC64_PCREL28),
		"R_PPC64_PCREL34":                           reflect.ValueOf(elf.R_PPC64_PCREL34),
		"R_PPC64_PCREL_OPT":                         reflect.ValueOf(elf.R_PPC64_PCREL_OPT),
		"R_PPC64_PLT16_HA":                          reflect.ValueOf(elf.R_PPC64_PLT16_HA),
		"R_PPC64_PLT16_HI":                          reflect.ValueOf(elf.R_PPC64_PLT16_HI),
		"R_PPC64_PLT16_LO":                          reflect.ValueOf(elf.R_PPC64_PLT16_LO),
		"R_PPC64_PLT16_LO_DS":                       reflect.ValueOf(elf.R_PPC64_PLT16_LO_DS),
		"R_PPC64_PLT32":                             reflect.ValueOf(elf.R_PPC64_PLT32),
		"R_PPC64_PLT64":                             reflect.ValueOf(elf.R_PPC64_PLT64),
		"R_PPC64_PLTCALL":                           reflect.ValueOf(elf.R_PPC64_PLTCALL),
		"R_PPC64_PLTCALL_NOTOC":                     reflect.ValueOf(elf.R_PPC64_PLTCALL_NOTOC),
		"R_PPC64_PLTGOT16":                          reflect.ValueOf(elf.R_PPC64_PLTGOT16),
		"R_PPC64_PLTGOT16_DS":                       reflect.ValueOf(elf.R_PPC64_PLTGOT16_DS),
		"R_PPC64_PLTGOT16_HA":                       reflect.ValueOf(elf.R_PPC64_PLTGOT16_HA),
		"R_PPC64_PLTGOT16_HI":                       reflect.ValueOf(elf.R_PPC64_PLTGOT16_HI),
		"R_PPC64_PLTGOT16_LO":                       reflect.ValueOf(elf.R_PPC64_PLTGOT16_LO),
		"R_PPC64_PLTGOT_LO_DS":                      reflect.ValueOf(elf.R_PPC64_PLTGOT_LO_DS),
		"R_PPC64_PLTREL32":                          reflect.ValueOf(elf.R_PPC64_PLTREL32),
		"R_PPC64_PLTREL64":                          reflect.ValueOf(elf.R_PPC64_PLTREL64),
		"R_PPC64_PLTSEQ":                            reflect.ValueOf(elf.R_PPC64_PLTSEQ),
		"R_PPC64_PLTSEQ_NOTOC":                      reflect.ValueOf(elf.R_PPC64_PLTSEQ_NOTOC),
		"R_PPC64_PLT_PCREL34":                       reflect.ValueOf(elf.R_PPC64_PLT_PCREL34),
		"R_PPC64_PLT_PCREL34_NOTOC":                 reflect.ValueOf(elf.R_PPC64_PLT_PCREL34_NOTOC),
		"R_PPC64_REL14":                             reflect.ValueOf(elf.R_PPC64_REL14),
		"R_PPC64_REL14_BRNTAKEN":                    reflect.ValueOf(elf.R_PPC64_REL14_BRNTAKEN),
		"R_PPC64_REL14_BRTAKEN":                     reflect.ValueOf(elf.R_PPC64_REL14_BRTAKEN),
		"R_PPC64_REL16":                             reflect.ValueOf(elf.R_PPC64_REL16),
		"R_PPC64_REL16DX_HA":                        reflect.ValueOf(elf.R_PPC64_REL16DX_HA),
		"R_PPC64_REL16_HA":                          reflect.ValueOf(elf.R_PPC64_REL16_HA),
		"R_PPC64_REL16_HI":                          reflect.ValueOf(elf.R_PPC64_REL16_HI),
		"R_PPC64_REL16_HIGH":                        reflect.ValueOf(elf.R_PPC64_REL16_HIGH),
		"R_PPC64_REL16_HIGHA":                       reflect.ValueOf(elf.R_PPC64_REL16_HIGHA),
		"R_PPC64_REL16_HIGHER":                      reflect.ValueOf(elf.R_PPC64_REL16_HIGHER),
		"R_PPC64_REL16_HIGHER34":                    reflect.ValueOf(elf.R_PPC64_REL16_HIGHER34),
		"R_PPC64_REL16_HIGHERA":                     reflect.ValueOf(elf.R_PPC64_REL16_HIGHERA),
		"R_PPC64_REL16_HIGHERA34":                   reflect.ValueOf(elf.R_PPC64_REL16_HIGHERA34),
		"R_PPC64_REL16_HIGHEST":                     reflect.ValueOf(elf.R_PPC64_REL16_HIGHEST),
		"R_PPC64_REL16_HIGHEST34":                   reflect.ValueOf(elf.R_PPC64_REL16_HIGHEST34),
		"R_PPC64_REL16_HIGHESTA":                    reflect.ValueOf(elf.R_PPC64_REL16_HIGHESTA),
		"R_PPC64_REL16_HIGHESTA34":                  reflect.ValueOf(elf.R_PPC64_REL16_HIGHESTA34),
		"R_PPC64_REL16_LO":                          reflect.ValueOf(elf.R_PPC64_REL16_LO),
		"R_PPC64_REL24":                             reflect.ValueOf(elf.R_PPC64_REL24),
		"R_PPC64_REL24_NOTOC":                       reflect.ValueOf(elf.R_PPC64_REL24_NOTOC),
		"R_PPC64_REL24_P9NOTOC":                     reflect.ValueOf(elf.R_PPC64_REL24_P9NOTOC),
		"R_PPC64_REL30":                             reflect.ValueOf(elf.R_PPC64_REL30),
		"R_PPC64_REL32":                             reflect.ValueOf(elf.R_PPC64_REL32),
		"R_PPC64_REL64":                             reflect.ValueOf(elf.R_PPC64_REL64),
		"R_PPC64_RELATIVE":                          reflect.ValueOf(elf.R_PPC64_RELATIVE),
		"R_PPC64_SECTOFF":                           reflect.ValueOf(elf.R_PPC64_SECTOFF),
		"R_PPC64_SECTOFF_DS":                        reflect.ValueOf(elf.R_PPC64_SECTOFF_DS),
		"R_PPC64_SECTOFF_HA":                        reflect.ValueOf(elf.R_PPC64_SECTOFF_HA),
		"R_PPC64_SECTOFF_HI":                        reflect.ValueOf(elf.R_PPC64_SECTOFF_HI),
		"R_PPC64_SECTOFF_LO":                        reflect.ValueOf(elf.R_PPC64_SECTOFF_LO),
		"R_PPC64_SECTOFF_LO_DS":                     reflect.ValueOf(elf.R_PPC64_SECTOFF_LO_DS),
		"R_PPC64_TLS":                               reflect.ValueOf(elf.R_PPC64_TLS),
		"R_PPC64_TLSGD":                             reflect.ValueOf(elf.R_PPC64_TLSGD),
		"R_PPC64_TLSLD":                             reflect.ValueOf(elf.R_PPC64_TLSLD),
		"R_PPC64_TOC":                               reflect.ValueOf(elf.R_PPC64_TOC),
		"R_PPC64_TOC16":                             reflect.ValueOf(elf.R_PPC64_TOC16),
		"R_PPC64_TOC16_DS":                          reflect.ValueOf(elf.R_PPC64_TOC16_DS),
		"R_PPC64_TOC16_HA":                          reflect.ValueOf(elf.R_PPC64_TOC16_HA),
		"R_PPC64_TOC16_HI":                          reflect.ValueOf(elf.R_PPC64_TOC16_HI),
		"R_PPC64_TOC16_LO":                          reflect.ValueOf(elf.R_PPC64_TOC16_LO),
		"R_PPC64_TOC16_LO_DS":                       reflect.ValueOf(elf.R_PPC64_TOC16_LO_DS),
		"R_PPC64_TOCSAVE":                           reflect.ValueOf(elf.R_PPC64_TOCSAVE),
		"R_PPC64_TPREL16":                           reflect.ValueOf(elf.R_PPC64_TPREL16),
		"R_PPC64_TPREL16_DS":                        reflect.ValueOf(elf.R_PPC64_TPREL16_DS),
		"R_PPC64_TPREL16_HA":                        reflect.ValueOf(elf.R_PPC64_TPREL16_HA),
		"R_PPC64_TPREL16_HI":                        reflect.ValueOf(elf.R_PPC64_TPREL16_HI),
		"R_PPC64_TPREL16_HIGH":                      reflect.ValueOf(elf.R_PPC64_TPREL16_HIGH),
		"R_PPC64_TPREL16_HIGHA":                     reflect.ValueOf(elf.R_PPC64_TPREL16_HIGHA),
		"R_PPC64_TPREL16_HIGHER":                    reflect.ValueOf(elf.R_PPC64_TPREL16_HIGHER),
		"R_PPC64_TPREL16_HIGHERA":                   reflect.ValueOf(elf.R_PPC64_TPREL16_HIGHERA),
		"R_PPC64_TPREL16_HIGHEST":                   reflect.ValueOf(elf.R_PPC64_TPREL16_HIGHEST),
		"R_PPC64_TPREL16_HIGHESTA":                  reflect.ValueOf(elf.R_PPC64_TPREL16_HIGHESTA),
		"R_PPC64_TPREL16_LO":                        reflect.ValueOf(elf.R_PPC64_TPREL16_LO),
		"R_PPC64_TPREL16_LO_DS":                     reflect.ValueOf(elf.R_PPC64_TPREL16_LO_DS),
		"R_PPC64_TPREL34":                           reflect.ValueOf(elf.R_PPC64_TPREL34),
		"R_PPC64_TPREL64":                           reflect.ValueOf(elf.R_PPC64_TPREL64),
		"R_PPC64_UADDR16":                           reflect.ValueOf(elf.R_PPC64_UADDR16),
		"R_PPC64_UADDR32":                           reflect.ValueOf(elf.R_PPC64_UADDR32),
		"R_PPC64_UADDR64":                           reflect.ValueOf(elf.R_PPC64_UADDR64),
		"R_PPC_ADDR14":                              reflect.ValueOf(elf.R_PPC_ADDR14),
		"R_PPC_ADDR14_BRNTAKEN":                     reflect.ValueOf(elf.R_PPC_ADDR14_BRNTAKEN),
		"R_PPC_ADDR14_BRTAKEN":                      reflect.ValueOf(elf.R_PPC_ADDR14_BRTAKEN),
		"R_PPC_ADDR16":                              reflect.ValueOf(elf.R_PPC_ADDR16),
		"R_PPC_ADDR16_HA":                           reflect.ValueOf(elf.R_PPC_ADDR16_HA),
		"R_PPC_ADDR16_HI":                           reflect.ValueOf(elf.R_PPC_ADDR16_HI),
		"R_PPC_ADDR16_LO":                           reflect.ValueOf(elf.R_PPC_ADDR16_LO),
		"R_PPC_ADDR24":                              reflect.ValueOf(elf.R_PPC_ADDR24),
		"R_PPC_ADDR32":                              reflect.ValueOf(elf.R_PPC_ADDR32),
		"R_PPC_COPY":                                reflect.ValueOf(elf.R_PPC_COPY),
		"R_PPC_DTPMOD32":                            reflect.ValueOf(elf.R_PPC_DTPMOD32),
		"R_PPC_DTPREL16":                            reflect.ValueOf(elf.R_PPC_DTPREL16),
		"R_PPC_DTPREL16_HA":                         reflect.ValueOf(elf.R_PPC_DTPREL16_HA),
		"R_PPC_DTPREL16_HI":                         reflect.ValueOf(elf.R_PPC_DTPREL16_HI),
		"R_PPC_DTPREL16_LO":                         reflect.ValueOf(elf.R_PPC_DTPREL16_LO),
		"R_PPC_DTPREL32":                            reflect.ValueOf(elf.R_PPC_DTPREL32),
		"R_PPC_EMB_BIT_FLD":                         reflect.ValueOf(elf.R_PPC_EMB_BIT_FLD),
		"R_PPC_EMB_MRKREF":                          reflect.ValueOf(elf.R_PPC_EMB_MRKREF),
		"R_PPC_EMB_NADDR16":                         reflect.ValueOf(elf.R_PPC_EMB_NADDR16),
		"R_PPC_EMB_NADDR16_HA":                      reflect.ValueOf(elf.R_PPC_EMB_NADDR16_HA),
		"R_PPC_EMB_NADDR16_HI":                      reflect.ValueOf(elf.R_PPC_EMB_NADDR16_HI),
		"R_PPC_EMB_NADDR16_LO":                      reflect.ValueOf(elf.R_PPC_EMB_NADDR16_LO),
		"R_PPC_EMB_NADDR32":                         reflect.ValueOf(elf.R_PPC_EMB_NADDR32),
		"R_PPC_EMB_RELSDA":                          reflect.ValueOf(elf.R_PPC_EMB_RELSDA),
		"R_PPC_EMB_RELSEC16":                        reflect.ValueOf(elf.R_PPC_EMB_RELSEC16),
		"R_PPC_EMB_RELST_HA":                        reflect.ValueOf(elf.R_PPC_EMB_RELST_HA),
		"R_PPC_EMB_RELST_HI":                        reflect.ValueOf(elf.R_PPC_EMB_RELST_HI),
		"R_PPC_EMB_RELST_LO":                        reflect.ValueOf(elf.R_PPC_EMB_RELST_LO),
		"R_PPC_EMB_SDA21":                           reflect.ValueOf(elf.R_PPC_EMB_SDA21),
		"R_PPC_EMB_SDA2I16":                         reflect.ValueOf(elf.R_PPC_EMB_SDA2I16),
		"R_PPC_EMB_SDA2REL":                         reflect.ValueOf(elf.R_PPC_EMB_SDA2REL),
		"R_PPC_EMB_SDAI16":                          reflect.ValueOf(elf.R_PPC_EMB_SDAI16),
		"R_PPC_GLOB_DAT":                            reflect.ValueOf(elf.R_PPC_GLOB_DAT),
		"R_PPC_GOT16":                               reflect.ValueOf(elf.R_PPC_GOT16),
		"R_PPC_GOT16_HA":                            reflect.ValueOf(elf.R_PPC_GOT16_HA),
		"R_PPC_GOT16_HI":                            reflect.ValueOf(elf.R_PPC_GOT16_HI),
		"R_PPC_GOT16_LO":                            reflect.ValueOf(elf.R_PPC_GOT16_LO),
		"R_PPC_GOT_TLSGD16":                         reflect.ValueOf(elf.R_PPC_GOT_TLSGD16),
		"R_PPC_GOT_TLSGD16_HA":                      reflect.ValueOf(elf.R_PPC_GOT_TLSGD16_HA),
		"R_PPC_GOT_TLSGD16_HI":                      reflect.ValueOf(elf.R_PPC_GOT_TLSGD16_HI),
		"R_PPC_GOT_TLSGD16_LO":                      reflect.ValueOf(elf.R_PPC_GOT_TLSGD16_LO),
		"R_PPC_GOT_TLSLD16":                         reflect.ValueOf(elf.R_PPC_GOT_TLSLD16),
		"R_PPC_GOT_TLSLD16_HA":                      reflect.ValueOf(elf.R_PPC_GOT_TLSLD16_HA),
		"R_PPC_GOT_TLSLD16_HI":                      reflect.ValueOf(elf.R_PPC_GOT_TLSLD16_HI),
		"R_PPC_GOT_TLSLD16_LO":                      reflect.ValueOf(elf.R_PPC_GOT_TLSLD16_LO),
		"R_PPC_GOT_TPREL16":                         reflect.ValueOf(elf.R_PPC_GOT_TPREL16),
		"R_PPC_GOT_TPREL16_HA":                      reflect.ValueOf(elf.R_PPC_GOT_TPREL16_HA),
		"R_PPC_GOT_TPREL16_HI":                      reflect.ValueOf(elf.R_PPC_GOT_TPREL16_HI),
		"R_PPC_GOT_TPREL16_LO":                      reflect.ValueOf(elf.R_PPC_GOT_TPREL16_LO),
		"R_PPC_JMP_SLOT":                            reflect.ValueOf(elf.R_PPC_JMP_SLOT),
		"R_PPC_LOCAL24PC":                           reflect.ValueOf(elf.R_PPC_LOCAL24PC),
		"R_PPC_NONE":                                reflect.ValueOf(elf.R_PPC_NONE),
		"R_PPC_PLT16_HA":                            reflect.ValueOf(elf.R_PPC_PLT16_HA),
		"R_PPC_PLT16_HI":                            reflect.ValueOf(elf.R_PPC_PLT16_HI),
		"R_PPC_PLT16_LO":                            reflect.ValueOf(elf.R_PPC_PLT16_LO),
		"R_PPC_PLT32":                               reflect.ValueOf(elf.R_PPC_PLT32),
		"R_PPC_PLTREL24":                            reflect.ValueOf(elf.R_PPC_PLTREL24),
		"R_PPC_PLTREL32":                            reflect.ValueOf(elf.R_PPC_PLTREL32),
		"R_PPC_REL14":                               reflect.ValueOf(elf.R_PPC_REL14),
		"R_PPC_REL14_BRNTAKEN":                      reflect.ValueOf(elf.R_PPC_REL14_BRNTAKEN),
		"R_PPC_REL14_BRTAKEN":                       reflect.ValueOf(elf.R_PPC_REL14_BRTAKEN),
		"R_PPC_REL24":                               reflect.ValueOf(elf.R_PPC_REL24),
		"R_PPC_REL32":                               reflect.ValueOf(elf.R_PPC_REL32),
		"R_PPC_RELATIVE":                            reflect.ValueOf(elf.R_PPC_RELATIVE),
		"R_PPC_SDAREL16":                            reflect.ValueOf(elf.R_PPC_SDAREL16),
		"R_PPC_SECTOFF":                             reflect.ValueOf(elf.R_PPC_SECTOFF),
		"R_PPC_SECTOFF_HA":                          reflect.ValueOf(elf.R_PPC_SECTOFF_HA),
		"R_PPC_SECTOFF_HI":                          reflect.ValueOf(elf.R_PPC_SECTOFF_HI),
		"R_PPC_SECTOFF_LO":                          reflect.ValueOf(elf.R_PPC_SECTOFF_LO),
		"R_PPC_TLS":                                 reflect.ValueOf(elf.R_PPC_TLS),
		"R_PPC_TPREL16":                             reflect.ValueOf(elf.R_PPC_TPREL16),
		"R_PPC_TPREL16_HA":                          reflect.ValueOf(elf.R_PPC_TPREL16_HA),
		"R_PPC_TPREL16_HI":                          reflect.ValueOf(elf.R_PPC_TPREL16_HI),
		"R_PPC_TPREL16_LO":                          reflect.ValueOf(elf.R_PPC_TPREL16_LO),
		"R_PPC_TPREL32":                             reflect.ValueOf(elf.R_PPC_TPREL32),
		"R_PPC_UADDR16":                             reflect.ValueOf(elf.R_PPC_UADDR16),
		"R_PPC_UADDR32":                             reflect.ValueOf(elf.R_PPC_UADDR32),
		"R_RISCV_32":                                reflect.ValueOf(elf.R_RISCV_32),
		"R_RISCV_32_PCREL":                          reflect.ValueOf(elf.R_RISCV_32_PCREL),
		"R_RISCV_64":                                reflect.ValueOf(elf.R_RISCV_64),
		"R_RISCV_ADD16":                             reflect.ValueOf(elf.R_RISCV_ADD16),
		"R_RISCV_ADD32":                             reflect.ValueOf(elf.R_RISCV_ADD32),
		"R_RISCV_ADD64":                             reflect.ValueOf(elf.R_RISCV_ADD64),
		"R_RISCV_ADD8":                              reflect.ValueOf(elf.R_RISCV_ADD8),
		"R_RISCV_ALIGN":                             reflect.ValueOf(elf.R_RISCV_ALIGN),
		"R_RISCV_BRANCH":                            reflect.ValueOf(elf.R_RISCV_BRANCH),
		"R_RISCV_CALL":                              reflect.ValueOf(elf.R_RISCV_CALL),
		"R_RISCV_CALL_PLT":                          reflect.ValueOf(elf.R_RISCV_CALL_PLT),
		"R_RISCV_COPY":                              reflect.ValueOf(elf.R_RISCV_COPY),
		"R_RISCV_GNU_VTENTRY":                       reflect.ValueOf(elf.R_RISCV_GNU_VTENTRY),
		"R_RISCV_GNU_VTINHERIT":                     reflect.ValueOf(elf.R_RISCV_GNU_VTINHERIT),
		"R_RISCV_GOT_HI20":                          reflect.ValueOf(elf.R_RISCV_GOT_HI20),
		"R_RISCV_GPREL_I":                           reflect.ValueOf(elf.R_RISCV_GPREL_I),
		"R_RISCV_GPREL_S":                           reflect.ValueOf(elf.R_RISCV_GPREL_S),
		"R_RISCV_HI20":                              reflect.ValueOf(elf.R_RISCV_HI20),
		"R_RISCV_JAL":                               reflect.ValueOf(elf.R_RISCV_JAL),
		"R_RISCV_JUMP_SLOT":                         reflect.ValueOf(elf.R_RISCV_JUMP_SLOT),
		"R_RISCV_LO12_I":                            reflect.ValueOf(elf.R_RISCV_LO12_I),
		"R_RISCV_LO12_S":                            reflect.ValueOf(elf.R_RISCV_LO12_S),
		"R_RISCV_NONE":                              reflect.ValueOf(elf.R_RISCV_NONE),
		"R_RISCV_PCREL_HI20":                        reflect.ValueOf(elf.R_RISCV_PCREL_HI20),
		"R_RISCV_PCREL_LO12_I":                      reflect.ValueOf(elf.R_RISCV_PCREL_LO12_I),
		"R_RISCV_PCREL_LO12_S":                      reflect.ValueOf(elf.R_RISCV_PCREL_LO12_S),
		"R_RISCV_RELATIVE":                          reflect.ValueOf(elf.R_RISCV_RELATIVE),
		"R_RISCV_RELAX":                             reflect.ValueOf(elf.R_RISCV_RELAX),
		"R_RISCV_RVC_BRANCH":                        reflect.ValueOf(elf.R_RISCV_RVC_BRANCH),
		"R_RISCV_RVC_JUMP":                          reflect.ValueOf(elf.R_RISCV_RVC_JUMP),
		"R_RISCV_RVC_LUI":                           reflect.ValueOf(elf.R_RISCV_RVC_LUI),
		"R_RISCV_SET16":                             reflect.ValueOf(elf.R_RISCV_SET16),
		"R_RISCV_SET32":                             reflect.ValueOf(elf.R_RISCV_SET32),
		"R_RISCV_SET6":                              reflect.ValueOf(elf.R_RISCV_SET6),
		"R_RISCV_SET8":                              reflect.ValueOf(elf.R_RISCV_SET8),
		"R_RISCV_SUB16":                             reflect.ValueOf(elf.R_RISCV_SUB16),
		"R_RISCV_SUB32":                             reflect.ValueOf(elf.R_RISCV_SUB32),
		"R_RISCV_SUB6":                              reflect.ValueOf(elf.R_RISCV_SUB6),
		"R_RISCV_SUB64":                             reflect.ValueOf(elf.R_RISCV_SUB64),
		"R_RISCV_SUB8":                              reflect.ValueOf(elf.R_RISCV_SUB8),
		"R_RISCV_TLS_DTPMOD32":                      reflect.ValueOf(elf.R_RISCV_TLS_DTPMOD32),
		"R_RISCV_TLS_DTPMOD64":                      reflect.ValueOf(elf.R_RISCV_TLS_DTPMOD64),
		"R_RISCV_TLS_DTPREL32":                      reflect.ValueOf(elf.R_RISCV_TLS_DTPREL32),
		"R_RISCV_TLS_DTPREL64":                      reflect.ValueOf(elf.R_RISCV_TLS_DTPREL64),
		"R_RISCV_TLS_GD_HI20":                       reflect.ValueOf(elf.R_RISCV_TLS_GD_HI20),
		"R_RISCV_TLS_GOT_HI20":                      reflect.ValueOf(elf.R_RISCV_TLS_GOT_HI20),
		"R_RISCV_TLS_TPREL32":                       reflect.ValueOf(elf.R_RISCV_TLS_TPREL32),
		"R_RISCV_TLS_TPREL64":                       reflect.ValueOf(elf.R_RISCV_TLS_TPREL64),
		"R_RISCV_TPREL_ADD":                         reflect.ValueOf(elf.R_RISCV_TPREL_ADD),
		"R_RISCV_TPREL_HI20":                        reflect.ValueOf(elf.R_RISCV_TPREL_HI20),
		"R_RISCV_TPREL_I":                           reflect.ValueOf(elf.R_RISCV_TPREL_I),
		"R_RISCV_TPREL_LO12_I":                      reflect.ValueOf(elf.R_RISCV_TPREL_LO12_I),
		"R_RISCV_TPREL_LO12_S":                      reflect.ValueOf(elf.R_RISCV_TPREL_LO12_S),
		"R_RISCV_TPREL_S":                           reflect.ValueOf(elf.R_RISCV_TPREL_S),
		"R_SPARC_10":                                reflect.ValueOf(elf.R_SPARC_10),
		"R_SPARC_11":                                reflect.ValueOf(elf.R_SPARC_11),
		"R_SPARC_13":                                reflect.ValueOf(elf.R_SPARC_13),
		"R_SPARC_16":                                reflect.ValueOf(elf.R_SPARC_16),
		"R_SPARC_22":                                reflect.ValueOf(elf.R_SPARC_22),
		"R_SPARC_32":                                reflect.ValueOf(elf.R_SPARC_32),
		"R_SPARC_5":                                 reflect.ValueOf(elf.R_SPARC_5),
		"R_SPARC_6":                                 reflect.ValueOf(elf.R_SPARC_6),
		"R_SPARC_64":                                reflect.ValueOf(elf.R_SPARC_64),
		"R_SPARC_7":                                 reflect.ValueOf(elf.R_SPARC_7),
		"R_SPARC_8":                                 reflect.ValueOf(elf.R_SPARC_8),
		"R_SPARC_COPY":                              reflect.ValueOf(elf.R_SPARC_COPY),
		"R_SPARC_DISP16":                            reflect.ValueOf(elf.R_SPARC_DISP16),
		"R_SPARC_DISP32":                            reflect.ValueOf(elf.R_SPARC_DISP32),
		"R_SPARC_DISP64":                            reflect.ValueOf(elf.R_SPARC_DISP64),
		"R_SPARC_DISP8":                             reflect.ValueOf(elf.R_SPARC_DISP8),
		"R_SPARC_GLOB_DAT":                          reflect.ValueOf(elf.R_SPARC_GLOB_DAT),
		"R_SPARC_GLOB_JMP":                          reflect.ValueOf(elf.R_SPARC_GLOB_JMP),
		"R_SPARC_GOT10":                             reflect.ValueOf(elf.R_SPARC_GOT10),
		"R_SPARC_GOT13":                             reflect.ValueOf(elf.R_SPARC_GOT13),
		"R_SPARC_GOT22":                             reflect.ValueOf(elf.R_SPARC_GOT22),
		"R_SPARC_H44":                               reflect.ValueOf(elf.R_SPARC_H44),
		"R_SPARC_HH22":                              reflect.ValueOf(elf.R_SPARC_HH22),
		"R_SPARC_HI22":                              reflect.ValueOf(elf.R_SPARC_HI22),
		"R_SPARC_HIPLT22":                           reflect.ValueOf(elf.R_SPARC_HIPLT22),
		"R_SPARC_HIX22":                             reflect.ValueOf(elf.R_SPARC_HIX22),
		"R_SPARC_HM10":                              reflect.ValueOf(elf.R_SPARC_HM10),
		"R_SPARC_JMP_SLOT":                          reflect.ValueOf(elf.R_SPARC_JMP_SLOT),
		"R_SPARC_L44":                               reflect.ValueOf(elf.R_SPARC_L44),
		"R_SPARC_LM22":                              reflect.ValueOf(elf.R_SPARC_LM22),
		"R_SPARC_LO10":                              reflect.ValueOf(elf.R_SPARC_LO10),
		"R_SPARC_LOPLT10":                           reflect.ValueOf(elf.R_SPARC_LOPLT10),
		"R_SPARC_LOX10":                             reflect.ValueOf(elf.R_SPARC_LOX10),
		"R_SPARC_M44":                               reflect.ValueOf(elf.R_SPARC_M44),
		"R_SPARC_NONE":                              reflect.ValueOf(elf.R_SPARC_NONE),
		"R_SPARC_OLO10":                             reflect.ValueOf(elf.R_SPARC_OLO10),
		"R_SPARC_PC10":                              reflect.ValueOf(elf.R_SPARC_PC10),
		"R_SPARC_PC22":                              reflect.ValueOf(elf.R_SPARC_PC22),
		"R_SPARC_PCPLT10":                           reflect.ValueOf(elf.R_SPARC_PCPLT10),
		"R_SPARC_PCPLT22":                           reflect.ValueOf(elf.R_SPARC_PCPLT22),
		"R_SPARC_PCPLT32":                           reflect.ValueOf(elf.R_SPARC_PCPLT32),
		"R_SPARC_PC_HH22":                           reflect.ValueOf(elf.R_SPARC_PC_HH22),
		"R_SPARC_PC_HM10":                           reflect.ValueOf(elf.R_SPARC_PC_HM10),
		"R_SPARC_PC_LM22":                           reflect.ValueOf(elf.R_SPARC_PC_LM22),
		"R_SPARC_PLT32":                             reflect.ValueOf(elf.R_SPARC_PLT32),
		"R_SPARC_PLT64":                             reflect.ValueOf(elf.R_SPARC_PLT64),
		"R_SPARC_REGISTER":                          reflect.ValueOf(elf.R_SPARC_REGISTER),
		"R_SPARC_RELATIVE":                          reflect.ValueOf(elf.R_SPARC_RELATIVE),
		"R_SPARC_UA16":                              reflect.ValueOf(elf.R_SPARC_UA16),
		"R_SPARC_UA32":                              reflect.ValueOf(elf.R_SPARC_UA32),
		"R_SPARC_UA64":                              reflect.ValueOf(elf.R_SPARC_UA64),
		"R_SPARC_WDISP16":                           reflect.ValueOf(elf.R_SPARC_WDISP16),
		"R_SPARC_WDISP19":                           reflect.ValueOf(elf.R_SPARC_WDISP19),
		"R_SPARC_WDISP22":                           reflect.ValueOf(elf.R_SPARC_WDISP22),
		"R_SPARC_WDISP30":                           reflect.ValueOf(elf.R_SPARC_WDISP30),
		"R_SPARC_WPLT30":                            reflect.ValueOf(elf.R_SPARC_WPLT30),
		"R_SYM32":                                   reflect.ValueOf(elf.R_SYM32),
		"R_SYM64":                                   reflect.ValueOf(elf.R_SYM64),
		"R_TYPE32":                                  reflect.ValueOf(elf.R_TYPE32),
		"R_TYPE64":                                  reflect.ValueOf(elf.R_TYPE64),
		"R_X86_64_16":                               reflect.ValueOf(elf.R_X86_64_16),
		"R_X86_64_32":                               reflect.ValueOf(elf.R_X86_64_32),
		"R_X86_64_32S":                              reflect.ValueOf(elf.R_X86_64_32S),
		"R_X86_64_64":                               reflect.ValueOf(elf.R_X86_64_64),
		"R_X86_64_8":                                reflect.ValueOf(elf.R_X86_64_8),
		"R_X86_64_COPY":                             reflect.ValueOf(elf.R_X86_64_COPY),
		"R_X86_64_DTPMOD64":                         reflect.ValueOf(elf.R_X86_64_DTPMOD64),
		"R_X86_64_DTPOFF32":                         reflect.ValueOf(elf.R_X86_64_DTPOFF32),
		"R_X86_64_DTPOFF64":                         reflect.ValueOf(elf.R_X86_64_DTPOFF64),
		"R_X86_64_GLOB_DAT":                         reflect.ValueOf(elf.R_X86_64_GLOB_DAT),
		"R_X86_64_GOT32":                            reflect.ValueOf(elf.R_X86_64_GOT32),
		"R_X86_64_GOT64":                            reflect.ValueOf(elf.R_X86_64_GOT64),
		"R_X86_64_GOTOFF64":                         reflect.ValueOf(elf.R_X86_64_GOTOFF64),
		"R_X86_64_GOTPC32":                          reflect.ValueOf(elf.R_X86_64_GOTPC32),
		"R_X86_64_GOTPC32_TLSDESC":                  reflect.ValueOf(elf.R_X86_64_GOTPC32_TLSDESC),
		"R_X86_64_GOTPC64":                          reflect.ValueOf(elf.R_X86_64_GOTPC64),
		"R_X86_64_GOTPCREL":                         reflect.ValueOf(elf.R_X86_64_GOTPCREL),
		"R_X86_64_GOTPCREL64":                       reflect.ValueOf(elf.R_X86_64_GOTPCREL64),
		"R_X86_64_GOTPCRELX":                        reflect.ValueOf(elf.R_X86_64_GOTPCRELX),
		"R_X86_64_GOTPLT64":                         reflect.ValueOf(elf.R_X86_64_GOTPLT64),
		"R_X86_64_GOTTPOFF":                         reflect.ValueOf(elf.R_X86_64_GOTTPOFF),
		"R_X86_64_IRELATIVE":                        reflect.ValueOf(elf.R_X86_64_IRELATIVE),
		"R_X86_64_JMP_SLOT":                         reflect.ValueOf(elf.R_X86_64_JMP_SLOT),
		"R_X86_64_NONE":                             reflect.ValueOf(elf.R_X86_64_NONE),
		"R_X86_64_PC16":                             reflect.ValueOf(elf.R_X86_64_PC16),
		"R_X86_64_PC32":                             reflect.ValueOf(elf.R_X86_64_PC32),
		"R_X86_64_PC32_BND":                         reflect.ValueOf(elf.R_X86_64_PC32_BND),
		"R_X86_64_PC64":                             reflect.ValueOf(elf.R_X86_64_PC64),
		"R_X86_64_PC8":                              reflect.ValueOf(elf.R_X86_64_PC8),
		"R_X86_64_PLT32":                            reflect.ValueOf(elf.R_X86_64_PLT32),
		"R_X86_64_PLT32_BND":                        reflect.ValueOf(elf.R_X86_64_PLT32_BND),
		"R_X86_64_PLTOFF64":                         reflect.ValueOf(elf.R_X86_64_PLTOFF64),
		"R_X86_64_RELATIVE":                         reflect.ValueOf(elf.R_X86_64_RELATIVE),
		"R_X86_64_RELATIVE64":                       reflect.ValueOf(elf.R_X86_64_RELATIVE64),
		"R_X86_64_REX_GOTPCRELX":                    reflect.ValueOf(elf.R_X86_64_REX_GOTPCRELX),
		"R_X86_64_SIZE32":                           reflect.ValueOf(elf.R_X86_64_SIZE32),
		"R_X86_64_SIZE64":                           reflect.ValueOf(elf.R_X86_64_SIZE64),
		"R_X86_64_TLSDESC":                          reflect.ValueOf(elf.R_X86_64_TLSDESC),
		"R_X86_64_TLSDESC_CALL":                     reflect.ValueOf(elf.R_X86_64_TLSDESC_CALL),
		"R_X86_64_TLSGD":                            reflect.ValueOf(elf.R_X86_64_TLSGD),
		"R_X86_64_TLSLD":                            reflect.ValueOf(elf.R_X86_64_TLSLD),
		"R_X86_64_TPOFF32":                          reflect.ValueOf(elf.R_X86_64_TPOFF32),
		"R_X86_64_TPOFF64":                          reflect.ValueOf(elf.R_X86_64_TPOFF64),
		"SHF_ALLOC":                                 reflect.ValueOf(elf.SHF_ALLOC),
		"SHF_COMPRESSED":                            reflect.ValueOf(elf.SHF_COMPRESSED),
		"SHF_EXECINSTR":                             reflect.ValueOf(elf.SHF_EXECINSTR),
		"SHF_GROUP":                                 reflect.ValueOf(elf.SHF_GROUP),
		"SHF_INFO_LINK":                             reflect.ValueOf(elf.SHF_INFO_LINK),
		"SHF_LINK_ORDER":                            reflect.ValueOf(elf.SHF_LINK_ORDER),
		"SHF_MASKOS":                                reflect.ValueOf(elf.SHF_MASKOS),
		"SHF_MASKPROC":                              reflect.ValueOf(elf.SHF_MASKPROC),
		"SHF_MERGE":                                 reflect.ValueOf(elf.SHF_MERGE),
		"SHF_OS_NONCONFORMING":                      reflect.ValueOf(elf.SHF_OS_NONCONFORMING),
		"SHF_STRINGS":                               reflect.ValueOf(elf.SHF_STRINGS),
		"SHF_TLS":                                   reflect.ValueOf(elf.SHF_TLS),
		"SHF_WRITE":                                 reflect.ValueOf(elf.SHF_WRITE),
		"SHN_ABS":                                   reflect.ValueOf(elf.SHN_ABS),
		"SHN_COMMON":                                reflect.ValueOf(elf.SHN_COMMON),
		"SHN_HIOS":                                  reflect.ValueOf(elf.SHN_HIOS),
		"SHN_HIPROC":                                reflect.ValueOf(elf.SHN_HIPROC),
		"SHN_HIRESERVE":                             reflect.ValueOf(elf.SHN_HIRESERVE),
		"SHN_LOOS":                                  reflect.ValueOf(elf.SHN_LOOS),
		"SHN_LOPROC":                                reflect.ValueOf(elf.SHN_LOPROC),
		"SHN_LORESERVE":                             reflect.ValueOf(elf.SHN_LORESERVE),
		"SHN_UNDEF":                                 reflect.ValueOf(elf.SHN_UNDEF),
		"SHN_XINDEX":                                reflect.ValueOf(elf.SHN_XINDEX),
		"SHT_DYNAMIC":                               reflect.ValueOf(elf.SHT_DYNAMIC),
		"SHT_DYNSYM":                                reflect.ValueOf(elf.SHT_DYNSYM),
		"SHT_FINI_ARRAY":                            reflect.ValueOf(elf.SHT_FINI_ARRAY),
		"SHT_GNU_ATTRIBUTES":                        reflect.ValueOf(elf.SHT_GNU_ATTRIBUTES),
		"SHT_GNU_HASH":                              reflect.ValueOf(elf.SHT_GNU_HASH),
		"SHT_GNU_LIBLIST":                           reflect.ValueOf(elf.SHT_GNU_LIBLIST),
		"SHT_GNU_VERDEF":                            reflect.ValueOf(elf.SHT_GNU_VERDEF),
		"SHT_GNU_VERNEED":                           reflect.ValueOf(elf.SHT_GNU_VERNEED),
		"SHT_GNU_VERSYM":                            reflect.ValueOf(elf.SHT_GNU_VERSYM),
		"SHT_GROUP":                                 reflect.ValueOf(elf.SHT_GROUP),
		"SHT_HASH":                                  reflect.ValueOf(elf.SHT_HASH),
		"SHT_HIOS":                                  reflect.ValueOf(elf.SHT_HIOS),
		"SHT_HIPROC":                                reflect.ValueOf(elf.SHT_HIPROC),
		"SHT_HIUSER":                                reflect.ValueOf(elf.SHT_HIUSER),
		"SHT_INIT_ARRAY":                            reflect.ValueOf(elf.SHT_INIT_ARRAY),
		"SHT_LOOS":                                  reflect.ValueOf(elf.SHT_LOOS),
		"SHT_LOPROC":                                reflect.ValueOf(elf.SHT_LOPROC),
		"SHT_LOUSER":                                reflect.ValueOf(elf.SHT_LOUSER),
		"SHT_MIPS_ABIFLAGS":                         reflect.ValueOf(elf.SHT_MIPS_ABIFLAGS),
		"SHT_NOBITS":                                reflect.ValueOf(elf.SHT_NOBITS),
		"SHT_NOTE":                                  reflect.ValueOf(elf.SHT_NOTE),
		"SHT_NULL":                                  reflect.ValueOf(elf.SHT_NULL),
		"SHT_PREINIT_ARRAY":                         reflect.ValueOf(elf.SHT_PREINIT_ARRAY),
		"SHT_PROGBITS":                              reflect.ValueOf(elf.SHT_PROGBITS),
		"SHT_REL":                                   reflect.ValueOf(elf.SHT_REL),
		"SHT_RELA":                                  reflect.ValueOf(elf.SHT_RELA),
		"SHT_SHLIB":                                 reflect.ValueOf(elf.SHT_SHLIB),
		"SHT_STRTAB":                                reflect.ValueOf(elf.SHT_STRTAB),
		"SHT_SYMTAB":                                reflect.ValueOf(elf.SHT_SYMTAB),
		"SHT_SYMTAB_SHNDX":                          reflect.ValueOf(elf.SHT_SYMTAB_SHNDX),
		"STB_GLOBAL":                                reflect.ValueOf(elf.STB_GLOBAL),
		"STB_HIOS":                                  reflect.ValueOf(elf.STB_HIOS),
		"STB_HIPROC":                                reflect.ValueOf(elf.STB_HIPROC),
		"STB_LOCAL":                                 reflect.ValueOf(elf.STB_LOCAL),
		"STB_LOOS":                                  reflect.ValueOf(elf.STB_LOOS),
		"STB_LOPROC":                                reflect.ValueOf(elf.STB_LOPROC),
		"STB_WEAK":                                  reflect.ValueOf(elf.STB_WEAK),
		"STT_COMMON":                                reflect.ValueOf(elf.STT_COMMON),
		"STT_FILE":                                  reflect.ValueOf(elf.STT_FILE),
		"STT_FUNC":                                  reflect.ValueOf(elf.STT_FUNC),
		"STT_HIOS":                                  reflect.ValueOf(elf.STT_HIOS),
		"STT_HIPROC":                                reflect.ValueOf(elf.STT_HIPROC),
		"STT_LOOS":                                  reflect.ValueOf(elf.STT_LOOS),
		"STT_LOPROC":                                reflect.ValueOf(elf.STT_LOPROC),
		"STT_NOTYPE":                                reflect.ValueOf(elf.STT_NOTYPE),
		"STT_OBJECT":                                reflect.ValueOf(elf.STT_OBJECT),
		"STT_SECTION":                               reflect.ValueOf(elf.STT_SECTION),
		"STT_TLS":                                   reflect.ValueOf(elf.STT_TLS),
		"STV_DEFAULT":                               reflect.ValueOf(elf.STV_DEFAULT),
		"STV_HIDDEN":                                reflect.ValueOf(elf.STV_HIDDEN),
		"STV_INTERNAL":                              reflect.ValueOf(elf.STV_INTERNAL),
		"STV_PROTECTED":                             reflect.ValueOf(elf.STV_PROTECTED),
		"ST_BIND":                                   reflect.ValueOf(elf.ST_BIND),
		"ST_INFO":                                   reflect.ValueOf(elf.ST_INFO),
		"ST_TYPE":                                   reflect.ValueOf(elf.ST_TYPE),
		"ST_VISIBILITY":                             reflect.ValueOf(elf.ST_VISIBILITY),
		"Sym32Size":                                 reflect.ValueOf(constant.MakeFromLiteral("16", token.INT, 0)),
		"Sym64Size":                                 reflect.ValueOf(constant.MakeFromLiteral("24", token.INT, 0)),

		// type definitions
		"Chdr32":          reflect.ValueOf((*elf.Chdr32)(nil)),
		"Chdr64":          reflect.ValueOf((*elf.Chdr64)(nil)),
		"Class":           reflect.ValueOf((*elf.Class)(nil)),
		"CompressionType": reflect.ValueOf((*elf.CompressionType)(nil)),
		"Data":            reflect.ValueOf((*elf.Data)(nil)),
		"Dyn32":           reflect.ValueOf((*elf.Dyn32)(nil)),
		"Dyn64":           reflect.ValueOf((*elf.Dyn64)(nil)),
		"DynFlag":         reflect.ValueOf((*elf.DynFlag)(nil)),
		"DynFlag1":        reflect.ValueOf((*elf.DynFlag1)(nil)),
		"DynTag":          reflect.ValueOf((*elf.DynTag)(nil)),
		"File":            reflect.ValueOf((*elf.File)(nil)),
		"FileHeader":      reflect.ValueOf((*elf.FileHeader)(nil)),
		"FormatError":     reflect.ValueOf((*elf.FormatError)(nil)),
		"Header32":        reflect.ValueOf((*elf.Header32)(nil)),
		"Header64":        reflect.ValueOf((*elf.Header64)(nil)),
		"ImportedSymbol":  reflect.ValueOf((*elf.ImportedSymbol)(nil)),
		"Machine":         reflect.ValueOf((*elf.Machine)(nil)),
		"NType":           reflect.ValueOf((*elf.NType)(nil)),
		"OSABI":           reflect.ValueOf((*elf.OSABI)(nil)),
		"Prog":            reflect.ValueOf((*elf.Prog)(nil)),
		"Prog32":          reflect.ValueOf((*elf.Prog32)(nil)),
		"Prog64":          reflect.ValueOf((*elf.Prog64)(nil)),
		"ProgFlag":        reflect.ValueOf((*elf.ProgFlag)(nil)),
		"ProgHeader":      reflect.ValueOf((*elf.ProgHeader)(nil)),
		"ProgType":        reflect.ValueOf((*elf.ProgType)(nil)),
		"R_386":           reflect.ValueOf((*elf.R_386)(nil)),
		"R_390":           reflect.ValueOf((*elf.R_390)(nil)),
		"R_AARCH64":       reflect.ValueOf((*elf.R_AARCH64)(nil)),
		"R_ALPHA":         reflect.ValueOf((*elf.R_ALPHA)(nil)),
		"R_ARM":           reflect.ValueOf((*elf.R_ARM)(nil)),
		"R_LARCH":         reflect.ValueOf((*elf.R_LARCH)(nil)),
		"R_MIPS":          reflect.ValueOf((*elf.R_MIPS)(nil)),
		"R_PPC":           reflect.ValueOf((*elf.R_PPC)(nil)),
		"R_PPC64":         reflect.ValueOf((*elf.R_PPC64)(nil)),
		"R_RISCV":         reflect.ValueOf((*elf.R_RISCV)(nil)),
		"R_SPARC":         reflect.ValueOf((*elf.R_SPARC)(nil)),
		"R_X86_64":        reflect.ValueOf((*elf.R_X86_64)(nil)),
		"Rel32":           reflect.ValueOf((*elf.Rel32)(nil)),
		"Rel64":           reflect.ValueOf((*elf.Rel64)(nil)),
		"Rela32":          reflect.ValueOf((*elf.Rela32)(nil)),
		"Rela64":          reflect.ValueOf((*elf.Rela64)(nil)),
		"Section":         reflect.ValueOf((*elf.Section)(nil)),
		"Section32":       reflect.ValueOf((*elf.Section32)(nil)),
		"Section64":       reflect.ValueOf((*elf.Section64)(nil)),
		"SectionFlag":     reflect.ValueOf((*elf.SectionFlag)(nil)),
		"SectionHeader":   reflect.ValueOf((*elf.SectionHeader)(nil)),
		"SectionIndex":    reflect.ValueOf((*elf.SectionIndex)(nil)),
		"SectionType":     reflect.ValueOf((*elf.SectionType)(nil)),
		"Sym32":           reflect.ValueOf((*elf.Sym32)(nil)),
		"Sym64":           reflect.ValueOf((*elf.Sym64)(nil)),
		"SymBind":         reflect.ValueOf((*elf.SymBind)(nil)),
		"SymType":         reflect.ValueOf((*elf.SymType)(nil)),
		"SymVis":          reflect.ValueOf((*elf.SymVis)(nil)),
		"Symbol":          reflect.ValueOf((*elf.Symbol)(nil)),
		"Type":            reflect.ValueOf((*elf.Type)(nil)),
		"Version":         reflect.ValueOf((*elf.Version)(nil)),
	}
}
