// Package helpdesk code generated by oapi sdk gen
/*
 * MIT License
 *
 * Copyright (c) 2022 Lark Technologies Pte. Ltd.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice, shall be included in all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */

package larkhelpdesk

import (
	"io"

	"io/ioutil"

	"fmt"

	"context"
	"errors"

	"github.com/larksuite/oapi-sdk-go/v3/event"

	"github.com/larksuite/oapi-sdk-go/v3/core"
)

const (
	MsgTypeText        = "text"        // 普通文本
	MsgTypePost        = "post"        // 富文本
	MsgTypeImage       = "image"       // 图片
	MsgTypeInteractive = "interactive" // 卡片消息
)

const (
	ReceiveTypeChat = "chat" // 通过服务台专属群发送
	ReceiveTypeUser = "user" // 通过服务台机器人私聊发送
)

const (
	UserIdTypeUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypeUnionId = "union_id" // 以union_id来识别用户
	UserIdTypeOpenId  = "open_id"  // 以open_id来识别用户
)

const (
	UserIdTypeCreateNotificationUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypeCreateNotificationUnionId = "union_id" // 以union_id来识别用户
	UserIdTypeCreateNotificationOpenId  = "open_id"  // 以open_id来识别用户
)

const (
	UserIdTypeGetNotificationUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypeGetNotificationUnionId = "union_id" // 以union_id来识别用户
	UserIdTypeGetNotificationOpenId  = "open_id"  // 以open_id来识别用户
)

const (
	UserIdTypePatchNotificationUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypePatchNotificationUnionId = "union_id" // 以union_id来识别用户
	UserIdTypePatchNotificationOpenId  = "open_id"  // 以open_id来识别用户
)

type Agent struct {
	Id          *string `json:"id,omitempty"`           // user id
	AvatarUrl   *string `json:"avatar_url,omitempty"`   // user avatar url
	Name        *string `json:"name,omitempty"`         // user name
	Email       *string `json:"email,omitempty"`        // user email
	Department  *string `json:"department,omitempty"`   // user department
	CompanyName *string `json:"company_name,omitempty"` // company
}

type AgentBuilder struct {
	id              string // user id
	idFlag          bool
	avatarUrl       string // user avatar url
	avatarUrlFlag   bool
	name            string // user name
	nameFlag        bool
	email           string // user email
	emailFlag       bool
	department      string // user department
	departmentFlag  bool
	companyName     string // company
	companyNameFlag bool
}

func NewAgentBuilder() *AgentBuilder {
	builder := &AgentBuilder{}
	return builder
}

// user id
//
// 示例值：ou_ea651a5c09e2d01af8acd34059f5359b
func (builder *AgentBuilder) Id(id string) *AgentBuilder {
	builder.id = id
	builder.idFlag = true
	return builder
}

// user avatar url
//
// 示例值：https://avatar-url.com/test.png
func (builder *AgentBuilder) AvatarUrl(avatarUrl string) *AgentBuilder {
	builder.avatarUrl = avatarUrl
	builder.avatarUrlFlag = true
	return builder
}

// user name
//
// 示例值：test-user
func (builder *AgentBuilder) Name(name string) *AgentBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

// user email
//
// 示例值：
func (builder *AgentBuilder) Email(email string) *AgentBuilder {
	builder.email = email
	builder.emailFlag = true
	return builder
}

// user department
//
// 示例值：
func (builder *AgentBuilder) Department(department string) *AgentBuilder {
	builder.department = department
	builder.departmentFlag = true
	return builder
}

// company
//
// 示例值：
func (builder *AgentBuilder) CompanyName(companyName string) *AgentBuilder {
	builder.companyName = companyName
	builder.companyNameFlag = true
	return builder
}

func (builder *AgentBuilder) Build() *Agent {
	req := &Agent{}
	if builder.idFlag {
		req.Id = &builder.id

	}
	if builder.avatarUrlFlag {
		req.AvatarUrl = &builder.avatarUrl

	}
	if builder.nameFlag {
		req.Name = &builder.name

	}
	if builder.emailFlag {
		req.Email = &builder.email

	}
	if builder.departmentFlag {
		req.Department = &builder.department

	}
	if builder.companyNameFlag {
		req.CompanyName = &builder.companyName

	}
	return req
}

type AgentSchedules struct {
	Status      *int                  `json:"status,omitempty"`       // status of agent
	Agent       *AgentUser            `json:"agent,omitempty"`        // agent info
	Schedule    []*WeekdaySchedule    `json:"schedule,omitempty"`     // day schedule
	AgentSkills []*AgentSkillLessInfo `json:"agent_skills,omitempty"` // agent skills
}

type AgentSchedulesBuilder struct {
	status          int // status of agent
	statusFlag      bool
	agent           *AgentUser // agent info
	agentFlag       bool
	schedule        []*WeekdaySchedule // day schedule
	scheduleFlag    bool
	agentSkills     []*AgentSkillLessInfo // agent skills
	agentSkillsFlag bool
}

func NewAgentSchedulesBuilder() *AgentSchedulesBuilder {
	builder := &AgentSchedulesBuilder{}
	return builder
}

// status of agent
//
// 示例值：
func (builder *AgentSchedulesBuilder) Status(status int) *AgentSchedulesBuilder {
	builder.status = status
	builder.statusFlag = true
	return builder
}

// agent info
//
// 示例值：
func (builder *AgentSchedulesBuilder) Agent(agent *AgentUser) *AgentSchedulesBuilder {
	builder.agent = agent
	builder.agentFlag = true
	return builder
}

// day schedule
//
// 示例值：
func (builder *AgentSchedulesBuilder) Schedule(schedule []*WeekdaySchedule) *AgentSchedulesBuilder {
	builder.schedule = schedule
	builder.scheduleFlag = true
	return builder
}

// agent skills
//
// 示例值：
func (builder *AgentSchedulesBuilder) AgentSkills(agentSkills []*AgentSkillLessInfo) *AgentSchedulesBuilder {
	builder.agentSkills = agentSkills
	builder.agentSkillsFlag = true
	return builder
}

func (builder *AgentSchedulesBuilder) Build() *AgentSchedules {
	req := &AgentSchedules{}
	if builder.statusFlag {
		req.Status = &builder.status

	}
	if builder.agentFlag {
		req.Agent = builder.agent
	}
	if builder.scheduleFlag {
		req.Schedule = builder.schedule
	}
	if builder.agentSkillsFlag {
		req.AgentSkills = builder.agentSkills
	}
	return req
}

type AgentSchedule struct {
	Status      *int                  `json:"status,omitempty"`       // 客服状态, 1 - online客服, 2 - offline(手动)客服, 3 - off duty(下班)自动处于非服务时间段
	Agent       *AgentUser            `json:"agent,omitempty"`        // 客服信息
	Schedule    []*WeekdaySchedule    `json:"schedule,omitempty"`     // 工作日程列表
	AgentSkills []*AgentSkillLessInfo `json:"agent_skills,omitempty"` // 客服技能
}

type AgentScheduleBuilder struct {
	status          int // 客服状态, 1 - online客服, 2 - offline(手动)客服, 3 - off duty(下班)自动处于非服务时间段
	statusFlag      bool
	agent           *AgentUser // 客服信息
	agentFlag       bool
	schedule        []*WeekdaySchedule // 工作日程列表
	scheduleFlag    bool
	agentSkills     []*AgentSkillLessInfo // 客服技能
	agentSkillsFlag bool
}

func NewAgentScheduleBuilder() *AgentScheduleBuilder {
	builder := &AgentScheduleBuilder{}
	return builder
}

// 客服状态, 1 - online客服, 2 - offline(手动)客服, 3 - off duty(下班)自动处于非服务时间段
//
// 示例值：1
func (builder *AgentScheduleBuilder) Status(status int) *AgentScheduleBuilder {
	builder.status = status
	builder.statusFlag = true
	return builder
}

// 客服信息
//
// 示例值：
func (builder *AgentScheduleBuilder) Agent(agent *AgentUser) *AgentScheduleBuilder {
	builder.agent = agent
	builder.agentFlag = true
	return builder
}

// 工作日程列表
//
// 示例值：
func (builder *AgentScheduleBuilder) Schedule(schedule []*WeekdaySchedule) *AgentScheduleBuilder {
	builder.schedule = schedule
	builder.scheduleFlag = true
	return builder
}

// 客服技能
//
// 示例值：
func (builder *AgentScheduleBuilder) AgentSkills(agentSkills []*AgentSkillLessInfo) *AgentScheduleBuilder {
	builder.agentSkills = agentSkills
	builder.agentSkillsFlag = true
	return builder
}

func (builder *AgentScheduleBuilder) Build() *AgentSchedule {
	req := &AgentSchedule{}
	if builder.statusFlag {
		req.Status = &builder.status

	}
	if builder.agentFlag {
		req.Agent = builder.agent
	}
	if builder.scheduleFlag {
		req.Schedule = builder.schedule
	}
	if builder.agentSkillsFlag {
		req.AgentSkills = builder.agentSkills
	}
	return req
}

type AgentScheduleUpdateInfo struct {
	AgentId       *string            `json:"agent_id,omitempty"`        // 客服id;;[可以以普通用户身份在服务台发起工单，从工单详情里面获取用户guest.id](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/helpdesk-v1/ticket/get)
	Schedule      []*WeekdaySchedule `json:"schedule,omitempty"`        // 工作日程列表
	AgentSkillIds []string           `json:"agent_skill_ids,omitempty"` // 客服技能 ids
}

type AgentScheduleUpdateInfoBuilder struct {
	agentId           string // 客服id;;[可以以普通用户身份在服务台发起工单，从工单详情里面获取用户guest.id](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/helpdesk-v1/ticket/get)
	agentIdFlag       bool
	schedule          []*WeekdaySchedule // 工作日程列表
	scheduleFlag      bool
	agentSkillIds     []string // 客服技能 ids
	agentSkillIdsFlag bool
}

func NewAgentScheduleUpdateInfoBuilder() *AgentScheduleUpdateInfoBuilder {
	builder := &AgentScheduleUpdateInfoBuilder{}
	return builder
}

// 客服id;;[可以以普通用户身份在服务台发起工单，从工单详情里面获取用户guest.id](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/helpdesk-v1/ticket/get)
//
// 示例值：agent-id
func (builder *AgentScheduleUpdateInfoBuilder) AgentId(agentId string) *AgentScheduleUpdateInfoBuilder {
	builder.agentId = agentId
	builder.agentIdFlag = true
	return builder
}

// 工作日程列表
//
// 示例值：
func (builder *AgentScheduleUpdateInfoBuilder) Schedule(schedule []*WeekdaySchedule) *AgentScheduleUpdateInfoBuilder {
	builder.schedule = schedule
	builder.scheduleFlag = true
	return builder
}

// 客服技能 ids
//
// 示例值：[“test-skill-id”]
func (builder *AgentScheduleUpdateInfoBuilder) AgentSkillIds(agentSkillIds []string) *AgentScheduleUpdateInfoBuilder {
	builder.agentSkillIds = agentSkillIds
	builder.agentSkillIdsFlag = true
	return builder
}

func (builder *AgentScheduleUpdateInfoBuilder) Build() *AgentScheduleUpdateInfo {
	req := &AgentScheduleUpdateInfo{}
	if builder.agentIdFlag {
		req.AgentId = &builder.agentId

	}
	if builder.scheduleFlag {
		req.Schedule = builder.schedule
	}
	if builder.agentSkillIdsFlag {
		req.AgentSkillIds = builder.agentSkillIds
	}
	return req
}

type AgentSkill struct {
	Id         *string           `json:"id,omitempty"`          // 技能id
	HelpdeskId *string           `json:"helpdesk_id,omitempty"` // helpdesk id
	Name       *string           `json:"name,omitempty"`        // 技能名
	Rules      []*AgentSkillRule `json:"rules,omitempty"`       // 技能rules
	AgentIds   []string          `json:"agent_ids,omitempty"`   // 具有此技能的客服ids
	IsDefault  *bool             `json:"is_default,omitempty"`  // 默认技能
	Agents     []*Agent          `json:"agents,omitempty"`      // 客服 info
}

type AgentSkillBuilder struct {
	id             string // 技能id
	idFlag         bool
	helpdeskId     string // helpdesk id
	helpdeskIdFlag bool
	name           string // 技能名
	nameFlag       bool
	rules          []*AgentSkillRule // 技能rules
	rulesFlag      bool
	agentIds       []string // 具有此技能的客服ids
	agentIdsFlag   bool
	isDefault      bool // 默认技能
	isDefaultFlag  bool
	agents         []*Agent // 客服 info
	agentsFlag     bool
}

func NewAgentSkillBuilder() *AgentSkillBuilder {
	builder := &AgentSkillBuilder{}
	return builder
}

// 技能id
//
// 示例值：test-skill-id
func (builder *AgentSkillBuilder) Id(id string) *AgentSkillBuilder {
	builder.id = id
	builder.idFlag = true
	return builder
}

// helpdesk id
//
// 示例值：
func (builder *AgentSkillBuilder) HelpdeskId(helpdeskId string) *AgentSkillBuilder {
	builder.helpdeskId = helpdeskId
	builder.helpdeskIdFlag = true
	return builder
}

// 技能名
//
// 示例值：skill-name
func (builder *AgentSkillBuilder) Name(name string) *AgentSkillBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

// 技能rules
//
// 示例值：
func (builder *AgentSkillBuilder) Rules(rules []*AgentSkillRule) *AgentSkillBuilder {
	builder.rules = rules
	builder.rulesFlag = true
	return builder
}

// 具有此技能的客服ids
//
// 示例值：["ou_ea21d7f018e1155d960e40d33191f966"]
func (builder *AgentSkillBuilder) AgentIds(agentIds []string) *AgentSkillBuilder {
	builder.agentIds = agentIds
	builder.agentIdsFlag = true
	return builder
}

// 默认技能
//
// 示例值：false
func (builder *AgentSkillBuilder) IsDefault(isDefault bool) *AgentSkillBuilder {
	builder.isDefault = isDefault
	builder.isDefaultFlag = true
	return builder
}

// 客服 info
//
// 示例值：
func (builder *AgentSkillBuilder) Agents(agents []*Agent) *AgentSkillBuilder {
	builder.agents = agents
	builder.agentsFlag = true
	return builder
}

func (builder *AgentSkillBuilder) Build() *AgentSkill {
	req := &AgentSkill{}
	if builder.idFlag {
		req.Id = &builder.id

	}
	if builder.helpdeskIdFlag {
		req.HelpdeskId = &builder.helpdeskId

	}
	if builder.nameFlag {
		req.Name = &builder.name

	}
	if builder.rulesFlag {
		req.Rules = builder.rules
	}
	if builder.agentIdsFlag {
		req.AgentIds = builder.agentIds
	}
	if builder.isDefaultFlag {
		req.IsDefault = &builder.isDefault

	}
	if builder.agentsFlag {
		req.Agents = builder.agents
	}
	return req
}

type AgentSkillLessInfo struct {
	Id        *string `json:"id,omitempty"`         // 客服技能 id
	Name      *string `json:"name,omitempty"`       // 客服技能名
	IsDefault *bool   `json:"is_default,omitempty"` // 是默认技能
}

type AgentSkillLessInfoBuilder struct {
	id            string // 客服技能 id
	idFlag        bool
	name          string // 客服技能名
	nameFlag      bool
	isDefault     bool // 是默认技能
	isDefaultFlag bool
}

func NewAgentSkillLessInfoBuilder() *AgentSkillLessInfoBuilder {
	builder := &AgentSkillLessInfoBuilder{}
	return builder
}

// 客服技能 id
//
// 示例值：agent-skill-id
func (builder *AgentSkillLessInfoBuilder) Id(id string) *AgentSkillLessInfoBuilder {
	builder.id = id
	builder.idFlag = true
	return builder
}

// 客服技能名
//
// 示例值：agent-skill
func (builder *AgentSkillLessInfoBuilder) Name(name string) *AgentSkillLessInfoBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

// 是默认技能
//
// 示例值：false
func (builder *AgentSkillLessInfoBuilder) IsDefault(isDefault bool) *AgentSkillLessInfoBuilder {
	builder.isDefault = isDefault
	builder.isDefaultFlag = true
	return builder
}

func (builder *AgentSkillLessInfoBuilder) Build() *AgentSkillLessInfo {
	req := &AgentSkillLessInfo{}
	if builder.idFlag {
		req.Id = &builder.id

	}
	if builder.nameFlag {
		req.Name = &builder.name

	}
	if builder.isDefaultFlag {
		req.IsDefault = &builder.isDefault

	}
	return req
}

type AgentSkillRule struct {
	Id               *string `json:"id,omitempty"`                // rule id, 参考[获取客服技能rules](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/helpdesk-v1/agent_skill_rule/list) 用于获取rules options
	SelectedOperator *int    `json:"selected_operator,omitempty"` // 运算符比较, 参考[客服技能运算符选项](https://open.feishu.cn/document/ukTMukTMukTM/ucDOyYjL3gjM24yN4IjN/operator-options)
	OperatorOptions  []int   `json:"operator_options,omitempty"`  // rule操作数value，[客服技能及运算符](https://open.feishu.cn/document/ukTMukTMukTM/ucDOyYjL3gjM24yN4IjN/operator-options)
	Operand          *string `json:"operand,omitempty"`           // rule 操作数的值
	Category         *int    `json:"category,omitempty"`          // rule 类型，1-知识库，2-工单信息，3-用户飞书信息
	DisplayName      *string `json:"display_name,omitempty"`      // rule 名
}

type AgentSkillRuleBuilder struct {
	id                   string // rule id, 参考[获取客服技能rules](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/helpdesk-v1/agent_skill_rule/list) 用于获取rules options
	idFlag               bool
	selectedOperator     int // 运算符比较, 参考[客服技能运算符选项](https://open.feishu.cn/document/ukTMukTMukTM/ucDOyYjL3gjM24yN4IjN/operator-options)
	selectedOperatorFlag bool
	operatorOptions      []int // rule操作数value，[客服技能及运算符](https://open.feishu.cn/document/ukTMukTMukTM/ucDOyYjL3gjM24yN4IjN/operator-options)
	operatorOptionsFlag  bool
	operand              string // rule 操作数的值
	operandFlag          bool
	category             int // rule 类型，1-知识库，2-工单信息，3-用户飞书信息
	categoryFlag         bool
	displayName          string // rule 名
	displayNameFlag      bool
}

func NewAgentSkillRuleBuilder() *AgentSkillRuleBuilder {
	builder := &AgentSkillRuleBuilder{}
	return builder
}

// rule id, 参考[获取客服技能rules](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/helpdesk-v1/agent_skill_rule/list) 用于获取rules options
//
// 示例值：test-skill-id
func (builder *AgentSkillRuleBuilder) Id(id string) *AgentSkillRuleBuilder {
	builder.id = id
	builder.idFlag = true
	return builder
}

// 运算符比较, 参考[客服技能运算符选项](https://open.feishu.cn/document/ukTMukTMukTM/ucDOyYjL3gjM24yN4IjN/operator-options)
//
// 示例值：8
func (builder *AgentSkillRuleBuilder) SelectedOperator(selectedOperator int) *AgentSkillRuleBuilder {
	builder.selectedOperator = selectedOperator
	builder.selectedOperatorFlag = true
	return builder
}

// rule操作数value，[客服技能及运算符](https://open.feishu.cn/document/ukTMukTMukTM/ucDOyYjL3gjM24yN4IjN/operator-options)
//
// 示例值：[3]
func (builder *AgentSkillRuleBuilder) OperatorOptions(operatorOptions []int) *AgentSkillRuleBuilder {
	builder.operatorOptions = operatorOptions
	builder.operatorOptionsFlag = true
	return builder
}

// rule 操作数的值
//
// 示例值：{;				"selected_departments": [;					{;						"id": "部门ID",;						"name": "IT";					};				];			}
func (builder *AgentSkillRuleBuilder) Operand(operand string) *AgentSkillRuleBuilder {
	builder.operand = operand
	builder.operandFlag = true
	return builder
}

// rule 类型，1-知识库，2-工单信息，3-用户飞书信息
//
// 示例值：3
func (builder *AgentSkillRuleBuilder) Category(category int) *AgentSkillRuleBuilder {
	builder.category = category
	builder.categoryFlag = true
	return builder
}

// rule 名
//
// 示例值：中文知识库分类
func (builder *AgentSkillRuleBuilder) DisplayName(displayName string) *AgentSkillRuleBuilder {
	builder.displayName = displayName
	builder.displayNameFlag = true
	return builder
}

func (builder *AgentSkillRuleBuilder) Build() *AgentSkillRule {
	req := &AgentSkillRule{}
	if builder.idFlag {
		req.Id = &builder.id

	}
	if builder.selectedOperatorFlag {
		req.SelectedOperator = &builder.selectedOperator

	}
	if builder.operatorOptionsFlag {
		req.OperatorOptions = builder.operatorOptions
	}
	if builder.operandFlag {
		req.Operand = &builder.operand

	}
	if builder.categoryFlag {
		req.Category = &builder.category

	}
	if builder.displayNameFlag {
		req.DisplayName = &builder.displayName

	}
	return req
}

type AgentUser struct {
	Id          *string `json:"id,omitempty"`           // 客服 id
	AvatarUrl   *string `json:"avatar_url,omitempty"`   // avatar url
	Name        *string `json:"name,omitempty"`         // 客服名字
	Email       *string `json:"email,omitempty"`        // email
	Department  *string `json:"department,omitempty"`   // 部门
	CompanyName *string `json:"company_name,omitempty"` // 公司名
}

type AgentUserBuilder struct {
	id              string // 客服 id
	idFlag          bool
	avatarUrl       string // avatar url
	avatarUrlFlag   bool
	name            string // 客服名字
	nameFlag        bool
	email           string // email
	emailFlag       bool
	department      string // 部门
	departmentFlag  bool
	companyName     string // 公司名
	companyNameFlag bool
}

func NewAgentUserBuilder() *AgentUserBuilder {
	builder := &AgentUserBuilder{}
	return builder
}

// 客服 id
//
// 示例值：ou_ea651a5c09e2d01af8acd34059f5359b
func (builder *AgentUserBuilder) Id(id string) *AgentUserBuilder {
	builder.id = id
	builder.idFlag = true
	return builder
}

// avatar url
//
// 示例值：https://avatar-url.com/test.png
func (builder *AgentUserBuilder) AvatarUrl(avatarUrl string) *AgentUserBuilder {
	builder.avatarUrl = avatarUrl
	builder.avatarUrlFlag = true
	return builder
}

// 客服名字
//
// 示例值：test-user
func (builder *AgentUserBuilder) Name(name string) *AgentUserBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

// email
//
// 示例值：<EMAIL>
func (builder *AgentUserBuilder) Email(email string) *AgentUserBuilder {
	builder.email = email
	builder.emailFlag = true
	return builder
}

// 部门
//
// 示例值：测试部门
func (builder *AgentUserBuilder) Department(department string) *AgentUserBuilder {
	builder.department = department
	builder.departmentFlag = true
	return builder
}

// 公司名
//
// 示例值：test-company
func (builder *AgentUserBuilder) CompanyName(companyName string) *AgentUserBuilder {
	builder.companyName = companyName
	builder.companyNameFlag = true
	return builder
}

func (builder *AgentUserBuilder) Build() *AgentUser {
	req := &AgentUser{}
	if builder.idFlag {
		req.Id = &builder.id

	}
	if builder.avatarUrlFlag {
		req.AvatarUrl = &builder.avatarUrl

	}
	if builder.nameFlag {
		req.Name = &builder.name

	}
	if builder.emailFlag {
		req.Email = &builder.email

	}
	if builder.departmentFlag {
		req.Department = &builder.department

	}
	if builder.companyNameFlag {
		req.CompanyName = &builder.companyName

	}
	return req
}

type BotMessage struct {
	MsgType     *string `json:"msg_type,omitempty"`     // 消息类型
	Content     *string `json:"content,omitempty"`      // 消息内容，json格式结构序列化成string。格式说明参考: [发送消息content说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/im-v1/message/create_json)
	ReceiverId  *string `json:"receiver_id,omitempty"`  // 接收消息用户id
	ReceiveType *string `json:"receive_type,omitempty"` // 接收消息方式，chat(服务台专属服务群)或user(服务台机器人私聊)。若选择专属服务群，用户有正在处理的工单将会发送失败。默认以chat方式发送。
}

type BotMessageBuilder struct {
	msgType         string // 消息类型
	msgTypeFlag     bool
	content         string // 消息内容，json格式结构序列化成string。格式说明参考: [发送消息content说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/im-v1/message/create_json)
	contentFlag     bool
	receiverId      string // 接收消息用户id
	receiverIdFlag  bool
	receiveType     string // 接收消息方式，chat(服务台专属服务群)或user(服务台机器人私聊)。若选择专属服务群，用户有正在处理的工单将会发送失败。默认以chat方式发送。
	receiveTypeFlag bool
}

func NewBotMessageBuilder() *BotMessageBuilder {
	builder := &BotMessageBuilder{}
	return builder
}

// 消息类型
//
// 示例值：post
func (builder *BotMessageBuilder) MsgType(msgType string) *BotMessageBuilder {
	builder.msgType = msgType
	builder.msgTypeFlag = true
	return builder
}

// 消息内容，json格式结构序列化成string。格式说明参考: [发送消息content说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/im-v1/message/create_json)
//
// 示例值：{\"post\":{\"zh_cn\":{\"title\":\"some title\",\"content\":[[{\"tag\":\"text\",\"text\":\"some content\"}]]}}}
func (builder *BotMessageBuilder) Content(content string) *BotMessageBuilder {
	builder.content = content
	builder.contentFlag = true
	return builder
}

// 接收消息用户id
//
// 示例值：ou_7346484524
func (builder *BotMessageBuilder) ReceiverId(receiverId string) *BotMessageBuilder {
	builder.receiverId = receiverId
	builder.receiverIdFlag = true
	return builder
}

// 接收消息方式，chat(服务台专属服务群)或user(服务台机器人私聊)。若选择专属服务群，用户有正在处理的工单将会发送失败。默认以chat方式发送。
//
// 示例值：chat
func (builder *BotMessageBuilder) ReceiveType(receiveType string) *BotMessageBuilder {
	builder.receiveType = receiveType
	builder.receiveTypeFlag = true
	return builder
}

func (builder *BotMessageBuilder) Build() *BotMessage {
	req := &BotMessage{}
	if builder.msgTypeFlag {
		req.MsgType = &builder.msgType

	}
	if builder.contentFlag {
		req.Content = &builder.content

	}
	if builder.receiverIdFlag {
		req.ReceiverId = &builder.receiverId

	}
	if builder.receiveTypeFlag {
		req.ReceiveType = &builder.receiveType

	}
	return req
}

type Category struct {
	CategoryId *string `json:"category_id,omitempty"` // 知识库分类ID
	Id         *string `json:"id,omitempty"`          // 知识库分类ID，（旧版，请使用category_id）
	Name       *string `json:"name,omitempty"`        // 名称
	ParentId   *string `json:"parent_id,omitempty"`   // 父知识库分类ID
	HelpdeskId *string `json:"helpdesk_id,omitempty"` // 服务台ID
	Language   *string `json:"language,omitempty"`    // 语言
}

type CategoryBuilder struct {
	categoryId     string // 知识库分类ID
	categoryIdFlag bool
	id             string // 知识库分类ID，（旧版，请使用category_id）
	idFlag         bool
	name           string // 名称
	nameFlag       bool
	parentId       string // 父知识库分类ID
	parentIdFlag   bool
	helpdeskId     string // 服务台ID
	helpdeskIdFlag bool
	language       string // 语言
	languageFlag   bool
}

func NewCategoryBuilder() *CategoryBuilder {
	builder := &CategoryBuilder{}
	return builder
}

// 知识库分类ID
//
// 示例值：6948728206392295444
func (builder *CategoryBuilder) CategoryId(categoryId string) *CategoryBuilder {
	builder.categoryId = categoryId
	builder.categoryIdFlag = true
	return builder
}

// 知识库分类ID，（旧版，请使用category_id）
//
// 示例值：6948728206392295444
func (builder *CategoryBuilder) Id(id string) *CategoryBuilder {
	builder.id = id
	builder.idFlag = true
	return builder
}

// 名称
//
// 示例值：创建团队和邀请成员
func (builder *CategoryBuilder) Name(name string) *CategoryBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

// 父知识库分类ID
//
// 示例值：0
func (builder *CategoryBuilder) ParentId(parentId string) *CategoryBuilder {
	builder.parentId = parentId
	builder.parentIdFlag = true
	return builder
}

// 服务台ID
//
// 示例值：6939771743531696147
func (builder *CategoryBuilder) HelpdeskId(helpdeskId string) *CategoryBuilder {
	builder.helpdeskId = helpdeskId
	builder.helpdeskIdFlag = true
	return builder
}

// 语言
//
// 示例值：zh_cn
func (builder *CategoryBuilder) Language(language string) *CategoryBuilder {
	builder.language = language
	builder.languageFlag = true
	return builder
}

func (builder *CategoryBuilder) Build() *Category {
	req := &Category{}
	if builder.categoryIdFlag {
		req.CategoryId = &builder.categoryId

	}
	if builder.idFlag {
		req.Id = &builder.id

	}
	if builder.nameFlag {
		req.Name = &builder.name

	}
	if builder.parentIdFlag {
		req.ParentId = &builder.parentId

	}
	if builder.helpdeskIdFlag {
		req.HelpdeskId = &builder.helpdeskId

	}
	if builder.languageFlag {
		req.Language = &builder.language

	}
	return req
}

type Comments struct {
	Content       *string `json:"content,omitempty"`         // 备注
	CreatedAt     *int    `json:"created_at,omitempty"`      // 备注时间，单位毫秒
	Id            *int    `json:"id,omitempty"`              // 备注ID
	UserAvatarUrl *string `json:"user_avatar_url,omitempty"` // 备注人头像
	UserName      *string `json:"user_name,omitempty"`       // 备注人姓名
	UserId        *int    `json:"user_id,omitempty"`         // 备注人ID
}

type CommentsBuilder struct {
	content           string // 备注
	contentFlag       bool
	createdAt         int // 备注时间，单位毫秒
	createdAtFlag     bool
	id                int // 备注ID
	idFlag            bool
	userAvatarUrl     string // 备注人头像
	userAvatarUrlFlag bool
	userName          string // 备注人姓名
	userNameFlag      bool
	userId            int // 备注人ID
	userIdFlag        bool
}

func NewCommentsBuilder() *CommentsBuilder {
	builder := &CommentsBuilder{}
	return builder
}

// 备注
//
// 示例值：备注内容
func (builder *CommentsBuilder) Content(content string) *CommentsBuilder {
	builder.content = content
	builder.contentFlag = true
	return builder
}

// 备注时间，单位毫秒
//
// 示例值：备注时间
func (builder *CommentsBuilder) CreatedAt(createdAt int) *CommentsBuilder {
	builder.createdAt = createdAt
	builder.createdAtFlag = true
	return builder
}

// 备注ID
//
// 示例值：备注id
func (builder *CommentsBuilder) Id(id int) *CommentsBuilder {
	builder.id = id
	builder.idFlag = true
	return builder
}

// 备注人头像
//
// 示例值：备注人头像
func (builder *CommentsBuilder) UserAvatarUrl(userAvatarUrl string) *CommentsBuilder {
	builder.userAvatarUrl = userAvatarUrl
	builder.userAvatarUrlFlag = true
	return builder
}

// 备注人姓名
//
// 示例值：备注人姓名
func (builder *CommentsBuilder) UserName(userName string) *CommentsBuilder {
	builder.userName = userName
	builder.userNameFlag = true
	return builder
}

// 备注人ID
//
// 示例值：备注人id
func (builder *CommentsBuilder) UserId(userId int) *CommentsBuilder {
	builder.userId = userId
	builder.userIdFlag = true
	return builder
}

func (builder *CommentsBuilder) Build() *Comments {
	req := &Comments{}
	if builder.contentFlag {
		req.Content = &builder.content

	}
	if builder.createdAtFlag {
		req.CreatedAt = &builder.createdAt

	}
	if builder.idFlag {
		req.Id = &builder.id

	}
	if builder.userAvatarUrlFlag {
		req.UserAvatarUrl = &builder.userAvatarUrl

	}
	if builder.userNameFlag {
		req.UserName = &builder.userName

	}
	if builder.userIdFlag {
		req.UserId = &builder.userId

	}
	return req
}

type CustomizedFieldDisplayItem struct {
	Id          *string `json:"id,omitempty"`           // 自定义字段ID
	Value       *string `json:"value,omitempty"`        // 自定义字段值
	KeyName     *string `json:"key_name,omitempty"`     // 键名
	DisplayName *string `json:"display_name,omitempty"` // 展示名称
	Position    *int    `json:"position,omitempty"`     // 展示位置
	Required    *bool   `json:"required,omitempty"`     // 是否必填
	Editable    *bool   `json:"editable,omitempty"`     // 是否可修改
}

type CustomizedFieldDisplayItemBuilder struct {
	id              string // 自定义字段ID
	idFlag          bool
	value           string // 自定义字段值
	valueFlag       bool
	keyName         string // 键名
	keyNameFlag     bool
	displayName     string // 展示名称
	displayNameFlag bool
	position        int // 展示位置
	positionFlag    bool
	required        bool // 是否必填
	requiredFlag    bool
	editable        bool // 是否可修改
	editableFlag    bool
}

func NewCustomizedFieldDisplayItemBuilder() *CustomizedFieldDisplayItemBuilder {
	builder := &CustomizedFieldDisplayItemBuilder{}
	return builder
}

// 自定义字段ID
//
// 示例值：123
func (builder *CustomizedFieldDisplayItemBuilder) Id(id string) *CustomizedFieldDisplayItemBuilder {
	builder.id = id
	builder.idFlag = true
	return builder
}

// 自定义字段值
//
// 示例值：value
func (builder *CustomizedFieldDisplayItemBuilder) Value(value string) *CustomizedFieldDisplayItemBuilder {
	builder.value = value
	builder.valueFlag = true
	return builder
}

// 键名
//
// 示例值：key
func (builder *CustomizedFieldDisplayItemBuilder) KeyName(keyName string) *CustomizedFieldDisplayItemBuilder {
	builder.keyName = keyName
	builder.keyNameFlag = true
	return builder
}

// 展示名称
//
// 示例值：display name
func (builder *CustomizedFieldDisplayItemBuilder) DisplayName(displayName string) *CustomizedFieldDisplayItemBuilder {
	builder.displayName = displayName
	builder.displayNameFlag = true
	return builder
}

// 展示位置
//
// 示例值：1
func (builder *CustomizedFieldDisplayItemBuilder) Position(position int) *CustomizedFieldDisplayItemBuilder {
	builder.position = position
	builder.positionFlag = true
	return builder
}

// 是否必填
//
// 示例值：true
func (builder *CustomizedFieldDisplayItemBuilder) Required(required bool) *CustomizedFieldDisplayItemBuilder {
	builder.required = required
	builder.requiredFlag = true
	return builder
}

// 是否可修改
//
// 示例值：true
func (builder *CustomizedFieldDisplayItemBuilder) Editable(editable bool) *CustomizedFieldDisplayItemBuilder {
	builder.editable = editable
	builder.editableFlag = true
	return builder
}

func (builder *CustomizedFieldDisplayItemBuilder) Build() *CustomizedFieldDisplayItem {
	req := &CustomizedFieldDisplayItem{}
	if builder.idFlag {
		req.Id = &builder.id

	}
	if builder.valueFlag {
		req.Value = &builder.value

	}
	if builder.keyNameFlag {
		req.KeyName = &builder.keyName

	}
	if builder.displayNameFlag {
		req.DisplayName = &builder.displayName

	}
	if builder.positionFlag {
		req.Position = &builder.position

	}
	if builder.requiredFlag {
		req.Required = &builder.required

	}
	if builder.editableFlag {
		req.Editable = &builder.editable

	}
	return req
}

type Event struct {
	Type    *string `json:"type,omitempty"`    // 事件类型
	Subtype *string `json:"subtype,omitempty"` // 事件子类型
}

type EventBuilder struct {
	type_       string // 事件类型
	typeFlag    bool
	subtype     string // 事件子类型
	subtypeFlag bool
}

func NewEventBuilder() *EventBuilder {
	builder := &EventBuilder{}
	return builder
}

// 事件类型
//
// 示例值：helpdesk.ticket_message
func (builder *EventBuilder) Type(type_ string) *EventBuilder {
	builder.type_ = type_
	builder.typeFlag = true
	return builder
}

// 事件子类型
//
// 示例值：ticket_message.created_v1
func (builder *EventBuilder) Subtype(subtype string) *EventBuilder {
	builder.subtype = subtype
	builder.subtypeFlag = true
	return builder
}

func (builder *EventBuilder) Build() *Event {
	req := &Event{}
	if builder.typeFlag {
		req.Type = &builder.type_

	}
	if builder.subtypeFlag {
		req.Subtype = &builder.subtype

	}
	return req
}

type Faq struct {
	FaqId          *string     `json:"faq_id,omitempty"`          // 知识库ID
	Id             *string     `json:"id,omitempty"`              // 知识库旧版ID，请使用faq_id
	HelpdeskId     *string     `json:"helpdesk_id,omitempty"`     // 服务台ID
	Question       *string     `json:"question,omitempty"`        // 问题
	Answer         *string     `json:"answer,omitempty"`          // 答案
	AnswerRichtext []*Richtext `json:"answer_richtext,omitempty"` // 富文本答案
	CreateTime     *int        `json:"create_time,omitempty"`     // 创建时间
	UpdateTime     *int        `json:"update_time,omitempty"`     // 修改时间
	Categories     []*Category `json:"categories,omitempty"`      // 分类
	Tags           []string    `json:"tags,omitempty"`            // 相似问题列表
	ExpireTime     *int        `json:"expire_time,omitempty"`     // 失效时间
	UpdateUser     *TicketUser `json:"update_user,omitempty"`     // 更新用户
	CreateUser     *TicketUser `json:"create_user,omitempty"`     // 创建用户
}

type FaqBuilder struct {
	faqId              string // 知识库ID
	faqIdFlag          bool
	id                 string // 知识库旧版ID，请使用faq_id
	idFlag             bool
	helpdeskId         string // 服务台ID
	helpdeskIdFlag     bool
	question           string // 问题
	questionFlag       bool
	answer             string // 答案
	answerFlag         bool
	answerRichtext     []*Richtext // 富文本答案
	answerRichtextFlag bool
	createTime         int // 创建时间
	createTimeFlag     bool
	updateTime         int // 修改时间
	updateTimeFlag     bool
	categories         []*Category // 分类
	categoriesFlag     bool
	tags               []string // 相似问题列表
	tagsFlag           bool
	expireTime         int // 失效时间
	expireTimeFlag     bool
	updateUser         *TicketUser // 更新用户
	updateUserFlag     bool
	createUser         *TicketUser // 创建用户
	createUserFlag     bool
}

func NewFaqBuilder() *FaqBuilder {
	builder := &FaqBuilder{}
	return builder
}

// 知识库ID
//
// 示例值：6936004780707807231
func (builder *FaqBuilder) FaqId(faqId string) *FaqBuilder {
	builder.faqId = faqId
	builder.faqIdFlag = true
	return builder
}

// 知识库旧版ID，请使用faq_id
//
// 示例值：6936004780707807231
func (builder *FaqBuilder) Id(id string) *FaqBuilder {
	builder.id = id
	builder.idFlag = true
	return builder
}

// 服务台ID
//
// 示例值：6936004780707807251
func (builder *FaqBuilder) HelpdeskId(helpdeskId string) *FaqBuilder {
	builder.helpdeskId = helpdeskId
	builder.helpdeskIdFlag = true
	return builder
}

// 问题
//
// 示例值：问题
func (builder *FaqBuilder) Question(question string) *FaqBuilder {
	builder.question = question
	builder.questionFlag = true
	return builder
}

// 答案
//
// 示例值：答案
func (builder *FaqBuilder) Answer(answer string) *FaqBuilder {
	builder.answer = answer
	builder.answerFlag = true
	return builder
}

// 富文本答案
//
// 示例值：
func (builder *FaqBuilder) AnswerRichtext(answerRichtext []*Richtext) *FaqBuilder {
	builder.answerRichtext = answerRichtext
	builder.answerRichtextFlag = true
	return builder
}

// 创建时间
//
// 示例值：1596379008
func (builder *FaqBuilder) CreateTime(createTime int) *FaqBuilder {
	builder.createTime = createTime
	builder.createTimeFlag = true
	return builder
}

// 修改时间
//
// 示例值：1596379008
func (builder *FaqBuilder) UpdateTime(updateTime int) *FaqBuilder {
	builder.updateTime = updateTime
	builder.updateTimeFlag = true
	return builder
}

// 分类
//
// 示例值：
func (builder *FaqBuilder) Categories(categories []*Category) *FaqBuilder {
	builder.categories = categories
	builder.categoriesFlag = true
	return builder
}

// 相似问题列表
//
// 示例值：
func (builder *FaqBuilder) Tags(tags []string) *FaqBuilder {
	builder.tags = tags
	builder.tagsFlag = true
	return builder
}

// 失效时间
//
// 示例值：1596379008
func (builder *FaqBuilder) ExpireTime(expireTime int) *FaqBuilder {
	builder.expireTime = expireTime
	builder.expireTimeFlag = true
	return builder
}

// 更新用户
//
// 示例值：
func (builder *FaqBuilder) UpdateUser(updateUser *TicketUser) *FaqBuilder {
	builder.updateUser = updateUser
	builder.updateUserFlag = true
	return builder
}

// 创建用户
//
// 示例值：
func (builder *FaqBuilder) CreateUser(createUser *TicketUser) *FaqBuilder {
	builder.createUser = createUser
	builder.createUserFlag = true
	return builder
}

func (builder *FaqBuilder) Build() *Faq {
	req := &Faq{}
	if builder.faqIdFlag {
		req.FaqId = &builder.faqId

	}
	if builder.idFlag {
		req.Id = &builder.id

	}
	if builder.helpdeskIdFlag {
		req.HelpdeskId = &builder.helpdeskId

	}
	if builder.questionFlag {
		req.Question = &builder.question

	}
	if builder.answerFlag {
		req.Answer = &builder.answer

	}
	if builder.answerRichtextFlag {
		req.AnswerRichtext = builder.answerRichtext
	}
	if builder.createTimeFlag {
		req.CreateTime = &builder.createTime

	}
	if builder.updateTimeFlag {
		req.UpdateTime = &builder.updateTime

	}
	if builder.categoriesFlag {
		req.Categories = builder.categories
	}
	if builder.tagsFlag {
		req.Tags = builder.tags
	}
	if builder.expireTimeFlag {
		req.ExpireTime = &builder.expireTime

	}
	if builder.updateUserFlag {
		req.UpdateUser = builder.updateUser
	}
	if builder.createUserFlag {
		req.CreateUser = builder.createUser
	}
	return req
}

type FaqUpdateInfo struct {
	CategoryId     *string  `json:"category_id,omitempty"`     // 知识库分类ID
	Question       *string  `json:"question,omitempty"`        // 问题
	Answer         *string  `json:"answer,omitempty"`          // 答案
	AnswerRichtext *string  `json:"answer_richtext,omitempty"` // 富文本答案和答案必须有一个必填。Json Array格式，富文本结构请见[了解更多: 富文本](https://open.feishu.cn/document/ukTMukTMukTM/uITM0YjLyEDN24iMxQjN)
	Tags           []string `json:"tags,omitempty"`            // 相似问题
}

type FaqUpdateInfoBuilder struct {
	categoryId         string // 知识库分类ID
	categoryIdFlag     bool
	question           string // 问题
	questionFlag       bool
	answer             string // 答案
	answerFlag         bool
	answerRichtext     string // 富文本答案和答案必须有一个必填。Json Array格式，富文本结构请见[了解更多: 富文本](https://open.feishu.cn/document/ukTMukTMukTM/uITM0YjLyEDN24iMxQjN)
	answerRichtextFlag bool
	tags               []string // 相似问题
	tagsFlag           bool
}

func NewFaqUpdateInfoBuilder() *FaqUpdateInfoBuilder {
	builder := &FaqUpdateInfoBuilder{}
	return builder
}

// 知识库分类ID
//
// 示例值：6836004780707807251
func (builder *FaqUpdateInfoBuilder) CategoryId(categoryId string) *FaqUpdateInfoBuilder {
	builder.categoryId = categoryId
	builder.categoryIdFlag = true
	return builder
}

// 问题
//
// 示例值：问题
func (builder *FaqUpdateInfoBuilder) Question(question string) *FaqUpdateInfoBuilder {
	builder.question = question
	builder.questionFlag = true
	return builder
}

// 答案
//
// 示例值：答案
func (builder *FaqUpdateInfoBuilder) Answer(answer string) *FaqUpdateInfoBuilder {
	builder.answer = answer
	builder.answerFlag = true
	return builder
}

// 富文本答案和答案必须有一个必填。Json Array格式，富文本结构请见[了解更多: 富文本](https://open.feishu.cn/document/ukTMukTMukTM/uITM0YjLyEDN24iMxQjN)
//
// 示例值：[{;						"content": "这只是一个测试，医保问题",;						"type": "text";					}]
func (builder *FaqUpdateInfoBuilder) AnswerRichtext(answerRichtext string) *FaqUpdateInfoBuilder {
	builder.answerRichtext = answerRichtext
	builder.answerRichtextFlag = true
	return builder
}

// 相似问题
//
// 示例值：["tag1","tag2","tag3"]
func (builder *FaqUpdateInfoBuilder) Tags(tags []string) *FaqUpdateInfoBuilder {
	builder.tags = tags
	builder.tagsFlag = true
	return builder
}

func (builder *FaqUpdateInfoBuilder) Build() *FaqUpdateInfo {
	req := &FaqUpdateInfo{}
	if builder.categoryIdFlag {
		req.CategoryId = &builder.categoryId

	}
	if builder.questionFlag {
		req.Question = &builder.question

	}
	if builder.answerFlag {
		req.Answer = &builder.answer

	}
	if builder.answerRichtextFlag {
		req.AnswerRichtext = &builder.answerRichtext

	}
	if builder.tagsFlag {
		req.Tags = builder.tags
	}
	return req
}

type Notification struct {
	Id                          *string                   `json:"id,omitempty"`                              // 非必填，创建成功后返回
	JobName                     *string                   `json:"job_name,omitempty"`                        // 必填，任务名称
	Status                      *int                      `json:"status,omitempty"`                          // 非必填，创建成功后返回
	CreateUser                  *NotificationUser         `json:"create_user,omitempty"`                     // 非必填，创建人
	CreatedAt                   *string                   `json:"created_at,omitempty"`                      // 非必填，创建时间（毫秒时间戳）
	UpdateUser                  *NotificationUser         `json:"update_user,omitempty"`                     // 非必填，更新用户
	UpdatedAt                   *string                   `json:"updated_at,omitempty"`                      // 非必填，更新时间（毫秒时间戳）
	TargetUserCount             *int                      `json:"target_user_count,omitempty"`               // 非必填，目标推送用户总数
	SentUserCount               *int                      `json:"sent_user_count,omitempty"`                 // 非必填，已推送用户总数
	ReadUserCount               *int                      `json:"read_user_count,omitempty"`                 // 非必填，已读用户总数
	SendAt                      *string                   `json:"send_at,omitempty"`                         // 非必填，推送任务触发时间（毫秒时间戳）
	PushContent                 *string                   `json:"push_content,omitempty"`                    // 必填，推送内容，详见：https://open.feishu.cn/tool/cardbuilder?from=howtoguide
	PushType                    *int                      `json:"push_type,omitempty"`                       // 必填，;0（定时推送：push_scope不能等于3） 1（新人入职推送：push_scope必须等于1或者3；new_staff_scope_type不能为空）
	PushScopeType               *int                      `json:"push_scope_type,omitempty"`                 // 必填，;推送范围（服务台私信） 0：组织内全部成员（user_list和department_list必须为空） 1：不推送任何成员（user_list和department_list必须为空，chat_list不可为空） 2：推送到部分成员（user_list或department_list不能为空） 3：入职新人 以上四种状态，chat_list都相对独立，只有在推送范围为1时，必须需要设置chat_list
	NewStaffScopeType           *int                      `json:"new_staff_scope_type,omitempty"`            // 非必填，;新人入职范围类型（push_type为1时生效） 0：组织内所有新人 1：组织内特定的部门（new_staff_scope_department_list 字段不能为空）
	NewStaffScopeDepartmentList []*NotificationDepartment `json:"new_staff_scope_department_list,omitempty"` // 非必填，新人入职生效部门列表
	UserList                    []*NotificationUser       `json:"user_list,omitempty"`                       // 非必填，push推送到成员列表
	DepartmentList              []*NotificationDepartment `json:"department_list,omitempty"`                 // 非必填，push推送到的部门信息列表
	ChatList                    []*NotificationChat       `json:"chat_list,omitempty"`                       // 非必填，push推送到的会话列表(群)
	Ext                         *string                   `json:"ext,omitempty"`                             // 非必填，预留扩展字段
}

type NotificationBuilder struct {
	id                              string // 非必填，创建成功后返回
	idFlag                          bool
	jobName                         string // 必填，任务名称
	jobNameFlag                     bool
	status                          int // 非必填，创建成功后返回
	statusFlag                      bool
	createUser                      *NotificationUser // 非必填，创建人
	createUserFlag                  bool
	createdAt                       string // 非必填，创建时间（毫秒时间戳）
	createdAtFlag                   bool
	updateUser                      *NotificationUser // 非必填，更新用户
	updateUserFlag                  bool
	updatedAt                       string // 非必填，更新时间（毫秒时间戳）
	updatedAtFlag                   bool
	targetUserCount                 int // 非必填，目标推送用户总数
	targetUserCountFlag             bool
	sentUserCount                   int // 非必填，已推送用户总数
	sentUserCountFlag               bool
	readUserCount                   int // 非必填，已读用户总数
	readUserCountFlag               bool
	sendAt                          string // 非必填，推送任务触发时间（毫秒时间戳）
	sendAtFlag                      bool
	pushContent                     string // 必填，推送内容，详见：https://open.feishu.cn/tool/cardbuilder?from=howtoguide
	pushContentFlag                 bool
	pushType                        int // 必填，;0（定时推送：push_scope不能等于3） 1（新人入职推送：push_scope必须等于1或者3；new_staff_scope_type不能为空）
	pushTypeFlag                    bool
	pushScopeType                   int // 必填，;推送范围（服务台私信） 0：组织内全部成员（user_list和department_list必须为空） 1：不推送任何成员（user_list和department_list必须为空，chat_list不可为空） 2：推送到部分成员（user_list或department_list不能为空） 3：入职新人 以上四种状态，chat_list都相对独立，只有在推送范围为1时，必须需要设置chat_list
	pushScopeTypeFlag               bool
	newStaffScopeType               int // 非必填，;新人入职范围类型（push_type为1时生效） 0：组织内所有新人 1：组织内特定的部门（new_staff_scope_department_list 字段不能为空）
	newStaffScopeTypeFlag           bool
	newStaffScopeDepartmentList     []*NotificationDepartment // 非必填，新人入职生效部门列表
	newStaffScopeDepartmentListFlag bool
	userList                        []*NotificationUser // 非必填，push推送到成员列表
	userListFlag                    bool
	departmentList                  []*NotificationDepartment // 非必填，push推送到的部门信息列表
	departmentListFlag              bool
	chatList                        []*NotificationChat // 非必填，push推送到的会话列表(群)
	chatListFlag                    bool
	ext                             string // 非必填，预留扩展字段
	extFlag                         bool
}

func NewNotificationBuilder() *NotificationBuilder {
	builder := &NotificationBuilder{}
	return builder
}

// 非必填，创建成功后返回
//
// 示例值：6981801914270744596
func (builder *NotificationBuilder) Id(id string) *NotificationBuilder {
	builder.id = id
	builder.idFlag = true
	return builder
}

// 必填，任务名称
//
// 示例值：测试推送任务
func (builder *NotificationBuilder) JobName(jobName string) *NotificationBuilder {
	builder.jobName = jobName
	builder.jobNameFlag = true
	return builder
}

// 非必填，创建成功后返回
//
// 示例值：0
func (builder *NotificationBuilder) Status(status int) *NotificationBuilder {
	builder.status = status
	builder.statusFlag = true
	return builder
}

// 非必填，创建人
//
// 示例值：{"avatar_url":"","name":"","user_id":"ou_7277fd1262bfafc363d5b2a1f9c2ac90"}
func (builder *NotificationBuilder) CreateUser(createUser *NotificationUser) *NotificationBuilder {
	builder.createUser = createUser
	builder.createUserFlag = true
	return builder
}

// 非必填，创建时间（毫秒时间戳）
//
// 示例值：1626332244719
func (builder *NotificationBuilder) CreatedAt(createdAt string) *NotificationBuilder {
	builder.createdAt = createdAt
	builder.createdAtFlag = true
	return builder
}

// 非必填，更新用户
//
// 示例值：{"avatar_url":"","name":"","user_id":"ou_7277fd1262bfafc363d5b2a1f9c2ac90"}
func (builder *NotificationBuilder) UpdateUser(updateUser *NotificationUser) *NotificationBuilder {
	builder.updateUser = updateUser
	builder.updateUserFlag = true
	return builder
}

// 非必填，更新时间（毫秒时间戳）
//
// 示例值：1626332244719
func (builder *NotificationBuilder) UpdatedAt(updatedAt string) *NotificationBuilder {
	builder.updatedAt = updatedAt
	builder.updatedAtFlag = true
	return builder
}

// 非必填，目标推送用户总数
//
// 示例值：1
func (builder *NotificationBuilder) TargetUserCount(targetUserCount int) *NotificationBuilder {
	builder.targetUserCount = targetUserCount
	builder.targetUserCountFlag = true
	return builder
}

// 非必填，已推送用户总数
//
// 示例值：1
func (builder *NotificationBuilder) SentUserCount(sentUserCount int) *NotificationBuilder {
	builder.sentUserCount = sentUserCount
	builder.sentUserCountFlag = true
	return builder
}

// 非必填，已读用户总数
//
// 示例值：1
func (builder *NotificationBuilder) ReadUserCount(readUserCount int) *NotificationBuilder {
	builder.readUserCount = readUserCount
	builder.readUserCountFlag = true
	return builder
}

// 非必填，推送任务触发时间（毫秒时间戳）
//
// 示例值：1626332244719
func (builder *NotificationBuilder) SendAt(sendAt string) *NotificationBuilder {
	builder.sendAt = sendAt
	builder.sendAtFlag = true
	return builder
}

// 必填，推送内容，详见：https://open.feishu.cn/tool/cardbuilder?from=howtoguide
//
// 示例值：{   \"config\": {	 \"wide_screen_mode\": true   },   \"elements\": [	 {	   \"tag\": \"div\",	   \"text\": {		 \"tag\": \"lark_md\",		 \"content\": \"[飞书](https://www.feishu.cn)整合即时沟通、日历、音视频会议、云文档、云盘、工作台等功能于一体，成就组织和个人，更高效、更愉悦。\"	   }	 }   ] }
func (builder *NotificationBuilder) PushContent(pushContent string) *NotificationBuilder {
	builder.pushContent = pushContent
	builder.pushContentFlag = true
	return builder
}

// 必填，;0（定时推送：push_scope不能等于3） 1（新人入职推送：push_scope必须等于1或者3；new_staff_scope_type不能为空）
//
// 示例值：0
func (builder *NotificationBuilder) PushType(pushType int) *NotificationBuilder {
	builder.pushType = pushType
	builder.pushTypeFlag = true
	return builder
}

// 必填，;推送范围（服务台私信） 0：组织内全部成员（user_list和department_list必须为空） 1：不推送任何成员（user_list和department_list必须为空，chat_list不可为空） 2：推送到部分成员（user_list或department_list不能为空） 3：入职新人 以上四种状态，chat_list都相对独立，只有在推送范围为1时，必须需要设置chat_list
//
// 示例值：0
func (builder *NotificationBuilder) PushScopeType(pushScopeType int) *NotificationBuilder {
	builder.pushScopeType = pushScopeType
	builder.pushScopeTypeFlag = true
	return builder
}

// 非必填，;新人入职范围类型（push_type为1时生效） 0：组织内所有新人 1：组织内特定的部门（new_staff_scope_department_list 字段不能为空）
//
// 示例值：0
func (builder *NotificationBuilder) NewStaffScopeType(newStaffScopeType int) *NotificationBuilder {
	builder.newStaffScopeType = newStaffScopeType
	builder.newStaffScopeTypeFlag = true
	return builder
}

// 非必填，新人入职生效部门列表
//
// 示例值：[{"department_id":"od_7c1a2815c9846b5e518b950de0e62de8"}]
func (builder *NotificationBuilder) NewStaffScopeDepartmentList(newStaffScopeDepartmentList []*NotificationDepartment) *NotificationBuilder {
	builder.newStaffScopeDepartmentList = newStaffScopeDepartmentList
	builder.newStaffScopeDepartmentListFlag = true
	return builder
}

// 非必填，push推送到成员列表
//
// 示例值：[{"user_id":"ou_7277fd1262bfafc363d5b2a1f9c2ac90"}]
func (builder *NotificationBuilder) UserList(userList []*NotificationUser) *NotificationBuilder {
	builder.userList = userList
	builder.userListFlag = true
	return builder
}

// 非必填，push推送到的部门信息列表
//
// 示例值：[{"department_id":"od_7c1a2815c9846b5e518b950de0e62de8"}]
func (builder *NotificationBuilder) DepartmentList(departmentList []*NotificationDepartment) *NotificationBuilder {
	builder.departmentList = departmentList
	builder.departmentListFlag = true
	return builder
}

// 非必填，push推送到的会话列表(群)
//
// 示例值：[{"chat_id":"oc_7c1a2815c9846b5e518b950de0e62de8"}]
func (builder *NotificationBuilder) ChatList(chatList []*NotificationChat) *NotificationBuilder {
	builder.chatList = chatList
	builder.chatListFlag = true
	return builder
}

// 非必填，预留扩展字段
//
// 示例值：{}
func (builder *NotificationBuilder) Ext(ext string) *NotificationBuilder {
	builder.ext = ext
	builder.extFlag = true
	return builder
}

func (builder *NotificationBuilder) Build() *Notification {
	req := &Notification{}
	if builder.idFlag {
		req.Id = &builder.id

	}
	if builder.jobNameFlag {
		req.JobName = &builder.jobName

	}
	if builder.statusFlag {
		req.Status = &builder.status

	}
	if builder.createUserFlag {
		req.CreateUser = builder.createUser
	}
	if builder.createdAtFlag {
		req.CreatedAt = &builder.createdAt

	}
	if builder.updateUserFlag {
		req.UpdateUser = builder.updateUser
	}
	if builder.updatedAtFlag {
		req.UpdatedAt = &builder.updatedAt

	}
	if builder.targetUserCountFlag {
		req.TargetUserCount = &builder.targetUserCount

	}
	if builder.sentUserCountFlag {
		req.SentUserCount = &builder.sentUserCount

	}
	if builder.readUserCountFlag {
		req.ReadUserCount = &builder.readUserCount

	}
	if builder.sendAtFlag {
		req.SendAt = &builder.sendAt

	}
	if builder.pushContentFlag {
		req.PushContent = &builder.pushContent

	}
	if builder.pushTypeFlag {
		req.PushType = &builder.pushType

	}
	if builder.pushScopeTypeFlag {
		req.PushScopeType = &builder.pushScopeType

	}
	if builder.newStaffScopeTypeFlag {
		req.NewStaffScopeType = &builder.newStaffScopeType

	}
	if builder.newStaffScopeDepartmentListFlag {
		req.NewStaffScopeDepartmentList = builder.newStaffScopeDepartmentList
	}
	if builder.userListFlag {
		req.UserList = builder.userList
	}
	if builder.departmentListFlag {
		req.DepartmentList = builder.departmentList
	}
	if builder.chatListFlag {
		req.ChatList = builder.chatList
	}
	if builder.extFlag {
		req.Ext = &builder.ext

	}
	return req
}

type NotificationChat struct {
	ChatId *string `json:"chat_id,omitempty"` // 非必填，会话ID
	Name   *string `json:"name,omitempty"`    // 非必填，会话名称
}

type NotificationChatBuilder struct {
	chatId     string // 非必填，会话ID
	chatIdFlag bool
	name       string // 非必填，会话名称
	nameFlag   bool
}

func NewNotificationChatBuilder() *NotificationChatBuilder {
	builder := &NotificationChatBuilder{}
	return builder
}

// 非必填，会话ID
//
// 示例值：oc_7277fd1262bfafc363d5b2a1f9c2ac90
func (builder *NotificationChatBuilder) ChatId(chatId string) *NotificationChatBuilder {
	builder.chatId = chatId
	builder.chatIdFlag = true
	return builder
}

// 非必填，会话名称
//
// 示例值：测试群聊
func (builder *NotificationChatBuilder) Name(name string) *NotificationChatBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

func (builder *NotificationChatBuilder) Build() *NotificationChat {
	req := &NotificationChat{}
	if builder.chatIdFlag {
		req.ChatId = &builder.chatId

	}
	if builder.nameFlag {
		req.Name = &builder.name

	}
	return req
}

type NotificationDepartment struct {
	DepartmentId *string `json:"department_id,omitempty"` // 部门ID
	Name         *string `json:"name,omitempty"`          // 非必填，部门名称
}

type NotificationDepartmentBuilder struct {
	departmentId     string // 部门ID
	departmentIdFlag bool
	name             string // 非必填，部门名称
	nameFlag         bool
}

func NewNotificationDepartmentBuilder() *NotificationDepartmentBuilder {
	builder := &NotificationDepartmentBuilder{}
	return builder
}

// 部门ID
//
// 示例值：od_7277fd1262bfafc363d5b2a1f9c2ac90
func (builder *NotificationDepartmentBuilder) DepartmentId(departmentId string) *NotificationDepartmentBuilder {
	builder.departmentId = departmentId
	builder.departmentIdFlag = true
	return builder
}

// 非必填，部门名称
//
// 示例值：测试部门
func (builder *NotificationDepartmentBuilder) Name(name string) *NotificationDepartmentBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

func (builder *NotificationDepartmentBuilder) Build() *NotificationDepartment {
	req := &NotificationDepartment{}
	if builder.departmentIdFlag {
		req.DepartmentId = &builder.departmentId

	}
	if builder.nameFlag {
		req.Name = &builder.name

	}
	return req
}

type NotificationUser struct {
	UserId    *string `json:"user_id,omitempty"`    // 非必填，用户id
	AvatarUrl *string `json:"avatar_url,omitempty"` // 非必填，头像地址
	Name      *string `json:"name,omitempty"`       // 非必填，用户名称
}

type NotificationUserBuilder struct {
	userId        string // 非必填，用户id
	userIdFlag    bool
	avatarUrl     string // 非必填，头像地址
	avatarUrlFlag bool
	name          string // 非必填，用户名称
	nameFlag      bool
}

func NewNotificationUserBuilder() *NotificationUserBuilder {
	builder := &NotificationUserBuilder{}
	return builder
}

// 非必填，用户id
//
// 示例值：ou_7277fd1262bfafc363d5b2a1f9c2ac90
func (builder *NotificationUserBuilder) UserId(userId string) *NotificationUserBuilder {
	builder.userId = userId
	builder.userIdFlag = true
	return builder
}

// 非必填，头像地址
//
// 示例值：http://*.com/*.png
func (builder *NotificationUserBuilder) AvatarUrl(avatarUrl string) *NotificationUserBuilder {
	builder.avatarUrl = avatarUrl
	builder.avatarUrlFlag = true
	return builder
}

// 非必填，用户名称
//
// 示例值：test
func (builder *NotificationUserBuilder) Name(name string) *NotificationUserBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

func (builder *NotificationUserBuilder) Build() *NotificationUser {
	req := &NotificationUser{}
	if builder.userIdFlag {
		req.UserId = &builder.userId

	}
	if builder.avatarUrlFlag {
		req.AvatarUrl = &builder.avatarUrl

	}
	if builder.nameFlag {
		req.Name = &builder.name

	}
	return req
}

type Richtext struct {
	Content *string `json:"content,omitempty"` // 内容
	Type    *string `json:"type,omitempty"`    // 类型
}

type RichtextBuilder struct {
	content     string // 内容
	contentFlag bool
	type_       string // 类型
	typeFlag    bool
}

func NewRichtextBuilder() *RichtextBuilder {
	builder := &RichtextBuilder{}
	return builder
}

// 内容
//
// 示例值：我的答案
func (builder *RichtextBuilder) Content(content string) *RichtextBuilder {
	builder.content = content
	builder.contentFlag = true
	return builder
}

// 类型
//
// 示例值：text
func (builder *RichtextBuilder) Type(type_ string) *RichtextBuilder {
	builder.type_ = type_
	builder.typeFlag = true
	return builder
}

func (builder *RichtextBuilder) Build() *Richtext {
	req := &Richtext{}
	if builder.contentFlag {
		req.Content = &builder.content

	}
	if builder.typeFlag {
		req.Type = &builder.type_

	}
	return req
}

type Ticket struct {
	TicketId                   *string                       `json:"ticket_id,omitempty"`                     // 工单ID;;[可以从工单列表里面取](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/helpdesk-v1/ticket/list);;[也可以订阅工单创建事件获取](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/helpdesk-v1/ticket/events/created)
	HelpdeskId                 *string                       `json:"helpdesk_id,omitempty"`                   // 服务台ID
	Guest                      *TicketUser                   `json:"guest,omitempty"`                         // 工单创建用户
	Comments                   *Comments                     `json:"comments,omitempty"`                      // 备注
	TicketType                 *int                          `json:"ticket_type,omitempty"`                   // 工单阶段：1. 机器人 2. 人工
	Status                     *int                          `json:"status,omitempty"`                        // 工单状态，1：已创建 2: 处理中 3: 排队中 4：待定 5：待用户响应 50: 被机器人关闭 51: 被客服关闭 52: 用户自己关闭
	Score                      *int                          `json:"score,omitempty"`                         // 工单评分，1：不满意，2:一般，3:满意
	CreatedAt                  *int                          `json:"created_at,omitempty"`                    // 工单创建时间
	UpdatedAt                  *int                          `json:"updated_at,omitempty"`                    // 工单更新时间，没有值时为-1
	ClosedAt                   *int                          `json:"closed_at,omitempty"`                     // 工单结束时间
	DissatisfactionReason      []string                      `json:"dissatisfaction_reason,omitempty"`        // 不满意原因
	Agents                     []*TicketUser                 `json:"agents,omitempty"`                        // 工单客服
	Channel                    *int                          `json:"channel,omitempty"`                       // 工单渠道，描述：;9：Open API 2：二维码 14：分享 13：搜索 其他数字：其他渠道
	Solve                      *int                          `json:"solve,omitempty"`                         // 工单是否解决 1:没解决 2:已解决
	ClosedBy                   *TicketUser                   `json:"closed_by,omitempty"`                     // 关单用户ID
	Collaborators              []*TicketUser                 `json:"collaborators,omitempty"`                 // 工单协作者
	CustomizedFields           []*CustomizedFieldDisplayItem `json:"customized_fields,omitempty"`             // 自定义字段列表，没有值时不设置  ;下拉菜单的value对应工单字段里面的children.display_name;[获取全部工单自定义字段](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/helpdesk-v1/ticket_customized_field/list-ticket-customized-fields)
	AgentServiceDuration       *float64                      `json:"agent_service_duration,omitempty"`        // 客服服务时长，客服最后一次回复时间距离客服进入时间间隔，单位分钟
	AgentFirstResponseDuration *int                          `json:"agent_first_response_duration,omitempty"` // 客服首次回复时间距离客服进入时间的间隔(秒)
	BotServiceDuration         *int                          `json:"bot_service_duration,omitempty"`          // 机器人服务时间：客服进入时间距离工单创建时间的间隔，单位秒
	AgentResolutionTime        *int                          `json:"agent_resolution_time,omitempty"`         // 客服解决时长，关单时间距离客服进入时间的间隔，单位秒
	ActualProcessingTime       *int                          `json:"actual_processing_time,omitempty"`        // 工单实际处理时间：从客服进入到关单，单位秒
	AgentEntryTime             *int                          `json:"agent_entry_time,omitempty"`              // 客服进入时间，单位毫秒
	AgentFirstResponseTime     *int                          `json:"agent_first_response_time,omitempty"`     // 客服首次回复时间，单位毫秒
	AgentLastResponseTime      *int                          `json:"agent_last_response_time,omitempty"`      // 客服最后回复时间，单位毫秒
	AgentOwner                 *TicketUser                   `json:"agent_owner,omitempty"`                   // 主责客服
}

type TicketBuilder struct {
	ticketId                       string // 工单ID;;[可以从工单列表里面取](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/helpdesk-v1/ticket/list);;[也可以订阅工单创建事件获取](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/helpdesk-v1/ticket/events/created)
	ticketIdFlag                   bool
	helpdeskId                     string // 服务台ID
	helpdeskIdFlag                 bool
	guest                          *TicketUser // 工单创建用户
	guestFlag                      bool
	comments                       *Comments // 备注
	commentsFlag                   bool
	ticketType                     int // 工单阶段：1. 机器人 2. 人工
	ticketTypeFlag                 bool
	status                         int // 工单状态，1：已创建 2: 处理中 3: 排队中 4：待定 5：待用户响应 50: 被机器人关闭 51: 被客服关闭 52: 用户自己关闭
	statusFlag                     bool
	score                          int // 工单评分，1：不满意，2:一般，3:满意
	scoreFlag                      bool
	createdAt                      int // 工单创建时间
	createdAtFlag                  bool
	updatedAt                      int // 工单更新时间，没有值时为-1
	updatedAtFlag                  bool
	closedAt                       int // 工单结束时间
	closedAtFlag                   bool
	dissatisfactionReason          []string // 不满意原因
	dissatisfactionReasonFlag      bool
	agents                         []*TicketUser // 工单客服
	agentsFlag                     bool
	channel                        int // 工单渠道，描述：;9：Open API 2：二维码 14：分享 13：搜索 其他数字：其他渠道
	channelFlag                    bool
	solve                          int // 工单是否解决 1:没解决 2:已解决
	solveFlag                      bool
	closedBy                       *TicketUser // 关单用户ID
	closedByFlag                   bool
	collaborators                  []*TicketUser // 工单协作者
	collaboratorsFlag              bool
	customizedFields               []*CustomizedFieldDisplayItem // 自定义字段列表，没有值时不设置  ;下拉菜单的value对应工单字段里面的children.display_name;[获取全部工单自定义字段](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/helpdesk-v1/ticket_customized_field/list-ticket-customized-fields)
	customizedFieldsFlag           bool
	agentServiceDuration           float64 // 客服服务时长，客服最后一次回复时间距离客服进入时间间隔，单位分钟
	agentServiceDurationFlag       bool
	agentFirstResponseDuration     int // 客服首次回复时间距离客服进入时间的间隔(秒)
	agentFirstResponseDurationFlag bool
	botServiceDuration             int // 机器人服务时间：客服进入时间距离工单创建时间的间隔，单位秒
	botServiceDurationFlag         bool
	agentResolutionTime            int // 客服解决时长，关单时间距离客服进入时间的间隔，单位秒
	agentResolutionTimeFlag        bool
	actualProcessingTime           int // 工单实际处理时间：从客服进入到关单，单位秒
	actualProcessingTimeFlag       bool
	agentEntryTime                 int // 客服进入时间，单位毫秒
	agentEntryTimeFlag             bool
	agentFirstResponseTime         int // 客服首次回复时间，单位毫秒
	agentFirstResponseTimeFlag     bool
	agentLastResponseTime          int // 客服最后回复时间，单位毫秒
	agentLastResponseTimeFlag      bool
	agentOwner                     *TicketUser // 主责客服
	agentOwnerFlag                 bool
}

func NewTicketBuilder() *TicketBuilder {
	builder := &TicketBuilder{}
	return builder
}

// 工单ID;;[可以从工单列表里面取](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/helpdesk-v1/ticket/list);;[也可以订阅工单创建事件获取](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/helpdesk-v1/ticket/events/created)
//
// 示例值：6626871355780366331
func (builder *TicketBuilder) TicketId(ticketId string) *TicketBuilder {
	builder.ticketId = ticketId
	builder.ticketIdFlag = true
	return builder
}

// 服务台ID
//
// 示例值：6626871355780366330
func (builder *TicketBuilder) HelpdeskId(helpdeskId string) *TicketBuilder {
	builder.helpdeskId = helpdeskId
	builder.helpdeskIdFlag = true
	return builder
}

// 工单创建用户
//
// 示例值：
func (builder *TicketBuilder) Guest(guest *TicketUser) *TicketBuilder {
	builder.guest = guest
	builder.guestFlag = true
	return builder
}

// 备注
//
// 示例值：
func (builder *TicketBuilder) Comments(comments *Comments) *TicketBuilder {
	builder.comments = comments
	builder.commentsFlag = true
	return builder
}

// 工单阶段：1. 机器人 2. 人工
//
// 示例值：1
func (builder *TicketBuilder) TicketType(ticketType int) *TicketBuilder {
	builder.ticketType = ticketType
	builder.ticketTypeFlag = true
	return builder
}

// 工单状态，1：已创建 2: 处理中 3: 排队中 4：待定 5：待用户响应 50: 被机器人关闭 51: 被客服关闭 52: 用户自己关闭
//
// 示例值：50
func (builder *TicketBuilder) Status(status int) *TicketBuilder {
	builder.status = status
	builder.statusFlag = true
	return builder
}

// 工单评分，1：不满意，2:一般，3:满意
//
// 示例值：1
func (builder *TicketBuilder) Score(score int) *TicketBuilder {
	builder.score = score
	builder.scoreFlag = true
	return builder
}

// 工单创建时间
//
// 示例值：1616920429000
func (builder *TicketBuilder) CreatedAt(createdAt int) *TicketBuilder {
	builder.createdAt = createdAt
	builder.createdAtFlag = true
	return builder
}

// 工单更新时间，没有值时为-1
//
// 示例值：1616920429000
func (builder *TicketBuilder) UpdatedAt(updatedAt int) *TicketBuilder {
	builder.updatedAt = updatedAt
	builder.updatedAtFlag = true
	return builder
}

// 工单结束时间
//
// 示例值：1616920429000
func (builder *TicketBuilder) ClosedAt(closedAt int) *TicketBuilder {
	builder.closedAt = closedAt
	builder.closedAtFlag = true
	return builder
}

// 不满意原因
//
// 示例值：
func (builder *TicketBuilder) DissatisfactionReason(dissatisfactionReason []string) *TicketBuilder {
	builder.dissatisfactionReason = dissatisfactionReason
	builder.dissatisfactionReasonFlag = true
	return builder
}

// 工单客服
//
// 示例值：
func (builder *TicketBuilder) Agents(agents []*TicketUser) *TicketBuilder {
	builder.agents = agents
	builder.agentsFlag = true
	return builder
}

// 工单渠道，描述：;9：Open API 2：二维码 14：分享 13：搜索 其他数字：其他渠道
//
// 示例值：0
func (builder *TicketBuilder) Channel(channel int) *TicketBuilder {
	builder.channel = channel
	builder.channelFlag = true
	return builder
}

// 工单是否解决 1:没解决 2:已解决
//
// 示例值：1
func (builder *TicketBuilder) Solve(solve int) *TicketBuilder {
	builder.solve = solve
	builder.solveFlag = true
	return builder
}

// 关单用户ID
//
// 示例值：
func (builder *TicketBuilder) ClosedBy(closedBy *TicketUser) *TicketBuilder {
	builder.closedBy = closedBy
	builder.closedByFlag = true
	return builder
}

// 工单协作者
//
// 示例值：
func (builder *TicketBuilder) Collaborators(collaborators []*TicketUser) *TicketBuilder {
	builder.collaborators = collaborators
	builder.collaboratorsFlag = true
	return builder
}

// 自定义字段列表，没有值时不设置  ;下拉菜单的value对应工单字段里面的children.display_name;[获取全部工单自定义字段](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/helpdesk-v1/ticket_customized_field/list-ticket-customized-fields)
//
// 示例值：
func (builder *TicketBuilder) CustomizedFields(customizedFields []*CustomizedFieldDisplayItem) *TicketBuilder {
	builder.customizedFields = customizedFields
	builder.customizedFieldsFlag = true
	return builder
}

// 客服服务时长，客服最后一次回复时间距离客服进入时间间隔，单位分钟
//
// 示例值：42624.95
func (builder *TicketBuilder) AgentServiceDuration(agentServiceDuration float64) *TicketBuilder {
	builder.agentServiceDuration = agentServiceDuration
	builder.agentServiceDurationFlag = true
	return builder
}

// 客服首次回复时间距离客服进入时间的间隔(秒)
//
// 示例值：123869
func (builder *TicketBuilder) AgentFirstResponseDuration(agentFirstResponseDuration int) *TicketBuilder {
	builder.agentFirstResponseDuration = agentFirstResponseDuration
	builder.agentFirstResponseDurationFlag = true
	return builder
}

// 机器人服务时间：客服进入时间距离工单创建时间的间隔，单位秒
//
// 示例值：1
func (builder *TicketBuilder) BotServiceDuration(botServiceDuration int) *TicketBuilder {
	builder.botServiceDuration = botServiceDuration
	builder.botServiceDurationFlag = true
	return builder
}

// 客服解决时长，关单时间距离客服进入时间的间隔，单位秒
//
// 示例值：66
func (builder *TicketBuilder) AgentResolutionTime(agentResolutionTime int) *TicketBuilder {
	builder.agentResolutionTime = agentResolutionTime
	builder.agentResolutionTimeFlag = true
	return builder
}

// 工单实际处理时间：从客服进入到关单，单位秒
//
// 示例值：68
func (builder *TicketBuilder) ActualProcessingTime(actualProcessingTime int) *TicketBuilder {
	builder.actualProcessingTime = actualProcessingTime
	builder.actualProcessingTimeFlag = true
	return builder
}

// 客服进入时间，单位毫秒
//
// 示例值：1636444596000
func (builder *TicketBuilder) AgentEntryTime(agentEntryTime int) *TicketBuilder {
	builder.agentEntryTime = agentEntryTime
	builder.agentEntryTimeFlag = true
	return builder
}

// 客服首次回复时间，单位毫秒
//
// 示例值：1636444696000
func (builder *TicketBuilder) AgentFirstResponseTime(agentFirstResponseTime int) *TicketBuilder {
	builder.agentFirstResponseTime = agentFirstResponseTime
	builder.agentFirstResponseTimeFlag = true
	return builder
}

// 客服最后回复时间，单位毫秒
//
// 示例值：1636444796000
func (builder *TicketBuilder) AgentLastResponseTime(agentLastResponseTime int) *TicketBuilder {
	builder.agentLastResponseTime = agentLastResponseTime
	builder.agentLastResponseTimeFlag = true
	return builder
}

// 主责客服
//
// 示例值：
func (builder *TicketBuilder) AgentOwner(agentOwner *TicketUser) *TicketBuilder {
	builder.agentOwner = agentOwner
	builder.agentOwnerFlag = true
	return builder
}

func (builder *TicketBuilder) Build() *Ticket {
	req := &Ticket{}
	if builder.ticketIdFlag {
		req.TicketId = &builder.ticketId

	}
	if builder.helpdeskIdFlag {
		req.HelpdeskId = &builder.helpdeskId

	}
	if builder.guestFlag {
		req.Guest = builder.guest
	}
	if builder.commentsFlag {
		req.Comments = builder.comments
	}
	if builder.ticketTypeFlag {
		req.TicketType = &builder.ticketType

	}
	if builder.statusFlag {
		req.Status = &builder.status

	}
	if builder.scoreFlag {
		req.Score = &builder.score

	}
	if builder.createdAtFlag {
		req.CreatedAt = &builder.createdAt

	}
	if builder.updatedAtFlag {
		req.UpdatedAt = &builder.updatedAt

	}
	if builder.closedAtFlag {
		req.ClosedAt = &builder.closedAt

	}
	if builder.dissatisfactionReasonFlag {
		req.DissatisfactionReason = builder.dissatisfactionReason
	}
	if builder.agentsFlag {
		req.Agents = builder.agents
	}
	if builder.channelFlag {
		req.Channel = &builder.channel

	}
	if builder.solveFlag {
		req.Solve = &builder.solve

	}
	if builder.closedByFlag {
		req.ClosedBy = builder.closedBy
	}
	if builder.collaboratorsFlag {
		req.Collaborators = builder.collaborators
	}
	if builder.customizedFieldsFlag {
		req.CustomizedFields = builder.customizedFields
	}
	if builder.agentServiceDurationFlag {
		req.AgentServiceDuration = &builder.agentServiceDuration

	}
	if builder.agentFirstResponseDurationFlag {
		req.AgentFirstResponseDuration = &builder.agentFirstResponseDuration

	}
	if builder.botServiceDurationFlag {
		req.BotServiceDuration = &builder.botServiceDuration

	}
	if builder.agentResolutionTimeFlag {
		req.AgentResolutionTime = &builder.agentResolutionTime

	}
	if builder.actualProcessingTimeFlag {
		req.ActualProcessingTime = &builder.actualProcessingTime

	}
	if builder.agentEntryTimeFlag {
		req.AgentEntryTime = &builder.agentEntryTime

	}
	if builder.agentFirstResponseTimeFlag {
		req.AgentFirstResponseTime = &builder.agentFirstResponseTime

	}
	if builder.agentLastResponseTimeFlag {
		req.AgentLastResponseTime = &builder.agentLastResponseTime

	}
	if builder.agentOwnerFlag {
		req.AgentOwner = builder.agentOwner
	}
	return req
}

type TicketMessage struct {
	Id          *string `json:"id,omitempty"`           // 工单消息ID
	MessageId   *string `json:"message_id,omitempty"`   // chat消息ID
	MessageType *string `json:"message_type,omitempty"` // 消息类型；text：纯文本；post：富文本
	CreatedAt   *int    `json:"created_at,omitempty"`   // 创建时间
	Content     *string `json:"content,omitempty"`      // 内容
	UserName    *string `json:"user_name,omitempty"`    // 用户名
	AvatarUrl   *string `json:"avatar_url,omitempty"`   // 用户图片url
	UserId      *string `json:"user_id,omitempty"`      // 用户open ID
}

type TicketMessageBuilder struct {
	id              string // 工单消息ID
	idFlag          bool
	messageId       string // chat消息ID
	messageIdFlag   bool
	messageType     string // 消息类型；text：纯文本；post：富文本
	messageTypeFlag bool
	createdAt       int // 创建时间
	createdAtFlag   bool
	content         string // 内容
	contentFlag     bool
	userName        string // 用户名
	userNameFlag    bool
	avatarUrl       string // 用户图片url
	avatarUrlFlag   bool
	userId          string // 用户open ID
	userIdFlag      bool
}

func NewTicketMessageBuilder() *TicketMessageBuilder {
	builder := &TicketMessageBuilder{}
	return builder
}

// 工单消息ID
//
// 示例值：6948728206392295444
func (builder *TicketMessageBuilder) Id(id string) *TicketMessageBuilder {
	builder.id = id
	builder.idFlag = true
	return builder
}

// chat消息ID
//
// 示例值：6949088236610273307
func (builder *TicketMessageBuilder) MessageId(messageId string) *TicketMessageBuilder {
	builder.messageId = messageId
	builder.messageIdFlag = true
	return builder
}

// 消息类型；text：纯文本；post：富文本
//
// 示例值：text
func (builder *TicketMessageBuilder) MessageType(messageType string) *TicketMessageBuilder {
	builder.messageType = messageType
	builder.messageTypeFlag = true
	return builder
}

// 创建时间
//
// 示例值：1617960686000
func (builder *TicketMessageBuilder) CreatedAt(createdAt int) *TicketMessageBuilder {
	builder.createdAt = createdAt
	builder.createdAtFlag = true
	return builder
}

// 内容
//
// 示例值："{\"content\":\"进入人工服务。 @李宁  为你提供服务，开始聊起来吧~\",\"msg_type\":\"text\"}"
func (builder *TicketMessageBuilder) Content(content string) *TicketMessageBuilder {
	builder.content = content
	builder.contentFlag = true
	return builder
}

// 用户名
//
// 示例值：李宁
func (builder *TicketMessageBuilder) UserName(userName string) *TicketMessageBuilder {
	builder.userName = userName
	builder.userNameFlag = true
	return builder
}

// 用户图片url
//
// 示例值：https://internal-api-lark-file.feishu-boe.cn/static-resource/v1/3e73cdce-54b0-4c6a-8226-b131fb2825dj~?image_size=72x72&cut_type=&quality=&format=image&sticker_format=.webp
func (builder *TicketMessageBuilder) AvatarUrl(avatarUrl string) *TicketMessageBuilder {
	builder.avatarUrl = avatarUrl
	builder.avatarUrlFlag = true
	return builder
}

// 用户open ID
//
// 示例值：ou_37019b7c830210acd88fdce886e25c71
func (builder *TicketMessageBuilder) UserId(userId string) *TicketMessageBuilder {
	builder.userId = userId
	builder.userIdFlag = true
	return builder
}

func (builder *TicketMessageBuilder) Build() *TicketMessage {
	req := &TicketMessage{}
	if builder.idFlag {
		req.Id = &builder.id

	}
	if builder.messageIdFlag {
		req.MessageId = &builder.messageId

	}
	if builder.messageTypeFlag {
		req.MessageType = &builder.messageType

	}
	if builder.createdAtFlag {
		req.CreatedAt = &builder.createdAt

	}
	if builder.contentFlag {
		req.Content = &builder.content

	}
	if builder.userNameFlag {
		req.UserName = &builder.userName

	}
	if builder.avatarUrlFlag {
		req.AvatarUrl = &builder.avatarUrl

	}
	if builder.userIdFlag {
		req.UserId = &builder.userId

	}
	return req
}

type TicketCustomizedField struct {
	TicketCustomizedFieldId *string     `json:"ticket_customized_field_id,omitempty"` // 工单自定义字段ID
	HelpdeskId              *string     `json:"helpdesk_id,omitempty"`                // 服务台ID
	KeyName                 *string     `json:"key_name,omitempty"`                   // 键名
	DisplayName             *string     `json:"display_name,omitempty"`               // 名称
	Position                *string     `json:"position,omitempty"`                   // 字段在列表后台管理列表中的位置
	FieldType               *string     `json:"field_type,omitempty"`                 // 类型;;string - 单行文本;;multiline - 多行文本;;dropdown - 下拉列表;;dropdown_nested - 级联下拉
	Description             *string     `json:"description,omitempty"`                // 描述
	Visible                 *bool       `json:"visible,omitempty"`                    // 是否可见
	Editable                *bool       `json:"editable,omitempty"`                   // 是否可以修改
	Required                *bool       `json:"required,omitempty"`                   // 是否必填
	CreatedAt               *string     `json:"created_at,omitempty"`                 // 创建时间
	UpdatedAt               *string     `json:"updated_at,omitempty"`                 // 更新时间
	CreatedBy               *TicketUser `json:"created_by,omitempty"`                 // 创建用户
	UpdatedBy               *TicketUser `json:"updated_by,omitempty"`                 // 更新用户
	DropdownAllowMultiple   *bool       `json:"dropdown_allow_multiple,omitempty"`    // 是否支持多选，仅在字段类型是dropdown的时候有效
}

type TicketCustomizedFieldBuilder struct {
	ticketCustomizedFieldId     string // 工单自定义字段ID
	ticketCustomizedFieldIdFlag bool
	helpdeskId                  string // 服务台ID
	helpdeskIdFlag              bool
	keyName                     string // 键名
	keyNameFlag                 bool
	displayName                 string // 名称
	displayNameFlag             bool
	position                    string // 字段在列表后台管理列表中的位置
	positionFlag                bool
	fieldType                   string // 类型;;string - 单行文本;;multiline - 多行文本;;dropdown - 下拉列表;;dropdown_nested - 级联下拉
	fieldTypeFlag               bool
	description                 string // 描述
	descriptionFlag             bool
	visible                     bool // 是否可见
	visibleFlag                 bool
	editable                    bool // 是否可以修改
	editableFlag                bool
	required                    bool // 是否必填
	requiredFlag                bool
	createdAt                   string // 创建时间
	createdAtFlag               bool
	updatedAt                   string // 更新时间
	updatedAtFlag               bool
	createdBy                   *TicketUser // 创建用户
	createdByFlag               bool
	updatedBy                   *TicketUser // 更新用户
	updatedByFlag               bool
	dropdownAllowMultiple       bool // 是否支持多选，仅在字段类型是dropdown的时候有效
	dropdownAllowMultipleFlag   bool
}

func NewTicketCustomizedFieldBuilder() *TicketCustomizedFieldBuilder {
	builder := &TicketCustomizedFieldBuilder{}
	return builder
}

// 工单自定义字段ID
//
// 示例值：6834320707288072194
func (builder *TicketCustomizedFieldBuilder) TicketCustomizedFieldId(ticketCustomizedFieldId string) *TicketCustomizedFieldBuilder {
	builder.ticketCustomizedFieldId = ticketCustomizedFieldId
	builder.ticketCustomizedFieldIdFlag = true
	return builder
}

// 服务台ID
//
// 示例值：1542164574896126
func (builder *TicketCustomizedFieldBuilder) HelpdeskId(helpdeskId string) *TicketCustomizedFieldBuilder {
	builder.helpdeskId = helpdeskId
	builder.helpdeskIdFlag = true
	return builder
}

// 键名
//
// 示例值：test dropdown
func (builder *TicketCustomizedFieldBuilder) KeyName(keyName string) *TicketCustomizedFieldBuilder {
	builder.keyName = keyName
	builder.keyNameFlag = true
	return builder
}

// 名称
//
// 示例值：test dropdown
func (builder *TicketCustomizedFieldBuilder) DisplayName(displayName string) *TicketCustomizedFieldBuilder {
	builder.displayName = displayName
	builder.displayNameFlag = true
	return builder
}

// 字段在列表后台管理列表中的位置
//
// 示例值：3
func (builder *TicketCustomizedFieldBuilder) Position(position string) *TicketCustomizedFieldBuilder {
	builder.position = position
	builder.positionFlag = true
	return builder
}

// 类型;;string - 单行文本;;multiline - 多行文本;;dropdown - 下拉列表;;dropdown_nested - 级联下拉
//
// 示例值：dropdown
func (builder *TicketCustomizedFieldBuilder) FieldType(fieldType string) *TicketCustomizedFieldBuilder {
	builder.fieldType = fieldType
	builder.fieldTypeFlag = true
	return builder
}

// 描述
//
// 示例值：下拉示例
func (builder *TicketCustomizedFieldBuilder) Description(description string) *TicketCustomizedFieldBuilder {
	builder.description = description
	builder.descriptionFlag = true
	return builder
}

// 是否可见
//
// 示例值：true
func (builder *TicketCustomizedFieldBuilder) Visible(visible bool) *TicketCustomizedFieldBuilder {
	builder.visible = visible
	builder.visibleFlag = true
	return builder
}

// 是否可以修改
//
// 示例值：true
func (builder *TicketCustomizedFieldBuilder) Editable(editable bool) *TicketCustomizedFieldBuilder {
	builder.editable = editable
	builder.editableFlag = true
	return builder
}

// 是否必填
//
// 示例值：false
func (builder *TicketCustomizedFieldBuilder) Required(required bool) *TicketCustomizedFieldBuilder {
	builder.required = required
	builder.requiredFlag = true
	return builder
}

// 创建时间
//
// 示例值：1591239289000
func (builder *TicketCustomizedFieldBuilder) CreatedAt(createdAt string) *TicketCustomizedFieldBuilder {
	builder.createdAt = createdAt
	builder.createdAtFlag = true
	return builder
}

// 更新时间
//
// 示例值：1591239289000
func (builder *TicketCustomizedFieldBuilder) UpdatedAt(updatedAt string) *TicketCustomizedFieldBuilder {
	builder.updatedAt = updatedAt
	builder.updatedAtFlag = true
	return builder
}

// 创建用户
//
// 示例值：
func (builder *TicketCustomizedFieldBuilder) CreatedBy(createdBy *TicketUser) *TicketCustomizedFieldBuilder {
	builder.createdBy = createdBy
	builder.createdByFlag = true
	return builder
}

// 更新用户
//
// 示例值：
func (builder *TicketCustomizedFieldBuilder) UpdatedBy(updatedBy *TicketUser) *TicketCustomizedFieldBuilder {
	builder.updatedBy = updatedBy
	builder.updatedByFlag = true
	return builder
}

// 是否支持多选，仅在字段类型是dropdown的时候有效
//
// 示例值：true
func (builder *TicketCustomizedFieldBuilder) DropdownAllowMultiple(dropdownAllowMultiple bool) *TicketCustomizedFieldBuilder {
	builder.dropdownAllowMultiple = dropdownAllowMultiple
	builder.dropdownAllowMultipleFlag = true
	return builder
}

func (builder *TicketCustomizedFieldBuilder) Build() *TicketCustomizedField {
	req := &TicketCustomizedField{}
	if builder.ticketCustomizedFieldIdFlag {
		req.TicketCustomizedFieldId = &builder.ticketCustomizedFieldId

	}
	if builder.helpdeskIdFlag {
		req.HelpdeskId = &builder.helpdeskId

	}
	if builder.keyNameFlag {
		req.KeyName = &builder.keyName

	}
	if builder.displayNameFlag {
		req.DisplayName = &builder.displayName

	}
	if builder.positionFlag {
		req.Position = &builder.position

	}
	if builder.fieldTypeFlag {
		req.FieldType = &builder.fieldType

	}
	if builder.descriptionFlag {
		req.Description = &builder.description

	}
	if builder.visibleFlag {
		req.Visible = &builder.visible

	}
	if builder.editableFlag {
		req.Editable = &builder.editable

	}
	if builder.requiredFlag {
		req.Required = &builder.required

	}
	if builder.createdAtFlag {
		req.CreatedAt = &builder.createdAt

	}
	if builder.updatedAtFlag {
		req.UpdatedAt = &builder.updatedAt

	}
	if builder.createdByFlag {
		req.CreatedBy = builder.createdBy
	}
	if builder.updatedByFlag {
		req.UpdatedBy = builder.updatedBy
	}
	if builder.dropdownAllowMultipleFlag {
		req.DropdownAllowMultiple = &builder.dropdownAllowMultiple

	}
	return req
}

type TicketEvent struct {
	TicketId         *string                       `json:"ticket_id,omitempty"`         // 工单ID;;[可以从工单列表里面取](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/helpdesk-v1/ticket/list);;[也可以订阅工单创建事件获取](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/helpdesk-v1/ticket/events/created)
	HelpdeskId       *string                       `json:"helpdesk_id,omitempty"`       // 服务台id
	Guest            *TicketUserEvent              `json:"guest,omitempty"`             // 用户id
	Stage            *int                          `json:"stage,omitempty"`             // 工单阶段：1. 机器人 2. 人工
	Status           *int                          `json:"status,omitempty"`            // 工单状态，1：已创建 2: 处理中 3: 排队中 4：待定 5：待用户响应 50: 被机器人关闭 51: 被客服关闭 52: 用户自己关闭
	Score            *int                          `json:"score,omitempty"`             // 工单评分，1：不满意，2:一般，3:满意
	CreatedAt        *int                          `json:"created_at,omitempty"`        // 创建时间
	UpdatedAt        *int                          `json:"updated_at,omitempty"`        // 工单更新时间，没有值时为-1
	ClosedAt         *int                          `json:"closed_at,omitempty"`         // 关单时间
	Agents           []*TicketUserEvent            `json:"agents,omitempty"`            // agents of this ticket
	Channel          *int                          `json:"channel,omitempty"`           // 工单渠道，描述：9：Open API 2：二维码 14：分享 13：搜索 其他数字：其他渠道
	Solve            *int                          `json:"solve,omitempty"`             // 工单是否解决 1:没解决 2:已解决
	ClosedBy         *TicketUserEvent              `json:"closed_by,omitempty"`         // closed user of this ticket
	Collaborators    []*TicketUserEvent            `json:"collaborators,omitempty"`     // collaborators of this ticket
	CustomizedFields []*CustomizedFieldDisplayItem `json:"customized_fields,omitempty"` // 自定义字段
	ChatId           *string                       `json:"chat_id,omitempty"`           // oc_xxxxxxx
}

type TicketEventBuilder struct {
	ticketId             string // 工单ID;;[可以从工单列表里面取](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/helpdesk-v1/ticket/list);;[也可以订阅工单创建事件获取](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/helpdesk-v1/ticket/events/created)
	ticketIdFlag         bool
	helpdeskId           string // 服务台id
	helpdeskIdFlag       bool
	guest                *TicketUserEvent // 用户id
	guestFlag            bool
	stage                int // 工单阶段：1. 机器人 2. 人工
	stageFlag            bool
	status               int // 工单状态，1：已创建 2: 处理中 3: 排队中 4：待定 5：待用户响应 50: 被机器人关闭 51: 被客服关闭 52: 用户自己关闭
	statusFlag           bool
	score                int // 工单评分，1：不满意，2:一般，3:满意
	scoreFlag            bool
	createdAt            int // 创建时间
	createdAtFlag        bool
	updatedAt            int // 工单更新时间，没有值时为-1
	updatedAtFlag        bool
	closedAt             int // 关单时间
	closedAtFlag         bool
	agents               []*TicketUserEvent // agents of this ticket
	agentsFlag           bool
	channel              int // 工单渠道，描述：9：Open API 2：二维码 14：分享 13：搜索 其他数字：其他渠道
	channelFlag          bool
	solve                int // 工单是否解决 1:没解决 2:已解决
	solveFlag            bool
	closedBy             *TicketUserEvent // closed user of this ticket
	closedByFlag         bool
	collaborators        []*TicketUserEvent // collaborators of this ticket
	collaboratorsFlag    bool
	customizedFields     []*CustomizedFieldDisplayItem // 自定义字段
	customizedFieldsFlag bool
	chatId               string // oc_xxxxxxx
	chatIdFlag           bool
}

func NewTicketEventBuilder() *TicketEventBuilder {
	builder := &TicketEventBuilder{}
	return builder
}

// 工单ID;;[可以从工单列表里面取](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/helpdesk-v1/ticket/list);;[也可以订阅工单创建事件获取](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/helpdesk-v1/ticket/events/created)
//
// 示例值：6626871355780366331
func (builder *TicketEventBuilder) TicketId(ticketId string) *TicketEventBuilder {
	builder.ticketId = ticketId
	builder.ticketIdFlag = true
	return builder
}

// 服务台id
//
// 示例值：6626871355780366330
func (builder *TicketEventBuilder) HelpdeskId(helpdeskId string) *TicketEventBuilder {
	builder.helpdeskId = helpdeskId
	builder.helpdeskIdFlag = true
	return builder
}

// 用户id
//
// 示例值：
func (builder *TicketEventBuilder) Guest(guest *TicketUserEvent) *TicketEventBuilder {
	builder.guest = guest
	builder.guestFlag = true
	return builder
}

// 工单阶段：1. 机器人 2. 人工
//
// 示例值：1
func (builder *TicketEventBuilder) Stage(stage int) *TicketEventBuilder {
	builder.stage = stage
	builder.stageFlag = true
	return builder
}

// 工单状态，1：已创建 2: 处理中 3: 排队中 4：待定 5：待用户响应 50: 被机器人关闭 51: 被客服关闭 52: 用户自己关闭
//
// 示例值：1
func (builder *TicketEventBuilder) Status(status int) *TicketEventBuilder {
	builder.status = status
	builder.statusFlag = true
	return builder
}

// 工单评分，1：不满意，2:一般，3:满意
//
// 示例值：1
func (builder *TicketEventBuilder) Score(score int) *TicketEventBuilder {
	builder.score = score
	builder.scoreFlag = true
	return builder
}

// 创建时间
//
// 示例值：1616920429000
func (builder *TicketEventBuilder) CreatedAt(createdAt int) *TicketEventBuilder {
	builder.createdAt = createdAt
	builder.createdAtFlag = true
	return builder
}

// 工单更新时间，没有值时为-1
//
// 示例值：1616920429000
func (builder *TicketEventBuilder) UpdatedAt(updatedAt int) *TicketEventBuilder {
	builder.updatedAt = updatedAt
	builder.updatedAtFlag = true
	return builder
}

// 关单时间
//
// 示例值：1616920429000
func (builder *TicketEventBuilder) ClosedAt(closedAt int) *TicketEventBuilder {
	builder.closedAt = closedAt
	builder.closedAtFlag = true
	return builder
}

// agents of this ticket
//
// 示例值：
func (builder *TicketEventBuilder) Agents(agents []*TicketUserEvent) *TicketEventBuilder {
	builder.agents = agents
	builder.agentsFlag = true
	return builder
}

// 工单渠道，描述：9：Open API 2：二维码 14：分享 13：搜索 其他数字：其他渠道
//
// 示例值：0
func (builder *TicketEventBuilder) Channel(channel int) *TicketEventBuilder {
	builder.channel = channel
	builder.channelFlag = true
	return builder
}

// 工单是否解决 1:没解决 2:已解决
//
// 示例值：1
func (builder *TicketEventBuilder) Solve(solve int) *TicketEventBuilder {
	builder.solve = solve
	builder.solveFlag = true
	return builder
}

// closed user of this ticket
//
// 示例值：
func (builder *TicketEventBuilder) ClosedBy(closedBy *TicketUserEvent) *TicketEventBuilder {
	builder.closedBy = closedBy
	builder.closedByFlag = true
	return builder
}

// collaborators of this ticket
//
// 示例值：
func (builder *TicketEventBuilder) Collaborators(collaborators []*TicketUserEvent) *TicketEventBuilder {
	builder.collaborators = collaborators
	builder.collaboratorsFlag = true
	return builder
}

// 自定义字段
//
// 示例值：
func (builder *TicketEventBuilder) CustomizedFields(customizedFields []*CustomizedFieldDisplayItem) *TicketEventBuilder {
	builder.customizedFields = customizedFields
	builder.customizedFieldsFlag = true
	return builder
}

// oc_xxxxxxx
//
// 示例值：oc_xxxxxxx
func (builder *TicketEventBuilder) ChatId(chatId string) *TicketEventBuilder {
	builder.chatId = chatId
	builder.chatIdFlag = true
	return builder
}

func (builder *TicketEventBuilder) Build() *TicketEvent {
	req := &TicketEvent{}
	if builder.ticketIdFlag {
		req.TicketId = &builder.ticketId

	}
	if builder.helpdeskIdFlag {
		req.HelpdeskId = &builder.helpdeskId

	}
	if builder.guestFlag {
		req.Guest = builder.guest
	}
	if builder.stageFlag {
		req.Stage = &builder.stage

	}
	if builder.statusFlag {
		req.Status = &builder.status

	}
	if builder.scoreFlag {
		req.Score = &builder.score

	}
	if builder.createdAtFlag {
		req.CreatedAt = &builder.createdAt

	}
	if builder.updatedAtFlag {
		req.UpdatedAt = &builder.updatedAt

	}
	if builder.closedAtFlag {
		req.ClosedAt = &builder.closedAt

	}
	if builder.agentsFlag {
		req.Agents = builder.agents
	}
	if builder.channelFlag {
		req.Channel = &builder.channel

	}
	if builder.solveFlag {
		req.Solve = &builder.solve

	}
	if builder.closedByFlag {
		req.ClosedBy = builder.closedBy
	}
	if builder.collaboratorsFlag {
		req.Collaborators = builder.collaborators
	}
	if builder.customizedFieldsFlag {
		req.CustomizedFields = builder.customizedFields
	}
	if builder.chatIdFlag {
		req.ChatId = &builder.chatId

	}
	return req
}

type TicketEventUpdateInfo struct {
	Stage     *int `json:"stage,omitempty"`      // ticket stage
	Status    *int `json:"status,omitempty"`     // ticket status
	UpdatedAt *int `json:"updated_at,omitempty"` // ticket update time
}

type TicketEventUpdateInfoBuilder struct {
	stage         int // ticket stage
	stageFlag     bool
	status        int // ticket status
	statusFlag    bool
	updatedAt     int // ticket update time
	updatedAtFlag bool
}

func NewTicketEventUpdateInfoBuilder() *TicketEventUpdateInfoBuilder {
	builder := &TicketEventUpdateInfoBuilder{}
	return builder
}

// ticket stage
//
// 示例值：1
func (builder *TicketEventUpdateInfoBuilder) Stage(stage int) *TicketEventUpdateInfoBuilder {
	builder.stage = stage
	builder.stageFlag = true
	return builder
}

// ticket status
//
// 示例值：50
func (builder *TicketEventUpdateInfoBuilder) Status(status int) *TicketEventUpdateInfoBuilder {
	builder.status = status
	builder.statusFlag = true
	return builder
}

// ticket update time
//
// 示例值：1616920429000
func (builder *TicketEventUpdateInfoBuilder) UpdatedAt(updatedAt int) *TicketEventUpdateInfoBuilder {
	builder.updatedAt = updatedAt
	builder.updatedAtFlag = true
	return builder
}

func (builder *TicketEventUpdateInfoBuilder) Build() *TicketEventUpdateInfo {
	req := &TicketEventUpdateInfo{}
	if builder.stageFlag {
		req.Stage = &builder.stage

	}
	if builder.statusFlag {
		req.Status = &builder.status

	}
	if builder.updatedAtFlag {
		req.UpdatedAt = &builder.updatedAt

	}
	return req
}

type TicketMessageContent struct {
	Content   *string  `json:"content,omitempty"`    // 内容
	MsgType   *string  `json:"msg_type,omitempty"`   // 消息类型；text：纯文本；post：富文本；image：图片
	ImageKeys []string `json:"image_keys,omitempty"` // 图片ID
	ImageKey  *string  `json:"image_key,omitempty"`  // 图片ID
}

type TicketMessageContentBuilder struct {
	content       string // 内容
	contentFlag   bool
	msgType       string // 消息类型；text：纯文本；post：富文本；image：图片
	msgTypeFlag   bool
	imageKeys     []string // 图片ID
	imageKeysFlag bool
	imageKey      string // 图片ID
	imageKeyFlag  bool
}

func NewTicketMessageContentBuilder() *TicketMessageContentBuilder {
	builder := &TicketMessageContentBuilder{}
	return builder
}

// 内容
//
// 示例值：请问vpn怎么下载
func (builder *TicketMessageContentBuilder) Content(content string) *TicketMessageContentBuilder {
	builder.content = content
	builder.contentFlag = true
	return builder
}

// 消息类型；text：纯文本；post：富文本；image：图片
//
// 示例值：text
func (builder *TicketMessageContentBuilder) MsgType(msgType string) *TicketMessageContentBuilder {
	builder.msgType = msgType
	builder.msgTypeFlag = true
	return builder
}

// 图片ID
//
// 示例值：
func (builder *TicketMessageContentBuilder) ImageKeys(imageKeys []string) *TicketMessageContentBuilder {
	builder.imageKeys = imageKeys
	builder.imageKeysFlag = true
	return builder
}

// 图片ID
//
// 示例值：xxx
func (builder *TicketMessageContentBuilder) ImageKey(imageKey string) *TicketMessageContentBuilder {
	builder.imageKey = imageKey
	builder.imageKeyFlag = true
	return builder
}

func (builder *TicketMessageContentBuilder) Build() *TicketMessageContent {
	req := &TicketMessageContent{}
	if builder.contentFlag {
		req.Content = &builder.content

	}
	if builder.msgTypeFlag {
		req.MsgType = &builder.msgType

	}
	if builder.imageKeysFlag {
		req.ImageKeys = builder.imageKeys
	}
	if builder.imageKeyFlag {
		req.ImageKey = &builder.imageKey

	}
	return req
}

type TicketMessageEvent struct {
	TicketMessageId *string               `json:"ticket_message_id,omitempty"` // ticket message id
	MessageId       *string               `json:"message_id,omitempty"`        // open message id
	MsgType         *string               `json:"msg_type,omitempty"`          // message type, text is the only supported type
	Position        *string               `json:"position,omitempty"`          // position of the message
	SenderId        *UserId               `json:"sender_id,omitempty"`         // 用户 ID
	SenderType      *int                  `json:"sender_type,omitempty"`       // sender type, 1 for bot, 2 for guest, 3 for agent
	Text            *string               `json:"text,omitempty"`              // message content
	Ticket          *Ticket               `json:"ticket,omitempty"`            // ticket related information
	EventId         *string               `json:"event_id,omitempty"`          // event id
	ChatId          *string               `json:"chat_id,omitempty"`           // chat id
	Content         *TicketMessageContent `json:"content,omitempty"`           // message content
}

type TicketMessageEventBuilder struct {
	ticketMessageId     string // ticket message id
	ticketMessageIdFlag bool
	messageId           string // open message id
	messageIdFlag       bool
	msgType             string // message type, text is the only supported type
	msgTypeFlag         bool
	position            string // position of the message
	positionFlag        bool
	senderId            *UserId // 用户 ID
	senderIdFlag        bool
	senderType          int // sender type, 1 for bot, 2 for guest, 3 for agent
	senderTypeFlag      bool
	text                string // message content
	textFlag            bool
	ticket              *Ticket // ticket related information
	ticketFlag          bool
	eventId             string // event id
	eventIdFlag         bool
	chatId              string // chat id
	chatIdFlag          bool
	content             *TicketMessageContent // message content
	contentFlag         bool
}

func NewTicketMessageEventBuilder() *TicketMessageEventBuilder {
	builder := &TicketMessageEventBuilder{}
	return builder
}

// ticket message id
//
// 示例值：6949088240624222236
func (builder *TicketMessageEventBuilder) TicketMessageId(ticketMessageId string) *TicketMessageEventBuilder {
	builder.ticketMessageId = ticketMessageId
	builder.ticketMessageIdFlag = true
	return builder
}

// open message id
//
// 示例值：om_8baa3656c7b41900d29bf9104bf5310b
func (builder *TicketMessageEventBuilder) MessageId(messageId string) *TicketMessageEventBuilder {
	builder.messageId = messageId
	builder.messageIdFlag = true
	return builder
}

// message type, text is the only supported type
//
// 示例值：text
func (builder *TicketMessageEventBuilder) MsgType(msgType string) *TicketMessageEventBuilder {
	builder.msgType = msgType
	builder.msgTypeFlag = true
	return builder
}

// position of the message
//
// 示例值：10
func (builder *TicketMessageEventBuilder) Position(position string) *TicketMessageEventBuilder {
	builder.position = position
	builder.positionFlag = true
	return builder
}

// 用户 ID
//
// 示例值：
func (builder *TicketMessageEventBuilder) SenderId(senderId *UserId) *TicketMessageEventBuilder {
	builder.senderId = senderId
	builder.senderIdFlag = true
	return builder
}

// sender type, 1 for bot, 2 for guest, 3 for agent
//
// 示例值：1
func (builder *TicketMessageEventBuilder) SenderType(senderType int) *TicketMessageEventBuilder {
	builder.senderType = senderType
	builder.senderTypeFlag = true
	return builder
}

// message content
//
// 示例值：请问vpn怎么下载
func (builder *TicketMessageEventBuilder) Text(text string) *TicketMessageEventBuilder {
	builder.text = text
	builder.textFlag = true
	return builder
}

// ticket related information
//
// 示例值：
func (builder *TicketMessageEventBuilder) Ticket(ticket *Ticket) *TicketMessageEventBuilder {
	builder.ticket = ticket
	builder.ticketFlag = true
	return builder
}

// event id
//
// 示例值：118a6492-122d-04ad-4370-010a3bb384d3
func (builder *TicketMessageEventBuilder) EventId(eventId string) *TicketMessageEventBuilder {
	builder.eventId = eventId
	builder.eventIdFlag = true
	return builder
}

// chat id
//
// 示例值：6949088236610273307
func (builder *TicketMessageEventBuilder) ChatId(chatId string) *TicketMessageEventBuilder {
	builder.chatId = chatId
	builder.chatIdFlag = true
	return builder
}

// message content
//
// 示例值：
func (builder *TicketMessageEventBuilder) Content(content *TicketMessageContent) *TicketMessageEventBuilder {
	builder.content = content
	builder.contentFlag = true
	return builder
}

func (builder *TicketMessageEventBuilder) Build() *TicketMessageEvent {
	req := &TicketMessageEvent{}
	if builder.ticketMessageIdFlag {
		req.TicketMessageId = &builder.ticketMessageId

	}
	if builder.messageIdFlag {
		req.MessageId = &builder.messageId

	}
	if builder.msgTypeFlag {
		req.MsgType = &builder.msgType

	}
	if builder.positionFlag {
		req.Position = &builder.position

	}
	if builder.senderIdFlag {
		req.SenderId = builder.senderId
	}
	if builder.senderTypeFlag {
		req.SenderType = &builder.senderType

	}
	if builder.textFlag {
		req.Text = &builder.text

	}
	if builder.ticketFlag {
		req.Ticket = builder.ticket
	}
	if builder.eventIdFlag {
		req.EventId = &builder.eventId

	}
	if builder.chatIdFlag {
		req.ChatId = &builder.chatId

	}
	if builder.contentFlag {
		req.Content = builder.content
	}
	return req
}

type TicketUser struct {
	Id         *string `json:"id,omitempty"`         // 用户ID
	AvatarUrl  *string `json:"avatar_url,omitempty"` // 用户头像url
	Name       *string `json:"name,omitempty"`       // 用户名
	Email      *string `json:"email,omitempty"`      // 用户邮箱
	Department *string `json:"department,omitempty"` // 所在部门名称
	City       *string `json:"city,omitempty"`       // 城市
	Country    *string `json:"country,omitempty"`    // 国家代号(CountryCode)，参考：http://www.mamicode.com/info-detail-2186501.html
}

type TicketUserBuilder struct {
	id             string // 用户ID
	idFlag         bool
	avatarUrl      string // 用户头像url
	avatarUrlFlag  bool
	name           string // 用户名
	nameFlag       bool
	email          string // 用户邮箱
	emailFlag      bool
	department     string // 所在部门名称
	departmentFlag bool
	city           string // 城市
	cityFlag       bool
	country        string // 国家代号(CountryCode)，参考：http://www.mamicode.com/info-detail-2186501.html
	countryFlag    bool
}

func NewTicketUserBuilder() *TicketUserBuilder {
	builder := &TicketUserBuilder{}
	return builder
}

// 用户ID
//
// 示例值：ou_37019b7c830210acd88fdce886e25c71
func (builder *TicketUserBuilder) Id(id string) *TicketUserBuilder {
	builder.id = id
	builder.idFlag = true
	return builder
}

// 用户头像url
//
// 示例值：https://xxxx
func (builder *TicketUserBuilder) AvatarUrl(avatarUrl string) *TicketUserBuilder {
	builder.avatarUrl = avatarUrl
	builder.avatarUrlFlag = true
	return builder
}

// 用户名
//
// 示例值：abc
func (builder *TicketUserBuilder) Name(name string) *TicketUserBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

// 用户邮箱
//
// 示例值：<EMAIL>
func (builder *TicketUserBuilder) Email(email string) *TicketUserBuilder {
	builder.email = email
	builder.emailFlag = true
	return builder
}

// 所在部门名称
//
// 示例值：用户部门名称(有权限才展示)
func (builder *TicketUserBuilder) Department(department string) *TicketUserBuilder {
	builder.department = department
	builder.departmentFlag = true
	return builder
}

// 城市
//
// 示例值：城市
func (builder *TicketUserBuilder) City(city string) *TicketUserBuilder {
	builder.city = city
	builder.cityFlag = true
	return builder
}

// 国家代号(CountryCode)，参考：http://www.mamicode.com/info-detail-2186501.html
//
// 示例值：国家
func (builder *TicketUserBuilder) Country(country string) *TicketUserBuilder {
	builder.country = country
	builder.countryFlag = true
	return builder
}

func (builder *TicketUserBuilder) Build() *TicketUser {
	req := &TicketUser{}
	if builder.idFlag {
		req.Id = &builder.id

	}
	if builder.avatarUrlFlag {
		req.AvatarUrl = &builder.avatarUrl

	}
	if builder.nameFlag {
		req.Name = &builder.name

	}
	if builder.emailFlag {
		req.Email = &builder.email

	}
	if builder.departmentFlag {
		req.Department = &builder.department

	}
	if builder.cityFlag {
		req.City = &builder.city

	}
	if builder.countryFlag {
		req.Country = &builder.country

	}
	return req
}

type TicketUserEvent struct {
	Id        *UserId `json:"id,omitempty"`         // id
	AvatarUrl *string `json:"avatar_url,omitempty"` // user avartal url
	Name      *string `json:"name,omitempty"`       // 名称
	Email     *string `json:"email,omitempty"`      // user email
}

type TicketUserEventBuilder struct {
	id            *UserId // id
	idFlag        bool
	avatarUrl     string // user avartal url
	avatarUrlFlag bool
	name          string // 名称
	nameFlag      bool
	email         string // user email
	emailFlag     bool
}

func NewTicketUserEventBuilder() *TicketUserEventBuilder {
	builder := &TicketUserEventBuilder{}
	return builder
}

// id
//
// 示例值：
func (builder *TicketUserEventBuilder) Id(id *UserId) *TicketUserEventBuilder {
	builder.id = id
	builder.idFlag = true
	return builder
}

// user avartal url
//
// 示例值：
func (builder *TicketUserEventBuilder) AvatarUrl(avatarUrl string) *TicketUserEventBuilder {
	builder.avatarUrl = avatarUrl
	builder.avatarUrlFlag = true
	return builder
}

// 名称
//
// 示例值：abc
func (builder *TicketUserEventBuilder) Name(name string) *TicketUserEventBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

// user email
//
// 示例值：
func (builder *TicketUserEventBuilder) Email(email string) *TicketUserEventBuilder {
	builder.email = email
	builder.emailFlag = true
	return builder
}

func (builder *TicketUserEventBuilder) Build() *TicketUserEvent {
	req := &TicketUserEvent{}
	if builder.idFlag {
		req.Id = builder.id
	}
	if builder.avatarUrlFlag {
		req.AvatarUrl = &builder.avatarUrl

	}
	if builder.nameFlag {
		req.Name = &builder.name

	}
	if builder.emailFlag {
		req.Email = &builder.email

	}
	return req
}

type UserCustomizedField struct {
	UserCustomizedFieldId *string `json:"user_customized_field_id,omitempty"` // 字段ID
	Id                    *string `json:"id,omitempty"`                       // 旧字段ID，向后兼容用
	HelpdeskId            *string `json:"helpdesk_id,omitempty"`              // 服务台ID
	KeyName               *string `json:"key_name,omitempty"`                 // 字段键
	DisplayName           *string `json:"display_name,omitempty"`             // 字段展示名称
	Position              *string `json:"position,omitempty"`                 // 字段在列表中的展示位置
	FieldType             *string `json:"field_type,omitempty"`               // 字段类型
	Description           *string `json:"description,omitempty"`              // 字段描述信息
	Visible               *bool   `json:"visible,omitempty"`                  // 字段是否可见
	Editable              *bool   `json:"editable,omitempty"`                 // 字段是否可编辑
	Required              *bool   `json:"required,omitempty"`                 // 字段是否必填
	CreatedAt             *string `json:"created_at,omitempty"`               // 字段创建时间
	UpdatedAt             *string `json:"updated_at,omitempty"`               // 字段修改时间
}

type UserCustomizedFieldBuilder struct {
	userCustomizedFieldId     string // 字段ID
	userCustomizedFieldIdFlag bool
	id                        string // 旧字段ID，向后兼容用
	idFlag                    bool
	helpdeskId                string // 服务台ID
	helpdeskIdFlag            bool
	keyName                   string // 字段键
	keyNameFlag               bool
	displayName               string // 字段展示名称
	displayNameFlag           bool
	position                  string // 字段在列表中的展示位置
	positionFlag              bool
	fieldType                 string // 字段类型
	fieldTypeFlag             bool
	description               string // 字段描述信息
	descriptionFlag           bool
	visible                   bool // 字段是否可见
	visibleFlag               bool
	editable                  bool // 字段是否可编辑
	editableFlag              bool
	required                  bool // 字段是否必填
	requiredFlag              bool
	createdAt                 string // 字段创建时间
	createdAtFlag             bool
	updatedAt                 string // 字段修改时间
	updatedAtFlag             bool
}

func NewUserCustomizedFieldBuilder() *UserCustomizedFieldBuilder {
	builder := &UserCustomizedFieldBuilder{}
	return builder
}

// 字段ID
//
// 示例值：6746384425543548981
func (builder *UserCustomizedFieldBuilder) UserCustomizedFieldId(userCustomizedFieldId string) *UserCustomizedFieldBuilder {
	builder.userCustomizedFieldId = userCustomizedFieldId
	builder.userCustomizedFieldIdFlag = true
	return builder
}

// 旧字段ID，向后兼容用
//
// 示例值：6746384425543548981
func (builder *UserCustomizedFieldBuilder) Id(id string) *UserCustomizedFieldBuilder {
	builder.id = id
	builder.idFlag = true
	return builder
}

// 服务台ID
//
// 示例值：1542164574896126
func (builder *UserCustomizedFieldBuilder) HelpdeskId(helpdeskId string) *UserCustomizedFieldBuilder {
	builder.helpdeskId = helpdeskId
	builder.helpdeskIdFlag = true
	return builder
}

// 字段键
//
// 示例值：company_id3
func (builder *UserCustomizedFieldBuilder) KeyName(keyName string) *UserCustomizedFieldBuilder {
	builder.keyName = keyName
	builder.keyNameFlag = true
	return builder
}

// 字段展示名称
//
// 示例值：Company ID
func (builder *UserCustomizedFieldBuilder) DisplayName(displayName string) *UserCustomizedFieldBuilder {
	builder.displayName = displayName
	builder.displayNameFlag = true
	return builder
}

// 字段在列表中的展示位置
//
// 示例值：1
func (builder *UserCustomizedFieldBuilder) Position(position string) *UserCustomizedFieldBuilder {
	builder.position = position
	builder.positionFlag = true
	return builder
}

// 字段类型
//
// 示例值：string
func (builder *UserCustomizedFieldBuilder) FieldType(fieldType string) *UserCustomizedFieldBuilder {
	builder.fieldType = fieldType
	builder.fieldTypeFlag = true
	return builder
}

// 字段描述信息
//
// 示例值：租户ID
func (builder *UserCustomizedFieldBuilder) Description(description string) *UserCustomizedFieldBuilder {
	builder.description = description
	builder.descriptionFlag = true
	return builder
}

// 字段是否可见
//
// 示例值：false
func (builder *UserCustomizedFieldBuilder) Visible(visible bool) *UserCustomizedFieldBuilder {
	builder.visible = visible
	builder.visibleFlag = true
	return builder
}

// 字段是否可编辑
//
// 示例值：false
func (builder *UserCustomizedFieldBuilder) Editable(editable bool) *UserCustomizedFieldBuilder {
	builder.editable = editable
	builder.editableFlag = true
	return builder
}

// 字段是否必填
//
// 示例值：false
func (builder *UserCustomizedFieldBuilder) Required(required bool) *UserCustomizedFieldBuilder {
	builder.required = required
	builder.requiredFlag = true
	return builder
}

// 字段创建时间
//
// 示例值：1574040677000
func (builder *UserCustomizedFieldBuilder) CreatedAt(createdAt string) *UserCustomizedFieldBuilder {
	builder.createdAt = createdAt
	builder.createdAtFlag = true
	return builder
}

// 字段修改时间
//
// 示例值：1574040677000
func (builder *UserCustomizedFieldBuilder) UpdatedAt(updatedAt string) *UserCustomizedFieldBuilder {
	builder.updatedAt = updatedAt
	builder.updatedAtFlag = true
	return builder
}

func (builder *UserCustomizedFieldBuilder) Build() *UserCustomizedField {
	req := &UserCustomizedField{}
	if builder.userCustomizedFieldIdFlag {
		req.UserCustomizedFieldId = &builder.userCustomizedFieldId

	}
	if builder.idFlag {
		req.Id = &builder.id

	}
	if builder.helpdeskIdFlag {
		req.HelpdeskId = &builder.helpdeskId

	}
	if builder.keyNameFlag {
		req.KeyName = &builder.keyName

	}
	if builder.displayNameFlag {
		req.DisplayName = &builder.displayName

	}
	if builder.positionFlag {
		req.Position = &builder.position

	}
	if builder.fieldTypeFlag {
		req.FieldType = &builder.fieldType

	}
	if builder.descriptionFlag {
		req.Description = &builder.description

	}
	if builder.visibleFlag {
		req.Visible = &builder.visible

	}
	if builder.editableFlag {
		req.Editable = &builder.editable

	}
	if builder.requiredFlag {
		req.Required = &builder.required

	}
	if builder.createdAtFlag {
		req.CreatedAt = &builder.createdAt

	}
	if builder.updatedAtFlag {
		req.UpdatedAt = &builder.updatedAt

	}
	return req
}

type UserId struct {
	UserId  *string `json:"user_id,omitempty"`  //
	OpenId  *string `json:"open_id,omitempty"`  //
	UnionId *string `json:"union_id,omitempty"` //
}

type UserIdBuilder struct {
	userId      string //
	userIdFlag  bool
	openId      string //
	openIdFlag  bool
	unionId     string //
	unionIdFlag bool
}

func NewUserIdBuilder() *UserIdBuilder {
	builder := &UserIdBuilder{}
	return builder
}

//
//
// 示例值：
func (builder *UserIdBuilder) UserId(userId string) *UserIdBuilder {
	builder.userId = userId
	builder.userIdFlag = true
	return builder
}

//
//
// 示例值：
func (builder *UserIdBuilder) OpenId(openId string) *UserIdBuilder {
	builder.openId = openId
	builder.openIdFlag = true
	return builder
}

//
//
// 示例值：
func (builder *UserIdBuilder) UnionId(unionId string) *UserIdBuilder {
	builder.unionId = unionId
	builder.unionIdFlag = true
	return builder
}

func (builder *UserIdBuilder) Build() *UserId {
	req := &UserId{}
	if builder.userIdFlag {
		req.UserId = &builder.userId

	}
	if builder.openIdFlag {
		req.OpenId = &builder.openId

	}
	if builder.unionIdFlag {
		req.UnionId = &builder.unionId

	}
	return req
}

type UserQueryFaqInfo struct {
	Id    *string  `json:"id,omitempty"`    // faq服务台内唯一标识
	Score *float64 `json:"score,omitempty"` // faq匹配得分
}

type UserQueryFaqInfoBuilder struct {
	id        string // faq服务台内唯一标识
	idFlag    bool
	score     float64 // faq匹配得分
	scoreFlag bool
}

func NewUserQueryFaqInfoBuilder() *UserQueryFaqInfoBuilder {
	builder := &UserQueryFaqInfoBuilder{}
	return builder
}

// faq服务台内唯一标识
//
// 示例值：12345
func (builder *UserQueryFaqInfoBuilder) Id(id string) *UserQueryFaqInfoBuilder {
	builder.id = id
	builder.idFlag = true
	return builder
}

// faq匹配得分
//
// 示例值：0.9
func (builder *UserQueryFaqInfoBuilder) Score(score float64) *UserQueryFaqInfoBuilder {
	builder.score = score
	builder.scoreFlag = true
	return builder
}

func (builder *UserQueryFaqInfoBuilder) Build() *UserQueryFaqInfo {
	req := &UserQueryFaqInfo{}
	if builder.idFlag {
		req.Id = &builder.id

	}
	if builder.scoreFlag {
		req.Score = &builder.score

	}
	return req
}

type WeekdaySchedule struct {
	StartTime *string `json:"start_time,omitempty"` // 开始时间, format 00:00 - 23:59
	EndTime   *string `json:"end_time,omitempty"`   // 结束时间, format 00:00 - 23:59
	Weekday   *int    `json:"weekday,omitempty"`    // 星期几, 1 - Monday, 2 - Tuesday, 3 - Wednesday, 4 - Thursday, 5 - Friday, 6 - Saturday, 7 - Sunday, 9 - Everday, 10 - Weekday, 11 - Weekend
}

type WeekdayScheduleBuilder struct {
	startTime     string // 开始时间, format 00:00 - 23:59
	startTimeFlag bool
	endTime       string // 结束时间, format 00:00 - 23:59
	endTimeFlag   bool
	weekday       int // 星期几, 1 - Monday, 2 - Tuesday, 3 - Wednesday, 4 - Thursday, 5 - Friday, 6 - Saturday, 7 - Sunday, 9 - Everday, 10 - Weekday, 11 - Weekend
	weekdayFlag   bool
}

func NewWeekdayScheduleBuilder() *WeekdayScheduleBuilder {
	builder := &WeekdayScheduleBuilder{}
	return builder
}

// 开始时间, format 00:00 - 23:59
//
// 示例值：00:00
func (builder *WeekdayScheduleBuilder) StartTime(startTime string) *WeekdayScheduleBuilder {
	builder.startTime = startTime
	builder.startTimeFlag = true
	return builder
}

// 结束时间, format 00:00 - 23:59
//
// 示例值：24:00
func (builder *WeekdayScheduleBuilder) EndTime(endTime string) *WeekdayScheduleBuilder {
	builder.endTime = endTime
	builder.endTimeFlag = true
	return builder
}

// 星期几, 1 - Monday, 2 - Tuesday, 3 - Wednesday, 4 - Thursday, 5 - Friday, 6 - Saturday, 7 - Sunday, 9 - Everday, 10 - Weekday, 11 - Weekend
//
// 示例值：9
func (builder *WeekdayScheduleBuilder) Weekday(weekday int) *WeekdayScheduleBuilder {
	builder.weekday = weekday
	builder.weekdayFlag = true
	return builder
}

func (builder *WeekdayScheduleBuilder) Build() *WeekdaySchedule {
	req := &WeekdaySchedule{}
	if builder.startTimeFlag {
		req.StartTime = &builder.startTime

	}
	if builder.endTimeFlag {
		req.EndTime = &builder.endTime

	}
	if builder.weekdayFlag {
		req.Weekday = &builder.weekday

	}
	return req
}

type AgentEmailAgentRespData struct {
	Agents *string `json:"agents,omitempty"` // agent emails
}

type AgentEmailAgentResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *AgentEmailAgentRespData `json:"data"` // 业务数据
}

func (resp *AgentEmailAgentResp) Success() bool {
	return resp.Code == 0
}

type PatchAgentReqBodyBuilder struct {
	status     int // agent status
	statusFlag bool
}

func NewPatchAgentReqBodyBuilder() *PatchAgentReqBodyBuilder {
	builder := &PatchAgentReqBodyBuilder{}
	return builder
}

// agent status
//
//示例值：1：在线；2：离线
func (builder *PatchAgentReqBodyBuilder) Status(status int) *PatchAgentReqBodyBuilder {
	builder.status = status
	builder.statusFlag = true
	return builder
}

func (builder *PatchAgentReqBodyBuilder) Build() *PatchAgentReqBody {
	req := &PatchAgentReqBody{}
	if builder.statusFlag {
		req.Status = &builder.status
	}
	return req
}

type PatchAgentPathReqBodyBuilder struct {
	status     int // agent status
	statusFlag bool
}

func NewPatchAgentPathReqBodyBuilder() *PatchAgentPathReqBodyBuilder {
	builder := &PatchAgentPathReqBodyBuilder{}
	return builder
}

// agent status
//
// 示例值：1：在线；2：离线
func (builder *PatchAgentPathReqBodyBuilder) Status(status int) *PatchAgentPathReqBodyBuilder {
	builder.status = status
	builder.statusFlag = true
	return builder
}

func (builder *PatchAgentPathReqBodyBuilder) Build() (*PatchAgentReqBody, error) {
	req := &PatchAgentReqBody{}
	if builder.statusFlag {
		req.Status = &builder.status
	}
	return req, nil
}

type PatchAgentReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *PatchAgentReqBody
}

func NewPatchAgentReqBuilder() *PatchAgentReqBuilder {
	builder := &PatchAgentReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 客服id
//
// 示例值：ou_14777d82ffef0f707de5a8c7ff2c5ebe
func (builder *PatchAgentReqBuilder) AgentId(agentId string) *PatchAgentReqBuilder {
	builder.apiReq.PathParams.Set("agent_id", fmt.Sprint(agentId))
	return builder
}

// 更新客服状态等信息
func (builder *PatchAgentReqBuilder) Body(body *PatchAgentReqBody) *PatchAgentReqBuilder {
	builder.body = body
	return builder
}

func (builder *PatchAgentReqBuilder) Build() *PatchAgentReq {
	req := &PatchAgentReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.Body = builder.body
	return req
}

type PatchAgentReqBody struct {
	Status *int `json:"status,omitempty"` // agent status
}

type PatchAgentReq struct {
	apiReq *larkcore.ApiReq
	Body   *PatchAgentReqBody `body:""`
}

type PatchAgentResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *PatchAgentResp) Success() bool {
	return resp.Code == 0
}

type DeleteAgentSchedulesReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewDeleteAgentSchedulesReqBuilder() *DeleteAgentSchedulesReqBuilder {
	builder := &DeleteAgentSchedulesReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// agent user id
//
// 示例值：12345
func (builder *DeleteAgentSchedulesReqBuilder) AgentId(agentId string) *DeleteAgentSchedulesReqBuilder {
	builder.apiReq.PathParams.Set("agent_id", fmt.Sprint(agentId))
	return builder
}

func (builder *DeleteAgentSchedulesReqBuilder) Build() *DeleteAgentSchedulesReq {
	req := &DeleteAgentSchedulesReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	return req
}

type DeleteAgentSchedulesReq struct {
	apiReq *larkcore.ApiReq
}

type DeleteAgentSchedulesResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *DeleteAgentSchedulesResp) Success() bool {
	return resp.Code == 0
}

type GetAgentSchedulesReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewGetAgentSchedulesReqBuilder() *GetAgentSchedulesReqBuilder {
	builder := &GetAgentSchedulesReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 	客服 id
//
// 示例值：	客服 id
func (builder *GetAgentSchedulesReqBuilder) AgentId(agentId string) *GetAgentSchedulesReqBuilder {
	builder.apiReq.PathParams.Set("agent_id", fmt.Sprint(agentId))
	return builder
}

func (builder *GetAgentSchedulesReqBuilder) Build() *GetAgentSchedulesReq {
	req := &GetAgentSchedulesReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	return req
}

type GetAgentSchedulesReq struct {
	apiReq *larkcore.ApiReq
}

type GetAgentSchedulesRespData struct {
	AgentSchedule *AgentSchedule `json:"agent_schedule,omitempty"` // 客服日程
}

type GetAgentSchedulesResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *GetAgentSchedulesRespData `json:"data"` // 业务数据
}

func (resp *GetAgentSchedulesResp) Success() bool {
	return resp.Code == 0
}

type PatchAgentSchedulesReqBodyBuilder struct {
	agentSchedule     *AgentScheduleUpdateInfo // 工作日程列表
	agentScheduleFlag bool
}

func NewPatchAgentSchedulesReqBodyBuilder() *PatchAgentSchedulesReqBodyBuilder {
	builder := &PatchAgentSchedulesReqBodyBuilder{}
	return builder
}

// 工作日程列表
//
//示例值：
func (builder *PatchAgentSchedulesReqBodyBuilder) AgentSchedule(agentSchedule *AgentScheduleUpdateInfo) *PatchAgentSchedulesReqBodyBuilder {
	builder.agentSchedule = agentSchedule
	builder.agentScheduleFlag = true
	return builder
}

func (builder *PatchAgentSchedulesReqBodyBuilder) Build() *PatchAgentSchedulesReqBody {
	req := &PatchAgentSchedulesReqBody{}
	if builder.agentScheduleFlag {
		req.AgentSchedule = builder.agentSchedule
	}
	return req
}

type PatchAgentSchedulesPathReqBodyBuilder struct {
	agentSchedule     *AgentScheduleUpdateInfo // 工作日程列表
	agentScheduleFlag bool
}

func NewPatchAgentSchedulesPathReqBodyBuilder() *PatchAgentSchedulesPathReqBodyBuilder {
	builder := &PatchAgentSchedulesPathReqBodyBuilder{}
	return builder
}

// 工作日程列表
//
// 示例值：
func (builder *PatchAgentSchedulesPathReqBodyBuilder) AgentSchedule(agentSchedule *AgentScheduleUpdateInfo) *PatchAgentSchedulesPathReqBodyBuilder {
	builder.agentSchedule = agentSchedule
	builder.agentScheduleFlag = true
	return builder
}

func (builder *PatchAgentSchedulesPathReqBodyBuilder) Build() (*PatchAgentSchedulesReqBody, error) {
	req := &PatchAgentSchedulesReqBody{}
	if builder.agentScheduleFlag {
		req.AgentSchedule = builder.agentSchedule
	}
	return req, nil
}

type PatchAgentSchedulesReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *PatchAgentSchedulesReqBody
}

func NewPatchAgentSchedulesReqBuilder() *PatchAgentSchedulesReqBuilder {
	builder := &PatchAgentSchedulesReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 客服 id
//
// 示例值：123456
func (builder *PatchAgentSchedulesReqBuilder) AgentId(agentId string) *PatchAgentSchedulesReqBuilder {
	builder.apiReq.PathParams.Set("agent_id", fmt.Sprint(agentId))
	return builder
}

// 该接口用于更新客服的日程
func (builder *PatchAgentSchedulesReqBuilder) Body(body *PatchAgentSchedulesReqBody) *PatchAgentSchedulesReqBuilder {
	builder.body = body
	return builder
}

func (builder *PatchAgentSchedulesReqBuilder) Build() *PatchAgentSchedulesReq {
	req := &PatchAgentSchedulesReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.Body = builder.body
	return req
}

type PatchAgentSchedulesReqBody struct {
	AgentSchedule *AgentScheduleUpdateInfo `json:"agent_schedule,omitempty"` // 工作日程列表
}

type PatchAgentSchedulesReq struct {
	apiReq *larkcore.ApiReq
	Body   *PatchAgentSchedulesReqBody `body:""`
}

type PatchAgentSchedulesResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *PatchAgentSchedulesResp) Success() bool {
	return resp.Code == 0
}

type CreateAgentScheduleReqBodyBuilder struct {
	agentSchedules     []*AgentScheduleUpdateInfo // 新客服日程
	agentSchedulesFlag bool
}

func NewCreateAgentScheduleReqBodyBuilder() *CreateAgentScheduleReqBodyBuilder {
	builder := &CreateAgentScheduleReqBodyBuilder{}
	return builder
}

// 新客服日程
//
//示例值：
func (builder *CreateAgentScheduleReqBodyBuilder) AgentSchedules(agentSchedules []*AgentScheduleUpdateInfo) *CreateAgentScheduleReqBodyBuilder {
	builder.agentSchedules = agentSchedules
	builder.agentSchedulesFlag = true
	return builder
}

func (builder *CreateAgentScheduleReqBodyBuilder) Build() *CreateAgentScheduleReqBody {
	req := &CreateAgentScheduleReqBody{}
	if builder.agentSchedulesFlag {
		req.AgentSchedules = builder.agentSchedules
	}
	return req
}

type CreateAgentSchedulePathReqBodyBuilder struct {
	agentSchedules     []*AgentScheduleUpdateInfo // 新客服日程
	agentSchedulesFlag bool
}

func NewCreateAgentSchedulePathReqBodyBuilder() *CreateAgentSchedulePathReqBodyBuilder {
	builder := &CreateAgentSchedulePathReqBodyBuilder{}
	return builder
}

// 新客服日程
//
// 示例值：
func (builder *CreateAgentSchedulePathReqBodyBuilder) AgentSchedules(agentSchedules []*AgentScheduleUpdateInfo) *CreateAgentSchedulePathReqBodyBuilder {
	builder.agentSchedules = agentSchedules
	builder.agentSchedulesFlag = true
	return builder
}

func (builder *CreateAgentSchedulePathReqBodyBuilder) Build() (*CreateAgentScheduleReqBody, error) {
	req := &CreateAgentScheduleReqBody{}
	if builder.agentSchedulesFlag {
		req.AgentSchedules = builder.agentSchedules
	}
	return req, nil
}

type CreateAgentScheduleReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *CreateAgentScheduleReqBody
}

func NewCreateAgentScheduleReqBuilder() *CreateAgentScheduleReqBuilder {
	builder := &CreateAgentScheduleReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 该接口用于创建客服
func (builder *CreateAgentScheduleReqBuilder) Body(body *CreateAgentScheduleReqBody) *CreateAgentScheduleReqBuilder {
	builder.body = body
	return builder
}

func (builder *CreateAgentScheduleReqBuilder) Build() *CreateAgentScheduleReq {
	req := &CreateAgentScheduleReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.Body = builder.body
	return req
}

type CreateAgentScheduleReqBody struct {
	AgentSchedules []*AgentScheduleUpdateInfo `json:"agent_schedules,omitempty"` // 新客服日程
}

type CreateAgentScheduleReq struct {
	apiReq *larkcore.ApiReq
	Body   *CreateAgentScheduleReqBody `body:""`
}

type CreateAgentScheduleResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *CreateAgentScheduleResp) Success() bool {
	return resp.Code == 0
}

type ListAgentScheduleReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewListAgentScheduleReqBuilder() *ListAgentScheduleReqBuilder {
	builder := &ListAgentScheduleReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 筛选条件, 1 - online客服, 2 - offline(手动)客服, 3 - off duty(下班)客服, 4 - 移除客服
//
// 示例值：status=1&status=2
func (builder *ListAgentScheduleReqBuilder) Status(status []int) *ListAgentScheduleReqBuilder {
	for _, v := range status {
		builder.apiReq.QueryParams.Add("status", fmt.Sprint(v))
	}
	return builder
}

func (builder *ListAgentScheduleReqBuilder) Build() *ListAgentScheduleReq {
	req := &ListAgentScheduleReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type ListAgentScheduleReq struct {
	apiReq *larkcore.ApiReq
}

type ListAgentScheduleRespData struct {
	AgentSchedules []*AgentSchedule `json:"agent_schedules,omitempty"` // 客服列表
}

type ListAgentScheduleResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *ListAgentScheduleRespData `json:"data"` // 业务数据
}

func (resp *ListAgentScheduleResp) Success() bool {
	return resp.Code == 0
}

type CreateAgentSkillReqBodyBuilder struct {
	name         string // 技能名
	nameFlag     bool
	rules        []*AgentSkillRule // 技能rules
	rulesFlag    bool
	agentIds     []string // 客服 ids
	agentIdsFlag bool
}

func NewCreateAgentSkillReqBodyBuilder() *CreateAgentSkillReqBodyBuilder {
	builder := &CreateAgentSkillReqBodyBuilder{}
	return builder
}

// 技能名
//
//示例值：test-skill
func (builder *CreateAgentSkillReqBodyBuilder) Name(name string) *CreateAgentSkillReqBodyBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

// 技能rules
//
//示例值：
func (builder *CreateAgentSkillReqBodyBuilder) Rules(rules []*AgentSkillRule) *CreateAgentSkillReqBodyBuilder {
	builder.rules = rules
	builder.rulesFlag = true
	return builder
}

// 客服 ids
//
//示例值：["客服ID"]
func (builder *CreateAgentSkillReqBodyBuilder) AgentIds(agentIds []string) *CreateAgentSkillReqBodyBuilder {
	builder.agentIds = agentIds
	builder.agentIdsFlag = true
	return builder
}

func (builder *CreateAgentSkillReqBodyBuilder) Build() *CreateAgentSkillReqBody {
	req := &CreateAgentSkillReqBody{}
	if builder.nameFlag {
		req.Name = &builder.name
	}
	if builder.rulesFlag {
		req.Rules = builder.rules
	}
	if builder.agentIdsFlag {
		req.AgentIds = builder.agentIds
	}
	return req
}

type CreateAgentSkillPathReqBodyBuilder struct {
	name         string // 技能名
	nameFlag     bool
	rules        []*AgentSkillRule // 技能rules
	rulesFlag    bool
	agentIds     []string // 客服 ids
	agentIdsFlag bool
}

func NewCreateAgentSkillPathReqBodyBuilder() *CreateAgentSkillPathReqBodyBuilder {
	builder := &CreateAgentSkillPathReqBodyBuilder{}
	return builder
}

// 技能名
//
// 示例值：test-skill
func (builder *CreateAgentSkillPathReqBodyBuilder) Name(name string) *CreateAgentSkillPathReqBodyBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

// 技能rules
//
// 示例值：
func (builder *CreateAgentSkillPathReqBodyBuilder) Rules(rules []*AgentSkillRule) *CreateAgentSkillPathReqBodyBuilder {
	builder.rules = rules
	builder.rulesFlag = true
	return builder
}

// 客服 ids
//
// 示例值：["客服ID"]
func (builder *CreateAgentSkillPathReqBodyBuilder) AgentIds(agentIds []string) *CreateAgentSkillPathReqBodyBuilder {
	builder.agentIds = agentIds
	builder.agentIdsFlag = true
	return builder
}

func (builder *CreateAgentSkillPathReqBodyBuilder) Build() (*CreateAgentSkillReqBody, error) {
	req := &CreateAgentSkillReqBody{}
	if builder.nameFlag {
		req.Name = &builder.name
	}
	if builder.rulesFlag {
		req.Rules = builder.rules
	}
	if builder.agentIdsFlag {
		req.AgentIds = builder.agentIds
	}
	return req, nil
}

type CreateAgentSkillReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *CreateAgentSkillReqBody
}

func NewCreateAgentSkillReqBuilder() *CreateAgentSkillReqBuilder {
	builder := &CreateAgentSkillReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 该接口用于创建客服技能
func (builder *CreateAgentSkillReqBuilder) Body(body *CreateAgentSkillReqBody) *CreateAgentSkillReqBuilder {
	builder.body = body
	return builder
}

func (builder *CreateAgentSkillReqBuilder) Build() *CreateAgentSkillReq {
	req := &CreateAgentSkillReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.Body = builder.body
	return req
}

type CreateAgentSkillReqBody struct {
	Name     *string           `json:"name,omitempty"`      // 技能名
	Rules    []*AgentSkillRule `json:"rules,omitempty"`     // 技能rules
	AgentIds []string          `json:"agent_ids,omitempty"` // 客服 ids
}

type CreateAgentSkillReq struct {
	apiReq *larkcore.ApiReq
	Body   *CreateAgentSkillReqBody `body:""`
}

type CreateAgentSkillRespData struct {
	AgentSkillId *string `json:"agent_skill_id,omitempty"` // 客服技能id
}

type CreateAgentSkillResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *CreateAgentSkillRespData `json:"data"` // 业务数据
}

func (resp *CreateAgentSkillResp) Success() bool {
	return resp.Code == 0
}

type DeleteAgentSkillReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewDeleteAgentSkillReqBuilder() *DeleteAgentSkillReqBuilder {
	builder := &DeleteAgentSkillReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// agent group id
//
// 示例值：test-skill-id
func (builder *DeleteAgentSkillReqBuilder) AgentSkillId(agentSkillId string) *DeleteAgentSkillReqBuilder {
	builder.apiReq.PathParams.Set("agent_skill_id", fmt.Sprint(agentSkillId))
	return builder
}

func (builder *DeleteAgentSkillReqBuilder) Build() *DeleteAgentSkillReq {
	req := &DeleteAgentSkillReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	return req
}

type DeleteAgentSkillReq struct {
	apiReq *larkcore.ApiReq
}

type DeleteAgentSkillResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *DeleteAgentSkillResp) Success() bool {
	return resp.Code == 0
}

type GetAgentSkillReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewGetAgentSkillReqBuilder() *GetAgentSkillReqBuilder {
	builder := &GetAgentSkillReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// agent skill id
//
// 示例值：6941215891786825756
func (builder *GetAgentSkillReqBuilder) AgentSkillId(agentSkillId string) *GetAgentSkillReqBuilder {
	builder.apiReq.PathParams.Set("agent_skill_id", fmt.Sprint(agentSkillId))
	return builder
}

func (builder *GetAgentSkillReqBuilder) Build() *GetAgentSkillReq {
	req := &GetAgentSkillReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	return req
}

type GetAgentSkillReq struct {
	apiReq *larkcore.ApiReq
}

type GetAgentSkillRespData struct {
	AgentSkill *AgentSkill `json:"agent_skill,omitempty"` // 技能
}

type GetAgentSkillResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *GetAgentSkillRespData `json:"data"` // 业务数据
}

func (resp *GetAgentSkillResp) Success() bool {
	return resp.Code == 0
}

type ListAgentSkillRespData struct {
	AgentSkills []*AgentSkill `json:"agent_skills,omitempty"` // 客服技能列表
}

type ListAgentSkillResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *ListAgentSkillRespData `json:"data"` // 业务数据
}

func (resp *ListAgentSkillResp) Success() bool {
	return resp.Code == 0
}

type PatchAgentSkillReqBodyBuilder struct {
	agentSkill     *AgentSkill // 更新技能
	agentSkillFlag bool
}

func NewPatchAgentSkillReqBodyBuilder() *PatchAgentSkillReqBodyBuilder {
	builder := &PatchAgentSkillReqBodyBuilder{}
	return builder
}

// 更新技能
//
//示例值：
func (builder *PatchAgentSkillReqBodyBuilder) AgentSkill(agentSkill *AgentSkill) *PatchAgentSkillReqBodyBuilder {
	builder.agentSkill = agentSkill
	builder.agentSkillFlag = true
	return builder
}

func (builder *PatchAgentSkillReqBodyBuilder) Build() *PatchAgentSkillReqBody {
	req := &PatchAgentSkillReqBody{}
	if builder.agentSkillFlag {
		req.AgentSkill = builder.agentSkill
	}
	return req
}

type PatchAgentSkillPathReqBodyBuilder struct {
	agentSkill     *AgentSkill // 更新技能
	agentSkillFlag bool
}

func NewPatchAgentSkillPathReqBodyBuilder() *PatchAgentSkillPathReqBodyBuilder {
	builder := &PatchAgentSkillPathReqBodyBuilder{}
	return builder
}

// 更新技能
//
// 示例值：
func (builder *PatchAgentSkillPathReqBodyBuilder) AgentSkill(agentSkill *AgentSkill) *PatchAgentSkillPathReqBodyBuilder {
	builder.agentSkill = agentSkill
	builder.agentSkillFlag = true
	return builder
}

func (builder *PatchAgentSkillPathReqBodyBuilder) Build() (*PatchAgentSkillReqBody, error) {
	req := &PatchAgentSkillReqBody{}
	if builder.agentSkillFlag {
		req.AgentSkill = builder.agentSkill
	}
	return req, nil
}

type PatchAgentSkillReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *PatchAgentSkillReqBody
}

func NewPatchAgentSkillReqBuilder() *PatchAgentSkillReqBuilder {
	builder := &PatchAgentSkillReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// agent skill id
//
// 示例值：test-skill-id
func (builder *PatchAgentSkillReqBuilder) AgentSkillId(agentSkillId string) *PatchAgentSkillReqBuilder {
	builder.apiReq.PathParams.Set("agent_skill_id", fmt.Sprint(agentSkillId))
	return builder
}

// 该接口用于更新客服技能
func (builder *PatchAgentSkillReqBuilder) Body(body *PatchAgentSkillReqBody) *PatchAgentSkillReqBuilder {
	builder.body = body
	return builder
}

func (builder *PatchAgentSkillReqBuilder) Build() *PatchAgentSkillReq {
	req := &PatchAgentSkillReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.Body = builder.body
	return req
}

type PatchAgentSkillReqBody struct {
	AgentSkill *AgentSkill `json:"agent_skill,omitempty"` // 更新技能
}

type PatchAgentSkillReq struct {
	apiReq *larkcore.ApiReq
	Body   *PatchAgentSkillReqBody `body:""`
}

type PatchAgentSkillResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *PatchAgentSkillResp) Success() bool {
	return resp.Code == 0
}

type ListAgentSkillRuleRespData struct {
	Rules []*AgentSkillRule `json:"rules,omitempty"` // rules列表
}

type ListAgentSkillRuleResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *ListAgentSkillRuleRespData `json:"data"` // 业务数据
}

func (resp *ListAgentSkillRuleResp) Success() bool {
	return resp.Code == 0
}

type CreateBotMessageReqBuilder struct {
	apiReq     *larkcore.ApiReq
	botMessage *BotMessage
}

func NewCreateBotMessageReqBuilder() *CreateBotMessageReqBuilder {
	builder := &CreateBotMessageReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 此次调用中使用的用户ID的类型
//
// 示例值：
func (builder *CreateBotMessageReqBuilder) UserIdType(userIdType string) *CreateBotMessageReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

// 通过服务台机器人给指定用户的服务台专属群或私聊发送消息，支持文本、富文本、卡片、图片。
func (builder *CreateBotMessageReqBuilder) BotMessage(botMessage *BotMessage) *CreateBotMessageReqBuilder {
	builder.botMessage = botMessage
	return builder
}

func (builder *CreateBotMessageReqBuilder) Build() *CreateBotMessageReq {
	req := &CreateBotMessageReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.botMessage
	return req
}

type CreateBotMessageReq struct {
	apiReq     *larkcore.ApiReq
	BotMessage *BotMessage `body:""`
}

type CreateBotMessageRespData struct {
	MessageId *string `json:"message_id,omitempty"` // chat消息open_id
}

type CreateBotMessageResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *CreateBotMessageRespData `json:"data"` // 业务数据
}

func (resp *CreateBotMessageResp) Success() bool {
	return resp.Code == 0
}

type CreateCategoryReqBuilder struct {
	apiReq   *larkcore.ApiReq
	category *Category
}

func NewCreateCategoryReqBuilder() *CreateCategoryReqBuilder {
	builder := &CreateCategoryReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 该接口用于创建知识库分类。
func (builder *CreateCategoryReqBuilder) Category(category *Category) *CreateCategoryReqBuilder {
	builder.category = category
	return builder
}

func (builder *CreateCategoryReqBuilder) Build() *CreateCategoryReq {
	req := &CreateCategoryReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.Body = builder.category
	return req
}

type CreateCategoryReq struct {
	apiReq   *larkcore.ApiReq
	Category *Category `body:""`
}

type CreateCategoryRespData struct {
	Category *Category `json:"category,omitempty"` // 知识库分类
}

type CreateCategoryResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *CreateCategoryRespData `json:"data"` // 业务数据
}

func (resp *CreateCategoryResp) Success() bool {
	return resp.Code == 0
}

type DeleteCategoryReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewDeleteCategoryReqBuilder() *DeleteCategoryReqBuilder {
	builder := &DeleteCategoryReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 知识库分类ID
//
// 示例值：6948728206392295444
func (builder *DeleteCategoryReqBuilder) Id(id string) *DeleteCategoryReqBuilder {
	builder.apiReq.PathParams.Set("id", fmt.Sprint(id))
	return builder
}

func (builder *DeleteCategoryReqBuilder) Build() *DeleteCategoryReq {
	req := &DeleteCategoryReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	return req
}

type DeleteCategoryReq struct {
	apiReq *larkcore.ApiReq
}

type DeleteCategoryResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *DeleteCategoryResp) Success() bool {
	return resp.Code == 0
}

type GetCategoryReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewGetCategoryReqBuilder() *GetCategoryReqBuilder {
	builder := &GetCategoryReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 知识库分类ID
//
// 示例值：6948728206392295444
func (builder *GetCategoryReqBuilder) Id(id string) *GetCategoryReqBuilder {
	builder.apiReq.PathParams.Set("id", fmt.Sprint(id))
	return builder
}

func (builder *GetCategoryReqBuilder) Build() *GetCategoryReq {
	req := &GetCategoryReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	return req
}

type GetCategoryReq struct {
	apiReq *larkcore.ApiReq
}

type GetCategoryRespData struct {
	CategoryId *string `json:"category_id,omitempty"` // category id
	Id         *string `json:"id,omitempty"`          // category id, for backward compatibility
	Name       *string `json:"name,omitempty"`        // category name
	HelpdeskId *string `json:"helpdesk_id,omitempty"` // helpdesk id
	Language   *string `json:"language,omitempty"`    // category language
}

type GetCategoryResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *GetCategoryRespData `json:"data"` // 业务数据
}

func (resp *GetCategoryResp) Success() bool {
	return resp.Code == 0
}

type ListCategoryReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewListCategoryReqBuilder() *ListCategoryReqBuilder {
	builder := &ListCategoryReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 知识库分类语言
//
// 示例值：zh_cn
func (builder *ListCategoryReqBuilder) Lang(lang string) *ListCategoryReqBuilder {
	builder.apiReq.QueryParams.Set("lang", fmt.Sprint(lang))
	return builder
}

// 排序键。1: 根据知识库分类更新时间排序
//
// 示例值：1
func (builder *ListCategoryReqBuilder) OrderBy(orderBy int) *ListCategoryReqBuilder {
	builder.apiReq.QueryParams.Set("order_by", fmt.Sprint(orderBy))
	return builder
}

// 顺序。true: 正序；false：反序
//
// 示例值：true
func (builder *ListCategoryReqBuilder) Asc(asc bool) *ListCategoryReqBuilder {
	builder.apiReq.QueryParams.Set("asc", fmt.Sprint(asc))
	return builder
}

func (builder *ListCategoryReqBuilder) Build() *ListCategoryReq {
	req := &ListCategoryReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type ListCategoryReq struct {
	apiReq *larkcore.ApiReq
}

type ListCategoryRespData struct {
	Categories []*Category `json:"categories,omitempty"` // 知识库分类列表
}

type ListCategoryResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *ListCategoryRespData `json:"data"` // 业务数据
}

func (resp *ListCategoryResp) Success() bool {
	return resp.Code == 0
}

type PatchCategoryReqBuilder struct {
	apiReq   *larkcore.ApiReq
	category *Category
}

func NewPatchCategoryReqBuilder() *PatchCategoryReqBuilder {
	builder := &PatchCategoryReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// category id
//
// 示例值：6948728206392295444
func (builder *PatchCategoryReqBuilder) Id(id string) *PatchCategoryReqBuilder {
	builder.apiReq.PathParams.Set("id", fmt.Sprint(id))
	return builder
}

// 该接口用于更新知识库分类详情。
func (builder *PatchCategoryReqBuilder) Category(category *Category) *PatchCategoryReqBuilder {
	builder.category = category
	return builder
}

func (builder *PatchCategoryReqBuilder) Build() *PatchCategoryReq {
	req := &PatchCategoryReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.Body = builder.category
	return req
}

type PatchCategoryReq struct {
	apiReq   *larkcore.ApiReq
	Category *Category `body:""`
}

type PatchCategoryResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *PatchCategoryResp) Success() bool {
	return resp.Code == 0
}

type SubscribeEventReqBodyBuilder struct {
	events     []*Event // 可订阅的事件列表
	eventsFlag bool
}

func NewSubscribeEventReqBodyBuilder() *SubscribeEventReqBodyBuilder {
	builder := &SubscribeEventReqBodyBuilder{}
	return builder
}

// 可订阅的事件列表
//
//示例值：
func (builder *SubscribeEventReqBodyBuilder) Events(events []*Event) *SubscribeEventReqBodyBuilder {
	builder.events = events
	builder.eventsFlag = true
	return builder
}

func (builder *SubscribeEventReqBodyBuilder) Build() *SubscribeEventReqBody {
	req := &SubscribeEventReqBody{}
	if builder.eventsFlag {
		req.Events = builder.events
	}
	return req
}

type SubscribeEventPathReqBodyBuilder struct {
	events     []*Event // 可订阅的事件列表
	eventsFlag bool
}

func NewSubscribeEventPathReqBodyBuilder() *SubscribeEventPathReqBodyBuilder {
	builder := &SubscribeEventPathReqBodyBuilder{}
	return builder
}

// 可订阅的事件列表
//
// 示例值：
func (builder *SubscribeEventPathReqBodyBuilder) Events(events []*Event) *SubscribeEventPathReqBodyBuilder {
	builder.events = events
	builder.eventsFlag = true
	return builder
}

func (builder *SubscribeEventPathReqBodyBuilder) Build() (*SubscribeEventReqBody, error) {
	req := &SubscribeEventReqBody{}
	if builder.eventsFlag {
		req.Events = builder.events
	}
	return req, nil
}

type SubscribeEventReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *SubscribeEventReqBody
}

func NewSubscribeEventReqBuilder() *SubscribeEventReqBuilder {
	builder := &SubscribeEventReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 用于订阅服务台事件
func (builder *SubscribeEventReqBuilder) Body(body *SubscribeEventReqBody) *SubscribeEventReqBuilder {
	builder.body = body
	return builder
}

func (builder *SubscribeEventReqBuilder) Build() *SubscribeEventReq {
	req := &SubscribeEventReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.Body = builder.body
	return req
}

type SubscribeEventReqBody struct {
	Events []*Event `json:"events,omitempty"` // 可订阅的事件列表
}

type SubscribeEventReq struct {
	apiReq *larkcore.ApiReq
	Body   *SubscribeEventReqBody `body:""`
}

type SubscribeEventResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *SubscribeEventResp) Success() bool {
	return resp.Code == 0
}

type UnsubscribeEventReqBodyBuilder struct {
	events     []*Event // event list to unsubscribe
	eventsFlag bool
}

func NewUnsubscribeEventReqBodyBuilder() *UnsubscribeEventReqBodyBuilder {
	builder := &UnsubscribeEventReqBodyBuilder{}
	return builder
}

// event list to unsubscribe
//
//示例值：
func (builder *UnsubscribeEventReqBodyBuilder) Events(events []*Event) *UnsubscribeEventReqBodyBuilder {
	builder.events = events
	builder.eventsFlag = true
	return builder
}

func (builder *UnsubscribeEventReqBodyBuilder) Build() *UnsubscribeEventReqBody {
	req := &UnsubscribeEventReqBody{}
	if builder.eventsFlag {
		req.Events = builder.events
	}
	return req
}

type UnsubscribeEventPathReqBodyBuilder struct {
	events     []*Event // event list to unsubscribe
	eventsFlag bool
}

func NewUnsubscribeEventPathReqBodyBuilder() *UnsubscribeEventPathReqBodyBuilder {
	builder := &UnsubscribeEventPathReqBodyBuilder{}
	return builder
}

// event list to unsubscribe
//
// 示例值：
func (builder *UnsubscribeEventPathReqBodyBuilder) Events(events []*Event) *UnsubscribeEventPathReqBodyBuilder {
	builder.events = events
	builder.eventsFlag = true
	return builder
}

func (builder *UnsubscribeEventPathReqBodyBuilder) Build() (*UnsubscribeEventReqBody, error) {
	req := &UnsubscribeEventReqBody{}
	if builder.eventsFlag {
		req.Events = builder.events
	}
	return req, nil
}

type UnsubscribeEventReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *UnsubscribeEventReqBody
}

func NewUnsubscribeEventReqBuilder() *UnsubscribeEventReqBuilder {
	builder := &UnsubscribeEventReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 用于取消订阅服务台事件
func (builder *UnsubscribeEventReqBuilder) Body(body *UnsubscribeEventReqBody) *UnsubscribeEventReqBuilder {
	builder.body = body
	return builder
}

func (builder *UnsubscribeEventReqBuilder) Build() *UnsubscribeEventReq {
	req := &UnsubscribeEventReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.Body = builder.body
	return req
}

type UnsubscribeEventReqBody struct {
	Events []*Event `json:"events,omitempty"` // event list to unsubscribe
}

type UnsubscribeEventReq struct {
	apiReq *larkcore.ApiReq
	Body   *UnsubscribeEventReqBody `body:""`
}

type UnsubscribeEventResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *UnsubscribeEventResp) Success() bool {
	return resp.Code == 0
}

type CreateFaqReqBodyBuilder struct {
	faq     *FaqUpdateInfo // 知识库详情
	faqFlag bool
}

func NewCreateFaqReqBodyBuilder() *CreateFaqReqBodyBuilder {
	builder := &CreateFaqReqBodyBuilder{}
	return builder
}

// 知识库详情
//
//示例值：
func (builder *CreateFaqReqBodyBuilder) Faq(faq *FaqUpdateInfo) *CreateFaqReqBodyBuilder {
	builder.faq = faq
	builder.faqFlag = true
	return builder
}

func (builder *CreateFaqReqBodyBuilder) Build() *CreateFaqReqBody {
	req := &CreateFaqReqBody{}
	if builder.faqFlag {
		req.Faq = builder.faq
	}
	return req
}

type CreateFaqPathReqBodyBuilder struct {
	faq     *FaqUpdateInfo // 知识库详情
	faqFlag bool
}

func NewCreateFaqPathReqBodyBuilder() *CreateFaqPathReqBodyBuilder {
	builder := &CreateFaqPathReqBodyBuilder{}
	return builder
}

// 知识库详情
//
// 示例值：
func (builder *CreateFaqPathReqBodyBuilder) Faq(faq *FaqUpdateInfo) *CreateFaqPathReqBodyBuilder {
	builder.faq = faq
	builder.faqFlag = true
	return builder
}

func (builder *CreateFaqPathReqBodyBuilder) Build() (*CreateFaqReqBody, error) {
	req := &CreateFaqReqBody{}
	if builder.faqFlag {
		req.Faq = builder.faq
	}
	return req, nil
}

type CreateFaqReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *CreateFaqReqBody
}

func NewCreateFaqReqBuilder() *CreateFaqReqBuilder {
	builder := &CreateFaqReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 该接口用于创建知识库。
func (builder *CreateFaqReqBuilder) Body(body *CreateFaqReqBody) *CreateFaqReqBuilder {
	builder.body = body
	return builder
}

func (builder *CreateFaqReqBuilder) Build() *CreateFaqReq {
	req := &CreateFaqReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.Body = builder.body
	return req
}

type CreateFaqReqBody struct {
	Faq *FaqUpdateInfo `json:"faq,omitempty"` // 知识库详情
}

type CreateFaqReq struct {
	apiReq *larkcore.ApiReq
	Body   *CreateFaqReqBody `body:""`
}

type CreateFaqRespData struct {
	Faq *Faq `json:"faq,omitempty"` // 知识库详情
}

type CreateFaqResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *CreateFaqRespData `json:"data"` // 业务数据
}

func (resp *CreateFaqResp) Success() bool {
	return resp.Code == 0
}

type DeleteFaqReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewDeleteFaqReqBuilder() *DeleteFaqReqBuilder {
	builder := &DeleteFaqReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// id
//
// 示例值：12345
func (builder *DeleteFaqReqBuilder) Id(id string) *DeleteFaqReqBuilder {
	builder.apiReq.PathParams.Set("id", fmt.Sprint(id))
	return builder
}

func (builder *DeleteFaqReqBuilder) Build() *DeleteFaqReq {
	req := &DeleteFaqReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	return req
}

type DeleteFaqReq struct {
	apiReq *larkcore.ApiReq
}

type DeleteFaqResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *DeleteFaqResp) Success() bool {
	return resp.Code == 0
}

type FaqImageFaqReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewFaqImageFaqReqBuilder() *FaqImageFaqReqBuilder {
	builder := &FaqImageFaqReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 知识库ID
//
// 示例值：12345
func (builder *FaqImageFaqReqBuilder) Id(id string) *FaqImageFaqReqBuilder {
	builder.apiReq.PathParams.Set("id", fmt.Sprint(id))
	return builder
}

// 图像key
//
// 示例值：img_b07ffac0-19c1-48a3-afca-599f8ea825fj
func (builder *FaqImageFaqReqBuilder) ImageKey(imageKey string) *FaqImageFaqReqBuilder {
	builder.apiReq.PathParams.Set("image_key", fmt.Sprint(imageKey))
	return builder
}

func (builder *FaqImageFaqReqBuilder) Build() *FaqImageFaqReq {
	req := &FaqImageFaqReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	return req
}

type FaqImageFaqReq struct {
	apiReq *larkcore.ApiReq
}

type FaqImageFaqResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	File     io.Reader `json:"-"`
	FileName string    `json:"-"`
}

func (resp *FaqImageFaqResp) Success() bool {
	return resp.Code == 0
}

func (resp *FaqImageFaqResp) WriteFile(fileName string) error {
	bs, err := ioutil.ReadAll(resp.File)
	if err != nil {
		return err
	}

	err = ioutil.WriteFile(fileName, bs, 0666)
	if err != nil {
		return err
	}
	return nil
}

type GetFaqReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewGetFaqReqBuilder() *GetFaqReqBuilder {
	builder := &GetFaqReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 知识库ID
//
// 示例值：6856395634652479491
func (builder *GetFaqReqBuilder) Id(id string) *GetFaqReqBuilder {
	builder.apiReq.PathParams.Set("id", fmt.Sprint(id))
	return builder
}

func (builder *GetFaqReqBuilder) Build() *GetFaqReq {
	req := &GetFaqReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	return req
}

type GetFaqReq struct {
	apiReq *larkcore.ApiReq
}

type GetFaqRespData struct {
	Faq *Faq `json:"faq,omitempty"` // 知识库详情
}

type GetFaqResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *GetFaqRespData `json:"data"` // 业务数据
}

func (resp *GetFaqResp) Success() bool {
	return resp.Code == 0
}

type ListFaqReqBuilder struct {
	apiReq *larkcore.ApiReq
	limit  int // 最大返回多少记录，当使用迭代器访问时才有效
}

func NewListFaqReqBuilder() *ListFaqReqBuilder {
	builder := &ListFaqReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 最大返回多少记录，当使用迭代器访问时才有效
func (builder *ListFaqReqBuilder) Limit(limit int) *ListFaqReqBuilder {
	builder.limit = limit
	return builder
}

//  知识库分类ID
//
// 示例值：6856395522433908739
func (builder *ListFaqReqBuilder) CategoryId(categoryId string) *ListFaqReqBuilder {
	builder.apiReq.QueryParams.Set("category_id", fmt.Sprint(categoryId))
	return builder
}

// 搜索条件: 知识库状态 1:在线 0:删除，可恢复 2：删除，不可恢复
//
// 示例值：1
func (builder *ListFaqReqBuilder) Status(status string) *ListFaqReqBuilder {
	builder.apiReq.QueryParams.Set("status", fmt.Sprint(status))
	return builder
}

// 搜索条件: 关键词，匹配问题标题，问题关键字，用户姓名
//
// 示例值：点餐
func (builder *ListFaqReqBuilder) Search(search string) *ListFaqReqBuilder {
	builder.apiReq.QueryParams.Set("search", fmt.Sprint(search))
	return builder
}

//
//
// 示例值：6856395634652479491
func (builder *ListFaqReqBuilder) PageToken(pageToken string) *ListFaqReqBuilder {
	builder.apiReq.QueryParams.Set("page_token", fmt.Sprint(pageToken))
	return builder
}

//
//
// 示例值：10
func (builder *ListFaqReqBuilder) PageSize(pageSize int) *ListFaqReqBuilder {
	builder.apiReq.QueryParams.Set("page_size", fmt.Sprint(pageSize))
	return builder
}

func (builder *ListFaqReqBuilder) Build() *ListFaqReq {
	req := &ListFaqReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.Limit = builder.limit
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type ListFaqReq struct {
	apiReq *larkcore.ApiReq
	Limit  int // 最多返回多少记录，只有在使用迭代器访问时，才有效

}

type ListFaqRespData struct {
	HasMore   *bool   `json:"has_more,omitempty"`   // if there's next page
	PageToken *string `json:"page_token,omitempty"` // the next page token
	PageSize  *int    `json:"page_size,omitempty"`  // 实际返回的FAQ数量
	Total     *int    `json:"total,omitempty"`      // 总数
	Items     []*Faq  `json:"items,omitempty"`      // 知识库列表
}

type ListFaqResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *ListFaqRespData `json:"data"` // 业务数据
}

func (resp *ListFaqResp) Success() bool {
	return resp.Code == 0
}

type PatchFaqReqBodyBuilder struct {
	faq     *FaqUpdateInfo // 修改的知识库内容
	faqFlag bool
}

func NewPatchFaqReqBodyBuilder() *PatchFaqReqBodyBuilder {
	builder := &PatchFaqReqBodyBuilder{}
	return builder
}

// 修改的知识库内容
//
//示例值：
func (builder *PatchFaqReqBodyBuilder) Faq(faq *FaqUpdateInfo) *PatchFaqReqBodyBuilder {
	builder.faq = faq
	builder.faqFlag = true
	return builder
}

func (builder *PatchFaqReqBodyBuilder) Build() *PatchFaqReqBody {
	req := &PatchFaqReqBody{}
	if builder.faqFlag {
		req.Faq = builder.faq
	}
	return req
}

type PatchFaqPathReqBodyBuilder struct {
	faq     *FaqUpdateInfo // 修改的知识库内容
	faqFlag bool
}

func NewPatchFaqPathReqBodyBuilder() *PatchFaqPathReqBodyBuilder {
	builder := &PatchFaqPathReqBodyBuilder{}
	return builder
}

// 修改的知识库内容
//
// 示例值：
func (builder *PatchFaqPathReqBodyBuilder) Faq(faq *FaqUpdateInfo) *PatchFaqPathReqBodyBuilder {
	builder.faq = faq
	builder.faqFlag = true
	return builder
}

func (builder *PatchFaqPathReqBodyBuilder) Build() (*PatchFaqReqBody, error) {
	req := &PatchFaqReqBody{}
	if builder.faqFlag {
		req.Faq = builder.faq
	}
	return req, nil
}

type PatchFaqReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *PatchFaqReqBody
}

func NewPatchFaqReqBuilder() *PatchFaqReqBuilder {
	builder := &PatchFaqReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 知识库ID
//
// 示例值：6856395634652479491
func (builder *PatchFaqReqBuilder) Id(id string) *PatchFaqReqBuilder {
	builder.apiReq.PathParams.Set("id", fmt.Sprint(id))
	return builder
}

// 该接口用于修改知识库。
func (builder *PatchFaqReqBuilder) Body(body *PatchFaqReqBody) *PatchFaqReqBuilder {
	builder.body = body
	return builder
}

func (builder *PatchFaqReqBuilder) Build() *PatchFaqReq {
	req := &PatchFaqReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.Body = builder.body
	return req
}

type PatchFaqReqBody struct {
	Faq *FaqUpdateInfo `json:"faq,omitempty"` // 修改的知识库内容
}

type PatchFaqReq struct {
	apiReq *larkcore.ApiReq
	Body   *PatchFaqReqBody `body:""`
}

type PatchFaqResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *PatchFaqResp) Success() bool {
	return resp.Code == 0
}

type SearchFaqReqBuilder struct {
	apiReq *larkcore.ApiReq
	limit  int // 最大返回多少记录，当使用迭代器访问时才有效
}

func NewSearchFaqReqBuilder() *SearchFaqReqBuilder {
	builder := &SearchFaqReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 最大返回多少记录，当使用迭代器访问时才有效
func (builder *SearchFaqReqBuilder) Limit(limit int) *SearchFaqReqBuilder {
	builder.limit = limit
	return builder
}

// 搜索query;，query内容如果不是英文，包含中文空格等有两种编码策略：1. url编码 2. base64编码，同时加上base64=true参数
//
// 示例值：wifi
func (builder *SearchFaqReqBuilder) Query(query string) *SearchFaqReqBuilder {
	builder.apiReq.QueryParams.Set("query", fmt.Sprint(query))
	return builder
}

// 是否转换为base64,输入true表示是，不填写表示否，中文需要转换为base64
//
// 示例值：5bel5Y2V
func (builder *SearchFaqReqBuilder) Base64(base64 string) *SearchFaqReqBuilder {
	builder.apiReq.QueryParams.Set("base64", fmt.Sprint(base64))
	return builder
}

//
//
// 示例值：6936004780707807251
func (builder *SearchFaqReqBuilder) PageToken(pageToken string) *SearchFaqReqBuilder {
	builder.apiReq.QueryParams.Set("page_token", fmt.Sprint(pageToken))
	return builder
}

//
//
// 示例值：10
func (builder *SearchFaqReqBuilder) PageSize(pageSize int) *SearchFaqReqBuilder {
	builder.apiReq.QueryParams.Set("page_size", fmt.Sprint(pageSize))
	return builder
}

func (builder *SearchFaqReqBuilder) Build() *SearchFaqReq {
	req := &SearchFaqReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.Limit = builder.limit
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type SearchFaqReq struct {
	apiReq *larkcore.ApiReq
	Limit  int // 最多返回多少记录，只有在使用迭代器访问时，才有效

}

type SearchFaqRespData struct {
	HasMore   *bool   `json:"has_more,omitempty"`   // if there's next page
	PageToken *string `json:"page_token,omitempty"` // the next page token
	Items     []*Faq  `json:"items,omitempty"`      // 知识库列表
}

type SearchFaqResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *SearchFaqRespData `json:"data"` // 业务数据
}

func (resp *SearchFaqResp) Success() bool {
	return resp.Code == 0
}

type CancelApproveNotificationReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewCancelApproveNotificationReqBuilder() *CancelApproveNotificationReqBuilder {
	builder := &CancelApproveNotificationReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 唯一ID
//
// 示例值：6981801914270744596
func (builder *CancelApproveNotificationReqBuilder) NotificationId(notificationId string) *CancelApproveNotificationReqBuilder {
	builder.apiReq.PathParams.Set("notification_id", fmt.Sprint(notificationId))
	return builder
}

func (builder *CancelApproveNotificationReqBuilder) Build() *CancelApproveNotificationReq {
	req := &CancelApproveNotificationReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	return req
}

type CancelApproveNotificationReq struct {
	apiReq *larkcore.ApiReq
}

type CancelApproveNotificationResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *CancelApproveNotificationResp) Success() bool {
	return resp.Code == 0
}

type CancelSendNotificationReqBodyBuilder struct {
	isRecall     bool // 是否召回已发送的消息,新人入职消息同样适用
	isRecallFlag bool
}

func NewCancelSendNotificationReqBodyBuilder() *CancelSendNotificationReqBodyBuilder {
	builder := &CancelSendNotificationReqBodyBuilder{}
	return builder
}

// 是否召回已发送的消息,新人入职消息同样适用
//
//示例值：true
func (builder *CancelSendNotificationReqBodyBuilder) IsRecall(isRecall bool) *CancelSendNotificationReqBodyBuilder {
	builder.isRecall = isRecall
	builder.isRecallFlag = true
	return builder
}

func (builder *CancelSendNotificationReqBodyBuilder) Build() *CancelSendNotificationReqBody {
	req := &CancelSendNotificationReqBody{}
	if builder.isRecallFlag {
		req.IsRecall = &builder.isRecall
	}
	return req
}

type CancelSendNotificationPathReqBodyBuilder struct {
	isRecall     bool // 是否召回已发送的消息,新人入职消息同样适用
	isRecallFlag bool
}

func NewCancelSendNotificationPathReqBodyBuilder() *CancelSendNotificationPathReqBodyBuilder {
	builder := &CancelSendNotificationPathReqBodyBuilder{}
	return builder
}

// 是否召回已发送的消息,新人入职消息同样适用
//
// 示例值：true
func (builder *CancelSendNotificationPathReqBodyBuilder) IsRecall(isRecall bool) *CancelSendNotificationPathReqBodyBuilder {
	builder.isRecall = isRecall
	builder.isRecallFlag = true
	return builder
}

func (builder *CancelSendNotificationPathReqBodyBuilder) Build() (*CancelSendNotificationReqBody, error) {
	req := &CancelSendNotificationReqBody{}
	if builder.isRecallFlag {
		req.IsRecall = &builder.isRecall
	}
	return req, nil
}

type CancelSendNotificationReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *CancelSendNotificationReqBody
}

func NewCancelSendNotificationReqBuilder() *CancelSendNotificationReqBuilder {
	builder := &CancelSendNotificationReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 唯一ID
//
// 示例值：6981801914270744596
func (builder *CancelSendNotificationReqBuilder) NotificationId(notificationId string) *CancelSendNotificationReqBuilder {
	builder.apiReq.PathParams.Set("notification_id", fmt.Sprint(notificationId))
	return builder
}

// 取消推送接口，审核通过后待调度可以调用，发送过程中可以调用（会撤回已发送的消息），发送完成后可以需要推送（会撤回所有已发送的消息）
func (builder *CancelSendNotificationReqBuilder) Body(body *CancelSendNotificationReqBody) *CancelSendNotificationReqBuilder {
	builder.body = body
	return builder
}

func (builder *CancelSendNotificationReqBuilder) Build() *CancelSendNotificationReq {
	req := &CancelSendNotificationReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.Body = builder.body
	return req
}

type CancelSendNotificationReqBody struct {
	IsRecall *bool `json:"is_recall,omitempty"` // 是否召回已发送的消息,新人入职消息同样适用
}

type CancelSendNotificationReq struct {
	apiReq *larkcore.ApiReq
	Body   *CancelSendNotificationReqBody `body:""`
}

type CancelSendNotificationResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *CancelSendNotificationResp) Success() bool {
	return resp.Code == 0
}

type CreateNotificationReqBuilder struct {
	apiReq       *larkcore.ApiReq
	notification *Notification
}

func NewCreateNotificationReqBuilder() *CreateNotificationReqBuilder {
	builder := &CreateNotificationReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 此次调用中使用的用户ID的类型
//
// 示例值：
func (builder *CreateNotificationReqBuilder) UserIdType(userIdType string) *CreateNotificationReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

// 调用接口创建推送，创建成功后为草稿状态
func (builder *CreateNotificationReqBuilder) Notification(notification *Notification) *CreateNotificationReqBuilder {
	builder.notification = notification
	return builder
}

func (builder *CreateNotificationReqBuilder) Build() *CreateNotificationReq {
	req := &CreateNotificationReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.notification
	return req
}

type CreateNotificationReq struct {
	apiReq       *larkcore.ApiReq
	Notification *Notification `body:""`
}

type CreateNotificationRespData struct {
	NotificationId *string `json:"notification_id,omitempty"` // 创建成功后的唯一id
	Status         *int    `json:"status,omitempty"`          // 当前状态
}

type CreateNotificationResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *CreateNotificationRespData `json:"data"` // 业务数据
}

func (resp *CreateNotificationResp) Success() bool {
	return resp.Code == 0
}

type ExecuteSendNotificationReqBodyBuilder struct {
	sendAt     string // 发送时间戳(毫秒)
	sendAtFlag bool
}

func NewExecuteSendNotificationReqBodyBuilder() *ExecuteSendNotificationReqBodyBuilder {
	builder := &ExecuteSendNotificationReqBodyBuilder{}
	return builder
}

// 发送时间戳(毫秒)
//
//示例值：1624326025000
func (builder *ExecuteSendNotificationReqBodyBuilder) SendAt(sendAt string) *ExecuteSendNotificationReqBodyBuilder {
	builder.sendAt = sendAt
	builder.sendAtFlag = true
	return builder
}

func (builder *ExecuteSendNotificationReqBodyBuilder) Build() *ExecuteSendNotificationReqBody {
	req := &ExecuteSendNotificationReqBody{}
	if builder.sendAtFlag {
		req.SendAt = &builder.sendAt
	}
	return req
}

type ExecuteSendNotificationPathReqBodyBuilder struct {
	sendAt     string // 发送时间戳(毫秒)
	sendAtFlag bool
}

func NewExecuteSendNotificationPathReqBodyBuilder() *ExecuteSendNotificationPathReqBodyBuilder {
	builder := &ExecuteSendNotificationPathReqBodyBuilder{}
	return builder
}

// 发送时间戳(毫秒)
//
// 示例值：1624326025000
func (builder *ExecuteSendNotificationPathReqBodyBuilder) SendAt(sendAt string) *ExecuteSendNotificationPathReqBodyBuilder {
	builder.sendAt = sendAt
	builder.sendAtFlag = true
	return builder
}

func (builder *ExecuteSendNotificationPathReqBodyBuilder) Build() (*ExecuteSendNotificationReqBody, error) {
	req := &ExecuteSendNotificationReqBody{}
	if builder.sendAtFlag {
		req.SendAt = &builder.sendAt
	}
	return req, nil
}

type ExecuteSendNotificationReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *ExecuteSendNotificationReqBody
}

func NewExecuteSendNotificationReqBuilder() *ExecuteSendNotificationReqBuilder {
	builder := &ExecuteSendNotificationReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 创建接口返回的唯一id
//
// 示例值：6985032626234982420
func (builder *ExecuteSendNotificationReqBuilder) NotificationId(notificationId string) *ExecuteSendNotificationReqBuilder {
	builder.apiReq.PathParams.Set("notification_id", fmt.Sprint(notificationId))
	return builder
}

// 审核通过后调用此接口设置推送时间，等待调度系统调度，发送消息
func (builder *ExecuteSendNotificationReqBuilder) Body(body *ExecuteSendNotificationReqBody) *ExecuteSendNotificationReqBuilder {
	builder.body = body
	return builder
}

func (builder *ExecuteSendNotificationReqBuilder) Build() *ExecuteSendNotificationReq {
	req := &ExecuteSendNotificationReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.Body = builder.body
	return req
}

type ExecuteSendNotificationReqBody struct {
	SendAt *string `json:"send_at,omitempty"` // 发送时间戳(毫秒)
}

type ExecuteSendNotificationReq struct {
	apiReq *larkcore.ApiReq
	Body   *ExecuteSendNotificationReqBody `body:""`
}

type ExecuteSendNotificationResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *ExecuteSendNotificationResp) Success() bool {
	return resp.Code == 0
}

type GetNotificationReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewGetNotificationReqBuilder() *GetNotificationReqBuilder {
	builder := &GetNotificationReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 唯一ID
//
// 示例值：1624326025000
func (builder *GetNotificationReqBuilder) NotificationId(notificationId string) *GetNotificationReqBuilder {
	builder.apiReq.PathParams.Set("notification_id", fmt.Sprint(notificationId))
	return builder
}

// 此次调用中使用的用户ID的类型
//
// 示例值：
func (builder *GetNotificationReqBuilder) UserIdType(userIdType string) *GetNotificationReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

func (builder *GetNotificationReqBuilder) Build() *GetNotificationReq {
	req := &GetNotificationReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type GetNotificationReq struct {
	apiReq *larkcore.ApiReq
}

type GetNotificationRespData struct {
	Notification    *Notification `json:"notification,omitempty"`      // push任务详情
	ApprovalAppLink *string       `json:"approval_app_link,omitempty"` // 审批链接
}

type GetNotificationResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *GetNotificationRespData `json:"data"` // 业务数据
}

func (resp *GetNotificationResp) Success() bool {
	return resp.Code == 0
}

type PatchNotificationReqBuilder struct {
	apiReq       *larkcore.ApiReq
	notification *Notification
}

func NewPatchNotificationReqBuilder() *PatchNotificationReqBuilder {
	builder := &PatchNotificationReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// push任务唯一id
//
// 示例值：6985032626234982420
func (builder *PatchNotificationReqBuilder) NotificationId(notificationId string) *PatchNotificationReqBuilder {
	builder.apiReq.PathParams.Set("notification_id", fmt.Sprint(notificationId))
	return builder
}

// 此次调用中使用的用户ID的类型
//
// 示例值：
func (builder *PatchNotificationReqBuilder) UserIdType(userIdType string) *PatchNotificationReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

// 更新推送信息，只有在草稿状态下才可以调用此接口进行更新
func (builder *PatchNotificationReqBuilder) Notification(notification *Notification) *PatchNotificationReqBuilder {
	builder.notification = notification
	return builder
}

func (builder *PatchNotificationReqBuilder) Build() *PatchNotificationReq {
	req := &PatchNotificationReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.notification
	return req
}

type PatchNotificationReq struct {
	apiReq       *larkcore.ApiReq
	Notification *Notification `body:""`
}

type PatchNotificationResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *PatchNotificationResp) Success() bool {
	return resp.Code == 0
}

type PreviewNotificationReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewPreviewNotificationReqBuilder() *PreviewNotificationReqBuilder {
	builder := &PreviewNotificationReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 创建推送接口成功后返回的唯一id
//
// 示例值：6985032626234982420
func (builder *PreviewNotificationReqBuilder) NotificationId(notificationId string) *PreviewNotificationReqBuilder {
	builder.apiReq.PathParams.Set("notification_id", fmt.Sprint(notificationId))
	return builder
}

func (builder *PreviewNotificationReqBuilder) Build() *PreviewNotificationReq {
	req := &PreviewNotificationReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	return req
}

type PreviewNotificationReq struct {
	apiReq *larkcore.ApiReq
}

type PreviewNotificationResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *PreviewNotificationResp) Success() bool {
	return resp.Code == 0
}

type SubmitApproveNotificationReqBodyBuilder struct {
	reason     string // 提交审批理由
	reasonFlag bool
}

func NewSubmitApproveNotificationReqBodyBuilder() *SubmitApproveNotificationReqBodyBuilder {
	builder := &SubmitApproveNotificationReqBodyBuilder{}
	return builder
}

// 提交审批理由
//
//示例值：测试发送消息
func (builder *SubmitApproveNotificationReqBodyBuilder) Reason(reason string) *SubmitApproveNotificationReqBodyBuilder {
	builder.reason = reason
	builder.reasonFlag = true
	return builder
}

func (builder *SubmitApproveNotificationReqBodyBuilder) Build() *SubmitApproveNotificationReqBody {
	req := &SubmitApproveNotificationReqBody{}
	if builder.reasonFlag {
		req.Reason = &builder.reason
	}
	return req
}

type SubmitApproveNotificationPathReqBodyBuilder struct {
	reason     string // 提交审批理由
	reasonFlag bool
}

func NewSubmitApproveNotificationPathReqBodyBuilder() *SubmitApproveNotificationPathReqBodyBuilder {
	builder := &SubmitApproveNotificationPathReqBodyBuilder{}
	return builder
}

// 提交审批理由
//
// 示例值：测试发送消息
func (builder *SubmitApproveNotificationPathReqBodyBuilder) Reason(reason string) *SubmitApproveNotificationPathReqBodyBuilder {
	builder.reason = reason
	builder.reasonFlag = true
	return builder
}

func (builder *SubmitApproveNotificationPathReqBodyBuilder) Build() (*SubmitApproveNotificationReqBody, error) {
	req := &SubmitApproveNotificationReqBody{}
	if builder.reasonFlag {
		req.Reason = &builder.reason
	}
	return req, nil
}

type SubmitApproveNotificationReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *SubmitApproveNotificationReqBody
}

func NewSubmitApproveNotificationReqBuilder() *SubmitApproveNotificationReqBuilder {
	builder := &SubmitApproveNotificationReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 创建接口返回的唯一id
//
// 示例值：6985032626234982420
func (builder *SubmitApproveNotificationReqBuilder) NotificationId(notificationId string) *SubmitApproveNotificationReqBuilder {
	builder.apiReq.PathParams.Set("notification_id", fmt.Sprint(notificationId))
	return builder
}

// 正常情况下调用创建推送接口后，就可以调用提交审核接口，如果创建人是服务台owner则会自动审核通过，否则会通知服务台owner审核此推送信息
func (builder *SubmitApproveNotificationReqBuilder) Body(body *SubmitApproveNotificationReqBody) *SubmitApproveNotificationReqBuilder {
	builder.body = body
	return builder
}

func (builder *SubmitApproveNotificationReqBuilder) Build() *SubmitApproveNotificationReq {
	req := &SubmitApproveNotificationReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.Body = builder.body
	return req
}

type SubmitApproveNotificationReqBody struct {
	Reason *string `json:"reason,omitempty"` // 提交审批理由
}

type SubmitApproveNotificationReq struct {
	apiReq *larkcore.ApiReq
	Body   *SubmitApproveNotificationReqBody `body:""`
}

type SubmitApproveNotificationRespData struct {
	HasAccess *bool `json:"has_access,omitempty"` // 是否有权限创建或者管理审批流程 （有两种情况会导致没有权限： 1：用户没有安装服务台小程序，需要在https://app.feishu.cn/app/cli_9f9f8825d53b900d 安装小程序 2：用户安装的服务台小程序版本过低）
}

type SubmitApproveNotificationResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *SubmitApproveNotificationRespData `json:"data"` // 业务数据
}

func (resp *SubmitApproveNotificationResp) Success() bool {
	return resp.Code == 0
}

type AnswerUserQueryTicketReqBodyBuilder struct {
	eventId     string // 事件ID,可从订阅事件中提取
	eventIdFlag bool
	faqs        []*UserQueryFaqInfo // faq结果列表
	faqsFlag    bool
}

func NewAnswerUserQueryTicketReqBodyBuilder() *AnswerUserQueryTicketReqBodyBuilder {
	builder := &AnswerUserQueryTicketReqBodyBuilder{}
	return builder
}

// 事件ID,可从订阅事件中提取
//
//示例值：abcd
func (builder *AnswerUserQueryTicketReqBodyBuilder) EventId(eventId string) *AnswerUserQueryTicketReqBodyBuilder {
	builder.eventId = eventId
	builder.eventIdFlag = true
	return builder
}

// faq结果列表
//
//示例值：
func (builder *AnswerUserQueryTicketReqBodyBuilder) Faqs(faqs []*UserQueryFaqInfo) *AnswerUserQueryTicketReqBodyBuilder {
	builder.faqs = faqs
	builder.faqsFlag = true
	return builder
}

func (builder *AnswerUserQueryTicketReqBodyBuilder) Build() *AnswerUserQueryTicketReqBody {
	req := &AnswerUserQueryTicketReqBody{}
	if builder.eventIdFlag {
		req.EventId = &builder.eventId
	}
	if builder.faqsFlag {
		req.Faqs = builder.faqs
	}
	return req
}

type AnswerUserQueryTicketPathReqBodyBuilder struct {
	eventId     string // 事件ID,可从订阅事件中提取
	eventIdFlag bool
	faqs        []*UserQueryFaqInfo // faq结果列表
	faqsFlag    bool
}

func NewAnswerUserQueryTicketPathReqBodyBuilder() *AnswerUserQueryTicketPathReqBodyBuilder {
	builder := &AnswerUserQueryTicketPathReqBodyBuilder{}
	return builder
}

// 事件ID,可从订阅事件中提取
//
// 示例值：abcd
func (builder *AnswerUserQueryTicketPathReqBodyBuilder) EventId(eventId string) *AnswerUserQueryTicketPathReqBodyBuilder {
	builder.eventId = eventId
	builder.eventIdFlag = true
	return builder
}

// faq结果列表
//
// 示例值：
func (builder *AnswerUserQueryTicketPathReqBodyBuilder) Faqs(faqs []*UserQueryFaqInfo) *AnswerUserQueryTicketPathReqBodyBuilder {
	builder.faqs = faqs
	builder.faqsFlag = true
	return builder
}

func (builder *AnswerUserQueryTicketPathReqBodyBuilder) Build() (*AnswerUserQueryTicketReqBody, error) {
	req := &AnswerUserQueryTicketReqBody{}
	if builder.eventIdFlag {
		req.EventId = &builder.eventId
	}
	if builder.faqsFlag {
		req.Faqs = builder.faqs
	}
	return req, nil
}

type AnswerUserQueryTicketReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *AnswerUserQueryTicketReqBody
}

func NewAnswerUserQueryTicketReqBuilder() *AnswerUserQueryTicketReqBuilder {
	builder := &AnswerUserQueryTicketReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 工单ID
//
// 示例值：6945345902185807891
func (builder *AnswerUserQueryTicketReqBuilder) TicketId(ticketId string) *AnswerUserQueryTicketReqBuilder {
	builder.apiReq.PathParams.Set("ticket_id", fmt.Sprint(ticketId))
	return builder
}

// 该接口用于回复用户提问结果至工单，需要工单仍处于进行中且未接入人工状态。仅支持自建应用。
func (builder *AnswerUserQueryTicketReqBuilder) Body(body *AnswerUserQueryTicketReqBody) *AnswerUserQueryTicketReqBuilder {
	builder.body = body
	return builder
}

func (builder *AnswerUserQueryTicketReqBuilder) Build() *AnswerUserQueryTicketReq {
	req := &AnswerUserQueryTicketReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.Body = builder.body
	return req
}

type AnswerUserQueryTicketReqBody struct {
	EventId *string             `json:"event_id,omitempty"` // 事件ID,可从订阅事件中提取
	Faqs    []*UserQueryFaqInfo `json:"faqs,omitempty"`     // faq结果列表
}

type AnswerUserQueryTicketReq struct {
	apiReq *larkcore.ApiReq
	Body   *AnswerUserQueryTicketReqBody `body:""`
}

type AnswerUserQueryTicketResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *AnswerUserQueryTicketResp) Success() bool {
	return resp.Code == 0
}

type CustomizedFieldsTicketReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewCustomizedFieldsTicketReqBuilder() *CustomizedFieldsTicketReqBuilder {
	builder := &CustomizedFieldsTicketReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// visible only
//
// 示例值：true
func (builder *CustomizedFieldsTicketReqBuilder) VisibleOnly(visibleOnly bool) *CustomizedFieldsTicketReqBuilder {
	builder.apiReq.QueryParams.Set("visible_only", fmt.Sprint(visibleOnly))
	return builder
}

func (builder *CustomizedFieldsTicketReqBuilder) Build() *CustomizedFieldsTicketReq {
	req := &CustomizedFieldsTicketReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type CustomizedFieldsTicketReq struct {
	apiReq *larkcore.ApiReq
}

type CustomizedFieldsTicketRespData struct {
	UserCustomizedFields   []*UserCustomizedField   `json:"user_customized_fields,omitempty"`   // 用户自定义字段
	TicketCustomizedFields []*TicketCustomizedField `json:"ticket_customized_fields,omitempty"` // 自定义工单字段
}

type CustomizedFieldsTicketResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *CustomizedFieldsTicketRespData `json:"data"` // 业务数据
}

func (resp *CustomizedFieldsTicketResp) Success() bool {
	return resp.Code == 0
}

type GetTicketReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewGetTicketReqBuilder() *GetTicketReqBuilder {
	builder := &GetTicketReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// ticket id
//
// 示例值：123456
func (builder *GetTicketReqBuilder) TicketId(ticketId string) *GetTicketReqBuilder {
	builder.apiReq.PathParams.Set("ticket_id", fmt.Sprint(ticketId))
	return builder
}

func (builder *GetTicketReqBuilder) Build() *GetTicketReq {
	req := &GetTicketReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	return req
}

type GetTicketReq struct {
	apiReq *larkcore.ApiReq
}

type GetTicketRespData struct {
	Ticket *Ticket `json:"ticket,omitempty"` // 工单详情
}

type GetTicketResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *GetTicketRespData `json:"data"` // 业务数据
}

func (resp *GetTicketResp) Success() bool {
	return resp.Code == 0
}

type ListTicketReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewListTicketReqBuilder() *ListTicketReqBuilder {
	builder := &ListTicketReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 搜索条件：工单ID
//
// 示例值：123456
func (builder *ListTicketReqBuilder) TicketId(ticketId string) *ListTicketReqBuilder {
	builder.apiReq.QueryParams.Set("ticket_id", fmt.Sprint(ticketId))
	return builder
}

// 搜索条件: 客服id
//
// 示例值：ou_b5de90429xxx
func (builder *ListTicketReqBuilder) AgentId(agentId string) *ListTicketReqBuilder {
	builder.apiReq.QueryParams.Set("agent_id", fmt.Sprint(agentId))
	return builder
}

// 搜索条件: 关单客服id
//
// 示例值：ou_b5de90429xxx
func (builder *ListTicketReqBuilder) ClosedById(closedById string) *ListTicketReqBuilder {
	builder.apiReq.QueryParams.Set("closed_by_id", fmt.Sprint(closedById))
	return builder
}

// 搜索条件: 工单类型 1:bot 2:人工
//
// 示例值：1
func (builder *ListTicketReqBuilder) Type(type_ int) *ListTicketReqBuilder {
	builder.apiReq.QueryParams.Set("type", fmt.Sprint(type_))
	return builder
}

// 搜索条件: 工单渠道
//
// 示例值：0
func (builder *ListTicketReqBuilder) Channel(channel int) *ListTicketReqBuilder {
	builder.apiReq.QueryParams.Set("channel", fmt.Sprint(channel))
	return builder
}

// 搜索条件: 工单是否解决 1:没解决 2:已解决
//
// 示例值：1
func (builder *ListTicketReqBuilder) Solved(solved int) *ListTicketReqBuilder {
	builder.apiReq.QueryParams.Set("solved", fmt.Sprint(solved))
	return builder
}

// 搜索条件: 工单评分
//
// 示例值：1
func (builder *ListTicketReqBuilder) Score(score int) *ListTicketReqBuilder {
	builder.apiReq.QueryParams.Set("score", fmt.Sprint(score))
	return builder
}

// 搜索条件: 工单状态列表
//
// 示例值：1
func (builder *ListTicketReqBuilder) StatusList(statusList []int) *ListTicketReqBuilder {
	for _, v := range statusList {
		builder.apiReq.QueryParams.Add("status_list", fmt.Sprint(v))
	}
	return builder
}

// 搜索条件: 用户名称
//
// 示例值：abc
func (builder *ListTicketReqBuilder) GuestName(guestName string) *ListTicketReqBuilder {
	builder.apiReq.QueryParams.Set("guest_name", fmt.Sprint(guestName))
	return builder
}

// 搜索条件: 用户id
//
// 示例值：ou_b5de90429xxx
func (builder *ListTicketReqBuilder) GuestId(guestId string) *ListTicketReqBuilder {
	builder.apiReq.QueryParams.Set("guest_id", fmt.Sprint(guestId))
	return builder
}

// 搜索条件: 用户标签列表
//
// 示例值：备注
func (builder *ListTicketReqBuilder) Tags(tags []string) *ListTicketReqBuilder {
	for _, v := range tags {
		builder.apiReq.QueryParams.Add("tags", fmt.Sprint(v))
	}
	return builder
}

// 页数, 从1开始, 默认为1
//
// 示例值：1
func (builder *ListTicketReqBuilder) Page(page int) *ListTicketReqBuilder {
	builder.apiReq.QueryParams.Set("page", fmt.Sprint(page))
	return builder
}

// 当前页大小，最大为200， 默认为20。分页查询最多累计返回一万条数据，超过一万条请更改查询条件，推荐通过时间查询。
//
// 示例值：20
func (builder *ListTicketReqBuilder) PageSize(pageSize int) *ListTicketReqBuilder {
	builder.apiReq.QueryParams.Set("page_size", fmt.Sprint(pageSize))
	return builder
}

// 搜索条件: 工单创建起始时间 ms (也需要填上create_time_end)，相当于>=create_time_start
//
// 示例值：1616920429000
func (builder *ListTicketReqBuilder) CreateTimeStart(createTimeStart int) *ListTicketReqBuilder {
	builder.apiReq.QueryParams.Set("create_time_start", fmt.Sprint(createTimeStart))
	return builder
}

// 搜索条件: 工单创建结束时间 ms (也需要填上create_time_start)，相当于<=create_time_end
//
// 示例值：1616920429000
func (builder *ListTicketReqBuilder) CreateTimeEnd(createTimeEnd int) *ListTicketReqBuilder {
	builder.apiReq.QueryParams.Set("create_time_end", fmt.Sprint(createTimeEnd))
	return builder
}

// 搜索条件: 工单修改起始时间 ms (也需要填上update_time_end)
//
// 示例值：1616920429000
func (builder *ListTicketReqBuilder) UpdateTimeStart(updateTimeStart int) *ListTicketReqBuilder {
	builder.apiReq.QueryParams.Set("update_time_start", fmt.Sprint(updateTimeStart))
	return builder
}

// 搜索条件: 工单修改结束时间 ms(也需要填上update_time_start)
//
// 示例值：1616920429000
func (builder *ListTicketReqBuilder) UpdateTimeEnd(updateTimeEnd int) *ListTicketReqBuilder {
	builder.apiReq.QueryParams.Set("update_time_end", fmt.Sprint(updateTimeEnd))
	return builder
}

func (builder *ListTicketReqBuilder) Build() *ListTicketReq {
	req := &ListTicketReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type ListTicketReq struct {
	apiReq *larkcore.ApiReq
}

type ListTicketRespData struct {
	Total   *int      `json:"total,omitempty"`   // 工单总数 (单次请求最大为10000条)
	Tickets []*Ticket `json:"tickets,omitempty"` // 工单
}

type ListTicketResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *ListTicketRespData `json:"data"` // 业务数据
}

func (resp *ListTicketResp) Success() bool {
	return resp.Code == 0
}

type StartServiceTicketReqBodyBuilder struct {
	humanService        bool // 是否直接进入人工(若appointed_agents填写了，该值为必填)
	humanServiceFlag    bool
	appointedAgents     []string // 客服 open ids (获取方式参考[获取单个用户信息](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/contact-v3/user/get))，human_service需要为true
	appointedAgentsFlag bool
	openId              string // 用户 open id,(获取方式参考[获取单个用户信息](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/contact-v3/user/get))
	openIdFlag          bool
	customizedInfo      string // 工单来源自定义信息，长度限制1024字符，如设置，[获取工单详情](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/helpdesk-v1/ticket/get)会返回此信息
	customizedInfoFlag  bool
}

func NewStartServiceTicketReqBodyBuilder() *StartServiceTicketReqBodyBuilder {
	builder := &StartServiceTicketReqBodyBuilder{}
	return builder
}

// 是否直接进入人工(若appointed_agents填写了，该值为必填)
//
//示例值：false
func (builder *StartServiceTicketReqBodyBuilder) HumanService(humanService bool) *StartServiceTicketReqBodyBuilder {
	builder.humanService = humanService
	builder.humanServiceFlag = true
	return builder
}

// 客服 open ids (获取方式参考[获取单个用户信息](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/contact-v3/user/get))，human_service需要为true
//
//示例值：[ou_7dab8a3d3cdcc9da365777c7ad535d62]
func (builder *StartServiceTicketReqBodyBuilder) AppointedAgents(appointedAgents []string) *StartServiceTicketReqBodyBuilder {
	builder.appointedAgents = appointedAgents
	builder.appointedAgentsFlag = true
	return builder
}

// 用户 open id,(获取方式参考[获取单个用户信息](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/contact-v3/user/get))
//
//示例值：ou_7dab8a3d3cdcc9da365777c7ad535d62
func (builder *StartServiceTicketReqBodyBuilder) OpenId(openId string) *StartServiceTicketReqBodyBuilder {
	builder.openId = openId
	builder.openIdFlag = true
	return builder
}

// 工单来源自定义信息，长度限制1024字符，如设置，[获取工单详情](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/helpdesk-v1/ticket/get)会返回此信息
//
//示例值：测试自定义字段信息
func (builder *StartServiceTicketReqBodyBuilder) CustomizedInfo(customizedInfo string) *StartServiceTicketReqBodyBuilder {
	builder.customizedInfo = customizedInfo
	builder.customizedInfoFlag = true
	return builder
}

func (builder *StartServiceTicketReqBodyBuilder) Build() *StartServiceTicketReqBody {
	req := &StartServiceTicketReqBody{}
	if builder.humanServiceFlag {
		req.HumanService = &builder.humanService
	}
	if builder.appointedAgentsFlag {
		req.AppointedAgents = builder.appointedAgents
	}
	if builder.openIdFlag {
		req.OpenId = &builder.openId
	}
	if builder.customizedInfoFlag {
		req.CustomizedInfo = &builder.customizedInfo
	}
	return req
}

type StartServiceTicketPathReqBodyBuilder struct {
	humanService        bool // 是否直接进入人工(若appointed_agents填写了，该值为必填)
	humanServiceFlag    bool
	appointedAgents     []string // 客服 open ids (获取方式参考[获取单个用户信息](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/contact-v3/user/get))，human_service需要为true
	appointedAgentsFlag bool
	openId              string // 用户 open id,(获取方式参考[获取单个用户信息](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/contact-v3/user/get))
	openIdFlag          bool
	customizedInfo      string // 工单来源自定义信息，长度限制1024字符，如设置，[获取工单详情](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/helpdesk-v1/ticket/get)会返回此信息
	customizedInfoFlag  bool
}

func NewStartServiceTicketPathReqBodyBuilder() *StartServiceTicketPathReqBodyBuilder {
	builder := &StartServiceTicketPathReqBodyBuilder{}
	return builder
}

// 是否直接进入人工(若appointed_agents填写了，该值为必填)
//
// 示例值：false
func (builder *StartServiceTicketPathReqBodyBuilder) HumanService(humanService bool) *StartServiceTicketPathReqBodyBuilder {
	builder.humanService = humanService
	builder.humanServiceFlag = true
	return builder
}

// 客服 open ids (获取方式参考[获取单个用户信息](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/contact-v3/user/get))，human_service需要为true
//
// 示例值：[ou_7dab8a3d3cdcc9da365777c7ad535d62]
func (builder *StartServiceTicketPathReqBodyBuilder) AppointedAgents(appointedAgents []string) *StartServiceTicketPathReqBodyBuilder {
	builder.appointedAgents = appointedAgents
	builder.appointedAgentsFlag = true
	return builder
}

// 用户 open id,(获取方式参考[获取单个用户信息](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/contact-v3/user/get))
//
// 示例值：ou_7dab8a3d3cdcc9da365777c7ad535d62
func (builder *StartServiceTicketPathReqBodyBuilder) OpenId(openId string) *StartServiceTicketPathReqBodyBuilder {
	builder.openId = openId
	builder.openIdFlag = true
	return builder
}

// 工单来源自定义信息，长度限制1024字符，如设置，[获取工单详情](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/helpdesk-v1/ticket/get)会返回此信息
//
// 示例值：测试自定义字段信息
func (builder *StartServiceTicketPathReqBodyBuilder) CustomizedInfo(customizedInfo string) *StartServiceTicketPathReqBodyBuilder {
	builder.customizedInfo = customizedInfo
	builder.customizedInfoFlag = true
	return builder
}

func (builder *StartServiceTicketPathReqBodyBuilder) Build() (*StartServiceTicketReqBody, error) {
	req := &StartServiceTicketReqBody{}
	if builder.humanServiceFlag {
		req.HumanService = &builder.humanService
	}
	if builder.appointedAgentsFlag {
		req.AppointedAgents = builder.appointedAgents
	}
	if builder.openIdFlag {
		req.OpenId = &builder.openId
	}
	if builder.customizedInfoFlag {
		req.CustomizedInfo = &builder.customizedInfo
	}
	return req, nil
}

type StartServiceTicketReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *StartServiceTicketReqBody
}

func NewStartServiceTicketReqBuilder() *StartServiceTicketReqBuilder {
	builder := &StartServiceTicketReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 该接口用于创建服务台对话。
func (builder *StartServiceTicketReqBuilder) Body(body *StartServiceTicketReqBody) *StartServiceTicketReqBuilder {
	builder.body = body
	return builder
}

func (builder *StartServiceTicketReqBuilder) Build() *StartServiceTicketReq {
	req := &StartServiceTicketReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.Body = builder.body
	return req
}

type StartServiceTicketReqBody struct {
	HumanService    *bool    `json:"human_service,omitempty"`    // 是否直接进入人工(若appointed_agents填写了，该值为必填)
	AppointedAgents []string `json:"appointed_agents,omitempty"` // 客服 open ids (获取方式参考[获取单个用户信息](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/contact-v3/user/get))，human_service需要为true
	OpenId          *string  `json:"open_id,omitempty"`          // 用户 open id,(获取方式参考[获取单个用户信息](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/contact-v3/user/get))
	CustomizedInfo  *string  `json:"customized_info,omitempty"`  // 工单来源自定义信息，长度限制1024字符，如设置，[获取工单详情](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/helpdesk-v1/ticket/get)会返回此信息
}

type StartServiceTicketReq struct {
	apiReq *larkcore.ApiReq
	Body   *StartServiceTicketReqBody `body:""`
}

type StartServiceTicketRespData struct {
	ChatId *string `json:"chat_id,omitempty"` // 客服群open ID
}

type StartServiceTicketResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *StartServiceTicketRespData `json:"data"` // 业务数据
}

func (resp *StartServiceTicketResp) Success() bool {
	return resp.Code == 0
}

type TicketImageTicketReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewTicketImageTicketReqBuilder() *TicketImageTicketReqBuilder {
	builder := &TicketImageTicketReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 工单ID
//
// 示例值：12345
func (builder *TicketImageTicketReqBuilder) TicketId(ticketId string) *TicketImageTicketReqBuilder {
	builder.apiReq.QueryParams.Set("ticket_id", fmt.Sprint(ticketId))
	return builder
}

// 消息ID;;[查询消息ID](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/helpdesk-v1/ticket-message/list)
//
// 示例值：12345
func (builder *TicketImageTicketReqBuilder) MsgId(msgId string) *TicketImageTicketReqBuilder {
	builder.apiReq.QueryParams.Set("msg_id", fmt.Sprint(msgId))
	return builder
}

// index，当消息类型为post时，需指定图片index，index从0开始。当消息类型为img时，无需index
//
// 示例值：0
func (builder *TicketImageTicketReqBuilder) Index(index int) *TicketImageTicketReqBuilder {
	builder.apiReq.QueryParams.Set("index", fmt.Sprint(index))
	return builder
}

func (builder *TicketImageTicketReqBuilder) Build() *TicketImageTicketReq {
	req := &TicketImageTicketReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type TicketImageTicketReq struct {
	apiReq *larkcore.ApiReq
}

type TicketImageTicketResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	File     io.Reader `json:"-"`
	FileName string    `json:"-"`
}

func (resp *TicketImageTicketResp) Success() bool {
	return resp.Code == 0
}

func (resp *TicketImageTicketResp) WriteFile(fileName string) error {
	bs, err := ioutil.ReadAll(resp.File)
	if err != nil {
		return err
	}

	err = ioutil.WriteFile(fileName, bs, 0666)
	if err != nil {
		return err
	}
	return nil
}

type UpdateTicketReqBodyBuilder struct {
	status               int // new status, 1: 已创建, 2: 处理中, 3: 排队中, 5: 待定, 50: 机器人关闭工单, 51: 关闭工单
	statusFlag           bool
	tagNames             []string // 新标签名
	tagNamesFlag         bool
	comment              string // 新评论
	commentFlag          bool
	customizedFields     []*CustomizedFieldDisplayItem // 自定义字段
	customizedFieldsFlag bool
	ticketType           int // ticket stage
	ticketTypeFlag       bool
	solved               int // 工单是否解决，1: 未解决, 2: 已解决
	solvedFlag           bool
	channel              int // 工单来源渠道ID
	channelFlag          bool
}

func NewUpdateTicketReqBodyBuilder() *UpdateTicketReqBodyBuilder {
	builder := &UpdateTicketReqBodyBuilder{}
	return builder
}

// new status, 1: 已创建, 2: 处理中, 3: 排队中, 5: 待定, 50: 机器人关闭工单, 51: 关闭工单
//
//示例值：1
func (builder *UpdateTicketReqBodyBuilder) Status(status int) *UpdateTicketReqBodyBuilder {
	builder.status = status
	builder.statusFlag = true
	return builder
}

// 新标签名
//
//示例值：abc
func (builder *UpdateTicketReqBodyBuilder) TagNames(tagNames []string) *UpdateTicketReqBodyBuilder {
	builder.tagNames = tagNames
	builder.tagNamesFlag = true
	return builder
}

// 新评论
//
//示例值：good
func (builder *UpdateTicketReqBodyBuilder) Comment(comment string) *UpdateTicketReqBodyBuilder {
	builder.comment = comment
	builder.commentFlag = true
	return builder
}

// 自定义字段
//
//示例值：
func (builder *UpdateTicketReqBodyBuilder) CustomizedFields(customizedFields []*CustomizedFieldDisplayItem) *UpdateTicketReqBodyBuilder {
	builder.customizedFields = customizedFields
	builder.customizedFieldsFlag = true
	return builder
}

// ticket stage
//
//示例值：1
func (builder *UpdateTicketReqBodyBuilder) TicketType(ticketType int) *UpdateTicketReqBodyBuilder {
	builder.ticketType = ticketType
	builder.ticketTypeFlag = true
	return builder
}

// 工单是否解决，1: 未解决, 2: 已解决
//
//示例值：1
func (builder *UpdateTicketReqBodyBuilder) Solved(solved int) *UpdateTicketReqBodyBuilder {
	builder.solved = solved
	builder.solvedFlag = true
	return builder
}

// 工单来源渠道ID
//
//示例值：1
func (builder *UpdateTicketReqBodyBuilder) Channel(channel int) *UpdateTicketReqBodyBuilder {
	builder.channel = channel
	builder.channelFlag = true
	return builder
}

func (builder *UpdateTicketReqBodyBuilder) Build() *UpdateTicketReqBody {
	req := &UpdateTicketReqBody{}
	if builder.statusFlag {
		req.Status = &builder.status
	}
	if builder.tagNamesFlag {
		req.TagNames = builder.tagNames
	}
	if builder.commentFlag {
		req.Comment = &builder.comment
	}
	if builder.customizedFieldsFlag {
		req.CustomizedFields = builder.customizedFields
	}
	if builder.ticketTypeFlag {
		req.TicketType = &builder.ticketType
	}
	if builder.solvedFlag {
		req.Solved = &builder.solved
	}
	if builder.channelFlag {
		req.Channel = &builder.channel
	}
	return req
}

type UpdateTicketPathReqBodyBuilder struct {
	status               int // new status, 1: 已创建, 2: 处理中, 3: 排队中, 5: 待定, 50: 机器人关闭工单, 51: 关闭工单
	statusFlag           bool
	tagNames             []string // 新标签名
	tagNamesFlag         bool
	comment              string // 新评论
	commentFlag          bool
	customizedFields     []*CustomizedFieldDisplayItem // 自定义字段
	customizedFieldsFlag bool
	ticketType           int // ticket stage
	ticketTypeFlag       bool
	solved               int // 工单是否解决，1: 未解决, 2: 已解决
	solvedFlag           bool
	channel              int // 工单来源渠道ID
	channelFlag          bool
}

func NewUpdateTicketPathReqBodyBuilder() *UpdateTicketPathReqBodyBuilder {
	builder := &UpdateTicketPathReqBodyBuilder{}
	return builder
}

// new status, 1: 已创建, 2: 处理中, 3: 排队中, 5: 待定, 50: 机器人关闭工单, 51: 关闭工单
//
// 示例值：1
func (builder *UpdateTicketPathReqBodyBuilder) Status(status int) *UpdateTicketPathReqBodyBuilder {
	builder.status = status
	builder.statusFlag = true
	return builder
}

// 新标签名
//
// 示例值：abc
func (builder *UpdateTicketPathReqBodyBuilder) TagNames(tagNames []string) *UpdateTicketPathReqBodyBuilder {
	builder.tagNames = tagNames
	builder.tagNamesFlag = true
	return builder
}

// 新评论
//
// 示例值：good
func (builder *UpdateTicketPathReqBodyBuilder) Comment(comment string) *UpdateTicketPathReqBodyBuilder {
	builder.comment = comment
	builder.commentFlag = true
	return builder
}

// 自定义字段
//
// 示例值：
func (builder *UpdateTicketPathReqBodyBuilder) CustomizedFields(customizedFields []*CustomizedFieldDisplayItem) *UpdateTicketPathReqBodyBuilder {
	builder.customizedFields = customizedFields
	builder.customizedFieldsFlag = true
	return builder
}

// ticket stage
//
// 示例值：1
func (builder *UpdateTicketPathReqBodyBuilder) TicketType(ticketType int) *UpdateTicketPathReqBodyBuilder {
	builder.ticketType = ticketType
	builder.ticketTypeFlag = true
	return builder
}

// 工单是否解决，1: 未解决, 2: 已解决
//
// 示例值：1
func (builder *UpdateTicketPathReqBodyBuilder) Solved(solved int) *UpdateTicketPathReqBodyBuilder {
	builder.solved = solved
	builder.solvedFlag = true
	return builder
}

// 工单来源渠道ID
//
// 示例值：1
func (builder *UpdateTicketPathReqBodyBuilder) Channel(channel int) *UpdateTicketPathReqBodyBuilder {
	builder.channel = channel
	builder.channelFlag = true
	return builder
}

func (builder *UpdateTicketPathReqBodyBuilder) Build() (*UpdateTicketReqBody, error) {
	req := &UpdateTicketReqBody{}
	if builder.statusFlag {
		req.Status = &builder.status
	}
	if builder.tagNamesFlag {
		req.TagNames = builder.tagNames
	}
	if builder.commentFlag {
		req.Comment = &builder.comment
	}
	if builder.customizedFieldsFlag {
		req.CustomizedFields = builder.customizedFields
	}
	if builder.ticketTypeFlag {
		req.TicketType = &builder.ticketType
	}
	if builder.solvedFlag {
		req.Solved = &builder.solved
	}
	if builder.channelFlag {
		req.Channel = &builder.channel
	}
	return req, nil
}

type UpdateTicketReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *UpdateTicketReqBody
}

func NewUpdateTicketReqBuilder() *UpdateTicketReqBuilder {
	builder := &UpdateTicketReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 工单ID
//
// 示例值：6945345902185807891
func (builder *UpdateTicketReqBuilder) TicketId(ticketId string) *UpdateTicketReqBuilder {
	builder.apiReq.PathParams.Set("ticket_id", fmt.Sprint(ticketId))
	return builder
}

// 该接口用于更新服务台工单详情。只会更新数据，不会触发相关操作。如修改工单状态到关单，不会关闭聊天页面。仅支持自建应用。要更新的工单字段必须至少输入一项。
func (builder *UpdateTicketReqBuilder) Body(body *UpdateTicketReqBody) *UpdateTicketReqBuilder {
	builder.body = body
	return builder
}

func (builder *UpdateTicketReqBuilder) Build() *UpdateTicketReq {
	req := &UpdateTicketReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.Body = builder.body
	return req
}

type UpdateTicketReqBody struct {
	Status           *int                          `json:"status,omitempty"`            // new status, 1: 已创建, 2: 处理中, 3: 排队中, 5: 待定, 50: 机器人关闭工单, 51: 关闭工单
	TagNames         []string                      `json:"tag_names,omitempty"`         // 新标签名
	Comment          *string                       `json:"comment,omitempty"`           // 新评论
	CustomizedFields []*CustomizedFieldDisplayItem `json:"customized_fields,omitempty"` // 自定义字段
	TicketType       *int                          `json:"ticket_type,omitempty"`       // ticket stage
	Solved           *int                          `json:"solved,omitempty"`            // 工单是否解决，1: 未解决, 2: 已解决
	Channel          *int                          `json:"channel,omitempty"`           // 工单来源渠道ID
}

type UpdateTicketReq struct {
	apiReq *larkcore.ApiReq
	Body   *UpdateTicketReqBody `body:""`
}

type UpdateTicketResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *UpdateTicketResp) Success() bool {
	return resp.Code == 0
}

type CreateTicketMessageReqBodyBuilder struct {
	msgType     string // 消息类型；text：纯文本；post：富文本
	msgTypeFlag bool
	content     string // - 纯文本，参考[发送文本消息](https://open.feishu.cn/document/ukTMukTMukTM/uUjNz4SN2MjL1YzM)中的content；;- 富文本，参考[发送富文本消息](https://open.feishu.cn/document/ukTMukTMukTM/uMDMxEjLzATMx4yMwETM)中的content
	contentFlag bool
}

func NewCreateTicketMessageReqBodyBuilder() *CreateTicketMessageReqBodyBuilder {
	builder := &CreateTicketMessageReqBodyBuilder{}
	return builder
}

// 消息类型；text：纯文本；post：富文本
//
//示例值：post
func (builder *CreateTicketMessageReqBodyBuilder) MsgType(msgType string) *CreateTicketMessageReqBodyBuilder {
	builder.msgType = msgType
	builder.msgTypeFlag = true
	return builder
}

// - 纯文本，参考[发送文本消息](https://open.feishu.cn/document/ukTMukTMukTM/uUjNz4SN2MjL1YzM)中的content；;- 富文本，参考[发送富文本消息](https://open.feishu.cn/document/ukTMukTMukTM/uMDMxEjLzATMx4yMwETM)中的content
//
//示例值：{;	"msg_type": "post",;	"content": {;		"post": {;			"zh_cn": {;				"title": "this is title",;				"content": [;					[;						{;							"tag": "text",;							"un_escape": true,;							"text": "第一行&nbsp;:";						},;						{;							"tag": "a",;							"text": "超链接",;							"href": "http://www.feishu.cn";						};					],;					[;						{;							"tag": "text",;							"text": "第二行 :";						},;						{;							"tag": "text",;							"text": "文本测试";						};					];				];			};		};	};}
func (builder *CreateTicketMessageReqBodyBuilder) Content(content string) *CreateTicketMessageReqBodyBuilder {
	builder.content = content
	builder.contentFlag = true
	return builder
}

func (builder *CreateTicketMessageReqBodyBuilder) Build() *CreateTicketMessageReqBody {
	req := &CreateTicketMessageReqBody{}
	if builder.msgTypeFlag {
		req.MsgType = &builder.msgType
	}
	if builder.contentFlag {
		req.Content = &builder.content
	}
	return req
}

type CreateTicketMessagePathReqBodyBuilder struct {
	msgType     string // 消息类型；text：纯文本；post：富文本
	msgTypeFlag bool
	content     string // - 纯文本，参考[发送文本消息](https://open.feishu.cn/document/ukTMukTMukTM/uUjNz4SN2MjL1YzM)中的content；;- 富文本，参考[发送富文本消息](https://open.feishu.cn/document/ukTMukTMukTM/uMDMxEjLzATMx4yMwETM)中的content
	contentFlag bool
}

func NewCreateTicketMessagePathReqBodyBuilder() *CreateTicketMessagePathReqBodyBuilder {
	builder := &CreateTicketMessagePathReqBodyBuilder{}
	return builder
}

// 消息类型；text：纯文本；post：富文本
//
// 示例值：post
func (builder *CreateTicketMessagePathReqBodyBuilder) MsgType(msgType string) *CreateTicketMessagePathReqBodyBuilder {
	builder.msgType = msgType
	builder.msgTypeFlag = true
	return builder
}

// - 纯文本，参考[发送文本消息](https://open.feishu.cn/document/ukTMukTMukTM/uUjNz4SN2MjL1YzM)中的content；;- 富文本，参考[发送富文本消息](https://open.feishu.cn/document/ukTMukTMukTM/uMDMxEjLzATMx4yMwETM)中的content
//
// 示例值：{;	"msg_type": "post",;	"content": {;		"post": {;			"zh_cn": {;				"title": "this is title",;				"content": [;					[;						{;							"tag": "text",;							"un_escape": true,;							"text": "第一行&nbsp;:";						},;						{;							"tag": "a",;							"text": "超链接",;							"href": "http://www.feishu.cn";						};					],;					[;						{;							"tag": "text",;							"text": "第二行 :";						},;						{;							"tag": "text",;							"text": "文本测试";						};					];				];			};		};	};}
func (builder *CreateTicketMessagePathReqBodyBuilder) Content(content string) *CreateTicketMessagePathReqBodyBuilder {
	builder.content = content
	builder.contentFlag = true
	return builder
}

func (builder *CreateTicketMessagePathReqBodyBuilder) Build() (*CreateTicketMessageReqBody, error) {
	req := &CreateTicketMessageReqBody{}
	if builder.msgTypeFlag {
		req.MsgType = &builder.msgType
	}
	if builder.contentFlag {
		req.Content = &builder.content
	}
	return req, nil
}

type CreateTicketMessageReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *CreateTicketMessageReqBody
}

func NewCreateTicketMessageReqBuilder() *CreateTicketMessageReqBuilder {
	builder := &CreateTicketMessageReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 工单ID
//
// 示例值：6948728206392295444
func (builder *CreateTicketMessageReqBuilder) TicketId(ticketId string) *CreateTicketMessageReqBuilder {
	builder.apiReq.PathParams.Set("ticket_id", fmt.Sprint(ticketId))
	return builder
}

// 该接口用于工单发送消息。
func (builder *CreateTicketMessageReqBuilder) Body(body *CreateTicketMessageReqBody) *CreateTicketMessageReqBuilder {
	builder.body = body
	return builder
}

func (builder *CreateTicketMessageReqBuilder) Build() *CreateTicketMessageReq {
	req := &CreateTicketMessageReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.Body = builder.body
	return req
}

type CreateTicketMessageReqBody struct {
	MsgType *string `json:"msg_type,omitempty"` // 消息类型；text：纯文本；post：富文本
	Content *string `json:"content,omitempty"`  // - 纯文本，参考[发送文本消息](https://open.feishu.cn/document/ukTMukTMukTM/uUjNz4SN2MjL1YzM)中的content；;- 富文本，参考[发送富文本消息](https://open.feishu.cn/document/ukTMukTMukTM/uMDMxEjLzATMx4yMwETM)中的content
}

type CreateTicketMessageReq struct {
	apiReq *larkcore.ApiReq
	Body   *CreateTicketMessageReqBody `body:""`
}

type CreateTicketMessageRespData struct {
	MessageId *string `json:"message_id,omitempty"` // chat消息open ID
}

type CreateTicketMessageResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *CreateTicketMessageRespData `json:"data"` // 业务数据
}

func (resp *CreateTicketMessageResp) Success() bool {
	return resp.Code == 0
}

type ListTicketMessageReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewListTicketMessageReqBuilder() *ListTicketMessageReqBuilder {
	builder := &ListTicketMessageReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 工单ID
//
// 示例值：6948728206392295444
func (builder *ListTicketMessageReqBuilder) TicketId(ticketId string) *ListTicketMessageReqBuilder {
	builder.apiReq.PathParams.Set("ticket_id", fmt.Sprint(ticketId))
	return builder
}

// 起始时间
//
// 示例值：1617960686
func (builder *ListTicketMessageReqBuilder) TimeStart(timeStart int) *ListTicketMessageReqBuilder {
	builder.apiReq.QueryParams.Set("time_start", fmt.Sprint(timeStart))
	return builder
}

// 结束时间
//
// 示例值：1617960687
func (builder *ListTicketMessageReqBuilder) TimeEnd(timeEnd int) *ListTicketMessageReqBuilder {
	builder.apiReq.QueryParams.Set("time_end", fmt.Sprint(timeEnd))
	return builder
}

// 页数ID
//
// 示例值：1
func (builder *ListTicketMessageReqBuilder) Page(page int) *ListTicketMessageReqBuilder {
	builder.apiReq.QueryParams.Set("page", fmt.Sprint(page))
	return builder
}

// 消息数量，最大200，默认20
//
// 示例值：10
func (builder *ListTicketMessageReqBuilder) PageSize(pageSize int) *ListTicketMessageReqBuilder {
	builder.apiReq.QueryParams.Set("page_size", fmt.Sprint(pageSize))
	return builder
}

func (builder *ListTicketMessageReqBuilder) Build() *ListTicketMessageReq {
	req := &ListTicketMessageReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type ListTicketMessageReq struct {
	apiReq *larkcore.ApiReq
}

type ListTicketMessageRespData struct {
	Messages []*TicketMessage `json:"messages,omitempty"` // 工单消息列表
	Total    *int             `json:"total,omitempty"`    // 消息总数
}

type ListTicketMessageResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *ListTicketMessageRespData `json:"data"` // 业务数据
}

func (resp *ListTicketMessageResp) Success() bool {
	return resp.Code == 0
}

type CreateTicketCustomizedFieldReqBuilder struct {
	apiReq                *larkcore.ApiReq
	ticketCustomizedField *TicketCustomizedField
}

func NewCreateTicketCustomizedFieldReqBuilder() *CreateTicketCustomizedFieldReqBuilder {
	builder := &CreateTicketCustomizedFieldReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 该接口用于创建自定义字段
func (builder *CreateTicketCustomizedFieldReqBuilder) TicketCustomizedField(ticketCustomizedField *TicketCustomizedField) *CreateTicketCustomizedFieldReqBuilder {
	builder.ticketCustomizedField = ticketCustomizedField
	return builder
}

func (builder *CreateTicketCustomizedFieldReqBuilder) Build() *CreateTicketCustomizedFieldReq {
	req := &CreateTicketCustomizedFieldReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.Body = builder.ticketCustomizedField
	return req
}

type CreateTicketCustomizedFieldReq struct {
	apiReq                *larkcore.ApiReq
	TicketCustomizedField *TicketCustomizedField `body:""`
}

type CreateTicketCustomizedFieldResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *CreateTicketCustomizedFieldResp) Success() bool {
	return resp.Code == 0
}

type DeleteTicketCustomizedFieldReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewDeleteTicketCustomizedFieldReqBuilder() *DeleteTicketCustomizedFieldReqBuilder {
	builder := &DeleteTicketCustomizedFieldReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 工单自定义字段ID
//
// 示例值：6948728206392295444
func (builder *DeleteTicketCustomizedFieldReqBuilder) TicketCustomizedFieldId(ticketCustomizedFieldId string) *DeleteTicketCustomizedFieldReqBuilder {
	builder.apiReq.PathParams.Set("ticket_customized_field_id", fmt.Sprint(ticketCustomizedFieldId))
	return builder
}

func (builder *DeleteTicketCustomizedFieldReqBuilder) Build() *DeleteTicketCustomizedFieldReq {
	req := &DeleteTicketCustomizedFieldReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	return req
}

type DeleteTicketCustomizedFieldReq struct {
	apiReq *larkcore.ApiReq
}

type DeleteTicketCustomizedFieldResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *DeleteTicketCustomizedFieldResp) Success() bool {
	return resp.Code == 0
}

type GetTicketCustomizedFieldReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewGetTicketCustomizedFieldReqBuilder() *GetTicketCustomizedFieldReqBuilder {
	builder := &GetTicketCustomizedFieldReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 工单自定义字段ID
//
// 示例值：6948728206392295444
func (builder *GetTicketCustomizedFieldReqBuilder) TicketCustomizedFieldId(ticketCustomizedFieldId string) *GetTicketCustomizedFieldReqBuilder {
	builder.apiReq.PathParams.Set("ticket_customized_field_id", fmt.Sprint(ticketCustomizedFieldId))
	return builder
}

func (builder *GetTicketCustomizedFieldReqBuilder) Build() *GetTicketCustomizedFieldReq {
	req := &GetTicketCustomizedFieldReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	return req
}

type GetTicketCustomizedFieldReq struct {
	apiReq *larkcore.ApiReq
}

type GetTicketCustomizedFieldRespData struct {
	TicketCustomizedFieldId *string     `json:"ticket_customized_field_id,omitempty"` // ticket customized field id
	HelpdeskId              *string     `json:"helpdesk_id,omitempty"`                // help desk id
	KeyName                 *string     `json:"key_name,omitempty"`                   // key name
	DisplayName             *string     `json:"display_name,omitempty"`               // display name
	Position                *string     `json:"position,omitempty"`                   // the position of ticket customized field in the page
	FieldType               *string     `json:"field_type,omitempty"`                 // type of the field
	Description             *string     `json:"description,omitempty"`                // description of the field
	Visible                 *bool       `json:"visible,omitempty"`                    // if the field is visible
	Editable                *bool       `json:"editable,omitempty"`                   // if the field is editable
	Required                *bool       `json:"required,omitempty"`                   // if the field is required
	CreatedAt               *string     `json:"created_at,omitempty"`                 // the time when the field is created
	UpdatedAt               *string     `json:"updated_at,omitempty"`                 // the time when the field is updated
	CreatedBy               *TicketUser `json:"created_by,omitempty"`                 // the user who created the ticket customized field
	UpdatedBy               *TicketUser `json:"updated_by,omitempty"`                 // the user who recently updated the ticket customized field
	DropdownAllowMultiple   *bool       `json:"dropdown_allow_multiple,omitempty"`    // if the dropdown field supports multi-select
}

type GetTicketCustomizedFieldResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *GetTicketCustomizedFieldRespData `json:"data"` // 业务数据
}

func (resp *GetTicketCustomizedFieldResp) Success() bool {
	return resp.Code == 0
}

type ListTicketCustomizedFieldReqBodyBuilder struct {
	visible     bool // 是否可见
	visibleFlag bool
}

func NewListTicketCustomizedFieldReqBodyBuilder() *ListTicketCustomizedFieldReqBodyBuilder {
	builder := &ListTicketCustomizedFieldReqBodyBuilder{}
	return builder
}

// 是否可见
//
//示例值：true
func (builder *ListTicketCustomizedFieldReqBodyBuilder) Visible(visible bool) *ListTicketCustomizedFieldReqBodyBuilder {
	builder.visible = visible
	builder.visibleFlag = true
	return builder
}

func (builder *ListTicketCustomizedFieldReqBodyBuilder) Build() *ListTicketCustomizedFieldReqBody {
	req := &ListTicketCustomizedFieldReqBody{}
	if builder.visibleFlag {
		req.Visible = &builder.visible
	}
	return req
}

type ListTicketCustomizedFieldPathReqBodyBuilder struct {
	visible     bool // 是否可见
	visibleFlag bool
}

func NewListTicketCustomizedFieldPathReqBodyBuilder() *ListTicketCustomizedFieldPathReqBodyBuilder {
	builder := &ListTicketCustomizedFieldPathReqBodyBuilder{}
	return builder
}

// 是否可见
//
// 示例值：true
func (builder *ListTicketCustomizedFieldPathReqBodyBuilder) Visible(visible bool) *ListTicketCustomizedFieldPathReqBodyBuilder {
	builder.visible = visible
	builder.visibleFlag = true
	return builder
}

func (builder *ListTicketCustomizedFieldPathReqBodyBuilder) Build() (*ListTicketCustomizedFieldReqBody, error) {
	req := &ListTicketCustomizedFieldReqBody{}
	if builder.visibleFlag {
		req.Visible = &builder.visible
	}
	return req, nil
}

type ListTicketCustomizedFieldReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *ListTicketCustomizedFieldReqBody
}

func NewListTicketCustomizedFieldReqBuilder() *ListTicketCustomizedFieldReqBuilder {
	builder := &ListTicketCustomizedFieldReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

//
//
// 示例值：6948728206392295444
func (builder *ListTicketCustomizedFieldReqBuilder) PageToken(pageToken string) *ListTicketCustomizedFieldReqBuilder {
	builder.apiReq.QueryParams.Set("page_token", fmt.Sprint(pageToken))
	return builder
}

//
//
// 示例值：10；默认为20
func (builder *ListTicketCustomizedFieldReqBuilder) PageSize(pageSize int) *ListTicketCustomizedFieldReqBuilder {
	builder.apiReq.QueryParams.Set("page_size", fmt.Sprint(pageSize))
	return builder
}

// 该接口用于获取全部工单自定义字段。
func (builder *ListTicketCustomizedFieldReqBuilder) Body(body *ListTicketCustomizedFieldReqBody) *ListTicketCustomizedFieldReqBuilder {
	builder.body = body
	return builder
}

func (builder *ListTicketCustomizedFieldReqBuilder) Build() *ListTicketCustomizedFieldReq {
	req := &ListTicketCustomizedFieldReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.body
	return req
}

type ListTicketCustomizedFieldReqBody struct {
	Visible *bool `json:"visible,omitempty"` // 是否可见
}

type ListTicketCustomizedFieldReq struct {
	apiReq *larkcore.ApiReq
	Body   *ListTicketCustomizedFieldReqBody `body:""`
}

type ListTicketCustomizedFieldRespData struct {
	HasMore       *bool                    `json:"has_more,omitempty"`        // whether there is more data
	NextPageToken *string                  `json:"next_page_token,omitempty"` // 下一分页标识
	Items         []*TicketCustomizedField `json:"items,omitempty"`           // 工单自定义字段列表
}

type ListTicketCustomizedFieldResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *ListTicketCustomizedFieldRespData `json:"data"` // 业务数据
}

func (resp *ListTicketCustomizedFieldResp) Success() bool {
	return resp.Code == 0
}

type PatchTicketCustomizedFieldReqBuilder struct {
	apiReq                *larkcore.ApiReq
	ticketCustomizedField *TicketCustomizedField
}

func NewPatchTicketCustomizedFieldReqBuilder() *PatchTicketCustomizedFieldReqBuilder {
	builder := &PatchTicketCustomizedFieldReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 工单自定义字段ID
//
// 示例值：6948728206392295444
func (builder *PatchTicketCustomizedFieldReqBuilder) TicketCustomizedFieldId(ticketCustomizedFieldId string) *PatchTicketCustomizedFieldReqBuilder {
	builder.apiReq.PathParams.Set("ticket_customized_field_id", fmt.Sprint(ticketCustomizedFieldId))
	return builder
}

// 该接口用于更新自定义字段。
func (builder *PatchTicketCustomizedFieldReqBuilder) TicketCustomizedField(ticketCustomizedField *TicketCustomizedField) *PatchTicketCustomizedFieldReqBuilder {
	builder.ticketCustomizedField = ticketCustomizedField
	return builder
}

func (builder *PatchTicketCustomizedFieldReqBuilder) Build() *PatchTicketCustomizedFieldReq {
	req := &PatchTicketCustomizedFieldReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.Body = builder.ticketCustomizedField
	return req
}

type PatchTicketCustomizedFieldReq struct {
	apiReq                *larkcore.ApiReq
	TicketCustomizedField *TicketCustomizedField `body:""`
}

type PatchTicketCustomizedFieldResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *PatchTicketCustomizedFieldResp) Success() bool {
	return resp.Code == 0
}

type P2NotificationApproveV1Data struct {
	NotificationId *string `json:"notification_id,omitempty"` // 推送任务唯一ID
	HelpdeskId     *string `json:"helpdesk_id,omitempty"`     // 服务台唯一ID
	ApproveStatus  *string `json:"approve_status,omitempty"`  // REJECTED(审核不通过);APPROVED(审核通过);CANCELED(取消审核);DELETED(删除审核)
}

type P2NotificationApproveV1 struct {
	*larkevent.EventV2Base                              // 事件基础数据
	*larkevent.EventReq                                 // 请求原生数据
	Event                  *P2NotificationApproveV1Data `json:"event"` // 事件内容
}

func (m *P2NotificationApproveV1) RawReq(req *larkevent.EventReq) {
	m.EventReq = req
}

type P2TicketCreatedV1Data struct {
	TicketId         *string                       `json:"ticket_id,omitempty"`         // ticket id
	HelpdeskId       *string                       `json:"helpdesk_id,omitempty"`       // helpdesk id
	Guest            *TicketUserEvent              `json:"guest,omitempty"`             // guest of this ticket
	Stage            *int                          `json:"stage,omitempty"`             // ticket stage
	Status           *int                          `json:"status,omitempty"`            // ticket status
	Score            *int                          `json:"score,omitempty"`             // ticket score
	CreatedAt        *int                          `json:"created_at,omitempty"`        // the time when the ticket is created
	UpdatedAt        *int                          `json:"updated_at,omitempty"`        // the time when the ticket is updated
	ClosedAt         *int                          `json:"closed_at,omitempty"`         // the time when the ticket is closed
	Channel          *int                          `json:"channel,omitempty"`           // the ticket channel
	Solve            *int                          `json:"solve,omitempty"`             // if ticket is solved
	CustomizedFields []*CustomizedFieldDisplayItem `json:"customized_fields,omitempty"` // ticket customized fields
	ChatId           *string                       `json:"chat_id,omitempty"`           //
}

type P2TicketCreatedV1 struct {
	*larkevent.EventV2Base                        // 事件基础数据
	*larkevent.EventReq                           // 请求原生数据
	Event                  *P2TicketCreatedV1Data `json:"event"` // 事件内容
}

func (m *P2TicketCreatedV1) RawReq(req *larkevent.EventReq) {
	m.EventReq = req
}

type P2TicketUpdatedV1Data struct {
	Object    *TicketEvent           `json:"object,omitempty"`     // ticket after update
	OldObject *TicketEventUpdateInfo `json:"old_object,omitempty"` // ticket before update, only has updated fields
}

type P2TicketUpdatedV1 struct {
	*larkevent.EventV2Base                        // 事件基础数据
	*larkevent.EventReq                           // 请求原生数据
	Event                  *P2TicketUpdatedV1Data `json:"event"` // 事件内容
}

func (m *P2TicketUpdatedV1) RawReq(req *larkevent.EventReq) {
	m.EventReq = req
}

type P2TicketMessageCreatedV1Data struct {
	TicketMessageId *string               `json:"ticket_message_id,omitempty"` // ticket message id
	MessageId       *string               `json:"message_id,omitempty"`        // open message id
	MsgType         *string               `json:"msg_type,omitempty"`          // message type, text is the only supported type
	Position        *string               `json:"position,omitempty"`          // position of the message
	SenderId        *UserId               `json:"sender_id,omitempty"`         // sender's open id, omitted if the sender is the bot
	SenderType      *int                  `json:"sender_type,omitempty"`       // sender type, 1 for bot, 2 for guest, 3 for agent
	Text            *string               `json:"text,omitempty"`              // message content
	Ticket          *Ticket               `json:"ticket,omitempty"`            // ticket related information
	EventId         *string               `json:"event_id,omitempty"`          // event id
	ChatId          *string               `json:"chat_id,omitempty"`           // chat id
	Content         *TicketMessageContent `json:"content,omitempty"`           // message content
}

type P2TicketMessageCreatedV1 struct {
	*larkevent.EventV2Base                               // 事件基础数据
	*larkevent.EventReq                                  // 请求原生数据
	Event                  *P2TicketMessageCreatedV1Data `json:"event"` // 事件内容
}

func (m *P2TicketMessageCreatedV1) RawReq(req *larkevent.EventReq) {
	m.EventReq = req
}

type ListFaqIterator struct {
	nextPageToken *string
	items         []*Faq
	index         int
	limit         int
	ctx           context.Context
	req           *ListFaqReq
	listFunc      func(ctx context.Context, req *ListFaqReq, options ...larkcore.RequestOptionFunc) (*ListFaqResp, error)
	options       []larkcore.RequestOptionFunc
	curlNum       int
}

func (iterator *ListFaqIterator) Next() (bool, *Faq, error) {
	// 达到最大量，则返回
	if iterator.limit > 0 && iterator.curlNum >= iterator.limit {
		return false, nil, nil
	}

	// 为0则拉取数据
	if iterator.index == 0 || iterator.index >= len(iterator.items) {
		if iterator.index != 0 && iterator.nextPageToken == nil {
			return false, nil, nil
		}
		if iterator.nextPageToken != nil {
			iterator.req.apiReq.QueryParams.Set("page_token", *iterator.nextPageToken)
		}
		resp, err := iterator.listFunc(iterator.ctx, iterator.req, iterator.options...)
		if err != nil {
			return false, nil, err
		}

		if resp.Code != 0 {
			return false, nil, errors.New(fmt.Sprintf("Code:%d,Msg:%s", resp.Code, resp.Msg))
		}

		if len(resp.Data.Items) == 0 {
			return false, nil, nil
		}

		iterator.nextPageToken = resp.Data.PageToken
		iterator.items = resp.Data.Items
		iterator.index = 0
	}

	block := iterator.items[iterator.index]
	iterator.index++
	iterator.curlNum++
	return true, block, nil
}

func (iterator *ListFaqIterator) NextPageToken() *string {
	return iterator.nextPageToken
}

type SearchFaqIterator struct {
	nextPageToken *string
	items         []*Faq
	index         int
	limit         int
	ctx           context.Context
	req           *SearchFaqReq
	listFunc      func(ctx context.Context, req *SearchFaqReq, options ...larkcore.RequestOptionFunc) (*SearchFaqResp, error)
	options       []larkcore.RequestOptionFunc
	curlNum       int
}

func (iterator *SearchFaqIterator) Next() (bool, *Faq, error) {
	// 达到最大量，则返回
	if iterator.limit > 0 && iterator.curlNum >= iterator.limit {
		return false, nil, nil
	}

	// 为0则拉取数据
	if iterator.index == 0 || iterator.index >= len(iterator.items) {
		if iterator.index != 0 && iterator.nextPageToken == nil {
			return false, nil, nil
		}
		if iterator.nextPageToken != nil {
			iterator.req.apiReq.QueryParams.Set("page_token", *iterator.nextPageToken)
		}
		resp, err := iterator.listFunc(iterator.ctx, iterator.req, iterator.options...)
		if err != nil {
			return false, nil, err
		}

		if resp.Code != 0 {
			return false, nil, errors.New(fmt.Sprintf("Code:%d,Msg:%s", resp.Code, resp.Msg))
		}

		if len(resp.Data.Items) == 0 {
			return false, nil, nil
		}

		iterator.nextPageToken = resp.Data.PageToken
		iterator.items = resp.Data.Items
		iterator.index = 0
	}

	block := iterator.items[iterator.index]
	iterator.index++
	iterator.curlNum++
	return true, block, nil
}

func (iterator *SearchFaqIterator) NextPageToken() *string {
	return iterator.nextPageToken
}
