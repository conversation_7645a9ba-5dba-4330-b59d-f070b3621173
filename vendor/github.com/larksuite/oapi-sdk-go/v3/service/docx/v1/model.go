// Package docx code generated by oapi sdk gen
/*
 * MIT License
 *
 * Copyright (c) 2022 Lark Technologies Pte. Ltd.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice, shall be included in all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */

package larkdocx

import (
	"fmt"

	"context"
	"errors"

	"github.com/larksuite/oapi-sdk-go/v3/core"
)

const (
	LangZH = 0 // 中文
	LangEN = 1 // 英文
	LangJP = 2 // 日文

)

const (
	UserIdTypeUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypeUnionId = "union_id" // 以union_id来识别用户
	UserIdTypeOpenId  = "open_id"  // 以open_id来识别用户
)

const (
	UserIdTypeGetDocumentBlockUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypeGetDocumentBlockUnionId = "union_id" // 以union_id来识别用户
	UserIdTypeGetDocumentBlockOpenId  = "open_id"  // 以open_id来识别用户
)

const (
	UserIdTypeListDocumentBlockUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypeListDocumentBlockUnionId = "union_id" // 以union_id来识别用户
	UserIdTypeListDocumentBlockOpenId  = "open_id"  // 以open_id来识别用户
)

const (
	UserIdTypePatchDocumentBlockUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypePatchDocumentBlockUnionId = "union_id" // 以union_id来识别用户
	UserIdTypePatchDocumentBlockOpenId  = "open_id"  // 以open_id来识别用户
)

const (
	UserIdTypeCreateDocumentBlockChildrenUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypeCreateDocumentBlockChildrenUnionId = "union_id" // 以union_id来识别用户
	UserIdTypeCreateDocumentBlockChildrenOpenId  = "open_id"  // 以open_id来识别用户
)

const (
	UserIdTypeGetDocumentBlockChildrenUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypeGetDocumentBlockChildrenUnionId = "union_id" // 以union_id来识别用户
	UserIdTypeGetDocumentBlockChildrenOpenId  = "open_id"  // 以open_id来识别用户
)

type Bitable struct {
	Token    *string `json:"token,omitempty"`     // 多维表格文档 Token
	ViewType *int    `json:"view_type,omitempty"` // 类型
}

type BitableBuilder struct {
	token        string // 多维表格文档 Token
	tokenFlag    bool
	viewType     int // 类型
	viewTypeFlag bool
}

func NewBitableBuilder() *BitableBuilder {
	builder := &BitableBuilder{}
	return builder
}

// 多维表格文档 Token
//
// 示例值：basbcqH9FfRn3sWCCBOtdNVpCsb_tblSAh8fEwhuMXQg
func (builder *BitableBuilder) Token(token string) *BitableBuilder {
	builder.token = token
	builder.tokenFlag = true
	return builder
}

// 类型
//
// 示例值：1
func (builder *BitableBuilder) ViewType(viewType int) *BitableBuilder {
	builder.viewType = viewType
	builder.viewTypeFlag = true
	return builder
}

func (builder *BitableBuilder) Build() *Bitable {
	req := &Bitable{}
	if builder.tokenFlag {
		req.Token = &builder.token

	}
	if builder.viewTypeFlag {
		req.ViewType = &builder.viewType

	}
	return req
}

type Block struct {
	BlockId        *string         `json:"block_id,omitempty"`        // Block 唯一标识
	ParentId       *string         `json:"parent_id,omitempty"`       // block 的父亲 id
	Children       []string        `json:"children,omitempty"`        // block 的孩子 id 列表
	BlockType      *int            `json:"block_type,omitempty"`      // block 类型
	Page           *Text           `json:"page,omitempty"`            // 文档 Block
	Text           *Text           `json:"text,omitempty"`            // 文本 Block
	Heading1       *Text           `json:"heading1,omitempty"`        // 一级标题 Block
	Heading2       *Text           `json:"heading2,omitempty"`        // 二级标题 Block
	Heading3       *Text           `json:"heading3,omitempty"`        // 三级标题 Block
	Heading4       *Text           `json:"heading4,omitempty"`        // 四级标题 Block
	Heading5       *Text           `json:"heading5,omitempty"`        // 五级标题 Block
	Heading6       *Text           `json:"heading6,omitempty"`        // 六级标题 Block
	Heading7       *Text           `json:"heading7,omitempty"`        // 七级标题 Block
	Heading8       *Text           `json:"heading8,omitempty"`        // 八级标题 Block
	Heading9       *Text           `json:"heading9,omitempty"`        // 九级标题 Block
	Bullet         *Text           `json:"bullet,omitempty"`          // 无序列表 Block
	Ordered        *Text           `json:"ordered,omitempty"`         // 有序列表 Block
	Code           *Text           `json:"code,omitempty"`            // 代码块 Block
	Quote          *Text           `json:"quote,omitempty"`           // 引用 Block
	Equation       *Text           `json:"equation,omitempty"`        // 公式 Block
	Todo           *Text           `json:"todo,omitempty"`            // 待办事项 Block
	Bitable        *Bitable        `json:"bitable,omitempty"`         // 多维表格 Block
	Callout        *Callout        `json:"callout,omitempty"`         // 高亮块 Block
	ChatCard       *ChatCard       `json:"chat_card,omitempty"`       // 群聊卡片 Block
	Diagram        *Diagram        `json:"diagram,omitempty"`         // 流程图/UML Block
	Divider        *Divider        `json:"divider,omitempty"`         // 分割线 Block
	File           *File           `json:"file,omitempty"`            // 文件 Block
	Grid           *Grid           `json:"grid,omitempty"`            // 分栏 Block
	GridColumn     *GridColumn     `json:"grid_column,omitempty"`     // 分栏列 Block
	Iframe         *Iframe         `json:"iframe,omitempty"`          // 内嵌 Block
	Image          *Image          `json:"image,omitempty"`           // 图片 Block
	Isv            *Isv            `json:"isv,omitempty"`             // 三方 Block
	Mindnote       *Mindnote       `json:"mindnote,omitempty"`        // 思维笔记 Block
	Sheet          *Sheet          `json:"sheet,omitempty"`           // 电子表格 Block
	Table          *Table          `json:"table,omitempty"`           // 表格 Block
	TableCell      *TableCell      `json:"table_cell,omitempty"`      // 单元格 Block
	View           *View           `json:"view,omitempty"`            // 视图 Block
	Undefined      *Undefined      `json:"undefined,omitempty"`       // 未支持 Block
	QuoteContainer *QuoteContainer `json:"quote_container,omitempty"` // 引用容器 Block
	Task           *Task           `json:"task,omitempty"`            // 任务 Block
	Okr            *Okr            `json:"okr,omitempty"`             // OKR Block，仅可在使用 `user_access_token` 时创建
	OkrObjective   *OkrObjective   `json:"okr_objective,omitempty"`   // OKR Objective Block
	OkrKeyResult   *OkrKeyResult   `json:"okr_key_result,omitempty"`  // OKR Key Result
	OkrProgress    *OkrProgress    `json:"okr_progress,omitempty"`    // OKR 进展信息
	CommentIds     []string        `json:"comment_ids,omitempty"`     // 评论 id 列表
}

type BlockBuilder struct {
	blockId            string // Block 唯一标识
	blockIdFlag        bool
	parentId           string // block 的父亲 id
	parentIdFlag       bool
	children           []string // block 的孩子 id 列表
	childrenFlag       bool
	blockType          int // block 类型
	blockTypeFlag      bool
	page               *Text // 文档 Block
	pageFlag           bool
	text               *Text // 文本 Block
	textFlag           bool
	heading1           *Text // 一级标题 Block
	heading1Flag       bool
	heading2           *Text // 二级标题 Block
	heading2Flag       bool
	heading3           *Text // 三级标题 Block
	heading3Flag       bool
	heading4           *Text // 四级标题 Block
	heading4Flag       bool
	heading5           *Text // 五级标题 Block
	heading5Flag       bool
	heading6           *Text // 六级标题 Block
	heading6Flag       bool
	heading7           *Text // 七级标题 Block
	heading7Flag       bool
	heading8           *Text // 八级标题 Block
	heading8Flag       bool
	heading9           *Text // 九级标题 Block
	heading9Flag       bool
	bullet             *Text // 无序列表 Block
	bulletFlag         bool
	ordered            *Text // 有序列表 Block
	orderedFlag        bool
	code               *Text // 代码块 Block
	codeFlag           bool
	quote              *Text // 引用 Block
	quoteFlag          bool
	equation           *Text // 公式 Block
	equationFlag       bool
	todo               *Text // 待办事项 Block
	todoFlag           bool
	bitable            *Bitable // 多维表格 Block
	bitableFlag        bool
	callout            *Callout // 高亮块 Block
	calloutFlag        bool
	chatCard           *ChatCard // 群聊卡片 Block
	chatCardFlag       bool
	diagram            *Diagram // 流程图/UML Block
	diagramFlag        bool
	divider            *Divider // 分割线 Block
	dividerFlag        bool
	file               *File // 文件 Block
	fileFlag           bool
	grid               *Grid // 分栏 Block
	gridFlag           bool
	gridColumn         *GridColumn // 分栏列 Block
	gridColumnFlag     bool
	iframe             *Iframe // 内嵌 Block
	iframeFlag         bool
	image              *Image // 图片 Block
	imageFlag          bool
	isv                *Isv // 三方 Block
	isvFlag            bool
	mindnote           *Mindnote // 思维笔记 Block
	mindnoteFlag       bool
	sheet              *Sheet // 电子表格 Block
	sheetFlag          bool
	table              *Table // 表格 Block
	tableFlag          bool
	tableCell          *TableCell // 单元格 Block
	tableCellFlag      bool
	view               *View // 视图 Block
	viewFlag           bool
	undefined          *Undefined // 未支持 Block
	undefinedFlag      bool
	quoteContainer     *QuoteContainer // 引用容器 Block
	quoteContainerFlag bool
	task               *Task // 任务 Block
	taskFlag           bool
	okr                *Okr // OKR Block，仅可在使用 `user_access_token` 时创建
	okrFlag            bool
	okrObjective       *OkrObjective // OKR Objective Block
	okrObjectiveFlag   bool
	okrKeyResult       *OkrKeyResult // OKR Key Result
	okrKeyResultFlag   bool
	okrProgress        *OkrProgress // OKR 进展信息
	okrProgressFlag    bool
	commentIds         []string // 评论 id 列表
	commentIdsFlag     bool
}

func NewBlockBuilder() *BlockBuilder {
	builder := &BlockBuilder{}
	return builder
}

// Block 唯一标识
//
// 示例值：doxcnSS4ouQkQEouGSUkTg9NJPe
func (builder *BlockBuilder) BlockId(blockId string) *BlockBuilder {
	builder.blockId = blockId
	builder.blockIdFlag = true
	return builder
}

// block 的父亲 id
//
// 示例值：doxcnePuYufKa49ISjhD8Ih0ikh
func (builder *BlockBuilder) ParentId(parentId string) *BlockBuilder {
	builder.parentId = parentId
	builder.parentIdFlag = true
	return builder
}

// block 的孩子 id 列表
//
// 示例值：["doxcnO6UW6wAw2qIcYf4hZpFIth"]
func (builder *BlockBuilder) Children(children []string) *BlockBuilder {
	builder.children = children
	builder.childrenFlag = true
	return builder
}

// block 类型
//
// 示例值：1
func (builder *BlockBuilder) BlockType(blockType int) *BlockBuilder {
	builder.blockType = blockType
	builder.blockTypeFlag = true
	return builder
}

// 文档 Block
//
// 示例值：
func (builder *BlockBuilder) Page(page *Text) *BlockBuilder {
	builder.page = page
	builder.pageFlag = true
	return builder
}

// 文本 Block
//
// 示例值：
func (builder *BlockBuilder) Text(text *Text) *BlockBuilder {
	builder.text = text
	builder.textFlag = true
	return builder
}

// 一级标题 Block
//
// 示例值：
func (builder *BlockBuilder) Heading1(heading1 *Text) *BlockBuilder {
	builder.heading1 = heading1
	builder.heading1Flag = true
	return builder
}

// 二级标题 Block
//
// 示例值：
func (builder *BlockBuilder) Heading2(heading2 *Text) *BlockBuilder {
	builder.heading2 = heading2
	builder.heading2Flag = true
	return builder
}

// 三级标题 Block
//
// 示例值：
func (builder *BlockBuilder) Heading3(heading3 *Text) *BlockBuilder {
	builder.heading3 = heading3
	builder.heading3Flag = true
	return builder
}

// 四级标题 Block
//
// 示例值：
func (builder *BlockBuilder) Heading4(heading4 *Text) *BlockBuilder {
	builder.heading4 = heading4
	builder.heading4Flag = true
	return builder
}

// 五级标题 Block
//
// 示例值：
func (builder *BlockBuilder) Heading5(heading5 *Text) *BlockBuilder {
	builder.heading5 = heading5
	builder.heading5Flag = true
	return builder
}

// 六级标题 Block
//
// 示例值：
func (builder *BlockBuilder) Heading6(heading6 *Text) *BlockBuilder {
	builder.heading6 = heading6
	builder.heading6Flag = true
	return builder
}

// 七级标题 Block
//
// 示例值：
func (builder *BlockBuilder) Heading7(heading7 *Text) *BlockBuilder {
	builder.heading7 = heading7
	builder.heading7Flag = true
	return builder
}

// 八级标题 Block
//
// 示例值：
func (builder *BlockBuilder) Heading8(heading8 *Text) *BlockBuilder {
	builder.heading8 = heading8
	builder.heading8Flag = true
	return builder
}

// 九级标题 Block
//
// 示例值：
func (builder *BlockBuilder) Heading9(heading9 *Text) *BlockBuilder {
	builder.heading9 = heading9
	builder.heading9Flag = true
	return builder
}

// 无序列表 Block
//
// 示例值：
func (builder *BlockBuilder) Bullet(bullet *Text) *BlockBuilder {
	builder.bullet = bullet
	builder.bulletFlag = true
	return builder
}

// 有序列表 Block
//
// 示例值：
func (builder *BlockBuilder) Ordered(ordered *Text) *BlockBuilder {
	builder.ordered = ordered
	builder.orderedFlag = true
	return builder
}

// 代码块 Block
//
// 示例值：
func (builder *BlockBuilder) Code(code *Text) *BlockBuilder {
	builder.code = code
	builder.codeFlag = true
	return builder
}

// 引用 Block
//
// 示例值：
func (builder *BlockBuilder) Quote(quote *Text) *BlockBuilder {
	builder.quote = quote
	builder.quoteFlag = true
	return builder
}

// 公式 Block
//
// 示例值：
func (builder *BlockBuilder) Equation(equation *Text) *BlockBuilder {
	builder.equation = equation
	builder.equationFlag = true
	return builder
}

// 待办事项 Block
//
// 示例值：
func (builder *BlockBuilder) Todo(todo *Text) *BlockBuilder {
	builder.todo = todo
	builder.todoFlag = true
	return builder
}

// 多维表格 Block
//
// 示例值：
func (builder *BlockBuilder) Bitable(bitable *Bitable) *BlockBuilder {
	builder.bitable = bitable
	builder.bitableFlag = true
	return builder
}

// 高亮块 Block
//
// 示例值：
func (builder *BlockBuilder) Callout(callout *Callout) *BlockBuilder {
	builder.callout = callout
	builder.calloutFlag = true
	return builder
}

// 群聊卡片 Block
//
// 示例值：
func (builder *BlockBuilder) ChatCard(chatCard *ChatCard) *BlockBuilder {
	builder.chatCard = chatCard
	builder.chatCardFlag = true
	return builder
}

// 流程图/UML Block
//
// 示例值：
func (builder *BlockBuilder) Diagram(diagram *Diagram) *BlockBuilder {
	builder.diagram = diagram
	builder.diagramFlag = true
	return builder
}

// 分割线 Block
//
// 示例值：
func (builder *BlockBuilder) Divider(divider *Divider) *BlockBuilder {
	builder.divider = divider
	builder.dividerFlag = true
	return builder
}

// 文件 Block
//
// 示例值：
func (builder *BlockBuilder) File(file *File) *BlockBuilder {
	builder.file = file
	builder.fileFlag = true
	return builder
}

// 分栏 Block
//
// 示例值：
func (builder *BlockBuilder) Grid(grid *Grid) *BlockBuilder {
	builder.grid = grid
	builder.gridFlag = true
	return builder
}

// 分栏列 Block
//
// 示例值：
func (builder *BlockBuilder) GridColumn(gridColumn *GridColumn) *BlockBuilder {
	builder.gridColumn = gridColumn
	builder.gridColumnFlag = true
	return builder
}

// 内嵌 Block
//
// 示例值：
func (builder *BlockBuilder) Iframe(iframe *Iframe) *BlockBuilder {
	builder.iframe = iframe
	builder.iframeFlag = true
	return builder
}

// 图片 Block
//
// 示例值：
func (builder *BlockBuilder) Image(image *Image) *BlockBuilder {
	builder.image = image
	builder.imageFlag = true
	return builder
}

// 三方 Block
//
// 示例值：
func (builder *BlockBuilder) Isv(isv *Isv) *BlockBuilder {
	builder.isv = isv
	builder.isvFlag = true
	return builder
}

// 思维笔记 Block
//
// 示例值：
func (builder *BlockBuilder) Mindnote(mindnote *Mindnote) *BlockBuilder {
	builder.mindnote = mindnote
	builder.mindnoteFlag = true
	return builder
}

// 电子表格 Block
//
// 示例值：
func (builder *BlockBuilder) Sheet(sheet *Sheet) *BlockBuilder {
	builder.sheet = sheet
	builder.sheetFlag = true
	return builder
}

// 表格 Block
//
// 示例值：
func (builder *BlockBuilder) Table(table *Table) *BlockBuilder {
	builder.table = table
	builder.tableFlag = true
	return builder
}

// 单元格 Block
//
// 示例值：
func (builder *BlockBuilder) TableCell(tableCell *TableCell) *BlockBuilder {
	builder.tableCell = tableCell
	builder.tableCellFlag = true
	return builder
}

// 视图 Block
//
// 示例值：
func (builder *BlockBuilder) View(view *View) *BlockBuilder {
	builder.view = view
	builder.viewFlag = true
	return builder
}

// 未支持 Block
//
// 示例值：
func (builder *BlockBuilder) Undefined(undefined *Undefined) *BlockBuilder {
	builder.undefined = undefined
	builder.undefinedFlag = true
	return builder
}

// 引用容器 Block
//
// 示例值：
func (builder *BlockBuilder) QuoteContainer(quoteContainer *QuoteContainer) *BlockBuilder {
	builder.quoteContainer = quoteContainer
	builder.quoteContainerFlag = true
	return builder
}

// 任务 Block
//
// 示例值：
func (builder *BlockBuilder) Task(task *Task) *BlockBuilder {
	builder.task = task
	builder.taskFlag = true
	return builder
}

// OKR Block，仅可在使用 `user_access_token` 时创建
//
// 示例值：
func (builder *BlockBuilder) Okr(okr *Okr) *BlockBuilder {
	builder.okr = okr
	builder.okrFlag = true
	return builder
}

// OKR Objective Block
//
// 示例值：
func (builder *BlockBuilder) OkrObjective(okrObjective *OkrObjective) *BlockBuilder {
	builder.okrObjective = okrObjective
	builder.okrObjectiveFlag = true
	return builder
}

// OKR Key Result
//
// 示例值：
func (builder *BlockBuilder) OkrKeyResult(okrKeyResult *OkrKeyResult) *BlockBuilder {
	builder.okrKeyResult = okrKeyResult
	builder.okrKeyResultFlag = true
	return builder
}

// OKR 进展信息
//
// 示例值：
func (builder *BlockBuilder) OkrProgress(okrProgress *OkrProgress) *BlockBuilder {
	builder.okrProgress = okrProgress
	builder.okrProgressFlag = true
	return builder
}

// 评论 id 列表
//
// 示例值：["1660030311959965796"]
func (builder *BlockBuilder) CommentIds(commentIds []string) *BlockBuilder {
	builder.commentIds = commentIds
	builder.commentIdsFlag = true
	return builder
}

func (builder *BlockBuilder) Build() *Block {
	req := &Block{}
	if builder.blockIdFlag {
		req.BlockId = &builder.blockId

	}
	if builder.parentIdFlag {
		req.ParentId = &builder.parentId

	}
	if builder.childrenFlag {
		req.Children = builder.children
	}
	if builder.blockTypeFlag {
		req.BlockType = &builder.blockType

	}
	if builder.pageFlag {
		req.Page = builder.page
	}
	if builder.textFlag {
		req.Text = builder.text
	}
	if builder.heading1Flag {
		req.Heading1 = builder.heading1
	}
	if builder.heading2Flag {
		req.Heading2 = builder.heading2
	}
	if builder.heading3Flag {
		req.Heading3 = builder.heading3
	}
	if builder.heading4Flag {
		req.Heading4 = builder.heading4
	}
	if builder.heading5Flag {
		req.Heading5 = builder.heading5
	}
	if builder.heading6Flag {
		req.Heading6 = builder.heading6
	}
	if builder.heading7Flag {
		req.Heading7 = builder.heading7
	}
	if builder.heading8Flag {
		req.Heading8 = builder.heading8
	}
	if builder.heading9Flag {
		req.Heading9 = builder.heading9
	}
	if builder.bulletFlag {
		req.Bullet = builder.bullet
	}
	if builder.orderedFlag {
		req.Ordered = builder.ordered
	}
	if builder.codeFlag {
		req.Code = builder.code
	}
	if builder.quoteFlag {
		req.Quote = builder.quote
	}
	if builder.equationFlag {
		req.Equation = builder.equation
	}
	if builder.todoFlag {
		req.Todo = builder.todo
	}
	if builder.bitableFlag {
		req.Bitable = builder.bitable
	}
	if builder.calloutFlag {
		req.Callout = builder.callout
	}
	if builder.chatCardFlag {
		req.ChatCard = builder.chatCard
	}
	if builder.diagramFlag {
		req.Diagram = builder.diagram
	}
	if builder.dividerFlag {
		req.Divider = builder.divider
	}
	if builder.fileFlag {
		req.File = builder.file
	}
	if builder.gridFlag {
		req.Grid = builder.grid
	}
	if builder.gridColumnFlag {
		req.GridColumn = builder.gridColumn
	}
	if builder.iframeFlag {
		req.Iframe = builder.iframe
	}
	if builder.imageFlag {
		req.Image = builder.image
	}
	if builder.isvFlag {
		req.Isv = builder.isv
	}
	if builder.mindnoteFlag {
		req.Mindnote = builder.mindnote
	}
	if builder.sheetFlag {
		req.Sheet = builder.sheet
	}
	if builder.tableFlag {
		req.Table = builder.table
	}
	if builder.tableCellFlag {
		req.TableCell = builder.tableCell
	}
	if builder.viewFlag {
		req.View = builder.view
	}
	if builder.undefinedFlag {
		req.Undefined = builder.undefined
	}
	if builder.quoteContainerFlag {
		req.QuoteContainer = builder.quoteContainer
	}
	if builder.taskFlag {
		req.Task = builder.task
	}
	if builder.okrFlag {
		req.Okr = builder.okr
	}
	if builder.okrObjectiveFlag {
		req.OkrObjective = builder.okrObjective
	}
	if builder.okrKeyResultFlag {
		req.OkrKeyResult = builder.okrKeyResult
	}
	if builder.okrProgressFlag {
		req.OkrProgress = builder.okrProgress
	}
	if builder.commentIdsFlag {
		req.CommentIds = builder.commentIds
	}
	return req
}

type Callout struct {
	BackgroundColor *int    `json:"background_color,omitempty"` // 高亮块背景色
	BorderColor     *int    `json:"border_color,omitempty"`     // 边框色
	TextColor       *int    `json:"text_color,omitempty"`       // 文字颜色
	EmojiId         *string `json:"emoji_id,omitempty"`         // 高亮块图标
}

type CalloutBuilder struct {
	backgroundColor     int // 高亮块背景色
	backgroundColorFlag bool
	borderColor         int // 边框色
	borderColorFlag     bool
	textColor           int // 文字颜色
	textColorFlag       bool
	emojiId             string // 高亮块图标
	emojiIdFlag         bool
}

func NewCalloutBuilder() *CalloutBuilder {
	builder := &CalloutBuilder{}
	return builder
}

// 高亮块背景色
//
// 示例值：1
func (builder *CalloutBuilder) BackgroundColor(backgroundColor int) *CalloutBuilder {
	builder.backgroundColor = backgroundColor
	builder.backgroundColorFlag = true
	return builder
}

// 边框色
//
// 示例值：1
func (builder *CalloutBuilder) BorderColor(borderColor int) *CalloutBuilder {
	builder.borderColor = borderColor
	builder.borderColorFlag = true
	return builder
}

// 文字颜色
//
// 示例值：1
func (builder *CalloutBuilder) TextColor(textColor int) *CalloutBuilder {
	builder.textColor = textColor
	builder.textColorFlag = true
	return builder
}

// 高亮块图标
//
// 示例值：pushpin
func (builder *CalloutBuilder) EmojiId(emojiId string) *CalloutBuilder {
	builder.emojiId = emojiId
	builder.emojiIdFlag = true
	return builder
}

func (builder *CalloutBuilder) Build() *Callout {
	req := &Callout{}
	if builder.backgroundColorFlag {
		req.BackgroundColor = &builder.backgroundColor

	}
	if builder.borderColorFlag {
		req.BorderColor = &builder.borderColor

	}
	if builder.textColorFlag {
		req.TextColor = &builder.textColor

	}
	if builder.emojiIdFlag {
		req.EmojiId = &builder.emojiId

	}
	return req
}

type ChatCard struct {
	ChatId *string `json:"chat_id,omitempty"` // 群聊天会话 ID
	Align  *int    `json:"align,omitempty"`   // 对齐方式
}

type ChatCardBuilder struct {
	chatId     string // 群聊天会话 ID
	chatIdFlag bool
	align      int // 对齐方式
	alignFlag  bool
}

func NewChatCardBuilder() *ChatCardBuilder {
	builder := &ChatCardBuilder{}
	return builder
}

// 群聊天会话 ID
//
// 示例值：7052227140476993555
func (builder *ChatCardBuilder) ChatId(chatId string) *ChatCardBuilder {
	builder.chatId = chatId
	builder.chatIdFlag = true
	return builder
}

// 对齐方式
//
// 示例值：1
func (builder *ChatCardBuilder) Align(align int) *ChatCardBuilder {
	builder.align = align
	builder.alignFlag = true
	return builder
}

func (builder *ChatCardBuilder) Build() *ChatCard {
	req := &ChatCard{}
	if builder.chatIdFlag {
		req.ChatId = &builder.chatId

	}
	if builder.alignFlag {
		req.Align = &builder.align

	}
	return req
}

type DeleteGridColumnRequest struct {
	ColumnIndex *int `json:"column_index,omitempty"` // 删除列索引，从 0 开始，如 0 表示删除第一列（-1表示删除最后一列）
}

type DeleteGridColumnRequestBuilder struct {
	columnIndex     int // 删除列索引，从 0 开始，如 0 表示删除第一列（-1表示删除最后一列）
	columnIndexFlag bool
}

func NewDeleteGridColumnRequestBuilder() *DeleteGridColumnRequestBuilder {
	builder := &DeleteGridColumnRequestBuilder{}
	return builder
}

// 删除列索引，从 0 开始，如 0 表示删除第一列（-1表示删除最后一列）
//
// 示例值：0
func (builder *DeleteGridColumnRequestBuilder) ColumnIndex(columnIndex int) *DeleteGridColumnRequestBuilder {
	builder.columnIndex = columnIndex
	builder.columnIndexFlag = true
	return builder
}

func (builder *DeleteGridColumnRequestBuilder) Build() *DeleteGridColumnRequest {
	req := &DeleteGridColumnRequest{}
	if builder.columnIndexFlag {
		req.ColumnIndex = &builder.columnIndex

	}
	return req
}

type DeleteTableColumnsRequest struct {
	ColumnStartIndex *int `json:"column_start_index,omitempty"` // 列开始索引（区间左闭右开）
	ColumnEndIndex   *int `json:"column_end_index,omitempty"`   // 列结束索引（区间左闭右开）
}

type DeleteTableColumnsRequestBuilder struct {
	columnStartIndex     int // 列开始索引（区间左闭右开）
	columnStartIndexFlag bool
	columnEndIndex       int // 列结束索引（区间左闭右开）
	columnEndIndexFlag   bool
}

func NewDeleteTableColumnsRequestBuilder() *DeleteTableColumnsRequestBuilder {
	builder := &DeleteTableColumnsRequestBuilder{}
	return builder
}

// 列开始索引（区间左闭右开）
//
// 示例值：0
func (builder *DeleteTableColumnsRequestBuilder) ColumnStartIndex(columnStartIndex int) *DeleteTableColumnsRequestBuilder {
	builder.columnStartIndex = columnStartIndex
	builder.columnStartIndexFlag = true
	return builder
}

// 列结束索引（区间左闭右开）
//
// 示例值：1
func (builder *DeleteTableColumnsRequestBuilder) ColumnEndIndex(columnEndIndex int) *DeleteTableColumnsRequestBuilder {
	builder.columnEndIndex = columnEndIndex
	builder.columnEndIndexFlag = true
	return builder
}

func (builder *DeleteTableColumnsRequestBuilder) Build() *DeleteTableColumnsRequest {
	req := &DeleteTableColumnsRequest{}
	if builder.columnStartIndexFlag {
		req.ColumnStartIndex = &builder.columnStartIndex

	}
	if builder.columnEndIndexFlag {
		req.ColumnEndIndex = &builder.columnEndIndex

	}
	return req
}

type DeleteTableRowsRequest struct {
	RowStartIndex *int `json:"row_start_index,omitempty"` // 行开始索引（区间左闭右开）
	RowEndIndex   *int `json:"row_end_index,omitempty"`   // 行结束索引（区间左闭右开）
}

type DeleteTableRowsRequestBuilder struct {
	rowStartIndex     int // 行开始索引（区间左闭右开）
	rowStartIndexFlag bool
	rowEndIndex       int // 行结束索引（区间左闭右开）
	rowEndIndexFlag   bool
}

func NewDeleteTableRowsRequestBuilder() *DeleteTableRowsRequestBuilder {
	builder := &DeleteTableRowsRequestBuilder{}
	return builder
}

// 行开始索引（区间左闭右开）
//
// 示例值：0
func (builder *DeleteTableRowsRequestBuilder) RowStartIndex(rowStartIndex int) *DeleteTableRowsRequestBuilder {
	builder.rowStartIndex = rowStartIndex
	builder.rowStartIndexFlag = true
	return builder
}

// 行结束索引（区间左闭右开）
//
// 示例值：1
func (builder *DeleteTableRowsRequestBuilder) RowEndIndex(rowEndIndex int) *DeleteTableRowsRequestBuilder {
	builder.rowEndIndex = rowEndIndex
	builder.rowEndIndexFlag = true
	return builder
}

func (builder *DeleteTableRowsRequestBuilder) Build() *DeleteTableRowsRequest {
	req := &DeleteTableRowsRequest{}
	if builder.rowStartIndexFlag {
		req.RowStartIndex = &builder.rowStartIndex

	}
	if builder.rowEndIndexFlag {
		req.RowEndIndex = &builder.rowEndIndex

	}
	return req
}

type Diagram struct {
	DiagramType *int `json:"diagram_type,omitempty"` // 绘图类型
}

type DiagramBuilder struct {
	diagramType     int // 绘图类型
	diagramTypeFlag bool
}

func NewDiagramBuilder() *DiagramBuilder {
	builder := &DiagramBuilder{}
	return builder
}

// 绘图类型
//
// 示例值：1
func (builder *DiagramBuilder) DiagramType(diagramType int) *DiagramBuilder {
	builder.diagramType = diagramType
	builder.diagramTypeFlag = true
	return builder
}

func (builder *DiagramBuilder) Build() *Diagram {
	req := &Diagram{}
	if builder.diagramTypeFlag {
		req.DiagramType = &builder.diagramType

	}
	return req
}

type Divider struct {
}

type Document struct {
	DocumentId *string `json:"document_id,omitempty"` // 文档唯一标识
	RevisionId *int    `json:"revision_id,omitempty"` // 文档版本 ID
	Title      *string `json:"title,omitempty"`       // 文档标题
}

type DocumentBuilder struct {
	documentId     string // 文档唯一标识
	documentIdFlag bool
	revisionId     int // 文档版本 ID
	revisionIdFlag bool
	title          string // 文档标题
	titleFlag      bool
}

func NewDocumentBuilder() *DocumentBuilder {
	builder := &DocumentBuilder{}
	return builder
}

// 文档唯一标识
//
// 示例值：doxcni6mOy7jLRWbEylaKKC7K88
func (builder *DocumentBuilder) DocumentId(documentId string) *DocumentBuilder {
	builder.documentId = documentId
	builder.documentIdFlag = true
	return builder
}

// 文档版本 ID
//
// 示例值：1
func (builder *DocumentBuilder) RevisionId(revisionId int) *DocumentBuilder {
	builder.revisionId = revisionId
	builder.revisionIdFlag = true
	return builder
}

// 文档标题
//
// 示例值：undefined
func (builder *DocumentBuilder) Title(title string) *DocumentBuilder {
	builder.title = title
	builder.titleFlag = true
	return builder
}

func (builder *DocumentBuilder) Build() *Document {
	req := &Document{}
	if builder.documentIdFlag {
		req.DocumentId = &builder.documentId

	}
	if builder.revisionIdFlag {
		req.RevisionId = &builder.revisionId

	}
	if builder.titleFlag {
		req.Title = &builder.title

	}
	return req
}

type Equation struct {
	Content          *string           `json:"content,omitempty"`            // 符合 KaTeX 语法的公式内容，语法规则请参考：https://katex.org/docs/supported.html
	TextElementStyle *TextElementStyle `json:"text_element_style,omitempty"` // 文本局部样式
}

type EquationBuilder struct {
	content              string // 符合 KaTeX 语法的公式内容，语法规则请参考：https://katex.org/docs/supported.html
	contentFlag          bool
	textElementStyle     *TextElementStyle // 文本局部样式
	textElementStyleFlag bool
}

func NewEquationBuilder() *EquationBuilder {
	builder := &EquationBuilder{}
	return builder
}

// 符合 KaTeX 语法的公式内容，语法规则请参考：https://katex.org/docs/supported.html
//
// 示例值：E=mc^2\n
func (builder *EquationBuilder) Content(content string) *EquationBuilder {
	builder.content = content
	builder.contentFlag = true
	return builder
}

// 文本局部样式
//
// 示例值：
func (builder *EquationBuilder) TextElementStyle(textElementStyle *TextElementStyle) *EquationBuilder {
	builder.textElementStyle = textElementStyle
	builder.textElementStyleFlag = true
	return builder
}

func (builder *EquationBuilder) Build() *Equation {
	req := &Equation{}
	if builder.contentFlag {
		req.Content = &builder.content

	}
	if builder.textElementStyleFlag {
		req.TextElementStyle = builder.textElementStyle
	}
	return req
}

type File struct {
	Token *string `json:"token,omitempty"` // 附件 Token
	Name  *string `json:"name,omitempty"`  // 文件名
}

type FileBuilder struct {
	token     string // 附件 Token
	tokenFlag bool
	name      string // 文件名
	nameFlag  bool
}

func NewFileBuilder() *FileBuilder {
	builder := &FileBuilder{}
	return builder
}

// 附件 Token
//
// 示例值：boxbcOj88GDkmWGm2zsTyCBqoLb
func (builder *FileBuilder) Token(token string) *FileBuilder {
	builder.token = token
	builder.tokenFlag = true
	return builder
}

// 文件名
//
// 示例值：文件名
func (builder *FileBuilder) Name(name string) *FileBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

func (builder *FileBuilder) Build() *File {
	req := &File{}
	if builder.tokenFlag {
		req.Token = &builder.token

	}
	if builder.nameFlag {
		req.Name = &builder.name

	}
	return req
}

type Grid struct {
	ColumnSize *int `json:"column_size,omitempty"` // 分栏列数量
}

type GridBuilder struct {
	columnSize     int // 分栏列数量
	columnSizeFlag bool
}

func NewGridBuilder() *GridBuilder {
	builder := &GridBuilder{}
	return builder
}

// 分栏列数量
//
// 示例值：2
func (builder *GridBuilder) ColumnSize(columnSize int) *GridBuilder {
	builder.columnSize = columnSize
	builder.columnSizeFlag = true
	return builder
}

func (builder *GridBuilder) Build() *Grid {
	req := &Grid{}
	if builder.columnSizeFlag {
		req.ColumnSize = &builder.columnSize

	}
	return req
}

type GridColumn struct {
	WidthRatio *int `json:"width_ratio,omitempty"` // 当前分栏列占整个分栏的比例
}

type GridColumnBuilder struct {
	widthRatio     int // 当前分栏列占整个分栏的比例
	widthRatioFlag bool
}

func NewGridColumnBuilder() *GridColumnBuilder {
	builder := &GridColumnBuilder{}
	return builder
}

// 当前分栏列占整个分栏的比例
//
// 示例值：50
func (builder *GridColumnBuilder) WidthRatio(widthRatio int) *GridColumnBuilder {
	builder.widthRatio = widthRatio
	builder.widthRatioFlag = true
	return builder
}

func (builder *GridColumnBuilder) Build() *GridColumn {
	req := &GridColumn{}
	if builder.widthRatioFlag {
		req.WidthRatio = &builder.widthRatio

	}
	return req
}

type Iframe struct {
	Component *IframeComponent `json:"component,omitempty"` // iframe 的组成元素
}

type IframeBuilder struct {
	component     *IframeComponent // iframe 的组成元素
	componentFlag bool
}

func NewIframeBuilder() *IframeBuilder {
	builder := &IframeBuilder{}
	return builder
}

// iframe 的组成元素
//
// 示例值：
func (builder *IframeBuilder) Component(component *IframeComponent) *IframeBuilder {
	builder.component = component
	builder.componentFlag = true
	return builder
}

func (builder *IframeBuilder) Build() *Iframe {
	req := &Iframe{}
	if builder.componentFlag {
		req.Component = builder.component
	}
	return req
}

type IframeComponent struct {
	IframeType *int    `json:"iframe_type,omitempty"` // iframe 类型
	Url        *string `json:"url,omitempty"`         // iframe 目标 url（需要进行 url_encode）
}

type IframeComponentBuilder struct {
	iframeType     int // iframe 类型
	iframeTypeFlag bool
	url            string // iframe 目标 url（需要进行 url_encode）
	urlFlag        bool
}

func NewIframeComponentBuilder() *IframeComponentBuilder {
	builder := &IframeComponentBuilder{}
	return builder
}

// iframe 类型
//
// 示例值：1
func (builder *IframeComponentBuilder) IframeType(iframeType int) *IframeComponentBuilder {
	builder.iframeType = iframeType
	builder.iframeTypeFlag = true
	return builder
}

// iframe 目标 url（需要进行 url_encode）
//
// 示例值：https%3A%2F%2Fwww.bilibili.com%2Fvideo%2FBV1Hi4y1w7V7
func (builder *IframeComponentBuilder) Url(url string) *IframeComponentBuilder {
	builder.url = url
	builder.urlFlag = true
	return builder
}

func (builder *IframeComponentBuilder) Build() *IframeComponent {
	req := &IframeComponent{}
	if builder.iframeTypeFlag {
		req.IframeType = &builder.iframeType

	}
	if builder.urlFlag {
		req.Url = &builder.url

	}
	return req
}

type Image struct {
	Width  *int    `json:"width,omitempty"`  // 宽度单位 px
	Height *int    `json:"height,omitempty"` // 高度
	Token  *string `json:"token,omitempty"`  // 图片 Token
}

type ImageBuilder struct {
	width      int // 宽度单位 px
	widthFlag  bool
	height     int // 高度
	heightFlag bool
	token      string // 图片 Token
	tokenFlag  bool
}

func NewImageBuilder() *ImageBuilder {
	builder := &ImageBuilder{}
	return builder
}

// 宽度单位 px
//
// 示例值：4069
func (builder *ImageBuilder) Width(width int) *ImageBuilder {
	builder.width = width
	builder.widthFlag = true
	return builder
}

// 高度
//
// 示例值：2480
func (builder *ImageBuilder) Height(height int) *ImageBuilder {
	builder.height = height
	builder.heightFlag = true
	return builder
}

// 图片 Token
//
// 示例值：boxbcVA91JtFgNhaCgy6s6wK4he
func (builder *ImageBuilder) Token(token string) *ImageBuilder {
	builder.token = token
	builder.tokenFlag = true
	return builder
}

func (builder *ImageBuilder) Build() *Image {
	req := &Image{}
	if builder.widthFlag {
		req.Width = &builder.width

	}
	if builder.heightFlag {
		req.Height = &builder.height

	}
	if builder.tokenFlag {
		req.Token = &builder.token

	}
	return req
}

type InlineBlock struct {
	BlockId          *string           `json:"block_id,omitempty"`           // 关联的内联状态的 block 的 block_id
	TextElementStyle *TextElementStyle `json:"text_element_style,omitempty"` // 文本局部样式
}

type InlineBlockBuilder struct {
	blockId              string // 关联的内联状态的 block 的 block_id
	blockIdFlag          bool
	textElementStyle     *TextElementStyle // 文本局部样式
	textElementStyleFlag bool
}

func NewInlineBlockBuilder() *InlineBlockBuilder {
	builder := &InlineBlockBuilder{}
	return builder
}

// 关联的内联状态的 block 的 block_id
//
// 示例值：doxcnPFi0R56ctbvh2MjkkROFWf
func (builder *InlineBlockBuilder) BlockId(blockId string) *InlineBlockBuilder {
	builder.blockId = blockId
	builder.blockIdFlag = true
	return builder
}

// 文本局部样式
//
// 示例值：
func (builder *InlineBlockBuilder) TextElementStyle(textElementStyle *TextElementStyle) *InlineBlockBuilder {
	builder.textElementStyle = textElementStyle
	builder.textElementStyleFlag = true
	return builder
}

func (builder *InlineBlockBuilder) Build() *InlineBlock {
	req := &InlineBlock{}
	if builder.blockIdFlag {
		req.BlockId = &builder.blockId

	}
	if builder.textElementStyleFlag {
		req.TextElementStyle = builder.textElementStyle
	}
	return req
}

type InlineFile struct {
	FileToken        *string           `json:"file_token,omitempty"`         // 附件 token
	SourceBlockId    *string           `json:"source_block_id,omitempty"`    // 当前文档中该附件所处的 block 的 id
	TextElementStyle *TextElementStyle `json:"text_element_style,omitempty"` // 文本局部样式
}

type InlineFileBuilder struct {
	fileToken            string // 附件 token
	fileTokenFlag        bool
	sourceBlockId        string // 当前文档中该附件所处的 block 的 id
	sourceBlockIdFlag    bool
	textElementStyle     *TextElementStyle // 文本局部样式
	textElementStyleFlag bool
}

func NewInlineFileBuilder() *InlineFileBuilder {
	builder := &InlineFileBuilder{}
	return builder
}

// 附件 token
//
// 示例值：boxcnOj88GDkmWGm2zsTyCBqoLb
func (builder *InlineFileBuilder) FileToken(fileToken string) *InlineFileBuilder {
	builder.fileToken = fileToken
	builder.fileTokenFlag = true
	return builder
}

// 当前文档中该附件所处的 block 的 id
//
// 示例值：doxcnM46kSWSkgUMW04ldKsJDsc
func (builder *InlineFileBuilder) SourceBlockId(sourceBlockId string) *InlineFileBuilder {
	builder.sourceBlockId = sourceBlockId
	builder.sourceBlockIdFlag = true
	return builder
}

// 文本局部样式
//
// 示例值：
func (builder *InlineFileBuilder) TextElementStyle(textElementStyle *TextElementStyle) *InlineFileBuilder {
	builder.textElementStyle = textElementStyle
	builder.textElementStyleFlag = true
	return builder
}

func (builder *InlineFileBuilder) Build() *InlineFile {
	req := &InlineFile{}
	if builder.fileTokenFlag {
		req.FileToken = &builder.fileToken

	}
	if builder.sourceBlockIdFlag {
		req.SourceBlockId = &builder.sourceBlockId

	}
	if builder.textElementStyleFlag {
		req.TextElementStyle = builder.textElementStyle
	}
	return req
}

type InsertGridColumnRequest struct {
	ColumnIndex *int `json:"column_index,omitempty"` // 插入列索引，从 1 开始，如 1 表示在第一列后插入，注意不允许传 0（-1表示在最后一列后插入）
}

type InsertGridColumnRequestBuilder struct {
	columnIndex     int // 插入列索引，从 1 开始，如 1 表示在第一列后插入，注意不允许传 0（-1表示在最后一列后插入）
	columnIndexFlag bool
}

func NewInsertGridColumnRequestBuilder() *InsertGridColumnRequestBuilder {
	builder := &InsertGridColumnRequestBuilder{}
	return builder
}

// 插入列索引，从 1 开始，如 1 表示在第一列后插入，注意不允许传 0（-1表示在最后一列后插入）
//
// 示例值：1
func (builder *InsertGridColumnRequestBuilder) ColumnIndex(columnIndex int) *InsertGridColumnRequestBuilder {
	builder.columnIndex = columnIndex
	builder.columnIndexFlag = true
	return builder
}

func (builder *InsertGridColumnRequestBuilder) Build() *InsertGridColumnRequest {
	req := &InsertGridColumnRequest{}
	if builder.columnIndexFlag {
		req.ColumnIndex = &builder.columnIndex

	}
	return req
}

type InsertTableColumnRequest struct {
	ColumnIndex *int `json:"column_index,omitempty"` // 插入的列在表格中的索引。（-1表示在表格末尾插入一列）
}

type InsertTableColumnRequestBuilder struct {
	columnIndex     int // 插入的列在表格中的索引。（-1表示在表格末尾插入一列）
	columnIndexFlag bool
}

func NewInsertTableColumnRequestBuilder() *InsertTableColumnRequestBuilder {
	builder := &InsertTableColumnRequestBuilder{}
	return builder
}

// 插入的列在表格中的索引。（-1表示在表格末尾插入一列）
//
// 示例值：-1
func (builder *InsertTableColumnRequestBuilder) ColumnIndex(columnIndex int) *InsertTableColumnRequestBuilder {
	builder.columnIndex = columnIndex
	builder.columnIndexFlag = true
	return builder
}

func (builder *InsertTableColumnRequestBuilder) Build() *InsertTableColumnRequest {
	req := &InsertTableColumnRequest{}
	if builder.columnIndexFlag {
		req.ColumnIndex = &builder.columnIndex

	}
	return req
}

type InsertTableRowRequest struct {
	RowIndex *int `json:"row_index,omitempty"` // 插入的行在表格中的索引。（-1表示在表格末尾插入一行）
}

type InsertTableRowRequestBuilder struct {
	rowIndex     int // 插入的行在表格中的索引。（-1表示在表格末尾插入一行）
	rowIndexFlag bool
}

func NewInsertTableRowRequestBuilder() *InsertTableRowRequestBuilder {
	builder := &InsertTableRowRequestBuilder{}
	return builder
}

// 插入的行在表格中的索引。（-1表示在表格末尾插入一行）
//
// 示例值：-1
func (builder *InsertTableRowRequestBuilder) RowIndex(rowIndex int) *InsertTableRowRequestBuilder {
	builder.rowIndex = rowIndex
	builder.rowIndexFlag = true
	return builder
}

func (builder *InsertTableRowRequestBuilder) Build() *InsertTableRowRequest {
	req := &InsertTableRowRequest{}
	if builder.rowIndexFlag {
		req.RowIndex = &builder.rowIndex

	}
	return req
}

type Isv struct {
	ComponentId     *string `json:"component_id,omitempty"`      // 团队互动应用唯一ID
	ComponentTypeId *string `json:"component_type_id,omitempty"` // 团队互动应用类型，比如信息收集"blk_5f992038c64240015d280958"
}

type IsvBuilder struct {
	componentId         string // 团队互动应用唯一ID
	componentIdFlag     bool
	componentTypeId     string // 团队互动应用类型，比如信息收集"blk_5f992038c64240015d280958"
	componentTypeIdFlag bool
}

func NewIsvBuilder() *IsvBuilder {
	builder := &IsvBuilder{}
	return builder
}

// 团队互动应用唯一ID
//
// 示例值：7056882725002051603
func (builder *IsvBuilder) ComponentId(componentId string) *IsvBuilder {
	builder.componentId = componentId
	builder.componentIdFlag = true
	return builder
}

// 团队互动应用类型，比如信息收集"blk_5f992038c64240015d280958"
//
// 示例值：blk_5f992038c64240015d280958
func (builder *IsvBuilder) ComponentTypeId(componentTypeId string) *IsvBuilder {
	builder.componentTypeId = componentTypeId
	builder.componentTypeIdFlag = true
	return builder
}

func (builder *IsvBuilder) Build() *Isv {
	req := &Isv{}
	if builder.componentIdFlag {
		req.ComponentId = &builder.componentId

	}
	if builder.componentTypeIdFlag {
		req.ComponentTypeId = &builder.componentTypeId

	}
	return req
}

type Link struct {
	Url *string `json:"url,omitempty"` // 超链接指向的 url (需要 url_encode)
}

type LinkBuilder struct {
	url     string // 超链接指向的 url (需要 url_encode)
	urlFlag bool
}

func NewLinkBuilder() *LinkBuilder {
	builder := &LinkBuilder{}
	return builder
}

// 超链接指向的 url (需要 url_encode)
//
// 示例值：https%3A%2F%2Fopen.feishu.cn%2F
func (builder *LinkBuilder) Url(url string) *LinkBuilder {
	builder.url = url
	builder.urlFlag = true
	return builder
}

func (builder *LinkBuilder) Build() *Link {
	req := &Link{}
	if builder.urlFlag {
		req.Url = &builder.url

	}
	return req
}

type MentionDoc struct {
	Token            *string           `json:"token,omitempty"`              // 云文档 token
	ObjType          *int              `json:"obj_type,omitempty"`           // 云文档类型
	Url              *string           `json:"url,omitempty"`                // 云文档链接（需要 url_encode)
	Title            *string           `json:"title,omitempty"`              // 文档标题，只读属性
	TextElementStyle *TextElementStyle `json:"text_element_style,omitempty"` // 文本局部样式
}

type MentionDocBuilder struct {
	token                string // 云文档 token
	tokenFlag            bool
	objType              int // 云文档类型
	objTypeFlag          bool
	url                  string // 云文档链接（需要 url_encode)
	urlFlag              bool
	title                string // 文档标题，只读属性
	titleFlag            bool
	textElementStyle     *TextElementStyle // 文本局部样式
	textElementStyleFlag bool
}

func NewMentionDocBuilder() *MentionDocBuilder {
	builder := &MentionDocBuilder{}
	return builder
}

// 云文档 token
//
// 示例值：doxbc873Y7cXD153gXqb76G1Y9b
func (builder *MentionDocBuilder) Token(token string) *MentionDocBuilder {
	builder.token = token
	builder.tokenFlag = true
	return builder
}

// 云文档类型
//
// 示例值：22
func (builder *MentionDocBuilder) ObjType(objType int) *MentionDocBuilder {
	builder.objType = objType
	builder.objTypeFlag = true
	return builder
}

// 云文档链接（需要 url_encode)
//
// 示例值：https%3A%2F%2Fbytedance.feishu-boe.cn%2Fdocx%2Fdoxbc873Y7cXD153gXqb76G1Y9b
func (builder *MentionDocBuilder) Url(url string) *MentionDocBuilder {
	builder.url = url
	builder.urlFlag = true
	return builder
}

// 文档标题，只读属性
//
// 示例值：undefined
func (builder *MentionDocBuilder) Title(title string) *MentionDocBuilder {
	builder.title = title
	builder.titleFlag = true
	return builder
}

// 文本局部样式
//
// 示例值：
func (builder *MentionDocBuilder) TextElementStyle(textElementStyle *TextElementStyle) *MentionDocBuilder {
	builder.textElementStyle = textElementStyle
	builder.textElementStyleFlag = true
	return builder
}

func (builder *MentionDocBuilder) Build() *MentionDoc {
	req := &MentionDoc{}
	if builder.tokenFlag {
		req.Token = &builder.token

	}
	if builder.objTypeFlag {
		req.ObjType = &builder.objType

	}
	if builder.urlFlag {
		req.Url = &builder.url

	}
	if builder.titleFlag {
		req.Title = &builder.title

	}
	if builder.textElementStyleFlag {
		req.TextElementStyle = builder.textElementStyle
	}
	return req
}

type MentionUser struct {
	UserId           *string           `json:"user_id,omitempty"`            // 用户 OpenID
	TextElementStyle *TextElementStyle `json:"text_element_style,omitempty"` // 文本局部样式
}

type MentionUserBuilder struct {
	userId               string // 用户 OpenID
	userIdFlag           bool
	textElementStyle     *TextElementStyle // 文本局部样式
	textElementStyleFlag bool
}

func NewMentionUserBuilder() *MentionUserBuilder {
	builder := &MentionUserBuilder{}
	return builder
}

// 用户 OpenID
//
// 示例值：ou_3bbe8a09c20e89cce9bff989ed840674
func (builder *MentionUserBuilder) UserId(userId string) *MentionUserBuilder {
	builder.userId = userId
	builder.userIdFlag = true
	return builder
}

// 文本局部样式
//
// 示例值：
func (builder *MentionUserBuilder) TextElementStyle(textElementStyle *TextElementStyle) *MentionUserBuilder {
	builder.textElementStyle = textElementStyle
	builder.textElementStyleFlag = true
	return builder
}

func (builder *MentionUserBuilder) Build() *MentionUser {
	req := &MentionUser{}
	if builder.userIdFlag {
		req.UserId = &builder.userId

	}
	if builder.textElementStyleFlag {
		req.TextElementStyle = builder.textElementStyle
	}
	return req
}

type MergeTableCellsRequest struct {
	RowStartIndex    *int `json:"row_start_index,omitempty"`    // 行起始索引（区间左闭右开）
	RowEndIndex      *int `json:"row_end_index,omitempty"`      // 行结束索引（区间左闭右开）
	ColumnStartIndex *int `json:"column_start_index,omitempty"` // 列起始索引（区间左闭右开）
	ColumnEndIndex   *int `json:"column_end_index,omitempty"`   // 列结束索引（区间左闭右开）
}

type MergeTableCellsRequestBuilder struct {
	rowStartIndex        int // 行起始索引（区间左闭右开）
	rowStartIndexFlag    bool
	rowEndIndex          int // 行结束索引（区间左闭右开）
	rowEndIndexFlag      bool
	columnStartIndex     int // 列起始索引（区间左闭右开）
	columnStartIndexFlag bool
	columnEndIndex       int // 列结束索引（区间左闭右开）
	columnEndIndexFlag   bool
}

func NewMergeTableCellsRequestBuilder() *MergeTableCellsRequestBuilder {
	builder := &MergeTableCellsRequestBuilder{}
	return builder
}

// 行起始索引（区间左闭右开）
//
// 示例值：0
func (builder *MergeTableCellsRequestBuilder) RowStartIndex(rowStartIndex int) *MergeTableCellsRequestBuilder {
	builder.rowStartIndex = rowStartIndex
	builder.rowStartIndexFlag = true
	return builder
}

// 行结束索引（区间左闭右开）
//
// 示例值：1
func (builder *MergeTableCellsRequestBuilder) RowEndIndex(rowEndIndex int) *MergeTableCellsRequestBuilder {
	builder.rowEndIndex = rowEndIndex
	builder.rowEndIndexFlag = true
	return builder
}

// 列起始索引（区间左闭右开）
//
// 示例值：0
func (builder *MergeTableCellsRequestBuilder) ColumnStartIndex(columnStartIndex int) *MergeTableCellsRequestBuilder {
	builder.columnStartIndex = columnStartIndex
	builder.columnStartIndexFlag = true
	return builder
}

// 列结束索引（区间左闭右开）
//
// 示例值：1
func (builder *MergeTableCellsRequestBuilder) ColumnEndIndex(columnEndIndex int) *MergeTableCellsRequestBuilder {
	builder.columnEndIndex = columnEndIndex
	builder.columnEndIndexFlag = true
	return builder
}

func (builder *MergeTableCellsRequestBuilder) Build() *MergeTableCellsRequest {
	req := &MergeTableCellsRequest{}
	if builder.rowStartIndexFlag {
		req.RowStartIndex = &builder.rowStartIndex

	}
	if builder.rowEndIndexFlag {
		req.RowEndIndex = &builder.rowEndIndex

	}
	if builder.columnStartIndexFlag {
		req.ColumnStartIndex = &builder.columnStartIndex

	}
	if builder.columnEndIndexFlag {
		req.ColumnEndIndex = &builder.columnEndIndex

	}
	return req
}

type Mindnote struct {
	Token *string `json:"token,omitempty"` // 思维导图 token
}

type MindnoteBuilder struct {
	token     string // 思维导图 token
	tokenFlag bool
}

func NewMindnoteBuilder() *MindnoteBuilder {
	builder := &MindnoteBuilder{}
	return builder
}

// 思维导图 token
//
// 示例值：bmnbcXXGPWfJMwDfGCbCiU14c6f
func (builder *MindnoteBuilder) Token(token string) *MindnoteBuilder {
	builder.token = token
	builder.tokenFlag = true
	return builder
}

func (builder *MindnoteBuilder) Build() *Mindnote {
	req := &Mindnote{}
	if builder.tokenFlag {
		req.Token = &builder.token

	}
	return req
}

type ObjectiveIdWithKrId struct {
	ObjectiveId *string  `json:"objective_id,omitempty"` // OKR 中 Objective 的 ID
	KrIds       []string `json:"kr_ids,omitempty"`       // Key Result 的 ID 列表，此值为空时插入当前 Objective 下的所有 Key Result
}

type ObjectiveIdWithKrIdBuilder struct {
	objectiveId     string // OKR 中 Objective 的 ID
	objectiveIdFlag bool
	krIds           []string // Key Result 的 ID 列表，此值为空时插入当前 Objective 下的所有 Key Result
	krIdsFlag       bool
}

func NewObjectiveIdWithKrIdBuilder() *ObjectiveIdWithKrIdBuilder {
	builder := &ObjectiveIdWithKrIdBuilder{}
	return builder
}

// OKR 中 Objective 的 ID
//
// 示例值：7109022409227026460
func (builder *ObjectiveIdWithKrIdBuilder) ObjectiveId(objectiveId string) *ObjectiveIdWithKrIdBuilder {
	builder.objectiveId = objectiveId
	builder.objectiveIdFlag = true
	return builder
}

// Key Result 的 ID 列表，此值为空时插入当前 Objective 下的所有 Key Result
//
// 示例值：["7109022573011894300","7109022546444517404"]
func (builder *ObjectiveIdWithKrIdBuilder) KrIds(krIds []string) *ObjectiveIdWithKrIdBuilder {
	builder.krIds = krIds
	builder.krIdsFlag = true
	return builder
}

func (builder *ObjectiveIdWithKrIdBuilder) Build() *ObjectiveIdWithKrId {
	req := &ObjectiveIdWithKrId{}
	if builder.objectiveIdFlag {
		req.ObjectiveId = &builder.objectiveId

	}
	if builder.krIdsFlag {
		req.KrIds = builder.krIds
	}
	return req
}

type Okr struct {
	OkrId               *string                `json:"okr_id,omitempty"`                // OKR ID，获取需要插入的 OKR ID 可见[获取用户的 OKR 列表](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/okr-v1/user-okr/list)
	Objectives          []*ObjectiveIdWithKrId `json:"objectives,omitempty"`            // OKR Block 中的 Objective ID 和 Key Result ID，此值为空时插入 OKR 下所有的 Objective 和 Key Result
	PeriodDisplayStatus *string                `json:"period_display_status,omitempty"` // 周期的状态
	PeriodNameZh        *string                `json:"period_name_zh,omitempty"`        // 周期名 - 中文
	PeriodNameEn        *string                `json:"period_name_en,omitempty"`        // 周期名 - 英文
	UserId              *string                `json:"user_id,omitempty"`               // OKR 所属的用户 ID
	VisibleSetting      *OkrVisibleSetting     `json:"visible_setting,omitempty"`       // 可见性设置
}

type OkrBuilder struct {
	okrId                   string // OKR ID，获取需要插入的 OKR ID 可见[获取用户的 OKR 列表](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/okr-v1/user-okr/list)
	okrIdFlag               bool
	objectives              []*ObjectiveIdWithKrId // OKR Block 中的 Objective ID 和 Key Result ID，此值为空时插入 OKR 下所有的 Objective 和 Key Result
	objectivesFlag          bool
	periodDisplayStatus     string // 周期的状态
	periodDisplayStatusFlag bool
	periodNameZh            string // 周期名 - 中文
	periodNameZhFlag        bool
	periodNameEn            string // 周期名 - 英文
	periodNameEnFlag        bool
	userId                  string // OKR 所属的用户 ID
	userIdFlag              bool
	visibleSetting          *OkrVisibleSetting // 可见性设置
	visibleSettingFlag      bool
}

func NewOkrBuilder() *OkrBuilder {
	builder := &OkrBuilder{}
	return builder
}

// OKR ID，获取需要插入的 OKR ID 可见[获取用户的 OKR 列表](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/okr-v1/user-okr/list)
//
// 示例值：7076349900476448796
func (builder *OkrBuilder) OkrId(okrId string) *OkrBuilder {
	builder.okrId = okrId
	builder.okrIdFlag = true
	return builder
}

// OKR Block 中的 Objective ID 和 Key Result ID，此值为空时插入 OKR 下所有的 Objective 和 Key Result
//
// 示例值：
func (builder *OkrBuilder) Objectives(objectives []*ObjectiveIdWithKrId) *OkrBuilder {
	builder.objectives = objectives
	builder.objectivesFlag = true
	return builder
}

// 周期的状态
//
// 示例值："default"
func (builder *OkrBuilder) PeriodDisplayStatus(periodDisplayStatus string) *OkrBuilder {
	builder.periodDisplayStatus = periodDisplayStatus
	builder.periodDisplayStatusFlag = true
	return builder
}

// 周期名 - 中文
//
// 示例值："2022 年 4 月 - 6 月"
func (builder *OkrBuilder) PeriodNameZh(periodNameZh string) *OkrBuilder {
	builder.periodNameZh = periodNameZh
	builder.periodNameZhFlag = true
	return builder
}

// 周期名 - 英文
//
// 示例值："Apr - Jun 2022"
func (builder *OkrBuilder) PeriodNameEn(periodNameEn string) *OkrBuilder {
	builder.periodNameEn = periodNameEn
	builder.periodNameEnFlag = true
	return builder
}

// OKR 所属的用户 ID
//
// 示例值："ou_3bbe8a09c20e89cce9bff989ed840674"
func (builder *OkrBuilder) UserId(userId string) *OkrBuilder {
	builder.userId = userId
	builder.userIdFlag = true
	return builder
}

// 可见性设置
//
// 示例值：
func (builder *OkrBuilder) VisibleSetting(visibleSetting *OkrVisibleSetting) *OkrBuilder {
	builder.visibleSetting = visibleSetting
	builder.visibleSettingFlag = true
	return builder
}

func (builder *OkrBuilder) Build() *Okr {
	req := &Okr{}
	if builder.okrIdFlag {
		req.OkrId = &builder.okrId

	}
	if builder.objectivesFlag {
		req.Objectives = builder.objectives
	}
	if builder.periodDisplayStatusFlag {
		req.PeriodDisplayStatus = &builder.periodDisplayStatus

	}
	if builder.periodNameZhFlag {
		req.PeriodNameZh = &builder.periodNameZh

	}
	if builder.periodNameEnFlag {
		req.PeriodNameEn = &builder.periodNameEn

	}
	if builder.userIdFlag {
		req.UserId = &builder.userId

	}
	if builder.visibleSettingFlag {
		req.VisibleSetting = builder.visibleSetting
	}
	return req
}

type OkrKeyResult struct {
	KrId         *string          `json:"kr_id,omitempty"`         // Key Result 的 ID
	Confidential *bool            `json:"confidential,omitempty"`  // 是否在 OKR 平台设置了私密权限
	Position     *int             `json:"position,omitempty"`      // Key Result 的位置编号，对应 Block 中 KR1、KR2 的 1、2。
	Score        *int             `json:"score,omitempty"`         // 打分信息
	Visible      *bool            `json:"visible,omitempty"`       // OKR Block 中此 Key Result 是否可见
	Weight       *float64         `json:"weight,omitempty"`        // Key Result 的权重
	ProgressRate *OkrProgressRate `json:"progress_rate,omitempty"` // 进展信息
	Content      *Text            `json:"content,omitempty"`       // Key Result 的文本内容
}

type OkrKeyResultBuilder struct {
	krId             string // Key Result 的 ID
	krIdFlag         bool
	confidential     bool // 是否在 OKR 平台设置了私密权限
	confidentialFlag bool
	position         int // Key Result 的位置编号，对应 Block 中 KR1、KR2 的 1、2。
	positionFlag     bool
	score            int // 打分信息
	scoreFlag        bool
	visible          bool // OKR Block 中此 Key Result 是否可见
	visibleFlag      bool
	weight           float64 // Key Result 的权重
	weightFlag       bool
	progressRate     *OkrProgressRate // 进展信息
	progressRateFlag bool
	content          *Text // Key Result 的文本内容
	contentFlag      bool
}

func NewOkrKeyResultBuilder() *OkrKeyResultBuilder {
	builder := &OkrKeyResultBuilder{}
	return builder
}

// Key Result 的 ID
//
// 示例值："7109022573011894300"
func (builder *OkrKeyResultBuilder) KrId(krId string) *OkrKeyResultBuilder {
	builder.krId = krId
	builder.krIdFlag = true
	return builder
}

// 是否在 OKR 平台设置了私密权限
//
// 示例值：false
func (builder *OkrKeyResultBuilder) Confidential(confidential bool) *OkrKeyResultBuilder {
	builder.confidential = confidential
	builder.confidentialFlag = true
	return builder
}

// Key Result 的位置编号，对应 Block 中 KR1、KR2 的 1、2。
//
// 示例值：1
func (builder *OkrKeyResultBuilder) Position(position int) *OkrKeyResultBuilder {
	builder.position = position
	builder.positionFlag = true
	return builder
}

// 打分信息
//
// 示例值：0
func (builder *OkrKeyResultBuilder) Score(score int) *OkrKeyResultBuilder {
	builder.score = score
	builder.scoreFlag = true
	return builder
}

// OKR Block 中此 Key Result 是否可见
//
// 示例值：true
func (builder *OkrKeyResultBuilder) Visible(visible bool) *OkrKeyResultBuilder {
	builder.visible = visible
	builder.visibleFlag = true
	return builder
}

// Key Result 的权重
//
// 示例值：0.5
func (builder *OkrKeyResultBuilder) Weight(weight float64) *OkrKeyResultBuilder {
	builder.weight = weight
	builder.weightFlag = true
	return builder
}

// 进展信息
//
// 示例值：
func (builder *OkrKeyResultBuilder) ProgressRate(progressRate *OkrProgressRate) *OkrKeyResultBuilder {
	builder.progressRate = progressRate
	builder.progressRateFlag = true
	return builder
}

// Key Result 的文本内容
//
// 示例值：
func (builder *OkrKeyResultBuilder) Content(content *Text) *OkrKeyResultBuilder {
	builder.content = content
	builder.contentFlag = true
	return builder
}

func (builder *OkrKeyResultBuilder) Build() *OkrKeyResult {
	req := &OkrKeyResult{}
	if builder.krIdFlag {
		req.KrId = &builder.krId

	}
	if builder.confidentialFlag {
		req.Confidential = &builder.confidential

	}
	if builder.positionFlag {
		req.Position = &builder.position

	}
	if builder.scoreFlag {
		req.Score = &builder.score

	}
	if builder.visibleFlag {
		req.Visible = &builder.visible

	}
	if builder.weightFlag {
		req.Weight = &builder.weight

	}
	if builder.progressRateFlag {
		req.ProgressRate = builder.progressRate
	}
	if builder.contentFlag {
		req.Content = builder.content
	}
	return req
}

type OkrObjective struct {
	ObjectiveId  *string          `json:"objective_id,omitempty"`  // Objective ID
	Confidential *bool            `json:"confidential,omitempty"`  // 是否在 OKR 平台设置了私密权限
	Position     *int             `json:"position,omitempty"`      // Objective 的位置编号，对应 Block 中 O1、O2 的 1、2
	Score        *int             `json:"score,omitempty"`         // 打分信息
	Visible      *bool            `json:"visible,omitempty"`       // OKR Block 中是否展示该 Objective
	Weight       *float64         `json:"weight,omitempty"`        // Objective 的权重
	ProgressRate *OkrProgressRate `json:"progress_rate,omitempty"` // 进展信息
	Content      *Text            `json:"content,omitempty"`       // Objective 的文本内容
}

type OkrObjectiveBuilder struct {
	objectiveId      string // Objective ID
	objectiveIdFlag  bool
	confidential     bool // 是否在 OKR 平台设置了私密权限
	confidentialFlag bool
	position         int // Objective 的位置编号，对应 Block 中 O1、O2 的 1、2
	positionFlag     bool
	score            int // 打分信息
	scoreFlag        bool
	visible          bool // OKR Block 中是否展示该 Objective
	visibleFlag      bool
	weight           float64 // Objective 的权重
	weightFlag       bool
	progressRate     *OkrProgressRate // 进展信息
	progressRateFlag bool
	content          *Text // Objective 的文本内容
	contentFlag      bool
}

func NewOkrObjectiveBuilder() *OkrObjectiveBuilder {
	builder := &OkrObjectiveBuilder{}
	return builder
}

// Objective ID
//
// 示例值："7109022409227026460"
func (builder *OkrObjectiveBuilder) ObjectiveId(objectiveId string) *OkrObjectiveBuilder {
	builder.objectiveId = objectiveId
	builder.objectiveIdFlag = true
	return builder
}

// 是否在 OKR 平台设置了私密权限
//
// 示例值：false
func (builder *OkrObjectiveBuilder) Confidential(confidential bool) *OkrObjectiveBuilder {
	builder.confidential = confidential
	builder.confidentialFlag = true
	return builder
}

// Objective 的位置编号，对应 Block 中 O1、O2 的 1、2
//
// 示例值：1
func (builder *OkrObjectiveBuilder) Position(position int) *OkrObjectiveBuilder {
	builder.position = position
	builder.positionFlag = true
	return builder
}

// 打分信息
//
// 示例值：0
func (builder *OkrObjectiveBuilder) Score(score int) *OkrObjectiveBuilder {
	builder.score = score
	builder.scoreFlag = true
	return builder
}

// OKR Block 中是否展示该 Objective
//
// 示例值：true
func (builder *OkrObjectiveBuilder) Visible(visible bool) *OkrObjectiveBuilder {
	builder.visible = visible
	builder.visibleFlag = true
	return builder
}

// Objective 的权重
//
// 示例值：1.0
func (builder *OkrObjectiveBuilder) Weight(weight float64) *OkrObjectiveBuilder {
	builder.weight = weight
	builder.weightFlag = true
	return builder
}

// 进展信息
//
// 示例值：
func (builder *OkrObjectiveBuilder) ProgressRate(progressRate *OkrProgressRate) *OkrObjectiveBuilder {
	builder.progressRate = progressRate
	builder.progressRateFlag = true
	return builder
}

// Objective 的文本内容
//
// 示例值：
func (builder *OkrObjectiveBuilder) Content(content *Text) *OkrObjectiveBuilder {
	builder.content = content
	builder.contentFlag = true
	return builder
}

func (builder *OkrObjectiveBuilder) Build() *OkrObjective {
	req := &OkrObjective{}
	if builder.objectiveIdFlag {
		req.ObjectiveId = &builder.objectiveId

	}
	if builder.confidentialFlag {
		req.Confidential = &builder.confidential

	}
	if builder.positionFlag {
		req.Position = &builder.position

	}
	if builder.scoreFlag {
		req.Score = &builder.score

	}
	if builder.visibleFlag {
		req.Visible = &builder.visible

	}
	if builder.weightFlag {
		req.Weight = &builder.weight

	}
	if builder.progressRateFlag {
		req.ProgressRate = builder.progressRate
	}
	if builder.contentFlag {
		req.Content = builder.content
	}
	return req
}

type OkrProgress struct {
}

type OkrProgressRate struct {
	Mode           *string  `json:"mode,omitempty"`            // 状态模式
	Current        *float64 `json:"current,omitempty"`         // 当前进度, advanced 模式使用
	Percent        *float64 `json:"percent,omitempty"`         // 当前进度百分比，simple 模式使用
	ProgressStatus *string  `json:"progress_status,omitempty"` // 进展状态
	Start          *float64 `json:"start,omitempty"`           // 进度起始值，advanced 模式使用
	StatusType     *string  `json:"status_type,omitempty"`     // 状态计算类型
	Target         *float64 `json:"target,omitempty"`          // 进度目标值，advanced 模式使用
}

type OkrProgressRateBuilder struct {
	mode               string // 状态模式
	modeFlag           bool
	current            float64 // 当前进度, advanced 模式使用
	currentFlag        bool
	percent            float64 // 当前进度百分比，simple 模式使用
	percentFlag        bool
	progressStatus     string // 进展状态
	progressStatusFlag bool
	start              float64 // 进度起始值，advanced 模式使用
	startFlag          bool
	statusType         string // 状态计算类型
	statusTypeFlag     bool
	target             float64 // 进度目标值，advanced 模式使用
	targetFlag         bool
}

func NewOkrProgressRateBuilder() *OkrProgressRateBuilder {
	builder := &OkrProgressRateBuilder{}
	return builder
}

// 状态模式
//
// 示例值："simple"
func (builder *OkrProgressRateBuilder) Mode(mode string) *OkrProgressRateBuilder {
	builder.mode = mode
	builder.modeFlag = true
	return builder
}

// 当前进度, advanced 模式使用
//
// 示例值：0
func (builder *OkrProgressRateBuilder) Current(current float64) *OkrProgressRateBuilder {
	builder.current = current
	builder.currentFlag = true
	return builder
}

// 当前进度百分比，simple 模式使用
//
// 示例值：100
func (builder *OkrProgressRateBuilder) Percent(percent float64) *OkrProgressRateBuilder {
	builder.percent = percent
	builder.percentFlag = true
	return builder
}

// 进展状态
//
// 示例值："normal"
func (builder *OkrProgressRateBuilder) ProgressStatus(progressStatus string) *OkrProgressRateBuilder {
	builder.progressStatus = progressStatus
	builder.progressStatusFlag = true
	return builder
}

// 进度起始值，advanced 模式使用
//
// 示例值：0
func (builder *OkrProgressRateBuilder) Start(start float64) *OkrProgressRateBuilder {
	builder.start = start
	builder.startFlag = true
	return builder
}

// 状态计算类型
//
// 示例值："default"
func (builder *OkrProgressRateBuilder) StatusType(statusType string) *OkrProgressRateBuilder {
	builder.statusType = statusType
	builder.statusTypeFlag = true
	return builder
}

// 进度目标值，advanced 模式使用
//
// 示例值：
func (builder *OkrProgressRateBuilder) Target(target float64) *OkrProgressRateBuilder {
	builder.target = target
	builder.targetFlag = true
	return builder
}

func (builder *OkrProgressRateBuilder) Build() *OkrProgressRate {
	req := &OkrProgressRate{}
	if builder.modeFlag {
		req.Mode = &builder.mode

	}
	if builder.currentFlag {
		req.Current = &builder.current

	}
	if builder.percentFlag {
		req.Percent = &builder.percent

	}
	if builder.progressStatusFlag {
		req.ProgressStatus = &builder.progressStatus

	}
	if builder.startFlag {
		req.Start = &builder.start

	}
	if builder.statusTypeFlag {
		req.StatusType = &builder.statusType

	}
	if builder.targetFlag {
		req.Target = &builder.target

	}
	return req
}

type OkrVisibleSetting struct {
	ProgressFillAreaVisible *bool `json:"progress_fill_area_visible,omitempty"` // 进展编辑区域是否可见
	ProgressStatusVisible   *bool `json:"progress_status_visible,omitempty"`    // 进展状态是否可见
	ScoreVisible            *bool `json:"score_visible,omitempty"`              // 分数是否可见
}

type OkrVisibleSettingBuilder struct {
	progressFillAreaVisible     bool // 进展编辑区域是否可见
	progressFillAreaVisibleFlag bool
	progressStatusVisible       bool // 进展状态是否可见
	progressStatusVisibleFlag   bool
	scoreVisible                bool // 分数是否可见
	scoreVisibleFlag            bool
}

func NewOkrVisibleSettingBuilder() *OkrVisibleSettingBuilder {
	builder := &OkrVisibleSettingBuilder{}
	return builder
}

// 进展编辑区域是否可见
//
// 示例值：true
func (builder *OkrVisibleSettingBuilder) ProgressFillAreaVisible(progressFillAreaVisible bool) *OkrVisibleSettingBuilder {
	builder.progressFillAreaVisible = progressFillAreaVisible
	builder.progressFillAreaVisibleFlag = true
	return builder
}

// 进展状态是否可见
//
// 示例值：true
func (builder *OkrVisibleSettingBuilder) ProgressStatusVisible(progressStatusVisible bool) *OkrVisibleSettingBuilder {
	builder.progressStatusVisible = progressStatusVisible
	builder.progressStatusVisibleFlag = true
	return builder
}

// 分数是否可见
//
// 示例值：true
func (builder *OkrVisibleSettingBuilder) ScoreVisible(scoreVisible bool) *OkrVisibleSettingBuilder {
	builder.scoreVisible = scoreVisible
	builder.scoreVisibleFlag = true
	return builder
}

func (builder *OkrVisibleSettingBuilder) Build() *OkrVisibleSetting {
	req := &OkrVisibleSetting{}
	if builder.progressFillAreaVisibleFlag {
		req.ProgressFillAreaVisible = &builder.progressFillAreaVisible

	}
	if builder.progressStatusVisibleFlag {
		req.ProgressStatusVisible = &builder.progressStatusVisible

	}
	if builder.scoreVisibleFlag {
		req.ScoreVisible = &builder.scoreVisible

	}
	return req
}

type QuoteContainer struct {
}

type Reminder struct {
	CreateUserId     *string           `json:"create_user_id,omitempty"`     // 创建者用户 ID
	IsNotify         *bool             `json:"is_notify,omitempty"`          // 是否通知
	IsWholeDay       *bool             `json:"is_whole_day,omitempty"`       // 是日期还是整点小时
	ExpireTime       *string           `json:"expire_time,omitempty"`        // 事件发生的时间（毫秒级事件戳）
	NotifyTime       *string           `json:"notify_time,omitempty"`        // 触发通知的时间（毫秒级时间戳）
	TextElementStyle *TextElementStyle `json:"text_element_style,omitempty"` // 文本局部样式
}

type ReminderBuilder struct {
	createUserId         string // 创建者用户 ID
	createUserIdFlag     bool
	isNotify             bool // 是否通知
	isNotifyFlag         bool
	isWholeDay           bool // 是日期还是整点小时
	isWholeDayFlag       bool
	expireTime           string // 事件发生的时间（毫秒级事件戳）
	expireTimeFlag       bool
	notifyTime           string // 触发通知的时间（毫秒级时间戳）
	notifyTimeFlag       bool
	textElementStyle     *TextElementStyle // 文本局部样式
	textElementStyleFlag bool
}

func NewReminderBuilder() *ReminderBuilder {
	builder := &ReminderBuilder{}
	return builder
}

// 创建者用户 ID
//
// 示例值：0e2633a3-aa1a-4171-af9e-0768ff863566
func (builder *ReminderBuilder) CreateUserId(createUserId string) *ReminderBuilder {
	builder.createUserId = createUserId
	builder.createUserIdFlag = true
	return builder
}

// 是否通知
//
// 示例值：true
func (builder *ReminderBuilder) IsNotify(isNotify bool) *ReminderBuilder {
	builder.isNotify = isNotify
	builder.isNotifyFlag = true
	return builder
}

// 是日期还是整点小时
//
// 示例值：true
func (builder *ReminderBuilder) IsWholeDay(isWholeDay bool) *ReminderBuilder {
	builder.isWholeDay = isWholeDay
	builder.isWholeDayFlag = true
	return builder
}

// 事件发生的时间（毫秒级事件戳）
//
// 示例值：1641967200000
func (builder *ReminderBuilder) ExpireTime(expireTime string) *ReminderBuilder {
	builder.expireTime = expireTime
	builder.expireTimeFlag = true
	return builder
}

// 触发通知的时间（毫秒级时间戳）
//
// 示例值：1643166000000
func (builder *ReminderBuilder) NotifyTime(notifyTime string) *ReminderBuilder {
	builder.notifyTime = notifyTime
	builder.notifyTimeFlag = true
	return builder
}

// 文本局部样式
//
// 示例值：
func (builder *ReminderBuilder) TextElementStyle(textElementStyle *TextElementStyle) *ReminderBuilder {
	builder.textElementStyle = textElementStyle
	builder.textElementStyleFlag = true
	return builder
}

func (builder *ReminderBuilder) Build() *Reminder {
	req := &Reminder{}
	if builder.createUserIdFlag {
		req.CreateUserId = &builder.createUserId

	}
	if builder.isNotifyFlag {
		req.IsNotify = &builder.isNotify

	}
	if builder.isWholeDayFlag {
		req.IsWholeDay = &builder.isWholeDay

	}
	if builder.expireTimeFlag {
		req.ExpireTime = &builder.expireTime

	}
	if builder.notifyTimeFlag {
		req.NotifyTime = &builder.notifyTime

	}
	if builder.textElementStyleFlag {
		req.TextElementStyle = builder.textElementStyle
	}
	return req
}

type ReplaceFileRequest struct {
	Token *string `json:"token,omitempty"` // 附件 token
}

type ReplaceFileRequestBuilder struct {
	token     string // 附件 token
	tokenFlag bool
}

func NewReplaceFileRequestBuilder() *ReplaceFileRequestBuilder {
	builder := &ReplaceFileRequestBuilder{}
	return builder
}

// 附件 token
//
// 示例值：boxbckbfvfcqEg22hAzN8Dh9gJd
func (builder *ReplaceFileRequestBuilder) Token(token string) *ReplaceFileRequestBuilder {
	builder.token = token
	builder.tokenFlag = true
	return builder
}

func (builder *ReplaceFileRequestBuilder) Build() *ReplaceFileRequest {
	req := &ReplaceFileRequest{}
	if builder.tokenFlag {
		req.Token = &builder.token

	}
	return req
}

type ReplaceImageRequest struct {
	Token *string `json:"token,omitempty"` // 图片 token
}

type ReplaceImageRequestBuilder struct {
	token     string // 图片 token
	tokenFlag bool
}

func NewReplaceImageRequestBuilder() *ReplaceImageRequestBuilder {
	builder := &ReplaceImageRequestBuilder{}
	return builder
}

// 图片 token
//
// 示例值：boxbckbfvfcqEg22hAzN8Dh9gJd
func (builder *ReplaceImageRequestBuilder) Token(token string) *ReplaceImageRequestBuilder {
	builder.token = token
	builder.tokenFlag = true
	return builder
}

func (builder *ReplaceImageRequestBuilder) Build() *ReplaceImageRequest {
	req := &ReplaceImageRequest{}
	if builder.tokenFlag {
		req.Token = &builder.token

	}
	return req
}

type Sheet struct {
	Token      *string `json:"token,omitempty"`       // 电子表格 block 的 token
	RowSize    *int    `json:"row_size,omitempty"`    // 电子表格行数量
	ColumnSize *int    `json:"column_size,omitempty"` // 电子表格列数量
}

type SheetBuilder struct {
	token          string // 电子表格 block 的 token
	tokenFlag      bool
	rowSize        int // 电子表格行数量
	rowSizeFlag    bool
	columnSize     int // 电子表格列数量
	columnSizeFlag bool
}

func NewSheetBuilder() *SheetBuilder {
	builder := &SheetBuilder{}
	return builder
}

// 电子表格 block 的 token
//
// 示例值：shtbcW6ufcUtRRet7Hz6Iv4ytzg
func (builder *SheetBuilder) Token(token string) *SheetBuilder {
	builder.token = token
	builder.tokenFlag = true
	return builder
}

// 电子表格行数量
//
// 示例值：2
func (builder *SheetBuilder) RowSize(rowSize int) *SheetBuilder {
	builder.rowSize = rowSize
	builder.rowSizeFlag = true
	return builder
}

// 电子表格列数量
//
// 示例值：2
func (builder *SheetBuilder) ColumnSize(columnSize int) *SheetBuilder {
	builder.columnSize = columnSize
	builder.columnSizeFlag = true
	return builder
}

func (builder *SheetBuilder) Build() *Sheet {
	req := &Sheet{}
	if builder.tokenFlag {
		req.Token = &builder.token

	}
	if builder.rowSizeFlag {
		req.RowSize = &builder.rowSize

	}
	if builder.columnSizeFlag {
		req.ColumnSize = &builder.columnSize

	}
	return req
}

type Table struct {
	Cells    []string       `json:"cells,omitempty"`    // 单元格数组，数组元素为 Table Cell Block 的 ID
	Property *TableProperty `json:"property,omitempty"` // 表格属性
}

type TableBuilder struct {
	cells        []string // 单元格数组，数组元素为 Table Cell Block 的 ID
	cellsFlag    bool
	property     *TableProperty // 表格属性
	propertyFlag bool
}

func NewTableBuilder() *TableBuilder {
	builder := &TableBuilder{}
	return builder
}

// 单元格数组，数组元素为 Table Cell Block 的 ID
//
// 示例值：
func (builder *TableBuilder) Cells(cells []string) *TableBuilder {
	builder.cells = cells
	builder.cellsFlag = true
	return builder
}

// 表格属性
//
// 示例值：
func (builder *TableBuilder) Property(property *TableProperty) *TableBuilder {
	builder.property = property
	builder.propertyFlag = true
	return builder
}

func (builder *TableBuilder) Build() *Table {
	req := &Table{}
	if builder.cellsFlag {
		req.Cells = builder.cells
	}
	if builder.propertyFlag {
		req.Property = builder.property
	}
	return req
}

type TableCell struct {
}

type TableMergeInfo struct {
	RowSpan *int `json:"row_span,omitempty"` // 从当前行索引起被合并的连续行数
	ColSpan *int `json:"col_span,omitempty"` // 从当前列索引起被合并的连续列数
}

type TableMergeInfoBuilder struct {
	rowSpan     int // 从当前行索引起被合并的连续行数
	rowSpanFlag bool
	colSpan     int // 从当前列索引起被合并的连续列数
	colSpanFlag bool
}

func NewTableMergeInfoBuilder() *TableMergeInfoBuilder {
	builder := &TableMergeInfoBuilder{}
	return builder
}

// 从当前行索引起被合并的连续行数
//
// 示例值：2
func (builder *TableMergeInfoBuilder) RowSpan(rowSpan int) *TableMergeInfoBuilder {
	builder.rowSpan = rowSpan
	builder.rowSpanFlag = true
	return builder
}

// 从当前列索引起被合并的连续列数
//
// 示例值：2
func (builder *TableMergeInfoBuilder) ColSpan(colSpan int) *TableMergeInfoBuilder {
	builder.colSpan = colSpan
	builder.colSpanFlag = true
	return builder
}

func (builder *TableMergeInfoBuilder) Build() *TableMergeInfo {
	req := &TableMergeInfo{}
	if builder.rowSpanFlag {
		req.RowSpan = &builder.rowSpan

	}
	if builder.colSpanFlag {
		req.ColSpan = &builder.colSpan

	}
	return req
}

type TableProperty struct {
	RowSize     *int              `json:"row_size,omitempty"`     // 行数
	ColumnSize  *int              `json:"column_size,omitempty"`  // 列数
	ColumnWidth []int             `json:"column_width,omitempty"` // 列宽，单位px
	MergeInfo   []*TableMergeInfo `json:"merge_info,omitempty"`   // 单元格合并信息
}

type TablePropertyBuilder struct {
	rowSize         int // 行数
	rowSizeFlag     bool
	columnSize      int // 列数
	columnSizeFlag  bool
	columnWidth     []int // 列宽，单位px
	columnWidthFlag bool
	mergeInfo       []*TableMergeInfo // 单元格合并信息
	mergeInfoFlag   bool
}

func NewTablePropertyBuilder() *TablePropertyBuilder {
	builder := &TablePropertyBuilder{}
	return builder
}

// 行数
//
// 示例值：1
func (builder *TablePropertyBuilder) RowSize(rowSize int) *TablePropertyBuilder {
	builder.rowSize = rowSize
	builder.rowSizeFlag = true
	return builder
}

// 列数
//
// 示例值：1
func (builder *TablePropertyBuilder) ColumnSize(columnSize int) *TablePropertyBuilder {
	builder.columnSize = columnSize
	builder.columnSizeFlag = true
	return builder
}

// 列宽，单位px
//
// 示例值：100
func (builder *TablePropertyBuilder) ColumnWidth(columnWidth []int) *TablePropertyBuilder {
	builder.columnWidth = columnWidth
	builder.columnWidthFlag = true
	return builder
}

// 单元格合并信息
//
// 示例值：
func (builder *TablePropertyBuilder) MergeInfo(mergeInfo []*TableMergeInfo) *TablePropertyBuilder {
	builder.mergeInfo = mergeInfo
	builder.mergeInfoFlag = true
	return builder
}

func (builder *TablePropertyBuilder) Build() *TableProperty {
	req := &TableProperty{}
	if builder.rowSizeFlag {
		req.RowSize = &builder.rowSize

	}
	if builder.columnSizeFlag {
		req.ColumnSize = &builder.columnSize

	}
	if builder.columnWidthFlag {
		req.ColumnWidth = builder.columnWidth
	}
	if builder.mergeInfoFlag {
		req.MergeInfo = builder.mergeInfo
	}
	return req
}

type Task struct {
	TaskId *string `json:"task_id,omitempty"` // 任务 ID，查询具体任务详情见[获取任务详情;](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/task-v1/task/get)
}

type TaskBuilder struct {
	taskId     string // 任务 ID，查询具体任务详情见[获取任务详情;](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/task-v1/task/get)
	taskIdFlag bool
}

func NewTaskBuilder() *TaskBuilder {
	builder := &TaskBuilder{}
	return builder
}

// 任务 ID，查询具体任务详情见[获取任务详情;](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/task-v1/task/get)
//
// 示例值：
func (builder *TaskBuilder) TaskId(taskId string) *TaskBuilder {
	builder.taskId = taskId
	builder.taskIdFlag = true
	return builder
}

func (builder *TaskBuilder) Build() *Task {
	req := &Task{}
	if builder.taskIdFlag {
		req.TaskId = &builder.taskId

	}
	return req
}

type Text struct {
	Style    *TextStyle     `json:"style,omitempty"`    // 文本样式
	Elements []*TextElement `json:"elements,omitempty"` // 文本元素
}

type TextBuilder struct {
	style        *TextStyle // 文本样式
	styleFlag    bool
	elements     []*TextElement // 文本元素
	elementsFlag bool
}

func NewTextBuilder() *TextBuilder {
	builder := &TextBuilder{}
	return builder
}

// 文本样式
//
// 示例值：
func (builder *TextBuilder) Style(style *TextStyle) *TextBuilder {
	builder.style = style
	builder.styleFlag = true
	return builder
}

// 文本元素
//
// 示例值：
func (builder *TextBuilder) Elements(elements []*TextElement) *TextBuilder {
	builder.elements = elements
	builder.elementsFlag = true
	return builder
}

func (builder *TextBuilder) Build() *Text {
	req := &Text{}
	if builder.styleFlag {
		req.Style = builder.style
	}
	if builder.elementsFlag {
		req.Elements = builder.elements
	}
	return req
}

type TextElement struct {
	TextRun     *TextRun          `json:"text_run,omitempty"`     // 文字
	MentionUser *MentionUser      `json:"mention_user,omitempty"` // @用户
	MentionDoc  *MentionDoc       `json:"mention_doc,omitempty"`  // @文档
	Reminder    *Reminder         `json:"reminder,omitempty"`     // 日期提醒
	File        *InlineFile       `json:"file,omitempty"`         // 内联附件
	Undefined   *UndefinedElement `json:"undefined,omitempty"`    // 未支持的 TextElement
	InlineBlock *InlineBlock      `json:"inline_block,omitempty"` // 内联 block
	Equation    *Equation         `json:"equation,omitempty"`     // 公式
}

type TextElementBuilder struct {
	textRun         *TextRun // 文字
	textRunFlag     bool
	mentionUser     *MentionUser // @用户
	mentionUserFlag bool
	mentionDoc      *MentionDoc // @文档
	mentionDocFlag  bool
	reminder        *Reminder // 日期提醒
	reminderFlag    bool
	file            *InlineFile // 内联附件
	fileFlag        bool
	undefined       *UndefinedElement // 未支持的 TextElement
	undefinedFlag   bool
	inlineBlock     *InlineBlock // 内联 block
	inlineBlockFlag bool
	equation        *Equation // 公式
	equationFlag    bool
}

func NewTextElementBuilder() *TextElementBuilder {
	builder := &TextElementBuilder{}
	return builder
}

// 文字
//
// 示例值：
func (builder *TextElementBuilder) TextRun(textRun *TextRun) *TextElementBuilder {
	builder.textRun = textRun
	builder.textRunFlag = true
	return builder
}

// @用户
//
// 示例值：
func (builder *TextElementBuilder) MentionUser(mentionUser *MentionUser) *TextElementBuilder {
	builder.mentionUser = mentionUser
	builder.mentionUserFlag = true
	return builder
}

// @文档
//
// 示例值：
func (builder *TextElementBuilder) MentionDoc(mentionDoc *MentionDoc) *TextElementBuilder {
	builder.mentionDoc = mentionDoc
	builder.mentionDocFlag = true
	return builder
}

// 日期提醒
//
// 示例值：
func (builder *TextElementBuilder) Reminder(reminder *Reminder) *TextElementBuilder {
	builder.reminder = reminder
	builder.reminderFlag = true
	return builder
}

// 内联附件
//
// 示例值：
func (builder *TextElementBuilder) File(file *InlineFile) *TextElementBuilder {
	builder.file = file
	builder.fileFlag = true
	return builder
}

// 未支持的 TextElement
//
// 示例值：
func (builder *TextElementBuilder) Undefined(undefined *UndefinedElement) *TextElementBuilder {
	builder.undefined = undefined
	builder.undefinedFlag = true
	return builder
}

// 内联 block
//
// 示例值：
func (builder *TextElementBuilder) InlineBlock(inlineBlock *InlineBlock) *TextElementBuilder {
	builder.inlineBlock = inlineBlock
	builder.inlineBlockFlag = true
	return builder
}

// 公式
//
// 示例值：
func (builder *TextElementBuilder) Equation(equation *Equation) *TextElementBuilder {
	builder.equation = equation
	builder.equationFlag = true
	return builder
}

func (builder *TextElementBuilder) Build() *TextElement {
	req := &TextElement{}
	if builder.textRunFlag {
		req.TextRun = builder.textRun
	}
	if builder.mentionUserFlag {
		req.MentionUser = builder.mentionUser
	}
	if builder.mentionDocFlag {
		req.MentionDoc = builder.mentionDoc
	}
	if builder.reminderFlag {
		req.Reminder = builder.reminder
	}
	if builder.fileFlag {
		req.File = builder.file
	}
	if builder.undefinedFlag {
		req.Undefined = builder.undefined
	}
	if builder.inlineBlockFlag {
		req.InlineBlock = builder.inlineBlock
	}
	if builder.equationFlag {
		req.Equation = builder.equation
	}
	return req
}

type TextElementStyle struct {
	Bold            *bool    `json:"bold,omitempty"`             // 加粗
	Italic          *bool    `json:"italic,omitempty"`           // 斜体
	Strikethrough   *bool    `json:"strikethrough,omitempty"`    // 删除线
	Underline       *bool    `json:"underline,omitempty"`        // 下划线
	InlineCode      *bool    `json:"inline_code,omitempty"`      // inline 代码
	BackgroundColor *int     `json:"background_color,omitempty"` // 背景色
	TextColor       *int     `json:"text_color,omitempty"`       // 字体颜色
	Link            *Link    `json:"link,omitempty"`             // 链接
	CommentIds      []string `json:"comment_ids,omitempty"`      // 评论 id 列表
}

type TextElementStyleBuilder struct {
	bold                bool // 加粗
	boldFlag            bool
	italic              bool // 斜体
	italicFlag          bool
	strikethrough       bool // 删除线
	strikethroughFlag   bool
	underline           bool // 下划线
	underlineFlag       bool
	inlineCode          bool // inline 代码
	inlineCodeFlag      bool
	backgroundColor     int // 背景色
	backgroundColorFlag bool
	textColor           int // 字体颜色
	textColorFlag       bool
	link                *Link // 链接
	linkFlag            bool
	commentIds          []string // 评论 id 列表
	commentIdsFlag      bool
}

func NewTextElementStyleBuilder() *TextElementStyleBuilder {
	builder := &TextElementStyleBuilder{}
	return builder
}

// 加粗
//
// 示例值：true
func (builder *TextElementStyleBuilder) Bold(bold bool) *TextElementStyleBuilder {
	builder.bold = bold
	builder.boldFlag = true
	return builder
}

// 斜体
//
// 示例值：true
func (builder *TextElementStyleBuilder) Italic(italic bool) *TextElementStyleBuilder {
	builder.italic = italic
	builder.italicFlag = true
	return builder
}

// 删除线
//
// 示例值：true
func (builder *TextElementStyleBuilder) Strikethrough(strikethrough bool) *TextElementStyleBuilder {
	builder.strikethrough = strikethrough
	builder.strikethroughFlag = true
	return builder
}

// 下划线
//
// 示例值：true
func (builder *TextElementStyleBuilder) Underline(underline bool) *TextElementStyleBuilder {
	builder.underline = underline
	builder.underlineFlag = true
	return builder
}

// inline 代码
//
// 示例值：true
func (builder *TextElementStyleBuilder) InlineCode(inlineCode bool) *TextElementStyleBuilder {
	builder.inlineCode = inlineCode
	builder.inlineCodeFlag = true
	return builder
}

// 背景色
//
// 示例值：1
func (builder *TextElementStyleBuilder) BackgroundColor(backgroundColor int) *TextElementStyleBuilder {
	builder.backgroundColor = backgroundColor
	builder.backgroundColorFlag = true
	return builder
}

// 字体颜色
//
// 示例值：1
func (builder *TextElementStyleBuilder) TextColor(textColor int) *TextElementStyleBuilder {
	builder.textColor = textColor
	builder.textColorFlag = true
	return builder
}

// 链接
//
// 示例值：
func (builder *TextElementStyleBuilder) Link(link *Link) *TextElementStyleBuilder {
	builder.link = link
	builder.linkFlag = true
	return builder
}

// 评论 id 列表
//
// 示例值：["1660030311959965796"]
func (builder *TextElementStyleBuilder) CommentIds(commentIds []string) *TextElementStyleBuilder {
	builder.commentIds = commentIds
	builder.commentIdsFlag = true
	return builder
}

func (builder *TextElementStyleBuilder) Build() *TextElementStyle {
	req := &TextElementStyle{}
	if builder.boldFlag {
		req.Bold = &builder.bold

	}
	if builder.italicFlag {
		req.Italic = &builder.italic

	}
	if builder.strikethroughFlag {
		req.Strikethrough = &builder.strikethrough

	}
	if builder.underlineFlag {
		req.Underline = &builder.underline

	}
	if builder.inlineCodeFlag {
		req.InlineCode = &builder.inlineCode

	}
	if builder.backgroundColorFlag {
		req.BackgroundColor = &builder.backgroundColor

	}
	if builder.textColorFlag {
		req.TextColor = &builder.textColor

	}
	if builder.linkFlag {
		req.Link = builder.link
	}
	if builder.commentIdsFlag {
		req.CommentIds = builder.commentIds
	}
	return req
}

type TextRun struct {
	Content          *string           `json:"content,omitempty"`            // 文本内容
	TextElementStyle *TextElementStyle `json:"text_element_style,omitempty"` // 文本局部样式
}

type TextRunBuilder struct {
	content              string // 文本内容
	contentFlag          bool
	textElementStyle     *TextElementStyle // 文本局部样式
	textElementStyleFlag bool
}

func NewTextRunBuilder() *TextRunBuilder {
	builder := &TextRunBuilder{}
	return builder
}

// 文本内容
//
// 示例值：文本
func (builder *TextRunBuilder) Content(content string) *TextRunBuilder {
	builder.content = content
	builder.contentFlag = true
	return builder
}

// 文本局部样式
//
// 示例值：
func (builder *TextRunBuilder) TextElementStyle(textElementStyle *TextElementStyle) *TextRunBuilder {
	builder.textElementStyle = textElementStyle
	builder.textElementStyleFlag = true
	return builder
}

func (builder *TextRunBuilder) Build() *TextRun {
	req := &TextRun{}
	if builder.contentFlag {
		req.Content = &builder.content

	}
	if builder.textElementStyleFlag {
		req.TextElementStyle = builder.textElementStyle
	}
	return req
}

type TextStyle struct {
	Align    *int  `json:"align,omitempty"`    // 对齐方式
	Done     *bool `json:"done,omitempty"`     // todo 的完成状态
	Folded   *bool `json:"folded,omitempty"`   // 文本的折叠状态
	Language *int  `json:"language,omitempty"` // 代码块语言
	Wrap     *bool `json:"wrap,omitempty"`     // 代码块是否自动换行
}

type TextStyleBuilder struct {
	align        int // 对齐方式
	alignFlag    bool
	done         bool // todo 的完成状态
	doneFlag     bool
	folded       bool // 文本的折叠状态
	foldedFlag   bool
	language     int // 代码块语言
	languageFlag bool
	wrap         bool // 代码块是否自动换行
	wrapFlag     bool
}

func NewTextStyleBuilder() *TextStyleBuilder {
	builder := &TextStyleBuilder{}
	return builder
}

// 对齐方式
//
// 示例值：1
func (builder *TextStyleBuilder) Align(align int) *TextStyleBuilder {
	builder.align = align
	builder.alignFlag = true
	return builder
}

// todo 的完成状态
//
// 示例值：true
func (builder *TextStyleBuilder) Done(done bool) *TextStyleBuilder {
	builder.done = done
	builder.doneFlag = true
	return builder
}

// 文本的折叠状态
//
// 示例值：true
func (builder *TextStyleBuilder) Folded(folded bool) *TextStyleBuilder {
	builder.folded = folded
	builder.foldedFlag = true
	return builder
}

// 代码块语言
//
// 示例值：1
func (builder *TextStyleBuilder) Language(language int) *TextStyleBuilder {
	builder.language = language
	builder.languageFlag = true
	return builder
}

// 代码块是否自动换行
//
// 示例值：true
func (builder *TextStyleBuilder) Wrap(wrap bool) *TextStyleBuilder {
	builder.wrap = wrap
	builder.wrapFlag = true
	return builder
}

func (builder *TextStyleBuilder) Build() *TextStyle {
	req := &TextStyle{}
	if builder.alignFlag {
		req.Align = &builder.align

	}
	if builder.doneFlag {
		req.Done = &builder.done

	}
	if builder.foldedFlag {
		req.Folded = &builder.folded

	}
	if builder.languageFlag {
		req.Language = &builder.language

	}
	if builder.wrapFlag {
		req.Wrap = &builder.wrap

	}
	return req
}

type Undefined struct {
}

type UndefinedElement struct {
}

type UnmergeTableCellsRequest struct {
	RowIndex    *int `json:"row_index,omitempty"`    // table 行索引
	ColumnIndex *int `json:"column_index,omitempty"` // table 列索引
}

type UnmergeTableCellsRequestBuilder struct {
	rowIndex        int // table 行索引
	rowIndexFlag    bool
	columnIndex     int // table 列索引
	columnIndexFlag bool
}

func NewUnmergeTableCellsRequestBuilder() *UnmergeTableCellsRequestBuilder {
	builder := &UnmergeTableCellsRequestBuilder{}
	return builder
}

// table 行索引
//
// 示例值：0
func (builder *UnmergeTableCellsRequestBuilder) RowIndex(rowIndex int) *UnmergeTableCellsRequestBuilder {
	builder.rowIndex = rowIndex
	builder.rowIndexFlag = true
	return builder
}

// table 列索引
//
// 示例值：0
func (builder *UnmergeTableCellsRequestBuilder) ColumnIndex(columnIndex int) *UnmergeTableCellsRequestBuilder {
	builder.columnIndex = columnIndex
	builder.columnIndexFlag = true
	return builder
}

func (builder *UnmergeTableCellsRequestBuilder) Build() *UnmergeTableCellsRequest {
	req := &UnmergeTableCellsRequest{}
	if builder.rowIndexFlag {
		req.RowIndex = &builder.rowIndex

	}
	if builder.columnIndexFlag {
		req.ColumnIndex = &builder.columnIndex

	}
	return req
}

type UpdateBlockRequest struct {
	UpdateTextElements         *UpdateTextElementsRequest         `json:"update_text_elements,omitempty"`           // 更新文本元素请求
	UpdateTextStyle            *UpdateTextStyleRequest            `json:"update_text_style,omitempty"`              // 更新文本样式请求
	UpdateTableProperty        *UpdateTablePropertyRequest        `json:"update_table_property,omitempty"`          // 更新表格属性请求
	InsertTableRow             *InsertTableRowRequest             `json:"insert_table_row,omitempty"`               // 表格插入新行请求
	InsertTableColumn          *InsertTableColumnRequest          `json:"insert_table_column,omitempty"`            // 表格插入新列请求
	DeleteTableRows            *DeleteTableRowsRequest            `json:"delete_table_rows,omitempty"`              // 表格批量删除行请求
	DeleteTableColumns         *DeleteTableColumnsRequest         `json:"delete_table_columns,omitempty"`           // 表格批量删除列请求
	MergeTableCells            *MergeTableCellsRequest            `json:"merge_table_cells,omitempty"`              // 表格合并单元格请求
	UnmergeTableCells          *UnmergeTableCellsRequest          `json:"unmerge_table_cells,omitempty"`            // 表格取消单元格合并状态请求
	InsertGridColumn           *InsertGridColumnRequest           `json:"insert_grid_column,omitempty"`             // 分栏插入新的分栏列请求
	DeleteGridColumn           *DeleteGridColumnRequest           `json:"delete_grid_column,omitempty"`             // 分栏删除列请求
	UpdateGridColumnWidthRatio *UpdateGridColumnWidthRatioRequest `json:"update_grid_column_width_ratio,omitempty"` // 更新分栏列宽比例请求
	ReplaceImage               *ReplaceImageRequest               `json:"replace_image,omitempty"`                  // 替换图片请求
	ReplaceFile                *ReplaceFileRequest                `json:"replace_file,omitempty"`                   // 替换附件请求
	BlockId                    *string                            `json:"block_id,omitempty"`                       // Block 唯一标识
	UpdateText                 *UpdateTextRequest                 `json:"update_text,omitempty"`                    // 更新文本元素及样式请求
}

type UpdateBlockRequestBuilder struct {
	updateTextElements             *UpdateTextElementsRequest // 更新文本元素请求
	updateTextElementsFlag         bool
	updateTextStyle                *UpdateTextStyleRequest // 更新文本样式请求
	updateTextStyleFlag            bool
	updateTableProperty            *UpdateTablePropertyRequest // 更新表格属性请求
	updateTablePropertyFlag        bool
	insertTableRow                 *InsertTableRowRequest // 表格插入新行请求
	insertTableRowFlag             bool
	insertTableColumn              *InsertTableColumnRequest // 表格插入新列请求
	insertTableColumnFlag          bool
	deleteTableRows                *DeleteTableRowsRequest // 表格批量删除行请求
	deleteTableRowsFlag            bool
	deleteTableColumns             *DeleteTableColumnsRequest // 表格批量删除列请求
	deleteTableColumnsFlag         bool
	mergeTableCells                *MergeTableCellsRequest // 表格合并单元格请求
	mergeTableCellsFlag            bool
	unmergeTableCells              *UnmergeTableCellsRequest // 表格取消单元格合并状态请求
	unmergeTableCellsFlag          bool
	insertGridColumn               *InsertGridColumnRequest // 分栏插入新的分栏列请求
	insertGridColumnFlag           bool
	deleteGridColumn               *DeleteGridColumnRequest // 分栏删除列请求
	deleteGridColumnFlag           bool
	updateGridColumnWidthRatio     *UpdateGridColumnWidthRatioRequest // 更新分栏列宽比例请求
	updateGridColumnWidthRatioFlag bool
	replaceImage                   *ReplaceImageRequest // 替换图片请求
	replaceImageFlag               bool
	replaceFile                    *ReplaceFileRequest // 替换附件请求
	replaceFileFlag                bool
	blockId                        string // Block 唯一标识
	blockIdFlag                    bool
	updateText                     *UpdateTextRequest // 更新文本元素及样式请求
	updateTextFlag                 bool
}

func NewUpdateBlockRequestBuilder() *UpdateBlockRequestBuilder {
	builder := &UpdateBlockRequestBuilder{}
	return builder
}

// 更新文本元素请求
//
// 示例值：
func (builder *UpdateBlockRequestBuilder) UpdateTextElements(updateTextElements *UpdateTextElementsRequest) *UpdateBlockRequestBuilder {
	builder.updateTextElements = updateTextElements
	builder.updateTextElementsFlag = true
	return builder
}

// 更新文本样式请求
//
// 示例值：
func (builder *UpdateBlockRequestBuilder) UpdateTextStyle(updateTextStyle *UpdateTextStyleRequest) *UpdateBlockRequestBuilder {
	builder.updateTextStyle = updateTextStyle
	builder.updateTextStyleFlag = true
	return builder
}

// 更新表格属性请求
//
// 示例值：
func (builder *UpdateBlockRequestBuilder) UpdateTableProperty(updateTableProperty *UpdateTablePropertyRequest) *UpdateBlockRequestBuilder {
	builder.updateTableProperty = updateTableProperty
	builder.updateTablePropertyFlag = true
	return builder
}

// 表格插入新行请求
//
// 示例值：
func (builder *UpdateBlockRequestBuilder) InsertTableRow(insertTableRow *InsertTableRowRequest) *UpdateBlockRequestBuilder {
	builder.insertTableRow = insertTableRow
	builder.insertTableRowFlag = true
	return builder
}

// 表格插入新列请求
//
// 示例值：
func (builder *UpdateBlockRequestBuilder) InsertTableColumn(insertTableColumn *InsertTableColumnRequest) *UpdateBlockRequestBuilder {
	builder.insertTableColumn = insertTableColumn
	builder.insertTableColumnFlag = true
	return builder
}

// 表格批量删除行请求
//
// 示例值：
func (builder *UpdateBlockRequestBuilder) DeleteTableRows(deleteTableRows *DeleteTableRowsRequest) *UpdateBlockRequestBuilder {
	builder.deleteTableRows = deleteTableRows
	builder.deleteTableRowsFlag = true
	return builder
}

// 表格批量删除列请求
//
// 示例值：
func (builder *UpdateBlockRequestBuilder) DeleteTableColumns(deleteTableColumns *DeleteTableColumnsRequest) *UpdateBlockRequestBuilder {
	builder.deleteTableColumns = deleteTableColumns
	builder.deleteTableColumnsFlag = true
	return builder
}

// 表格合并单元格请求
//
// 示例值：
func (builder *UpdateBlockRequestBuilder) MergeTableCells(mergeTableCells *MergeTableCellsRequest) *UpdateBlockRequestBuilder {
	builder.mergeTableCells = mergeTableCells
	builder.mergeTableCellsFlag = true
	return builder
}

// 表格取消单元格合并状态请求
//
// 示例值：
func (builder *UpdateBlockRequestBuilder) UnmergeTableCells(unmergeTableCells *UnmergeTableCellsRequest) *UpdateBlockRequestBuilder {
	builder.unmergeTableCells = unmergeTableCells
	builder.unmergeTableCellsFlag = true
	return builder
}

// 分栏插入新的分栏列请求
//
// 示例值：
func (builder *UpdateBlockRequestBuilder) InsertGridColumn(insertGridColumn *InsertGridColumnRequest) *UpdateBlockRequestBuilder {
	builder.insertGridColumn = insertGridColumn
	builder.insertGridColumnFlag = true
	return builder
}

// 分栏删除列请求
//
// 示例值：
func (builder *UpdateBlockRequestBuilder) DeleteGridColumn(deleteGridColumn *DeleteGridColumnRequest) *UpdateBlockRequestBuilder {
	builder.deleteGridColumn = deleteGridColumn
	builder.deleteGridColumnFlag = true
	return builder
}

// 更新分栏列宽比例请求
//
// 示例值：
func (builder *UpdateBlockRequestBuilder) UpdateGridColumnWidthRatio(updateGridColumnWidthRatio *UpdateGridColumnWidthRatioRequest) *UpdateBlockRequestBuilder {
	builder.updateGridColumnWidthRatio = updateGridColumnWidthRatio
	builder.updateGridColumnWidthRatioFlag = true
	return builder
}

// 替换图片请求
//
// 示例值：
func (builder *UpdateBlockRequestBuilder) ReplaceImage(replaceImage *ReplaceImageRequest) *UpdateBlockRequestBuilder {
	builder.replaceImage = replaceImage
	builder.replaceImageFlag = true
	return builder
}

// 替换附件请求
//
// 示例值：
func (builder *UpdateBlockRequestBuilder) ReplaceFile(replaceFile *ReplaceFileRequest) *UpdateBlockRequestBuilder {
	builder.replaceFile = replaceFile
	builder.replaceFileFlag = true
	return builder
}

// Block 唯一标识
//
// 示例值：doxcnSS4ouQkQEouGSUkTg9NJPe
func (builder *UpdateBlockRequestBuilder) BlockId(blockId string) *UpdateBlockRequestBuilder {
	builder.blockId = blockId
	builder.blockIdFlag = true
	return builder
}

// 更新文本元素及样式请求
//
// 示例值：
func (builder *UpdateBlockRequestBuilder) UpdateText(updateText *UpdateTextRequest) *UpdateBlockRequestBuilder {
	builder.updateText = updateText
	builder.updateTextFlag = true
	return builder
}

func (builder *UpdateBlockRequestBuilder) Build() *UpdateBlockRequest {
	req := &UpdateBlockRequest{}
	if builder.updateTextElementsFlag {
		req.UpdateTextElements = builder.updateTextElements
	}
	if builder.updateTextStyleFlag {
		req.UpdateTextStyle = builder.updateTextStyle
	}
	if builder.updateTablePropertyFlag {
		req.UpdateTableProperty = builder.updateTableProperty
	}
	if builder.insertTableRowFlag {
		req.InsertTableRow = builder.insertTableRow
	}
	if builder.insertTableColumnFlag {
		req.InsertTableColumn = builder.insertTableColumn
	}
	if builder.deleteTableRowsFlag {
		req.DeleteTableRows = builder.deleteTableRows
	}
	if builder.deleteTableColumnsFlag {
		req.DeleteTableColumns = builder.deleteTableColumns
	}
	if builder.mergeTableCellsFlag {
		req.MergeTableCells = builder.mergeTableCells
	}
	if builder.unmergeTableCellsFlag {
		req.UnmergeTableCells = builder.unmergeTableCells
	}
	if builder.insertGridColumnFlag {
		req.InsertGridColumn = builder.insertGridColumn
	}
	if builder.deleteGridColumnFlag {
		req.DeleteGridColumn = builder.deleteGridColumn
	}
	if builder.updateGridColumnWidthRatioFlag {
		req.UpdateGridColumnWidthRatio = builder.updateGridColumnWidthRatio
	}
	if builder.replaceImageFlag {
		req.ReplaceImage = builder.replaceImage
	}
	if builder.replaceFileFlag {
		req.ReplaceFile = builder.replaceFile
	}
	if builder.blockIdFlag {
		req.BlockId = &builder.blockId

	}
	if builder.updateTextFlag {
		req.UpdateText = builder.updateText
	}
	return req
}

type UpdateGridColumnWidthRatioRequest struct {
	WidthRatios []int `json:"width_ratios,omitempty"` // 更新列宽比例时，需要传入所有列宽占比
}

type UpdateGridColumnWidthRatioRequestBuilder struct {
	widthRatios     []int // 更新列宽比例时，需要传入所有列宽占比
	widthRatiosFlag bool
}

func NewUpdateGridColumnWidthRatioRequestBuilder() *UpdateGridColumnWidthRatioRequestBuilder {
	builder := &UpdateGridColumnWidthRatioRequestBuilder{}
	return builder
}

// 更新列宽比例时，需要传入所有列宽占比
//
// 示例值：50
func (builder *UpdateGridColumnWidthRatioRequestBuilder) WidthRatios(widthRatios []int) *UpdateGridColumnWidthRatioRequestBuilder {
	builder.widthRatios = widthRatios
	builder.widthRatiosFlag = true
	return builder
}

func (builder *UpdateGridColumnWidthRatioRequestBuilder) Build() *UpdateGridColumnWidthRatioRequest {
	req := &UpdateGridColumnWidthRatioRequest{}
	if builder.widthRatiosFlag {
		req.WidthRatios = builder.widthRatios
	}
	return req
}

type UpdateTablePropertyRequest struct {
	ColumnWidth *int `json:"column_width,omitempty"` // 表格列宽
	ColumnIndex *int `json:"column_index,omitempty"` // 需要修改列宽的表格列的索引
}

type UpdateTablePropertyRequestBuilder struct {
	columnWidth     int // 表格列宽
	columnWidthFlag bool
	columnIndex     int // 需要修改列宽的表格列的索引
	columnIndexFlag bool
}

func NewUpdateTablePropertyRequestBuilder() *UpdateTablePropertyRequestBuilder {
	builder := &UpdateTablePropertyRequestBuilder{}
	return builder
}

// 表格列宽
//
// 示例值：100
func (builder *UpdateTablePropertyRequestBuilder) ColumnWidth(columnWidth int) *UpdateTablePropertyRequestBuilder {
	builder.columnWidth = columnWidth
	builder.columnWidthFlag = true
	return builder
}

// 需要修改列宽的表格列的索引
//
// 示例值：0
func (builder *UpdateTablePropertyRequestBuilder) ColumnIndex(columnIndex int) *UpdateTablePropertyRequestBuilder {
	builder.columnIndex = columnIndex
	builder.columnIndexFlag = true
	return builder
}

func (builder *UpdateTablePropertyRequestBuilder) Build() *UpdateTablePropertyRequest {
	req := &UpdateTablePropertyRequest{}
	if builder.columnWidthFlag {
		req.ColumnWidth = &builder.columnWidth

	}
	if builder.columnIndexFlag {
		req.ColumnIndex = &builder.columnIndex

	}
	return req
}

type UpdateTextElementsRequest struct {
	Elements []*TextElement `json:"elements,omitempty"` // 更新的文本元素列表，单次更新中 reminder 上限 30 个，mention_doc 上限 50 个，mention_user 上限 100 个
}

type UpdateTextElementsRequestBuilder struct {
	elements     []*TextElement // 更新的文本元素列表，单次更新中 reminder 上限 30 个，mention_doc 上限 50 个，mention_user 上限 100 个
	elementsFlag bool
}

func NewUpdateTextElementsRequestBuilder() *UpdateTextElementsRequestBuilder {
	builder := &UpdateTextElementsRequestBuilder{}
	return builder
}

// 更新的文本元素列表，单次更新中 reminder 上限 30 个，mention_doc 上限 50 个，mention_user 上限 100 个
//
// 示例值：
func (builder *UpdateTextElementsRequestBuilder) Elements(elements []*TextElement) *UpdateTextElementsRequestBuilder {
	builder.elements = elements
	builder.elementsFlag = true
	return builder
}

func (builder *UpdateTextElementsRequestBuilder) Build() *UpdateTextElementsRequest {
	req := &UpdateTextElementsRequest{}
	if builder.elementsFlag {
		req.Elements = builder.elements
	}
	return req
}

type UpdateTextRequest struct {
	Elements []*TextElement `json:"elements,omitempty"` // 更新的文本元素列表，单次更新中 reminder 上限 30 个，mention_doc 上限 50 个，mention_user 上限 100 个
	Style    *TextStyle     `json:"style,omitempty"`    // 更新的文本样式
	Fields   []int          `json:"fields,omitempty"`   // 文本样式中应更新的字段，必须至少指定一个字段。例如，要调整 Block 对齐方式，请设置 fields 为 [1]。
}

type UpdateTextRequestBuilder struct {
	elements     []*TextElement // 更新的文本元素列表，单次更新中 reminder 上限 30 个，mention_doc 上限 50 个，mention_user 上限 100 个
	elementsFlag bool
	style        *TextStyle // 更新的文本样式
	styleFlag    bool
	fields       []int // 文本样式中应更新的字段，必须至少指定一个字段。例如，要调整 Block 对齐方式，请设置 fields 为 [1]。
	fieldsFlag   bool
}

func NewUpdateTextRequestBuilder() *UpdateTextRequestBuilder {
	builder := &UpdateTextRequestBuilder{}
	return builder
}

// 更新的文本元素列表，单次更新中 reminder 上限 30 个，mention_doc 上限 50 个，mention_user 上限 100 个
//
// 示例值：
func (builder *UpdateTextRequestBuilder) Elements(elements []*TextElement) *UpdateTextRequestBuilder {
	builder.elements = elements
	builder.elementsFlag = true
	return builder
}

// 更新的文本样式
//
// 示例值：
func (builder *UpdateTextRequestBuilder) Style(style *TextStyle) *UpdateTextRequestBuilder {
	builder.style = style
	builder.styleFlag = true
	return builder
}

// 文本样式中应更新的字段，必须至少指定一个字段。例如，要调整 Block 对齐方式，请设置 fields 为 [1]。
//
// 示例值：[1]
func (builder *UpdateTextRequestBuilder) Fields(fields []int) *UpdateTextRequestBuilder {
	builder.fields = fields
	builder.fieldsFlag = true
	return builder
}

func (builder *UpdateTextRequestBuilder) Build() *UpdateTextRequest {
	req := &UpdateTextRequest{}
	if builder.elementsFlag {
		req.Elements = builder.elements
	}
	if builder.styleFlag {
		req.Style = builder.style
	}
	if builder.fieldsFlag {
		req.Fields = builder.fields
	}
	return req
}

type UpdateTextStyleRequest struct {
	Style  *TextStyle `json:"style,omitempty"`  // 文本样式
	Fields []int      `json:"fields,omitempty"` // 应更新的字段，必须至少指定一个字段。例如，要调整 Block 对齐方式，请设置 fields 为 [1]。
}

type UpdateTextStyleRequestBuilder struct {
	style      *TextStyle // 文本样式
	styleFlag  bool
	fields     []int // 应更新的字段，必须至少指定一个字段。例如，要调整 Block 对齐方式，请设置 fields 为 [1]。
	fieldsFlag bool
}

func NewUpdateTextStyleRequestBuilder() *UpdateTextStyleRequestBuilder {
	builder := &UpdateTextStyleRequestBuilder{}
	return builder
}

// 文本样式
//
// 示例值：
func (builder *UpdateTextStyleRequestBuilder) Style(style *TextStyle) *UpdateTextStyleRequestBuilder {
	builder.style = style
	builder.styleFlag = true
	return builder
}

// 应更新的字段，必须至少指定一个字段。例如，要调整 Block 对齐方式，请设置 fields 为 [1]。
//
// 示例值：修改的文字样式属性
func (builder *UpdateTextStyleRequestBuilder) Fields(fields []int) *UpdateTextStyleRequestBuilder {
	builder.fields = fields
	builder.fieldsFlag = true
	return builder
}

func (builder *UpdateTextStyleRequestBuilder) Build() *UpdateTextStyleRequest {
	req := &UpdateTextStyleRequest{}
	if builder.styleFlag {
		req.Style = builder.style
	}
	if builder.fieldsFlag {
		req.Fields = builder.fields
	}
	return req
}

type View struct {
	ViewType *int `json:"view_type,omitempty"` // 视图类型
}

type ViewBuilder struct {
	viewType     int // 视图类型
	viewTypeFlag bool
}

func NewViewBuilder() *ViewBuilder {
	builder := &ViewBuilder{}
	return builder
}

// 视图类型
//
// 示例值：1
func (builder *ViewBuilder) ViewType(viewType int) *ViewBuilder {
	builder.viewType = viewType
	builder.viewTypeFlag = true
	return builder
}

func (builder *ViewBuilder) Build() *View {
	req := &View{}
	if builder.viewTypeFlag {
		req.ViewType = &builder.viewType

	}
	return req
}

type CreateDocumentReqBodyBuilder struct {
	folderToken     string // 文件夹 token，获取方式见云文档接口快速入门；空表示根目录，tenant_access_token应用权限仅允许操作应用创建的目录
	folderTokenFlag bool
	title           string // 文档标题，只支持纯文本
	titleFlag       bool
}

func NewCreateDocumentReqBodyBuilder() *CreateDocumentReqBodyBuilder {
	builder := &CreateDocumentReqBodyBuilder{}
	return builder
}

// 文件夹 token，获取方式见云文档接口快速入门；空表示根目录，tenant_access_token应用权限仅允许操作应用创建的目录
//
//示例值：fldcnqquW1svRIYVT2Np6IuLCKd
func (builder *CreateDocumentReqBodyBuilder) FolderToken(folderToken string) *CreateDocumentReqBodyBuilder {
	builder.folderToken = folderToken
	builder.folderTokenFlag = true
	return builder
}

// 文档标题，只支持纯文本
//
//示例值：undefined
func (builder *CreateDocumentReqBodyBuilder) Title(title string) *CreateDocumentReqBodyBuilder {
	builder.title = title
	builder.titleFlag = true
	return builder
}

func (builder *CreateDocumentReqBodyBuilder) Build() *CreateDocumentReqBody {
	req := &CreateDocumentReqBody{}
	if builder.folderTokenFlag {
		req.FolderToken = &builder.folderToken
	}
	if builder.titleFlag {
		req.Title = &builder.title
	}
	return req
}

type CreateDocumentPathReqBodyBuilder struct {
	folderToken     string // 文件夹 token，获取方式见云文档接口快速入门；空表示根目录，tenant_access_token应用权限仅允许操作应用创建的目录
	folderTokenFlag bool
	title           string // 文档标题，只支持纯文本
	titleFlag       bool
}

func NewCreateDocumentPathReqBodyBuilder() *CreateDocumentPathReqBodyBuilder {
	builder := &CreateDocumentPathReqBodyBuilder{}
	return builder
}

// 文件夹 token，获取方式见云文档接口快速入门；空表示根目录，tenant_access_token应用权限仅允许操作应用创建的目录
//
// 示例值：fldcnqquW1svRIYVT2Np6IuLCKd
func (builder *CreateDocumentPathReqBodyBuilder) FolderToken(folderToken string) *CreateDocumentPathReqBodyBuilder {
	builder.folderToken = folderToken
	builder.folderTokenFlag = true
	return builder
}

// 文档标题，只支持纯文本
//
// 示例值：undefined
func (builder *CreateDocumentPathReqBodyBuilder) Title(title string) *CreateDocumentPathReqBodyBuilder {
	builder.title = title
	builder.titleFlag = true
	return builder
}

func (builder *CreateDocumentPathReqBodyBuilder) Build() (*CreateDocumentReqBody, error) {
	req := &CreateDocumentReqBody{}
	if builder.folderTokenFlag {
		req.FolderToken = &builder.folderToken
	}
	if builder.titleFlag {
		req.Title = &builder.title
	}
	return req, nil
}

type CreateDocumentReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *CreateDocumentReqBody
}

func NewCreateDocumentReqBuilder() *CreateDocumentReqBuilder {
	builder := &CreateDocumentReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 创建新版文档，文档标题和目录可选。
func (builder *CreateDocumentReqBuilder) Body(body *CreateDocumentReqBody) *CreateDocumentReqBuilder {
	builder.body = body
	return builder
}

func (builder *CreateDocumentReqBuilder) Build() *CreateDocumentReq {
	req := &CreateDocumentReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.Body = builder.body
	return req
}

type CreateDocumentReqBody struct {
	FolderToken *string `json:"folder_token,omitempty"` // 文件夹 token，获取方式见云文档接口快速入门；空表示根目录，tenant_access_token应用权限仅允许操作应用创建的目录
	Title       *string `json:"title,omitempty"`        // 文档标题，只支持纯文本
}

type CreateDocumentReq struct {
	apiReq *larkcore.ApiReq
	Body   *CreateDocumentReqBody `body:""`
}

type CreateDocumentRespData struct {
	Document *Document `json:"document,omitempty"` // 新建文档的文档信息
}

type CreateDocumentResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *CreateDocumentRespData `json:"data"` // 业务数据
}

func (resp *CreateDocumentResp) Success() bool {
	return resp.Code == 0
}

type GetDocumentReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewGetDocumentReqBuilder() *GetDocumentReqBuilder {
	builder := &GetDocumentReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 文档的唯一标识
//
// 示例值：doxcnePuYufKa49ISjhD8Ih0ikh
func (builder *GetDocumentReqBuilder) DocumentId(documentId string) *GetDocumentReqBuilder {
	builder.apiReq.PathParams.Set("document_id", fmt.Sprint(documentId))
	return builder
}

func (builder *GetDocumentReqBuilder) Build() *GetDocumentReq {
	req := &GetDocumentReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	return req
}

type GetDocumentReq struct {
	apiReq *larkcore.ApiReq
}

type GetDocumentRespData struct {
	Document *Document `json:"document,omitempty"` // 文档信息
}

type GetDocumentResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *GetDocumentRespData `json:"data"` // 业务数据
}

func (resp *GetDocumentResp) Success() bool {
	return resp.Code == 0
}

type RawContentDocumentReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewRawContentDocumentReqBuilder() *RawContentDocumentReqBuilder {
	builder := &RawContentDocumentReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 文档的唯一标识
//
// 示例值：doxbcmEtbFrbbq10nPNu8gO1F3b
func (builder *RawContentDocumentReqBuilder) DocumentId(documentId string) *RawContentDocumentReqBuilder {
	builder.apiReq.PathParams.Set("document_id", fmt.Sprint(documentId))
	return builder
}

// 语言（用于 MentionUser 语言的选取）
//
// 示例值：0
func (builder *RawContentDocumentReqBuilder) Lang(lang int) *RawContentDocumentReqBuilder {
	builder.apiReq.QueryParams.Set("lang", fmt.Sprint(lang))
	return builder
}

func (builder *RawContentDocumentReqBuilder) Build() *RawContentDocumentReq {
	req := &RawContentDocumentReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type RawContentDocumentReq struct {
	apiReq *larkcore.ApiReq
}

type RawContentDocumentRespData struct {
	Content *string `json:"content,omitempty"` // 文档纯文本
}

type RawContentDocumentResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *RawContentDocumentRespData `json:"data"` // 业务数据
}

func (resp *RawContentDocumentResp) Success() bool {
	return resp.Code == 0
}

type BatchUpdateDocumentBlockReqBodyBuilder struct {
	requests     []*UpdateBlockRequest // 批量更新 Block
	requestsFlag bool
}

func NewBatchUpdateDocumentBlockReqBodyBuilder() *BatchUpdateDocumentBlockReqBodyBuilder {
	builder := &BatchUpdateDocumentBlockReqBodyBuilder{}
	return builder
}

// 批量更新 Block
//
//示例值：
func (builder *BatchUpdateDocumentBlockReqBodyBuilder) Requests(requests []*UpdateBlockRequest) *BatchUpdateDocumentBlockReqBodyBuilder {
	builder.requests = requests
	builder.requestsFlag = true
	return builder
}

func (builder *BatchUpdateDocumentBlockReqBodyBuilder) Build() *BatchUpdateDocumentBlockReqBody {
	req := &BatchUpdateDocumentBlockReqBody{}
	if builder.requestsFlag {
		req.Requests = builder.requests
	}
	return req
}

type BatchUpdateDocumentBlockPathReqBodyBuilder struct {
	requests     []*UpdateBlockRequest // 批量更新 Block
	requestsFlag bool
}

func NewBatchUpdateDocumentBlockPathReqBodyBuilder() *BatchUpdateDocumentBlockPathReqBodyBuilder {
	builder := &BatchUpdateDocumentBlockPathReqBodyBuilder{}
	return builder
}

// 批量更新 Block
//
// 示例值：
func (builder *BatchUpdateDocumentBlockPathReqBodyBuilder) Requests(requests []*UpdateBlockRequest) *BatchUpdateDocumentBlockPathReqBodyBuilder {
	builder.requests = requests
	builder.requestsFlag = true
	return builder
}

func (builder *BatchUpdateDocumentBlockPathReqBodyBuilder) Build() (*BatchUpdateDocumentBlockReqBody, error) {
	req := &BatchUpdateDocumentBlockReqBody{}
	if builder.requestsFlag {
		req.Requests = builder.requests
	}
	return req, nil
}

type BatchUpdateDocumentBlockReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *BatchUpdateDocumentBlockReqBody
}

func NewBatchUpdateDocumentBlockReqBuilder() *BatchUpdateDocumentBlockReqBuilder {
	builder := &BatchUpdateDocumentBlockReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 文档的唯一标识
//
// 示例值：doxcnePuYufKa49ISjhD8Ih0ikh
func (builder *BatchUpdateDocumentBlockReqBuilder) DocumentId(documentId string) *BatchUpdateDocumentBlockReqBuilder {
	builder.apiReq.PathParams.Set("document_id", fmt.Sprint(documentId))
	return builder
}

// 操作的文档版本，-1表示文档最新版本。若此时操作的版本为文档最新版本，则需要持有文档的阅读权限；若此时操作的版本为文档的历史版本，则需要持有文档的编辑权限。
//
// 示例值：-1
func (builder *BatchUpdateDocumentBlockReqBuilder) DocumentRevisionId(documentRevisionId int) *BatchUpdateDocumentBlockReqBuilder {
	builder.apiReq.QueryParams.Set("document_revision_id", fmt.Sprint(documentRevisionId))
	return builder
}

// 操作的唯一标识，与接口返回值的 client_token 相对应，用于幂等的进行更新操作。此值为空表示将发起一次新的请求，此值非空表示幂等的进行更新操作。
//
// 示例值：0e2633a3-aa1a-4171-af9e-0768ff863566
func (builder *BatchUpdateDocumentBlockReqBuilder) ClientToken(clientToken string) *BatchUpdateDocumentBlockReqBuilder {
	builder.apiReq.QueryParams.Set("client_token", fmt.Sprint(clientToken))
	return builder
}

// 此次调用中使用的用户ID的类型
//
// 示例值：
func (builder *BatchUpdateDocumentBlockReqBuilder) UserIdType(userIdType string) *BatchUpdateDocumentBlockReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

// 批量更新块的富文本内容。
func (builder *BatchUpdateDocumentBlockReqBuilder) Body(body *BatchUpdateDocumentBlockReqBody) *BatchUpdateDocumentBlockReqBuilder {
	builder.body = body
	return builder
}

func (builder *BatchUpdateDocumentBlockReqBuilder) Build() *BatchUpdateDocumentBlockReq {
	req := &BatchUpdateDocumentBlockReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.body
	return req
}

type BatchUpdateDocumentBlockReqBody struct {
	Requests []*UpdateBlockRequest `json:"requests,omitempty"` // 批量更新 Block
}

type BatchUpdateDocumentBlockReq struct {
	apiReq *larkcore.ApiReq
	Body   *BatchUpdateDocumentBlockReqBody `body:""`
}

type BatchUpdateDocumentBlockRespData struct {
	Blocks             []*Block `json:"blocks,omitempty"`               // 批量更新的 Block
	DocumentRevisionId *int     `json:"document_revision_id,omitempty"` // 当前更新成功后文档的版本号
	ClientToken        *string  `json:"client_token,omitempty"`         // 操作的唯一标识，更新请求中使用此值表示幂等的进行此次更新
}

type BatchUpdateDocumentBlockResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *BatchUpdateDocumentBlockRespData `json:"data"` // 业务数据
}

func (resp *BatchUpdateDocumentBlockResp) Success() bool {
	return resp.Code == 0
}

type GetDocumentBlockReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewGetDocumentBlockReqBuilder() *GetDocumentBlockReqBuilder {
	builder := &GetDocumentBlockReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 文档的唯一标识
//
// 示例值：doxcnePuYufKa49ISjhD8Ih0ikh
func (builder *GetDocumentBlockReqBuilder) DocumentId(documentId string) *GetDocumentBlockReqBuilder {
	builder.apiReq.PathParams.Set("document_id", fmt.Sprint(documentId))
	return builder
}

// Block 的唯一标识
//
// 示例值：doxcnO6UW6wAw2qIcYf4hZpFIth
func (builder *GetDocumentBlockReqBuilder) BlockId(blockId string) *GetDocumentBlockReqBuilder {
	builder.apiReq.PathParams.Set("block_id", fmt.Sprint(blockId))
	return builder
}

// 查询的文档版本，-1表示文档最新版本。若此时查询的版本为文档最新版本，则需要持有文档的阅读权限；若此时查询的版本为文档的历史版本，则需要持有文档的编辑权限。
//
// 示例值：-1
func (builder *GetDocumentBlockReqBuilder) DocumentRevisionId(documentRevisionId int) *GetDocumentBlockReqBuilder {
	builder.apiReq.QueryParams.Set("document_revision_id", fmt.Sprint(documentRevisionId))
	return builder
}

// 此次调用中使用的用户ID的类型
//
// 示例值：
func (builder *GetDocumentBlockReqBuilder) UserIdType(userIdType string) *GetDocumentBlockReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

func (builder *GetDocumentBlockReqBuilder) Build() *GetDocumentBlockReq {
	req := &GetDocumentBlockReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type GetDocumentBlockReq struct {
	apiReq *larkcore.ApiReq
}

type GetDocumentBlockRespData struct {
	Block *Block `json:"block,omitempty"` // 查询的 Block 的信息
}

type GetDocumentBlockResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *GetDocumentBlockRespData `json:"data"` // 业务数据
}

func (resp *GetDocumentBlockResp) Success() bool {
	return resp.Code == 0
}

type ListDocumentBlockReqBuilder struct {
	apiReq *larkcore.ApiReq
	limit  int // 最大返回多少记录，当使用迭代器访问时才有效
}

func NewListDocumentBlockReqBuilder() *ListDocumentBlockReqBuilder {
	builder := &ListDocumentBlockReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 最大返回多少记录，当使用迭代器访问时才有效
func (builder *ListDocumentBlockReqBuilder) Limit(limit int) *ListDocumentBlockReqBuilder {
	builder.limit = limit
	return builder
}

// 文档的唯一标识
//
// 示例值：doxcnePuYufKa49ISjhD8Ih0ikh
func (builder *ListDocumentBlockReqBuilder) DocumentId(documentId string) *ListDocumentBlockReqBuilder {
	builder.apiReq.PathParams.Set("document_id", fmt.Sprint(documentId))
	return builder
}

// 分页大小
//
// 示例值：500
func (builder *ListDocumentBlockReqBuilder) PageSize(pageSize int) *ListDocumentBlockReqBuilder {
	builder.apiReq.QueryParams.Set("page_size", fmt.Sprint(pageSize))
	return builder
}

// 分页标记，第一次请求不填，表示从头开始遍历；分页查询结果还有更多项时会同时返回新的 page_token，下次遍历可采用该 page_token 获取查询结果
//
// 示例值：aw7DoMKBFMOGwqHCrcO8w6jCmMOvw6ILeADCvsKNw57Di8O5XGV3LG4_w5HCqhFxSnDCrCzCn0BgZcOYUg85EMOYcEAcwqYOw4ojw5QFwofCu8KoIMO3K8Ktw4IuNMOBBHNYw4bCgCV3U1zDu8K-J8KSR8Kgw7Y0fsKZdsKvW3d9w53DnkHDrcO5bDkYwrvDisOEPcOtVFJ-I03CnsOILMOoAmLDknd6dsKqG1bClAjDuS3CvcOTwo7Dg8OrwovDsRdqIcKxw5HDohTDtXN9w5rCkWo
func (builder *ListDocumentBlockReqBuilder) PageToken(pageToken string) *ListDocumentBlockReqBuilder {
	builder.apiReq.QueryParams.Set("page_token", fmt.Sprint(pageToken))
	return builder
}

// 查询的文档版本，-1表示文档最新版本。若此时查询的版本为文档最新版本，则需要持有文档的阅读权限；若此时查询的版本为文档的历史版本，则需要持有文档的编辑权限。
//
// 示例值：-1
func (builder *ListDocumentBlockReqBuilder) DocumentRevisionId(documentRevisionId int) *ListDocumentBlockReqBuilder {
	builder.apiReq.QueryParams.Set("document_revision_id", fmt.Sprint(documentRevisionId))
	return builder
}

// 此次调用中使用的用户ID的类型
//
// 示例值：
func (builder *ListDocumentBlockReqBuilder) UserIdType(userIdType string) *ListDocumentBlockReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

func (builder *ListDocumentBlockReqBuilder) Build() *ListDocumentBlockReq {
	req := &ListDocumentBlockReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.Limit = builder.limit
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type ListDocumentBlockReq struct {
	apiReq *larkcore.ApiReq
	Limit  int // 最多返回多少记录，只有在使用迭代器访问时，才有效

}

type ListDocumentBlockRespData struct {
	Items     []*Block `json:"items,omitempty"`      // 文档的 Block 信息
	PageToken *string  `json:"page_token,omitempty"` // 下一个分页的分页标记
	HasMore   *bool    `json:"has_more,omitempty"`   // 是否还有下一个分页
}

type ListDocumentBlockResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *ListDocumentBlockRespData `json:"data"` // 业务数据
}

func (resp *ListDocumentBlockResp) Success() bool {
	return resp.Code == 0
}

type PatchDocumentBlockReqBuilder struct {
	apiReq             *larkcore.ApiReq
	updateBlockRequest *UpdateBlockRequest
}

func NewPatchDocumentBlockReqBuilder() *PatchDocumentBlockReqBuilder {
	builder := &PatchDocumentBlockReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 文档的唯一标识
//
// 示例值：doxcnePuYufKa49ISjhD8Ih0ikh
func (builder *PatchDocumentBlockReqBuilder) DocumentId(documentId string) *PatchDocumentBlockReqBuilder {
	builder.apiReq.PathParams.Set("document_id", fmt.Sprint(documentId))
	return builder
}

// Block 的唯一标识
//
// 示例值：doxcnO6UW6wAw2qIcYf4hZpFIth
func (builder *PatchDocumentBlockReqBuilder) BlockId(blockId string) *PatchDocumentBlockReqBuilder {
	builder.apiReq.PathParams.Set("block_id", fmt.Sprint(blockId))
	return builder
}

// 操作的文档版本，-1表示文档最新版本。若此时操作的版本为文档最新版本，则需要持有文档的阅读权限；若此时操作的版本为文档的历史版本，则需要持有文档的编辑权限。
//
// 示例值：-1
func (builder *PatchDocumentBlockReqBuilder) DocumentRevisionId(documentRevisionId int) *PatchDocumentBlockReqBuilder {
	builder.apiReq.QueryParams.Set("document_revision_id", fmt.Sprint(documentRevisionId))
	return builder
}

// 操作的唯一标识，与接口返回值的 client_token 相对应，用于幂等的进行更新操作。此值为空表示将发起一次新的请求，此值非空表示幂等的进行更新操作。
//
// 示例值：0e2633a3-aa1a-4171-af9e-0768ff863566
func (builder *PatchDocumentBlockReqBuilder) ClientToken(clientToken string) *PatchDocumentBlockReqBuilder {
	builder.apiReq.QueryParams.Set("client_token", fmt.Sprint(clientToken))
	return builder
}

// 此次调用中使用的用户ID的类型
//
// 示例值：
func (builder *PatchDocumentBlockReqBuilder) UserIdType(userIdType string) *PatchDocumentBlockReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

// 更新指定的块。
func (builder *PatchDocumentBlockReqBuilder) UpdateBlockRequest(updateBlockRequest *UpdateBlockRequest) *PatchDocumentBlockReqBuilder {
	builder.updateBlockRequest = updateBlockRequest
	return builder
}

func (builder *PatchDocumentBlockReqBuilder) Build() *PatchDocumentBlockReq {
	req := &PatchDocumentBlockReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.updateBlockRequest
	return req
}

type PatchDocumentBlockReq struct {
	apiReq             *larkcore.ApiReq
	UpdateBlockRequest *UpdateBlockRequest `body:""`
}

type PatchDocumentBlockRespData struct {
	Block              *Block  `json:"block,omitempty"`                // 更新后的 block 信息
	DocumentRevisionId *int    `json:"document_revision_id,omitempty"` // 当前更新成功后文档的版本号
	ClientToken        *string `json:"client_token,omitempty"`         // 操作的唯一标识，更新请求中使用此值表示幂等的进行此次更新
}

type PatchDocumentBlockResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *PatchDocumentBlockRespData `json:"data"` // 业务数据
}

func (resp *PatchDocumentBlockResp) Success() bool {
	return resp.Code == 0
}

type BatchDeleteDocumentBlockChildrenReqBodyBuilder struct {
	startIndex     int // 删除的起始索引（操作区间左闭右开）
	startIndexFlag bool
	endIndex       int // 删除的末尾索引（操作区间左闭右开）
	endIndexFlag   bool
}

func NewBatchDeleteDocumentBlockChildrenReqBodyBuilder() *BatchDeleteDocumentBlockChildrenReqBodyBuilder {
	builder := &BatchDeleteDocumentBlockChildrenReqBodyBuilder{}
	return builder
}

// 删除的起始索引（操作区间左闭右开）
//
//示例值：0
func (builder *BatchDeleteDocumentBlockChildrenReqBodyBuilder) StartIndex(startIndex int) *BatchDeleteDocumentBlockChildrenReqBodyBuilder {
	builder.startIndex = startIndex
	builder.startIndexFlag = true
	return builder
}

// 删除的末尾索引（操作区间左闭右开）
//
//示例值：1
func (builder *BatchDeleteDocumentBlockChildrenReqBodyBuilder) EndIndex(endIndex int) *BatchDeleteDocumentBlockChildrenReqBodyBuilder {
	builder.endIndex = endIndex
	builder.endIndexFlag = true
	return builder
}

func (builder *BatchDeleteDocumentBlockChildrenReqBodyBuilder) Build() *BatchDeleteDocumentBlockChildrenReqBody {
	req := &BatchDeleteDocumentBlockChildrenReqBody{}
	if builder.startIndexFlag {
		req.StartIndex = &builder.startIndex
	}
	if builder.endIndexFlag {
		req.EndIndex = &builder.endIndex
	}
	return req
}

type BatchDeleteDocumentBlockChildrenPathReqBodyBuilder struct {
	startIndex     int // 删除的起始索引（操作区间左闭右开）
	startIndexFlag bool
	endIndex       int // 删除的末尾索引（操作区间左闭右开）
	endIndexFlag   bool
}

func NewBatchDeleteDocumentBlockChildrenPathReqBodyBuilder() *BatchDeleteDocumentBlockChildrenPathReqBodyBuilder {
	builder := &BatchDeleteDocumentBlockChildrenPathReqBodyBuilder{}
	return builder
}

// 删除的起始索引（操作区间左闭右开）
//
// 示例值：0
func (builder *BatchDeleteDocumentBlockChildrenPathReqBodyBuilder) StartIndex(startIndex int) *BatchDeleteDocumentBlockChildrenPathReqBodyBuilder {
	builder.startIndex = startIndex
	builder.startIndexFlag = true
	return builder
}

// 删除的末尾索引（操作区间左闭右开）
//
// 示例值：1
func (builder *BatchDeleteDocumentBlockChildrenPathReqBodyBuilder) EndIndex(endIndex int) *BatchDeleteDocumentBlockChildrenPathReqBodyBuilder {
	builder.endIndex = endIndex
	builder.endIndexFlag = true
	return builder
}

func (builder *BatchDeleteDocumentBlockChildrenPathReqBodyBuilder) Build() (*BatchDeleteDocumentBlockChildrenReqBody, error) {
	req := &BatchDeleteDocumentBlockChildrenReqBody{}
	if builder.startIndexFlag {
		req.StartIndex = &builder.startIndex
	}
	if builder.endIndexFlag {
		req.EndIndex = &builder.endIndex
	}
	return req, nil
}

type BatchDeleteDocumentBlockChildrenReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *BatchDeleteDocumentBlockChildrenReqBody
}

func NewBatchDeleteDocumentBlockChildrenReqBuilder() *BatchDeleteDocumentBlockChildrenReqBuilder {
	builder := &BatchDeleteDocumentBlockChildrenReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 文档的唯一标识
//
// 示例值：doxcnePuYufKa49ISjhD8Ih0ikh
func (builder *BatchDeleteDocumentBlockChildrenReqBuilder) DocumentId(documentId string) *BatchDeleteDocumentBlockChildrenReqBuilder {
	builder.apiReq.PathParams.Set("document_id", fmt.Sprint(documentId))
	return builder
}

// 父 Block 的唯一标识
//
// 示例值：doxcnO6UW6wAw2qIcYf4hZpFIth
func (builder *BatchDeleteDocumentBlockChildrenReqBuilder) BlockId(blockId string) *BatchDeleteDocumentBlockChildrenReqBuilder {
	builder.apiReq.PathParams.Set("block_id", fmt.Sprint(blockId))
	return builder
}

// 操作的文档版本，-1表示文档最新版本。若此时操作的版本为文档最新版本，则需要持有文档的阅读权限；若此时操作的版本为文档的历史版本，则需要持有文档的编辑权限。
//
// 示例值：-1
func (builder *BatchDeleteDocumentBlockChildrenReqBuilder) DocumentRevisionId(documentRevisionId int) *BatchDeleteDocumentBlockChildrenReqBuilder {
	builder.apiReq.QueryParams.Set("document_revision_id", fmt.Sprint(documentRevisionId))
	return builder
}

// 操作的唯一标识，与接口返回值的 client_token 相对应，用于幂等的进行更新操作。此值为空表示将发起一次新的请求，此值非空表示幂等的进行更新操作。
//
// 示例值：fe599b60-450f-46ff-b2ef-9f6675625b97
func (builder *BatchDeleteDocumentBlockChildrenReqBuilder) ClientToken(clientToken string) *BatchDeleteDocumentBlockChildrenReqBuilder {
	builder.apiReq.QueryParams.Set("client_token", fmt.Sprint(clientToken))
	return builder
}

// 指定需要操作的块，删除其指定范围的子块。如果操作成功，接口将返回应用删除操作后的文档版本号。
func (builder *BatchDeleteDocumentBlockChildrenReqBuilder) Body(body *BatchDeleteDocumentBlockChildrenReqBody) *BatchDeleteDocumentBlockChildrenReqBuilder {
	builder.body = body
	return builder
}

func (builder *BatchDeleteDocumentBlockChildrenReqBuilder) Build() *BatchDeleteDocumentBlockChildrenReq {
	req := &BatchDeleteDocumentBlockChildrenReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.body
	return req
}

type BatchDeleteDocumentBlockChildrenReqBody struct {
	StartIndex *int `json:"start_index,omitempty"` // 删除的起始索引（操作区间左闭右开）
	EndIndex   *int `json:"end_index,omitempty"`   // 删除的末尾索引（操作区间左闭右开）
}

type BatchDeleteDocumentBlockChildrenReq struct {
	apiReq *larkcore.ApiReq
	Body   *BatchDeleteDocumentBlockChildrenReqBody `body:""`
}

type BatchDeleteDocumentBlockChildrenRespData struct {
	DocumentRevisionId *int    `json:"document_revision_id,omitempty"` // 当前删除操作成功后文档的版本号
	ClientToken        *string `json:"client_token,omitempty"`         // 操作的唯一标识，更新请求中使用此值表示幂等的进行此次更新
}

type BatchDeleteDocumentBlockChildrenResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *BatchDeleteDocumentBlockChildrenRespData `json:"data"` // 业务数据
}

func (resp *BatchDeleteDocumentBlockChildrenResp) Success() bool {
	return resp.Code == 0
}

type CreateDocumentBlockChildrenReqBodyBuilder struct {
	children     []*Block // 添加的孩子列表。
	childrenFlag bool
	index        int // 当前 block 在 children 中的插入位置，起始值为 0，最大值为原 children 长度
	indexFlag    bool
}

func NewCreateDocumentBlockChildrenReqBodyBuilder() *CreateDocumentBlockChildrenReqBodyBuilder {
	builder := &CreateDocumentBlockChildrenReqBodyBuilder{}
	return builder
}

// 添加的孩子列表。
//
//示例值：
func (builder *CreateDocumentBlockChildrenReqBodyBuilder) Children(children []*Block) *CreateDocumentBlockChildrenReqBodyBuilder {
	builder.children = children
	builder.childrenFlag = true
	return builder
}

// 当前 block 在 children 中的插入位置，起始值为 0，最大值为原 children 长度
//
//示例值：0
func (builder *CreateDocumentBlockChildrenReqBodyBuilder) Index(index int) *CreateDocumentBlockChildrenReqBodyBuilder {
	builder.index = index
	builder.indexFlag = true
	return builder
}

func (builder *CreateDocumentBlockChildrenReqBodyBuilder) Build() *CreateDocumentBlockChildrenReqBody {
	req := &CreateDocumentBlockChildrenReqBody{}
	if builder.childrenFlag {
		req.Children = builder.children
	}
	if builder.indexFlag {
		req.Index = &builder.index
	}
	return req
}

type CreateDocumentBlockChildrenPathReqBodyBuilder struct {
	children     []*Block // 添加的孩子列表。
	childrenFlag bool
	index        int // 当前 block 在 children 中的插入位置，起始值为 0，最大值为原 children 长度
	indexFlag    bool
}

func NewCreateDocumentBlockChildrenPathReqBodyBuilder() *CreateDocumentBlockChildrenPathReqBodyBuilder {
	builder := &CreateDocumentBlockChildrenPathReqBodyBuilder{}
	return builder
}

// 添加的孩子列表。
//
// 示例值：
func (builder *CreateDocumentBlockChildrenPathReqBodyBuilder) Children(children []*Block) *CreateDocumentBlockChildrenPathReqBodyBuilder {
	builder.children = children
	builder.childrenFlag = true
	return builder
}

// 当前 block 在 children 中的插入位置，起始值为 0，最大值为原 children 长度
//
// 示例值：0
func (builder *CreateDocumentBlockChildrenPathReqBodyBuilder) Index(index int) *CreateDocumentBlockChildrenPathReqBodyBuilder {
	builder.index = index
	builder.indexFlag = true
	return builder
}

func (builder *CreateDocumentBlockChildrenPathReqBodyBuilder) Build() (*CreateDocumentBlockChildrenReqBody, error) {
	req := &CreateDocumentBlockChildrenReqBody{}
	if builder.childrenFlag {
		req.Children = builder.children
	}
	if builder.indexFlag {
		req.Index = &builder.index
	}
	return req, nil
}

type CreateDocumentBlockChildrenReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *CreateDocumentBlockChildrenReqBody
}

func NewCreateDocumentBlockChildrenReqBuilder() *CreateDocumentBlockChildrenReqBuilder {
	builder := &CreateDocumentBlockChildrenReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 文档的唯一标识
//
// 示例值：doxcnePuYufKa49ISjhD8Ih0ikh
func (builder *CreateDocumentBlockChildrenReqBuilder) DocumentId(documentId string) *CreateDocumentBlockChildrenReqBuilder {
	builder.apiReq.PathParams.Set("document_id", fmt.Sprint(documentId))
	return builder
}

// Block 的唯一标识
//
// 示例值：doxcnO6UW6wAw2qIcYf4hZpFIth
func (builder *CreateDocumentBlockChildrenReqBuilder) BlockId(blockId string) *CreateDocumentBlockChildrenReqBuilder {
	builder.apiReq.PathParams.Set("block_id", fmt.Sprint(blockId))
	return builder
}

// 操作的文档版本，-1表示文档最新版本。若此时操作的版本为文档最新版本，则需要持有文档的阅读权限；若此时操作的版本为文档的历史版本，则需要持有文档的编辑权限。
//
// 示例值：-1
func (builder *CreateDocumentBlockChildrenReqBuilder) DocumentRevisionId(documentRevisionId int) *CreateDocumentBlockChildrenReqBuilder {
	builder.apiReq.QueryParams.Set("document_revision_id", fmt.Sprint(documentRevisionId))
	return builder
}

// 操作的唯一标识，与接口返回值的 client_token 相对应，用于幂等的进行更新操作。此值为空表示将发起一次新的请求，此值非空表示幂等的进行更新操作。
//
// 示例值：fe599b60-450f-46ff-b2ef-9f6675625b97
func (builder *CreateDocumentBlockChildrenReqBuilder) ClientToken(clientToken string) *CreateDocumentBlockChildrenReqBuilder {
	builder.apiReq.QueryParams.Set("client_token", fmt.Sprint(clientToken))
	return builder
}

// 此次调用中使用的用户ID的类型
//
// 示例值：
func (builder *CreateDocumentBlockChildrenReqBuilder) UserIdType(userIdType string) *CreateDocumentBlockChildrenReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

// 指定需要操作的块，为其创建一批子块，并插入到指定位置。如果操作成功，接口将返回新创建子块的富文本内容。
func (builder *CreateDocumentBlockChildrenReqBuilder) Body(body *CreateDocumentBlockChildrenReqBody) *CreateDocumentBlockChildrenReqBuilder {
	builder.body = body
	return builder
}

func (builder *CreateDocumentBlockChildrenReqBuilder) Build() *CreateDocumentBlockChildrenReq {
	req := &CreateDocumentBlockChildrenReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.body
	return req
}

type CreateDocumentBlockChildrenReqBody struct {
	Children []*Block `json:"children,omitempty"` // 添加的孩子列表。
	Index    *int     `json:"index,omitempty"`    // 当前 block 在 children 中的插入位置，起始值为 0，最大值为原 children 长度
}

type CreateDocumentBlockChildrenReq struct {
	apiReq *larkcore.ApiReq
	Body   *CreateDocumentBlockChildrenReqBody `body:""`
}

type CreateDocumentBlockChildrenRespData struct {
	Children           []*Block `json:"children,omitempty"`             // 所添加的孩子的 Block 信息
	DocumentRevisionId *int     `json:"document_revision_id,omitempty"` // 当前 block children 创建成功后文档的版本号
	ClientToken        *string  `json:"client_token,omitempty"`         // 操作的唯一标识，更新请求中使用此值表示幂等的进行此次更新
}

type CreateDocumentBlockChildrenResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *CreateDocumentBlockChildrenRespData `json:"data"` // 业务数据
}

func (resp *CreateDocumentBlockChildrenResp) Success() bool {
	return resp.Code == 0
}

type GetDocumentBlockChildrenReqBuilder struct {
	apiReq *larkcore.ApiReq
	limit  int // 最大返回多少记录，当使用迭代器访问时才有效
}

func NewGetDocumentBlockChildrenReqBuilder() *GetDocumentBlockChildrenReqBuilder {
	builder := &GetDocumentBlockChildrenReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 最大返回多少记录，当使用迭代器访问时才有效
func (builder *GetDocumentBlockChildrenReqBuilder) Limit(limit int) *GetDocumentBlockChildrenReqBuilder {
	builder.limit = limit
	return builder
}

// 文档的唯一标识
//
// 示例值：doxcnePuYufKa49ISjhD8Ih0ikh
func (builder *GetDocumentBlockChildrenReqBuilder) DocumentId(documentId string) *GetDocumentBlockChildrenReqBuilder {
	builder.apiReq.PathParams.Set("document_id", fmt.Sprint(documentId))
	return builder
}

// Block 的唯一标识
//
// 示例值：doxcnO6UW6wAw2qIcYf4hZpFIth
func (builder *GetDocumentBlockChildrenReqBuilder) BlockId(blockId string) *GetDocumentBlockChildrenReqBuilder {
	builder.apiReq.PathParams.Set("block_id", fmt.Sprint(blockId))
	return builder
}

// 操作的文档版本，-1表示文档最新版本。若此时操作的版本为文档最新版本，则需要持有文档的阅读权限；若此时操作的版本为文档的历史版本，则需要持有文档的编辑权限。
//
// 示例值：-1
func (builder *GetDocumentBlockChildrenReqBuilder) DocumentRevisionId(documentRevisionId int) *GetDocumentBlockChildrenReqBuilder {
	builder.apiReq.QueryParams.Set("document_revision_id", fmt.Sprint(documentRevisionId))
	return builder
}

// 分页标记，第一次请求不填，表示从头开始遍历；分页查询结果还有更多项时会同时返回新的 page_token，下次遍历可采用该 page_token 获取查询结果
//
// 示例值：aw7DoMKBFMOGwqHCrcO8w6jCmMOvw6ILeADCvsKNw57Di8O5XGV3LG4_w5HCqhFxSnDCrCzCn0BgZcOYUg85EMOYcEAcwqYOw4ojw5QFwofCu8KoIMO3K8Ktw4IuNMOBBHNYw4bCgCV3U1zDu8K-J8KSR8Kgw7Y0fsKZdsKvW3d9w53DnkHDrcO5bDkYwrvDisOEPcOtVFJ-I03CnsOILMOoAmLDknd6dsKqG1bClAjDuS3CvcOTwo7Dg8OrwovDsRdqIcKxw5HDohTDtXN9w5rCkWo
func (builder *GetDocumentBlockChildrenReqBuilder) PageToken(pageToken string) *GetDocumentBlockChildrenReqBuilder {
	builder.apiReq.QueryParams.Set("page_token", fmt.Sprint(pageToken))
	return builder
}

// 分页大小
//
// 示例值：500
func (builder *GetDocumentBlockChildrenReqBuilder) PageSize(pageSize int) *GetDocumentBlockChildrenReqBuilder {
	builder.apiReq.QueryParams.Set("page_size", fmt.Sprint(pageSize))
	return builder
}

// 此次调用中使用的用户ID的类型
//
// 示例值：
func (builder *GetDocumentBlockChildrenReqBuilder) UserIdType(userIdType string) *GetDocumentBlockChildrenReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

func (builder *GetDocumentBlockChildrenReqBuilder) Build() *GetDocumentBlockChildrenReq {
	req := &GetDocumentBlockChildrenReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.Limit = builder.limit
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type GetDocumentBlockChildrenReq struct {
	apiReq *larkcore.ApiReq
	Limit  int // 最多返回多少记录，只有在使用迭代器访问时，才有效

}

type GetDocumentBlockChildrenRespData struct {
	Items     []*Block `json:"items,omitempty"`      // block 的 children 列表
	PageToken *string  `json:"page_token,omitempty"` // 下一个分页的分页标记
	HasMore   *bool    `json:"has_more,omitempty"`   // 是否还有下一个分页
}

type GetDocumentBlockChildrenResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *GetDocumentBlockChildrenRespData `json:"data"` // 业务数据
}

func (resp *GetDocumentBlockChildrenResp) Success() bool {
	return resp.Code == 0
}

type ListDocumentBlockIterator struct {
	nextPageToken *string
	items         []*Block
	index         int
	limit         int
	ctx           context.Context
	req           *ListDocumentBlockReq
	listFunc      func(ctx context.Context, req *ListDocumentBlockReq, options ...larkcore.RequestOptionFunc) (*ListDocumentBlockResp, error)
	options       []larkcore.RequestOptionFunc
	curlNum       int
}

func (iterator *ListDocumentBlockIterator) Next() (bool, *Block, error) {
	// 达到最大量，则返回
	if iterator.limit > 0 && iterator.curlNum >= iterator.limit {
		return false, nil, nil
	}

	// 为0则拉取数据
	if iterator.index == 0 || iterator.index >= len(iterator.items) {
		if iterator.index != 0 && iterator.nextPageToken == nil {
			return false, nil, nil
		}
		if iterator.nextPageToken != nil {
			iterator.req.apiReq.QueryParams.Set("page_token", *iterator.nextPageToken)
		}
		resp, err := iterator.listFunc(iterator.ctx, iterator.req, iterator.options...)
		if err != nil {
			return false, nil, err
		}

		if resp.Code != 0 {
			return false, nil, errors.New(fmt.Sprintf("Code:%d,Msg:%s", resp.Code, resp.Msg))
		}

		if len(resp.Data.Items) == 0 {
			return false, nil, nil
		}

		iterator.nextPageToken = resp.Data.PageToken
		iterator.items = resp.Data.Items
		iterator.index = 0
	}

	block := iterator.items[iterator.index]
	iterator.index++
	iterator.curlNum++
	return true, block, nil
}

func (iterator *ListDocumentBlockIterator) NextPageToken() *string {
	return iterator.nextPageToken
}

type GetDocumentBlockChildrenIterator struct {
	nextPageToken *string
	items         []*Block
	index         int
	limit         int
	ctx           context.Context
	req           *GetDocumentBlockChildrenReq
	listFunc      func(ctx context.Context, req *GetDocumentBlockChildrenReq, options ...larkcore.RequestOptionFunc) (*GetDocumentBlockChildrenResp, error)
	options       []larkcore.RequestOptionFunc
	curlNum       int
}

func (iterator *GetDocumentBlockChildrenIterator) Next() (bool, *Block, error) {
	// 达到最大量，则返回
	if iterator.limit > 0 && iterator.curlNum >= iterator.limit {
		return false, nil, nil
	}

	// 为0则拉取数据
	if iterator.index == 0 || iterator.index >= len(iterator.items) {
		if iterator.index != 0 && iterator.nextPageToken == nil {
			return false, nil, nil
		}
		if iterator.nextPageToken != nil {
			iterator.req.apiReq.QueryParams.Set("page_token", *iterator.nextPageToken)
		}
		resp, err := iterator.listFunc(iterator.ctx, iterator.req, iterator.options...)
		if err != nil {
			return false, nil, err
		}

		if resp.Code != 0 {
			return false, nil, errors.New(fmt.Sprintf("Code:%d,Msg:%s", resp.Code, resp.Msg))
		}

		if len(resp.Data.Items) == 0 {
			return false, nil, nil
		}

		iterator.nextPageToken = resp.Data.PageToken
		iterator.items = resp.Data.Items
		iterator.index = 0
	}

	block := iterator.items[iterator.index]
	iterator.index++
	iterator.curlNum++
	return true, block, nil
}

func (iterator *GetDocumentBlockChildrenIterator) NextPageToken() *string {
	return iterator.nextPageToken
}
