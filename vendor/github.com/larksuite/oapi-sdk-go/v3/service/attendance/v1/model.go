// Package attendance code generated by oapi sdk gen
/*
 * MIT License
 *
 * Copyright (c) 2022 Lark Technologies Pte. Ltd.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice, shall be included in all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */

package larkattendance

import (
	"io"

	"bytes"

	"io/ioutil"

	"fmt"

	"context"
	"errors"

	"github.com/larksuite/oapi-sdk-go/v3/core"
)

const (
	EmployeeTypeEmployeeId = "employee_id" // 员工employeeId
	EmployeeTypeEmployeeNo = "employee_no" // 员工工号
)

const (
	DeptTypeOpenId = "open_id" // 开放openID
)

const (
	EmployeeTypeGetGroupEmployeeId = "employee_id" // 员工employeeId
	EmployeeTypeGetGroupEmployeeNo = "employee_no" // 员工工号
)

const (
	DeptTypeGetGroupOpenId = "open_id" // 开放openID
)

const (
	EmployeeTypeCreateUserApprovalEmployeeId = "employee_id" // 员工employeeId
	EmployeeTypeCreateUserApprovalEmployeeNo = "employee_no" // 员工工号
)

const (
	CheckDateTypePeriodTime = "PeriodTime" // 单据作用时间（即写入的end_time）
	CheckDateTypeCreateTime = "CreateTime" // 单据创建时间
	CheckDateTypeUpdateTime = "UpdateTime" // 单据状态更新时间
)

const (
	ApprovalStatusTodo     = 0 // 待审批
	ApprovalStatusRejected = 1 // 审批未通过
	ApprovalStatusApproved = 2 // 审批通过
	ApprovalStatusCanceled = 3 // 审批已取消
	ApprovalStatusReverted = 4 // 已撤回

)

const (
	EmployeeTypeQueryUserApprovalEmployeeId = "employee_id" // 员工employeeId
	EmployeeTypeQueryUserApprovalEmployeeNo = "employee_no" // 员工工号
)

const (
	EmployeeTypeBatchCreateUserDailyShiftEmployeeId = "employee_id" // 员工employeeId
	EmployeeTypeBatchCreateUserDailyShiftEmployeeNo = "employee_no" // 员工工号
)

const (
	EmployeeTypeQueryUserDailyShiftEmployeeId = "employee_id" // 员工employeeId
	EmployeeTypeQueryUserDailyShiftEmployeeNo = "employee_no" // 员工工号
)

const (
	EmployeeTypeBatchCreateUserFlowEmployeeId = "employee_id" // 员工employeeId
	EmployeeTypeBatchCreateUserFlowEmployeeNo = "employee_no" // 员工工号
)

const (
	EmployeeTypeGetUserFlowOpenId     = "open_id"     // 开放openID
	EmployeeTypeGetUserFlowEmployeeId = "employee_id" // 员工employeeId
	EmployeeTypeGetUserFlowEmployeeNo = "employee_no" // 员工工号
)

const (
	EmployeeTypeQueryUserFlowEmployeeId = "employee_id" // 员工employeeId
	EmployeeTypeQueryUserFlowEmployeeNo = "employee_no" // 员工工号
)

const (
	EmployeeTypeModifyUserSettingEmployeeId = "employee_id" // 员工employeeId
	EmployeeTypeModifyUserSettingEmployeeNo = "employee_no" // 员工工号
)

const (
	EmployeeTypeQueryUserSettingEmployeeId = "employee_id" // 员工employeeId
	EmployeeTypeQueryUserSettingEmployeeNo = "employee_no" // 员工工号
)

const (
	LocaleEn = "en" // 英文
	LocaleJa = "ja" // 日文
	LocaleZh = "zh" // 中文
)

const (
	StatsTypeDaily = "daily" // 日度统计
	StatsTypeMonth = "month" // 月度统计
)

const (
	EmployeeTypeQueryUserStatsDataEmployeeId = "employee_id" // 员工employeeId
	EmployeeTypeQueryUserStatsDataEmployeeNo = "employee_no" // 员工工号
)

const (
	LocaleQueryUserStatsFieldEn = "en" // 英文
	LocaleQueryUserStatsFieldJa = "ja" // 日文
	LocaleQueryUserStatsFieldZh = "zh" // 中文
)

const (
	StatsTypeQueryUserStatsFieldDaily = "daily" // 日度统计
	StatsTypeQueryUserStatsFieldMonth = "month" // 月度统计
)

const (
	EmployeeTypeQueryUserStatsFieldEmployeeId = "employee_id" // 员工employeeId
	EmployeeTypeQueryUserStatsFieldEmployeeNo = "employee_no" // 员工工号
)

const (
	LocaleQueryUserStatsViewEn = "en" // 英文
	LocaleQueryUserStatsViewJa = "ja" // 日文
	LocaleQueryUserStatsViewZh = "zh" // 中文
)

const (
	StatsTypeQueryUserStatsViewDaily = "daily" // 日度统计
	StatsTypeQueryUserStatsViewMonth = "month" // 月度统计
)

const (
	EmployeeTypeQueryUserStatsViewEmployeeId = "employee_id" // 员工employeeId
	EmployeeTypeQueryUserStatsViewEmployeeNo = "employee_no" // 员工工号
)

const (
	EmployeeTypeUpdateUserStatsViewEmployeeId = "employee_id" // 员工employeeId
	EmployeeTypeUpdateUserStatsViewEmployeeNo = "employee_no" // 员工工号
)

const (
	EmployeeTypeQueryUserTaskEmployeeId = "employee_id" // 员工employeeId
	EmployeeTypeQueryUserTaskEmployeeNo = "employee_no" // 员工工号
)

const (
	EmployeeTypeCreateUserTaskRemedyEmployeeId = "employee_id" // 员工employeeId
	EmployeeTypeCreateUserTaskRemedyEmployeeNo = "employee_no" // 员工工号
)

const (
	CheckDateTypeQueryUserTaskRemedyPeriodTime = "PeriodTime" // 单据作用时间（即remedy_time）
	CheckDateTypeQueryUserTaskRemedyCreateTime = "CreateTime" // 单据创建时间
	CheckDateTypeQueryUserTaskRemedyUpdateTime = "UpdateTime" // 单据状态更新时间
)

const (
	RemedyStatusPending  = 0 // 待审批
	RemedyStatusRejected = 1 // 未通过
	RemedyStatusPass     = 2 // 已通过
	RemedyStatusCancel   = 3 // 已取消
	RemedyStatusWithdraw = 4 // 已撤回

)

const (
	EmployeeTypeQueryUserTaskRemedyEmployeeId = "employee_id" // 员工employeeId
	EmployeeTypeQueryUserTaskRemedyEmployeeNo = "employee_no" // 员工工号
)

const (
	EmployeeTypeQueryUserAllowedRemedysUserTaskRemedyEmployeeId = "employee_id" // 员工employeeId
	EmployeeTypeQueryUserAllowedRemedysUserTaskRemedyEmployeeNo = "employee_no" // 员工工号
)

type ApprovalInfo struct {
	ApprovalId   *string `json:"approval_id,omitempty"`   // 审批实例 ID
	ApprovalType *string `json:"approval_type,omitempty"` // 审批类型
	Status       *int    `json:"status,omitempty"`        // 审批状态
}

type ApprovalInfoBuilder struct {
	approvalId       string // 审批实例 ID
	approvalIdFlag   bool
	approvalType     string // 审批类型
	approvalTypeFlag bool
	status           int // 审批状态
	statusFlag       bool
}

func NewApprovalInfoBuilder() *ApprovalInfoBuilder {
	builder := &ApprovalInfoBuilder{}
	return builder
}

// 审批实例 ID
//
// 示例值：6737202939523236113
func (builder *ApprovalInfoBuilder) ApprovalId(approvalId string) *ApprovalInfoBuilder {
	builder.approvalId = approvalId
	builder.approvalIdFlag = true
	return builder
}

// 审批类型
//
// 示例值：remedy
func (builder *ApprovalInfoBuilder) ApprovalType(approvalType string) *ApprovalInfoBuilder {
	builder.approvalType = approvalType
	builder.approvalTypeFlag = true
	return builder
}

// 审批状态
//
// 示例值：0
func (builder *ApprovalInfoBuilder) Status(status int) *ApprovalInfoBuilder {
	builder.status = status
	builder.statusFlag = true
	return builder
}

func (builder *ApprovalInfoBuilder) Build() *ApprovalInfo {
	req := &ApprovalInfo{}
	if builder.approvalIdFlag {
		req.ApprovalId = &builder.approvalId

	}
	if builder.approvalTypeFlag {
		req.ApprovalType = &builder.approvalType

	}
	if builder.statusFlag {
		req.Status = &builder.status

	}
	return req
}

type Area struct {
	Type   *string       `json:"type,omitempty"`   // 区域类型
	Center *Coordinate   `json:"center,omitempty"` // 中心点
	Radius *string       `json:"radius,omitempty"` // 半径
	Coords []*Coordinate `json:"coords,omitempty"` //
}

type AreaBuilder struct {
	type_      string // 区域类型
	typeFlag   bool
	center     *Coordinate // 中心点
	centerFlag bool
	radius     string // 半径
	radiusFlag bool
	coords     []*Coordinate //
	coordsFlag bool
}

func NewAreaBuilder() *AreaBuilder {
	builder := &AreaBuilder{}
	return builder
}

// 区域类型
//
// 示例值：
func (builder *AreaBuilder) Type(type_ string) *AreaBuilder {
	builder.type_ = type_
	builder.typeFlag = true
	return builder
}

// 中心点
//
// 示例值：
func (builder *AreaBuilder) Center(center *Coordinate) *AreaBuilder {
	builder.center = center
	builder.centerFlag = true
	return builder
}

// 半径
//
// 示例值：
func (builder *AreaBuilder) Radius(radius string) *AreaBuilder {
	builder.radius = radius
	builder.radiusFlag = true
	return builder
}

//
//
// 示例值：
func (builder *AreaBuilder) Coords(coords []*Coordinate) *AreaBuilder {
	builder.coords = coords
	builder.coordsFlag = true
	return builder
}

func (builder *AreaBuilder) Build() *Area {
	req := &Area{}
	if builder.typeFlag {
		req.Type = &builder.type_

	}
	if builder.centerFlag {
		req.Center = builder.center
	}
	if builder.radiusFlag {
		req.Radius = &builder.radius

	}
	if builder.coordsFlag {
		req.Coords = builder.coords
	}
	return req
}

type ChildField struct {
	Code     *string `json:"code,omitempty"`      // 子字段编号
	Title    *string `json:"title,omitempty"`     // 子字段名称
	TimeUnit *string `json:"time_unit,omitempty"` // 时间单位
}

type ChildFieldBuilder struct {
	code         string // 子字段编号
	codeFlag     bool
	title        string // 子字段名称
	titleFlag    bool
	timeUnit     string // 时间单位
	timeUnitFlag bool
}

func NewChildFieldBuilder() *ChildFieldBuilder {
	builder := &ChildFieldBuilder{}
	return builder
}

// 子字段编号
//
// 示例值：50121
func (builder *ChildFieldBuilder) Code(code string) *ChildFieldBuilder {
	builder.code = code
	builder.codeFlag = true
	return builder
}

// 子字段名称
//
// 示例值：工号
func (builder *ChildFieldBuilder) Title(title string) *ChildFieldBuilder {
	builder.title = title
	builder.titleFlag = true
	return builder
}

// 时间单位
//
// 示例值：
func (builder *ChildFieldBuilder) TimeUnit(timeUnit string) *ChildFieldBuilder {
	builder.timeUnit = timeUnit
	builder.timeUnitFlag = true
	return builder
}

func (builder *ChildFieldBuilder) Build() *ChildField {
	req := &ChildField{}
	if builder.codeFlag {
		req.Code = &builder.code

	}
	if builder.titleFlag {
		req.Title = &builder.title

	}
	if builder.timeUnitFlag {
		req.TimeUnit = &builder.timeUnit

	}
	return req
}

type ChildItem struct {
	Code       *string `json:"code,omitempty"`        // 子标题编号
	Value      *string `json:"value,omitempty"`       // 开关字段，0：关闭，1：开启（非开关字段场景：code = 51501 可选值为1-6）
	Title      *string `json:"title,omitempty"`       // 子标题名称
	ColumnType *int    `json:"column_type,omitempty"` // 列类型
	ReadOnly   *bool   `json:"read_only,omitempty"`   // 是否只读
	MinValue   *string `json:"min_value,omitempty"`   // 最小值
	MaxValue   *string `json:"max_value,omitempty"`   // 最大值
}

type ChildItemBuilder struct {
	code           string // 子标题编号
	codeFlag       bool
	value          string // 开关字段，0：关闭，1：开启（非开关字段场景：code = 51501 可选值为1-6）
	valueFlag      bool
	title          string // 子标题名称
	titleFlag      bool
	columnType     int // 列类型
	columnTypeFlag bool
	readOnly       bool // 是否只读
	readOnlyFlag   bool
	minValue       string // 最小值
	minValueFlag   bool
	maxValue       string // 最大值
	maxValueFlag   bool
}

func NewChildItemBuilder() *ChildItemBuilder {
	builder := &ChildItemBuilder{}
	return builder
}

// 子标题编号
//
// 示例值：50101
func (builder *ChildItemBuilder) Code(code string) *ChildItemBuilder {
	builder.code = code
	builder.codeFlag = true
	return builder
}

// 开关字段，0：关闭，1：开启（非开关字段场景：code = 51501 可选值为1-6）
//
// 示例值：0
func (builder *ChildItemBuilder) Value(value string) *ChildItemBuilder {
	builder.value = value
	builder.valueFlag = true
	return builder
}

// 子标题名称
//
// 示例值：工号
func (builder *ChildItemBuilder) Title(title string) *ChildItemBuilder {
	builder.title = title
	builder.titleFlag = true
	return builder
}

// 列类型
//
// 示例值：
func (builder *ChildItemBuilder) ColumnType(columnType int) *ChildItemBuilder {
	builder.columnType = columnType
	builder.columnTypeFlag = true
	return builder
}

// 是否只读
//
// 示例值：
func (builder *ChildItemBuilder) ReadOnly(readOnly bool) *ChildItemBuilder {
	builder.readOnly = readOnly
	builder.readOnlyFlag = true
	return builder
}

// 最小值
//
// 示例值：
func (builder *ChildItemBuilder) MinValue(minValue string) *ChildItemBuilder {
	builder.minValue = minValue
	builder.minValueFlag = true
	return builder
}

// 最大值
//
// 示例值：
func (builder *ChildItemBuilder) MaxValue(maxValue string) *ChildItemBuilder {
	builder.maxValue = maxValue
	builder.maxValueFlag = true
	return builder
}

func (builder *ChildItemBuilder) Build() *ChildItem {
	req := &ChildItem{}
	if builder.codeFlag {
		req.Code = &builder.code

	}
	if builder.valueFlag {
		req.Value = &builder.value

	}
	if builder.titleFlag {
		req.Title = &builder.title

	}
	if builder.columnTypeFlag {
		req.ColumnType = &builder.columnType

	}
	if builder.readOnlyFlag {
		req.ReadOnly = &builder.readOnly

	}
	if builder.minValueFlag {
		req.MinValue = &builder.minValue

	}
	if builder.maxValueFlag {
		req.MaxValue = &builder.maxValue

	}
	return req
}

type Coordinate struct {
	Longitude *float64 `json:"longitude,omitempty"` // 经度
	Latitude  *float64 `json:"latitude,omitempty"`  // 纬度
	Accuracy  *float64 `json:"accuracy,omitempty"`  // 精度
}

type CoordinateBuilder struct {
	longitude     float64 // 经度
	longitudeFlag bool
	latitude      float64 // 纬度
	latitudeFlag  bool
	accuracy      float64 // 精度
	accuracyFlag  bool
}

func NewCoordinateBuilder() *CoordinateBuilder {
	builder := &CoordinateBuilder{}
	return builder
}

// 经度
//
// 示例值：
func (builder *CoordinateBuilder) Longitude(longitude float64) *CoordinateBuilder {
	builder.longitude = longitude
	builder.longitudeFlag = true
	return builder
}

// 纬度
//
// 示例值：
func (builder *CoordinateBuilder) Latitude(latitude float64) *CoordinateBuilder {
	builder.latitude = latitude
	builder.latitudeFlag = true
	return builder
}

// 精度
//
// 示例值：
func (builder *CoordinateBuilder) Accuracy(accuracy float64) *CoordinateBuilder {
	builder.accuracy = accuracy
	builder.accuracyFlag = true
	return builder
}

func (builder *CoordinateBuilder) Build() *Coordinate {
	req := &Coordinate{}
	if builder.longitudeFlag {
		req.Longitude = &builder.longitude

	}
	if builder.latitudeFlag {
		req.Latitude = &builder.latitude

	}
	if builder.accuracyFlag {
		req.Accuracy = &builder.accuracy

	}
	return req
}

type Field struct {
	Code        *string       `json:"code,omitempty"`         // 字段编号
	Title       *string       `json:"title,omitempty"`        // 字段名称
	ChildFields []*ChildField `json:"child_fields,omitempty"` // 子字段列表
}

type FieldBuilder struct {
	code            string // 字段编号
	codeFlag        bool
	title           string // 字段名称
	titleFlag       bool
	childFields     []*ChildField // 子字段列表
	childFieldsFlag bool
}

func NewFieldBuilder() *FieldBuilder {
	builder := &FieldBuilder{}
	return builder
}

// 字段编号
//
// 示例值：50121
func (builder *FieldBuilder) Code(code string) *FieldBuilder {
	builder.code = code
	builder.codeFlag = true
	return builder
}

// 字段名称
//
// 示例值：工号
func (builder *FieldBuilder) Title(title string) *FieldBuilder {
	builder.title = title
	builder.titleFlag = true
	return builder
}

// 子字段列表
//
// 示例值：
func (builder *FieldBuilder) ChildFields(childFields []*ChildField) *FieldBuilder {
	builder.childFields = childFields
	builder.childFieldsFlag = true
	return builder
}

func (builder *FieldBuilder) Build() *Field {
	req := &Field{}
	if builder.codeFlag {
		req.Code = &builder.code

	}
	if builder.titleFlag {
		req.Title = &builder.title

	}
	if builder.childFieldsFlag {
		req.ChildFields = builder.childFields
	}
	return req
}

type File struct {
	FileId *string `json:"file_id,omitempty"` // 文件 ID
}

type FileBuilder struct {
	fileId     string // 文件 ID
	fileIdFlag bool
}

func NewFileBuilder() *FileBuilder {
	builder := &FileBuilder{}
	return builder
}

// 文件 ID
//
// 示例值：6b30e7636a38861bbe02869c726a4612
func (builder *FileBuilder) FileId(fileId string) *FileBuilder {
	builder.fileId = fileId
	builder.fileIdFlag = true
	return builder
}

func (builder *FileBuilder) Build() *File {
	req := &File{}
	if builder.fileIdFlag {
		req.FileId = &builder.fileId

	}
	return req
}

type FlexibleRule struct {
	FlexibleEarlyMinutes *int `json:"flexible_early_minutes,omitempty"` // 下班最多可早走（上班早到几分钟，下班可早走几分钟）
	FlexibleLateMinutes  *int `json:"flexible_late_minutes,omitempty"`  // 上班最多可晚到（上班晚到几分钟，下班须晚走几分钟）
}

type FlexibleRuleBuilder struct {
	flexibleEarlyMinutes     int // 下班最多可早走（上班早到几分钟，下班可早走几分钟）
	flexibleEarlyMinutesFlag bool
	flexibleLateMinutes      int // 上班最多可晚到（上班晚到几分钟，下班须晚走几分钟）
	flexibleLateMinutesFlag  bool
}

func NewFlexibleRuleBuilder() *FlexibleRuleBuilder {
	builder := &FlexibleRuleBuilder{}
	return builder
}

// 下班最多可早走（上班早到几分钟，下班可早走几分钟）
//
// 示例值：60
func (builder *FlexibleRuleBuilder) FlexibleEarlyMinutes(flexibleEarlyMinutes int) *FlexibleRuleBuilder {
	builder.flexibleEarlyMinutes = flexibleEarlyMinutes
	builder.flexibleEarlyMinutesFlag = true
	return builder
}

// 上班最多可晚到（上班晚到几分钟，下班须晚走几分钟）
//
// 示例值：60
func (builder *FlexibleRuleBuilder) FlexibleLateMinutes(flexibleLateMinutes int) *FlexibleRuleBuilder {
	builder.flexibleLateMinutes = flexibleLateMinutes
	builder.flexibleLateMinutesFlag = true
	return builder
}

func (builder *FlexibleRuleBuilder) Build() *FlexibleRule {
	req := &FlexibleRule{}
	if builder.flexibleEarlyMinutesFlag {
		req.FlexibleEarlyMinutes = &builder.flexibleEarlyMinutes

	}
	if builder.flexibleLateMinutesFlag {
		req.FlexibleLateMinutes = &builder.flexibleLateMinutes

	}
	return req
}

type FreePunchCfg struct {
	FreeStartTime        *string `json:"free_start_time,omitempty"`           // 自由班制打卡开始时间
	FreeEndTime          *string `json:"free_end_time,omitempty"`             // 自由班制打卡结束时间
	PunchDay             *int    `json:"punch_day,omitempty"`                 // 打卡的时间，为 7 位数字，每一位依次代表周一到周日，0 为不上班，1 为上班
	WorkDayNoPunchAsLack *bool   `json:"work_day_no_punch_as_lack,omitempty"` // 工作日不打卡是否记为缺卡
}

type FreePunchCfgBuilder struct {
	freeStartTime            string // 自由班制打卡开始时间
	freeStartTimeFlag        bool
	freeEndTime              string // 自由班制打卡结束时间
	freeEndTimeFlag          bool
	punchDay                 int // 打卡的时间，为 7 位数字，每一位依次代表周一到周日，0 为不上班，1 为上班
	punchDayFlag             bool
	workDayNoPunchAsLack     bool // 工作日不打卡是否记为缺卡
	workDayNoPunchAsLackFlag bool
}

func NewFreePunchCfgBuilder() *FreePunchCfgBuilder {
	builder := &FreePunchCfgBuilder{}
	return builder
}

// 自由班制打卡开始时间
//
// 示例值：7:00
func (builder *FreePunchCfgBuilder) FreeStartTime(freeStartTime string) *FreePunchCfgBuilder {
	builder.freeStartTime = freeStartTime
	builder.freeStartTimeFlag = true
	return builder
}

// 自由班制打卡结束时间
//
// 示例值：18:00
func (builder *FreePunchCfgBuilder) FreeEndTime(freeEndTime string) *FreePunchCfgBuilder {
	builder.freeEndTime = freeEndTime
	builder.freeEndTimeFlag = true
	return builder
}

// 打卡的时间，为 7 位数字，每一位依次代表周一到周日，0 为不上班，1 为上班
//
// 示例值：1111100
func (builder *FreePunchCfgBuilder) PunchDay(punchDay int) *FreePunchCfgBuilder {
	builder.punchDay = punchDay
	builder.punchDayFlag = true
	return builder
}

// 工作日不打卡是否记为缺卡
//
// 示例值：true
func (builder *FreePunchCfgBuilder) WorkDayNoPunchAsLack(workDayNoPunchAsLack bool) *FreePunchCfgBuilder {
	builder.workDayNoPunchAsLack = workDayNoPunchAsLack
	builder.workDayNoPunchAsLackFlag = true
	return builder
}

func (builder *FreePunchCfgBuilder) Build() *FreePunchCfg {
	req := &FreePunchCfg{}
	if builder.freeStartTimeFlag {
		req.FreeStartTime = &builder.freeStartTime

	}
	if builder.freeEndTimeFlag {
		req.FreeEndTime = &builder.freeEndTime

	}
	if builder.punchDayFlag {
		req.PunchDay = &builder.punchDay

	}
	if builder.workDayNoPunchAsLackFlag {
		req.WorkDayNoPunchAsLack = &builder.workDayNoPunchAsLack

	}
	return req
}

type Group struct {
	GroupId                 *string                  `json:"group_id,omitempty"`                    // 考勤组 ID（仅修改时提供）， 需要从“获取打卡结果”的接口中获取 groupId
	GroupName               *string                  `json:"group_name,omitempty"`                  // 考勤组名称
	TimeZone                *string                  `json:"time_zone,omitempty"`                   // 时区
	BindDeptIds             []string                 `json:"bind_dept_ids,omitempty"`               // 绑定的部门 ID
	ExceptDeptIds           []string                 `json:"except_dept_ids,omitempty"`             // 排除的部门 ID
	BindUserIds             []string                 `json:"bind_user_ids,omitempty"`               // 绑定的用户 ID
	ExceptUserIds           []string                 `json:"except_user_ids,omitempty"`             // 排除的用户 ID
	GroupLeaderIds          []string                 `json:"group_leader_ids,omitempty"`            // 考勤主负责人 ID 列表，必选字段（需至少拥有考勤组管理员权限）
	SubGroupLeaderIds       []string                 `json:"sub_group_leader_ids,omitempty"`        // 考勤子负责人 ID 列表
	AllowOutPunch           *bool                    `json:"allow_out_punch,omitempty"`             // 是否允许外勤打卡
	OutPunchNeedApproval    *bool                    `json:"out_punch_need_approval,omitempty"`     // 外勤打卡需审批（需要允许外勤打卡才能设置生效）
	OutPunchNeedRemark      *bool                    `json:"out_punch_need_remark,omitempty"`       // 外勤打卡需填写备注（需要允许外勤打卡才能设置生效）
	OutPunchNeedPhoto       *bool                    `json:"out_punch_need_photo,omitempty"`        // 外勤打卡需拍照（需要允许外勤打卡才能设置生效）
	OutPunchAllowedHideAddr *bool                    `json:"out_punch_allowed_hide_addr,omitempty"` // 外勤打卡允许员工隐藏详细地址（需要允许外勤打卡才能设置生效）
	AllowPcPunch            *bool                    `json:"allow_pc_punch,omitempty"`              // 是否允许 PC 端打卡
	AllowRemedy             *bool                    `json:"allow_remedy,omitempty"`                // 是否限制补卡
	RemedyLimit             *bool                    `json:"remedy_limit,omitempty"`                // 是否限制补卡次数
	RemedyLimitCount        *int                     `json:"remedy_limit_count,omitempty"`          // 补卡次数
	RemedyDateLimit         *bool                    `json:"remedy_date_limit,omitempty"`           // 是否限制补卡时间
	RemedyDateNum           *int                     `json:"remedy_date_num,omitempty"`             // 补卡时间，几天内补卡
	AllowRemedyTypeLack     *bool                    `json:"allow_remedy_type_lack,omitempty"`      // 允许缺卡补卡（需要允许补卡才能设置生效）
	AllowRemedyTypeLate     *bool                    `json:"allow_remedy_type_late,omitempty"`      // 允许迟到补卡（需要允许补卡才能设置生效）
	AllowRemedyTypeEarly    *bool                    `json:"allow_remedy_type_early,omitempty"`     // 允许早退补卡（需要允许补卡才能设置生效）
	AllowRemedyTypeNormal   *bool                    `json:"allow_remedy_type_normal,omitempty"`    // 允许正常补卡（需要允许补卡才能设置生效）
	ShowCumulativeTime      *bool                    `json:"show_cumulative_time,omitempty"`        // 是否展示累计时长
	ShowOverTime            *bool                    `json:"show_over_time,omitempty"`              // 是否展示加班时长
	HideStaffPunchTime      *bool                    `json:"hide_staff_punch_time,omitempty"`       // 是否隐藏员工打卡详情
	FacePunch               *bool                    `json:"face_punch,omitempty"`                  // 是否开启人脸识别打卡
	FacePunchCfg            *int                     `json:"face_punch_cfg,omitempty"`              // 人脸识别打卡规则，1：每次打卡均需人脸识别，2：疑似作弊打卡时需要人脸识别
	FaceDowngrade           *bool                    `json:"face_downgrade,omitempty"`              // 人脸识别失败时是否允许普通拍照打卡
	ReplaceBasicPic         *bool                    `json:"replace_basic_pic,omitempty"`           // 人脸识别失败时是否允许替换基准图片
	Machines                []*Machine               `json:"machines,omitempty"`                    // 考勤机列表
	GpsRange                *int                     `json:"gps_range,omitempty"`                   // GPS 打卡的有效范围（不建议使用）
	Locations               []*Location              `json:"locations,omitempty"`                   // 地址列表
	GroupType               *int                     `json:"group_type,omitempty"`                  // 考勤类型，0：固定班制，2：排班制， 3：自由班制
	PunchDayShiftIds        []string                 `json:"punch_day_shift_ids,omitempty"`         // 固定班制必须填
	FreePunchCfg            *FreePunchCfg            `json:"free_punch_cfg,omitempty"`              // 配置自由班制
	CalendarId              *int                     `json:"calendar_id,omitempty"`                 // 国家日历  ID，0：不根据国家日历排休，1：中国大陆，2：美国，3：日本，4：印度，5：新加坡，默认 1
	NeedPunchSpecialDays    []*PunchSpecialDateShift `json:"need_punch_special_days,omitempty"`     // 必须打卡的特殊日期
	NoNeedPunchSpecialDays  []*PunchSpecialDateShift `json:"no_need_punch_special_days,omitempty"`  // 无需打卡的特殊日期
	WorkDayNoPunchAsLack    *bool                    `json:"work_day_no_punch_as_lack,omitempty"`   // 自由班制下工作日不打卡是否记为缺卡
	EffectNow               *bool                    `json:"effect_now,omitempty"`                  // 是否立即生效，默认 false
	RemedyPeriodType        *int                     `json:"remedy_period_type,omitempty"`          // 补卡周期类型
	RemedyPeriodCustomDate  *int                     `json:"remedy_period_custom_date,omitempty"`   // 补卡自定义周期起始日期
	PunchType               *int                     `json:"punch_type,omitempty"`                  // 打卡类型，位运算。1：GPS 打卡，2：Wi-Fi 打卡，4：考勤机打卡，8：IP 打卡
	EffectTime              *string                  `json:"effect_time,omitempty"`                 // 生效时间，精确到秒的时间戳
	FixshiftEffectTime      *string                  `json:"fixshift_effect_time,omitempty"`        // 固定班次生效时间，精确到秒的时间戳
	MemberEffectTime        *string                  `json:"member_effect_time,omitempty"`          // 参加考勤的人员、部门变动生效时间，精确到秒的时间戳
	RestClockInNeedApproval *bool                    `json:"rest_clockIn_need_approval,omitempty"`  // 休息日打卡需审批
	ClockInNeedPhoto        *bool                    `json:"clockIn_need_photo,omitempty"`          // 每次打卡均需拍照
}

type GroupBuilder struct {
	groupId                     string // 考勤组 ID（仅修改时提供）， 需要从“获取打卡结果”的接口中获取 groupId
	groupIdFlag                 bool
	groupName                   string // 考勤组名称
	groupNameFlag               bool
	timeZone                    string // 时区
	timeZoneFlag                bool
	bindDeptIds                 []string // 绑定的部门 ID
	bindDeptIdsFlag             bool
	exceptDeptIds               []string // 排除的部门 ID
	exceptDeptIdsFlag           bool
	bindUserIds                 []string // 绑定的用户 ID
	bindUserIdsFlag             bool
	exceptUserIds               []string // 排除的用户 ID
	exceptUserIdsFlag           bool
	groupLeaderIds              []string // 考勤主负责人 ID 列表，必选字段（需至少拥有考勤组管理员权限）
	groupLeaderIdsFlag          bool
	subGroupLeaderIds           []string // 考勤子负责人 ID 列表
	subGroupLeaderIdsFlag       bool
	allowOutPunch               bool // 是否允许外勤打卡
	allowOutPunchFlag           bool
	outPunchNeedApproval        bool // 外勤打卡需审批（需要允许外勤打卡才能设置生效）
	outPunchNeedApprovalFlag    bool
	outPunchNeedRemark          bool // 外勤打卡需填写备注（需要允许外勤打卡才能设置生效）
	outPunchNeedRemarkFlag      bool
	outPunchNeedPhoto           bool // 外勤打卡需拍照（需要允许外勤打卡才能设置生效）
	outPunchNeedPhotoFlag       bool
	outPunchAllowedHideAddr     bool // 外勤打卡允许员工隐藏详细地址（需要允许外勤打卡才能设置生效）
	outPunchAllowedHideAddrFlag bool
	allowPcPunch                bool // 是否允许 PC 端打卡
	allowPcPunchFlag            bool
	allowRemedy                 bool // 是否限制补卡
	allowRemedyFlag             bool
	remedyLimit                 bool // 是否限制补卡次数
	remedyLimitFlag             bool
	remedyLimitCount            int // 补卡次数
	remedyLimitCountFlag        bool
	remedyDateLimit             bool // 是否限制补卡时间
	remedyDateLimitFlag         bool
	remedyDateNum               int // 补卡时间，几天内补卡
	remedyDateNumFlag           bool
	allowRemedyTypeLack         bool // 允许缺卡补卡（需要允许补卡才能设置生效）
	allowRemedyTypeLackFlag     bool
	allowRemedyTypeLate         bool // 允许迟到补卡（需要允许补卡才能设置生效）
	allowRemedyTypeLateFlag     bool
	allowRemedyTypeEarly        bool // 允许早退补卡（需要允许补卡才能设置生效）
	allowRemedyTypeEarlyFlag    bool
	allowRemedyTypeNormal       bool // 允许正常补卡（需要允许补卡才能设置生效）
	allowRemedyTypeNormalFlag   bool
	showCumulativeTime          bool // 是否展示累计时长
	showCumulativeTimeFlag      bool
	showOverTime                bool // 是否展示加班时长
	showOverTimeFlag            bool
	hideStaffPunchTime          bool // 是否隐藏员工打卡详情
	hideStaffPunchTimeFlag      bool
	facePunch                   bool // 是否开启人脸识别打卡
	facePunchFlag               bool
	facePunchCfg                int // 人脸识别打卡规则，1：每次打卡均需人脸识别，2：疑似作弊打卡时需要人脸识别
	facePunchCfgFlag            bool
	faceDowngrade               bool // 人脸识别失败时是否允许普通拍照打卡
	faceDowngradeFlag           bool
	replaceBasicPic             bool // 人脸识别失败时是否允许替换基准图片
	replaceBasicPicFlag         bool
	machines                    []*Machine // 考勤机列表
	machinesFlag                bool
	gpsRange                    int // GPS 打卡的有效范围（不建议使用）
	gpsRangeFlag                bool
	locations                   []*Location // 地址列表
	locationsFlag               bool
	groupType                   int // 考勤类型，0：固定班制，2：排班制， 3：自由班制
	groupTypeFlag               bool
	punchDayShiftIds            []string // 固定班制必须填
	punchDayShiftIdsFlag        bool
	freePunchCfg                *FreePunchCfg // 配置自由班制
	freePunchCfgFlag            bool
	calendarId                  int // 国家日历  ID，0：不根据国家日历排休，1：中国大陆，2：美国，3：日本，4：印度，5：新加坡，默认 1
	calendarIdFlag              bool
	needPunchSpecialDays        []*PunchSpecialDateShift // 必须打卡的特殊日期
	needPunchSpecialDaysFlag    bool
	noNeedPunchSpecialDays      []*PunchSpecialDateShift // 无需打卡的特殊日期
	noNeedPunchSpecialDaysFlag  bool
	workDayNoPunchAsLack        bool // 自由班制下工作日不打卡是否记为缺卡
	workDayNoPunchAsLackFlag    bool
	effectNow                   bool // 是否立即生效，默认 false
	effectNowFlag               bool
	remedyPeriodType            int // 补卡周期类型
	remedyPeriodTypeFlag        bool
	remedyPeriodCustomDate      int // 补卡自定义周期起始日期
	remedyPeriodCustomDateFlag  bool
	punchType                   int // 打卡类型，位运算。1：GPS 打卡，2：Wi-Fi 打卡，4：考勤机打卡，8：IP 打卡
	punchTypeFlag               bool
	effectTime                  string // 生效时间，精确到秒的时间戳
	effectTimeFlag              bool
	fixshiftEffectTime          string // 固定班次生效时间，精确到秒的时间戳
	fixshiftEffectTimeFlag      bool
	memberEffectTime            string // 参加考勤的人员、部门变动生效时间，精确到秒的时间戳
	memberEffectTimeFlag        bool
	restClockInNeedApproval     bool // 休息日打卡需审批
	restClockInNeedApprovalFlag bool
	clockInNeedPhoto            bool // 每次打卡均需拍照
	clockInNeedPhotoFlag        bool
}

func NewGroupBuilder() *GroupBuilder {
	builder := &GroupBuilder{}
	return builder
}

// 考勤组 ID（仅修改时提供）， 需要从“获取打卡结果”的接口中获取 groupId
//
// 示例值：6919358128597097404
func (builder *GroupBuilder) GroupId(groupId string) *GroupBuilder {
	builder.groupId = groupId
	builder.groupIdFlag = true
	return builder
}

// 考勤组名称
//
// 示例值：开心考勤
func (builder *GroupBuilder) GroupName(groupName string) *GroupBuilder {
	builder.groupName = groupName
	builder.groupNameFlag = true
	return builder
}

// 时区
//
// 示例值：Asia/Shanghai
func (builder *GroupBuilder) TimeZone(timeZone string) *GroupBuilder {
	builder.timeZone = timeZone
	builder.timeZoneFlag = true
	return builder
}

// 绑定的部门 ID
//
// 示例值：od-fcb45c28a45311afd440b7869541fce8
func (builder *GroupBuilder) BindDeptIds(bindDeptIds []string) *GroupBuilder {
	builder.bindDeptIds = bindDeptIds
	builder.bindDeptIdsFlag = true
	return builder
}

// 排除的部门 ID
//
// 示例值：od-fcb45c28a45311afd440b7869541fce8
func (builder *GroupBuilder) ExceptDeptIds(exceptDeptIds []string) *GroupBuilder {
	builder.exceptDeptIds = exceptDeptIds
	builder.exceptDeptIdsFlag = true
	return builder
}

// 绑定的用户 ID
//
// 示例值：52aa1fa1
func (builder *GroupBuilder) BindUserIds(bindUserIds []string) *GroupBuilder {
	builder.bindUserIds = bindUserIds
	builder.bindUserIdsFlag = true
	return builder
}

// 排除的用户 ID
//
// 示例值：52aa1fa1
func (builder *GroupBuilder) ExceptUserIds(exceptUserIds []string) *GroupBuilder {
	builder.exceptUserIds = exceptUserIds
	builder.exceptUserIdsFlag = true
	return builder
}

// 考勤主负责人 ID 列表，必选字段（需至少拥有考勤组管理员权限）
//
// 示例值：2bg4a9be
func (builder *GroupBuilder) GroupLeaderIds(groupLeaderIds []string) *GroupBuilder {
	builder.groupLeaderIds = groupLeaderIds
	builder.groupLeaderIdsFlag = true
	return builder
}

// 考勤子负责人 ID 列表
//
// 示例值：52aa1fa1
func (builder *GroupBuilder) SubGroupLeaderIds(subGroupLeaderIds []string) *GroupBuilder {
	builder.subGroupLeaderIds = subGroupLeaderIds
	builder.subGroupLeaderIdsFlag = true
	return builder
}

// 是否允许外勤打卡
//
// 示例值：true
func (builder *GroupBuilder) AllowOutPunch(allowOutPunch bool) *GroupBuilder {
	builder.allowOutPunch = allowOutPunch
	builder.allowOutPunchFlag = true
	return builder
}

// 外勤打卡需审批（需要允许外勤打卡才能设置生效）
//
// 示例值：true
func (builder *GroupBuilder) OutPunchNeedApproval(outPunchNeedApproval bool) *GroupBuilder {
	builder.outPunchNeedApproval = outPunchNeedApproval
	builder.outPunchNeedApprovalFlag = true
	return builder
}

// 外勤打卡需填写备注（需要允许外勤打卡才能设置生效）
//
// 示例值：true
func (builder *GroupBuilder) OutPunchNeedRemark(outPunchNeedRemark bool) *GroupBuilder {
	builder.outPunchNeedRemark = outPunchNeedRemark
	builder.outPunchNeedRemarkFlag = true
	return builder
}

// 外勤打卡需拍照（需要允许外勤打卡才能设置生效）
//
// 示例值：true
func (builder *GroupBuilder) OutPunchNeedPhoto(outPunchNeedPhoto bool) *GroupBuilder {
	builder.outPunchNeedPhoto = outPunchNeedPhoto
	builder.outPunchNeedPhotoFlag = true
	return builder
}

// 外勤打卡允许员工隐藏详细地址（需要允许外勤打卡才能设置生效）
//
// 示例值：true
func (builder *GroupBuilder) OutPunchAllowedHideAddr(outPunchAllowedHideAddr bool) *GroupBuilder {
	builder.outPunchAllowedHideAddr = outPunchAllowedHideAddr
	builder.outPunchAllowedHideAddrFlag = true
	return builder
}

// 是否允许 PC 端打卡
//
// 示例值：true
func (builder *GroupBuilder) AllowPcPunch(allowPcPunch bool) *GroupBuilder {
	builder.allowPcPunch = allowPcPunch
	builder.allowPcPunchFlag = true
	return builder
}

// 是否限制补卡
//
// 示例值：true
func (builder *GroupBuilder) AllowRemedy(allowRemedy bool) *GroupBuilder {
	builder.allowRemedy = allowRemedy
	builder.allowRemedyFlag = true
	return builder
}

// 是否限制补卡次数
//
// 示例值：true
func (builder *GroupBuilder) RemedyLimit(remedyLimit bool) *GroupBuilder {
	builder.remedyLimit = remedyLimit
	builder.remedyLimitFlag = true
	return builder
}

// 补卡次数
//
// 示例值：3
func (builder *GroupBuilder) RemedyLimitCount(remedyLimitCount int) *GroupBuilder {
	builder.remedyLimitCount = remedyLimitCount
	builder.remedyLimitCountFlag = true
	return builder
}

// 是否限制补卡时间
//
// 示例值：true
func (builder *GroupBuilder) RemedyDateLimit(remedyDateLimit bool) *GroupBuilder {
	builder.remedyDateLimit = remedyDateLimit
	builder.remedyDateLimitFlag = true
	return builder
}

// 补卡时间，几天内补卡
//
// 示例值：3
func (builder *GroupBuilder) RemedyDateNum(remedyDateNum int) *GroupBuilder {
	builder.remedyDateNum = remedyDateNum
	builder.remedyDateNumFlag = true
	return builder
}

// 允许缺卡补卡（需要允许补卡才能设置生效）
//
// 示例值：true
func (builder *GroupBuilder) AllowRemedyTypeLack(allowRemedyTypeLack bool) *GroupBuilder {
	builder.allowRemedyTypeLack = allowRemedyTypeLack
	builder.allowRemedyTypeLackFlag = true
	return builder
}

// 允许迟到补卡（需要允许补卡才能设置生效）
//
// 示例值：true
func (builder *GroupBuilder) AllowRemedyTypeLate(allowRemedyTypeLate bool) *GroupBuilder {
	builder.allowRemedyTypeLate = allowRemedyTypeLate
	builder.allowRemedyTypeLateFlag = true
	return builder
}

// 允许早退补卡（需要允许补卡才能设置生效）
//
// 示例值：true
func (builder *GroupBuilder) AllowRemedyTypeEarly(allowRemedyTypeEarly bool) *GroupBuilder {
	builder.allowRemedyTypeEarly = allowRemedyTypeEarly
	builder.allowRemedyTypeEarlyFlag = true
	return builder
}

// 允许正常补卡（需要允许补卡才能设置生效）
//
// 示例值：true
func (builder *GroupBuilder) AllowRemedyTypeNormal(allowRemedyTypeNormal bool) *GroupBuilder {
	builder.allowRemedyTypeNormal = allowRemedyTypeNormal
	builder.allowRemedyTypeNormalFlag = true
	return builder
}

// 是否展示累计时长
//
// 示例值：true
func (builder *GroupBuilder) ShowCumulativeTime(showCumulativeTime bool) *GroupBuilder {
	builder.showCumulativeTime = showCumulativeTime
	builder.showCumulativeTimeFlag = true
	return builder
}

// 是否展示加班时长
//
// 示例值：true
func (builder *GroupBuilder) ShowOverTime(showOverTime bool) *GroupBuilder {
	builder.showOverTime = showOverTime
	builder.showOverTimeFlag = true
	return builder
}

// 是否隐藏员工打卡详情
//
// 示例值：true
func (builder *GroupBuilder) HideStaffPunchTime(hideStaffPunchTime bool) *GroupBuilder {
	builder.hideStaffPunchTime = hideStaffPunchTime
	builder.hideStaffPunchTimeFlag = true
	return builder
}

// 是否开启人脸识别打卡
//
// 示例值：true
func (builder *GroupBuilder) FacePunch(facePunch bool) *GroupBuilder {
	builder.facePunch = facePunch
	builder.facePunchFlag = true
	return builder
}

// 人脸识别打卡规则，1：每次打卡均需人脸识别，2：疑似作弊打卡时需要人脸识别
//
// 示例值：1
func (builder *GroupBuilder) FacePunchCfg(facePunchCfg int) *GroupBuilder {
	builder.facePunchCfg = facePunchCfg
	builder.facePunchCfgFlag = true
	return builder
}

// 人脸识别失败时是否允许普通拍照打卡
//
// 示例值：true
func (builder *GroupBuilder) FaceDowngrade(faceDowngrade bool) *GroupBuilder {
	builder.faceDowngrade = faceDowngrade
	builder.faceDowngradeFlag = true
	return builder
}

// 人脸识别失败时是否允许替换基准图片
//
// 示例值：true
func (builder *GroupBuilder) ReplaceBasicPic(replaceBasicPic bool) *GroupBuilder {
	builder.replaceBasicPic = replaceBasicPic
	builder.replaceBasicPicFlag = true
	return builder
}

// 考勤机列表
//
// 示例值：
func (builder *GroupBuilder) Machines(machines []*Machine) *GroupBuilder {
	builder.machines = machines
	builder.machinesFlag = true
	return builder
}

// GPS 打卡的有效范围（不建议使用）
//
// 示例值：300
func (builder *GroupBuilder) GpsRange(gpsRange int) *GroupBuilder {
	builder.gpsRange = gpsRange
	builder.gpsRangeFlag = true
	return builder
}

// 地址列表
//
// 示例值：
func (builder *GroupBuilder) Locations(locations []*Location) *GroupBuilder {
	builder.locations = locations
	builder.locationsFlag = true
	return builder
}

// 考勤类型，0：固定班制，2：排班制， 3：自由班制
//
// 示例值：0
func (builder *GroupBuilder) GroupType(groupType int) *GroupBuilder {
	builder.groupType = groupType
	builder.groupTypeFlag = true
	return builder
}

// 固定班制必须填
//
// 示例值：6921319402260496386
func (builder *GroupBuilder) PunchDayShiftIds(punchDayShiftIds []string) *GroupBuilder {
	builder.punchDayShiftIds = punchDayShiftIds
	builder.punchDayShiftIdsFlag = true
	return builder
}

// 配置自由班制
//
// 示例值：
func (builder *GroupBuilder) FreePunchCfg(freePunchCfg *FreePunchCfg) *GroupBuilder {
	builder.freePunchCfg = freePunchCfg
	builder.freePunchCfgFlag = true
	return builder
}

// 国家日历  ID，0：不根据国家日历排休，1：中国大陆，2：美国，3：日本，4：印度，5：新加坡，默认 1
//
// 示例值：1
func (builder *GroupBuilder) CalendarId(calendarId int) *GroupBuilder {
	builder.calendarId = calendarId
	builder.calendarIdFlag = true
	return builder
}

// 必须打卡的特殊日期
//
// 示例值：
func (builder *GroupBuilder) NeedPunchSpecialDays(needPunchSpecialDays []*PunchSpecialDateShift) *GroupBuilder {
	builder.needPunchSpecialDays = needPunchSpecialDays
	builder.needPunchSpecialDaysFlag = true
	return builder
}

// 无需打卡的特殊日期
//
// 示例值：
func (builder *GroupBuilder) NoNeedPunchSpecialDays(noNeedPunchSpecialDays []*PunchSpecialDateShift) *GroupBuilder {
	builder.noNeedPunchSpecialDays = noNeedPunchSpecialDays
	builder.noNeedPunchSpecialDaysFlag = true
	return builder
}

// 自由班制下工作日不打卡是否记为缺卡
//
// 示例值：true
func (builder *GroupBuilder) WorkDayNoPunchAsLack(workDayNoPunchAsLack bool) *GroupBuilder {
	builder.workDayNoPunchAsLack = workDayNoPunchAsLack
	builder.workDayNoPunchAsLackFlag = true
	return builder
}

// 是否立即生效，默认 false
//
// 示例值：true
func (builder *GroupBuilder) EffectNow(effectNow bool) *GroupBuilder {
	builder.effectNow = effectNow
	builder.effectNowFlag = true
	return builder
}

// 补卡周期类型
//
// 示例值：0
func (builder *GroupBuilder) RemedyPeriodType(remedyPeriodType int) *GroupBuilder {
	builder.remedyPeriodType = remedyPeriodType
	builder.remedyPeriodTypeFlag = true
	return builder
}

// 补卡自定义周期起始日期
//
// 示例值：1
func (builder *GroupBuilder) RemedyPeriodCustomDate(remedyPeriodCustomDate int) *GroupBuilder {
	builder.remedyPeriodCustomDate = remedyPeriodCustomDate
	builder.remedyPeriodCustomDateFlag = true
	return builder
}

// 打卡类型，位运算。1：GPS 打卡，2：Wi-Fi 打卡，4：考勤机打卡，8：IP 打卡
//
// 示例值：1
func (builder *GroupBuilder) PunchType(punchType int) *GroupBuilder {
	builder.punchType = punchType
	builder.punchTypeFlag = true
	return builder
}

// 生效时间，精确到秒的时间戳
//
// 示例值：1611476284
func (builder *GroupBuilder) EffectTime(effectTime string) *GroupBuilder {
	builder.effectTime = effectTime
	builder.effectTimeFlag = true
	return builder
}

// 固定班次生效时间，精确到秒的时间戳
//
// 示例值：1611476284
func (builder *GroupBuilder) FixshiftEffectTime(fixshiftEffectTime string) *GroupBuilder {
	builder.fixshiftEffectTime = fixshiftEffectTime
	builder.fixshiftEffectTimeFlag = true
	return builder
}

// 参加考勤的人员、部门变动生效时间，精确到秒的时间戳
//
// 示例值：1611476284
func (builder *GroupBuilder) MemberEffectTime(memberEffectTime string) *GroupBuilder {
	builder.memberEffectTime = memberEffectTime
	builder.memberEffectTimeFlag = true
	return builder
}

// 休息日打卡需审批
//
// 示例值：true
func (builder *GroupBuilder) RestClockInNeedApproval(restClockInNeedApproval bool) *GroupBuilder {
	builder.restClockInNeedApproval = restClockInNeedApproval
	builder.restClockInNeedApprovalFlag = true
	return builder
}

// 每次打卡均需拍照
//
// 示例值：true
func (builder *GroupBuilder) ClockInNeedPhoto(clockInNeedPhoto bool) *GroupBuilder {
	builder.clockInNeedPhoto = clockInNeedPhoto
	builder.clockInNeedPhotoFlag = true
	return builder
}

func (builder *GroupBuilder) Build() *Group {
	req := &Group{}
	if builder.groupIdFlag {
		req.GroupId = &builder.groupId

	}
	if builder.groupNameFlag {
		req.GroupName = &builder.groupName

	}
	if builder.timeZoneFlag {
		req.TimeZone = &builder.timeZone

	}
	if builder.bindDeptIdsFlag {
		req.BindDeptIds = builder.bindDeptIds
	}
	if builder.exceptDeptIdsFlag {
		req.ExceptDeptIds = builder.exceptDeptIds
	}
	if builder.bindUserIdsFlag {
		req.BindUserIds = builder.bindUserIds
	}
	if builder.exceptUserIdsFlag {
		req.ExceptUserIds = builder.exceptUserIds
	}
	if builder.groupLeaderIdsFlag {
		req.GroupLeaderIds = builder.groupLeaderIds
	}
	if builder.subGroupLeaderIdsFlag {
		req.SubGroupLeaderIds = builder.subGroupLeaderIds
	}
	if builder.allowOutPunchFlag {
		req.AllowOutPunch = &builder.allowOutPunch

	}
	if builder.outPunchNeedApprovalFlag {
		req.OutPunchNeedApproval = &builder.outPunchNeedApproval

	}
	if builder.outPunchNeedRemarkFlag {
		req.OutPunchNeedRemark = &builder.outPunchNeedRemark

	}
	if builder.outPunchNeedPhotoFlag {
		req.OutPunchNeedPhoto = &builder.outPunchNeedPhoto

	}
	if builder.outPunchAllowedHideAddrFlag {
		req.OutPunchAllowedHideAddr = &builder.outPunchAllowedHideAddr

	}
	if builder.allowPcPunchFlag {
		req.AllowPcPunch = &builder.allowPcPunch

	}
	if builder.allowRemedyFlag {
		req.AllowRemedy = &builder.allowRemedy

	}
	if builder.remedyLimitFlag {
		req.RemedyLimit = &builder.remedyLimit

	}
	if builder.remedyLimitCountFlag {
		req.RemedyLimitCount = &builder.remedyLimitCount

	}
	if builder.remedyDateLimitFlag {
		req.RemedyDateLimit = &builder.remedyDateLimit

	}
	if builder.remedyDateNumFlag {
		req.RemedyDateNum = &builder.remedyDateNum

	}
	if builder.allowRemedyTypeLackFlag {
		req.AllowRemedyTypeLack = &builder.allowRemedyTypeLack

	}
	if builder.allowRemedyTypeLateFlag {
		req.AllowRemedyTypeLate = &builder.allowRemedyTypeLate

	}
	if builder.allowRemedyTypeEarlyFlag {
		req.AllowRemedyTypeEarly = &builder.allowRemedyTypeEarly

	}
	if builder.allowRemedyTypeNormalFlag {
		req.AllowRemedyTypeNormal = &builder.allowRemedyTypeNormal

	}
	if builder.showCumulativeTimeFlag {
		req.ShowCumulativeTime = &builder.showCumulativeTime

	}
	if builder.showOverTimeFlag {
		req.ShowOverTime = &builder.showOverTime

	}
	if builder.hideStaffPunchTimeFlag {
		req.HideStaffPunchTime = &builder.hideStaffPunchTime

	}
	if builder.facePunchFlag {
		req.FacePunch = &builder.facePunch

	}
	if builder.facePunchCfgFlag {
		req.FacePunchCfg = &builder.facePunchCfg

	}
	if builder.faceDowngradeFlag {
		req.FaceDowngrade = &builder.faceDowngrade

	}
	if builder.replaceBasicPicFlag {
		req.ReplaceBasicPic = &builder.replaceBasicPic

	}
	if builder.machinesFlag {
		req.Machines = builder.machines
	}
	if builder.gpsRangeFlag {
		req.GpsRange = &builder.gpsRange

	}
	if builder.locationsFlag {
		req.Locations = builder.locations
	}
	if builder.groupTypeFlag {
		req.GroupType = &builder.groupType

	}
	if builder.punchDayShiftIdsFlag {
		req.PunchDayShiftIds = builder.punchDayShiftIds
	}
	if builder.freePunchCfgFlag {
		req.FreePunchCfg = builder.freePunchCfg
	}
	if builder.calendarIdFlag {
		req.CalendarId = &builder.calendarId

	}
	if builder.needPunchSpecialDaysFlag {
		req.NeedPunchSpecialDays = builder.needPunchSpecialDays
	}
	if builder.noNeedPunchSpecialDaysFlag {
		req.NoNeedPunchSpecialDays = builder.noNeedPunchSpecialDays
	}
	if builder.workDayNoPunchAsLackFlag {
		req.WorkDayNoPunchAsLack = &builder.workDayNoPunchAsLack

	}
	if builder.effectNowFlag {
		req.EffectNow = &builder.effectNow

	}
	if builder.remedyPeriodTypeFlag {
		req.RemedyPeriodType = &builder.remedyPeriodType

	}
	if builder.remedyPeriodCustomDateFlag {
		req.RemedyPeriodCustomDate = &builder.remedyPeriodCustomDate

	}
	if builder.punchTypeFlag {
		req.PunchType = &builder.punchType

	}
	if builder.effectTimeFlag {
		req.EffectTime = &builder.effectTime

	}
	if builder.fixshiftEffectTimeFlag {
		req.FixshiftEffectTime = &builder.fixshiftEffectTime

	}
	if builder.memberEffectTimeFlag {
		req.MemberEffectTime = &builder.memberEffectTime

	}
	if builder.restClockInNeedApprovalFlag {
		req.RestClockInNeedApproval = &builder.restClockInNeedApproval

	}
	if builder.clockInNeedPhotoFlag {
		req.ClockInNeedPhoto = &builder.clockInNeedPhoto

	}
	return req
}

type GroupMeta struct {
	GroupId   *string `json:"group_id,omitempty"`   // 考勤组 ID
	GroupName *string `json:"group_name,omitempty"` // 考勤组名称
}

type GroupMetaBuilder struct {
	groupId       string // 考勤组 ID
	groupIdFlag   bool
	groupName     string // 考勤组名称
	groupNameFlag bool
}

func NewGroupMetaBuilder() *GroupMetaBuilder {
	builder := &GroupMetaBuilder{}
	return builder
}

// 考勤组 ID
//
// 示例值：6919358128597097404
func (builder *GroupMetaBuilder) GroupId(groupId string) *GroupMetaBuilder {
	builder.groupId = groupId
	builder.groupIdFlag = true
	return builder
}

// 考勤组名称
//
// 示例值：考勤组1
func (builder *GroupMetaBuilder) GroupName(groupName string) *GroupMetaBuilder {
	builder.groupName = groupName
	builder.groupNameFlag = true
	return builder
}

func (builder *GroupMetaBuilder) Build() *GroupMeta {
	req := &GroupMeta{}
	if builder.groupIdFlag {
		req.GroupId = &builder.groupId

	}
	if builder.groupNameFlag {
		req.GroupName = &builder.groupName

	}
	return req
}

type I18nNames struct {
	Ch *string `json:"ch,omitempty"` // 中文描述
	En *string `json:"en,omitempty"` // 英语描述
	Ja *string `json:"ja,omitempty"` // 日语描述
}

type I18nNamesBuilder struct {
	ch     string // 中文描述
	chFlag bool
	en     string // 英语描述
	enFlag bool
	ja     string // 日语描述
	jaFlag bool
}

func NewI18nNamesBuilder() *I18nNamesBuilder {
	builder := &I18nNamesBuilder{}
	return builder
}

// 中文描述
//
// 示例值：中文描述
func (builder *I18nNamesBuilder) Ch(ch string) *I18nNamesBuilder {
	builder.ch = ch
	builder.chFlag = true
	return builder
}

// 英语描述
//
// 示例值：English description
func (builder *I18nNamesBuilder) En(en string) *I18nNamesBuilder {
	builder.en = en
	builder.enFlag = true
	return builder
}

// 日语描述
//
// 示例值：日本語の説明
func (builder *I18nNamesBuilder) Ja(ja string) *I18nNamesBuilder {
	builder.ja = ja
	builder.jaFlag = true
	return builder
}

func (builder *I18nNamesBuilder) Build() *I18nNames {
	req := &I18nNames{}
	if builder.chFlag {
		req.Ch = &builder.ch

	}
	if builder.enFlag {
		req.En = &builder.en

	}
	if builder.jaFlag {
		req.Ja = &builder.ja

	}
	return req
}

type Item struct {
	Code       *string      `json:"code,omitempty"`        // 标题编号
	Title      *string      `json:"title,omitempty"`       // 标题名称
	ChildItems []*ChildItem `json:"child_items,omitempty"` // 子标题
}

type ItemBuilder struct {
	code           string // 标题编号
	codeFlag       bool
	title          string // 标题名称
	titleFlag      bool
	childItems     []*ChildItem // 子标题
	childItemsFlag bool
}

func NewItemBuilder() *ItemBuilder {
	builder := &ItemBuilder{}
	return builder
}

// 标题编号
//
// 示例值：522
func (builder *ItemBuilder) Code(code string) *ItemBuilder {
	builder.code = code
	builder.codeFlag = true
	return builder
}

// 标题名称
//
// 示例值：基本信息
func (builder *ItemBuilder) Title(title string) *ItemBuilder {
	builder.title = title
	builder.titleFlag = true
	return builder
}

// 子标题
//
// 示例值：
func (builder *ItemBuilder) ChildItems(childItems []*ChildItem) *ItemBuilder {
	builder.childItems = childItems
	builder.childItemsFlag = true
	return builder
}

func (builder *ItemBuilder) Build() *Item {
	req := &Item{}
	if builder.codeFlag {
		req.Code = &builder.code

	}
	if builder.titleFlag {
		req.Title = &builder.title

	}
	if builder.childItemsFlag {
		req.ChildItems = builder.childItems
	}
	return req
}

type LateOffLateOnRule struct {
	LateOffMinutes *int `json:"late_off_minutes,omitempty"` // 晚走多久
	LateOnMinutes  *int `json:"late_on_minutes,omitempty"`  // 晚到多久
}

type LateOffLateOnRuleBuilder struct {
	lateOffMinutes     int // 晚走多久
	lateOffMinutesFlag bool
	lateOnMinutes      int // 晚到多久
	lateOnMinutesFlag  bool
}

func NewLateOffLateOnRuleBuilder() *LateOffLateOnRuleBuilder {
	builder := &LateOffLateOnRuleBuilder{}
	return builder
}

// 晚走多久
//
// 示例值：60
func (builder *LateOffLateOnRuleBuilder) LateOffMinutes(lateOffMinutes int) *LateOffLateOnRuleBuilder {
	builder.lateOffMinutes = lateOffMinutes
	builder.lateOffMinutesFlag = true
	return builder
}

// 晚到多久
//
// 示例值：30
func (builder *LateOffLateOnRuleBuilder) LateOnMinutes(lateOnMinutes int) *LateOffLateOnRuleBuilder {
	builder.lateOnMinutes = lateOnMinutes
	builder.lateOnMinutesFlag = true
	return builder
}

func (builder *LateOffLateOnRuleBuilder) Build() *LateOffLateOnRule {
	req := &LateOffLateOnRule{}
	if builder.lateOffMinutesFlag {
		req.LateOffMinutes = &builder.lateOffMinutes

	}
	if builder.lateOnMinutesFlag {
		req.LateOnMinutes = &builder.lateOnMinutes

	}
	return req
}

type Location struct {
	LocationId   *string  `json:"location_id,omitempty"`   // 地址 ID
	LocationName *string  `json:"location_name,omitempty"` // 地址名称
	LocationType *int     `json:"location_type,omitempty"` // 地址类型，1：GPS，2：Wi-Fi，8：IP
	Latitude     *float64 `json:"latitude,omitempty"`      // 地址纬度
	Longitude    *float64 `json:"longitude,omitempty"`     // 地址经度
	Ssid         *string  `json:"ssid,omitempty"`          // Wi-Fi 名称
	Bssid        *string  `json:"bssid,omitempty"`         // Wi-Fi 的 MAC 地址
	MapType      *int     `json:"map_type,omitempty"`      // 地图类型，1：高德， 2：谷歌
	Address      *string  `json:"address,omitempty"`       // 地址名称
	Ip           *string  `json:"ip,omitempty"`            // IP 地址
	Feature      *string  `json:"feature,omitempty"`       // 额外信息，例如：运营商信息
	GpsRange     *int     `json:"gps_range,omitempty"`     // GPS 打卡的有效范围
}

type LocationBuilder struct {
	locationId       string // 地址 ID
	locationIdFlag   bool
	locationName     string // 地址名称
	locationNameFlag bool
	locationType     int // 地址类型，1：GPS，2：Wi-Fi，8：IP
	locationTypeFlag bool
	latitude         float64 // 地址纬度
	latitudeFlag     bool
	longitude        float64 // 地址经度
	longitudeFlag    bool
	ssid             string // Wi-Fi 名称
	ssidFlag         bool
	bssid            string // Wi-Fi 的 MAC 地址
	bssidFlag        bool
	mapType          int // 地图类型，1：高德， 2：谷歌
	mapTypeFlag      bool
	address          string // 地址名称
	addressFlag      bool
	ip               string // IP 地址
	ipFlag           bool
	feature          string // 额外信息，例如：运营商信息
	featureFlag      bool
	gpsRange         int // GPS 打卡的有效范围
	gpsRangeFlag     bool
}

func NewLocationBuilder() *LocationBuilder {
	builder := &LocationBuilder{}
	return builder
}

// 地址 ID
//
// 示例值：6921213751454744578
func (builder *LocationBuilder) LocationId(locationId string) *LocationBuilder {
	builder.locationId = locationId
	builder.locationIdFlag = true
	return builder
}

// 地址名称
//
// 示例值：浙江省杭州市余杭区五常街道木桥头西溪八方城
func (builder *LocationBuilder) LocationName(locationName string) *LocationBuilder {
	builder.locationName = locationName
	builder.locationNameFlag = true
	return builder
}

// 地址类型，1：GPS，2：Wi-Fi，8：IP
//
// 示例值：1
func (builder *LocationBuilder) LocationType(locationType int) *LocationBuilder {
	builder.locationType = locationType
	builder.locationTypeFlag = true
	return builder
}

// 地址纬度
//
// 示例值：30.28994
func (builder *LocationBuilder) Latitude(latitude float64) *LocationBuilder {
	builder.latitude = latitude
	builder.latitudeFlag = true
	return builder
}

// 地址经度
//
// 示例值：120.04509
func (builder *LocationBuilder) Longitude(longitude float64) *LocationBuilder {
	builder.longitude = longitude
	builder.longitudeFlag = true
	return builder
}

// Wi-Fi 名称
//
// 示例值：TP-Link-af12ca
func (builder *LocationBuilder) Ssid(ssid string) *LocationBuilder {
	builder.ssid = ssid
	builder.ssidFlag = true
	return builder
}

// Wi-Fi 的 MAC 地址
//
// 示例值：08:00:20:0A:8C:6D
func (builder *LocationBuilder) Bssid(bssid string) *LocationBuilder {
	builder.bssid = bssid
	builder.bssidFlag = true
	return builder
}

// 地图类型，1：高德， 2：谷歌
//
// 示例值：1
func (builder *LocationBuilder) MapType(mapType int) *LocationBuilder {
	builder.mapType = mapType
	builder.mapTypeFlag = true
	return builder
}

// 地址名称
//
// 示例值：北京市海淀区中航广场
func (builder *LocationBuilder) Address(address string) *LocationBuilder {
	builder.address = address
	builder.addressFlag = true
	return builder
}

// IP 地址
//
// 示例值：***************
func (builder *LocationBuilder) Ip(ip string) *LocationBuilder {
	builder.ip = ip
	builder.ipFlag = true
	return builder
}

// 额外信息，例如：运营商信息
//
// 示例值：中国电信
func (builder *LocationBuilder) Feature(feature string) *LocationBuilder {
	builder.feature = feature
	builder.featureFlag = true
	return builder
}

// GPS 打卡的有效范围
//
// 示例值：300
func (builder *LocationBuilder) GpsRange(gpsRange int) *LocationBuilder {
	builder.gpsRange = gpsRange
	builder.gpsRangeFlag = true
	return builder
}

func (builder *LocationBuilder) Build() *Location {
	req := &Location{}
	if builder.locationIdFlag {
		req.LocationId = &builder.locationId

	}
	if builder.locationNameFlag {
		req.LocationName = &builder.locationName

	}
	if builder.locationTypeFlag {
		req.LocationType = &builder.locationType

	}
	if builder.latitudeFlag {
		req.Latitude = &builder.latitude

	}
	if builder.longitudeFlag {
		req.Longitude = &builder.longitude

	}
	if builder.ssidFlag {
		req.Ssid = &builder.ssid

	}
	if builder.bssidFlag {
		req.Bssid = &builder.bssid

	}
	if builder.mapTypeFlag {
		req.MapType = &builder.mapType

	}
	if builder.addressFlag {
		req.Address = &builder.address

	}
	if builder.ipFlag {
		req.Ip = &builder.ip

	}
	if builder.featureFlag {
		req.Feature = &builder.feature

	}
	if builder.gpsRangeFlag {
		req.GpsRange = &builder.gpsRange

	}
	return req
}

type LocationInfo struct {
	Status    *int    `json:"status,omitempty"`    // 开关状态
	Geofences []*Area `json:"geofences,omitempty"` //
}

type LocationInfoBuilder struct {
	status        int // 开关状态
	statusFlag    bool
	geofences     []*Area //
	geofencesFlag bool
}

func NewLocationInfoBuilder() *LocationInfoBuilder {
	builder := &LocationInfoBuilder{}
	return builder
}

// 开关状态
//
// 示例值：
func (builder *LocationInfoBuilder) Status(status int) *LocationInfoBuilder {
	builder.status = status
	builder.statusFlag = true
	return builder
}

//
//
// 示例值：
func (builder *LocationInfoBuilder) Geofences(geofences []*Area) *LocationInfoBuilder {
	builder.geofences = geofences
	builder.geofencesFlag = true
	return builder
}

func (builder *LocationInfoBuilder) Build() *LocationInfo {
	req := &LocationInfo{}
	if builder.statusFlag {
		req.Status = &builder.status

	}
	if builder.geofencesFlag {
		req.Geofences = builder.geofences
	}
	return req
}

type LocationInfoEvent struct {
	Coord *Coordinate `json:"coord,omitempty"` //
}

type LocationInfoEventBuilder struct {
	coord     *Coordinate //
	coordFlag bool
}

func NewLocationInfoEventBuilder() *LocationInfoEventBuilder {
	builder := &LocationInfoEventBuilder{}
	return builder
}

//
//
// 示例值：
func (builder *LocationInfoEventBuilder) Coord(coord *Coordinate) *LocationInfoEventBuilder {
	builder.coord = coord
	builder.coordFlag = true
	return builder
}

func (builder *LocationInfoEventBuilder) Build() *LocationInfoEvent {
	req := &LocationInfoEvent{}
	if builder.coordFlag {
		req.Coord = builder.coord
	}
	return req
}

type LocationRecord struct {
	UserId         *UserId            `json:"user_id,omitempty"`          // 用户id
	Timestamp      *string            `json:"timestamp,omitempty"`        // 时间点
	Location       *LocationInfoEvent `json:"location,omitempty"`         // 位置信息
	Wifi           *WifiInfoEvent     `json:"wifi,omitempty"`             // wifi信息
	RuleSnapshotId *string            `json:"rule_snapshot_id,omitempty"` // 规则快照id
	Type           *string            `json:"type,omitempty"`             // 事件类型
	ScanWifiList   []*ScanWifiInfo    `json:"scan_wifi_list,omitempty"`   // 附近的wifi设备列表
	DeviceId       *string            `json:"device_id,omitempty"`        // 上报事件的设备ID
	ClientInfo     *string            `json:"client_info,omitempty"`      //
}

type LocationRecordBuilder struct {
	userId             *UserId // 用户id
	userIdFlag         bool
	timestamp          string // 时间点
	timestampFlag      bool
	location           *LocationInfoEvent // 位置信息
	locationFlag       bool
	wifi               *WifiInfoEvent // wifi信息
	wifiFlag           bool
	ruleSnapshotId     string // 规则快照id
	ruleSnapshotIdFlag bool
	type_              string // 事件类型
	typeFlag           bool
	scanWifiList       []*ScanWifiInfo // 附近的wifi设备列表
	scanWifiListFlag   bool
	deviceId           string // 上报事件的设备ID
	deviceIdFlag       bool
	clientInfo         string //
	clientInfoFlag     bool
}

func NewLocationRecordBuilder() *LocationRecordBuilder {
	builder := &LocationRecordBuilder{}
	return builder
}

// 用户id
//
// 示例值：
func (builder *LocationRecordBuilder) UserId(userId *UserId) *LocationRecordBuilder {
	builder.userId = userId
	builder.userIdFlag = true
	return builder
}

// 时间点
//
// 示例值：
func (builder *LocationRecordBuilder) Timestamp(timestamp string) *LocationRecordBuilder {
	builder.timestamp = timestamp
	builder.timestampFlag = true
	return builder
}

// 位置信息
//
// 示例值：
func (builder *LocationRecordBuilder) Location(location *LocationInfoEvent) *LocationRecordBuilder {
	builder.location = location
	builder.locationFlag = true
	return builder
}

// wifi信息
//
// 示例值：
func (builder *LocationRecordBuilder) Wifi(wifi *WifiInfoEvent) *LocationRecordBuilder {
	builder.wifi = wifi
	builder.wifiFlag = true
	return builder
}

// 规则快照id
//
// 示例值：
func (builder *LocationRecordBuilder) RuleSnapshotId(ruleSnapshotId string) *LocationRecordBuilder {
	builder.ruleSnapshotId = ruleSnapshotId
	builder.ruleSnapshotIdFlag = true
	return builder
}

// 事件类型
//
// 示例值：
func (builder *LocationRecordBuilder) Type(type_ string) *LocationRecordBuilder {
	builder.type_ = type_
	builder.typeFlag = true
	return builder
}

// 附近的wifi设备列表
//
// 示例值：
func (builder *LocationRecordBuilder) ScanWifiList(scanWifiList []*ScanWifiInfo) *LocationRecordBuilder {
	builder.scanWifiList = scanWifiList
	builder.scanWifiListFlag = true
	return builder
}

// 上报事件的设备ID
//
// 示例值：
func (builder *LocationRecordBuilder) DeviceId(deviceId string) *LocationRecordBuilder {
	builder.deviceId = deviceId
	builder.deviceIdFlag = true
	return builder
}

//
//
// 示例值：
func (builder *LocationRecordBuilder) ClientInfo(clientInfo string) *LocationRecordBuilder {
	builder.clientInfo = clientInfo
	builder.clientInfoFlag = true
	return builder
}

func (builder *LocationRecordBuilder) Build() *LocationRecord {
	req := &LocationRecord{}
	if builder.userIdFlag {
		req.UserId = builder.userId
	}
	if builder.timestampFlag {
		req.Timestamp = &builder.timestamp

	}
	if builder.locationFlag {
		req.Location = builder.location
	}
	if builder.wifiFlag {
		req.Wifi = builder.wifi
	}
	if builder.ruleSnapshotIdFlag {
		req.RuleSnapshotId = &builder.ruleSnapshotId

	}
	if builder.typeFlag {
		req.Type = &builder.type_

	}
	if builder.scanWifiListFlag {
		req.ScanWifiList = builder.scanWifiList
	}
	if builder.deviceIdFlag {
		req.DeviceId = &builder.deviceId

	}
	if builder.clientInfoFlag {
		req.ClientInfo = &builder.clientInfo

	}
	return req
}

type LocationSetting struct {
	Location *LocationInfo `json:"location,omitempty"` // 位置配置
	Wifi     *WifiInfo     `json:"wifi,omitempty"`     // wifi配置
	UserId   *string       `json:"user_id,omitempty"`  //
}

type LocationSettingBuilder struct {
	location     *LocationInfo // 位置配置
	locationFlag bool
	wifi         *WifiInfo // wifi配置
	wifiFlag     bool
	userId       string //
	userIdFlag   bool
}

func NewLocationSettingBuilder() *LocationSettingBuilder {
	builder := &LocationSettingBuilder{}
	return builder
}

// 位置配置
//
// 示例值：
func (builder *LocationSettingBuilder) Location(location *LocationInfo) *LocationSettingBuilder {
	builder.location = location
	builder.locationFlag = true
	return builder
}

// wifi配置
//
// 示例值：
func (builder *LocationSettingBuilder) Wifi(wifi *WifiInfo) *LocationSettingBuilder {
	builder.wifi = wifi
	builder.wifiFlag = true
	return builder
}

//
//
// 示例值：
func (builder *LocationSettingBuilder) UserId(userId string) *LocationSettingBuilder {
	builder.userId = userId
	builder.userIdFlag = true
	return builder
}

func (builder *LocationSettingBuilder) Build() *LocationSetting {
	req := &LocationSetting{}
	if builder.locationFlag {
		req.Location = builder.location
	}
	if builder.wifiFlag {
		req.Wifi = builder.wifi
	}
	if builder.userIdFlag {
		req.UserId = &builder.userId

	}
	return req
}

type Machine struct {
	MachineSn   *string `json:"machine_sn,omitempty"`   // 考勤机序列号
	MachineName *string `json:"machine_name,omitempty"` // 考勤机名称
}

type MachineBuilder struct {
	machineSn       string // 考勤机序列号
	machineSnFlag   bool
	machineName     string // 考勤机名称
	machineNameFlag bool
}

func NewMachineBuilder() *MachineBuilder {
	builder := &MachineBuilder{}
	return builder
}

// 考勤机序列号
//
// 示例值：FS0701
func (builder *MachineBuilder) MachineSn(machineSn string) *MachineBuilder {
	builder.machineSn = machineSn
	builder.machineSnFlag = true
	return builder
}

// 考勤机名称
//
// 示例值：创实 9 楼
func (builder *MachineBuilder) MachineName(machineName string) *MachineBuilder {
	builder.machineName = machineName
	builder.machineNameFlag = true
	return builder
}

func (builder *MachineBuilder) Build() *Machine {
	req := &Machine{}
	if builder.machineSnFlag {
		req.MachineSn = &builder.machineSn

	}
	if builder.machineNameFlag {
		req.MachineName = &builder.machineName

	}
	return req
}

type PunchSpecialDateShift struct {
	PunchDay *int    `json:"punch_day,omitempty"` // 打卡日期
	ShiftId  *string `json:"shift_id,omitempty"`  // 班次 ID
}

type PunchSpecialDateShiftBuilder struct {
	punchDay     int // 打卡日期
	punchDayFlag bool
	shiftId      string // 班次 ID
	shiftIdFlag  bool
}

func NewPunchSpecialDateShiftBuilder() *PunchSpecialDateShiftBuilder {
	builder := &PunchSpecialDateShiftBuilder{}
	return builder
}

// 打卡日期
//
// 示例值：20190101
func (builder *PunchSpecialDateShiftBuilder) PunchDay(punchDay int) *PunchSpecialDateShiftBuilder {
	builder.punchDay = punchDay
	builder.punchDayFlag = true
	return builder
}

// 班次 ID
//
// 示例值：6919668827865513935
func (builder *PunchSpecialDateShiftBuilder) ShiftId(shiftId string) *PunchSpecialDateShiftBuilder {
	builder.shiftId = shiftId
	builder.shiftIdFlag = true
	return builder
}

func (builder *PunchSpecialDateShiftBuilder) Build() *PunchSpecialDateShift {
	req := &PunchSpecialDateShift{}
	if builder.punchDayFlag {
		req.PunchDay = &builder.punchDay

	}
	if builder.shiftIdFlag {
		req.ShiftId = &builder.shiftId

	}
	return req
}

type PunchTimeRule struct {
	OnTime                   *string `json:"on_time,omitempty"`                      // 上班时间
	OffTime                  *string `json:"off_time,omitempty"`                     // 下班时间
	LateMinutesAsLate        *int    `json:"late_minutes_as_late,omitempty"`         // 晚到多久记为迟到
	LateMinutesAsLack        *int    `json:"late_minutes_as_lack,omitempty"`         // 晚到多久记为缺卡
	OnAdvanceMinutes         *int    `json:"on_advance_minutes,omitempty"`           // 最早多久可打上班卡
	EarlyMinutesAsEarly      *int    `json:"early_minutes_as_early,omitempty"`       // 早退多久记为早退
	EarlyMinutesAsLack       *int    `json:"early_minutes_as_lack,omitempty"`        // 早退多久记为缺卡
	OffDelayMinutes          *int    `json:"off_delay_minutes,omitempty"`            // 最晚多久可打下班卡
	LateMinutesAsSeriousLate *int    `json:"late_minutes_as_serious_late,omitempty"` // 晚到多久记为严重迟到
}

type PunchTimeRuleBuilder struct {
	onTime                       string // 上班时间
	onTimeFlag                   bool
	offTime                      string // 下班时间
	offTimeFlag                  bool
	lateMinutesAsLate            int // 晚到多久记为迟到
	lateMinutesAsLateFlag        bool
	lateMinutesAsLack            int // 晚到多久记为缺卡
	lateMinutesAsLackFlag        bool
	onAdvanceMinutes             int // 最早多久可打上班卡
	onAdvanceMinutesFlag         bool
	earlyMinutesAsEarly          int // 早退多久记为早退
	earlyMinutesAsEarlyFlag      bool
	earlyMinutesAsLack           int // 早退多久记为缺卡
	earlyMinutesAsLackFlag       bool
	offDelayMinutes              int // 最晚多久可打下班卡
	offDelayMinutesFlag          bool
	lateMinutesAsSeriousLate     int // 晚到多久记为严重迟到
	lateMinutesAsSeriousLateFlag bool
}

func NewPunchTimeRuleBuilder() *PunchTimeRuleBuilder {
	builder := &PunchTimeRuleBuilder{}
	return builder
}

// 上班时间
//
// 示例值：9:00
func (builder *PunchTimeRuleBuilder) OnTime(onTime string) *PunchTimeRuleBuilder {
	builder.onTime = onTime
	builder.onTimeFlag = true
	return builder
}

// 下班时间
//
// 示例值：18:00， 第二天凌晨2点， 26:00
func (builder *PunchTimeRuleBuilder) OffTime(offTime string) *PunchTimeRuleBuilder {
	builder.offTime = offTime
	builder.offTimeFlag = true
	return builder
}

// 晚到多久记为迟到
//
// 示例值：30
func (builder *PunchTimeRuleBuilder) LateMinutesAsLate(lateMinutesAsLate int) *PunchTimeRuleBuilder {
	builder.lateMinutesAsLate = lateMinutesAsLate
	builder.lateMinutesAsLateFlag = true
	return builder
}

// 晚到多久记为缺卡
//
// 示例值：60
func (builder *PunchTimeRuleBuilder) LateMinutesAsLack(lateMinutesAsLack int) *PunchTimeRuleBuilder {
	builder.lateMinutesAsLack = lateMinutesAsLack
	builder.lateMinutesAsLackFlag = true
	return builder
}

// 最早多久可打上班卡
//
// 示例值：60
func (builder *PunchTimeRuleBuilder) OnAdvanceMinutes(onAdvanceMinutes int) *PunchTimeRuleBuilder {
	builder.onAdvanceMinutes = onAdvanceMinutes
	builder.onAdvanceMinutesFlag = true
	return builder
}

// 早退多久记为早退
//
// 示例值：30
func (builder *PunchTimeRuleBuilder) EarlyMinutesAsEarly(earlyMinutesAsEarly int) *PunchTimeRuleBuilder {
	builder.earlyMinutesAsEarly = earlyMinutesAsEarly
	builder.earlyMinutesAsEarlyFlag = true
	return builder
}

// 早退多久记为缺卡
//
// 示例值：60
func (builder *PunchTimeRuleBuilder) EarlyMinutesAsLack(earlyMinutesAsLack int) *PunchTimeRuleBuilder {
	builder.earlyMinutesAsLack = earlyMinutesAsLack
	builder.earlyMinutesAsLackFlag = true
	return builder
}

// 最晚多久可打下班卡
//
// 示例值：60
func (builder *PunchTimeRuleBuilder) OffDelayMinutes(offDelayMinutes int) *PunchTimeRuleBuilder {
	builder.offDelayMinutes = offDelayMinutes
	builder.offDelayMinutesFlag = true
	return builder
}

// 晚到多久记为严重迟到
//
// 示例值：40
func (builder *PunchTimeRuleBuilder) LateMinutesAsSeriousLate(lateMinutesAsSeriousLate int) *PunchTimeRuleBuilder {
	builder.lateMinutesAsSeriousLate = lateMinutesAsSeriousLate
	builder.lateMinutesAsSeriousLateFlag = true
	return builder
}

func (builder *PunchTimeRuleBuilder) Build() *PunchTimeRule {
	req := &PunchTimeRule{}
	if builder.onTimeFlag {
		req.OnTime = &builder.onTime

	}
	if builder.offTimeFlag {
		req.OffTime = &builder.offTime

	}
	if builder.lateMinutesAsLateFlag {
		req.LateMinutesAsLate = &builder.lateMinutesAsLate

	}
	if builder.lateMinutesAsLackFlag {
		req.LateMinutesAsLack = &builder.lateMinutesAsLack

	}
	if builder.onAdvanceMinutesFlag {
		req.OnAdvanceMinutes = &builder.onAdvanceMinutes

	}
	if builder.earlyMinutesAsEarlyFlag {
		req.EarlyMinutesAsEarly = &builder.earlyMinutesAsEarly

	}
	if builder.earlyMinutesAsLackFlag {
		req.EarlyMinutesAsLack = &builder.earlyMinutesAsLack

	}
	if builder.offDelayMinutesFlag {
		req.OffDelayMinutes = &builder.offDelayMinutes

	}
	if builder.lateMinutesAsSeriousLateFlag {
		req.LateMinutesAsSeriousLate = &builder.lateMinutesAsSeriousLate

	}
	return req
}

type RestRule struct {
	RestBeginTime *string `json:"rest_begin_time,omitempty"` // 休息开始
	RestEndTime   *string `json:"rest_end_time,omitempty"`   // 休息结束
}

type RestRuleBuilder struct {
	restBeginTime     string // 休息开始
	restBeginTimeFlag bool
	restEndTime       string // 休息结束
	restEndTimeFlag   bool
}

func NewRestRuleBuilder() *RestRuleBuilder {
	builder := &RestRuleBuilder{}
	return builder
}

// 休息开始
//
// 示例值：13:00
func (builder *RestRuleBuilder) RestBeginTime(restBeginTime string) *RestRuleBuilder {
	builder.restBeginTime = restBeginTime
	builder.restBeginTimeFlag = true
	return builder
}

// 休息结束
//
// 示例值：14:00
func (builder *RestRuleBuilder) RestEndTime(restEndTime string) *RestRuleBuilder {
	builder.restEndTime = restEndTime
	builder.restEndTimeFlag = true
	return builder
}

func (builder *RestRuleBuilder) Build() *RestRule {
	req := &RestRule{}
	if builder.restBeginTimeFlag {
		req.RestBeginTime = &builder.restBeginTime

	}
	if builder.restEndTimeFlag {
		req.RestEndTime = &builder.restEndTime

	}
	return req
}

type ScanWifiInfo struct {
	Ssid  *string `json:"ssid,omitempty"`  //
	Bssid *string `json:"bssid,omitempty"` //
}

type ScanWifiInfoBuilder struct {
	ssid      string //
	ssidFlag  bool
	bssid     string //
	bssidFlag bool
}

func NewScanWifiInfoBuilder() *ScanWifiInfoBuilder {
	builder := &ScanWifiInfoBuilder{}
	return builder
}

//
//
// 示例值：
func (builder *ScanWifiInfoBuilder) Ssid(ssid string) *ScanWifiInfoBuilder {
	builder.ssid = ssid
	builder.ssidFlag = true
	return builder
}

//
//
// 示例值：
func (builder *ScanWifiInfoBuilder) Bssid(bssid string) *ScanWifiInfoBuilder {
	builder.bssid = bssid
	builder.bssidFlag = true
	return builder
}

func (builder *ScanWifiInfoBuilder) Build() *ScanWifiInfo {
	req := &ScanWifiInfo{}
	if builder.ssidFlag {
		req.Ssid = &builder.ssid

	}
	if builder.bssidFlag {
		req.Bssid = &builder.bssid

	}
	return req
}

type Schedule struct {
	GroupId *string  `json:"group_id,omitempty"` // 考勤组名称
	Shifts  []string `json:"shifts,omitempty"`   // 班次列表
}

type ScheduleBuilder struct {
	groupId     string // 考勤组名称
	groupIdFlag bool
	shifts      []string // 班次列表
	shiftsFlag  bool
}

func NewScheduleBuilder() *ScheduleBuilder {
	builder := &ScheduleBuilder{}
	return builder
}

// 考勤组名称
//
// 示例值：6919358778597097404
func (builder *ScheduleBuilder) GroupId(groupId string) *ScheduleBuilder {
	builder.groupId = groupId
	builder.groupIdFlag = true
	return builder
}

// 班次列表
//
// 示例值：
func (builder *ScheduleBuilder) Shifts(shifts []string) *ScheduleBuilder {
	builder.shifts = shifts
	builder.shiftsFlag = true
	return builder
}

func (builder *ScheduleBuilder) Build() *Schedule {
	req := &Schedule{}
	if builder.groupIdFlag {
		req.GroupId = &builder.groupId

	}
	if builder.shiftsFlag {
		req.Shifts = builder.shifts
	}
	return req
}

type Shift struct {
	ShiftId           *string              `json:"shift_id,omitempty"`              // 班次 ID
	ShiftName         *string              `json:"shift_name,omitempty"`            // 班次名称
	PunchTimes        *int                 `json:"punch_times,omitempty"`           // 打卡次数
	IsFlexible        *bool                `json:"is_flexible,omitempty"`           // 是否弹性打卡
	FlexibleMinutes   *int                 `json:"flexible_minutes,omitempty"`      // 弹性打卡时间，设置【上班最多可晚到】与【下班最多可早走】时间，如果不设置flexible_rule则生效
	FlexibleRule      []*FlexibleRule      `json:"flexible_rule,omitempty"`         // 弹性打卡时间设置
	NoNeedOff         *bool                `json:"no_need_off,omitempty"`           // 不需要打下班卡
	PunchTimeRule     []*PunchTimeRule     `json:"punch_time_rule,omitempty"`       // 打卡规则
	LateOffLateOnRule []*LateOffLateOnRule `json:"late_off_late_on_rule,omitempty"` // 晚走晚到规则
	RestTimeRule      []*RestRule          `json:"rest_time_rule,omitempty"`        // 休息规则
}

type ShiftBuilder struct {
	shiftId               string // 班次 ID
	shiftIdFlag           bool
	shiftName             string // 班次名称
	shiftNameFlag         bool
	punchTimes            int // 打卡次数
	punchTimesFlag        bool
	isFlexible            bool // 是否弹性打卡
	isFlexibleFlag        bool
	flexibleMinutes       int // 弹性打卡时间，设置【上班最多可晚到】与【下班最多可早走】时间，如果不设置flexible_rule则生效
	flexibleMinutesFlag   bool
	flexibleRule          []*FlexibleRule // 弹性打卡时间设置
	flexibleRuleFlag      bool
	noNeedOff             bool // 不需要打下班卡
	noNeedOffFlag         bool
	punchTimeRule         []*PunchTimeRule // 打卡规则
	punchTimeRuleFlag     bool
	lateOffLateOnRule     []*LateOffLateOnRule // 晚走晚到规则
	lateOffLateOnRuleFlag bool
	restTimeRule          []*RestRule // 休息规则
	restTimeRuleFlag      bool
}

func NewShiftBuilder() *ShiftBuilder {
	builder := &ShiftBuilder{}
	return builder
}

// 班次 ID
//
// 示例值：6919358778597097404
func (builder *ShiftBuilder) ShiftId(shiftId string) *ShiftBuilder {
	builder.shiftId = shiftId
	builder.shiftIdFlag = true
	return builder
}

// 班次名称
//
// 示例值：早班
func (builder *ShiftBuilder) ShiftName(shiftName string) *ShiftBuilder {
	builder.shiftName = shiftName
	builder.shiftNameFlag = true
	return builder
}

// 打卡次数
//
// 示例值：1
func (builder *ShiftBuilder) PunchTimes(punchTimes int) *ShiftBuilder {
	builder.punchTimes = punchTimes
	builder.punchTimesFlag = true
	return builder
}

// 是否弹性打卡
//
// 示例值：false
func (builder *ShiftBuilder) IsFlexible(isFlexible bool) *ShiftBuilder {
	builder.isFlexible = isFlexible
	builder.isFlexibleFlag = true
	return builder
}

// 弹性打卡时间，设置【上班最多可晚到】与【下班最多可早走】时间，如果不设置flexible_rule则生效
//
// 示例值：60
func (builder *ShiftBuilder) FlexibleMinutes(flexibleMinutes int) *ShiftBuilder {
	builder.flexibleMinutes = flexibleMinutes
	builder.flexibleMinutesFlag = true
	return builder
}

// 弹性打卡时间设置
//
// 示例值：
func (builder *ShiftBuilder) FlexibleRule(flexibleRule []*FlexibleRule) *ShiftBuilder {
	builder.flexibleRule = flexibleRule
	builder.flexibleRuleFlag = true
	return builder
}

// 不需要打下班卡
//
// 示例值：true
func (builder *ShiftBuilder) NoNeedOff(noNeedOff bool) *ShiftBuilder {
	builder.noNeedOff = noNeedOff
	builder.noNeedOffFlag = true
	return builder
}

// 打卡规则
//
// 示例值：
func (builder *ShiftBuilder) PunchTimeRule(punchTimeRule []*PunchTimeRule) *ShiftBuilder {
	builder.punchTimeRule = punchTimeRule
	builder.punchTimeRuleFlag = true
	return builder
}

// 晚走晚到规则
//
// 示例值：
func (builder *ShiftBuilder) LateOffLateOnRule(lateOffLateOnRule []*LateOffLateOnRule) *ShiftBuilder {
	builder.lateOffLateOnRule = lateOffLateOnRule
	builder.lateOffLateOnRuleFlag = true
	return builder
}

// 休息规则
//
// 示例值：
func (builder *ShiftBuilder) RestTimeRule(restTimeRule []*RestRule) *ShiftBuilder {
	builder.restTimeRule = restTimeRule
	builder.restTimeRuleFlag = true
	return builder
}

func (builder *ShiftBuilder) Build() *Shift {
	req := &Shift{}
	if builder.shiftIdFlag {
		req.ShiftId = &builder.shiftId

	}
	if builder.shiftNameFlag {
		req.ShiftName = &builder.shiftName

	}
	if builder.punchTimesFlag {
		req.PunchTimes = &builder.punchTimes

	}
	if builder.isFlexibleFlag {
		req.IsFlexible = &builder.isFlexible

	}
	if builder.flexibleMinutesFlag {
		req.FlexibleMinutes = &builder.flexibleMinutes

	}
	if builder.flexibleRuleFlag {
		req.FlexibleRule = builder.flexibleRule
	}
	if builder.noNeedOffFlag {
		req.NoNeedOff = &builder.noNeedOff

	}
	if builder.punchTimeRuleFlag {
		req.PunchTimeRule = builder.punchTimeRule
	}
	if builder.lateOffLateOnRuleFlag {
		req.LateOffLateOnRule = builder.lateOffLateOnRule
	}
	if builder.restTimeRuleFlag {
		req.RestTimeRule = builder.restTimeRule
	}
	return req
}

type StatusChange struct {
	Index             *int    `json:"index,omitempty"`              //
	BeforeStatus      *string `json:"before_status,omitempty"`      //
	CurrentStatus     *string `json:"current_status,omitempty"`     //
	BeforeSupplement  *string `json:"before_supplement,omitempty"`  //
	CurrentSupplement *string `json:"current_supplement,omitempty"` //
	WorkType          *string `json:"work_type,omitempty"`          //
}

type StatusChangeBuilder struct {
	index                 int //
	indexFlag             bool
	beforeStatus          string //
	beforeStatusFlag      bool
	currentStatus         string //
	currentStatusFlag     bool
	beforeSupplement      string //
	beforeSupplementFlag  bool
	currentSupplement     string //
	currentSupplementFlag bool
	workType              string //
	workTypeFlag          bool
}

func NewStatusChangeBuilder() *StatusChangeBuilder {
	builder := &StatusChangeBuilder{}
	return builder
}

//
//
// 示例值：
func (builder *StatusChangeBuilder) Index(index int) *StatusChangeBuilder {
	builder.index = index
	builder.indexFlag = true
	return builder
}

//
//
// 示例值：
func (builder *StatusChangeBuilder) BeforeStatus(beforeStatus string) *StatusChangeBuilder {
	builder.beforeStatus = beforeStatus
	builder.beforeStatusFlag = true
	return builder
}

//
//
// 示例值：
func (builder *StatusChangeBuilder) CurrentStatus(currentStatus string) *StatusChangeBuilder {
	builder.currentStatus = currentStatus
	builder.currentStatusFlag = true
	return builder
}

//
//
// 示例值：
func (builder *StatusChangeBuilder) BeforeSupplement(beforeSupplement string) *StatusChangeBuilder {
	builder.beforeSupplement = beforeSupplement
	builder.beforeSupplementFlag = true
	return builder
}

//
//
// 示例值：
func (builder *StatusChangeBuilder) CurrentSupplement(currentSupplement string) *StatusChangeBuilder {
	builder.currentSupplement = currentSupplement
	builder.currentSupplementFlag = true
	return builder
}

//
//
// 示例值：
func (builder *StatusChangeBuilder) WorkType(workType string) *StatusChangeBuilder {
	builder.workType = workType
	builder.workTypeFlag = true
	return builder
}

func (builder *StatusChangeBuilder) Build() *StatusChange {
	req := &StatusChange{}
	if builder.indexFlag {
		req.Index = &builder.index

	}
	if builder.beforeStatusFlag {
		req.BeforeStatus = &builder.beforeStatus

	}
	if builder.currentStatusFlag {
		req.CurrentStatus = &builder.currentStatus

	}
	if builder.beforeSupplementFlag {
		req.BeforeSupplement = &builder.beforeSupplement

	}
	if builder.currentSupplementFlag {
		req.CurrentSupplement = &builder.currentSupplement

	}
	if builder.workTypeFlag {
		req.WorkType = &builder.workType

	}
	return req
}

type TaskResult struct {
	CheckInRecordId          *string   `json:"check_in_record_id,omitempty"`          // 上班打卡记录 ID
	CheckInRecord            *UserFlow `json:"check_in_record,omitempty"`             // 上班打卡记录
	CheckOutRecordId         *string   `json:"check_out_record_id,omitempty"`         // 下班打卡记录 ID
	CheckOutRecord           *UserFlow `json:"check_out_record,omitempty"`            // 下班打卡记录
	CheckInResult            *string   `json:"check_in_result,omitempty"`             // 上班打卡结果
	CheckOutResult           *string   `json:"check_out_result,omitempty"`            // 下班打卡结果
	CheckInResultSupplement  *string   `json:"check_in_result_supplement,omitempty"`  // 上班打卡结果补充
	CheckOutResultSupplement *string   `json:"check_out_result_supplement,omitempty"` // 下班打卡结果补充
	CheckInShiftTime         *string   `json:"check_in_shift_time,omitempty"`         // 上班打卡时间
	CheckOutShiftTime        *string   `json:"check_out_shift_time,omitempty"`        // 下班打卡时间
}

type TaskResultBuilder struct {
	checkInRecordId              string // 上班打卡记录 ID
	checkInRecordIdFlag          bool
	checkInRecord                *UserFlow // 上班打卡记录
	checkInRecordFlag            bool
	checkOutRecordId             string // 下班打卡记录 ID
	checkOutRecordIdFlag         bool
	checkOutRecord               *UserFlow // 下班打卡记录
	checkOutRecordFlag           bool
	checkInResult                string // 上班打卡结果
	checkInResultFlag            bool
	checkOutResult               string // 下班打卡结果
	checkOutResultFlag           bool
	checkInResultSupplement      string // 上班打卡结果补充
	checkInResultSupplementFlag  bool
	checkOutResultSupplement     string // 下班打卡结果补充
	checkOutResultSupplementFlag bool
	checkInShiftTime             string // 上班打卡时间
	checkInShiftTimeFlag         bool
	checkOutShiftTime            string // 下班打卡时间
	checkOutShiftTimeFlag        bool
}

func NewTaskResultBuilder() *TaskResultBuilder {
	builder := &TaskResultBuilder{}
	return builder
}

// 上班打卡记录 ID
//
// 示例值：6709359313699356941
func (builder *TaskResultBuilder) CheckInRecordId(checkInRecordId string) *TaskResultBuilder {
	builder.checkInRecordId = checkInRecordId
	builder.checkInRecordIdFlag = true
	return builder
}

// 上班打卡记录
//
// 示例值：
func (builder *TaskResultBuilder) CheckInRecord(checkInRecord *UserFlow) *TaskResultBuilder {
	builder.checkInRecord = checkInRecord
	builder.checkInRecordFlag = true
	return builder
}

// 下班打卡记录 ID
//
// 示例值：6709359313699356942
func (builder *TaskResultBuilder) CheckOutRecordId(checkOutRecordId string) *TaskResultBuilder {
	builder.checkOutRecordId = checkOutRecordId
	builder.checkOutRecordIdFlag = true
	return builder
}

// 下班打卡记录
//
// 示例值：
func (builder *TaskResultBuilder) CheckOutRecord(checkOutRecord *UserFlow) *TaskResultBuilder {
	builder.checkOutRecord = checkOutRecord
	builder.checkOutRecordFlag = true
	return builder
}

// 上班打卡结果
//
// 示例值：SystemCheck
func (builder *TaskResultBuilder) CheckInResult(checkInResult string) *TaskResultBuilder {
	builder.checkInResult = checkInResult
	builder.checkInResultFlag = true
	return builder
}

// 下班打卡结果
//
// 示例值：SystemCheck
func (builder *TaskResultBuilder) CheckOutResult(checkOutResult string) *TaskResultBuilder {
	builder.checkOutResult = checkOutResult
	builder.checkOutResultFlag = true
	return builder
}

// 上班打卡结果补充
//
// 示例值：None
func (builder *TaskResultBuilder) CheckInResultSupplement(checkInResultSupplement string) *TaskResultBuilder {
	builder.checkInResultSupplement = checkInResultSupplement
	builder.checkInResultSupplementFlag = true
	return builder
}

// 下班打卡结果补充
//
// 示例值：None
func (builder *TaskResultBuilder) CheckOutResultSupplement(checkOutResultSupplement string) *TaskResultBuilder {
	builder.checkOutResultSupplement = checkOutResultSupplement
	builder.checkOutResultSupplementFlag = true
	return builder
}

// 上班打卡时间
//
// 示例值：1609722000
func (builder *TaskResultBuilder) CheckInShiftTime(checkInShiftTime string) *TaskResultBuilder {
	builder.checkInShiftTime = checkInShiftTime
	builder.checkInShiftTimeFlag = true
	return builder
}

// 下班打卡时间
//
// 示例值：1609754400
func (builder *TaskResultBuilder) CheckOutShiftTime(checkOutShiftTime string) *TaskResultBuilder {
	builder.checkOutShiftTime = checkOutShiftTime
	builder.checkOutShiftTimeFlag = true
	return builder
}

func (builder *TaskResultBuilder) Build() *TaskResult {
	req := &TaskResult{}
	if builder.checkInRecordIdFlag {
		req.CheckInRecordId = &builder.checkInRecordId

	}
	if builder.checkInRecordFlag {
		req.CheckInRecord = builder.checkInRecord
	}
	if builder.checkOutRecordIdFlag {
		req.CheckOutRecordId = &builder.checkOutRecordId

	}
	if builder.checkOutRecordFlag {
		req.CheckOutRecord = builder.checkOutRecord
	}
	if builder.checkInResultFlag {
		req.CheckInResult = &builder.checkInResult

	}
	if builder.checkOutResultFlag {
		req.CheckOutResult = &builder.checkOutResult

	}
	if builder.checkInResultSupplementFlag {
		req.CheckInResultSupplement = &builder.checkInResultSupplement

	}
	if builder.checkOutResultSupplementFlag {
		req.CheckOutResultSupplement = &builder.checkOutResultSupplement

	}
	if builder.checkInShiftTimeFlag {
		req.CheckInShiftTime = &builder.checkInShiftTime

	}
	if builder.checkOutShiftTimeFlag {
		req.CheckOutShiftTime = &builder.checkOutShiftTime

	}
	return req
}

type UserAllowedRemedy struct {
	UserId          *string `json:"user_id,omitempty"`           // 用户 ID
	RemedyDate      *int    `json:"remedy_date,omitempty"`       // 补卡日期
	IsFreePunch     *bool   `json:"is_free_punch,omitempty"`     // 是否为自由班次，若为自由班次，则不用选择考虑第几次上下班，直接选择补卡时间即可
	PunchNo         *int    `json:"punch_no,omitempty"`          // 第几次上下班，0：第 1 次上下班，1：第 2 次上下班，2：第 3 次上下班
	WorkType        *int    `json:"work_type,omitempty"`         // 上班 / 下班，1：上班，2：下班
	PunchStatus     *string `json:"punch_status,omitempty"`      // 打卡状态，Early：早退，Late：迟到，Lack：缺卡
	NormalPunchTime *string `json:"normal_punch_time,omitempty"` // 正常的应打卡时间，时间格式为 yyyy-MM-dd HH:mm
	RemedyStartTime *string `json:"remedy_start_time,omitempty"` // 可选的补卡时间的最小值，时间格式为 yyyy-MM-dd HH:mm
	RemedyEndTime   *string `json:"remedy_end_time,omitempty"`   // 可选的补卡时间的最大值，时间格式为 yyyy-MM-dd HH:mm
}

type UserAllowedRemedyBuilder struct {
	userId              string // 用户 ID
	userIdFlag          bool
	remedyDate          int // 补卡日期
	remedyDateFlag      bool
	isFreePunch         bool // 是否为自由班次，若为自由班次，则不用选择考虑第几次上下班，直接选择补卡时间即可
	isFreePunchFlag     bool
	punchNo             int // 第几次上下班，0：第 1 次上下班，1：第 2 次上下班，2：第 3 次上下班
	punchNoFlag         bool
	workType            int // 上班 / 下班，1：上班，2：下班
	workTypeFlag        bool
	punchStatus         string // 打卡状态，Early：早退，Late：迟到，Lack：缺卡
	punchStatusFlag     bool
	normalPunchTime     string // 正常的应打卡时间，时间格式为 yyyy-MM-dd HH:mm
	normalPunchTimeFlag bool
	remedyStartTime     string // 可选的补卡时间的最小值，时间格式为 yyyy-MM-dd HH:mm
	remedyStartTimeFlag bool
	remedyEndTime       string // 可选的补卡时间的最大值，时间格式为 yyyy-MM-dd HH:mm
	remedyEndTimeFlag   bool
}

func NewUserAllowedRemedyBuilder() *UserAllowedRemedyBuilder {
	builder := &UserAllowedRemedyBuilder{}
	return builder
}

// 用户 ID
//
// 示例值：abd754f7
func (builder *UserAllowedRemedyBuilder) UserId(userId string) *UserAllowedRemedyBuilder {
	builder.userId = userId
	builder.userIdFlag = true
	return builder
}

// 补卡日期
//
// 示例值：20210104
func (builder *UserAllowedRemedyBuilder) RemedyDate(remedyDate int) *UserAllowedRemedyBuilder {
	builder.remedyDate = remedyDate
	builder.remedyDateFlag = true
	return builder
}

// 是否为自由班次，若为自由班次，则不用选择考虑第几次上下班，直接选择补卡时间即可
//
// 示例值：false
func (builder *UserAllowedRemedyBuilder) IsFreePunch(isFreePunch bool) *UserAllowedRemedyBuilder {
	builder.isFreePunch = isFreePunch
	builder.isFreePunchFlag = true
	return builder
}

// 第几次上下班，0：第 1 次上下班，1：第 2 次上下班，2：第 3 次上下班
//
// 示例值：0
func (builder *UserAllowedRemedyBuilder) PunchNo(punchNo int) *UserAllowedRemedyBuilder {
	builder.punchNo = punchNo
	builder.punchNoFlag = true
	return builder
}

// 上班 / 下班，1：上班，2：下班
//
// 示例值：1
func (builder *UserAllowedRemedyBuilder) WorkType(workType int) *UserAllowedRemedyBuilder {
	builder.workType = workType
	builder.workTypeFlag = true
	return builder
}

// 打卡状态，Early：早退，Late：迟到，Lack：缺卡
//
// 示例值：Lack
func (builder *UserAllowedRemedyBuilder) PunchStatus(punchStatus string) *UserAllowedRemedyBuilder {
	builder.punchStatus = punchStatus
	builder.punchStatusFlag = true
	return builder
}

// 正常的应打卡时间，时间格式为 yyyy-MM-dd HH:mm
//
// 示例值：2021-07-01 09:00
func (builder *UserAllowedRemedyBuilder) NormalPunchTime(normalPunchTime string) *UserAllowedRemedyBuilder {
	builder.normalPunchTime = normalPunchTime
	builder.normalPunchTimeFlag = true
	return builder
}

// 可选的补卡时间的最小值，时间格式为 yyyy-MM-dd HH:mm
//
// 示例值：2021-07-01 08:00
func (builder *UserAllowedRemedyBuilder) RemedyStartTime(remedyStartTime string) *UserAllowedRemedyBuilder {
	builder.remedyStartTime = remedyStartTime
	builder.remedyStartTimeFlag = true
	return builder
}

// 可选的补卡时间的最大值，时间格式为 yyyy-MM-dd HH:mm
//
// 示例值：2021-07-01 10:00
func (builder *UserAllowedRemedyBuilder) RemedyEndTime(remedyEndTime string) *UserAllowedRemedyBuilder {
	builder.remedyEndTime = remedyEndTime
	builder.remedyEndTimeFlag = true
	return builder
}

func (builder *UserAllowedRemedyBuilder) Build() *UserAllowedRemedy {
	req := &UserAllowedRemedy{}
	if builder.userIdFlag {
		req.UserId = &builder.userId

	}
	if builder.remedyDateFlag {
		req.RemedyDate = &builder.remedyDate

	}
	if builder.isFreePunchFlag {
		req.IsFreePunch = &builder.isFreePunch

	}
	if builder.punchNoFlag {
		req.PunchNo = &builder.punchNo

	}
	if builder.workTypeFlag {
		req.WorkType = &builder.workType

	}
	if builder.punchStatusFlag {
		req.PunchStatus = &builder.punchStatus

	}
	if builder.normalPunchTimeFlag {
		req.NormalPunchTime = &builder.normalPunchTime

	}
	if builder.remedyStartTimeFlag {
		req.RemedyStartTime = &builder.remedyStartTime

	}
	if builder.remedyEndTimeFlag {
		req.RemedyEndTime = &builder.remedyEndTime

	}
	return req
}

type UserApproval struct {
	UserId        *string             `json:"user_id,omitempty"`        // 审批用户 ID
	Date          *string             `json:"date,omitempty"`           // 审批作用日期
	Outs          []*UserOut          `json:"outs,omitempty"`           // 外出信息
	Leaves        []*UserLeave        `json:"leaves,omitempty"`         // 请假信息
	OvertimeWorks []*UserOvertimeWork `json:"overtime_works,omitempty"` // 加班信息
	Trips         []*UserTrip         `json:"trips,omitempty"`          // 出差信息
	TimeZone      *string             `json:"time_zone,omitempty"`      // 计算时间所用的时区信息，为空是0时区
}

type UserApprovalBuilder struct {
	userId            string // 审批用户 ID
	userIdFlag        bool
	date              string // 审批作用日期
	dateFlag          bool
	outs              []*UserOut // 外出信息
	outsFlag          bool
	leaves            []*UserLeave // 请假信息
	leavesFlag        bool
	overtimeWorks     []*UserOvertimeWork // 加班信息
	overtimeWorksFlag bool
	trips             []*UserTrip // 出差信息
	tripsFlag         bool
	timeZone          string // 计算时间所用的时区信息，为空是0时区
	timeZoneFlag      bool
}

func NewUserApprovalBuilder() *UserApprovalBuilder {
	builder := &UserApprovalBuilder{}
	return builder
}

// 审批用户 ID
//
// 示例值：abd754f7
func (builder *UserApprovalBuilder) UserId(userId string) *UserApprovalBuilder {
	builder.userId = userId
	builder.userIdFlag = true
	return builder
}

// 审批作用日期
//
// 示例值：20210104
func (builder *UserApprovalBuilder) Date(date string) *UserApprovalBuilder {
	builder.date = date
	builder.dateFlag = true
	return builder
}

// 外出信息
//
// 示例值：
func (builder *UserApprovalBuilder) Outs(outs []*UserOut) *UserApprovalBuilder {
	builder.outs = outs
	builder.outsFlag = true
	return builder
}

// 请假信息
//
// 示例值：
func (builder *UserApprovalBuilder) Leaves(leaves []*UserLeave) *UserApprovalBuilder {
	builder.leaves = leaves
	builder.leavesFlag = true
	return builder
}

// 加班信息
//
// 示例值：
func (builder *UserApprovalBuilder) OvertimeWorks(overtimeWorks []*UserOvertimeWork) *UserApprovalBuilder {
	builder.overtimeWorks = overtimeWorks
	builder.overtimeWorksFlag = true
	return builder
}

// 出差信息
//
// 示例值：
func (builder *UserApprovalBuilder) Trips(trips []*UserTrip) *UserApprovalBuilder {
	builder.trips = trips
	builder.tripsFlag = true
	return builder
}

// 计算时间所用的时区信息，为空是0时区
//
// 示例值：Asia/Shanghai
func (builder *UserApprovalBuilder) TimeZone(timeZone string) *UserApprovalBuilder {
	builder.timeZone = timeZone
	builder.timeZoneFlag = true
	return builder
}

func (builder *UserApprovalBuilder) Build() *UserApproval {
	req := &UserApproval{}
	if builder.userIdFlag {
		req.UserId = &builder.userId

	}
	if builder.dateFlag {
		req.Date = &builder.date

	}
	if builder.outsFlag {
		req.Outs = builder.outs
	}
	if builder.leavesFlag {
		req.Leaves = builder.leaves
	}
	if builder.overtimeWorksFlag {
		req.OvertimeWorks = builder.overtimeWorks
	}
	if builder.tripsFlag {
		req.Trips = builder.trips
	}
	if builder.timeZoneFlag {
		req.TimeZone = &builder.timeZone

	}
	return req
}

type UserDailyShift struct {
	GroupId *string `json:"group_id,omitempty"` // 考勤组 ID，获取方式：1）[创建或修改考勤组](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/attendance-v1/group/create) 2）[按名称查询考勤组](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/attendance-v1/group/search) 3）[获取打卡结果](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/attendance-v1/user_task/query)
	ShiftId *string `json:"shift_id,omitempty"` // 班次 ID，获取方式：1）[按名称查询班次](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/attendance-v1/shift/query) 2）[创建班次](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/attendance-v1/shift/create)
	Month   *int    `json:"month,omitempty"`    // 月份
	UserId  *string `json:"user_id,omitempty"`  // 用户 ID
	DayNo   *int    `json:"day_no,omitempty"`   // 日期
}

type UserDailyShiftBuilder struct {
	groupId     string // 考勤组 ID，获取方式：1）[创建或修改考勤组](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/attendance-v1/group/create) 2）[按名称查询考勤组](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/attendance-v1/group/search) 3）[获取打卡结果](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/attendance-v1/user_task/query)
	groupIdFlag bool
	shiftId     string // 班次 ID，获取方式：1）[按名称查询班次](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/attendance-v1/shift/query) 2）[创建班次](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/attendance-v1/shift/create)
	shiftIdFlag bool
	month       int // 月份
	monthFlag   bool
	userId      string // 用户 ID
	userIdFlag  bool
	dayNo       int // 日期
	dayNoFlag   bool
}

func NewUserDailyShiftBuilder() *UserDailyShiftBuilder {
	builder := &UserDailyShiftBuilder{}
	return builder
}

// 考勤组 ID，获取方式：1）[创建或修改考勤组](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/attendance-v1/group/create) 2）[按名称查询考勤组](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/attendance-v1/group/search) 3）[获取打卡结果](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/attendance-v1/user_task/query)
//
// 示例值：6737202939523236110
func (builder *UserDailyShiftBuilder) GroupId(groupId string) *UserDailyShiftBuilder {
	builder.groupId = groupId
	builder.groupIdFlag = true
	return builder
}

// 班次 ID，获取方式：1）[按名称查询班次](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/attendance-v1/shift/query) 2）[创建班次](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/attendance-v1/shift/create)
//
// 示例值：6753520403404030215
func (builder *UserDailyShiftBuilder) ShiftId(shiftId string) *UserDailyShiftBuilder {
	builder.shiftId = shiftId
	builder.shiftIdFlag = true
	return builder
}

// 月份
//
// 示例值：202101
func (builder *UserDailyShiftBuilder) Month(month int) *UserDailyShiftBuilder {
	builder.month = month
	builder.monthFlag = true
	return builder
}

// 用户 ID
//
// 示例值：abd754f7
func (builder *UserDailyShiftBuilder) UserId(userId string) *UserDailyShiftBuilder {
	builder.userId = userId
	builder.userIdFlag = true
	return builder
}

// 日期
//
// 示例值：21
func (builder *UserDailyShiftBuilder) DayNo(dayNo int) *UserDailyShiftBuilder {
	builder.dayNo = dayNo
	builder.dayNoFlag = true
	return builder
}

func (builder *UserDailyShiftBuilder) Build() *UserDailyShift {
	req := &UserDailyShift{}
	if builder.groupIdFlag {
		req.GroupId = &builder.groupId

	}
	if builder.shiftIdFlag {
		req.ShiftId = &builder.shiftId

	}
	if builder.monthFlag {
		req.Month = &builder.month

	}
	if builder.userIdFlag {
		req.UserId = &builder.userId

	}
	if builder.dayNoFlag {
		req.DayNo = &builder.dayNo

	}
	return req
}

type UserFlow struct {
	UserId       *string `json:"user_id,omitempty"`       // 用户 ID
	CreatorId    *string `json:"creator_id,omitempty"`    // 记录创建者 ID
	LocationName *string `json:"location_name,omitempty"` // 打卡位置名称信息
	CheckTime    *string `json:"check_time,omitempty"`    // 打卡时间，精确到秒的时间戳
	Comment      *string `json:"comment,omitempty"`       // 打卡备注
	RecordId     *string `json:"record_id,omitempty"`     // 打卡记录 ID

	Ssid      *string  `json:"ssid,omitempty"`       // 打卡 Wi-Fi 的 SSID
	Bssid     *string  `json:"bssid,omitempty"`      // 打卡 Wi-Fi 的 MAC 地址
	IsField   *bool    `json:"is_field,omitempty"`   // 是否为外勤打卡
	IsWifi    *bool    `json:"is_wifi,omitempty"`    // 是否为 Wi-Fi 打卡
	Type      *int     `json:"type,omitempty"`       // 记录生成方式
	PhotoUrls []string `json:"photo_urls,omitempty"` // 打卡照片列表

	CheckResult *string `json:"check_result,omitempty"` // 打卡结果
}

type UserFlowBuilder struct {
	userId           string // 用户 ID
	userIdFlag       bool
	creatorId        string // 记录创建者 ID
	creatorIdFlag    bool
	locationName     string // 打卡位置名称信息
	locationNameFlag bool
	checkTime        string // 打卡时间，精确到秒的时间戳
	checkTimeFlag    bool
	comment          string // 打卡备注
	commentFlag      bool
	recordId         string // 打卡记录 ID
	recordIdFlag     bool

	ssid          string // 打卡 Wi-Fi 的 SSID
	ssidFlag      bool
	bssid         string // 打卡 Wi-Fi 的 MAC 地址
	bssidFlag     bool
	isField       bool // 是否为外勤打卡
	isFieldFlag   bool
	isWifi        bool // 是否为 Wi-Fi 打卡
	isWifiFlag    bool
	type_         int // 记录生成方式
	typeFlag      bool
	photoUrls     []string // 打卡照片列表
	photoUrlsFlag bool

	checkResult     string // 打卡结果
	checkResultFlag bool
}

func NewUserFlowBuilder() *UserFlowBuilder {
	builder := &UserFlowBuilder{}
	return builder
}

// 用户 ID
//
// 示例值：abd754f7
func (builder *UserFlowBuilder) UserId(userId string) *UserFlowBuilder {
	builder.userId = userId
	builder.userIdFlag = true
	return builder
}

// 记录创建者 ID
//
// 示例值：abd754f7
func (builder *UserFlowBuilder) CreatorId(creatorId string) *UserFlowBuilder {
	builder.creatorId = creatorId
	builder.creatorIdFlag = true
	return builder
}

// 打卡位置名称信息
//
// 示例值：西溪八方城
func (builder *UserFlowBuilder) LocationName(locationName string) *UserFlowBuilder {
	builder.locationName = locationName
	builder.locationNameFlag = true
	return builder
}

// 打卡时间，精确到秒的时间戳
//
// 示例值：1611476284
func (builder *UserFlowBuilder) CheckTime(checkTime string) *UserFlowBuilder {
	builder.checkTime = checkTime
	builder.checkTimeFlag = true
	return builder
}

// 打卡备注
//
// 示例值：上班打卡
func (builder *UserFlowBuilder) Comment(comment string) *UserFlowBuilder {
	builder.comment = comment
	builder.commentFlag = true
	return builder
}

// 打卡记录 ID
//
// 示例值：6709359313699356941
func (builder *UserFlowBuilder) RecordId(recordId string) *UserFlowBuilder {
	builder.recordId = recordId
	builder.recordIdFlag = true
	return builder
}

// 打卡 Wi-Fi 的 SSID
//
// 示例值：b0:b8:67:5c:1d:72
func (builder *UserFlowBuilder) Ssid(ssid string) *UserFlowBuilder {
	builder.ssid = ssid
	builder.ssidFlag = true
	return builder
}

// 打卡 Wi-Fi 的 MAC 地址
//
// 示例值：b0:b8:67:5c:1d:72
func (builder *UserFlowBuilder) Bssid(bssid string) *UserFlowBuilder {
	builder.bssid = bssid
	builder.bssidFlag = true
	return builder
}

// 是否为外勤打卡
//
// 示例值：true
func (builder *UserFlowBuilder) IsField(isField bool) *UserFlowBuilder {
	builder.isField = isField
	builder.isFieldFlag = true
	return builder
}

// 是否为 Wi-Fi 打卡
//
// 示例值：true
func (builder *UserFlowBuilder) IsWifi(isWifi bool) *UserFlowBuilder {
	builder.isWifi = isWifi
	builder.isWifiFlag = true
	return builder
}

// 记录生成方式
//
// 示例值：在开放平台调用时，此参数无效，内部值始终是7
func (builder *UserFlowBuilder) Type(type_ int) *UserFlowBuilder {
	builder.type_ = type_
	builder.typeFlag = true
	return builder
}

// 打卡照片列表
//
// 示例值：https://time.clockin.biz/manage/download/6840389754748502021
func (builder *UserFlowBuilder) PhotoUrls(photoUrls []string) *UserFlowBuilder {
	builder.photoUrls = photoUrls
	builder.photoUrlsFlag = true
	return builder
}

// 打卡结果
//
// 示例值：Invalid
func (builder *UserFlowBuilder) CheckResult(checkResult string) *UserFlowBuilder {
	builder.checkResult = checkResult
	builder.checkResultFlag = true
	return builder
}

func (builder *UserFlowBuilder) Build() *UserFlow {
	req := &UserFlow{}
	if builder.userIdFlag {
		req.UserId = &builder.userId

	}
	if builder.creatorIdFlag {
		req.CreatorId = &builder.creatorId

	}
	if builder.locationNameFlag {
		req.LocationName = &builder.locationName

	}
	if builder.checkTimeFlag {
		req.CheckTime = &builder.checkTime

	}
	if builder.commentFlag {
		req.Comment = &builder.comment

	}
	if builder.recordIdFlag {
		req.RecordId = &builder.recordId

	}

	if builder.ssidFlag {
		req.Ssid = &builder.ssid

	}
	if builder.bssidFlag {
		req.Bssid = &builder.bssid

	}
	if builder.isFieldFlag {
		req.IsField = &builder.isField

	}
	if builder.isWifiFlag {
		req.IsWifi = &builder.isWifi

	}
	if builder.typeFlag {
		req.Type = &builder.type_

	}
	if builder.photoUrlsFlag {
		req.PhotoUrls = builder.photoUrls
	}

	if builder.checkResultFlag {
		req.CheckResult = &builder.checkResult

	}
	return req
}

type UserId struct {
	UserId  *string `json:"user_id,omitempty"`  //
	OpenId  *string `json:"open_id,omitempty"`  //
	UnionId *string `json:"union_id,omitempty"` //
}

type UserIdBuilder struct {
	userId      string //
	userIdFlag  bool
	openId      string //
	openIdFlag  bool
	unionId     string //
	unionIdFlag bool
}

func NewUserIdBuilder() *UserIdBuilder {
	builder := &UserIdBuilder{}
	return builder
}

//
//
// 示例值：
func (builder *UserIdBuilder) UserId(userId string) *UserIdBuilder {
	builder.userId = userId
	builder.userIdFlag = true
	return builder
}

//
//
// 示例值：
func (builder *UserIdBuilder) OpenId(openId string) *UserIdBuilder {
	builder.openId = openId
	builder.openIdFlag = true
	return builder
}

//
//
// 示例值：
func (builder *UserIdBuilder) UnionId(unionId string) *UserIdBuilder {
	builder.unionId = unionId
	builder.unionIdFlag = true
	return builder
}

func (builder *UserIdBuilder) Build() *UserId {
	req := &UserId{}
	if builder.userIdFlag {
		req.UserId = &builder.userId

	}
	if builder.openIdFlag {
		req.OpenId = &builder.openId

	}
	if builder.unionIdFlag {
		req.UnionId = &builder.unionId

	}
	return req
}

type UserLeave struct {
	ApprovalId       *string    `json:"approval_id,omitempty"`        // 审批实例 ID
	UniqId           *string    `json:"uniq_id,omitempty"`            // 假期类型唯一 ID，代表一种假期类型，长度小于 14
	Unit             *int       `json:"unit,omitempty"`               // 假期时长单位
	Interval         *int       `json:"interval,omitempty"`           // 假期时长（单位：秒），暂未开放提供，待后续提供
	StartTime        *string    `json:"start_time,omitempty"`         // 开始时间，时间格式为 yyyy-MM-dd HH:mm:ss
	EndTime          *string    `json:"end_time,omitempty"`           // 结束时间，时间格式为 yyyy-MM-dd HH:mm:ss
	I18nNames        *I18nNames `json:"i18n_names,omitempty"`         // 假期多语言展示，格式为 map，key 为 ["ch"、"en"、"ja"]，其中 ch 代表中文、en 代表英语、ja 代表日语
	DefaultLocale    *string    `json:"default_locale,omitempty"`     // 默认语言类型，由于飞书客户端支持中、英、日三种语言，当用户切换语言时，如果假期名称没有所对应的语言，会使用默认语言的名称
	Reason           *string    `json:"reason,omitempty"`             // 请假理由，必选字段
	ApprovePassTime  *string    `json:"approve_pass_time,omitempty"`  // 审批通过时间，时间格式为 yyyy-MM-dd HH:mm:ss
	ApproveApplyTime *string    `json:"approve_apply_time,omitempty"` // 审批申请时间，时间格式为 yyyy-MM-dd HH:mm:ss
}

type UserLeaveBuilder struct {
	approvalId           string // 审批实例 ID
	approvalIdFlag       bool
	uniqId               string // 假期类型唯一 ID，代表一种假期类型，长度小于 14
	uniqIdFlag           bool
	unit                 int // 假期时长单位
	unitFlag             bool
	interval             int // 假期时长（单位：秒），暂未开放提供，待后续提供
	intervalFlag         bool
	startTime            string // 开始时间，时间格式为 yyyy-MM-dd HH:mm:ss
	startTimeFlag        bool
	endTime              string // 结束时间，时间格式为 yyyy-MM-dd HH:mm:ss
	endTimeFlag          bool
	i18nNames            *I18nNames // 假期多语言展示，格式为 map，key 为 ["ch"、"en"、"ja"]，其中 ch 代表中文、en 代表英语、ja 代表日语
	i18nNamesFlag        bool
	defaultLocale        string // 默认语言类型，由于飞书客户端支持中、英、日三种语言，当用户切换语言时，如果假期名称没有所对应的语言，会使用默认语言的名称
	defaultLocaleFlag    bool
	reason               string // 请假理由，必选字段
	reasonFlag           bool
	approvePassTime      string // 审批通过时间，时间格式为 yyyy-MM-dd HH:mm:ss
	approvePassTimeFlag  bool
	approveApplyTime     string // 审批申请时间，时间格式为 yyyy-MM-dd HH:mm:ss
	approveApplyTimeFlag bool
}

func NewUserLeaveBuilder() *UserLeaveBuilder {
	builder := &UserLeaveBuilder{}
	return builder
}

// 审批实例 ID
//
// 示例值：6737202939523236113
func (builder *UserLeaveBuilder) ApprovalId(approvalId string) *UserLeaveBuilder {
	builder.approvalId = approvalId
	builder.approvalIdFlag = true
	return builder
}

// 假期类型唯一 ID，代表一种假期类型，长度小于 14
//
// 示例值：6852582717813440527
func (builder *UserLeaveBuilder) UniqId(uniqId string) *UserLeaveBuilder {
	builder.uniqId = uniqId
	builder.uniqIdFlag = true
	return builder
}

// 假期时长单位
//
// 示例值：1
func (builder *UserLeaveBuilder) Unit(unit int) *UserLeaveBuilder {
	builder.unit = unit
	builder.unitFlag = true
	return builder
}

// 假期时长（单位：秒），暂未开放提供，待后续提供
//
// 示例值：28800
func (builder *UserLeaveBuilder) Interval(interval int) *UserLeaveBuilder {
	builder.interval = interval
	builder.intervalFlag = true
	return builder
}

// 开始时间，时间格式为 yyyy-MM-dd HH:mm:ss
//
// 示例值：2021-01-04 09:00:00
func (builder *UserLeaveBuilder) StartTime(startTime string) *UserLeaveBuilder {
	builder.startTime = startTime
	builder.startTimeFlag = true
	return builder
}

// 结束时间，时间格式为 yyyy-MM-dd HH:mm:ss
//
// 示例值：2021-01-04 19:00:00
func (builder *UserLeaveBuilder) EndTime(endTime string) *UserLeaveBuilder {
	builder.endTime = endTime
	builder.endTimeFlag = true
	return builder
}

// 假期多语言展示，格式为 map，key 为 ["ch"、"en"、"ja"]，其中 ch 代表中文、en 代表英语、ja 代表日语
//
// 示例值：
func (builder *UserLeaveBuilder) I18nNames(i18nNames *I18nNames) *UserLeaveBuilder {
	builder.i18nNames = i18nNames
	builder.i18nNamesFlag = true
	return builder
}

// 默认语言类型，由于飞书客户端支持中、英、日三种语言，当用户切换语言时，如果假期名称没有所对应的语言，会使用默认语言的名称
//
// 示例值：ch
func (builder *UserLeaveBuilder) DefaultLocale(defaultLocale string) *UserLeaveBuilder {
	builder.defaultLocale = defaultLocale
	builder.defaultLocaleFlag = true
	return builder
}

// 请假理由，必选字段
//
// 示例值：家里有事
func (builder *UserLeaveBuilder) Reason(reason string) *UserLeaveBuilder {
	builder.reason = reason
	builder.reasonFlag = true
	return builder
}

// 审批通过时间，时间格式为 yyyy-MM-dd HH:mm:ss
//
// 示例值：2021-01-04 12:00:00
func (builder *UserLeaveBuilder) ApprovePassTime(approvePassTime string) *UserLeaveBuilder {
	builder.approvePassTime = approvePassTime
	builder.approvePassTimeFlag = true
	return builder
}

// 审批申请时间，时间格式为 yyyy-MM-dd HH:mm:ss
//
// 示例值：2021-01-04 11:00:00
func (builder *UserLeaveBuilder) ApproveApplyTime(approveApplyTime string) *UserLeaveBuilder {
	builder.approveApplyTime = approveApplyTime
	builder.approveApplyTimeFlag = true
	return builder
}

func (builder *UserLeaveBuilder) Build() *UserLeave {
	req := &UserLeave{}
	if builder.approvalIdFlag {
		req.ApprovalId = &builder.approvalId

	}
	if builder.uniqIdFlag {
		req.UniqId = &builder.uniqId

	}
	if builder.unitFlag {
		req.Unit = &builder.unit

	}
	if builder.intervalFlag {
		req.Interval = &builder.interval

	}
	if builder.startTimeFlag {
		req.StartTime = &builder.startTime

	}
	if builder.endTimeFlag {
		req.EndTime = &builder.endTime

	}
	if builder.i18nNamesFlag {
		req.I18nNames = builder.i18nNames
	}
	if builder.defaultLocaleFlag {
		req.DefaultLocale = &builder.defaultLocale

	}
	if builder.reasonFlag {
		req.Reason = &builder.reason

	}
	if builder.approvePassTimeFlag {
		req.ApprovePassTime = &builder.approvePassTime

	}
	if builder.approveApplyTimeFlag {
		req.ApproveApplyTime = &builder.approveApplyTime

	}
	return req
}

type UserOut struct {
	ApprovalId       *string    `json:"approval_id,omitempty"`        // 审批实例 ID
	UniqId           *string    `json:"uniq_id,omitempty"`            // 外出类型唯一 ID，代表一种假期类型，长度小于 14
	Unit             *int       `json:"unit,omitempty"`               // 外出时长单位
	Interval         *int       `json:"interval,omitempty"`           // 外出时长（单位：秒）
	StartTime        *string    `json:"start_time,omitempty"`         // 开始时间，时间格式为 yyyy-MM-dd HH:mm:ss
	EndTime          *string    `json:"end_time,omitempty"`           // 结束时间，时间格式为 yyyy-MM-dd HH:mm:ss
	I18nNames        *I18nNames `json:"i18n_names,omitempty"`         // 外出多语言展示，格式为 map，key 为 ["ch"、"en"、"ja"]，其中 ch 代表中文、en 代表英语、ja 代表日语
	DefaultLocale    *string    `json:"default_locale,omitempty"`     // 默认语言类型，由于飞书客户端支持中、英、日三种语言，当用户切换语言时，如果假期名称没有所对应的语言，会使用默认语言的名称
	Reason           *string    `json:"reason,omitempty"`             // 外出理由
	ApprovePassTime  *string    `json:"approve_pass_time,omitempty"`  // 审批通过时间
	ApproveApplyTime *string    `json:"approve_apply_time,omitempty"` // 审批申请时间
}

type UserOutBuilder struct {
	approvalId           string // 审批实例 ID
	approvalIdFlag       bool
	uniqId               string // 外出类型唯一 ID，代表一种假期类型，长度小于 14
	uniqIdFlag           bool
	unit                 int // 外出时长单位
	unitFlag             bool
	interval             int // 外出时长（单位：秒）
	intervalFlag         bool
	startTime            string // 开始时间，时间格式为 yyyy-MM-dd HH:mm:ss
	startTimeFlag        bool
	endTime              string // 结束时间，时间格式为 yyyy-MM-dd HH:mm:ss
	endTimeFlag          bool
	i18nNames            *I18nNames // 外出多语言展示，格式为 map，key 为 ["ch"、"en"、"ja"]，其中 ch 代表中文、en 代表英语、ja 代表日语
	i18nNamesFlag        bool
	defaultLocale        string // 默认语言类型，由于飞书客户端支持中、英、日三种语言，当用户切换语言时，如果假期名称没有所对应的语言，会使用默认语言的名称
	defaultLocaleFlag    bool
	reason               string // 外出理由
	reasonFlag           bool
	approvePassTime      string // 审批通过时间
	approvePassTimeFlag  bool
	approveApplyTime     string // 审批申请时间
	approveApplyTimeFlag bool
}

func NewUserOutBuilder() *UserOutBuilder {
	builder := &UserOutBuilder{}
	return builder
}

// 审批实例 ID
//
// 示例值：6737202939523236113
func (builder *UserOutBuilder) ApprovalId(approvalId string) *UserOutBuilder {
	builder.approvalId = approvalId
	builder.approvalIdFlag = true
	return builder
}

// 外出类型唯一 ID，代表一种假期类型，长度小于 14
//
// 示例值：9496E43696967658A512969523E89870
func (builder *UserOutBuilder) UniqId(uniqId string) *UserOutBuilder {
	builder.uniqId = uniqId
	builder.uniqIdFlag = true
	return builder
}

// 外出时长单位
//
// 示例值：1
func (builder *UserOutBuilder) Unit(unit int) *UserOutBuilder {
	builder.unit = unit
	builder.unitFlag = true
	return builder
}

// 外出时长（单位：秒）
//
// 示例值：28800
func (builder *UserOutBuilder) Interval(interval int) *UserOutBuilder {
	builder.interval = interval
	builder.intervalFlag = true
	return builder
}

// 开始时间，时间格式为 yyyy-MM-dd HH:mm:ss
//
// 示例值：2021-01-04 09:00:00
func (builder *UserOutBuilder) StartTime(startTime string) *UserOutBuilder {
	builder.startTime = startTime
	builder.startTimeFlag = true
	return builder
}

// 结束时间，时间格式为 yyyy-MM-dd HH:mm:ss
//
// 示例值：2021-01-04 19:00:00
func (builder *UserOutBuilder) EndTime(endTime string) *UserOutBuilder {
	builder.endTime = endTime
	builder.endTimeFlag = true
	return builder
}

// 外出多语言展示，格式为 map，key 为 ["ch"、"en"、"ja"]，其中 ch 代表中文、en 代表英语、ja 代表日语
//
// 示例值：
func (builder *UserOutBuilder) I18nNames(i18nNames *I18nNames) *UserOutBuilder {
	builder.i18nNames = i18nNames
	builder.i18nNamesFlag = true
	return builder
}

// 默认语言类型，由于飞书客户端支持中、英、日三种语言，当用户切换语言时，如果假期名称没有所对应的语言，会使用默认语言的名称
//
// 示例值：ch
func (builder *UserOutBuilder) DefaultLocale(defaultLocale string) *UserOutBuilder {
	builder.defaultLocale = defaultLocale
	builder.defaultLocaleFlag = true
	return builder
}

// 外出理由
//
// 示例值：外出办事
func (builder *UserOutBuilder) Reason(reason string) *UserOutBuilder {
	builder.reason = reason
	builder.reasonFlag = true
	return builder
}

// 审批通过时间
//
// 示例值：2021-01-04 12:00:00
func (builder *UserOutBuilder) ApprovePassTime(approvePassTime string) *UserOutBuilder {
	builder.approvePassTime = approvePassTime
	builder.approvePassTimeFlag = true
	return builder
}

// 审批申请时间
//
// 示例值：2021-01-04 11:00:00
func (builder *UserOutBuilder) ApproveApplyTime(approveApplyTime string) *UserOutBuilder {
	builder.approveApplyTime = approveApplyTime
	builder.approveApplyTimeFlag = true
	return builder
}

func (builder *UserOutBuilder) Build() *UserOut {
	req := &UserOut{}
	if builder.approvalIdFlag {
		req.ApprovalId = &builder.approvalId

	}
	if builder.uniqIdFlag {
		req.UniqId = &builder.uniqId

	}
	if builder.unitFlag {
		req.Unit = &builder.unit

	}
	if builder.intervalFlag {
		req.Interval = &builder.interval

	}
	if builder.startTimeFlag {
		req.StartTime = &builder.startTime

	}
	if builder.endTimeFlag {
		req.EndTime = &builder.endTime

	}
	if builder.i18nNamesFlag {
		req.I18nNames = builder.i18nNames
	}
	if builder.defaultLocaleFlag {
		req.DefaultLocale = &builder.defaultLocale

	}
	if builder.reasonFlag {
		req.Reason = &builder.reason

	}
	if builder.approvePassTimeFlag {
		req.ApprovePassTime = &builder.approvePassTime

	}
	if builder.approveApplyTimeFlag {
		req.ApproveApplyTime = &builder.approveApplyTime

	}
	return req
}

type UserOvertimeWork struct {
	ApprovalId *string  `json:"approval_id,omitempty"` // 审批实例 ID
	Duration   *float64 `json:"duration,omitempty"`    // 加班时长
	Unit       *int     `json:"unit,omitempty"`        // 加班时长单位
	Category   *int     `json:"category,omitempty"`    // 加班日期类型
	Type       *int     `json:"type,omitempty"`        // 加班规则类型
	StartTime  *string  `json:"start_time,omitempty"`  // 开始时间，时间格式为 yyyy-MM-dd HH:mm:ss
	EndTime    *string  `json:"end_time,omitempty"`    // 结束时间，时间格式为 yyyy-MM-dd HH:mm:ss
}

type UserOvertimeWorkBuilder struct {
	approvalId     string // 审批实例 ID
	approvalIdFlag bool
	duration       float64 // 加班时长
	durationFlag   bool
	unit           int // 加班时长单位
	unitFlag       bool
	category       int // 加班日期类型
	categoryFlag   bool
	type_          int // 加班规则类型
	typeFlag       bool
	startTime      string // 开始时间，时间格式为 yyyy-MM-dd HH:mm:ss
	startTimeFlag  bool
	endTime        string // 结束时间，时间格式为 yyyy-MM-dd HH:mm:ss
	endTimeFlag    bool
}

func NewUserOvertimeWorkBuilder() *UserOvertimeWorkBuilder {
	builder := &UserOvertimeWorkBuilder{}
	return builder
}

// 审批实例 ID
//
// 示例值：6737202939523236113
func (builder *UserOvertimeWorkBuilder) ApprovalId(approvalId string) *UserOvertimeWorkBuilder {
	builder.approvalId = approvalId
	builder.approvalIdFlag = true
	return builder
}

// 加班时长
//
// 示例值：1.5
func (builder *UserOvertimeWorkBuilder) Duration(duration float64) *UserOvertimeWorkBuilder {
	builder.duration = duration
	builder.durationFlag = true
	return builder
}

// 加班时长单位
//
// 示例值：1
func (builder *UserOvertimeWorkBuilder) Unit(unit int) *UserOvertimeWorkBuilder {
	builder.unit = unit
	builder.unitFlag = true
	return builder
}

// 加班日期类型
//
// 示例值：2
func (builder *UserOvertimeWorkBuilder) Category(category int) *UserOvertimeWorkBuilder {
	builder.category = category
	builder.categoryFlag = true
	return builder
}

// 加班规则类型
//
// 示例值：1
func (builder *UserOvertimeWorkBuilder) Type(type_ int) *UserOvertimeWorkBuilder {
	builder.type_ = type_
	builder.typeFlag = true
	return builder
}

// 开始时间，时间格式为 yyyy-MM-dd HH:mm:ss
//
// 示例值：2021-01-09 09:00:00
func (builder *UserOvertimeWorkBuilder) StartTime(startTime string) *UserOvertimeWorkBuilder {
	builder.startTime = startTime
	builder.startTimeFlag = true
	return builder
}

// 结束时间，时间格式为 yyyy-MM-dd HH:mm:ss
//
// 示例值：2021-01-10 13:00:00
func (builder *UserOvertimeWorkBuilder) EndTime(endTime string) *UserOvertimeWorkBuilder {
	builder.endTime = endTime
	builder.endTimeFlag = true
	return builder
}

func (builder *UserOvertimeWorkBuilder) Build() *UserOvertimeWork {
	req := &UserOvertimeWork{}
	if builder.approvalIdFlag {
		req.ApprovalId = &builder.approvalId

	}
	if builder.durationFlag {
		req.Duration = &builder.duration

	}
	if builder.unitFlag {
		req.Unit = &builder.unit

	}
	if builder.categoryFlag {
		req.Category = &builder.category

	}
	if builder.typeFlag {
		req.Type = &builder.type_

	}
	if builder.startTimeFlag {
		req.StartTime = &builder.startTime

	}
	if builder.endTimeFlag {
		req.EndTime = &builder.endTime

	}
	return req
}

type UserSetting struct {
	UserId            *string `json:"user_id,omitempty"`              // 用户 ID
	FaceKey           *string `json:"face_key,omitempty"`             // 人脸照片文件 ID，获取方式：[文件上传](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/attendance-v1/file/upload)
	FaceKeyUpdateTime *string `json:"face_key_update_time,omitempty"` // 人脸照片更新时间，精确到秒的时间戳
}

type UserSettingBuilder struct {
	userId                string // 用户 ID
	userIdFlag            bool
	faceKey               string // 人脸照片文件 ID，获取方式：[文件上传](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/attendance-v1/file/upload)
	faceKeyFlag           bool
	faceKeyUpdateTime     string // 人脸照片更新时间，精确到秒的时间戳
	faceKeyUpdateTimeFlag bool
}

func NewUserSettingBuilder() *UserSettingBuilder {
	builder := &UserSettingBuilder{}
	return builder
}

// 用户 ID
//
// 示例值：abd754f7
func (builder *UserSettingBuilder) UserId(userId string) *UserSettingBuilder {
	builder.userId = userId
	builder.userIdFlag = true
	return builder
}

// 人脸照片文件 ID，获取方式：[文件上传](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/attendance-v1/file/upload)
//
// 示例值：xxxxxb306842b1c189bc5212eefxxxxx
func (builder *UserSettingBuilder) FaceKey(faceKey string) *UserSettingBuilder {
	builder.faceKey = faceKey
	builder.faceKeyFlag = true
	return builder
}

// 人脸照片更新时间，精确到秒的时间戳
//
// 示例值：1625681917
func (builder *UserSettingBuilder) FaceKeyUpdateTime(faceKeyUpdateTime string) *UserSettingBuilder {
	builder.faceKeyUpdateTime = faceKeyUpdateTime
	builder.faceKeyUpdateTimeFlag = true
	return builder
}

func (builder *UserSettingBuilder) Build() *UserSetting {
	req := &UserSetting{}
	if builder.userIdFlag {
		req.UserId = &builder.userId

	}
	if builder.faceKeyFlag {
		req.FaceKey = &builder.faceKey

	}
	if builder.faceKeyUpdateTimeFlag {
		req.FaceKeyUpdateTime = &builder.faceKeyUpdateTime

	}
	return req
}

type UserStatsData struct {
	Name   *string              `json:"name,omitempty"`    // 用户姓名
	UserId *string              `json:"user_id,omitempty"` // 用户 ID
	Datas  []*UserStatsDataCell `json:"datas,omitempty"`   // 用户的统计数据
}

type UserStatsDataBuilder struct {
	name       string // 用户姓名
	nameFlag   bool
	userId     string // 用户 ID
	userIdFlag bool
	datas      []*UserStatsDataCell // 用户的统计数据
	datasFlag  bool
}

func NewUserStatsDataBuilder() *UserStatsDataBuilder {
	builder := &UserStatsDataBuilder{}
	return builder
}

// 用户姓名
//
// 示例值：小李
func (builder *UserStatsDataBuilder) Name(name string) *UserStatsDataBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

// 用户 ID
//
// 示例值：ec8ddg56
func (builder *UserStatsDataBuilder) UserId(userId string) *UserStatsDataBuilder {
	builder.userId = userId
	builder.userIdFlag = true
	return builder
}

// 用户的统计数据
//
// 示例值：
func (builder *UserStatsDataBuilder) Datas(datas []*UserStatsDataCell) *UserStatsDataBuilder {
	builder.datas = datas
	builder.datasFlag = true
	return builder
}

func (builder *UserStatsDataBuilder) Build() *UserStatsData {
	req := &UserStatsData{}
	if builder.nameFlag {
		req.Name = &builder.name

	}
	if builder.userIdFlag {
		req.UserId = &builder.userId

	}
	if builder.datasFlag {
		req.Datas = builder.datas
	}
	return req
}

type UserStatsDataCell struct {
	Code     *string                 `json:"code,omitempty"`     // 字段编号
	Value    *string                 `json:"value,omitempty"`    // 数据值
	Features []*UserStatsDataFeature `json:"features,omitempty"` // 数据属性
	Title    *string                 `json:"title,omitempty"`    // 字段标题
}

type UserStatsDataCellBuilder struct {
	code         string // 字段编号
	codeFlag     bool
	value        string // 数据值
	valueFlag    bool
	features     []*UserStatsDataFeature // 数据属性
	featuresFlag bool
	title        string // 字段标题
	titleFlag    bool
}

func NewUserStatsDataCellBuilder() *UserStatsDataCellBuilder {
	builder := &UserStatsDataCellBuilder{}
	return builder
}

// 字段编号
//
// 示例值：50102
func (builder *UserStatsDataCellBuilder) Code(code string) *UserStatsDataCellBuilder {
	builder.code = code
	builder.codeFlag = true
	return builder
}

// 数据值
//
// 示例值：无需打卡(-), 无需打卡(-)
func (builder *UserStatsDataCellBuilder) Value(value string) *UserStatsDataCellBuilder {
	builder.value = value
	builder.valueFlag = true
	return builder
}

// 数据属性
//
// 示例值：
func (builder *UserStatsDataCellBuilder) Features(features []*UserStatsDataFeature) *UserStatsDataCellBuilder {
	builder.features = features
	builder.featuresFlag = true
	return builder
}

// 字段标题
//
// 示例值：姓名
func (builder *UserStatsDataCellBuilder) Title(title string) *UserStatsDataCellBuilder {
	builder.title = title
	builder.titleFlag = true
	return builder
}

func (builder *UserStatsDataCellBuilder) Build() *UserStatsDataCell {
	req := &UserStatsDataCell{}
	if builder.codeFlag {
		req.Code = &builder.code

	}
	if builder.valueFlag {
		req.Value = &builder.value

	}
	if builder.featuresFlag {
		req.Features = builder.features
	}
	if builder.titleFlag {
		req.Title = &builder.title

	}
	return req
}

type UserStatsDataFeature struct {
	Key   *string `json:"key,omitempty"`   // 统计数据列附加属性的名称
	Value *string `json:"value,omitempty"` // 统计数据列附加属性的值
}

type UserStatsDataFeatureBuilder struct {
	key       string // 统计数据列附加属性的名称
	keyFlag   bool
	value     string // 统计数据列附加属性的值
	valueFlag bool
}

func NewUserStatsDataFeatureBuilder() *UserStatsDataFeatureBuilder {
	builder := &UserStatsDataFeatureBuilder{}
	return builder
}

// 统计数据列附加属性的名称
//
// 示例值：Abnormal
func (builder *UserStatsDataFeatureBuilder) Key(key string) *UserStatsDataFeatureBuilder {
	builder.key = key
	builder.keyFlag = true
	return builder
}

// 统计数据列附加属性的值
//
// 示例值：false
func (builder *UserStatsDataFeatureBuilder) Value(value string) *UserStatsDataFeatureBuilder {
	builder.value = value
	builder.valueFlag = true
	return builder
}

func (builder *UserStatsDataFeatureBuilder) Build() *UserStatsDataFeature {
	req := &UserStatsDataFeature{}
	if builder.keyFlag {
		req.Key = &builder.key

	}
	if builder.valueFlag {
		req.Value = &builder.value

	}
	return req
}

type UserStatsField struct {
	StatsType *string  `json:"stats_type,omitempty"` // 统计类型
	UserId    *string  `json:"user_id,omitempty"`    // 用户 ID
	Fields    []*Field `json:"fields,omitempty"`     // 字段列表
}

type UserStatsFieldBuilder struct {
	statsType     string // 统计类型
	statsTypeFlag bool
	userId        string // 用户 ID
	userIdFlag    bool
	fields        []*Field // 字段列表
	fieldsFlag    bool
}

func NewUserStatsFieldBuilder() *UserStatsFieldBuilder {
	builder := &UserStatsFieldBuilder{}
	return builder
}

// 统计类型
//
// 示例值：
func (builder *UserStatsFieldBuilder) StatsType(statsType string) *UserStatsFieldBuilder {
	builder.statsType = statsType
	builder.statsTypeFlag = true
	return builder
}

// 用户 ID
//
// 示例值：
func (builder *UserStatsFieldBuilder) UserId(userId string) *UserStatsFieldBuilder {
	builder.userId = userId
	builder.userIdFlag = true
	return builder
}

// 字段列表
//
// 示例值：
func (builder *UserStatsFieldBuilder) Fields(fields []*Field) *UserStatsFieldBuilder {
	builder.fields = fields
	builder.fieldsFlag = true
	return builder
}

func (builder *UserStatsFieldBuilder) Build() *UserStatsField {
	req := &UserStatsField{}
	if builder.statsTypeFlag {
		req.StatsType = &builder.statsType

	}
	if builder.userIdFlag {
		req.UserId = &builder.userId

	}
	if builder.fieldsFlag {
		req.Fields = builder.fields
	}
	return req
}

type UserStatsView struct {
	ViewId    *string `json:"view_id,omitempty"`    // 视图 ID
	StatsType *string `json:"stats_type,omitempty"` // 视图类型
	UserId    *string `json:"user_id,omitempty"`    // 查询用户id，同【查询统计数据】、【查询统计设置】user_id
	Items     []*Item `json:"items,omitempty"`      // 用户设置字段
}

type UserStatsViewBuilder struct {
	viewId        string // 视图 ID
	viewIdFlag    bool
	statsType     string // 视图类型
	statsTypeFlag bool
	userId        string // 查询用户id，同【查询统计数据】、【查询统计设置】user_id
	userIdFlag    bool
	items         []*Item // 用户设置字段
	itemsFlag     bool
}

func NewUserStatsViewBuilder() *UserStatsViewBuilder {
	builder := &UserStatsViewBuilder{}
	return builder
}

// 视图 ID
//
// 示例值：TmpZNU5qTTJORFF6T1RnNU5UTTNOakV6TWl0dGIyNTBhQT09
func (builder *UserStatsViewBuilder) ViewId(viewId string) *UserStatsViewBuilder {
	builder.viewId = viewId
	builder.viewIdFlag = true
	return builder
}

// 视图类型
//
// 示例值：month
func (builder *UserStatsViewBuilder) StatsType(statsType string) *UserStatsViewBuilder {
	builder.statsType = statsType
	builder.statsTypeFlag = true
	return builder
}

// 查询用户id，同【查询统计数据】、【查询统计设置】user_id
//
// 示例值：ec8ddg56
func (builder *UserStatsViewBuilder) UserId(userId string) *UserStatsViewBuilder {
	builder.userId = userId
	builder.userIdFlag = true
	return builder
}

// 用户设置字段
//
// 示例值：
func (builder *UserStatsViewBuilder) Items(items []*Item) *UserStatsViewBuilder {
	builder.items = items
	builder.itemsFlag = true
	return builder
}

func (builder *UserStatsViewBuilder) Build() *UserStatsView {
	req := &UserStatsView{}
	if builder.viewIdFlag {
		req.ViewId = &builder.viewId

	}
	if builder.statsTypeFlag {
		req.StatsType = &builder.statsType

	}
	if builder.userIdFlag {
		req.UserId = &builder.userId

	}
	if builder.itemsFlag {
		req.Items = builder.items
	}
	return req
}

type UserTask struct {
	ResultId     *string       `json:"result_id,omitempty"`     // 打卡记录 ID
	UserId       *string       `json:"user_id,omitempty"`       // 用户 ID
	EmployeeName *string       `json:"employee_name,omitempty"` // 用户姓名
	Day          *int          `json:"day,omitempty"`           // 日期
	GroupId      *string       `json:"group_id,omitempty"`      // 考勤组 ID
	ShiftId      *string       `json:"shift_id,omitempty"`      // 班次 ID
	Records      []*TaskResult `json:"records,omitempty"`       // 用户考勤记录
}

type UserTaskBuilder struct {
	resultId         string // 打卡记录 ID
	resultIdFlag     bool
	userId           string // 用户 ID
	userIdFlag       bool
	employeeName     string // 用户姓名
	employeeNameFlag bool
	day              int // 日期
	dayFlag          bool
	groupId          string // 考勤组 ID
	groupIdFlag      bool
	shiftId          string // 班次 ID
	shiftIdFlag      bool
	records          []*TaskResult // 用户考勤记录
	recordsFlag      bool
}

func NewUserTaskBuilder() *UserTaskBuilder {
	builder := &UserTaskBuilder{}
	return builder
}

// 打卡记录 ID
//
// 示例值：6709359313699356941
func (builder *UserTaskBuilder) ResultId(resultId string) *UserTaskBuilder {
	builder.resultId = resultId
	builder.resultIdFlag = true
	return builder
}

// 用户 ID
//
// 示例值：abd754f7
func (builder *UserTaskBuilder) UserId(userId string) *UserTaskBuilder {
	builder.userId = userId
	builder.userIdFlag = true
	return builder
}

// 用户姓名
//
// 示例值：张三
func (builder *UserTaskBuilder) EmployeeName(employeeName string) *UserTaskBuilder {
	builder.employeeName = employeeName
	builder.employeeNameFlag = true
	return builder
}

// 日期
//
// 示例值：20190819
func (builder *UserTaskBuilder) Day(day int) *UserTaskBuilder {
	builder.day = day
	builder.dayFlag = true
	return builder
}

// 考勤组 ID
//
// 示例值：6737202939523236110
func (builder *UserTaskBuilder) GroupId(groupId string) *UserTaskBuilder {
	builder.groupId = groupId
	builder.groupIdFlag = true
	return builder
}

// 班次 ID
//
// 示例值：6753520403404030215
func (builder *UserTaskBuilder) ShiftId(shiftId string) *UserTaskBuilder {
	builder.shiftId = shiftId
	builder.shiftIdFlag = true
	return builder
}

// 用户考勤记录
//
// 示例值：
func (builder *UserTaskBuilder) Records(records []*TaskResult) *UserTaskBuilder {
	builder.records = records
	builder.recordsFlag = true
	return builder
}

func (builder *UserTaskBuilder) Build() *UserTask {
	req := &UserTask{}
	if builder.resultIdFlag {
		req.ResultId = &builder.resultId

	}
	if builder.userIdFlag {
		req.UserId = &builder.userId

	}
	if builder.employeeNameFlag {
		req.EmployeeName = &builder.employeeName

	}
	if builder.dayFlag {
		req.Day = &builder.day

	}
	if builder.groupIdFlag {
		req.GroupId = &builder.groupId

	}
	if builder.shiftIdFlag {
		req.ShiftId = &builder.shiftId

	}
	if builder.recordsFlag {
		req.Records = builder.records
	}
	return req
}

type UserTaskRemedy struct {
	UserId     *string `json:"user_id,omitempty"`     // 用户 ID
	RemedyDate *int    `json:"remedy_date,omitempty"` // 补卡日期
	PunchNo    *int    `json:"punch_no,omitempty"`    // 第几次上下班，0：第 1 次上下班，1：第 2 次上下班，2：第 3 次上下班，自由班制填 0
	WorkType   *int    `json:"work_type,omitempty"`   // 上班 / 下班，1：上班，2：下班，自由班制填 0
	ApprovalId *string `json:"approval_id,omitempty"` // 审批 ID
	RemedyTime *string `json:"remedy_time,omitempty"` // 补卡时间，时间格式为 yyyy-MM-dd HH:mm
	Status     *int    `json:"status,omitempty"`      // 补卡状态（默认为审批中）
	Reason     *string `json:"reason,omitempty"`      // 补卡原因
	Time       *string `json:"time,omitempty"`        // 补卡时间，精确到秒的时间戳
	TimeZone   *string `json:"time_zone,omitempty"`   // 补卡时考勤组时区
	CreateTime *string `json:"create_time,omitempty"` // 补卡发起时间，精确到秒的时间戳
	UpdateTime *string `json:"update_time,omitempty"` // 补卡状态更新时间，精确到秒的时间戳
}

type UserTaskRemedyBuilder struct {
	userId         string // 用户 ID
	userIdFlag     bool
	remedyDate     int // 补卡日期
	remedyDateFlag bool
	punchNo        int // 第几次上下班，0：第 1 次上下班，1：第 2 次上下班，2：第 3 次上下班，自由班制填 0
	punchNoFlag    bool
	workType       int // 上班 / 下班，1：上班，2：下班，自由班制填 0
	workTypeFlag   bool
	approvalId     string // 审批 ID
	approvalIdFlag bool
	remedyTime     string // 补卡时间，时间格式为 yyyy-MM-dd HH:mm
	remedyTimeFlag bool
	status         int // 补卡状态（默认为审批中）
	statusFlag     bool
	reason         string // 补卡原因
	reasonFlag     bool
	time           string // 补卡时间，精确到秒的时间戳
	timeFlag       bool
	timeZone       string // 补卡时考勤组时区
	timeZoneFlag   bool
	createTime     string // 补卡发起时间，精确到秒的时间戳
	createTimeFlag bool
	updateTime     string // 补卡状态更新时间，精确到秒的时间戳
	updateTimeFlag bool
}

func NewUserTaskRemedyBuilder() *UserTaskRemedyBuilder {
	builder := &UserTaskRemedyBuilder{}
	return builder
}

// 用户 ID
//
// 示例值：abd754f7
func (builder *UserTaskRemedyBuilder) UserId(userId string) *UserTaskRemedyBuilder {
	builder.userId = userId
	builder.userIdFlag = true
	return builder
}

// 补卡日期
//
// 示例值：20210701
func (builder *UserTaskRemedyBuilder) RemedyDate(remedyDate int) *UserTaskRemedyBuilder {
	builder.remedyDate = remedyDate
	builder.remedyDateFlag = true
	return builder
}

// 第几次上下班，0：第 1 次上下班，1：第 2 次上下班，2：第 3 次上下班，自由班制填 0
//
// 示例值：0
func (builder *UserTaskRemedyBuilder) PunchNo(punchNo int) *UserTaskRemedyBuilder {
	builder.punchNo = punchNo
	builder.punchNoFlag = true
	return builder
}

// 上班 / 下班，1：上班，2：下班，自由班制填 0
//
// 示例值：1
func (builder *UserTaskRemedyBuilder) WorkType(workType int) *UserTaskRemedyBuilder {
	builder.workType = workType
	builder.workTypeFlag = true
	return builder
}

// 审批 ID
//
// 示例值：6737202939523236113
func (builder *UserTaskRemedyBuilder) ApprovalId(approvalId string) *UserTaskRemedyBuilder {
	builder.approvalId = approvalId
	builder.approvalIdFlag = true
	return builder
}

// 补卡时间，时间格式为 yyyy-MM-dd HH:mm
//
// 示例值：2021-07-01 08:00
func (builder *UserTaskRemedyBuilder) RemedyTime(remedyTime string) *UserTaskRemedyBuilder {
	builder.remedyTime = remedyTime
	builder.remedyTimeFlag = true
	return builder
}

// 补卡状态（默认为审批中）
//
// 示例值：2
func (builder *UserTaskRemedyBuilder) Status(status int) *UserTaskRemedyBuilder {
	builder.status = status
	builder.statusFlag = true
	return builder
}

// 补卡原因
//
// 示例值：忘记打卡
func (builder *UserTaskRemedyBuilder) Reason(reason string) *UserTaskRemedyBuilder {
	builder.reason = reason
	builder.reasonFlag = true
	return builder
}

// 补卡时间，精确到秒的时间戳
//
// 示例值：1611476284
func (builder *UserTaskRemedyBuilder) Time(time string) *UserTaskRemedyBuilder {
	builder.time = time
	builder.timeFlag = true
	return builder
}

// 补卡时考勤组时区
//
// 示例值：Asia/Shanghai
func (builder *UserTaskRemedyBuilder) TimeZone(timeZone string) *UserTaskRemedyBuilder {
	builder.timeZone = timeZone
	builder.timeZoneFlag = true
	return builder
}

// 补卡发起时间，精确到秒的时间戳
//
// 示例值：1611476284
func (builder *UserTaskRemedyBuilder) CreateTime(createTime string) *UserTaskRemedyBuilder {
	builder.createTime = createTime
	builder.createTimeFlag = true
	return builder
}

// 补卡状态更新时间，精确到秒的时间戳
//
// 示例值：1611476284
func (builder *UserTaskRemedyBuilder) UpdateTime(updateTime string) *UserTaskRemedyBuilder {
	builder.updateTime = updateTime
	builder.updateTimeFlag = true
	return builder
}

func (builder *UserTaskRemedyBuilder) Build() *UserTaskRemedy {
	req := &UserTaskRemedy{}
	if builder.userIdFlag {
		req.UserId = &builder.userId

	}
	if builder.remedyDateFlag {
		req.RemedyDate = &builder.remedyDate

	}
	if builder.punchNoFlag {
		req.PunchNo = &builder.punchNo

	}
	if builder.workTypeFlag {
		req.WorkType = &builder.workType

	}
	if builder.approvalIdFlag {
		req.ApprovalId = &builder.approvalId

	}
	if builder.remedyTimeFlag {
		req.RemedyTime = &builder.remedyTime

	}
	if builder.statusFlag {
		req.Status = &builder.status

	}
	if builder.reasonFlag {
		req.Reason = &builder.reason

	}
	if builder.timeFlag {
		req.Time = &builder.time

	}
	if builder.timeZoneFlag {
		req.TimeZone = &builder.timeZone

	}
	if builder.createTimeFlag {
		req.CreateTime = &builder.createTime

	}
	if builder.updateTimeFlag {
		req.UpdateTime = &builder.updateTime

	}
	return req
}

type UserTrip struct {
	ApprovalId       *string `json:"approval_id,omitempty"`        // 审批实例 ID
	StartTime        *string `json:"start_time,omitempty"`         // 开始时间，时间格式为 yyyy-MM-dd HH:mm:ss
	EndTime          *string `json:"end_time,omitempty"`           // 结束时间，时间格式为 yyyy-MM-dd HH:mm:ss
	Reason           *string `json:"reason,omitempty"`             // 出差理由
	ApprovePassTime  *string `json:"approve_pass_time,omitempty"`  // 审批通过时间，时间格式为 yyyy-MM-dd HH:mm:ss
	ApproveApplyTime *string `json:"approve_apply_time,omitempty"` // 审批申请时间，时间格式为 yyyy-MM-dd HH:mm:ss
}

type UserTripBuilder struct {
	approvalId           string // 审批实例 ID
	approvalIdFlag       bool
	startTime            string // 开始时间，时间格式为 yyyy-MM-dd HH:mm:ss
	startTimeFlag        bool
	endTime              string // 结束时间，时间格式为 yyyy-MM-dd HH:mm:ss
	endTimeFlag          bool
	reason               string // 出差理由
	reasonFlag           bool
	approvePassTime      string // 审批通过时间，时间格式为 yyyy-MM-dd HH:mm:ss
	approvePassTimeFlag  bool
	approveApplyTime     string // 审批申请时间，时间格式为 yyyy-MM-dd HH:mm:ss
	approveApplyTimeFlag bool
}

func NewUserTripBuilder() *UserTripBuilder {
	builder := &UserTripBuilder{}
	return builder
}

// 审批实例 ID
//
// 示例值：6737202939523236113
func (builder *UserTripBuilder) ApprovalId(approvalId string) *UserTripBuilder {
	builder.approvalId = approvalId
	builder.approvalIdFlag = true
	return builder
}

// 开始时间，时间格式为 yyyy-MM-dd HH:mm:ss
//
// 示例值：2021-01-04 09:00:00
func (builder *UserTripBuilder) StartTime(startTime string) *UserTripBuilder {
	builder.startTime = startTime
	builder.startTimeFlag = true
	return builder
}

// 结束时间，时间格式为 yyyy-MM-dd HH:mm:ss
//
// 示例值：2021-01-04 19:00:00
func (builder *UserTripBuilder) EndTime(endTime string) *UserTripBuilder {
	builder.endTime = endTime
	builder.endTimeFlag = true
	return builder
}

// 出差理由
//
// 示例值：培训
func (builder *UserTripBuilder) Reason(reason string) *UserTripBuilder {
	builder.reason = reason
	builder.reasonFlag = true
	return builder
}

// 审批通过时间，时间格式为 yyyy-MM-dd HH:mm:ss
//
// 示例值：2021-01-04 12:00:00
func (builder *UserTripBuilder) ApprovePassTime(approvePassTime string) *UserTripBuilder {
	builder.approvePassTime = approvePassTime
	builder.approvePassTimeFlag = true
	return builder
}

// 审批申请时间，时间格式为 yyyy-MM-dd HH:mm:ss
//
// 示例值：2021-01-04 11:00:00
func (builder *UserTripBuilder) ApproveApplyTime(approveApplyTime string) *UserTripBuilder {
	builder.approveApplyTime = approveApplyTime
	builder.approveApplyTimeFlag = true
	return builder
}

func (builder *UserTripBuilder) Build() *UserTrip {
	req := &UserTrip{}
	if builder.approvalIdFlag {
		req.ApprovalId = &builder.approvalId

	}
	if builder.startTimeFlag {
		req.StartTime = &builder.startTime

	}
	if builder.endTimeFlag {
		req.EndTime = &builder.endTime

	}
	if builder.reasonFlag {
		req.Reason = &builder.reason

	}
	if builder.approvePassTimeFlag {
		req.ApprovePassTime = &builder.approvePassTime

	}
	if builder.approveApplyTimeFlag {
		req.ApproveApplyTime = &builder.approveApplyTime

	}
	return req
}

type WifiInfo struct {
	Status *int `json:"status,omitempty"` //
}

type WifiInfoBuilder struct {
	status     int //
	statusFlag bool
}

func NewWifiInfoBuilder() *WifiInfoBuilder {
	builder := &WifiInfoBuilder{}
	return builder
}

//
//
// 示例值：
func (builder *WifiInfoBuilder) Status(status int) *WifiInfoBuilder {
	builder.status = status
	builder.statusFlag = true
	return builder
}

func (builder *WifiInfoBuilder) Build() *WifiInfo {
	req := &WifiInfo{}
	if builder.statusFlag {
		req.Status = &builder.status

	}
	return req
}

type WifiInfoEvent struct {
	Ssid      *string `json:"ssid,omitempty"`      //
	Bssid     *string `json:"bssid,omitempty"`     //
	Lastssid  *string `json:"lastssid,omitempty"`  //
	Lastbssid *string `json:"lastbssid,omitempty"` //
}

type WifiInfoEventBuilder struct {
	ssid          string //
	ssidFlag      bool
	bssid         string //
	bssidFlag     bool
	lastssid      string //
	lastssidFlag  bool
	lastbssid     string //
	lastbssidFlag bool
}

func NewWifiInfoEventBuilder() *WifiInfoEventBuilder {
	builder := &WifiInfoEventBuilder{}
	return builder
}

//
//
// 示例值：
func (builder *WifiInfoEventBuilder) Ssid(ssid string) *WifiInfoEventBuilder {
	builder.ssid = ssid
	builder.ssidFlag = true
	return builder
}

//
//
// 示例值：
func (builder *WifiInfoEventBuilder) Bssid(bssid string) *WifiInfoEventBuilder {
	builder.bssid = bssid
	builder.bssidFlag = true
	return builder
}

//
//
// 示例值：
func (builder *WifiInfoEventBuilder) Lastssid(lastssid string) *WifiInfoEventBuilder {
	builder.lastssid = lastssid
	builder.lastssidFlag = true
	return builder
}

//
//
// 示例值：
func (builder *WifiInfoEventBuilder) Lastbssid(lastbssid string) *WifiInfoEventBuilder {
	builder.lastbssid = lastbssid
	builder.lastbssidFlag = true
	return builder
}

func (builder *WifiInfoEventBuilder) Build() *WifiInfoEvent {
	req := &WifiInfoEvent{}
	if builder.ssidFlag {
		req.Ssid = &builder.ssid

	}
	if builder.bssidFlag {
		req.Bssid = &builder.bssid

	}
	if builder.lastssidFlag {
		req.Lastssid = &builder.lastssid

	}
	if builder.lastbssidFlag {
		req.Lastbssid = &builder.lastbssid

	}
	return req
}

type ProcessApprovalInfoReqBodyBuilder struct {
	approvalId       string // 审批实例 ID，获取方式：1）[获取审批通过数据](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/attendance-v1/user_approval/query) 2）[写入审批结果](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/attendance-v1/user_approval/create) 3）[通知补卡审批发起（补卡情况下）](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/attendance-v1/user_task_remedy/create)
	approvalIdFlag   bool
	approvalType     string // 审批类型，leave：请假，out：外出，overtime：加班，trip：出差，remedy：补卡
	approvalTypeFlag bool
	status           int // 审批状态，1：不通过，2：通过，4：撤销
	statusFlag       bool
}

func NewProcessApprovalInfoReqBodyBuilder() *ProcessApprovalInfoReqBodyBuilder {
	builder := &ProcessApprovalInfoReqBodyBuilder{}
	return builder
}

// 审批实例 ID，获取方式：1）[获取审批通过数据](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/attendance-v1/user_approval/query) 2）[写入审批结果](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/attendance-v1/user_approval/create) 3）[通知补卡审批发起（补卡情况下）](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/attendance-v1/user_task_remedy/create)
//
//示例值：6737202939523236113
func (builder *ProcessApprovalInfoReqBodyBuilder) ApprovalId(approvalId string) *ProcessApprovalInfoReqBodyBuilder {
	builder.approvalId = approvalId
	builder.approvalIdFlag = true
	return builder
}

// 审批类型，leave：请假，out：外出，overtime：加班，trip：出差，remedy：补卡
//
//示例值：remedy
func (builder *ProcessApprovalInfoReqBodyBuilder) ApprovalType(approvalType string) *ProcessApprovalInfoReqBodyBuilder {
	builder.approvalType = approvalType
	builder.approvalTypeFlag = true
	return builder
}

// 审批状态，1：不通过，2：通过，4：撤销
//
//示例值：4
func (builder *ProcessApprovalInfoReqBodyBuilder) Status(status int) *ProcessApprovalInfoReqBodyBuilder {
	builder.status = status
	builder.statusFlag = true
	return builder
}

func (builder *ProcessApprovalInfoReqBodyBuilder) Build() *ProcessApprovalInfoReqBody {
	req := &ProcessApprovalInfoReqBody{}
	if builder.approvalIdFlag {
		req.ApprovalId = &builder.approvalId
	}
	if builder.approvalTypeFlag {
		req.ApprovalType = &builder.approvalType
	}
	if builder.statusFlag {
		req.Status = &builder.status
	}
	return req
}

type ProcessApprovalInfoPathReqBodyBuilder struct {
	approvalId       string // 审批实例 ID，获取方式：1）[获取审批通过数据](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/attendance-v1/user_approval/query) 2）[写入审批结果](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/attendance-v1/user_approval/create) 3）[通知补卡审批发起（补卡情况下）](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/attendance-v1/user_task_remedy/create)
	approvalIdFlag   bool
	approvalType     string // 审批类型，leave：请假，out：外出，overtime：加班，trip：出差，remedy：补卡
	approvalTypeFlag bool
	status           int // 审批状态，1：不通过，2：通过，4：撤销
	statusFlag       bool
}

func NewProcessApprovalInfoPathReqBodyBuilder() *ProcessApprovalInfoPathReqBodyBuilder {
	builder := &ProcessApprovalInfoPathReqBodyBuilder{}
	return builder
}

// 审批实例 ID，获取方式：1）[获取审批通过数据](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/attendance-v1/user_approval/query) 2）[写入审批结果](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/attendance-v1/user_approval/create) 3）[通知补卡审批发起（补卡情况下）](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/attendance-v1/user_task_remedy/create)
//
// 示例值：6737202939523236113
func (builder *ProcessApprovalInfoPathReqBodyBuilder) ApprovalId(approvalId string) *ProcessApprovalInfoPathReqBodyBuilder {
	builder.approvalId = approvalId
	builder.approvalIdFlag = true
	return builder
}

// 审批类型，leave：请假，out：外出，overtime：加班，trip：出差，remedy：补卡
//
// 示例值：remedy
func (builder *ProcessApprovalInfoPathReqBodyBuilder) ApprovalType(approvalType string) *ProcessApprovalInfoPathReqBodyBuilder {
	builder.approvalType = approvalType
	builder.approvalTypeFlag = true
	return builder
}

// 审批状态，1：不通过，2：通过，4：撤销
//
// 示例值：4
func (builder *ProcessApprovalInfoPathReqBodyBuilder) Status(status int) *ProcessApprovalInfoPathReqBodyBuilder {
	builder.status = status
	builder.statusFlag = true
	return builder
}

func (builder *ProcessApprovalInfoPathReqBodyBuilder) Build() (*ProcessApprovalInfoReqBody, error) {
	req := &ProcessApprovalInfoReqBody{}
	if builder.approvalIdFlag {
		req.ApprovalId = &builder.approvalId
	}
	if builder.approvalTypeFlag {
		req.ApprovalType = &builder.approvalType
	}
	if builder.statusFlag {
		req.Status = &builder.status
	}
	return req, nil
}

type ProcessApprovalInfoReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *ProcessApprovalInfoReqBody
}

func NewProcessApprovalInfoReqBuilder() *ProcessApprovalInfoReqBuilder {
	builder := &ProcessApprovalInfoReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 对于只使用飞书考勤系统而未使用飞书审批系统的企业，可以通过该接口更新写入飞书考勤系统中的三方系统审批状态，例如请假、加班、外出、出差、补卡等审批，状态包括通过、不通过、撤销等。
func (builder *ProcessApprovalInfoReqBuilder) Body(body *ProcessApprovalInfoReqBody) *ProcessApprovalInfoReqBuilder {
	builder.body = body
	return builder
}

func (builder *ProcessApprovalInfoReqBuilder) Build() *ProcessApprovalInfoReq {
	req := &ProcessApprovalInfoReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.Body = builder.body
	return req
}

type ProcessApprovalInfoReqBody struct {
	ApprovalId   *string `json:"approval_id,omitempty"`   // 审批实例 ID，获取方式：1）[获取审批通过数据](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/attendance-v1/user_approval/query) 2）[写入审批结果](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/attendance-v1/user_approval/create) 3）[通知补卡审批发起（补卡情况下）](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/attendance-v1/user_task_remedy/create)
	ApprovalType *string `json:"approval_type,omitempty"` // 审批类型，leave：请假，out：外出，overtime：加班，trip：出差，remedy：补卡
	Status       *int    `json:"status,omitempty"`        // 审批状态，1：不通过，2：通过，4：撤销
}

type ProcessApprovalInfoReq struct {
	apiReq *larkcore.ApiReq
	Body   *ProcessApprovalInfoReqBody `body:""`
}

type ProcessApprovalInfoRespData struct {
	ApprovalInfo *ApprovalInfo `json:"approval_info,omitempty"` // 审批信息
}

type ProcessApprovalInfoResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *ProcessApprovalInfoRespData `json:"data"` // 业务数据
}

func (resp *ProcessApprovalInfoResp) Success() bool {
	return resp.Code == 0
}

type DownloadFileReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewDownloadFileReqBuilder() *DownloadFileReqBuilder {
	builder := &DownloadFileReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 文件 ID
//
// 示例值：xxxxxb306842b1c189bc5212eefxxxxx
func (builder *DownloadFileReqBuilder) FileId(fileId string) *DownloadFileReqBuilder {
	builder.apiReq.PathParams.Set("file_id", fmt.Sprint(fileId))
	return builder
}

func (builder *DownloadFileReqBuilder) Build() *DownloadFileReq {
	req := &DownloadFileReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	return req
}

type DownloadFileReq struct {
	apiReq *larkcore.ApiReq
}

type DownloadFileResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	File     io.Reader `json:"-"`
	FileName string    `json:"-"`
}

func (resp *DownloadFileResp) Success() bool {
	return resp.Code == 0
}

func (resp *DownloadFileResp) WriteFile(fileName string) error {
	bs, err := ioutil.ReadAll(resp.File)
	if err != nil {
		return err
	}

	err = ioutil.WriteFile(fileName, bs, 0666)
	if err != nil {
		return err
	}
	return nil
}

type UploadFileReqBodyBuilder struct {
	file     io.Reader // 文件内容
	fileFlag bool
}

func NewUploadFileReqBodyBuilder() *UploadFileReqBodyBuilder {
	builder := &UploadFileReqBodyBuilder{}
	return builder
}

// 文件内容
//
//示例值：二进制文件
func (builder *UploadFileReqBodyBuilder) File(file io.Reader) *UploadFileReqBodyBuilder {
	builder.file = file
	builder.fileFlag = true
	return builder
}

func (builder *UploadFileReqBodyBuilder) Build() *UploadFileReqBody {
	req := &UploadFileReqBody{}
	if builder.fileFlag {
		req.File = builder.file
	}
	return req
}

type UploadFilePathReqBodyBuilder struct {
	filePath     string // 文件内容
	filePathFlag bool
}

func NewUploadFilePathReqBodyBuilder() *UploadFilePathReqBodyBuilder {
	builder := &UploadFilePathReqBodyBuilder{}
	return builder
}

// 文件内容
//
// 示例值：二进制文件
func (builder *UploadFilePathReqBodyBuilder) FilePath(filePath string) *UploadFilePathReqBodyBuilder {
	builder.filePath = filePath
	builder.filePathFlag = true
	return builder
}

func (builder *UploadFilePathReqBodyBuilder) Build() (*UploadFileReqBody, error) {
	req := &UploadFileReqBody{}
	if builder.filePathFlag {
		data, err := larkcore.File2Bytes(builder.filePath)
		if err != nil {
			return nil, err
		}
		req.File = bytes.NewBuffer(data)
	}
	return req, nil
}

type UploadFileReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *UploadFileReqBody
}

func NewUploadFileReqBuilder() *UploadFileReqBuilder {
	builder := &UploadFileReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 带后缀的文件名
//
// 示例值：人脸照片.jpg
func (builder *UploadFileReqBuilder) FileName(fileName string) *UploadFileReqBuilder {
	builder.apiReq.QueryParams.Set("file_name", fmt.Sprint(fileName))
	return builder
}

// 上传文件并获取文件 ID，可用于“修改用户设置”接口中的 face_key 参数。
func (builder *UploadFileReqBuilder) Body(body *UploadFileReqBody) *UploadFileReqBuilder {
	builder.body = body
	return builder
}

func (builder *UploadFileReqBuilder) Build() *UploadFileReq {
	req := &UploadFileReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.body
	return req
}

type UploadFileReqBody struct {
	File io.Reader `json:"file,omitempty"` // 文件内容
}

type UploadFileReq struct {
	apiReq *larkcore.ApiReq
	Body   *UploadFileReqBody `body:""`
}

type UploadFileRespData struct {
	File *File `json:"file,omitempty"` // 文件
}

type UploadFileResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *UploadFileRespData `json:"data"` // 业务数据
}

func (resp *UploadFileResp) Success() bool {
	return resp.Code == 0
}

type CreateGroupReqBodyBuilder struct {
	group          *Group // 6921319402260496386
	groupFlag      bool
	operatorId     string // 操作人uid，如果您未操作[考勤管理后台“API 接入”流程](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/attendance-v1/attendance-development-guidelines)，则此字段为必填字段
	operatorIdFlag bool
}

func NewCreateGroupReqBodyBuilder() *CreateGroupReqBodyBuilder {
	builder := &CreateGroupReqBodyBuilder{}
	return builder
}

// 6921319402260496386
//
//示例值：
func (builder *CreateGroupReqBodyBuilder) Group(group *Group) *CreateGroupReqBodyBuilder {
	builder.group = group
	builder.groupFlag = true
	return builder
}

// 操作人uid，如果您未操作[考勤管理后台“API 接入”流程](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/attendance-v1/attendance-development-guidelines)，则此字段为必填字段
//
//示例值：dd31248a
func (builder *CreateGroupReqBodyBuilder) OperatorId(operatorId string) *CreateGroupReqBodyBuilder {
	builder.operatorId = operatorId
	builder.operatorIdFlag = true
	return builder
}

func (builder *CreateGroupReqBodyBuilder) Build() *CreateGroupReqBody {
	req := &CreateGroupReqBody{}
	if builder.groupFlag {
		req.Group = builder.group
	}
	if builder.operatorIdFlag {
		req.OperatorId = &builder.operatorId
	}
	return req
}

type CreateGroupPathReqBodyBuilder struct {
	group          *Group // 6921319402260496386
	groupFlag      bool
	operatorId     string // 操作人uid，如果您未操作[考勤管理后台“API 接入”流程](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/attendance-v1/attendance-development-guidelines)，则此字段为必填字段
	operatorIdFlag bool
}

func NewCreateGroupPathReqBodyBuilder() *CreateGroupPathReqBodyBuilder {
	builder := &CreateGroupPathReqBodyBuilder{}
	return builder
}

// 6921319402260496386
//
// 示例值：
func (builder *CreateGroupPathReqBodyBuilder) Group(group *Group) *CreateGroupPathReqBodyBuilder {
	builder.group = group
	builder.groupFlag = true
	return builder
}

// 操作人uid，如果您未操作[考勤管理后台“API 接入”流程](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/attendance-v1/attendance-development-guidelines)，则此字段为必填字段
//
// 示例值：dd31248a
func (builder *CreateGroupPathReqBodyBuilder) OperatorId(operatorId string) *CreateGroupPathReqBodyBuilder {
	builder.operatorId = operatorId
	builder.operatorIdFlag = true
	return builder
}

func (builder *CreateGroupPathReqBodyBuilder) Build() (*CreateGroupReqBody, error) {
	req := &CreateGroupReqBody{}
	if builder.groupFlag {
		req.Group = builder.group
	}
	if builder.operatorIdFlag {
		req.OperatorId = &builder.operatorId
	}
	return req, nil
}

type CreateGroupReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *CreateGroupReqBody
}

func NewCreateGroupReqBuilder() *CreateGroupReqBuilder {
	builder := &CreateGroupReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 用户 ID 的类型
//
// 示例值：employee_id
func (builder *CreateGroupReqBuilder) EmployeeType(employeeType string) *CreateGroupReqBuilder {
	builder.apiReq.QueryParams.Set("employee_type", fmt.Sprint(employeeType))
	return builder
}

// 部门 ID 的类型
//
// 示例值：od-fcb45c28a45311afd441b8869541ece8
func (builder *CreateGroupReqBuilder) DeptType(deptType string) *CreateGroupReqBuilder {
	builder.apiReq.QueryParams.Set("dept_type", fmt.Sprint(deptType))
	return builder
}

// 考勤组，是对部门或者员工在某个特定场所及特定时间段内的出勤情况（包括上下班、迟到、早退、病假、婚假、丧假、公休、工作时间、加班情况等）的一种规则设定。;;通过设置考勤组，可以从部门、员工两个维度，来设定考勤方式、考勤时间、考勤地点等考勤规则。
func (builder *CreateGroupReqBuilder) Body(body *CreateGroupReqBody) *CreateGroupReqBuilder {
	builder.body = body
	return builder
}

func (builder *CreateGroupReqBuilder) Build() *CreateGroupReq {
	req := &CreateGroupReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.body
	return req
}

type CreateGroupReqBody struct {
	Group      *Group  `json:"group,omitempty"`       // 6921319402260496386
	OperatorId *string `json:"operator_id,omitempty"` // 操作人uid，如果您未操作[考勤管理后台“API 接入”流程](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/attendance-v1/attendance-development-guidelines)，则此字段为必填字段
}

type CreateGroupReq struct {
	apiReq *larkcore.ApiReq
	Body   *CreateGroupReqBody `body:""`
}

type CreateGroupRespData struct {
	Group *Group `json:"group,omitempty"` // 6921319402260496386
}

type CreateGroupResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *CreateGroupRespData `json:"data"` // 业务数据
}

func (resp *CreateGroupResp) Success() bool {
	return resp.Code == 0
}

type DeleteGroupReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewDeleteGroupReqBuilder() *DeleteGroupReqBuilder {
	builder := &DeleteGroupReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 考勤组 ID，获取方式：1）[创建或修改考勤组](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/attendance-v1/group/create) 2）[按名称查询考勤组](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/attendance-v1/group/search) 3）[获取打卡结果](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/attendance-v1/user_task/query)
//
// 示例值：6919358128597097404
func (builder *DeleteGroupReqBuilder) GroupId(groupId string) *DeleteGroupReqBuilder {
	builder.apiReq.PathParams.Set("group_id", fmt.Sprint(groupId))
	return builder
}

func (builder *DeleteGroupReqBuilder) Build() *DeleteGroupReq {
	req := &DeleteGroupReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	return req
}

type DeleteGroupReq struct {
	apiReq *larkcore.ApiReq
}

type DeleteGroupResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *DeleteGroupResp) Success() bool {
	return resp.Code == 0
}

type GetGroupReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewGetGroupReqBuilder() *GetGroupReqBuilder {
	builder := &GetGroupReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 考勤组 ID，获取方式：1）[创建或修改考勤组](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/attendance-v1/group/create) 2）[按名称查询考勤组](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/attendance-v1/group/search) 3）[获取打卡结果](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/attendance-v1/user_task/query)
//
// 示例值：6919358128597097404
func (builder *GetGroupReqBuilder) GroupId(groupId string) *GetGroupReqBuilder {
	builder.apiReq.PathParams.Set("group_id", fmt.Sprint(groupId))
	return builder
}

// 用户 ID 的类型
//
// 示例值：employee_id
func (builder *GetGroupReqBuilder) EmployeeType(employeeType string) *GetGroupReqBuilder {
	builder.apiReq.QueryParams.Set("employee_type", fmt.Sprint(employeeType))
	return builder
}

// 部门 ID 的类型
//
// 示例值：od-fcb45c28a45311afd441b8869541ece8
func (builder *GetGroupReqBuilder) DeptType(deptType string) *GetGroupReqBuilder {
	builder.apiReq.QueryParams.Set("dept_type", fmt.Sprint(deptType))
	return builder
}

func (builder *GetGroupReqBuilder) Build() *GetGroupReq {
	req := &GetGroupReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type GetGroupReq struct {
	apiReq *larkcore.ApiReq
}

type GetGroupRespData struct {
	GroupId                 *string                  `json:"group_id,omitempty"`                    // 考勤组的Id， 需要从获取用户打卡结果信息的接口中获取groupId，修改考勤组时必填
	GroupName               *string                  `json:"group_name,omitempty"`                  // 考勤组名称
	TimeZone                *string                  `json:"time_zone,omitempty"`                   // 考勤组时区
	BindDeptIds             []string                 `json:"bind_dept_ids,omitempty"`               //
	ExceptDeptIds           []string                 `json:"except_dept_ids,omitempty"`             //
	BindUserIds             []string                 `json:"bind_user_ids,omitempty"`               //
	ExceptUserIds           []string                 `json:"except_user_ids,omitempty"`             //
	GroupLeaderIds          []string                 `json:"group_leader_ids,omitempty"`            //
	SubGroupLeaderIds       []string                 `json:"sub_group_leader_ids,omitempty"`        //
	AllowOutPunch           *bool                    `json:"allow_out_punch,omitempty"`             // 是否允许外勤打卡
	OutPunchNeedApproval    *bool                    `json:"out_punch_need_approval,omitempty"`     // 外勤打卡需审批（需要允许外勤打卡才能设置生效）
	OutPunchNeedRemark      *bool                    `json:"out_punch_need_remark,omitempty"`       // 外勤打卡需填写备注（需要允许外勤打卡才能设置生效）
	OutPunchNeedPhoto       *bool                    `json:"out_punch_need_photo,omitempty"`        // 外勤打卡需拍照（需要允许外勤打卡才能设置生效）
	OutPunchAllowedHideAddr *bool                    `json:"out_punch_allowed_hide_addr,omitempty"` // 外勤打卡允许员工隐藏详细地址（需要允许外勤打卡才能设置生效）
	AllowPcPunch            *bool                    `json:"allow_pc_punch,omitempty"`              // 是否允许pc打卡
	AllowRemedy             *bool                    `json:"allow_remedy,omitempty"`                // 是否允许补卡
	RemedyLimit             *bool                    `json:"remedy_limit,omitempty"`                // 补卡次数是否限制（需要允许补卡才能设置生效）
	RemedyLimitCount        *int                     `json:"remedy_limit_count,omitempty"`          // 补卡次数（需要允许补卡才能设置生效）
	RemedyDateLimit         *bool                    `json:"remedy_date_limit,omitempty"`           // 补卡时间是否限制（需要允许补卡才能设置生效）
	RemedyDateNum           *int                     `json:"remedy_date_num,omitempty"`             // 补卡时间,几天内可以发起补卡（需要允许补卡才能设置生效）
	AllowRemedyTypeLack     *bool                    `json:"allow_remedy_type_lack,omitempty"`      // 允许缺卡补卡（需要允许补卡才能设置生效）
	AllowRemedyTypeLate     *bool                    `json:"allow_remedy_type_late,omitempty"`      // 允许迟到补卡（需要允许补卡才能设置生效）
	AllowRemedyTypeEarly    *bool                    `json:"allow_remedy_type_early,omitempty"`     // 允许早退补卡（需要允许补卡才能设置生效）
	AllowRemedyTypeNormal   *bool                    `json:"allow_remedy_type_normal,omitempty"`    // 允许正常补卡（需要允许补卡才能设置生效）
	ShowCumulativeTime      *bool                    `json:"show_cumulative_time,omitempty"`        // 是否展示累计时长
	ShowOverTime            *bool                    `json:"show_over_time,omitempty"`              // 是否展示加班时长
	HideStaffPunchTime      *bool                    `json:"hide_staff_punch_time,omitempty"`       // 是否隐藏员工打卡详情
	FacePunch               *bool                    `json:"face_punch,omitempty"`                  // 是否开启人脸打卡
	FacePunchCfg            *int                     `json:"face_punch_cfg,omitempty"`              // 人脸打卡规则， 1：每次打卡均需人脸识别 2：疑似需要
	FaceDowngrade           *bool                    `json:"face_downgrade,omitempty"`              // 脸识别失败时允许普通拍照打卡
	ReplaceBasicPic         *bool                    `json:"replace_basic_pic,omitempty"`           // 是否允许替换基准图片
	Machines                []*Machine               `json:"machines,omitempty"`                    //
	GpsRange                *int                     `json:"gps_range,omitempty"`                   // GPS打卡的地址范围
	Locations               []*Location              `json:"locations,omitempty"`                   //
	GroupType               *int                     `json:"group_type,omitempty"`                  // 考勤类型 0：固定考勤  2：排班考勤， 3：自由班次
	PunchDayShiftIds        []string                 `json:"punch_day_shift_ids,omitempty"`         // 固定班次必需填
	FreePunchCfg            *FreePunchCfg            `json:"free_punch_cfg,omitempty"`              //
	CalendarId              *int                     `json:"calendar_id,omitempty"`                 // 国家日历 id，（0：不根据国家日历休息, 1：中国，2：美国，3：日本，4：印度，5：新加坡），默认 1
	NeedPunchSpecialDays    []*PunchSpecialDateShift `json:"need_punch_special_days,omitempty"`     // 强制需要打卡的日期
	NoNeedPunchSpecialDays  []*PunchSpecialDateShift `json:"no_need_punch_special_days,omitempty"`  // 强制不需要打卡的日期
	WorkDayNoPunchAsLack    *bool                    `json:"work_day_no_punch_as_lack,omitempty"`   // 自由班次下工作日不打卡是否记为缺卡
	RemedyPeriodType        *int                     `json:"remedy_period_type,omitempty"`          // 补卡周期类型
	RemedyPeriodCustomDate  *int                     `json:"remedy_period_custom_date,omitempty"`   // 补卡自定义周期起始日期
	PunchType               *int                     `json:"punch_type,omitempty"`                  // 打卡类型，位运算。1:GPS打卡；2:wifi打卡；4:考勤机打卡；8:IP打卡
	EffectTime              *string                  `json:"effect_time,omitempty"`                 // 生效时间，精确到秒的时间戳
	FixshiftEffectTime      *string                  `json:"fixshift_effect_time,omitempty"`        // 固定班次生效时间，精确到秒的时间戳
	MemberEffectTime        *string                  `json:"member_effect_time,omitempty"`          // 参加考勤的人员、部门变动生效时间，精确到秒的时间戳
	RestClockInNeedApproval *bool                    `json:"rest_clockIn_need_approval,omitempty"`  // 休息日打卡需审批
	ClockInNeedPhoto        *bool                    `json:"clockIn_need_photo,omitempty"`          // 每次打卡均需拍照
}

type GetGroupResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *GetGroupRespData `json:"data"` // 业务数据
}

func (resp *GetGroupResp) Success() bool {
	return resp.Code == 0
}

type ListGroupReqBuilder struct {
	apiReq *larkcore.ApiReq
	limit  int // 最大返回多少记录，当使用迭代器访问时才有效
}

func NewListGroupReqBuilder() *ListGroupReqBuilder {
	builder := &ListGroupReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 最大返回多少记录，当使用迭代器访问时才有效
func (builder *ListGroupReqBuilder) Limit(limit int) *ListGroupReqBuilder {
	builder.limit = limit
	return builder
}

// 分页大小
//
// 示例值：10
func (builder *ListGroupReqBuilder) PageSize(pageSize int) *ListGroupReqBuilder {
	builder.apiReq.QueryParams.Set("page_size", fmt.Sprint(pageSize))
	return builder
}

// 分页标记，第一次请求不填，表示从头开始遍历；分页查询结果还有更多项时会同时返回新的 page_token，下次遍历可采用该 page_token 获取查询结果
//
// 示例值：wgNOR1rmxogRvAsGl6CXlQ==
func (builder *ListGroupReqBuilder) PageToken(pageToken string) *ListGroupReqBuilder {
	builder.apiReq.QueryParams.Set("page_token", fmt.Sprint(pageToken))
	return builder
}

func (builder *ListGroupReqBuilder) Build() *ListGroupReq {
	req := &ListGroupReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.Limit = builder.limit
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type ListGroupReq struct {
	apiReq *larkcore.ApiReq
	Limit  int // 最多返回多少记录，只有在使用迭代器访问时，才有效

}

type ListGroupRespData struct {
	GroupList []*GroupMeta `json:"group_list,omitempty"` // 考勤组列表
	PageToken *string      `json:"page_token,omitempty"` //
	HasMore   *bool        `json:"has_more,omitempty"`   //
}

type ListGroupResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *ListGroupRespData `json:"data"` // 业务数据
}

func (resp *ListGroupResp) Success() bool {
	return resp.Code == 0
}

type SearchGroupReqBodyBuilder struct {
	groupName     string // 考勤组名称
	groupNameFlag bool
}

func NewSearchGroupReqBodyBuilder() *SearchGroupReqBodyBuilder {
	builder := &SearchGroupReqBodyBuilder{}
	return builder
}

// 考勤组名称
//
//示例值：考勤组1
func (builder *SearchGroupReqBodyBuilder) GroupName(groupName string) *SearchGroupReqBodyBuilder {
	builder.groupName = groupName
	builder.groupNameFlag = true
	return builder
}

func (builder *SearchGroupReqBodyBuilder) Build() *SearchGroupReqBody {
	req := &SearchGroupReqBody{}
	if builder.groupNameFlag {
		req.GroupName = &builder.groupName
	}
	return req
}

type SearchGroupPathReqBodyBuilder struct {
	groupName          string // 考勤组名称
	groupNameFlag      bool
	exactlyMatched     bool // 是否精准匹配，默认为false:模糊匹配; true:精准匹配
	exactlyMatchedFlag bool
}

func NewSearchGroupPathReqBodyBuilder() *SearchGroupPathReqBodyBuilder {
	builder := &SearchGroupPathReqBodyBuilder{}
	return builder
}

// 考勤组名称
//
// 示例值：考勤组1
func (builder *SearchGroupPathReqBodyBuilder) GroupName(groupName string) *SearchGroupPathReqBodyBuilder {
	builder.groupName = groupName
	builder.groupNameFlag = true
	return builder
}

func (builder *SearchGroupPathReqBodyBuilder) Build() (*SearchGroupReqBody, error) {
	req := &SearchGroupReqBody{}
	if builder.groupNameFlag {
		req.GroupName = &builder.groupName
	}
	return req, nil
}

type SearchGroupReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *SearchGroupReqBody
}

func NewSearchGroupReqBuilder() *SearchGroupReqBuilder {
	builder := &SearchGroupReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 按考勤组名称查询考勤组摘要信息。查询条件支持名称精确匹配和模糊匹配两种方式。查询结果按考勤组修改时间 desc 排序，且最大记录数为 10 条。
func (builder *SearchGroupReqBuilder) Body(body *SearchGroupReqBody) *SearchGroupReqBuilder {
	builder.body = body
	return builder
}

func (builder *SearchGroupReqBuilder) Build() *SearchGroupReq {
	req := &SearchGroupReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.Body = builder.body
	return req
}

type SearchGroupReqBody struct {
	GroupName *string `json:"group_name,omitempty"` // 考勤组名称

}

type SearchGroupReq struct {
	apiReq *larkcore.ApiReq
	Body   *SearchGroupReqBody `body:""`
}

type SearchGroupRespData struct {
	GroupList []*GroupMeta `json:"group_list,omitempty"` // 考勤组列表
}

type SearchGroupResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *SearchGroupRespData `json:"data"` // 业务数据
}

func (resp *SearchGroupResp) Success() bool {
	return resp.Code == 0
}

type CreateShiftReqBuilder struct {
	apiReq *larkcore.ApiReq
	shift  *Shift
}

func NewCreateShiftReqBuilder() *CreateShiftReqBuilder {
	builder := &CreateShiftReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 班次是描述一次考勤任务时间规则的统称，比如一天打多少次卡，每次卡的上下班时间，晚到多长时间算迟到，晚到多长时间算缺卡等。
func (builder *CreateShiftReqBuilder) Shift(shift *Shift) *CreateShiftReqBuilder {
	builder.shift = shift
	return builder
}

func (builder *CreateShiftReqBuilder) Build() *CreateShiftReq {
	req := &CreateShiftReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.Body = builder.shift
	return req
}

type CreateShiftReq struct {
	apiReq *larkcore.ApiReq
	Shift  *Shift `body:""`
}

type CreateShiftRespData struct {
	Shift *Shift `json:"shift,omitempty"` // 班次
}

type CreateShiftResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *CreateShiftRespData `json:"data"` // 业务数据
}

func (resp *CreateShiftResp) Success() bool {
	return resp.Code == 0
}

type DeleteShiftReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewDeleteShiftReqBuilder() *DeleteShiftReqBuilder {
	builder := &DeleteShiftReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 班次 ID，获取方式：1）[按名称查询班次](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/attendance-v1/shift/query) 2）[创建班次](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/attendance-v1/shift/create)
//
// 示例值：6919358778597097404
func (builder *DeleteShiftReqBuilder) ShiftId(shiftId string) *DeleteShiftReqBuilder {
	builder.apiReq.PathParams.Set("shift_id", fmt.Sprint(shiftId))
	return builder
}

func (builder *DeleteShiftReqBuilder) Build() *DeleteShiftReq {
	req := &DeleteShiftReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	return req
}

type DeleteShiftReq struct {
	apiReq *larkcore.ApiReq
}

type DeleteShiftResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *DeleteShiftResp) Success() bool {
	return resp.Code == 0
}

type GetShiftReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewGetShiftReqBuilder() *GetShiftReqBuilder {
	builder := &GetShiftReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 班次 ID，获取方式：1）[按名称查询班次](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/attendance-v1/shift/query) 2）[创建班次](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/attendance-v1/shift/create)
//
// 示例值：6919358778597097404
func (builder *GetShiftReqBuilder) ShiftId(shiftId string) *GetShiftReqBuilder {
	builder.apiReq.PathParams.Set("shift_id", fmt.Sprint(shiftId))
	return builder
}

func (builder *GetShiftReqBuilder) Build() *GetShiftReq {
	req := &GetShiftReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	return req
}

type GetShiftReq struct {
	apiReq *larkcore.ApiReq
}

type GetShiftRespData struct {
	ShiftId           *string              `json:"shift_id,omitempty"`              // 班次Id
	ShiftName         *string              `json:"shift_name,omitempty"`            // 班次名称
	PunchTimes        *int                 `json:"punch_times,omitempty"`           // 打卡次数
	IsFlexible        *bool                `json:"is_flexible,omitempty"`           // 是否弹性打卡
	FlexibleMinutes   *int                 `json:"flexible_minutes,omitempty"`      // 弹性打卡时间，设置【上班最多可晚到】与【下班最多可早走】时间，如果不设置flexible_rule则生效
	FlexibleRule      []*FlexibleRule      `json:"flexible_rule,omitempty"`         // 弹性打卡时间设置
	NoNeedOff         *bool                `json:"no_need_off,omitempty"`           // 不需要打下班卡
	PunchTimeRule     []*PunchTimeRule     `json:"punch_time_rule,omitempty"`       // 打卡规则
	LateOffLateOnRule []*LateOffLateOnRule `json:"late_off_late_on_rule,omitempty"` // 晚走晚到规则
	RestTimeRule      []*RestRule          `json:"rest_time_rule,omitempty"`        // 休息规则
}

type GetShiftResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *GetShiftRespData `json:"data"` // 业务数据
}

func (resp *GetShiftResp) Success() bool {
	return resp.Code == 0
}

type ListShiftReqBuilder struct {
	apiReq *larkcore.ApiReq
	limit  int // 最大返回多少记录，当使用迭代器访问时才有效
}

func NewListShiftReqBuilder() *ListShiftReqBuilder {
	builder := &ListShiftReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 最大返回多少记录，当使用迭代器访问时才有效
func (builder *ListShiftReqBuilder) Limit(limit int) *ListShiftReqBuilder {
	builder.limit = limit
	return builder
}

// 分页大小
//
// 示例值：10
func (builder *ListShiftReqBuilder) PageSize(pageSize int) *ListShiftReqBuilder {
	builder.apiReq.QueryParams.Set("page_size", fmt.Sprint(pageSize))
	return builder
}

// 分页标记，第一次请求不填，表示从头开始遍历；分页查询结果还有更多项时会同时返回新的 page_token，下次遍历可采用该 page_token 获取查询结果
//
// 示例值：YrkvQ1wGaPVta45tkxuGiQ==
func (builder *ListShiftReqBuilder) PageToken(pageToken string) *ListShiftReqBuilder {
	builder.apiReq.QueryParams.Set("page_token", fmt.Sprint(pageToken))
	return builder
}

func (builder *ListShiftReqBuilder) Build() *ListShiftReq {
	req := &ListShiftReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.Limit = builder.limit
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type ListShiftReq struct {
	apiReq *larkcore.ApiReq
	Limit  int // 最多返回多少记录，只有在使用迭代器访问时，才有效

}

type ListShiftRespData struct {
	ShiftList []*Shift `json:"shift_list,omitempty"` // 班次列表
	PageToken *string  `json:"page_token,omitempty"` //
	HasMore   *bool    `json:"has_more,omitempty"`   //
}

type ListShiftResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *ListShiftRespData `json:"data"` // 业务数据
}

func (resp *ListShiftResp) Success() bool {
	return resp.Code == 0
}

type QueryShiftReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewQueryShiftReqBuilder() *QueryShiftReqBuilder {
	builder := &QueryShiftReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 班次名称
//
// 示例值：早班
func (builder *QueryShiftReqBuilder) ShiftName(shiftName string) *QueryShiftReqBuilder {
	builder.apiReq.QueryParams.Set("shift_name", fmt.Sprint(shiftName))
	return builder
}

func (builder *QueryShiftReqBuilder) Build() *QueryShiftReq {
	req := &QueryShiftReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type QueryShiftReq struct {
	apiReq *larkcore.ApiReq
}

type QueryShiftRespData struct {
	ShiftId           *string              `json:"shift_id,omitempty"`              // 班次Id
	ShiftName         *string              `json:"shift_name,omitempty"`            // 班次名称
	PunchTimes        *int                 `json:"punch_times,omitempty"`           // 打卡次数
	IsFlexible        *bool                `json:"is_flexible,omitempty"`           // 是否弹性打卡
	FlexibleMinutes   *int                 `json:"flexible_minutes,omitempty"`      // 弹性打卡时间，设置【上班最多可晚到】与【下班最多可早走】时间，如果不设置flexible_rule则生效
	FlexibleRule      []*FlexibleRule      `json:"flexible_rule,omitempty"`         // 弹性打卡时间设置
	NoNeedOff         *bool                `json:"no_need_off,omitempty"`           // 不需要打下班卡
	PunchTimeRule     []*PunchTimeRule     `json:"punch_time_rule,omitempty"`       // 打卡规则
	LateOffLateOnRule []*LateOffLateOnRule `json:"late_off_late_on_rule,omitempty"` // 晚走晚到规则
	RestTimeRule      []*RestRule          `json:"rest_time_rule,omitempty"`        // 休息规则
}

type QueryShiftResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *QueryShiftRespData `json:"data"` // 业务数据
}

func (resp *QueryShiftResp) Success() bool {
	return resp.Code == 0
}

type CreateUserApprovalReqBodyBuilder struct {
	userApproval     *UserApproval // 审批信息
	userApprovalFlag bool
}

func NewCreateUserApprovalReqBodyBuilder() *CreateUserApprovalReqBodyBuilder {
	builder := &CreateUserApprovalReqBodyBuilder{}
	return builder
}

// 审批信息
//
//示例值：
func (builder *CreateUserApprovalReqBodyBuilder) UserApproval(userApproval *UserApproval) *CreateUserApprovalReqBodyBuilder {
	builder.userApproval = userApproval
	builder.userApprovalFlag = true
	return builder
}

func (builder *CreateUserApprovalReqBodyBuilder) Build() *CreateUserApprovalReqBody {
	req := &CreateUserApprovalReqBody{}
	if builder.userApprovalFlag {
		req.UserApproval = builder.userApproval
	}
	return req
}

type CreateUserApprovalPathReqBodyBuilder struct {
	userApproval     *UserApproval // 审批信息
	userApprovalFlag bool
}

func NewCreateUserApprovalPathReqBodyBuilder() *CreateUserApprovalPathReqBodyBuilder {
	builder := &CreateUserApprovalPathReqBodyBuilder{}
	return builder
}

// 审批信息
//
// 示例值：
func (builder *CreateUserApprovalPathReqBodyBuilder) UserApproval(userApproval *UserApproval) *CreateUserApprovalPathReqBodyBuilder {
	builder.userApproval = userApproval
	builder.userApprovalFlag = true
	return builder
}

func (builder *CreateUserApprovalPathReqBodyBuilder) Build() (*CreateUserApprovalReqBody, error) {
	req := &CreateUserApprovalReqBody{}
	if builder.userApprovalFlag {
		req.UserApproval = builder.userApproval
	}
	return req, nil
}

type CreateUserApprovalReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *CreateUserApprovalReqBody
}

func NewCreateUserApprovalReqBuilder() *CreateUserApprovalReqBuilder {
	builder := &CreateUserApprovalReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 请求体和响应体中的 user_id 的员工工号类型
//
// 示例值：employee_id
func (builder *CreateUserApprovalReqBuilder) EmployeeType(employeeType string) *CreateUserApprovalReqBuilder {
	builder.apiReq.QueryParams.Set("employee_type", fmt.Sprint(employeeType))
	return builder
}

// 由于部分企业使用的是自己的审批系统，而不是飞书审批系统，因此员工的请假、加班等数据无法流入到飞书考勤系统中，导致员工在请假时间段内依然收到打卡提醒，并且被记为缺卡。;;对于这些只使用飞书考勤系统，而未使用飞书审批系统的企业，可以通过考勤开放接口的形式，将三方审批结果数据回写到飞书考勤系统中。
func (builder *CreateUserApprovalReqBuilder) Body(body *CreateUserApprovalReqBody) *CreateUserApprovalReqBuilder {
	builder.body = body
	return builder
}

func (builder *CreateUserApprovalReqBuilder) Build() *CreateUserApprovalReq {
	req := &CreateUserApprovalReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.body
	return req
}

type CreateUserApprovalReqBody struct {
	UserApproval *UserApproval `json:"user_approval,omitempty"` // 审批信息
}

type CreateUserApprovalReq struct {
	apiReq *larkcore.ApiReq
	Body   *CreateUserApprovalReqBody `body:""`
}

type CreateUserApprovalRespData struct {
	UserApproval *UserApproval `json:"user_approval,omitempty"` // 审批信息
}

type CreateUserApprovalResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *CreateUserApprovalRespData `json:"data"` // 业务数据
}

func (resp *CreateUserApprovalResp) Success() bool {
	return resp.Code == 0
}

type QueryUserApprovalReqBodyBuilder struct {
	userIds           []string // employee_no 或 employee_id 列表
	userIdsFlag       bool
	checkDateFrom     int // 查询的起始工作日
	checkDateFromFlag bool
	checkDateTo       int // 查询的结束工作日，与 check_date_from 的时间间隔不超过 30 天
	checkDateToFlag   bool
	checkDateType     string // 查询依据的时间类型（不填默认依据PeriodTime）
	checkDateTypeFlag bool
	status            int // 查询状态（不填默认查询已通过状态）
	statusFlag        bool
	checkTimeFrom     string // 查询的起始时间，精确到秒的时间戳
	checkTimeFromFlag bool
	checkTimeTo       string // 查询的结束时间，精确到秒的时间戳
	checkTimeToFlag   bool
}

func NewQueryUserApprovalReqBodyBuilder() *QueryUserApprovalReqBodyBuilder {
	builder := &QueryUserApprovalReqBodyBuilder{}
	return builder
}

// employee_no 或 employee_id 列表
//
//示例值：["abd754f7"]
func (builder *QueryUserApprovalReqBodyBuilder) UserIds(userIds []string) *QueryUserApprovalReqBodyBuilder {
	builder.userIds = userIds
	builder.userIdsFlag = true
	return builder
}

// 查询的起始工作日
//
//示例值：20190817
func (builder *QueryUserApprovalReqBodyBuilder) CheckDateFrom(checkDateFrom int) *QueryUserApprovalReqBodyBuilder {
	builder.checkDateFrom = checkDateFrom
	builder.checkDateFromFlag = true
	return builder
}

// 查询的结束工作日，与 check_date_from 的时间间隔不超过 30 天
//
//示例值：20190820
func (builder *QueryUserApprovalReqBodyBuilder) CheckDateTo(checkDateTo int) *QueryUserApprovalReqBodyBuilder {
	builder.checkDateTo = checkDateTo
	builder.checkDateToFlag = true
	return builder
}

// 查询依据的时间类型（不填默认依据PeriodTime）
//
//示例值：PeriodTime
func (builder *QueryUserApprovalReqBodyBuilder) CheckDateType(checkDateType string) *QueryUserApprovalReqBodyBuilder {
	builder.checkDateType = checkDateType
	builder.checkDateTypeFlag = true
	return builder
}

// 查询状态（不填默认查询已通过状态）
//
//示例值：2
func (builder *QueryUserApprovalReqBodyBuilder) Status(status int) *QueryUserApprovalReqBodyBuilder {
	builder.status = status
	builder.statusFlag = true
	return builder
}

// 查询的起始时间，精确到秒的时间戳
//
//示例值：1566641088
func (builder *QueryUserApprovalReqBodyBuilder) CheckTimeFrom(checkTimeFrom string) *QueryUserApprovalReqBodyBuilder {
	builder.checkTimeFrom = checkTimeFrom
	builder.checkTimeFromFlag = true
	return builder
}

// 查询的结束时间，精确到秒的时间戳
//
//示例值：1592561088
func (builder *QueryUserApprovalReqBodyBuilder) CheckTimeTo(checkTimeTo string) *QueryUserApprovalReqBodyBuilder {
	builder.checkTimeTo = checkTimeTo
	builder.checkTimeToFlag = true
	return builder
}

func (builder *QueryUserApprovalReqBodyBuilder) Build() *QueryUserApprovalReqBody {
	req := &QueryUserApprovalReqBody{}
	if builder.userIdsFlag {
		req.UserIds = builder.userIds
	}
	if builder.checkDateFromFlag {
		req.CheckDateFrom = &builder.checkDateFrom
	}
	if builder.checkDateToFlag {
		req.CheckDateTo = &builder.checkDateTo
	}
	if builder.checkDateTypeFlag {
		req.CheckDateType = &builder.checkDateType
	}
	if builder.statusFlag {
		req.Status = &builder.status
	}
	if builder.checkTimeFromFlag {
		req.CheckTimeFrom = &builder.checkTimeFrom
	}
	if builder.checkTimeToFlag {
		req.CheckTimeTo = &builder.checkTimeTo
	}
	return req
}

type QueryUserApprovalPathReqBodyBuilder struct {
	userIds           []string // employee_no 或 employee_id 列表
	userIdsFlag       bool
	checkDateFrom     int // 查询的起始工作日
	checkDateFromFlag bool
	checkDateTo       int // 查询的结束工作日，与 check_date_from 的时间间隔不超过 30 天
	checkDateToFlag   bool
	checkDateType     string // 查询依据的时间类型（不填默认依据PeriodTime）
	checkDateTypeFlag bool
	status            int // 查询状态（不填默认查询已通过状态）
	statusFlag        bool
	checkTimeFrom     string // 查询的起始时间，精确到秒的时间戳
	checkTimeFromFlag bool
	checkTimeTo       string // 查询的结束时间，精确到秒的时间戳
	checkTimeToFlag   bool
}

func NewQueryUserApprovalPathReqBodyBuilder() *QueryUserApprovalPathReqBodyBuilder {
	builder := &QueryUserApprovalPathReqBodyBuilder{}
	return builder
}

// employee_no 或 employee_id 列表
//
// 示例值：["abd754f7"]
func (builder *QueryUserApprovalPathReqBodyBuilder) UserIds(userIds []string) *QueryUserApprovalPathReqBodyBuilder {
	builder.userIds = userIds
	builder.userIdsFlag = true
	return builder
}

// 查询的起始工作日
//
// 示例值：20190817
func (builder *QueryUserApprovalPathReqBodyBuilder) CheckDateFrom(checkDateFrom int) *QueryUserApprovalPathReqBodyBuilder {
	builder.checkDateFrom = checkDateFrom
	builder.checkDateFromFlag = true
	return builder
}

// 查询的结束工作日，与 check_date_from 的时间间隔不超过 30 天
//
// 示例值：20190820
func (builder *QueryUserApprovalPathReqBodyBuilder) CheckDateTo(checkDateTo int) *QueryUserApprovalPathReqBodyBuilder {
	builder.checkDateTo = checkDateTo
	builder.checkDateToFlag = true
	return builder
}

// 查询依据的时间类型（不填默认依据PeriodTime）
//
// 示例值：PeriodTime
func (builder *QueryUserApprovalPathReqBodyBuilder) CheckDateType(checkDateType string) *QueryUserApprovalPathReqBodyBuilder {
	builder.checkDateType = checkDateType
	builder.checkDateTypeFlag = true
	return builder
}

// 查询状态（不填默认查询已通过状态）
//
// 示例值：2
func (builder *QueryUserApprovalPathReqBodyBuilder) Status(status int) *QueryUserApprovalPathReqBodyBuilder {
	builder.status = status
	builder.statusFlag = true
	return builder
}

// 查询的起始时间，精确到秒的时间戳
//
// 示例值：1566641088
func (builder *QueryUserApprovalPathReqBodyBuilder) CheckTimeFrom(checkTimeFrom string) *QueryUserApprovalPathReqBodyBuilder {
	builder.checkTimeFrom = checkTimeFrom
	builder.checkTimeFromFlag = true
	return builder
}

// 查询的结束时间，精确到秒的时间戳
//
// 示例值：1592561088
func (builder *QueryUserApprovalPathReqBodyBuilder) CheckTimeTo(checkTimeTo string) *QueryUserApprovalPathReqBodyBuilder {
	builder.checkTimeTo = checkTimeTo
	builder.checkTimeToFlag = true
	return builder
}

func (builder *QueryUserApprovalPathReqBodyBuilder) Build() (*QueryUserApprovalReqBody, error) {
	req := &QueryUserApprovalReqBody{}
	if builder.userIdsFlag {
		req.UserIds = builder.userIds
	}
	if builder.checkDateFromFlag {
		req.CheckDateFrom = &builder.checkDateFrom
	}
	if builder.checkDateToFlag {
		req.CheckDateTo = &builder.checkDateTo
	}
	if builder.checkDateTypeFlag {
		req.CheckDateType = &builder.checkDateType
	}
	if builder.statusFlag {
		req.Status = &builder.status
	}
	if builder.checkTimeFromFlag {
		req.CheckTimeFrom = &builder.checkTimeFrom
	}
	if builder.checkTimeToFlag {
		req.CheckTimeTo = &builder.checkTimeTo
	}
	return req, nil
}

type QueryUserApprovalReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *QueryUserApprovalReqBody
}

func NewQueryUserApprovalReqBuilder() *QueryUserApprovalReqBuilder {
	builder := &QueryUserApprovalReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 请求体中的 user_ids 和响应体中的 user_id 的员工工号类型
//
// 示例值：employee_id
func (builder *QueryUserApprovalReqBuilder) EmployeeType(employeeType string) *QueryUserApprovalReqBuilder {
	builder.apiReq.QueryParams.Set("employee_type", fmt.Sprint(employeeType))
	return builder
}

// 获取员工在某段时间内的请假、加班、外出和出差四种审批的通过数据。
func (builder *QueryUserApprovalReqBuilder) Body(body *QueryUserApprovalReqBody) *QueryUserApprovalReqBuilder {
	builder.body = body
	return builder
}

func (builder *QueryUserApprovalReqBuilder) Build() *QueryUserApprovalReq {
	req := &QueryUserApprovalReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.body
	return req
}

type QueryUserApprovalReqBody struct {
	UserIds       []string `json:"user_ids,omitempty"`        // employee_no 或 employee_id 列表
	CheckDateFrom *int     `json:"check_date_from,omitempty"` // 查询的起始工作日
	CheckDateTo   *int     `json:"check_date_to,omitempty"`   // 查询的结束工作日，与 check_date_from 的时间间隔不超过 30 天
	CheckDateType *string  `json:"check_date_type,omitempty"` // 查询依据的时间类型（不填默认依据PeriodTime）
	Status        *int     `json:"status,omitempty"`          // 查询状态（不填默认查询已通过状态）
	CheckTimeFrom *string  `json:"check_time_from,omitempty"` // 查询的起始时间，精确到秒的时间戳
	CheckTimeTo   *string  `json:"check_time_to,omitempty"`   // 查询的结束时间，精确到秒的时间戳
}

type QueryUserApprovalReq struct {
	apiReq *larkcore.ApiReq
	Body   *QueryUserApprovalReqBody `body:""`
}

type QueryUserApprovalRespData struct {
	UserApprovals []*UserApproval `json:"user_approvals,omitempty"` // 审批结果列表
}

type QueryUserApprovalResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *QueryUserApprovalRespData `json:"data"` // 业务数据
}

func (resp *QueryUserApprovalResp) Success() bool {
	return resp.Code == 0
}

type BatchCreateUserDailyShiftReqBodyBuilder struct {
	userDailyShifts     []*UserDailyShift // 班表信息列表（数量限制50以内）
	userDailyShiftsFlag bool
	operatorId          string // 操作人uid，如果您未操作[考勤管理后台“API 接入”流程](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/attendance-v1/attendance-development-guidelines)，则此字段为必填字段
	operatorIdFlag      bool
}

func NewBatchCreateUserDailyShiftReqBodyBuilder() *BatchCreateUserDailyShiftReqBodyBuilder {
	builder := &BatchCreateUserDailyShiftReqBodyBuilder{}
	return builder
}

// 班表信息列表（数量限制50以内）
//
//示例值：
func (builder *BatchCreateUserDailyShiftReqBodyBuilder) UserDailyShifts(userDailyShifts []*UserDailyShift) *BatchCreateUserDailyShiftReqBodyBuilder {
	builder.userDailyShifts = userDailyShifts
	builder.userDailyShiftsFlag = true
	return builder
}

// 操作人uid，如果您未操作[考勤管理后台“API 接入”流程](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/attendance-v1/attendance-development-guidelines)，则此字段为必填字段
//
//示例值：dd31248a
func (builder *BatchCreateUserDailyShiftReqBodyBuilder) OperatorId(operatorId string) *BatchCreateUserDailyShiftReqBodyBuilder {
	builder.operatorId = operatorId
	builder.operatorIdFlag = true
	return builder
}

func (builder *BatchCreateUserDailyShiftReqBodyBuilder) Build() *BatchCreateUserDailyShiftReqBody {
	req := &BatchCreateUserDailyShiftReqBody{}
	if builder.userDailyShiftsFlag {
		req.UserDailyShifts = builder.userDailyShifts
	}
	if builder.operatorIdFlag {
		req.OperatorId = &builder.operatorId
	}
	return req
}

type BatchCreateUserDailyShiftPathReqBodyBuilder struct {
	userDailyShifts     []*UserDailyShift // 班表信息列表（数量限制50以内）
	userDailyShiftsFlag bool
	operatorId          string // 操作人uid，如果您未操作[考勤管理后台“API 接入”流程](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/attendance-v1/attendance-development-guidelines)，则此字段为必填字段
	operatorIdFlag      bool
}

func NewBatchCreateUserDailyShiftPathReqBodyBuilder() *BatchCreateUserDailyShiftPathReqBodyBuilder {
	builder := &BatchCreateUserDailyShiftPathReqBodyBuilder{}
	return builder
}

// 班表信息列表（数量限制50以内）
//
// 示例值：
func (builder *BatchCreateUserDailyShiftPathReqBodyBuilder) UserDailyShifts(userDailyShifts []*UserDailyShift) *BatchCreateUserDailyShiftPathReqBodyBuilder {
	builder.userDailyShifts = userDailyShifts
	builder.userDailyShiftsFlag = true
	return builder
}

// 操作人uid，如果您未操作[考勤管理后台“API 接入”流程](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/attendance-v1/attendance-development-guidelines)，则此字段为必填字段
//
// 示例值：dd31248a
func (builder *BatchCreateUserDailyShiftPathReqBodyBuilder) OperatorId(operatorId string) *BatchCreateUserDailyShiftPathReqBodyBuilder {
	builder.operatorId = operatorId
	builder.operatorIdFlag = true
	return builder
}

func (builder *BatchCreateUserDailyShiftPathReqBodyBuilder) Build() (*BatchCreateUserDailyShiftReqBody, error) {
	req := &BatchCreateUserDailyShiftReqBody{}
	if builder.userDailyShiftsFlag {
		req.UserDailyShifts = builder.userDailyShifts
	}
	if builder.operatorIdFlag {
		req.OperatorId = &builder.operatorId
	}
	return req, nil
}

type BatchCreateUserDailyShiftReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *BatchCreateUserDailyShiftReqBody
}

func NewBatchCreateUserDailyShiftReqBuilder() *BatchCreateUserDailyShiftReqBuilder {
	builder := &BatchCreateUserDailyShiftReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 请求体和响应体中的 user_id 的员工工号类型
//
// 示例值：employee_id
func (builder *BatchCreateUserDailyShiftReqBuilder) EmployeeType(employeeType string) *BatchCreateUserDailyShiftReqBuilder {
	builder.apiReq.QueryParams.Set("employee_type", fmt.Sprint(employeeType))
	return builder
}

// 班表是用来描述考勤组内人员每天按哪个班次进行上班。目前班表支持按一个整月对一位或多位人员进行排班。
func (builder *BatchCreateUserDailyShiftReqBuilder) Body(body *BatchCreateUserDailyShiftReqBody) *BatchCreateUserDailyShiftReqBuilder {
	builder.body = body
	return builder
}

func (builder *BatchCreateUserDailyShiftReqBuilder) Build() *BatchCreateUserDailyShiftReq {
	req := &BatchCreateUserDailyShiftReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.body
	return req
}

type BatchCreateUserDailyShiftReqBody struct {
	UserDailyShifts []*UserDailyShift `json:"user_daily_shifts,omitempty"` // 班表信息列表（数量限制50以内）
	OperatorId      *string           `json:"operator_id,omitempty"`       // 操作人uid，如果您未操作[考勤管理后台“API 接入”流程](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/attendance-v1/attendance-development-guidelines)，则此字段为必填字段
}

type BatchCreateUserDailyShiftReq struct {
	apiReq *larkcore.ApiReq
	Body   *BatchCreateUserDailyShiftReqBody `body:""`
}

type BatchCreateUserDailyShiftRespData struct {
	UserDailyShifts []*UserDailyShift `json:"user_daily_shifts,omitempty"` // 班表信息列表
}

type BatchCreateUserDailyShiftResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *BatchCreateUserDailyShiftRespData `json:"data"` // 业务数据
}

func (resp *BatchCreateUserDailyShiftResp) Success() bool {
	return resp.Code == 0
}

type QueryUserDailyShiftReqBodyBuilder struct {
	userIds           []string // employee_no 或 employee_id 列表
	userIdsFlag       bool
	checkDateFrom     int // 查询的起始工作日
	checkDateFromFlag bool
	checkDateTo       int // 查询的结束工作日
	checkDateToFlag   bool
}

func NewQueryUserDailyShiftReqBodyBuilder() *QueryUserDailyShiftReqBodyBuilder {
	builder := &QueryUserDailyShiftReqBodyBuilder{}
	return builder
}

// employee_no 或 employee_id 列表
//
//示例值：["abd754f7"]
func (builder *QueryUserDailyShiftReqBodyBuilder) UserIds(userIds []string) *QueryUserDailyShiftReqBodyBuilder {
	builder.userIds = userIds
	builder.userIdsFlag = true
	return builder
}

// 查询的起始工作日
//
//示例值：20190817
func (builder *QueryUserDailyShiftReqBodyBuilder) CheckDateFrom(checkDateFrom int) *QueryUserDailyShiftReqBodyBuilder {
	builder.checkDateFrom = checkDateFrom
	builder.checkDateFromFlag = true
	return builder
}

// 查询的结束工作日
//
//示例值：20190820
func (builder *QueryUserDailyShiftReqBodyBuilder) CheckDateTo(checkDateTo int) *QueryUserDailyShiftReqBodyBuilder {
	builder.checkDateTo = checkDateTo
	builder.checkDateToFlag = true
	return builder
}

func (builder *QueryUserDailyShiftReqBodyBuilder) Build() *QueryUserDailyShiftReqBody {
	req := &QueryUserDailyShiftReqBody{}
	if builder.userIdsFlag {
		req.UserIds = builder.userIds
	}
	if builder.checkDateFromFlag {
		req.CheckDateFrom = &builder.checkDateFrom
	}
	if builder.checkDateToFlag {
		req.CheckDateTo = &builder.checkDateTo
	}
	return req
}

type QueryUserDailyShiftPathReqBodyBuilder struct {
	userIds           []string // employee_no 或 employee_id 列表
	userIdsFlag       bool
	checkDateFrom     int // 查询的起始工作日
	checkDateFromFlag bool
	checkDateTo       int // 查询的结束工作日
	checkDateToFlag   bool
}

func NewQueryUserDailyShiftPathReqBodyBuilder() *QueryUserDailyShiftPathReqBodyBuilder {
	builder := &QueryUserDailyShiftPathReqBodyBuilder{}
	return builder
}

// employee_no 或 employee_id 列表
//
// 示例值：["abd754f7"]
func (builder *QueryUserDailyShiftPathReqBodyBuilder) UserIds(userIds []string) *QueryUserDailyShiftPathReqBodyBuilder {
	builder.userIds = userIds
	builder.userIdsFlag = true
	return builder
}

// 查询的起始工作日
//
// 示例值：20190817
func (builder *QueryUserDailyShiftPathReqBodyBuilder) CheckDateFrom(checkDateFrom int) *QueryUserDailyShiftPathReqBodyBuilder {
	builder.checkDateFrom = checkDateFrom
	builder.checkDateFromFlag = true
	return builder
}

// 查询的结束工作日
//
// 示例值：20190820
func (builder *QueryUserDailyShiftPathReqBodyBuilder) CheckDateTo(checkDateTo int) *QueryUserDailyShiftPathReqBodyBuilder {
	builder.checkDateTo = checkDateTo
	builder.checkDateToFlag = true
	return builder
}

func (builder *QueryUserDailyShiftPathReqBodyBuilder) Build() (*QueryUserDailyShiftReqBody, error) {
	req := &QueryUserDailyShiftReqBody{}
	if builder.userIdsFlag {
		req.UserIds = builder.userIds
	}
	if builder.checkDateFromFlag {
		req.CheckDateFrom = &builder.checkDateFrom
	}
	if builder.checkDateToFlag {
		req.CheckDateTo = &builder.checkDateTo
	}
	return req, nil
}

type QueryUserDailyShiftReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *QueryUserDailyShiftReqBody
}

func NewQueryUserDailyShiftReqBuilder() *QueryUserDailyShiftReqBuilder {
	builder := &QueryUserDailyShiftReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 请求体中的 user_ids 和响应体中的 user_id 的员工工号类型
//
// 示例值：employee_id
func (builder *QueryUserDailyShiftReqBuilder) EmployeeType(employeeType string) *QueryUserDailyShiftReqBuilder {
	builder.apiReq.QueryParams.Set("employee_type", fmt.Sprint(employeeType))
	return builder
}

// 支持查询多个用户的排班情况，查询的时间跨度不能超过 30 天。
func (builder *QueryUserDailyShiftReqBuilder) Body(body *QueryUserDailyShiftReqBody) *QueryUserDailyShiftReqBuilder {
	builder.body = body
	return builder
}

func (builder *QueryUserDailyShiftReqBuilder) Build() *QueryUserDailyShiftReq {
	req := &QueryUserDailyShiftReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.body
	return req
}

type QueryUserDailyShiftReqBody struct {
	UserIds       []string `json:"user_ids,omitempty"`        // employee_no 或 employee_id 列表
	CheckDateFrom *int     `json:"check_date_from,omitempty"` // 查询的起始工作日
	CheckDateTo   *int     `json:"check_date_to,omitempty"`   // 查询的结束工作日
}

type QueryUserDailyShiftReq struct {
	apiReq *larkcore.ApiReq
	Body   *QueryUserDailyShiftReqBody `body:""`
}

type QueryUserDailyShiftRespData struct {
	UserDailyShifts []*UserDailyShift `json:"user_daily_shifts,omitempty"` // 班表信息列表
}

type QueryUserDailyShiftResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *QueryUserDailyShiftRespData `json:"data"` // 业务数据
}

func (resp *QueryUserDailyShiftResp) Success() bool {
	return resp.Code == 0
}

type BatchCreateUserFlowReqBodyBuilder struct {
	flowRecords     []*UserFlow // 打卡流水记录列表(数量限制50)
	flowRecordsFlag bool
}

func NewBatchCreateUserFlowReqBodyBuilder() *BatchCreateUserFlowReqBodyBuilder {
	builder := &BatchCreateUserFlowReqBodyBuilder{}
	return builder
}

// 打卡流水记录列表(数量限制50)
//
//示例值：
func (builder *BatchCreateUserFlowReqBodyBuilder) FlowRecords(flowRecords []*UserFlow) *BatchCreateUserFlowReqBodyBuilder {
	builder.flowRecords = flowRecords
	builder.flowRecordsFlag = true
	return builder
}

func (builder *BatchCreateUserFlowReqBodyBuilder) Build() *BatchCreateUserFlowReqBody {
	req := &BatchCreateUserFlowReqBody{}
	if builder.flowRecordsFlag {
		req.FlowRecords = builder.flowRecords
	}
	return req
}

type BatchCreateUserFlowPathReqBodyBuilder struct {
	flowRecords     []*UserFlow // 打卡流水记录列表(数量限制50)
	flowRecordsFlag bool
}

func NewBatchCreateUserFlowPathReqBodyBuilder() *BatchCreateUserFlowPathReqBodyBuilder {
	builder := &BatchCreateUserFlowPathReqBodyBuilder{}
	return builder
}

// 打卡流水记录列表(数量限制50)
//
// 示例值：
func (builder *BatchCreateUserFlowPathReqBodyBuilder) FlowRecords(flowRecords []*UserFlow) *BatchCreateUserFlowPathReqBodyBuilder {
	builder.flowRecords = flowRecords
	builder.flowRecordsFlag = true
	return builder
}

func (builder *BatchCreateUserFlowPathReqBodyBuilder) Build() (*BatchCreateUserFlowReqBody, error) {
	req := &BatchCreateUserFlowReqBody{}
	if builder.flowRecordsFlag {
		req.FlowRecords = builder.flowRecords
	}
	return req, nil
}

type BatchCreateUserFlowReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *BatchCreateUserFlowReqBody
}

func NewBatchCreateUserFlowReqBuilder() *BatchCreateUserFlowReqBuilder {
	builder := &BatchCreateUserFlowReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 请求体和响应体中的 user_id 和 creator_id 的员工工号类型
//
// 示例值：employee_id
func (builder *BatchCreateUserFlowReqBuilder) EmployeeType(employeeType string) *BatchCreateUserFlowReqBuilder {
	builder.apiReq.QueryParams.Set("employee_type", fmt.Sprint(employeeType))
	return builder
}

// 导入授权内员工的打卡流水记录。导入后，会根据员工所在的考勤组班次规则，计算最终的打卡状态与结果。
func (builder *BatchCreateUserFlowReqBuilder) Body(body *BatchCreateUserFlowReqBody) *BatchCreateUserFlowReqBuilder {
	builder.body = body
	return builder
}

func (builder *BatchCreateUserFlowReqBuilder) Build() *BatchCreateUserFlowReq {
	req := &BatchCreateUserFlowReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.body
	return req
}

type BatchCreateUserFlowReqBody struct {
	FlowRecords []*UserFlow `json:"flow_records,omitempty"` // 打卡流水记录列表(数量限制50)
}

type BatchCreateUserFlowReq struct {
	apiReq *larkcore.ApiReq
	Body   *BatchCreateUserFlowReqBody `body:""`
}

type BatchCreateUserFlowRespData struct {
	FlowRecords []*UserFlow `json:"flow_records,omitempty"` // 打卡流水记录列表
}

type BatchCreateUserFlowResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *BatchCreateUserFlowRespData `json:"data"` // 业务数据
}

func (resp *BatchCreateUserFlowResp) Success() bool {
	return resp.Code == 0
}

type GetUserFlowReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewGetUserFlowReqBuilder() *GetUserFlowReqBuilder {
	builder := &GetUserFlowReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 打卡流水记录 ID，获取方式：1）[批量查询打卡流水记录](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/attendance-v1/user_flow/query) 2）[获取打卡结果](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/attendance-v1/user_task/query) 3）[导入打卡流水记录](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/attendance-v1/user_flow/batch_create)
//
// 示例值：6708236686834352397
func (builder *GetUserFlowReqBuilder) UserFlowId(userFlowId string) *GetUserFlowReqBuilder {
	builder.apiReq.PathParams.Set("user_flow_id", fmt.Sprint(userFlowId))
	return builder
}

// 响应体中的 user_id 和 creator_id 的员工工号类型
//
// 示例值：employee_id
func (builder *GetUserFlowReqBuilder) EmployeeType(employeeType string) *GetUserFlowReqBuilder {
	builder.apiReq.QueryParams.Set("employee_type", fmt.Sprint(employeeType))
	return builder
}

func (builder *GetUserFlowReqBuilder) Build() *GetUserFlowReq {
	req := &GetUserFlowReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type GetUserFlowReq struct {
	apiReq *larkcore.ApiReq
}

type GetUserFlowRespData struct {
	UserId       *string `json:"user_id,omitempty"`       // 用户工号
	CreatorId    *string `json:"creator_id,omitempty"`    // 记录创建者的工号
	LocationName *string `json:"location_name,omitempty"` // 打卡位置名称信息
	CheckTime    *string `json:"check_time,omitempty"`    // 打卡时间，精确到秒的时间戳
	Comment      *string `json:"comment,omitempty"`       // 打卡备注
	RecordId     *string `json:"record_id,omitempty"`     // 打卡记录ID

	Ssid      *string  `json:"ssid,omitempty"`       // 打卡wifi ssid
	Bssid     *string  `json:"bssid,omitempty"`      // 打卡wifi MAC地址
	IsField   *bool    `json:"is_field,omitempty"`   // 是否为外勤打卡
	IsWifi    *bool    `json:"is_wifi,omitempty"`    // 是否为wifi打卡
	Type      *int     `json:"type,omitempty"`       // 记录生成方式
	PhotoUrls []string `json:"photo_urls,omitempty"` // 打卡照片列表

	CheckResult *string `json:"check_result,omitempty"` // 打卡结果
}

type GetUserFlowResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *GetUserFlowRespData `json:"data"` // 业务数据
}

func (resp *GetUserFlowResp) Success() bool {
	return resp.Code == 0
}

type QueryUserFlowReqBodyBuilder struct {
	userIds           []string // employee_no 或 employee_id 列表，长度不超过 50
	userIdsFlag       bool
	checkTimeFrom     string // 查询的起始时间，时间戳
	checkTimeFromFlag bool
	checkTimeTo       string // 查询的结束时间，时间戳
	checkTimeToFlag   bool
}

func NewQueryUserFlowReqBodyBuilder() *QueryUserFlowReqBodyBuilder {
	builder := &QueryUserFlowReqBodyBuilder{}
	return builder
}

// employee_no 或 employee_id 列表，长度不超过 50
//
//示例值：[ "abd754f7"]
func (builder *QueryUserFlowReqBodyBuilder) UserIds(userIds []string) *QueryUserFlowReqBodyBuilder {
	builder.userIds = userIds
	builder.userIdsFlag = true
	return builder
}

// 查询的起始时间，时间戳
//
//示例值：1566641088
func (builder *QueryUserFlowReqBodyBuilder) CheckTimeFrom(checkTimeFrom string) *QueryUserFlowReqBodyBuilder {
	builder.checkTimeFrom = checkTimeFrom
	builder.checkTimeFromFlag = true
	return builder
}

// 查询的结束时间，时间戳
//
//示例值：1566641088
func (builder *QueryUserFlowReqBodyBuilder) CheckTimeTo(checkTimeTo string) *QueryUserFlowReqBodyBuilder {
	builder.checkTimeTo = checkTimeTo
	builder.checkTimeToFlag = true
	return builder
}

func (builder *QueryUserFlowReqBodyBuilder) Build() *QueryUserFlowReqBody {
	req := &QueryUserFlowReqBody{}
	if builder.userIdsFlag {
		req.UserIds = builder.userIds
	}
	if builder.checkTimeFromFlag {
		req.CheckTimeFrom = &builder.checkTimeFrom
	}
	if builder.checkTimeToFlag {
		req.CheckTimeTo = &builder.checkTimeTo
	}
	return req
}

type QueryUserFlowPathReqBodyBuilder struct {
	userIds           []string // employee_no 或 employee_id 列表，长度不超过 50
	userIdsFlag       bool
	checkTimeFrom     string // 查询的起始时间，时间戳
	checkTimeFromFlag bool
	checkTimeTo       string // 查询的结束时间，时间戳
	checkTimeToFlag   bool
}

func NewQueryUserFlowPathReqBodyBuilder() *QueryUserFlowPathReqBodyBuilder {
	builder := &QueryUserFlowPathReqBodyBuilder{}
	return builder
}

// employee_no 或 employee_id 列表，长度不超过 50
//
// 示例值：[ "abd754f7"]
func (builder *QueryUserFlowPathReqBodyBuilder) UserIds(userIds []string) *QueryUserFlowPathReqBodyBuilder {
	builder.userIds = userIds
	builder.userIdsFlag = true
	return builder
}

// 查询的起始时间，时间戳
//
// 示例值：1566641088
func (builder *QueryUserFlowPathReqBodyBuilder) CheckTimeFrom(checkTimeFrom string) *QueryUserFlowPathReqBodyBuilder {
	builder.checkTimeFrom = checkTimeFrom
	builder.checkTimeFromFlag = true
	return builder
}

// 查询的结束时间，时间戳
//
// 示例值：1566641088
func (builder *QueryUserFlowPathReqBodyBuilder) CheckTimeTo(checkTimeTo string) *QueryUserFlowPathReqBodyBuilder {
	builder.checkTimeTo = checkTimeTo
	builder.checkTimeToFlag = true
	return builder
}

func (builder *QueryUserFlowPathReqBodyBuilder) Build() (*QueryUserFlowReqBody, error) {
	req := &QueryUserFlowReqBody{}
	if builder.userIdsFlag {
		req.UserIds = builder.userIds
	}
	if builder.checkTimeFromFlag {
		req.CheckTimeFrom = &builder.checkTimeFrom
	}
	if builder.checkTimeToFlag {
		req.CheckTimeTo = &builder.checkTimeTo
	}
	return req, nil
}

type QueryUserFlowReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *QueryUserFlowReqBody
}

func NewQueryUserFlowReqBuilder() *QueryUserFlowReqBuilder {
	builder := &QueryUserFlowReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 请求体中的 user_ids 和响应体中的 user_id 的员工工号类型
//
// 示例值：employee_id
func (builder *QueryUserFlowReqBuilder) EmployeeType(employeeType string) *QueryUserFlowReqBuilder {
	builder.apiReq.QueryParams.Set("employee_type", fmt.Sprint(employeeType))
	return builder
}

// 由于新入职用户可以复用已离职用户的employee_no/employee_id。如果true，返回employee_no/employee_id对应的所有在职+离职用户数据；如果false，只返回employee_no/employee_id对应的在职或最近一个离职用户数据
//
// 示例值：true
func (builder *QueryUserFlowReqBuilder) IncludeTerminatedUser(includeTerminatedUser bool) *QueryUserFlowReqBuilder {
	builder.apiReq.QueryParams.Set("include_terminated_user", fmt.Sprint(includeTerminatedUser))
	return builder
}

// 批量查询授权内员工的实际打卡流水记录。例如，企业给一个员工设定的班次是上午 9 点和下午 6 点各打一次上下班卡，但是该员工在这期间打了多次卡，该接口会把所有的打卡记录都返回。
func (builder *QueryUserFlowReqBuilder) Body(body *QueryUserFlowReqBody) *QueryUserFlowReqBuilder {
	builder.body = body
	return builder
}

func (builder *QueryUserFlowReqBuilder) Build() *QueryUserFlowReq {
	req := &QueryUserFlowReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.body
	return req
}

type QueryUserFlowReqBody struct {
	UserIds       []string `json:"user_ids,omitempty"`        // employee_no 或 employee_id 列表，长度不超过 50
	CheckTimeFrom *string  `json:"check_time_from,omitempty"` // 查询的起始时间，时间戳
	CheckTimeTo   *string  `json:"check_time_to,omitempty"`   // 查询的结束时间，时间戳
}

type QueryUserFlowReq struct {
	apiReq *larkcore.ApiReq
	Body   *QueryUserFlowReqBody `body:""`
}

type QueryUserFlowRespData struct {
	UserFlowResults []*UserFlow `json:"user_flow_results,omitempty"` // 打卡记录列表
}

type QueryUserFlowResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *QueryUserFlowRespData `json:"data"` // 业务数据
}

func (resp *QueryUserFlowResp) Success() bool {
	return resp.Code == 0
}

type ModifyUserSettingReqBodyBuilder struct {
	userSetting     *UserSetting // 用户设置
	userSettingFlag bool
}

func NewModifyUserSettingReqBodyBuilder() *ModifyUserSettingReqBodyBuilder {
	builder := &ModifyUserSettingReqBodyBuilder{}
	return builder
}

// 用户设置
//
//示例值：
func (builder *ModifyUserSettingReqBodyBuilder) UserSetting(userSetting *UserSetting) *ModifyUserSettingReqBodyBuilder {
	builder.userSetting = userSetting
	builder.userSettingFlag = true
	return builder
}

func (builder *ModifyUserSettingReqBodyBuilder) Build() *ModifyUserSettingReqBody {
	req := &ModifyUserSettingReqBody{}
	if builder.userSettingFlag {
		req.UserSetting = builder.userSetting
	}
	return req
}

type ModifyUserSettingPathReqBodyBuilder struct {
	userSetting     *UserSetting // 用户设置
	userSettingFlag bool
}

func NewModifyUserSettingPathReqBodyBuilder() *ModifyUserSettingPathReqBodyBuilder {
	builder := &ModifyUserSettingPathReqBodyBuilder{}
	return builder
}

// 用户设置
//
// 示例值：
func (builder *ModifyUserSettingPathReqBodyBuilder) UserSetting(userSetting *UserSetting) *ModifyUserSettingPathReqBodyBuilder {
	builder.userSetting = userSetting
	builder.userSettingFlag = true
	return builder
}

func (builder *ModifyUserSettingPathReqBodyBuilder) Build() (*ModifyUserSettingReqBody, error) {
	req := &ModifyUserSettingReqBody{}
	if builder.userSettingFlag {
		req.UserSetting = builder.userSetting
	}
	return req, nil
}

type ModifyUserSettingReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *ModifyUserSettingReqBody
}

func NewModifyUserSettingReqBuilder() *ModifyUserSettingReqBuilder {
	builder := &ModifyUserSettingReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 请求体和响应体中的 user_id 的员工工号类型
//
// 示例值：employee_id
func (builder *ModifyUserSettingReqBuilder) EmployeeType(employeeType string) *ModifyUserSettingReqBuilder {
	builder.apiReq.QueryParams.Set("employee_type", fmt.Sprint(employeeType))
	return builder
}

// 修改授权内员工的用户设置信息，包括人脸照片文件 ID。
func (builder *ModifyUserSettingReqBuilder) Body(body *ModifyUserSettingReqBody) *ModifyUserSettingReqBuilder {
	builder.body = body
	return builder
}

func (builder *ModifyUserSettingReqBuilder) Build() *ModifyUserSettingReq {
	req := &ModifyUserSettingReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.body
	return req
}

type ModifyUserSettingReqBody struct {
	UserSetting *UserSetting `json:"user_setting,omitempty"` // 用户设置
}

type ModifyUserSettingReq struct {
	apiReq *larkcore.ApiReq
	Body   *ModifyUserSettingReqBody `body:""`
}

type ModifyUserSettingRespData struct {
	UserSetting *UserSetting `json:"user_setting,omitempty"` // 用户设置
}

type ModifyUserSettingResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *ModifyUserSettingRespData `json:"data"` // 业务数据
}

func (resp *ModifyUserSettingResp) Success() bool {
	return resp.Code == 0
}

type QueryUserSettingReqBodyBuilder struct {
	userIds     []string // employee_no 或 employee_id 列表
	userIdsFlag bool
}

func NewQueryUserSettingReqBodyBuilder() *QueryUserSettingReqBodyBuilder {
	builder := &QueryUserSettingReqBodyBuilder{}
	return builder
}

// employee_no 或 employee_id 列表
//
//示例值：["abd754f7"]
func (builder *QueryUserSettingReqBodyBuilder) UserIds(userIds []string) *QueryUserSettingReqBodyBuilder {
	builder.userIds = userIds
	builder.userIdsFlag = true
	return builder
}

func (builder *QueryUserSettingReqBodyBuilder) Build() *QueryUserSettingReqBody {
	req := &QueryUserSettingReqBody{}
	if builder.userIdsFlag {
		req.UserIds = builder.userIds
	}
	return req
}

type QueryUserSettingPathReqBodyBuilder struct {
	userIds     []string // employee_no 或 employee_id 列表
	userIdsFlag bool
}

func NewQueryUserSettingPathReqBodyBuilder() *QueryUserSettingPathReqBodyBuilder {
	builder := &QueryUserSettingPathReqBodyBuilder{}
	return builder
}

// employee_no 或 employee_id 列表
//
// 示例值：["abd754f7"]
func (builder *QueryUserSettingPathReqBodyBuilder) UserIds(userIds []string) *QueryUserSettingPathReqBodyBuilder {
	builder.userIds = userIds
	builder.userIdsFlag = true
	return builder
}

func (builder *QueryUserSettingPathReqBodyBuilder) Build() (*QueryUserSettingReqBody, error) {
	req := &QueryUserSettingReqBody{}
	if builder.userIdsFlag {
		req.UserIds = builder.userIds
	}
	return req, nil
}

type QueryUserSettingReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *QueryUserSettingReqBody
}

func NewQueryUserSettingReqBuilder() *QueryUserSettingReqBuilder {
	builder := &QueryUserSettingReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 请求体中的 user_ids 和响应体中的 user_id 的员工工号类型
//
// 示例值：employee_id
func (builder *QueryUserSettingReqBuilder) EmployeeType(employeeType string) *QueryUserSettingReqBuilder {
	builder.apiReq.QueryParams.Set("employee_type", fmt.Sprint(employeeType))
	return builder
}

// 批量查询授权内员工的用户设置信息，包括人脸照片文件 ID、人脸照片更新时间。
func (builder *QueryUserSettingReqBuilder) Body(body *QueryUserSettingReqBody) *QueryUserSettingReqBuilder {
	builder.body = body
	return builder
}

func (builder *QueryUserSettingReqBuilder) Build() *QueryUserSettingReq {
	req := &QueryUserSettingReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.body
	return req
}

type QueryUserSettingReqBody struct {
	UserIds []string `json:"user_ids,omitempty"` // employee_no 或 employee_id 列表
}

type QueryUserSettingReq struct {
	apiReq *larkcore.ApiReq
	Body   *QueryUserSettingReqBody `body:""`
}

type QueryUserSettingRespData struct {
	UserSettings []*UserSetting `json:"user_settings,omitempty"` // 用户设置信息列表
}

type QueryUserSettingResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *QueryUserSettingRespData `json:"data"` // 业务数据
}

func (resp *QueryUserSettingResp) Success() bool {
	return resp.Code == 0
}

type QueryUserStatsDataReqBodyBuilder struct {
	locale               string // 语言类型
	localeFlag           bool
	statsType            string // 统计类型
	statsTypeFlag        bool
	startDate            int // 开始时间
	startDateFlag        bool
	endDate              int // 结束时间;（时间间隔不超过 31 天）
	endDateFlag          bool
	userIds              []string // 查询的用户 ID 列表;（用户数量不超过 200）
	userIdsFlag          bool
	needHistory          bool // 是否需要历史数据
	needHistoryFlag      bool
	currentGroupOnly     bool // 只展示当前考勤组
	currentGroupOnlyFlag bool
	userId               string // 查询用户id，同【更新统计设置】、【查询统计设置】user_id（新系统用户必填，否则会报错）
	userIdFlag           bool
}

func NewQueryUserStatsDataReqBodyBuilder() *QueryUserStatsDataReqBodyBuilder {
	builder := &QueryUserStatsDataReqBodyBuilder{}
	return builder
}

// 语言类型
//
//示例值：zh
func (builder *QueryUserStatsDataReqBodyBuilder) Locale(locale string) *QueryUserStatsDataReqBodyBuilder {
	builder.locale = locale
	builder.localeFlag = true
	return builder
}

// 统计类型
//
//示例值：month
func (builder *QueryUserStatsDataReqBodyBuilder) StatsType(statsType string) *QueryUserStatsDataReqBodyBuilder {
	builder.statsType = statsType
	builder.statsTypeFlag = true
	return builder
}

// 开始时间
//
//示例值：20210316
func (builder *QueryUserStatsDataReqBodyBuilder) StartDate(startDate int) *QueryUserStatsDataReqBodyBuilder {
	builder.startDate = startDate
	builder.startDateFlag = true
	return builder
}

// 结束时间;（时间间隔不超过 31 天）
//
//示例值：20210323
func (builder *QueryUserStatsDataReqBodyBuilder) EndDate(endDate int) *QueryUserStatsDataReqBodyBuilder {
	builder.endDate = endDate
	builder.endDateFlag = true
	return builder
}

// 查询的用户 ID 列表;（用户数量不超过 200）
//
//示例值：[;		"ec8ddg56",;		"4dbb52f2",;		"4167842e";	]
func (builder *QueryUserStatsDataReqBodyBuilder) UserIds(userIds []string) *QueryUserStatsDataReqBodyBuilder {
	builder.userIds = userIds
	builder.userIdsFlag = true
	return builder
}

// 是否需要历史数据
//
//示例值：true
func (builder *QueryUserStatsDataReqBodyBuilder) NeedHistory(needHistory bool) *QueryUserStatsDataReqBodyBuilder {
	builder.needHistory = needHistory
	builder.needHistoryFlag = true
	return builder
}

// 只展示当前考勤组
//
//示例值：true
func (builder *QueryUserStatsDataReqBodyBuilder) CurrentGroupOnly(currentGroupOnly bool) *QueryUserStatsDataReqBodyBuilder {
	builder.currentGroupOnly = currentGroupOnly
	builder.currentGroupOnlyFlag = true
	return builder
}

// 查询用户id，同【更新统计设置】、【查询统计设置】user_id（新系统用户必填，否则会报错）
//
//示例值：ec8ddg56
func (builder *QueryUserStatsDataReqBodyBuilder) UserId(userId string) *QueryUserStatsDataReqBodyBuilder {
	builder.userId = userId
	builder.userIdFlag = true
	return builder
}

func (builder *QueryUserStatsDataReqBodyBuilder) Build() *QueryUserStatsDataReqBody {
	req := &QueryUserStatsDataReqBody{}
	if builder.localeFlag {
		req.Locale = &builder.locale
	}
	if builder.statsTypeFlag {
		req.StatsType = &builder.statsType
	}
	if builder.startDateFlag {
		req.StartDate = &builder.startDate
	}
	if builder.endDateFlag {
		req.EndDate = &builder.endDate
	}
	if builder.userIdsFlag {
		req.UserIds = builder.userIds
	}
	if builder.needHistoryFlag {
		req.NeedHistory = &builder.needHistory
	}
	if builder.currentGroupOnlyFlag {
		req.CurrentGroupOnly = &builder.currentGroupOnly
	}
	if builder.userIdFlag {
		req.UserId = &builder.userId
	}
	return req
}

type QueryUserStatsDataPathReqBodyBuilder struct {
	locale               string // 语言类型
	localeFlag           bool
	statsType            string // 统计类型
	statsTypeFlag        bool
	startDate            int // 开始时间
	startDateFlag        bool
	endDate              int // 结束时间;（时间间隔不超过 31 天）
	endDateFlag          bool
	userIds              []string // 查询的用户 ID 列表;（用户数量不超过 200）
	userIdsFlag          bool
	needHistory          bool // 是否需要历史数据
	needHistoryFlag      bool
	currentGroupOnly     bool // 只展示当前考勤组
	currentGroupOnlyFlag bool
	userId               string // 查询用户id，同【更新统计设置】、【查询统计设置】user_id（新系统用户必填，否则会报错）
	userIdFlag           bool
}

func NewQueryUserStatsDataPathReqBodyBuilder() *QueryUserStatsDataPathReqBodyBuilder {
	builder := &QueryUserStatsDataPathReqBodyBuilder{}
	return builder
}

// 语言类型
//
// 示例值：zh
func (builder *QueryUserStatsDataPathReqBodyBuilder) Locale(locale string) *QueryUserStatsDataPathReqBodyBuilder {
	builder.locale = locale
	builder.localeFlag = true
	return builder
}

// 统计类型
//
// 示例值：month
func (builder *QueryUserStatsDataPathReqBodyBuilder) StatsType(statsType string) *QueryUserStatsDataPathReqBodyBuilder {
	builder.statsType = statsType
	builder.statsTypeFlag = true
	return builder
}

// 开始时间
//
// 示例值：20210316
func (builder *QueryUserStatsDataPathReqBodyBuilder) StartDate(startDate int) *QueryUserStatsDataPathReqBodyBuilder {
	builder.startDate = startDate
	builder.startDateFlag = true
	return builder
}

// 结束时间;（时间间隔不超过 31 天）
//
// 示例值：20210323
func (builder *QueryUserStatsDataPathReqBodyBuilder) EndDate(endDate int) *QueryUserStatsDataPathReqBodyBuilder {
	builder.endDate = endDate
	builder.endDateFlag = true
	return builder
}

// 查询的用户 ID 列表;（用户数量不超过 200）
//
// 示例值：[;		"ec8ddg56",;		"4dbb52f2",;		"4167842e";	]
func (builder *QueryUserStatsDataPathReqBodyBuilder) UserIds(userIds []string) *QueryUserStatsDataPathReqBodyBuilder {
	builder.userIds = userIds
	builder.userIdsFlag = true
	return builder
}

// 是否需要历史数据
//
// 示例值：true
func (builder *QueryUserStatsDataPathReqBodyBuilder) NeedHistory(needHistory bool) *QueryUserStatsDataPathReqBodyBuilder {
	builder.needHistory = needHistory
	builder.needHistoryFlag = true
	return builder
}

// 只展示当前考勤组
//
// 示例值：true
func (builder *QueryUserStatsDataPathReqBodyBuilder) CurrentGroupOnly(currentGroupOnly bool) *QueryUserStatsDataPathReqBodyBuilder {
	builder.currentGroupOnly = currentGroupOnly
	builder.currentGroupOnlyFlag = true
	return builder
}

// 查询用户id，同【更新统计设置】、【查询统计设置】user_id（新系统用户必填，否则会报错）
//
// 示例值：ec8ddg56
func (builder *QueryUserStatsDataPathReqBodyBuilder) UserId(userId string) *QueryUserStatsDataPathReqBodyBuilder {
	builder.userId = userId
	builder.userIdFlag = true
	return builder
}

func (builder *QueryUserStatsDataPathReqBodyBuilder) Build() (*QueryUserStatsDataReqBody, error) {
	req := &QueryUserStatsDataReqBody{}
	if builder.localeFlag {
		req.Locale = &builder.locale
	}
	if builder.statsTypeFlag {
		req.StatsType = &builder.statsType
	}
	if builder.startDateFlag {
		req.StartDate = &builder.startDate
	}
	if builder.endDateFlag {
		req.EndDate = &builder.endDate
	}
	if builder.userIdsFlag {
		req.UserIds = builder.userIds
	}
	if builder.needHistoryFlag {
		req.NeedHistory = &builder.needHistory
	}
	if builder.currentGroupOnlyFlag {
		req.CurrentGroupOnly = &builder.currentGroupOnly
	}
	if builder.userIdFlag {
		req.UserId = &builder.userId
	}
	return req, nil
}

type QueryUserStatsDataReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *QueryUserStatsDataReqBody
}

func NewQueryUserStatsDataReqBuilder() *QueryUserStatsDataReqBuilder {
	builder := &QueryUserStatsDataReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 请求体中的 user_ids 和响应体中的 user_id 的员工工号类型
//
// 示例值：employee_id
func (builder *QueryUserStatsDataReqBuilder) EmployeeType(employeeType string) *QueryUserStatsDataReqBuilder {
	builder.apiReq.QueryParams.Set("employee_type", fmt.Sprint(employeeType))
	return builder
}

// 查询日度统计或月度统计的统计数据。
func (builder *QueryUserStatsDataReqBuilder) Body(body *QueryUserStatsDataReqBody) *QueryUserStatsDataReqBuilder {
	builder.body = body
	return builder
}

func (builder *QueryUserStatsDataReqBuilder) Build() *QueryUserStatsDataReq {
	req := &QueryUserStatsDataReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.body
	return req
}

type QueryUserStatsDataReqBody struct {
	Locale           *string  `json:"locale,omitempty"`             // 语言类型
	StatsType        *string  `json:"stats_type,omitempty"`         // 统计类型
	StartDate        *int     `json:"start_date,omitempty"`         // 开始时间
	EndDate          *int     `json:"end_date,omitempty"`           // 结束时间;（时间间隔不超过 31 天）
	UserIds          []string `json:"user_ids,omitempty"`           // 查询的用户 ID 列表;（用户数量不超过 200）
	NeedHistory      *bool    `json:"need_history,omitempty"`       // 是否需要历史数据
	CurrentGroupOnly *bool    `json:"current_group_only,omitempty"` // 只展示当前考勤组
	UserId           *string  `json:"user_id,omitempty"`            // 查询用户id，同【更新统计设置】、【查询统计设置】user_id（新系统用户必填，否则会报错）
}

type QueryUserStatsDataReq struct {
	apiReq *larkcore.ApiReq
	Body   *QueryUserStatsDataReqBody `body:""`
}

type QueryUserStatsDataRespData struct {
	UserDatas []*UserStatsData `json:"user_datas,omitempty"` // 用户统计数据（限制1000条，超过1000条会截断）
}

type QueryUserStatsDataResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *QueryUserStatsDataRespData `json:"data"` // 业务数据
}

func (resp *QueryUserStatsDataResp) Success() bool {
	return resp.Code == 0
}

type QueryUserStatsFieldReqBodyBuilder struct {
	locale        string // 语言类型
	localeFlag    bool
	statsType     string // 统计类型
	statsTypeFlag bool
	startDate     int // 开始时间
	startDateFlag bool
	endDate       int // 结束时间（时间间隔不超过 40 天）
	endDateFlag   bool
}

func NewQueryUserStatsFieldReqBodyBuilder() *QueryUserStatsFieldReqBodyBuilder {
	builder := &QueryUserStatsFieldReqBodyBuilder{}
	return builder
}

// 语言类型
//
//示例值：zh
func (builder *QueryUserStatsFieldReqBodyBuilder) Locale(locale string) *QueryUserStatsFieldReqBodyBuilder {
	builder.locale = locale
	builder.localeFlag = true
	return builder
}

// 统计类型
//
//示例值：daily
func (builder *QueryUserStatsFieldReqBodyBuilder) StatsType(statsType string) *QueryUserStatsFieldReqBodyBuilder {
	builder.statsType = statsType
	builder.statsTypeFlag = true
	return builder
}

// 开始时间
//
//示例值：20210316
func (builder *QueryUserStatsFieldReqBodyBuilder) StartDate(startDate int) *QueryUserStatsFieldReqBodyBuilder {
	builder.startDate = startDate
	builder.startDateFlag = true
	return builder
}

// 结束时间（时间间隔不超过 40 天）
//
//示例值：20210323
func (builder *QueryUserStatsFieldReqBodyBuilder) EndDate(endDate int) *QueryUserStatsFieldReqBodyBuilder {
	builder.endDate = endDate
	builder.endDateFlag = true
	return builder
}

func (builder *QueryUserStatsFieldReqBodyBuilder) Build() *QueryUserStatsFieldReqBody {
	req := &QueryUserStatsFieldReqBody{}
	if builder.localeFlag {
		req.Locale = &builder.locale
	}
	if builder.statsTypeFlag {
		req.StatsType = &builder.statsType
	}
	if builder.startDateFlag {
		req.StartDate = &builder.startDate
	}
	if builder.endDateFlag {
		req.EndDate = &builder.endDate
	}
	return req
}

type QueryUserStatsFieldPathReqBodyBuilder struct {
	locale        string // 语言类型
	localeFlag    bool
	statsType     string // 统计类型
	statsTypeFlag bool
	startDate     int // 开始时间
	startDateFlag bool
	endDate       int // 结束时间（时间间隔不超过 40 天）
	endDateFlag   bool
}

func NewQueryUserStatsFieldPathReqBodyBuilder() *QueryUserStatsFieldPathReqBodyBuilder {
	builder := &QueryUserStatsFieldPathReqBodyBuilder{}
	return builder
}

// 语言类型
//
// 示例值：zh
func (builder *QueryUserStatsFieldPathReqBodyBuilder) Locale(locale string) *QueryUserStatsFieldPathReqBodyBuilder {
	builder.locale = locale
	builder.localeFlag = true
	return builder
}

// 统计类型
//
// 示例值：daily
func (builder *QueryUserStatsFieldPathReqBodyBuilder) StatsType(statsType string) *QueryUserStatsFieldPathReqBodyBuilder {
	builder.statsType = statsType
	builder.statsTypeFlag = true
	return builder
}

// 开始时间
//
// 示例值：20210316
func (builder *QueryUserStatsFieldPathReqBodyBuilder) StartDate(startDate int) *QueryUserStatsFieldPathReqBodyBuilder {
	builder.startDate = startDate
	builder.startDateFlag = true
	return builder
}

// 结束时间（时间间隔不超过 40 天）
//
// 示例值：20210323
func (builder *QueryUserStatsFieldPathReqBodyBuilder) EndDate(endDate int) *QueryUserStatsFieldPathReqBodyBuilder {
	builder.endDate = endDate
	builder.endDateFlag = true
	return builder
}

func (builder *QueryUserStatsFieldPathReqBodyBuilder) Build() (*QueryUserStatsFieldReqBody, error) {
	req := &QueryUserStatsFieldReqBody{}
	if builder.localeFlag {
		req.Locale = &builder.locale
	}
	if builder.statsTypeFlag {
		req.StatsType = &builder.statsType
	}
	if builder.startDateFlag {
		req.StartDate = &builder.startDate
	}
	if builder.endDateFlag {
		req.EndDate = &builder.endDate
	}
	return req, nil
}

type QueryUserStatsFieldReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *QueryUserStatsFieldReqBody
}

func NewQueryUserStatsFieldReqBuilder() *QueryUserStatsFieldReqBuilder {
	builder := &QueryUserStatsFieldReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 响应体中的 user_id 的员工工号类型
//
// 示例值：employee_id
func (builder *QueryUserStatsFieldReqBuilder) EmployeeType(employeeType string) *QueryUserStatsFieldReqBuilder {
	builder.apiReq.QueryParams.Set("employee_type", fmt.Sprint(employeeType))
	return builder
}

// 查询考勤统计支持的日度统计或月度统计的统计表头。
func (builder *QueryUserStatsFieldReqBuilder) Body(body *QueryUserStatsFieldReqBody) *QueryUserStatsFieldReqBuilder {
	builder.body = body
	return builder
}

func (builder *QueryUserStatsFieldReqBuilder) Build() *QueryUserStatsFieldReq {
	req := &QueryUserStatsFieldReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.body
	return req
}

type QueryUserStatsFieldReqBody struct {
	Locale    *string `json:"locale,omitempty"`     // 语言类型
	StatsType *string `json:"stats_type,omitempty"` // 统计类型
	StartDate *int    `json:"start_date,omitempty"` // 开始时间
	EndDate   *int    `json:"end_date,omitempty"`   // 结束时间（时间间隔不超过 40 天）
}

type QueryUserStatsFieldReq struct {
	apiReq *larkcore.ApiReq
	Body   *QueryUserStatsFieldReqBody `body:""`
}

type QueryUserStatsFieldRespData struct {
	UserStatsField *UserStatsField `json:"user_stats_field,omitempty"` // 统计数据表头
}

type QueryUserStatsFieldResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *QueryUserStatsFieldRespData `json:"data"` // 业务数据
}

func (resp *QueryUserStatsFieldResp) Success() bool {
	return resp.Code == 0
}

type QueryUserStatsViewReqBodyBuilder struct {
	locale        string // 语言类型
	localeFlag    bool
	statsType     string // 统计类型
	statsTypeFlag bool
	userId        string // 查询用户id，同【查询统计数据】、【更新统计设置】user_id
	userIdFlag    bool
}

func NewQueryUserStatsViewReqBodyBuilder() *QueryUserStatsViewReqBodyBuilder {
	builder := &QueryUserStatsViewReqBodyBuilder{}
	return builder
}

// 语言类型
//
//示例值：zh
func (builder *QueryUserStatsViewReqBodyBuilder) Locale(locale string) *QueryUserStatsViewReqBodyBuilder {
	builder.locale = locale
	builder.localeFlag = true
	return builder
}

// 统计类型
//
//示例值：daily
func (builder *QueryUserStatsViewReqBodyBuilder) StatsType(statsType string) *QueryUserStatsViewReqBodyBuilder {
	builder.statsType = statsType
	builder.statsTypeFlag = true
	return builder
}

// 查询用户id，同【查询统计数据】、【更新统计设置】user_id
//
//示例值：dd31248a
func (builder *QueryUserStatsViewReqBodyBuilder) UserId(userId string) *QueryUserStatsViewReqBodyBuilder {
	builder.userId = userId
	builder.userIdFlag = true
	return builder
}

func (builder *QueryUserStatsViewReqBodyBuilder) Build() *QueryUserStatsViewReqBody {
	req := &QueryUserStatsViewReqBody{}
	if builder.localeFlag {
		req.Locale = &builder.locale
	}
	if builder.statsTypeFlag {
		req.StatsType = &builder.statsType
	}
	if builder.userIdFlag {
		req.UserId = &builder.userId
	}
	return req
}

type QueryUserStatsViewPathReqBodyBuilder struct {
	locale        string // 语言类型
	localeFlag    bool
	statsType     string // 统计类型
	statsTypeFlag bool
	userId        string // 查询用户id，同【查询统计数据】、【更新统计设置】user_id
	userIdFlag    bool
}

func NewQueryUserStatsViewPathReqBodyBuilder() *QueryUserStatsViewPathReqBodyBuilder {
	builder := &QueryUserStatsViewPathReqBodyBuilder{}
	return builder
}

// 语言类型
//
// 示例值：zh
func (builder *QueryUserStatsViewPathReqBodyBuilder) Locale(locale string) *QueryUserStatsViewPathReqBodyBuilder {
	builder.locale = locale
	builder.localeFlag = true
	return builder
}

// 统计类型
//
// 示例值：daily
func (builder *QueryUserStatsViewPathReqBodyBuilder) StatsType(statsType string) *QueryUserStatsViewPathReqBodyBuilder {
	builder.statsType = statsType
	builder.statsTypeFlag = true
	return builder
}

// 查询用户id，同【查询统计数据】、【更新统计设置】user_id
//
// 示例值：dd31248a
func (builder *QueryUserStatsViewPathReqBodyBuilder) UserId(userId string) *QueryUserStatsViewPathReqBodyBuilder {
	builder.userId = userId
	builder.userIdFlag = true
	return builder
}

func (builder *QueryUserStatsViewPathReqBodyBuilder) Build() (*QueryUserStatsViewReqBody, error) {
	req := &QueryUserStatsViewReqBody{}
	if builder.localeFlag {
		req.Locale = &builder.locale
	}
	if builder.statsTypeFlag {
		req.StatsType = &builder.statsType
	}
	if builder.userIdFlag {
		req.UserId = &builder.userId
	}
	return req, nil
}

type QueryUserStatsViewReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *QueryUserStatsViewReqBody
}

func NewQueryUserStatsViewReqBuilder() *QueryUserStatsViewReqBuilder {
	builder := &QueryUserStatsViewReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 响应体中的 user_id 的员工工号类型
//
// 示例值：employee_id
func (builder *QueryUserStatsViewReqBuilder) EmployeeType(employeeType string) *QueryUserStatsViewReqBuilder {
	builder.apiReq.QueryParams.Set("employee_type", fmt.Sprint(employeeType))
	return builder
}

// 查询开发者定制的日度统计或月度统计的统计报表表头设置信息。
func (builder *QueryUserStatsViewReqBuilder) Body(body *QueryUserStatsViewReqBody) *QueryUserStatsViewReqBuilder {
	builder.body = body
	return builder
}

func (builder *QueryUserStatsViewReqBuilder) Build() *QueryUserStatsViewReq {
	req := &QueryUserStatsViewReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.body
	return req
}

type QueryUserStatsViewReqBody struct {
	Locale    *string `json:"locale,omitempty"`     // 语言类型
	StatsType *string `json:"stats_type,omitempty"` // 统计类型
	UserId    *string `json:"user_id,omitempty"`    // 查询用户id，同【查询统计数据】、【更新统计设置】user_id
}

type QueryUserStatsViewReq struct {
	apiReq *larkcore.ApiReq
	Body   *QueryUserStatsViewReqBody `body:""`
}

type QueryUserStatsViewRespData struct {
	View *UserStatsView `json:"view,omitempty"` // 统计视图
}

type QueryUserStatsViewResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *QueryUserStatsViewRespData `json:"data"` // 业务数据
}

func (resp *QueryUserStatsViewResp) Success() bool {
	return resp.Code == 0
}

type UpdateUserStatsViewReqBodyBuilder struct {
	view     *UserStatsView // 统计设置
	viewFlag bool
}

func NewUpdateUserStatsViewReqBodyBuilder() *UpdateUserStatsViewReqBodyBuilder {
	builder := &UpdateUserStatsViewReqBodyBuilder{}
	return builder
}

// 统计设置
//
//示例值：
func (builder *UpdateUserStatsViewReqBodyBuilder) View(view *UserStatsView) *UpdateUserStatsViewReqBodyBuilder {
	builder.view = view
	builder.viewFlag = true
	return builder
}

func (builder *UpdateUserStatsViewReqBodyBuilder) Build() *UpdateUserStatsViewReqBody {
	req := &UpdateUserStatsViewReqBody{}
	if builder.viewFlag {
		req.View = builder.view
	}
	return req
}

type UpdateUserStatsViewPathReqBodyBuilder struct {
	view     *UserStatsView // 统计设置
	viewFlag bool
}

func NewUpdateUserStatsViewPathReqBodyBuilder() *UpdateUserStatsViewPathReqBodyBuilder {
	builder := &UpdateUserStatsViewPathReqBodyBuilder{}
	return builder
}

// 统计设置
//
// 示例值：
func (builder *UpdateUserStatsViewPathReqBodyBuilder) View(view *UserStatsView) *UpdateUserStatsViewPathReqBodyBuilder {
	builder.view = view
	builder.viewFlag = true
	return builder
}

func (builder *UpdateUserStatsViewPathReqBodyBuilder) Build() (*UpdateUserStatsViewReqBody, error) {
	req := &UpdateUserStatsViewReqBody{}
	if builder.viewFlag {
		req.View = builder.view
	}
	return req, nil
}

type UpdateUserStatsViewReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *UpdateUserStatsViewReqBody
}

func NewUpdateUserStatsViewReqBuilder() *UpdateUserStatsViewReqBuilder {
	builder := &UpdateUserStatsViewReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 用户视图 ID，获取方式：1）[查询统计设置](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/attendance-v1/user_stats_view/query)
//
// 示例值：TmpZNU5qTTJORFF6T1RnNU5UTTNOakV6TWl0dGIyNTBhQT09
func (builder *UpdateUserStatsViewReqBuilder) UserStatsViewId(userStatsViewId string) *UpdateUserStatsViewReqBuilder {
	builder.apiReq.PathParams.Set("user_stats_view_id", fmt.Sprint(userStatsViewId))
	return builder
}

// 员工工号类型
//
// 示例值：employee_id
func (builder *UpdateUserStatsViewReqBuilder) EmployeeType(employeeType string) *UpdateUserStatsViewReqBuilder {
	builder.apiReq.QueryParams.Set("employee_type", fmt.Sprint(employeeType))
	return builder
}

// 更新开发者定制的日度统计或月度统计的统计报表表头设置信息。
func (builder *UpdateUserStatsViewReqBuilder) Body(body *UpdateUserStatsViewReqBody) *UpdateUserStatsViewReqBuilder {
	builder.body = body
	return builder
}

func (builder *UpdateUserStatsViewReqBuilder) Build() *UpdateUserStatsViewReq {
	req := &UpdateUserStatsViewReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.body
	return req
}

type UpdateUserStatsViewReqBody struct {
	View *UserStatsView `json:"view,omitempty"` // 统计设置
}

type UpdateUserStatsViewReq struct {
	apiReq *larkcore.ApiReq
	Body   *UpdateUserStatsViewReqBody `body:""`
}

type UpdateUserStatsViewRespData struct {
	View *UserStatsView `json:"view,omitempty"` // 视图
}

type UpdateUserStatsViewResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *UpdateUserStatsViewRespData `json:"data"` // 业务数据
}

func (resp *UpdateUserStatsViewResp) Success() bool {
	return resp.Code == 0
}

type QueryUserTaskReqBodyBuilder struct {
	userIds           []string // employee_no 或 employee_id 列表，长度不超过 50
	userIdsFlag       bool
	checkDateFrom     int // 查询的起始工作日
	checkDateFromFlag bool
	checkDateTo       int // 查询的结束工作日
	checkDateToFlag   bool
}

func NewQueryUserTaskReqBodyBuilder() *QueryUserTaskReqBodyBuilder {
	builder := &QueryUserTaskReqBodyBuilder{}
	return builder
}

// employee_no 或 employee_id 列表，长度不超过 50
//
//示例值：abd754f7
func (builder *QueryUserTaskReqBodyBuilder) UserIds(userIds []string) *QueryUserTaskReqBodyBuilder {
	builder.userIds = userIds
	builder.userIdsFlag = true
	return builder
}

// 查询的起始工作日
//
//示例值：20190817
func (builder *QueryUserTaskReqBodyBuilder) CheckDateFrom(checkDateFrom int) *QueryUserTaskReqBodyBuilder {
	builder.checkDateFrom = checkDateFrom
	builder.checkDateFromFlag = true
	return builder
}

// 查询的结束工作日
//
//示例值：20190820
func (builder *QueryUserTaskReqBodyBuilder) CheckDateTo(checkDateTo int) *QueryUserTaskReqBodyBuilder {
	builder.checkDateTo = checkDateTo
	builder.checkDateToFlag = true
	return builder
}

func (builder *QueryUserTaskReqBodyBuilder) Build() *QueryUserTaskReqBody {
	req := &QueryUserTaskReqBody{}
	if builder.userIdsFlag {
		req.UserIds = builder.userIds
	}
	if builder.checkDateFromFlag {
		req.CheckDateFrom = &builder.checkDateFrom
	}
	if builder.checkDateToFlag {
		req.CheckDateTo = &builder.checkDateTo
	}
	return req
}

type QueryUserTaskPathReqBodyBuilder struct {
	userIds           []string // employee_no 或 employee_id 列表，长度不超过 50
	userIdsFlag       bool
	checkDateFrom     int // 查询的起始工作日
	checkDateFromFlag bool
	checkDateTo       int // 查询的结束工作日
	checkDateToFlag   bool
}

func NewQueryUserTaskPathReqBodyBuilder() *QueryUserTaskPathReqBodyBuilder {
	builder := &QueryUserTaskPathReqBodyBuilder{}
	return builder
}

// employee_no 或 employee_id 列表，长度不超过 50
//
// 示例值：abd754f7
func (builder *QueryUserTaskPathReqBodyBuilder) UserIds(userIds []string) *QueryUserTaskPathReqBodyBuilder {
	builder.userIds = userIds
	builder.userIdsFlag = true
	return builder
}

// 查询的起始工作日
//
// 示例值：20190817
func (builder *QueryUserTaskPathReqBodyBuilder) CheckDateFrom(checkDateFrom int) *QueryUserTaskPathReqBodyBuilder {
	builder.checkDateFrom = checkDateFrom
	builder.checkDateFromFlag = true
	return builder
}

// 查询的结束工作日
//
// 示例值：20190820
func (builder *QueryUserTaskPathReqBodyBuilder) CheckDateTo(checkDateTo int) *QueryUserTaskPathReqBodyBuilder {
	builder.checkDateTo = checkDateTo
	builder.checkDateToFlag = true
	return builder
}

func (builder *QueryUserTaskPathReqBodyBuilder) Build() (*QueryUserTaskReqBody, error) {
	req := &QueryUserTaskReqBody{}
	if builder.userIdsFlag {
		req.UserIds = builder.userIds
	}
	if builder.checkDateFromFlag {
		req.CheckDateFrom = &builder.checkDateFrom
	}
	if builder.checkDateToFlag {
		req.CheckDateTo = &builder.checkDateTo
	}
	return req, nil
}

type QueryUserTaskReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *QueryUserTaskReqBody
}

func NewQueryUserTaskReqBuilder() *QueryUserTaskReqBuilder {
	builder := &QueryUserTaskReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 员工工号类型
//
// 示例值：employee_id
func (builder *QueryUserTaskReqBuilder) EmployeeType(employeeType string) *QueryUserTaskReqBuilder {
	builder.apiReq.QueryParams.Set("employee_type", fmt.Sprint(employeeType))
	return builder
}

// 是否忽略无效和没有权限的用户。如果 true，则返回有效用户的信息，并告知无效和没有权限的用户信息；如果 false，且 user_ids 中存在无效或没有权限的用户，则返回错误
//
// 示例值：true
func (builder *QueryUserTaskReqBuilder) IgnoreInvalidUsers(ignoreInvalidUsers bool) *QueryUserTaskReqBuilder {
	builder.apiReq.QueryParams.Set("ignore_invalid_users", fmt.Sprint(ignoreInvalidUsers))
	return builder
}

// 由于新入职员工可以复用已离职员工的 employee_no/employee_id，如果 true，则返回 employee_no/employee_id 对应的所有在职 + 离职员工的数据；如果 false，则只返回 employee_no/employee_id 对应的在职或最近一个离职员工的数据
//
// 示例值：true
func (builder *QueryUserTaskReqBuilder) IncludeTerminatedUser(includeTerminatedUser bool) *QueryUserTaskReqBuilder {
	builder.apiReq.QueryParams.Set("include_terminated_user", fmt.Sprint(includeTerminatedUser))
	return builder
}

// 获取企业内员工的实际打卡结果，包括上班打卡结果和下班打卡结果。
func (builder *QueryUserTaskReqBuilder) Body(body *QueryUserTaskReqBody) *QueryUserTaskReqBuilder {
	builder.body = body
	return builder
}

func (builder *QueryUserTaskReqBuilder) Build() *QueryUserTaskReq {
	req := &QueryUserTaskReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.body
	return req
}

type QueryUserTaskReqBody struct {
	UserIds       []string `json:"user_ids,omitempty"`        // employee_no 或 employee_id 列表，长度不超过 50
	CheckDateFrom *int     `json:"check_date_from,omitempty"` // 查询的起始工作日
	CheckDateTo   *int     `json:"check_date_to,omitempty"`   // 查询的结束工作日
}

type QueryUserTaskReq struct {
	apiReq *larkcore.ApiReq
	Body   *QueryUserTaskReqBody `body:""`
}

type QueryUserTaskRespData struct {
	UserTaskResults     []*UserTask `json:"user_task_results,omitempty"`     // 打卡任务列表
	InvalidUserIds      []string    `json:"invalid_user_ids,omitempty"`      // 无效用户 ID 列表
	UnauthorizedUserIds []string    `json:"unauthorized_user_ids,omitempty"` // 没有权限用户 ID 列表
}

type QueryUserTaskResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *QueryUserTaskRespData `json:"data"` // 业务数据
}

func (resp *QueryUserTaskResp) Success() bool {
	return resp.Code == 0
}

type CreateUserTaskRemedyReqBuilder struct {
	apiReq         *larkcore.ApiReq
	userTaskRemedy *UserTaskRemedy
}

func NewCreateUserTaskRemedyReqBuilder() *CreateUserTaskRemedyReqBuilder {
	builder := &CreateUserTaskRemedyReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 请求体和响应体中的 user_id 的员工工号类型
//
// 示例值：employee_id
func (builder *CreateUserTaskRemedyReqBuilder) EmployeeType(employeeType string) *CreateUserTaskRemedyReqBuilder {
	builder.apiReq.QueryParams.Set("employee_type", fmt.Sprint(employeeType))
	return builder
}

// 对于只使用飞书考勤系统而未使用飞书审批系统的企业，可以通过该接口，将在三方审批系统中发起的补卡审批数据，写入到飞书考勤系统中，状态为审批中。写入后可以由[通知审批状态更新](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/attendance-v1/approval_info/process) 进行状态更新
func (builder *CreateUserTaskRemedyReqBuilder) UserTaskRemedy(userTaskRemedy *UserTaskRemedy) *CreateUserTaskRemedyReqBuilder {
	builder.userTaskRemedy = userTaskRemedy
	return builder
}

func (builder *CreateUserTaskRemedyReqBuilder) Build() *CreateUserTaskRemedyReq {
	req := &CreateUserTaskRemedyReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.userTaskRemedy
	return req
}

type CreateUserTaskRemedyReq struct {
	apiReq         *larkcore.ApiReq
	UserTaskRemedy *UserTaskRemedy `body:""`
}

type CreateUserTaskRemedyRespData struct {
	UserRemedy *UserTaskRemedy `json:"user_remedy,omitempty"` // 补卡审批信息
}

type CreateUserTaskRemedyResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *CreateUserTaskRemedyRespData `json:"data"` // 业务数据
}

func (resp *CreateUserTaskRemedyResp) Success() bool {
	return resp.Code == 0
}

type QueryUserTaskRemedyReqBodyBuilder struct {
	userIds           []string // employee_no 或 employee_id 列表
	userIdsFlag       bool
	checkTimeFrom     string // 查询的起始时间，精确到秒的时间戳
	checkTimeFromFlag bool
	checkTimeTo       string // 查询的结束时间，精确到秒的时间戳
	checkTimeToFlag   bool
	checkDateType     string // 查询依据的时间类型（不填默认依据PeriodTime）
	checkDateTypeFlag bool
	status            int // 查询状态（不填默认查询已通过状态）
	statusFlag        bool
}

func NewQueryUserTaskRemedyReqBodyBuilder() *QueryUserTaskRemedyReqBodyBuilder {
	builder := &QueryUserTaskRemedyReqBodyBuilder{}
	return builder
}

// employee_no 或 employee_id 列表
//
//示例值：["abd754f7"]
func (builder *QueryUserTaskRemedyReqBodyBuilder) UserIds(userIds []string) *QueryUserTaskRemedyReqBodyBuilder {
	builder.userIds = userIds
	builder.userIdsFlag = true
	return builder
}

// 查询的起始时间，精确到秒的时间戳
//
//示例值：1566641088
func (builder *QueryUserTaskRemedyReqBodyBuilder) CheckTimeFrom(checkTimeFrom string) *QueryUserTaskRemedyReqBodyBuilder {
	builder.checkTimeFrom = checkTimeFrom
	builder.checkTimeFromFlag = true
	return builder
}

// 查询的结束时间，精确到秒的时间戳
//
//示例值：1592561088
func (builder *QueryUserTaskRemedyReqBodyBuilder) CheckTimeTo(checkTimeTo string) *QueryUserTaskRemedyReqBodyBuilder {
	builder.checkTimeTo = checkTimeTo
	builder.checkTimeToFlag = true
	return builder
}

// 查询依据的时间类型（不填默认依据PeriodTime）
//
//示例值：PeriodTime
func (builder *QueryUserTaskRemedyReqBodyBuilder) CheckDateType(checkDateType string) *QueryUserTaskRemedyReqBodyBuilder {
	builder.checkDateType = checkDateType
	builder.checkDateTypeFlag = true
	return builder
}

// 查询状态（不填默认查询已通过状态）
//
//示例值：2
func (builder *QueryUserTaskRemedyReqBodyBuilder) Status(status int) *QueryUserTaskRemedyReqBodyBuilder {
	builder.status = status
	builder.statusFlag = true
	return builder
}

func (builder *QueryUserTaskRemedyReqBodyBuilder) Build() *QueryUserTaskRemedyReqBody {
	req := &QueryUserTaskRemedyReqBody{}
	if builder.userIdsFlag {
		req.UserIds = builder.userIds
	}
	if builder.checkTimeFromFlag {
		req.CheckTimeFrom = &builder.checkTimeFrom
	}
	if builder.checkTimeToFlag {
		req.CheckTimeTo = &builder.checkTimeTo
	}
	if builder.checkDateTypeFlag {
		req.CheckDateType = &builder.checkDateType
	}
	if builder.statusFlag {
		req.Status = &builder.status
	}
	return req
}

type QueryUserTaskRemedyPathReqBodyBuilder struct {
	userIds           []string // employee_no 或 employee_id 列表
	userIdsFlag       bool
	checkTimeFrom     string // 查询的起始时间，精确到秒的时间戳
	checkTimeFromFlag bool
	checkTimeTo       string // 查询的结束时间，精确到秒的时间戳
	checkTimeToFlag   bool
	checkDateType     string // 查询依据的时间类型（不填默认依据PeriodTime）
	checkDateTypeFlag bool
	status            int // 查询状态（不填默认查询已通过状态）
	statusFlag        bool
}

func NewQueryUserTaskRemedyPathReqBodyBuilder() *QueryUserTaskRemedyPathReqBodyBuilder {
	builder := &QueryUserTaskRemedyPathReqBodyBuilder{}
	return builder
}

// employee_no 或 employee_id 列表
//
// 示例值：["abd754f7"]
func (builder *QueryUserTaskRemedyPathReqBodyBuilder) UserIds(userIds []string) *QueryUserTaskRemedyPathReqBodyBuilder {
	builder.userIds = userIds
	builder.userIdsFlag = true
	return builder
}

// 查询的起始时间，精确到秒的时间戳
//
// 示例值：1566641088
func (builder *QueryUserTaskRemedyPathReqBodyBuilder) CheckTimeFrom(checkTimeFrom string) *QueryUserTaskRemedyPathReqBodyBuilder {
	builder.checkTimeFrom = checkTimeFrom
	builder.checkTimeFromFlag = true
	return builder
}

// 查询的结束时间，精确到秒的时间戳
//
// 示例值：1592561088
func (builder *QueryUserTaskRemedyPathReqBodyBuilder) CheckTimeTo(checkTimeTo string) *QueryUserTaskRemedyPathReqBodyBuilder {
	builder.checkTimeTo = checkTimeTo
	builder.checkTimeToFlag = true
	return builder
}

// 查询依据的时间类型（不填默认依据PeriodTime）
//
// 示例值：PeriodTime
func (builder *QueryUserTaskRemedyPathReqBodyBuilder) CheckDateType(checkDateType string) *QueryUserTaskRemedyPathReqBodyBuilder {
	builder.checkDateType = checkDateType
	builder.checkDateTypeFlag = true
	return builder
}

// 查询状态（不填默认查询已通过状态）
//
// 示例值：2
func (builder *QueryUserTaskRemedyPathReqBodyBuilder) Status(status int) *QueryUserTaskRemedyPathReqBodyBuilder {
	builder.status = status
	builder.statusFlag = true
	return builder
}

func (builder *QueryUserTaskRemedyPathReqBodyBuilder) Build() (*QueryUserTaskRemedyReqBody, error) {
	req := &QueryUserTaskRemedyReqBody{}
	if builder.userIdsFlag {
		req.UserIds = builder.userIds
	}
	if builder.checkTimeFromFlag {
		req.CheckTimeFrom = &builder.checkTimeFrom
	}
	if builder.checkTimeToFlag {
		req.CheckTimeTo = &builder.checkTimeTo
	}
	if builder.checkDateTypeFlag {
		req.CheckDateType = &builder.checkDateType
	}
	if builder.statusFlag {
		req.Status = &builder.status
	}
	return req, nil
}

type QueryUserTaskRemedyReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *QueryUserTaskRemedyReqBody
}

func NewQueryUserTaskRemedyReqBuilder() *QueryUserTaskRemedyReqBuilder {
	builder := &QueryUserTaskRemedyReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 请求体中的 user_ids 和响应体中的 user_id 的员工工号类型
//
// 示例值：employee_id
func (builder *QueryUserTaskRemedyReqBuilder) EmployeeType(employeeType string) *QueryUserTaskRemedyReqBuilder {
	builder.apiReq.QueryParams.Set("employee_type", fmt.Sprint(employeeType))
	return builder
}

// 获取授权内员工的补卡记录。
func (builder *QueryUserTaskRemedyReqBuilder) Body(body *QueryUserTaskRemedyReqBody) *QueryUserTaskRemedyReqBuilder {
	builder.body = body
	return builder
}

func (builder *QueryUserTaskRemedyReqBuilder) Build() *QueryUserTaskRemedyReq {
	req := &QueryUserTaskRemedyReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.body
	return req
}

type QueryUserTaskRemedyReqBody struct {
	UserIds       []string `json:"user_ids,omitempty"`        // employee_no 或 employee_id 列表
	CheckTimeFrom *string  `json:"check_time_from,omitempty"` // 查询的起始时间，精确到秒的时间戳
	CheckTimeTo   *string  `json:"check_time_to,omitempty"`   // 查询的结束时间，精确到秒的时间戳
	CheckDateType *string  `json:"check_date_type,omitempty"` // 查询依据的时间类型（不填默认依据PeriodTime）
	Status        *int     `json:"status,omitempty"`          // 查询状态（不填默认查询已通过状态）
}

type QueryUserTaskRemedyReq struct {
	apiReq *larkcore.ApiReq
	Body   *QueryUserTaskRemedyReqBody `body:""`
}

type QueryUserTaskRemedyRespData struct {
	UserRemedys []*UserTaskRemedy `json:"user_remedys,omitempty"` // 补卡记录列表
}

type QueryUserTaskRemedyResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *QueryUserTaskRemedyRespData `json:"data"` // 业务数据
}

func (resp *QueryUserTaskRemedyResp) Success() bool {
	return resp.Code == 0
}

type QueryUserAllowedRemedysUserTaskRemedyReqBodyBuilder struct {
	userId         string // 用户 ID
	userIdFlag     bool
	remedyDate     int // 补卡日期
	remedyDateFlag bool
}

func NewQueryUserAllowedRemedysUserTaskRemedyReqBodyBuilder() *QueryUserAllowedRemedysUserTaskRemedyReqBodyBuilder {
	builder := &QueryUserAllowedRemedysUserTaskRemedyReqBodyBuilder{}
	return builder
}

// 用户 ID
//
//示例值：abd754f7
func (builder *QueryUserAllowedRemedysUserTaskRemedyReqBodyBuilder) UserId(userId string) *QueryUserAllowedRemedysUserTaskRemedyReqBodyBuilder {
	builder.userId = userId
	builder.userIdFlag = true
	return builder
}

// 补卡日期
//
//示例值：20210104
func (builder *QueryUserAllowedRemedysUserTaskRemedyReqBodyBuilder) RemedyDate(remedyDate int) *QueryUserAllowedRemedysUserTaskRemedyReqBodyBuilder {
	builder.remedyDate = remedyDate
	builder.remedyDateFlag = true
	return builder
}

func (builder *QueryUserAllowedRemedysUserTaskRemedyReqBodyBuilder) Build() *QueryUserAllowedRemedysUserTaskRemedyReqBody {
	req := &QueryUserAllowedRemedysUserTaskRemedyReqBody{}
	if builder.userIdFlag {
		req.UserId = &builder.userId
	}
	if builder.remedyDateFlag {
		req.RemedyDate = &builder.remedyDate
	}
	return req
}

type QueryUserAllowedRemedysUserTaskRemedyPathReqBodyBuilder struct {
	userId         string // 用户 ID
	userIdFlag     bool
	remedyDate     int // 补卡日期
	remedyDateFlag bool
}

func NewQueryUserAllowedRemedysUserTaskRemedyPathReqBodyBuilder() *QueryUserAllowedRemedysUserTaskRemedyPathReqBodyBuilder {
	builder := &QueryUserAllowedRemedysUserTaskRemedyPathReqBodyBuilder{}
	return builder
}

// 用户 ID
//
// 示例值：abd754f7
func (builder *QueryUserAllowedRemedysUserTaskRemedyPathReqBodyBuilder) UserId(userId string) *QueryUserAllowedRemedysUserTaskRemedyPathReqBodyBuilder {
	builder.userId = userId
	builder.userIdFlag = true
	return builder
}

// 补卡日期
//
// 示例值：20210104
func (builder *QueryUserAllowedRemedysUserTaskRemedyPathReqBodyBuilder) RemedyDate(remedyDate int) *QueryUserAllowedRemedysUserTaskRemedyPathReqBodyBuilder {
	builder.remedyDate = remedyDate
	builder.remedyDateFlag = true
	return builder
}

func (builder *QueryUserAllowedRemedysUserTaskRemedyPathReqBodyBuilder) Build() (*QueryUserAllowedRemedysUserTaskRemedyReqBody, error) {
	req := &QueryUserAllowedRemedysUserTaskRemedyReqBody{}
	if builder.userIdFlag {
		req.UserId = &builder.userId
	}
	if builder.remedyDateFlag {
		req.RemedyDate = &builder.remedyDate
	}
	return req, nil
}

type QueryUserAllowedRemedysUserTaskRemedyReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *QueryUserAllowedRemedysUserTaskRemedyReqBody
}

func NewQueryUserAllowedRemedysUserTaskRemedyReqBuilder() *QueryUserAllowedRemedysUserTaskRemedyReqBuilder {
	builder := &QueryUserAllowedRemedysUserTaskRemedyReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 请求体和响应体中的 user_id 的员工工号类型
//
// 示例值：employee_id
func (builder *QueryUserAllowedRemedysUserTaskRemedyReqBuilder) EmployeeType(employeeType string) *QueryUserAllowedRemedysUserTaskRemedyReqBuilder {
	builder.apiReq.QueryParams.Set("employee_type", fmt.Sprint(employeeType))
	return builder
}

// 获取用户某天可以补的第几次上 / 下班卡的时间。
func (builder *QueryUserAllowedRemedysUserTaskRemedyReqBuilder) Body(body *QueryUserAllowedRemedysUserTaskRemedyReqBody) *QueryUserAllowedRemedysUserTaskRemedyReqBuilder {
	builder.body = body
	return builder
}

func (builder *QueryUserAllowedRemedysUserTaskRemedyReqBuilder) Build() *QueryUserAllowedRemedysUserTaskRemedyReq {
	req := &QueryUserAllowedRemedysUserTaskRemedyReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.body
	return req
}

type QueryUserAllowedRemedysUserTaskRemedyReqBody struct {
	UserId     *string `json:"user_id,omitempty"`     // 用户 ID
	RemedyDate *int    `json:"remedy_date,omitempty"` // 补卡日期
}

type QueryUserAllowedRemedysUserTaskRemedyReq struct {
	apiReq *larkcore.ApiReq
	Body   *QueryUserAllowedRemedysUserTaskRemedyReqBody `body:""`
}

type QueryUserAllowedRemedysUserTaskRemedyRespData struct {
	UserAllowedRemedys []*UserAllowedRemedy `json:"user_allowed_remedys,omitempty"` // 用户可补卡时间
}

type QueryUserAllowedRemedysUserTaskRemedyResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *QueryUserAllowedRemedysUserTaskRemedyRespData `json:"data"` // 业务数据
}

func (resp *QueryUserAllowedRemedysUserTaskRemedyResp) Success() bool {
	return resp.Code == 0
}

type ListGroupIterator struct {
	nextPageToken *string
	items         []*GroupMeta
	index         int
	limit         int
	ctx           context.Context
	req           *ListGroupReq
	listFunc      func(ctx context.Context, req *ListGroupReq, options ...larkcore.RequestOptionFunc) (*ListGroupResp, error)
	options       []larkcore.RequestOptionFunc
	curlNum       int
}

func (iterator *ListGroupIterator) Next() (bool, *GroupMeta, error) {
	// 达到最大量，则返回
	if iterator.limit > 0 && iterator.curlNum >= iterator.limit {
		return false, nil, nil
	}

	// 为0则拉取数据
	if iterator.index == 0 || iterator.index >= len(iterator.items) {
		if iterator.index != 0 && iterator.nextPageToken == nil {
			return false, nil, nil
		}
		if iterator.nextPageToken != nil {
			iterator.req.apiReq.QueryParams.Set("page_token", *iterator.nextPageToken)
		}
		resp, err := iterator.listFunc(iterator.ctx, iterator.req, iterator.options...)
		if err != nil {
			return false, nil, err
		}

		if resp.Code != 0 {
			return false, nil, errors.New(fmt.Sprintf("Code:%d,Msg:%s", resp.Code, resp.Msg))
		}

		if len(resp.Data.GroupList) == 0 {
			return false, nil, nil
		}

		iterator.nextPageToken = resp.Data.PageToken
		iterator.items = resp.Data.GroupList
		iterator.index = 0
	}

	block := iterator.items[iterator.index]
	iterator.index++
	iterator.curlNum++
	return true, block, nil
}

func (iterator *ListGroupIterator) NextPageToken() *string {
	return iterator.nextPageToken
}

type ListShiftIterator struct {
	nextPageToken *string
	items         []*Shift
	index         int
	limit         int
	ctx           context.Context
	req           *ListShiftReq
	listFunc      func(ctx context.Context, req *ListShiftReq, options ...larkcore.RequestOptionFunc) (*ListShiftResp, error)
	options       []larkcore.RequestOptionFunc
	curlNum       int
}

func (iterator *ListShiftIterator) Next() (bool, *Shift, error) {
	// 达到最大量，则返回
	if iterator.limit > 0 && iterator.curlNum >= iterator.limit {
		return false, nil, nil
	}

	// 为0则拉取数据
	if iterator.index == 0 || iterator.index >= len(iterator.items) {
		if iterator.index != 0 && iterator.nextPageToken == nil {
			return false, nil, nil
		}
		if iterator.nextPageToken != nil {
			iterator.req.apiReq.QueryParams.Set("page_token", *iterator.nextPageToken)
		}
		resp, err := iterator.listFunc(iterator.ctx, iterator.req, iterator.options...)
		if err != nil {
			return false, nil, err
		}

		if resp.Code != 0 {
			return false, nil, errors.New(fmt.Sprintf("Code:%d,Msg:%s", resp.Code, resp.Msg))
		}

		if len(resp.Data.ShiftList) == 0 {
			return false, nil, nil
		}

		iterator.nextPageToken = resp.Data.PageToken
		iterator.items = resp.Data.ShiftList
		iterator.index = 0
	}

	block := iterator.items[iterator.index]
	iterator.index++
	iterator.curlNum++
	return true, block, nil
}

func (iterator *ListShiftIterator) NextPageToken() *string {
	return iterator.nextPageToken
}
