// Package optical_char_recognition code generated by oapi sdk gen
/*
 * MIT License
 *
 * Copyright (c) 2022 Lark Technologies Pte. Ltd.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice, shall be included in all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */

package larkoptical_char_recognition

import (
	"github.com/larksuite/oapi-sdk-go/v3/core"
)

type Image struct {
}

type BasicRecognizeImageReqBodyBuilder struct {
	image     string // base64 后的图片数据
	imageFlag bool
}

func NewBasicRecognizeImageReqBodyBuilder() *BasicRecognizeImageReqBodyBuilder {
	builder := &BasicRecognizeImageReqBodyBuilder{}
	return builder
}

// base64 后的图片数据
//
//示例值：base64后的图片二进制数据
func (builder *BasicRecognizeImageReqBodyBuilder) Image(image string) *BasicRecognizeImageReqBodyBuilder {
	builder.image = image
	builder.imageFlag = true
	return builder
}

func (builder *BasicRecognizeImageReqBodyBuilder) Build() *BasicRecognizeImageReqBody {
	req := &BasicRecognizeImageReqBody{}
	if builder.imageFlag {
		req.Image = &builder.image
	}
	return req
}

type BasicRecognizeImagePathReqBodyBuilder struct {
	image     string // base64 后的图片数据
	imageFlag bool
}

func NewBasicRecognizeImagePathReqBodyBuilder() *BasicRecognizeImagePathReqBodyBuilder {
	builder := &BasicRecognizeImagePathReqBodyBuilder{}
	return builder
}

// base64 后的图片数据
//
// 示例值：base64后的图片二进制数据
func (builder *BasicRecognizeImagePathReqBodyBuilder) Image(image string) *BasicRecognizeImagePathReqBodyBuilder {
	builder.image = image
	builder.imageFlag = true
	return builder
}

func (builder *BasicRecognizeImagePathReqBodyBuilder) Build() (*BasicRecognizeImageReqBody, error) {
	req := &BasicRecognizeImageReqBody{}
	if builder.imageFlag {
		req.Image = &builder.image
	}
	return req, nil
}

type BasicRecognizeImageReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *BasicRecognizeImageReqBody
}

func NewBasicRecognizeImageReqBuilder() *BasicRecognizeImageReqBuilder {
	builder := &BasicRecognizeImageReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 可识别图片中的文字，按图片中的区域划分，分段返回文本列表
func (builder *BasicRecognizeImageReqBuilder) Body(body *BasicRecognizeImageReqBody) *BasicRecognizeImageReqBuilder {
	builder.body = body
	return builder
}

func (builder *BasicRecognizeImageReqBuilder) Build() *BasicRecognizeImageReq {
	req := &BasicRecognizeImageReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.Body = builder.body
	return req
}

type BasicRecognizeImageReqBody struct {
	Image *string `json:"image,omitempty"` // base64 后的图片数据
}

type BasicRecognizeImageReq struct {
	apiReq *larkcore.ApiReq
	Body   *BasicRecognizeImageReqBody `body:""`
}

type BasicRecognizeImageRespData struct {
	TextList []string `json:"text_list,omitempty"` // 按区域识别，返回文本列表
}

type BasicRecognizeImageResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *BasicRecognizeImageRespData `json:"data"` // 业务数据
}

func (resp *BasicRecognizeImageResp) Success() bool {
	return resp.Code == 0
}
