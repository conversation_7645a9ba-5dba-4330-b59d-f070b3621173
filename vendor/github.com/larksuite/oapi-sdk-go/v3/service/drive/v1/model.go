// Package drive code generated by oapi sdk gen
/*
 * MIT License
 *
 * Copyright (c) 2022 Lark Technologies Pte. Ltd.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice, shall be included in all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */

package larkdrive

import (
	"io"

	"bytes"

	"io/ioutil"

	"fmt"

	"context"
	"errors"

	"github.com/larksuite/oapi-sdk-go/v3/event"

	"github.com/larksuite/oapi-sdk-go/v3/core"
)

const (
	FileExtensionDocx = "docx" // word docx 格式
	FileExtensionPdf  = "pdf"  // pdf 格式
	FileExtensionXlsx = "xlsx" // excel xlsx 格式
	FileExtensionCsv  = "csv"  // csv 格式
)

const (
	TypeDoc     = "doc"     // 旧版飞书云文档类型
	TypeSheet   = "sheet"   // 飞书电子表格类型
	TypeBitable = "bitable" // 飞书多维表格类型
	TypeDocx    = "docx"    // 新版飞书云文档类型
)

const (
	TypeCopyFileFile     = "file"     // 文件类型
	TypeCopyFileDoc      = "doc"      // 云文档类型
	TypeCopyFileSheet    = "sheet"    // 电子表格类型
	TypeCopyFileBitable  = "bitable"  // 多维表格类型
	TypeCopyFileDocx     = "docx"     // 新版云文档类型
	TypeCopyFileMindnote = "mindnote" // 思维笔记类型
)

const (
	TypeDeleteFileFile     = "file"     // 文件类型
	TypeDeleteFileDocx     = "docx"     // docx文档类型
	TypeDeleteFileBitable  = "bitable"  // 多维表格类型
	TypeDeleteFileFolder   = "folder"   // 文件夹类型
	TypeDeleteFileDoc      = "doc"      // doc文档类型
	TypeDeleteFileSheet    = "sheet"    // 电子表格类型
	TypeDeleteFileMindnote = "mindnote" // 思维笔记类型
	TypeDeleteFileShortcut = "shortcut" // 快捷方式类型
)

const (
	TypeMoveFileFile     = "file"     // 普通文件类型
	TypeMoveFileDocx     = "docx"     // 新版文档类型
	TypeMoveFileBitable  = "bitable"  // 多维表格类型
	TypeMoveFileDoc      = "doc"      // doc文档类型
	TypeMoveFileSheet    = "sheet"    // 电子表格类型
	TypeMoveFileMindnote = "mindnote" // 思维笔记类型
	TypeMoveFileFolder   = "folder"   // 文件夹类型
)

const (
	FileTypeDoc     = "doc"     // 文档
	FileTypeDocx    = "docx"    // docx文档
	FileTypeSheet   = "sheet"   // 表格
	FileTypeBitable = "bitable" // 多维表格
)

const (
	ParentTypeExplorer = "explorer" // 云空间
)

const (
	ParentTypeUploadPrepareFileExplorer = "explorer" // 云空间
)

const (
	FileTypeCreateFileCommentDoc  = "doc"  // 文档
	FileTypeCreateFileCommentDocx = "docx" // 新版文档
)

const (
	UserIdTypeUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypeUnionId = "union_id" // 以union_id来识别用户
	UserIdTypeOpenId  = "open_id"  // 以open_id来识别用户
)

const (
	FileTypeGetFileCommentDoc   = "doc"   // 文档
	FileTypeGetFileCommentSheet = "sheet" // 表格
	FileTypeGetFileCommentFile  = "file"  // 文件
	FileTypeGetFileCommentDocx  = "docx"  // 新版文档
)

const (
	UserIdTypeGetFileCommentUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypeGetFileCommentUnionId = "union_id" // 以union_id来识别用户
	UserIdTypeGetFileCommentOpenId  = "open_id"  // 以open_id来识别用户
)

const (
	FileTypeListFileCommentDoc   = "doc"   // 文档
	FileTypeListFileCommentSheet = "sheet" // 表格
	FileTypeListFileCommentFile  = "file"  // 文件
	FileTypeListFileCommentDocx  = "docx"  // 新版文档
)

const (
	UserIdTypeListFileCommentUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypeListFileCommentUnionId = "union_id" // 以union_id来识别用户
	UserIdTypeListFileCommentOpenId  = "open_id"  // 以open_id来识别用户
)

const (
	FileTypePatchFileCommentDoc   = "doc"   // 文档
	FileTypePatchFileCommentSheet = "sheet" // 表格
	FileTypePatchFileCommentFile  = "file"  // 文件
	FileTypePatchFileCommentDocx  = "docx"  // 新版文档
)

const (
	FileTypeDeleteFileCommentReplyDoc   = "doc"   // 文档
	FileTypeDeleteFileCommentReplySheet = "sheet" // 表格
	FileTypeDeleteFileCommentReplyFile  = "file"  // 文件
	FileTypeDeleteFileCommentReplyDocx  = "docx"  // 新版文档
)

const (
	FileTypeUpdateFileCommentReplyDoc   = "doc"   // 文档
	FileTypeUpdateFileCommentReplySheet = "sheet" // 表格
	FileTypeUpdateFileCommentReplyFile  = "file"  // 文件
	FileTypeUpdateFileCommentReplyDocx  = "docx"  // 新版文档
)

const (
	UserIdTypeUpdateFileCommentReplyUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypeUpdateFileCommentReplyUnionId = "union_id" // 以union_id来识别用户
	UserIdTypeUpdateFileCommentReplyOpenId  = "open_id"  // 以open_id来识别用户
)

const (
	FileTypeGetFileStatisticsDoc      = "doc"      // doc文档
	FileTypeGetFileStatisticsSheet    = "sheet"    // 表格
	FileTypeGetFileStatisticsMindnote = "mindnote" // 思维笔记
	FileTypeGetFileStatisticsBitable  = "bitable"  // 多维表格
	FileTypeGetFileStatisticsWiki     = "wiki"     // 知识库wiki
	FileTypeGetFileStatisticsFile     = "file"     // 文件
	FileTypeGetFileStatisticsDocx     = "docx"     // docx文档
)

const (
	SubscriptionTypeCommentUpdate = "comment_update" // 评论更新
)

const (
	FileTypeCreateFileSubscriptionDoc  = "doc"  // 文档
	FileTypeCreateFileSubscriptionDocx = "docx" // 文档2.0
	FileTypeCreateFileSubscriptionWiki = "wiki" // 知识库wiki
)

const (
	SubscriptionTypeGetFileSubscriptionCommentUpdate = "comment_update" // 评论更新
)

const (
	FileTypeGetFileSubscriptionDoc  = "doc"  // 文档
	FileTypeGetFileSubscriptionDocx = "docx" // 文档2.0
	FileTypeGetFileSubscriptionWiki = "wiki" // 知识库wiki
)

const (
	FileTypePatchFileSubscriptionDoc  = "doc"  // 文档1.0
	FileTypePatchFileSubscriptionDocx = "docx" // 文档2.0
	FileTypePatchFileSubscriptionWiki = "wiki" // 知识库wiki
)

const (
	ParentTypeUploadAllMediaDocImage            = "doc_image"             // docs图片
	ParentTypeUploadAllMediaDocxImage           = "docx_image"            // docx图片
	ParentTypeUploadAllMediaSheetImage          = "sheet_image"           // sheet图片
	ParentTypeUploadAllMediaDocFile             = "doc_file"              // doc文件
	ParentTypeUploadAllMediaDocxFile            = "docx_file"             // docx文件
	ParentTypeUploadAllMediaSheetFile           = "sheet_file"            // sheet文件
	ParentTypeUploadAllMediaVcVirtualBackground = "vc_virtual_background" // vc虚拟背景
	ParentTypeUploadAllMediaBitableImage        = "bitable_image"         // 多维表格图片
	ParentTypeUploadAllMediaBitableFile         = "bitable_file"          // 多维表格文件
	ParentTypeUploadAllMediaMoments             = "moments"               // 同事圈
	ParentTypeUploadAllMediaCcmImportOpen       = "ccm_import_open"       // 云文档导入文件
)

const (
	ParentTypeUploadPrepareMediaDocImage            = "doc_image"             // docs图片
	ParentTypeUploadPrepareMediaSheetImage          = "sheet_image"           // sheet图片
	ParentTypeUploadPrepareMediaDocFile             = "doc_file"              // doc文件
	ParentTypeUploadPrepareMediaSheetFile           = "sheet_file"            // sheet文件
	ParentTypeUploadPrepareMediaVcVirtualBackground = "vc_virtual_background" // vc虚拟背景
	ParentTypeUploadPrepareMediaBitableImage        = "bitable_image"         // bitable图片
	ParentTypeUploadPrepareMediaBitableFile         = "bitable_file"          // bitable文件
	ParentTypeUploadPrepareMediaMoments             = "moments"               // 同事圈
	ParentTypeUploadPrepareMediaCcmImportOpen       = "ccm_import_open"       // 云文档导入文件
)

const (
	UserIdTypeBatchQueryMetaUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypeBatchQueryMetaUnionId = "union_id" // 以union_id来识别用户
	UserIdTypeBatchQueryMetaOpenId  = "open_id"  // 以open_id来识别用户
)

const (
	MemberTypeEmail            = "email"            // 飞书邮箱
	MemberTypeOpenId           = "openid"           // 开放平台ID
	MemberTypeOpenChat         = "openchat"         // 开放平台群组ID
	MemberTypeOpenDepartmentId = "opendepartmentid" // 开放平台部门ID
	MemberTypeUserId           = "userid"           // 用户自定义ID
)

const (
	PermView       = "view"        // 可阅读角色
	PermEdit       = "edit"        // 可编辑角色
	PermFullAccess = "full_access" // 可管理角色
)

const (
	TokenTypeV2Doc      = "doc"      // 文档
	TokenTypeV2Sheet    = "sheet"    // 电子表格
	TokenTypeV2File     = "file"     // 云空间文件
	TokenTypeV2Wiki     = "wiki"     // 知识库节点
	TokenTypeV2Bitable  = "bitable"  // 多维表格
	TokenTypeV2Docx     = "docx"     // 新版文档
	TokenTypeV2Folder   = "folder"   // 文件夹
	TokenTypeV2Mindnote = "mindnote" // 思维笔记
)

const (
	TokenTypeV2DeletePermissionMemberDoc      = "doc"      // 文档
	TokenTypeV2DeletePermissionMemberSheet    = "sheet"    // 电子表格
	TokenTypeV2DeletePermissionMemberFile     = "file"     // 云空间文件
	TokenTypeV2DeletePermissionMemberWiki     = "wiki"     // 知识库节点
	TokenTypeV2DeletePermissionMemberBitable  = "bitable"  // 多维表格
	TokenTypeV2DeletePermissionMemberDocx     = "docx"     // 文档
	TokenTypeV2DeletePermissionMemberFolder   = "folder"   // 文件夹
	TokenTypeV2DeletePermissionMemberMindnote = "mindnote" // 思维笔记
)

const (
	MemberTypeDeletePermissionMemberEmail            = "email"            // 邮箱地址
	MemberTypeDeletePermissionMemberOpenID           = "openid"           // 开放平台ID
	MemberTypeDeletePermissionMemberOpenChatID       = "openchat"         // 开放平台群ID
	MemberTypeDeletePermissionMemberOpenDepartmentID = "opendepartmentid" // 开放平台部门ID
	MemberTypeDeletePermissionMemberUserID           = "userid"           // 自定义用户ID
)

const (
	TokenTypeDoc      = "doc"      // 文档
	TokenTypeSheet    = "sheet"    // 电子表格
	TokenTypeFile     = "file"     // 云空间文件
	TokenTypeWiki     = "wiki"     // 知识库节点
	TokenTypeBitable  = "bitable"  // 多维表格
	TokenTypeDocx     = "docx"     // 新版文档
	TokenTypeMindnote = "mindnote" // 思维笔记
)

const (
	MemberTypeUpdatePermissionMemberEmail            = "email"            // 飞书邮箱
	MemberTypeUpdatePermissionMemberOpenId           = "openid"           // 开放平台ID
	MemberTypeUpdatePermissionMemberOpenChat         = "openchat"         // 开放平台群组ID
	MemberTypeUpdatePermissionMemberOpenDepartmentId = "opendepartmentid" // 开放平台部门ID
	MemberTypeUpdatePermissionMemberUserId           = "userid"           // 用户自定义ID
)

const (
	PermUpdatePermissionMemberView       = "view"        // 可阅读角色
	PermUpdatePermissionMemberEdit       = "edit"        // 可编辑角色
	PermUpdatePermissionMemberFullAccess = "full_access" // 可管理角色
)

const (
	TokenTypeUpdatePermissionMemberDoc      = "doc"      // 文档
	TokenTypeUpdatePermissionMemberSheet    = "sheet"    // 电子表格
	TokenTypeUpdatePermissionMemberFile     = "file"     // 云空间文件
	TokenTypeUpdatePermissionMemberWiki     = "wiki"     // 知识库节点
	TokenTypeUpdatePermissionMemberBitable  = "bitable"  // 多维表格
	TokenTypeUpdatePermissionMemberDocx     = "docx"     // 文档
	TokenTypeUpdatePermissionMemberMindnote = "mindnote" // 思维笔记
)

const (
	TokenTypeGetPermissionPublicDoc     = "doc"     // 文档
	TokenTypeGetPermissionPublicSheet   = "sheet"   // 电子表格
	TokenTypeGetPermissionPublicFile    = "file"    // 云空间文件
	TokenTypeGetPermissionPublicWiki    = "wiki"    // 知识库节点
	TokenTypeGetPermissionPublicBitable = "bitable" // 多维表格
	TokenTypeGetPermissionPublicDocx    = "docx"    // 文档
)

const (
	SecurityEntityAnyoneCanView  = "anyone_can_view"  // 拥有可阅读权限的用户
	SecurityEntityAnyoneCanEdit  = "anyone_can_edit"  // 拥有可编辑权限的用户
	SecurityEntityOnlyFullAccess = "only_full_access" // 拥有可管理权限（包括我）的用户
)

const (
	CommentEntityAnyoneCanView = "anyone_can_view" // 拥有可阅读权限的用户
	CommentEntityAnyoneCanEdit = "anyone_can_edit" // 拥有可编辑权限的用户
)

const (
	ShareEntityAnyone         = "anyone"           // 所有可阅读或编辑此文档的用户
	ShareEntitySameTenant     = "same_tenant"      // 组织内所有可阅读或编辑此文档的用户
	ShareEntityOnlyFullAccess = "only_full_access" // 拥有可管理权限（包括我）的用户
)

const (
	LinkShareEntityTenantReadable = "tenant_readable" // 组织内获得链接的人可阅读
	LinkShareEntityTenantEditable = "tenant_editable" // 组织内获得链接的人可编辑
	LinkShareEntityAnyoneReadable = "anyone_readable" // 互联网上获得链接的任何人可阅读（仅external_access=“true”时有效）
	LinkShareEntityAnyoneEditable = "anyone_editable" // 互联网上获得链接的任何人可编辑（仅external_access=“true”时有效）
	LinkShareEntityClosed         = "closed"          // 关闭链接分享
)

const (
	TokenTypePatchPermissionPublicDoc     = "doc"     // 文档
	TokenTypePatchPermissionPublicSheet   = "sheet"   // 电子表格
	TokenTypePatchPermissionPublicFile    = "file"    // 云空间文件
	TokenTypePatchPermissionPublicWiki    = "wiki"    // 知识库节点
	TokenTypePatchPermissionPublicBitable = "bitable" // 多维表格
	TokenTypePatchPermissionPublicDocx    = "docx"    // 文档
)

type ApplyMemberRequest struct {
	Perm   *string `json:"perm,omitempty"`   // 需要申请的权限，权限值："view"，"edit"
	Remark *string `json:"remark,omitempty"` // 申请权限备注
}

type ApplyMemberRequestBuilder struct {
	perm       string // 需要申请的权限，权限值："view"，"edit"
	permFlag   bool
	remark     string // 申请权限备注
	remarkFlag bool
}

func NewApplyMemberRequestBuilder() *ApplyMemberRequestBuilder {
	builder := &ApplyMemberRequestBuilder{}
	return builder
}

// 需要申请的权限，权限值："view"，"edit"
//
// 示例值：view
func (builder *ApplyMemberRequestBuilder) Perm(perm string) *ApplyMemberRequestBuilder {
	builder.perm = perm
	builder.permFlag = true
	return builder
}

// 申请权限备注
//
// 示例值：apply_remark
func (builder *ApplyMemberRequestBuilder) Remark(remark string) *ApplyMemberRequestBuilder {
	builder.remark = remark
	builder.remarkFlag = true
	return builder
}

func (builder *ApplyMemberRequestBuilder) Build() *ApplyMemberRequest {
	req := &ApplyMemberRequest{}
	if builder.permFlag {
		req.Perm = &builder.perm

	}
	if builder.remarkFlag {
		req.Remark = &builder.remark

	}
	return req
}

type BaseMember struct {
	MemberType *string `json:"member_type,omitempty"` // 协作者 ID 类型，与协作者 ID 需要对应
	MemberId   *string `json:"member_id,omitempty"`   // 协作者 ID，与协作者 ID 类型需要对应
	Perm       *string `json:"perm,omitempty"`        // 协作者对应的权限角色
}

type BaseMemberBuilder struct {
	memberType     string // 协作者 ID 类型，与协作者 ID 需要对应
	memberTypeFlag bool
	memberId       string // 协作者 ID，与协作者 ID 类型需要对应
	memberIdFlag   bool
	perm           string // 协作者对应的权限角色
	permFlag       bool
}

func NewBaseMemberBuilder() *BaseMemberBuilder {
	builder := &BaseMemberBuilder{}
	return builder
}

// 协作者 ID 类型，与协作者 ID 需要对应
//
// 示例值：openid
func (builder *BaseMemberBuilder) MemberType(memberType string) *BaseMemberBuilder {
	builder.memberType = memberType
	builder.memberTypeFlag = true
	return builder
}

// 协作者 ID，与协作者 ID 类型需要对应
//
// 示例值：string
func (builder *BaseMemberBuilder) MemberId(memberId string) *BaseMemberBuilder {
	builder.memberId = memberId
	builder.memberIdFlag = true
	return builder
}

// 协作者对应的权限角色
//
// 示例值：view
func (builder *BaseMemberBuilder) Perm(perm string) *BaseMemberBuilder {
	builder.perm = perm
	builder.permFlag = true
	return builder
}

func (builder *BaseMemberBuilder) Build() *BaseMember {
	req := &BaseMember{}
	if builder.memberTypeFlag {
		req.MemberType = &builder.memberType

	}
	if builder.memberIdFlag {
		req.MemberId = &builder.memberId

	}
	if builder.permFlag {
		req.Perm = &builder.perm

	}
	return req
}

type BitableTableFieldAction struct {
	Action      *string                       `json:"action,omitempty"`       // 操作类型
	FieldId     *string                       `json:"field_id,omitempty"`     // 字段 ID
	BeforeValue *BitableTableFieldActionValue `json:"before_value,omitempty"` // 操作前的字段值
	AfterValue  *BitableTableFieldActionValue `json:"after_value,omitempty"`  // 操作后的字段值
}

type BitableTableFieldActionBuilder struct {
	action          string // 操作类型
	actionFlag      bool
	fieldId         string // 字段 ID
	fieldIdFlag     bool
	beforeValue     *BitableTableFieldActionValue // 操作前的字段值
	beforeValueFlag bool
	afterValue      *BitableTableFieldActionValue // 操作后的字段值
	afterValueFlag  bool
}

func NewBitableTableFieldActionBuilder() *BitableTableFieldActionBuilder {
	builder := &BitableTableFieldActionBuilder{}
	return builder
}

// 操作类型
//
// 示例值：field_edited
func (builder *BitableTableFieldActionBuilder) Action(action string) *BitableTableFieldActionBuilder {
	builder.action = action
	builder.actionFlag = true
	return builder
}

// 字段 ID
//
// 示例值：fldmj5qNii
func (builder *BitableTableFieldActionBuilder) FieldId(fieldId string) *BitableTableFieldActionBuilder {
	builder.fieldId = fieldId
	builder.fieldIdFlag = true
	return builder
}

// 操作前的字段值
//
// 示例值：
func (builder *BitableTableFieldActionBuilder) BeforeValue(beforeValue *BitableTableFieldActionValue) *BitableTableFieldActionBuilder {
	builder.beforeValue = beforeValue
	builder.beforeValueFlag = true
	return builder
}

// 操作后的字段值
//
// 示例值：
func (builder *BitableTableFieldActionBuilder) AfterValue(afterValue *BitableTableFieldActionValue) *BitableTableFieldActionBuilder {
	builder.afterValue = afterValue
	builder.afterValueFlag = true
	return builder
}

func (builder *BitableTableFieldActionBuilder) Build() *BitableTableFieldAction {
	req := &BitableTableFieldAction{}
	if builder.actionFlag {
		req.Action = &builder.action

	}
	if builder.fieldIdFlag {
		req.FieldId = &builder.fieldId

	}
	if builder.beforeValueFlag {
		req.BeforeValue = builder.beforeValue
	}
	if builder.afterValueFlag {
		req.AfterValue = builder.afterValue
	}
	return req
}

type BitableTableFieldActionValue struct {
	Id          *string                               `json:"id,omitempty"`          // 字段 ID
	Name        *string                               `json:"name,omitempty"`        // 字段名字
	Type        *int                                  `json:"type,omitempty"`        // 字段类型
	Description *string                               `json:"description,omitempty"` // 字段描述
	Property    *BitableTableFieldActionValueProperty `json:"property,omitempty"`    // 字段属性
}

type BitableTableFieldActionValueBuilder struct {
	id              string // 字段 ID
	idFlag          bool
	name            string // 字段名字
	nameFlag        bool
	type_           int // 字段类型
	typeFlag        bool
	description     string // 字段描述
	descriptionFlag bool
	property        *BitableTableFieldActionValueProperty // 字段属性
	propertyFlag    bool
}

func NewBitableTableFieldActionValueBuilder() *BitableTableFieldActionValueBuilder {
	builder := &BitableTableFieldActionValueBuilder{}
	return builder
}

// 字段 ID
//
// 示例值：fldmj5qNii
func (builder *BitableTableFieldActionValueBuilder) Id(id string) *BitableTableFieldActionValueBuilder {
	builder.id = id
	builder.idFlag = true
	return builder
}

// 字段名字
//
// 示例值：field name
func (builder *BitableTableFieldActionValueBuilder) Name(name string) *BitableTableFieldActionValueBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

// 字段类型
//
// 示例值：20
func (builder *BitableTableFieldActionValueBuilder) Type(type_ int) *BitableTableFieldActionValueBuilder {
	builder.type_ = type_
	builder.typeFlag = true
	return builder
}

// 字段描述
//
// 示例值：description
func (builder *BitableTableFieldActionValueBuilder) Description(description string) *BitableTableFieldActionValueBuilder {
	builder.description = description
	builder.descriptionFlag = true
	return builder
}

// 字段属性
//
// 示例值：
func (builder *BitableTableFieldActionValueBuilder) Property(property *BitableTableFieldActionValueProperty) *BitableTableFieldActionValueBuilder {
	builder.property = property
	builder.propertyFlag = true
	return builder
}

func (builder *BitableTableFieldActionValueBuilder) Build() *BitableTableFieldActionValue {
	req := &BitableTableFieldActionValue{}
	if builder.idFlag {
		req.Id = &builder.id

	}
	if builder.nameFlag {
		req.Name = &builder.name

	}
	if builder.typeFlag {
		req.Type = &builder.type_

	}
	if builder.descriptionFlag {
		req.Description = &builder.description

	}
	if builder.propertyFlag {
		req.Property = builder.property
	}
	return req
}

type BitableTableFieldActionValueProperty struct {
	Formatter         *string                                         `json:"formatter,omitempty"`          // 数字、公式字段的显示格式
	DateFormatter     *string                                         `json:"date_formatter,omitempty"`     // 日期、创建时间、最后更新时间字段的显示格式
	AutoFill          *bool                                           `json:"auto_fill,omitempty"`          // 日期字段中新纪录自动填写创建时间
	Multiple          *bool                                           `json:"multiple,omitempty"`           // 人员字段中允许添加多个成员，单向关联、双向关联中允许添加多个记录
	TableId           *string                                         `json:"table_id,omitempty"`           // 单向关联、双向关联字段中关联的数据表的ID
	TableName         *string                                         `json:"table_name,omitempty"`         // 单向关联、双向关联字段中关联的数据表的名字
	BackFieldName     *string                                         `json:"back_field_name,omitempty"`    // 双向关联字段中关联的数据表中对应的双向关联字段的名字
	InputType         *string                                         `json:"input_type,omitempty"`         // 地理位置输入限制
	BackFieldId       *string                                         `json:"back_field_id,omitempty"`      // 双向关联字段中关联的数据表中对应的双向关联字段的id
	AutoSerial        *BitableTableFieldActionValuePropertyAutoSerial `json:"auto_serial,omitempty"`        // 自动编号类型
	Options           []*BitableTableFieldActionValuePropertyOption   `json:"options,omitempty"`            // 单选、多选字段的选项信息
	FormulaExpression *string                                         `json:"formula_expression,omitempty"` // 公式字段的公式表达式
}

type BitableTableFieldActionValuePropertyBuilder struct {
	formatter             string // 数字、公式字段的显示格式
	formatterFlag         bool
	dateFormatter         string // 日期、创建时间、最后更新时间字段的显示格式
	dateFormatterFlag     bool
	autoFill              bool // 日期字段中新纪录自动填写创建时间
	autoFillFlag          bool
	multiple              bool // 人员字段中允许添加多个成员，单向关联、双向关联中允许添加多个记录
	multipleFlag          bool
	tableId               string // 单向关联、双向关联字段中关联的数据表的ID
	tableIdFlag           bool
	tableName             string // 单向关联、双向关联字段中关联的数据表的名字
	tableNameFlag         bool
	backFieldName         string // 双向关联字段中关联的数据表中对应的双向关联字段的名字
	backFieldNameFlag     bool
	inputType             string // 地理位置输入限制
	inputTypeFlag         bool
	backFieldId           string // 双向关联字段中关联的数据表中对应的双向关联字段的id
	backFieldIdFlag       bool
	autoSerial            *BitableTableFieldActionValuePropertyAutoSerial // 自动编号类型
	autoSerialFlag        bool
	options               []*BitableTableFieldActionValuePropertyOption // 单选、多选字段的选项信息
	optionsFlag           bool
	formulaExpression     string // 公式字段的公式表达式
	formulaExpressionFlag bool
}

func NewBitableTableFieldActionValuePropertyBuilder() *BitableTableFieldActionValuePropertyBuilder {
	builder := &BitableTableFieldActionValuePropertyBuilder{}
	return builder
}

// 数字、公式字段的显示格式
//
// 示例值：1,000
func (builder *BitableTableFieldActionValuePropertyBuilder) Formatter(formatter string) *BitableTableFieldActionValuePropertyBuilder {
	builder.formatter = formatter
	builder.formatterFlag = true
	return builder
}

// 日期、创建时间、最后更新时间字段的显示格式
//
// 示例值：yyyyMMdd
func (builder *BitableTableFieldActionValuePropertyBuilder) DateFormatter(dateFormatter string) *BitableTableFieldActionValuePropertyBuilder {
	builder.dateFormatter = dateFormatter
	builder.dateFormatterFlag = true
	return builder
}

// 日期字段中新纪录自动填写创建时间
//
// 示例值：true
func (builder *BitableTableFieldActionValuePropertyBuilder) AutoFill(autoFill bool) *BitableTableFieldActionValuePropertyBuilder {
	builder.autoFill = autoFill
	builder.autoFillFlag = true
	return builder
}

// 人员字段中允许添加多个成员，单向关联、双向关联中允许添加多个记录
//
// 示例值：true
func (builder *BitableTableFieldActionValuePropertyBuilder) Multiple(multiple bool) *BitableTableFieldActionValuePropertyBuilder {
	builder.multiple = multiple
	builder.multipleFlag = true
	return builder
}

// 单向关联、双向关联字段中关联的数据表的ID
//
// 示例值：tblIniLz0Ic8oXyN
func (builder *BitableTableFieldActionValuePropertyBuilder) TableId(tableId string) *BitableTableFieldActionValuePropertyBuilder {
	builder.tableId = tableId
	builder.tableIdFlag = true
	return builder
}

// 单向关联、双向关联字段中关联的数据表的名字
//
// 示例值：table name
func (builder *BitableTableFieldActionValuePropertyBuilder) TableName(tableName string) *BitableTableFieldActionValuePropertyBuilder {
	builder.tableName = tableName
	builder.tableNameFlag = true
	return builder
}

// 双向关联字段中关联的数据表中对应的双向关联字段的名字
//
// 示例值：field name
func (builder *BitableTableFieldActionValuePropertyBuilder) BackFieldName(backFieldName string) *BitableTableFieldActionValuePropertyBuilder {
	builder.backFieldName = backFieldName
	builder.backFieldNameFlag = true
	return builder
}

// 地理位置输入限制
//
// 示例值：only_mobile
func (builder *BitableTableFieldActionValuePropertyBuilder) InputType(inputType string) *BitableTableFieldActionValuePropertyBuilder {
	builder.inputType = inputType
	builder.inputTypeFlag = true
	return builder
}

// 双向关联字段中关联的数据表中对应的双向关联字段的id
//
// 示例值：fldmj5qNii
func (builder *BitableTableFieldActionValuePropertyBuilder) BackFieldId(backFieldId string) *BitableTableFieldActionValuePropertyBuilder {
	builder.backFieldId = backFieldId
	builder.backFieldIdFlag = true
	return builder
}

// 自动编号类型
//
// 示例值：
func (builder *BitableTableFieldActionValuePropertyBuilder) AutoSerial(autoSerial *BitableTableFieldActionValuePropertyAutoSerial) *BitableTableFieldActionValuePropertyBuilder {
	builder.autoSerial = autoSerial
	builder.autoSerialFlag = true
	return builder
}

// 单选、多选字段的选项信息
//
// 示例值：
func (builder *BitableTableFieldActionValuePropertyBuilder) Options(options []*BitableTableFieldActionValuePropertyOption) *BitableTableFieldActionValuePropertyBuilder {
	builder.options = options
	builder.optionsFlag = true
	return builder
}

// 公式字段的公式表达式
//
// 示例值：bitable::$table[tblIniLz0Ic8oXyN].$field[fldqatAwxx]*6+333
func (builder *BitableTableFieldActionValuePropertyBuilder) FormulaExpression(formulaExpression string) *BitableTableFieldActionValuePropertyBuilder {
	builder.formulaExpression = formulaExpression
	builder.formulaExpressionFlag = true
	return builder
}

func (builder *BitableTableFieldActionValuePropertyBuilder) Build() *BitableTableFieldActionValueProperty {
	req := &BitableTableFieldActionValueProperty{}
	if builder.formatterFlag {
		req.Formatter = &builder.formatter

	}
	if builder.dateFormatterFlag {
		req.DateFormatter = &builder.dateFormatter

	}
	if builder.autoFillFlag {
		req.AutoFill = &builder.autoFill

	}
	if builder.multipleFlag {
		req.Multiple = &builder.multiple

	}
	if builder.tableIdFlag {
		req.TableId = &builder.tableId

	}
	if builder.tableNameFlag {
		req.TableName = &builder.tableName

	}
	if builder.backFieldNameFlag {
		req.BackFieldName = &builder.backFieldName

	}
	if builder.inputTypeFlag {
		req.InputType = &builder.inputType

	}
	if builder.backFieldIdFlag {
		req.BackFieldId = &builder.backFieldId

	}
	if builder.autoSerialFlag {
		req.AutoSerial = builder.autoSerial
	}
	if builder.optionsFlag {
		req.Options = builder.options
	}
	if builder.formulaExpressionFlag {
		req.FormulaExpression = &builder.formulaExpression

	}
	return req
}

type BitableTableFieldActionValuePropertyAutoSerial struct {
	Type    *string                                                  `json:"type,omitempty"`    // 自动编号类型
	Options []*BitableTableFieldActionValuePropertyAutoSerialOptions `json:"options,omitempty"` // 自动编号规则列表
}

type BitableTableFieldActionValuePropertyAutoSerialBuilder struct {
	type_       string // 自动编号类型
	typeFlag    bool
	options     []*BitableTableFieldActionValuePropertyAutoSerialOptions // 自动编号规则列表
	optionsFlag bool
}

func NewBitableTableFieldActionValuePropertyAutoSerialBuilder() *BitableTableFieldActionValuePropertyAutoSerialBuilder {
	builder := &BitableTableFieldActionValuePropertyAutoSerialBuilder{}
	return builder
}

// 自动编号类型
//
// 示例值：custom
func (builder *BitableTableFieldActionValuePropertyAutoSerialBuilder) Type(type_ string) *BitableTableFieldActionValuePropertyAutoSerialBuilder {
	builder.type_ = type_
	builder.typeFlag = true
	return builder
}

// 自动编号规则列表
//
// 示例值：
func (builder *BitableTableFieldActionValuePropertyAutoSerialBuilder) Options(options []*BitableTableFieldActionValuePropertyAutoSerialOptions) *BitableTableFieldActionValuePropertyAutoSerialBuilder {
	builder.options = options
	builder.optionsFlag = true
	return builder
}

func (builder *BitableTableFieldActionValuePropertyAutoSerialBuilder) Build() *BitableTableFieldActionValuePropertyAutoSerial {
	req := &BitableTableFieldActionValuePropertyAutoSerial{}
	if builder.typeFlag {
		req.Type = &builder.type_

	}
	if builder.optionsFlag {
		req.Options = builder.options
	}
	return req
}

type BitableTableFieldActionValuePropertyAutoSerialOptions struct {
	Type  *string `json:"type,omitempty"`  // 自动编号的可选规则项类型
	Value *string `json:"value,omitempty"` // 与类型相对应的取值
}

type BitableTableFieldActionValuePropertyAutoSerialOptionsBuilder struct {
	type_     string // 自动编号的可选规则项类型
	typeFlag  bool
	value     string // 与类型相对应的取值
	valueFlag bool
}

func NewBitableTableFieldActionValuePropertyAutoSerialOptionsBuilder() *BitableTableFieldActionValuePropertyAutoSerialOptionsBuilder {
	builder := &BitableTableFieldActionValuePropertyAutoSerialOptionsBuilder{}
	return builder
}

// 自动编号的可选规则项类型
//
// 示例值：created_time
func (builder *BitableTableFieldActionValuePropertyAutoSerialOptionsBuilder) Type(type_ string) *BitableTableFieldActionValuePropertyAutoSerialOptionsBuilder {
	builder.type_ = type_
	builder.typeFlag = true
	return builder
}

// 与类型相对应的取值
//
// 示例值：yyyyMMdd
func (builder *BitableTableFieldActionValuePropertyAutoSerialOptionsBuilder) Value(value string) *BitableTableFieldActionValuePropertyAutoSerialOptionsBuilder {
	builder.value = value
	builder.valueFlag = true
	return builder
}

func (builder *BitableTableFieldActionValuePropertyAutoSerialOptionsBuilder) Build() *BitableTableFieldActionValuePropertyAutoSerialOptions {
	req := &BitableTableFieldActionValuePropertyAutoSerialOptions{}
	if builder.typeFlag {
		req.Type = &builder.type_

	}
	if builder.valueFlag {
		req.Value = &builder.value

	}
	return req
}

type BitableTableFieldActionValuePropertyOption struct {
	Name  *string `json:"name,omitempty"`  // 选项名
	Id    *string `json:"id,omitempty"`    // 选项ID
	Color *int    `json:"color,omitempty"` // 选项颜色
}

type BitableTableFieldActionValuePropertyOptionBuilder struct {
	name      string // 选项名
	nameFlag  bool
	id        string // 选项ID
	idFlag    bool
	color     int // 选项颜色
	colorFlag bool
}

func NewBitableTableFieldActionValuePropertyOptionBuilder() *BitableTableFieldActionValuePropertyOptionBuilder {
	builder := &BitableTableFieldActionValuePropertyOptionBuilder{}
	return builder
}

// 选项名
//
// 示例值：option name
func (builder *BitableTableFieldActionValuePropertyOptionBuilder) Name(name string) *BitableTableFieldActionValuePropertyOptionBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

// 选项ID
//
// 示例值：optabcef
func (builder *BitableTableFieldActionValuePropertyOptionBuilder) Id(id string) *BitableTableFieldActionValuePropertyOptionBuilder {
	builder.id = id
	builder.idFlag = true
	return builder
}

// 选项颜色
//
// 示例值：3
func (builder *BitableTableFieldActionValuePropertyOptionBuilder) Color(color int) *BitableTableFieldActionValuePropertyOptionBuilder {
	builder.color = color
	builder.colorFlag = true
	return builder
}

func (builder *BitableTableFieldActionValuePropertyOptionBuilder) Build() *BitableTableFieldActionValuePropertyOption {
	req := &BitableTableFieldActionValuePropertyOption{}
	if builder.nameFlag {
		req.Name = &builder.name

	}
	if builder.idFlag {
		req.Id = &builder.id

	}
	if builder.colorFlag {
		req.Color = &builder.color

	}
	return req
}

type BitableTableRecordAction struct {
	RecordId    *string                          `json:"record_id,omitempty"`    // 记录 ID
	Action      *string                          `json:"action,omitempty"`       // 操作类型
	BeforeValue []*BitableTableRecordActionField `json:"before_value,omitempty"` // 操作前的记录值
	AfterValue  []*BitableTableRecordActionField `json:"after_value,omitempty"`  // 操作后的记录值
}

type BitableTableRecordActionBuilder struct {
	recordId        string // 记录 ID
	recordIdFlag    bool
	action          string // 操作类型
	actionFlag      bool
	beforeValue     []*BitableTableRecordActionField // 操作前的记录值
	beforeValueFlag bool
	afterValue      []*BitableTableRecordActionField // 操作后的记录值
	afterValueFlag  bool
}

func NewBitableTableRecordActionBuilder() *BitableTableRecordActionBuilder {
	builder := &BitableTableRecordActionBuilder{}
	return builder
}

// 记录 ID
//
// 示例值：
func (builder *BitableTableRecordActionBuilder) RecordId(recordId string) *BitableTableRecordActionBuilder {
	builder.recordId = recordId
	builder.recordIdFlag = true
	return builder
}

// 操作类型
//
// 示例值：
func (builder *BitableTableRecordActionBuilder) Action(action string) *BitableTableRecordActionBuilder {
	builder.action = action
	builder.actionFlag = true
	return builder
}

// 操作前的记录值
//
// 示例值：
func (builder *BitableTableRecordActionBuilder) BeforeValue(beforeValue []*BitableTableRecordActionField) *BitableTableRecordActionBuilder {
	builder.beforeValue = beforeValue
	builder.beforeValueFlag = true
	return builder
}

// 操作后的记录值
//
// 示例值：
func (builder *BitableTableRecordActionBuilder) AfterValue(afterValue []*BitableTableRecordActionField) *BitableTableRecordActionBuilder {
	builder.afterValue = afterValue
	builder.afterValueFlag = true
	return builder
}

func (builder *BitableTableRecordActionBuilder) Build() *BitableTableRecordAction {
	req := &BitableTableRecordAction{}
	if builder.recordIdFlag {
		req.RecordId = &builder.recordId

	}
	if builder.actionFlag {
		req.Action = &builder.action

	}
	if builder.beforeValueFlag {
		req.BeforeValue = builder.beforeValue
	}
	if builder.afterValueFlag {
		req.AfterValue = builder.afterValue
	}
	return req
}

type BitableTableRecordActionField struct {
	FieldId            *string                                `json:"field_id,omitempty"`             // 字段 ID
	FieldValue         *string                                `json:"field_value,omitempty"`          // 字段值
	FieldIdentityValue *BitableTableRecordActionFieldIdentity `json:"field_identity_value,omitempty"` // 人员字段补充信息
}

type BitableTableRecordActionFieldBuilder struct {
	fieldId                string // 字段 ID
	fieldIdFlag            bool
	fieldValue             string // 字段值
	fieldValueFlag         bool
	fieldIdentityValue     *BitableTableRecordActionFieldIdentity // 人员字段补充信息
	fieldIdentityValueFlag bool
}

func NewBitableTableRecordActionFieldBuilder() *BitableTableRecordActionFieldBuilder {
	builder := &BitableTableRecordActionFieldBuilder{}
	return builder
}

// 字段 ID
//
// 示例值：
func (builder *BitableTableRecordActionFieldBuilder) FieldId(fieldId string) *BitableTableRecordActionFieldBuilder {
	builder.fieldId = fieldId
	builder.fieldIdFlag = true
	return builder
}

// 字段值
//
// 示例值：
func (builder *BitableTableRecordActionFieldBuilder) FieldValue(fieldValue string) *BitableTableRecordActionFieldBuilder {
	builder.fieldValue = fieldValue
	builder.fieldValueFlag = true
	return builder
}

// 人员字段补充信息
//
// 示例值：
func (builder *BitableTableRecordActionFieldBuilder) FieldIdentityValue(fieldIdentityValue *BitableTableRecordActionFieldIdentity) *BitableTableRecordActionFieldBuilder {
	builder.fieldIdentityValue = fieldIdentityValue
	builder.fieldIdentityValueFlag = true
	return builder
}

func (builder *BitableTableRecordActionFieldBuilder) Build() *BitableTableRecordActionField {
	req := &BitableTableRecordActionField{}
	if builder.fieldIdFlag {
		req.FieldId = &builder.fieldId

	}
	if builder.fieldValueFlag {
		req.FieldValue = &builder.fieldValue

	}
	if builder.fieldIdentityValueFlag {
		req.FieldIdentityValue = builder.fieldIdentityValue
	}
	return req
}

type BitableTableRecordActionFieldIdentity struct {
	Users []*BitableTableRecordActionFieldIdentityUser `json:"users,omitempty"` // 用户信息列表
}

type BitableTableRecordActionFieldIdentityBuilder struct {
	users     []*BitableTableRecordActionFieldIdentityUser // 用户信息列表
	usersFlag bool
}

func NewBitableTableRecordActionFieldIdentityBuilder() *BitableTableRecordActionFieldIdentityBuilder {
	builder := &BitableTableRecordActionFieldIdentityBuilder{}
	return builder
}

// 用户信息列表
//
// 示例值：
func (builder *BitableTableRecordActionFieldIdentityBuilder) Users(users []*BitableTableRecordActionFieldIdentityUser) *BitableTableRecordActionFieldIdentityBuilder {
	builder.users = users
	builder.usersFlag = true
	return builder
}

func (builder *BitableTableRecordActionFieldIdentityBuilder) Build() *BitableTableRecordActionFieldIdentity {
	req := &BitableTableRecordActionFieldIdentity{}
	if builder.usersFlag {
		req.Users = builder.users
	}
	return req
}

type BitableTableRecordActionFieldIdentityUser struct {
	UserId    *UserId `json:"user_id,omitempty"`    // 用户ID
	Name      *string `json:"name,omitempty"`       // 用户名称
	EnName    *string `json:"en_name,omitempty"`    // 用户英文名称
	AvatarUrl *string `json:"avatar_url,omitempty"` // 用户头像URL
}

type BitableTableRecordActionFieldIdentityUserBuilder struct {
	userId        *UserId // 用户ID
	userIdFlag    bool
	name          string // 用户名称
	nameFlag      bool
	enName        string // 用户英文名称
	enNameFlag    bool
	avatarUrl     string // 用户头像URL
	avatarUrlFlag bool
}

func NewBitableTableRecordActionFieldIdentityUserBuilder() *BitableTableRecordActionFieldIdentityUserBuilder {
	builder := &BitableTableRecordActionFieldIdentityUserBuilder{}
	return builder
}

// 用户ID
//
// 示例值：
func (builder *BitableTableRecordActionFieldIdentityUserBuilder) UserId(userId *UserId) *BitableTableRecordActionFieldIdentityUserBuilder {
	builder.userId = userId
	builder.userIdFlag = true
	return builder
}

// 用户名称
//
// 示例值：
func (builder *BitableTableRecordActionFieldIdentityUserBuilder) Name(name string) *BitableTableRecordActionFieldIdentityUserBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

// 用户英文名称
//
// 示例值：
func (builder *BitableTableRecordActionFieldIdentityUserBuilder) EnName(enName string) *BitableTableRecordActionFieldIdentityUserBuilder {
	builder.enName = enName
	builder.enNameFlag = true
	return builder
}

// 用户头像URL
//
// 示例值：
func (builder *BitableTableRecordActionFieldIdentityUserBuilder) AvatarUrl(avatarUrl string) *BitableTableRecordActionFieldIdentityUserBuilder {
	builder.avatarUrl = avatarUrl
	builder.avatarUrlFlag = true
	return builder
}

func (builder *BitableTableRecordActionFieldIdentityUserBuilder) Build() *BitableTableRecordActionFieldIdentityUser {
	req := &BitableTableRecordActionFieldIdentityUser{}
	if builder.userIdFlag {
		req.UserId = builder.userId
	}
	if builder.nameFlag {
		req.Name = &builder.name

	}
	if builder.enNameFlag {
		req.EnName = &builder.enName

	}
	if builder.avatarUrlFlag {
		req.AvatarUrl = &builder.avatarUrl

	}
	return req
}

type Collaborator struct {
	MemberType   *string `json:"member_type,omitempty"`    // 协作者类型 "user" or "chat"
	MemberOpenId *string `json:"member_open_id,omitempty"` // 协作者openid
	MemberUserId *string `json:"member_user_id,omitempty"` // 协作者userid(仅当member_type="user"时有效)
	Perm         *string `json:"perm,omitempty"`           // 协作者权限 (注意: **有"edit"权限的协作者一定有"view"权限**)
}

type CollaboratorBuilder struct {
	memberType       string // 协作者类型 "user" or "chat"
	memberTypeFlag   bool
	memberOpenId     string // 协作者openid
	memberOpenIdFlag bool
	memberUserId     string // 协作者userid(仅当member_type="user"时有效)
	memberUserIdFlag bool
	perm             string // 协作者权限 (注意: **有"edit"权限的协作者一定有"view"权限**)
	permFlag         bool
}

func NewCollaboratorBuilder() *CollaboratorBuilder {
	builder := &CollaboratorBuilder{}
	return builder
}

// 协作者类型 "user" or "chat"
//
// 示例值：user
func (builder *CollaboratorBuilder) MemberType(memberType string) *CollaboratorBuilder {
	builder.memberType = memberType
	builder.memberTypeFlag = true
	return builder
}

// 协作者openid
//
// 示例值：ou_65b0affcc6c342a50e4c66f700137b64
func (builder *CollaboratorBuilder) MemberOpenId(memberOpenId string) *CollaboratorBuilder {
	builder.memberOpenId = memberOpenId
	builder.memberOpenIdFlag = true
	return builder
}

// 协作者userid(仅当member_type="user"时有效)
//
// 示例值：96g3c421
func (builder *CollaboratorBuilder) MemberUserId(memberUserId string) *CollaboratorBuilder {
	builder.memberUserId = memberUserId
	builder.memberUserIdFlag = true
	return builder
}

// 协作者权限 (注意: **有"edit"权限的协作者一定有"view"权限**)
//
// 示例值：view
func (builder *CollaboratorBuilder) Perm(perm string) *CollaboratorBuilder {
	builder.perm = perm
	builder.permFlag = true
	return builder
}

func (builder *CollaboratorBuilder) Build() *Collaborator {
	req := &Collaborator{}
	if builder.memberTypeFlag {
		req.MemberType = &builder.memberType

	}
	if builder.memberOpenIdFlag {
		req.MemberOpenId = &builder.memberOpenId

	}
	if builder.memberUserIdFlag {
		req.MemberUserId = &builder.memberUserId

	}
	if builder.permFlag {
		req.Perm = &builder.perm

	}
	return req
}

type DocsLink struct {
	Url *string `json:"url,omitempty"` // 回复 at云文档
}

type DocsLinkBuilder struct {
	url     string // 回复 at云文档
	urlFlag bool
}

func NewDocsLinkBuilder() *DocsLinkBuilder {
	builder := &DocsLinkBuilder{}
	return builder
}

// 回复 at云文档
//
// 示例值：https://bytedance.feishu.cn/docs/doccnHh7U87HOFpii5u5Gabcef
func (builder *DocsLinkBuilder) Url(url string) *DocsLinkBuilder {
	builder.url = url
	builder.urlFlag = true
	return builder
}

func (builder *DocsLinkBuilder) Build() *DocsLink {
	req := &DocsLink{}
	if builder.urlFlag {
		req.Url = &builder.url

	}
	return req
}

type ExportTask struct {
	FileExtension *string `json:"file_extension,omitempty"` // 导出文件扩展名
	Token         *string `json:"token,omitempty"`          // 导出文档 token [获取文档 token](https://open.feishu.cn/document/ukTMukTMukTM/uczNzUjL3czM14yN3MTN#08bb5df6)
	Type          *string `json:"type,omitempty"`           // 导出文档类型 [文档类型说明](/ssl::ttdoc/ukTMukTMukTM/uczNzUjL3czM14yN3MTN#560bf735)
	FileName      *string `json:"file_name,omitempty"`      // 导出文件名
	SubId         *string `json:"sub_id,omitempty"`         // 导出子表ID，仅当将电子表格/多维表格导出为 csv 时使用;;;[获取电子表格子表ID](https://open.feishu.cn/document/ukTMukTMukTM/uUDN04SN0QjL1QDN/sheets-v3/spreadsheet-sheet/query) 文档中的 sheet_id;;[获取多维表格子表ID](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/bitable-v1/app-table/list) 文档中的 table_id
	FileToken     *string `json:"file_token,omitempty"`     // 导出文件 drive token
	FileSize      *int    `json:"file_size,omitempty"`      // 导出文件大小，单位字节
	JobErrorMsg   *string `json:"job_error_msg,omitempty"`  // 任务失败原因
	JobStatus     *int    `json:"job_status,omitempty"`     // 任务状态
}

type ExportTaskBuilder struct {
	fileExtension     string // 导出文件扩展名
	fileExtensionFlag bool
	token             string // 导出文档 token [获取文档 token](https://open.feishu.cn/document/ukTMukTMukTM/uczNzUjL3czM14yN3MTN#08bb5df6)
	tokenFlag         bool
	type_             string // 导出文档类型 [文档类型说明](/ssl::ttdoc/ukTMukTMukTM/uczNzUjL3czM14yN3MTN#560bf735)
	typeFlag          bool
	fileName          string // 导出文件名
	fileNameFlag      bool
	subId             string // 导出子表ID，仅当将电子表格/多维表格导出为 csv 时使用;;;[获取电子表格子表ID](https://open.feishu.cn/document/ukTMukTMukTM/uUDN04SN0QjL1QDN/sheets-v3/spreadsheet-sheet/query) 文档中的 sheet_id;;[获取多维表格子表ID](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/bitable-v1/app-table/list) 文档中的 table_id
	subIdFlag         bool
	fileToken         string // 导出文件 drive token
	fileTokenFlag     bool
	fileSize          int // 导出文件大小，单位字节
	fileSizeFlag      bool
	jobErrorMsg       string // 任务失败原因
	jobErrorMsgFlag   bool
	jobStatus         int // 任务状态
	jobStatusFlag     bool
}

func NewExportTaskBuilder() *ExportTaskBuilder {
	builder := &ExportTaskBuilder{}
	return builder
}

// 导出文件扩展名
//
// 示例值：pdf
func (builder *ExportTaskBuilder) FileExtension(fileExtension string) *ExportTaskBuilder {
	builder.fileExtension = fileExtension
	builder.fileExtensionFlag = true
	return builder
}

// 导出文档 token [获取文档 token](https://open.feishu.cn/document/ukTMukTMukTM/uczNzUjL3czM14yN3MTN#08bb5df6)
//
// 示例值：doccnxe5OxxxxxxxSNdsJviENsk
func (builder *ExportTaskBuilder) Token(token string) *ExportTaskBuilder {
	builder.token = token
	builder.tokenFlag = true
	return builder
}

// 导出文档类型 [文档类型说明](/ssl::ttdoc/ukTMukTMukTM/uczNzUjL3czM14yN3MTN#560bf735)
//
// 示例值：doc
func (builder *ExportTaskBuilder) Type(type_ string) *ExportTaskBuilder {
	builder.type_ = type_
	builder.typeFlag = true
	return builder
}

// 导出文件名
//
// 示例值：docName
func (builder *ExportTaskBuilder) FileName(fileName string) *ExportTaskBuilder {
	builder.fileName = fileName
	builder.fileNameFlag = true
	return builder
}

// 导出子表ID，仅当将电子表格/多维表格导出为 csv 时使用;;;[获取电子表格子表ID](https://open.feishu.cn/document/ukTMukTMukTM/uUDN04SN0QjL1QDN/sheets-v3/spreadsheet-sheet/query) 文档中的 sheet_id;;[获取多维表格子表ID](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/bitable-v1/app-table/list) 文档中的 table_id
//
// 示例值：tblKz5D60T4JlfcT
func (builder *ExportTaskBuilder) SubId(subId string) *ExportTaskBuilder {
	builder.subId = subId
	builder.subIdFlag = true
	return builder
}

// 导出文件 drive token
//
// 示例值：boxcnxe5OxxxxxxxSNdsJviENsk
func (builder *ExportTaskBuilder) FileToken(fileToken string) *ExportTaskBuilder {
	builder.fileToken = fileToken
	builder.fileTokenFlag = true
	return builder
}

// 导出文件大小，单位字节
//
// 示例值：34356
func (builder *ExportTaskBuilder) FileSize(fileSize int) *ExportTaskBuilder {
	builder.fileSize = fileSize
	builder.fileSizeFlag = true
	return builder
}

// 任务失败原因
//
// 示例值：success
func (builder *ExportTaskBuilder) JobErrorMsg(jobErrorMsg string) *ExportTaskBuilder {
	builder.jobErrorMsg = jobErrorMsg
	builder.jobErrorMsgFlag = true
	return builder
}

// 任务状态
//
// 示例值：0
func (builder *ExportTaskBuilder) JobStatus(jobStatus int) *ExportTaskBuilder {
	builder.jobStatus = jobStatus
	builder.jobStatusFlag = true
	return builder
}

func (builder *ExportTaskBuilder) Build() *ExportTask {
	req := &ExportTask{}
	if builder.fileExtensionFlag {
		req.FileExtension = &builder.fileExtension

	}
	if builder.tokenFlag {
		req.Token = &builder.token

	}
	if builder.typeFlag {
		req.Type = &builder.type_

	}
	if builder.fileNameFlag {
		req.FileName = &builder.fileName

	}
	if builder.subIdFlag {
		req.SubId = &builder.subId

	}
	if builder.fileTokenFlag {
		req.FileToken = &builder.fileToken

	}
	if builder.fileSizeFlag {
		req.FileSize = &builder.fileSize

	}
	if builder.jobErrorMsgFlag {
		req.JobErrorMsg = &builder.jobErrorMsg

	}
	if builder.jobStatusFlag {
		req.JobStatus = &builder.jobStatus

	}
	return req
}

type File struct {
	Token        *string       `json:"token,omitempty"`         // 文件标识
	Name         *string       `json:"name,omitempty"`          // 文件名
	Type         *string       `json:"type,omitempty"`          // 文件类型
	ParentToken  *string       `json:"parent_token,omitempty"`  // 父文件夹标识
	Url          *string       `json:"url,omitempty"`           // 在浏览器中查看的链接
	ShortcutInfo *ShortcutInfo `json:"shortcut_info,omitempty"` // 快捷方式文件信息
}

type FileBuilder struct {
	token            string // 文件标识
	tokenFlag        bool
	name             string // 文件名
	nameFlag         bool
	type_            string // 文件类型
	typeFlag         bool
	parentToken      string // 父文件夹标识
	parentTokenFlag  bool
	url              string // 在浏览器中查看的链接
	urlFlag          bool
	shortcutInfo     *ShortcutInfo // 快捷方式文件信息
	shortcutInfoFlag bool
}

func NewFileBuilder() *FileBuilder {
	builder := &FileBuilder{}
	return builder
}

// 文件标识
//
// 示例值：
func (builder *FileBuilder) Token(token string) *FileBuilder {
	builder.token = token
	builder.tokenFlag = true
	return builder
}

// 文件名
//
// 示例值：
func (builder *FileBuilder) Name(name string) *FileBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

// 文件类型
//
// 示例值：
func (builder *FileBuilder) Type(type_ string) *FileBuilder {
	builder.type_ = type_
	builder.typeFlag = true
	return builder
}

// 父文件夹标识
//
// 示例值：
func (builder *FileBuilder) ParentToken(parentToken string) *FileBuilder {
	builder.parentToken = parentToken
	builder.parentTokenFlag = true
	return builder
}

// 在浏览器中查看的链接
//
// 示例值：
func (builder *FileBuilder) Url(url string) *FileBuilder {
	builder.url = url
	builder.urlFlag = true
	return builder
}

// 快捷方式文件信息
//
// 示例值：
func (builder *FileBuilder) ShortcutInfo(shortcutInfo *ShortcutInfo) *FileBuilder {
	builder.shortcutInfo = shortcutInfo
	builder.shortcutInfoFlag = true
	return builder
}

func (builder *FileBuilder) Build() *File {
	req := &File{}
	if builder.tokenFlag {
		req.Token = &builder.token

	}
	if builder.nameFlag {
		req.Name = &builder.name

	}
	if builder.typeFlag {
		req.Type = &builder.type_

	}
	if builder.parentTokenFlag {
		req.ParentToken = &builder.parentToken

	}
	if builder.urlFlag {
		req.Url = &builder.url

	}
	if builder.shortcutInfoFlag {
		req.ShortcutInfo = builder.shortcutInfo
	}
	return req
}

type FileComment struct {
	CommentId    *string    `json:"comment_id,omitempty"`     // 评论ID（创建新评论可不填；如填写，则视为回复已有评论）
	UserId       *string    `json:"user_id,omitempty"`        // 用户ID
	CreateTime   *int       `json:"create_time,omitempty"`    // 创建时间
	UpdateTime   *int       `json:"update_time,omitempty"`    // 更新时间
	IsSolved     *bool      `json:"is_solved,omitempty"`      // 是否已解决
	SolvedTime   *int       `json:"solved_time,omitempty"`    // 解决评论时间
	SolverUserId *string    `json:"solver_user_id,omitempty"` // 解决评论者的用户ID
	HasMore      *bool      `json:"has_more,omitempty"`       // 是否有更多回复
	PageToken    *string    `json:"page_token,omitempty"`     // 回复分页标记
	IsWhole      *bool      `json:"is_whole,omitempty"`       // 是否是全文评论
	Quote        *string    `json:"quote,omitempty"`          // 如果是局部评论，引用字段
	ReplyList    *ReplyList `json:"reply_list,omitempty"`     // 评论里的回复列表
}

type FileCommentBuilder struct {
	commentId        string // 评论ID（创建新评论可不填；如填写，则视为回复已有评论）
	commentIdFlag    bool
	userId           string // 用户ID
	userIdFlag       bool
	createTime       int // 创建时间
	createTimeFlag   bool
	updateTime       int // 更新时间
	updateTimeFlag   bool
	isSolved         bool // 是否已解决
	isSolvedFlag     bool
	solvedTime       int // 解决评论时间
	solvedTimeFlag   bool
	solverUserId     string // 解决评论者的用户ID
	solverUserIdFlag bool
	hasMore          bool // 是否有更多回复
	hasMoreFlag      bool
	pageToken        string // 回复分页标记
	pageTokenFlag    bool
	isWhole          bool // 是否是全文评论
	isWholeFlag      bool
	quote            string // 如果是局部评论，引用字段
	quoteFlag        bool
	replyList        *ReplyList // 评论里的回复列表
	replyListFlag    bool
}

func NewFileCommentBuilder() *FileCommentBuilder {
	builder := &FileCommentBuilder{}
	return builder
}

// 评论ID（创建新评论可不填；如填写，则视为回复已有评论）
//
// 示例值：6916106822734512356
func (builder *FileCommentBuilder) CommentId(commentId string) *FileCommentBuilder {
	builder.commentId = commentId
	builder.commentIdFlag = true
	return builder
}

// 用户ID
//
// 示例值：ou_cc19b2bfb93f8a44db4b4d6eababcef
func (builder *FileCommentBuilder) UserId(userId string) *FileCommentBuilder {
	builder.userId = userId
	builder.userIdFlag = true
	return builder
}

// 创建时间
//
// 示例值：1610281603
func (builder *FileCommentBuilder) CreateTime(createTime int) *FileCommentBuilder {
	builder.createTime = createTime
	builder.createTimeFlag = true
	return builder
}

// 更新时间
//
// 示例值：1610281603
func (builder *FileCommentBuilder) UpdateTime(updateTime int) *FileCommentBuilder {
	builder.updateTime = updateTime
	builder.updateTimeFlag = true
	return builder
}

// 是否已解决
//
// 示例值：false
func (builder *FileCommentBuilder) IsSolved(isSolved bool) *FileCommentBuilder {
	builder.isSolved = isSolved
	builder.isSolvedFlag = true
	return builder
}

// 解决评论时间
//
// 示例值：1610281603
func (builder *FileCommentBuilder) SolvedTime(solvedTime int) *FileCommentBuilder {
	builder.solvedTime = solvedTime
	builder.solvedTimeFlag = true
	return builder
}

// 解决评论者的用户ID
//
// 示例值：null
func (builder *FileCommentBuilder) SolverUserId(solverUserId string) *FileCommentBuilder {
	builder.solverUserId = solverUserId
	builder.solverUserIdFlag = true
	return builder
}

// 是否有更多回复
//
// 示例值：false
func (builder *FileCommentBuilder) HasMore(hasMore bool) *FileCommentBuilder {
	builder.hasMore = hasMore
	builder.hasMoreFlag = true
	return builder
}

// 回复分页标记
//
// 示例值：6916106822734512356
func (builder *FileCommentBuilder) PageToken(pageToken string) *FileCommentBuilder {
	builder.pageToken = pageToken
	builder.pageTokenFlag = true
	return builder
}

// 是否是全文评论
//
// 示例值：true
func (builder *FileCommentBuilder) IsWhole(isWhole bool) *FileCommentBuilder {
	builder.isWhole = isWhole
	builder.isWholeFlag = true
	return builder
}

// 如果是局部评论，引用字段
//
// 示例值：划词评论引用内容
func (builder *FileCommentBuilder) Quote(quote string) *FileCommentBuilder {
	builder.quote = quote
	builder.quoteFlag = true
	return builder
}

// 评论里的回复列表
//
// 示例值：
func (builder *FileCommentBuilder) ReplyList(replyList *ReplyList) *FileCommentBuilder {
	builder.replyList = replyList
	builder.replyListFlag = true
	return builder
}

func (builder *FileCommentBuilder) Build() *FileComment {
	req := &FileComment{}
	if builder.commentIdFlag {
		req.CommentId = &builder.commentId

	}
	if builder.userIdFlag {
		req.UserId = &builder.userId

	}
	if builder.createTimeFlag {
		req.CreateTime = &builder.createTime

	}
	if builder.updateTimeFlag {
		req.UpdateTime = &builder.updateTime

	}
	if builder.isSolvedFlag {
		req.IsSolved = &builder.isSolved

	}
	if builder.solvedTimeFlag {
		req.SolvedTime = &builder.solvedTime

	}
	if builder.solverUserIdFlag {
		req.SolverUserId = &builder.solverUserId

	}
	if builder.hasMoreFlag {
		req.HasMore = &builder.hasMore

	}
	if builder.pageTokenFlag {
		req.PageToken = &builder.pageToken

	}
	if builder.isWholeFlag {
		req.IsWhole = &builder.isWhole

	}
	if builder.quoteFlag {
		req.Quote = &builder.quote

	}
	if builder.replyListFlag {
		req.ReplyList = builder.replyList
	}
	return req
}

type FileCommentReply struct {
	ReplyId    *string       `json:"reply_id,omitempty"`    // 回复ID
	UserId     *string       `json:"user_id,omitempty"`     // 用户ID
	CreateTime *int          `json:"create_time,omitempty"` // 创建时间
	UpdateTime *int          `json:"update_time,omitempty"` // 更新时间
	Content    *ReplyContent `json:"content,omitempty"`     // 回复内容
	Extra      *ReplyExtra   `json:"extra,omitempty"`       // 回复的其他内容，图片token等
}

type FileCommentReplyBuilder struct {
	replyId        string // 回复ID
	replyIdFlag    bool
	userId         string // 用户ID
	userIdFlag     bool
	createTime     int // 创建时间
	createTimeFlag bool
	updateTime     int // 更新时间
	updateTimeFlag bool
	content        *ReplyContent // 回复内容
	contentFlag    bool
	extra          *ReplyExtra // 回复的其他内容，图片token等
	extraFlag      bool
}

func NewFileCommentReplyBuilder() *FileCommentReplyBuilder {
	builder := &FileCommentReplyBuilder{}
	return builder
}

// 回复ID
//
// 示例值：6916106822734512356
func (builder *FileCommentReplyBuilder) ReplyId(replyId string) *FileCommentReplyBuilder {
	builder.replyId = replyId
	builder.replyIdFlag = true
	return builder
}

// 用户ID
//
// 示例值：ou_cc19b2bfb93f8a44db4b4d6eab2abcef
func (builder *FileCommentReplyBuilder) UserId(userId string) *FileCommentReplyBuilder {
	builder.userId = userId
	builder.userIdFlag = true
	return builder
}

// 创建时间
//
// 示例值：1610281603
func (builder *FileCommentReplyBuilder) CreateTime(createTime int) *FileCommentReplyBuilder {
	builder.createTime = createTime
	builder.createTimeFlag = true
	return builder
}

// 更新时间
//
// 示例值：1610281603
func (builder *FileCommentReplyBuilder) UpdateTime(updateTime int) *FileCommentReplyBuilder {
	builder.updateTime = updateTime
	builder.updateTimeFlag = true
	return builder
}

// 回复内容
//
// 示例值：
func (builder *FileCommentReplyBuilder) Content(content *ReplyContent) *FileCommentReplyBuilder {
	builder.content = content
	builder.contentFlag = true
	return builder
}

// 回复的其他内容，图片token等
//
// 示例值：
func (builder *FileCommentReplyBuilder) Extra(extra *ReplyExtra) *FileCommentReplyBuilder {
	builder.extra = extra
	builder.extraFlag = true
	return builder
}

func (builder *FileCommentReplyBuilder) Build() *FileCommentReply {
	req := &FileCommentReply{}
	if builder.replyIdFlag {
		req.ReplyId = &builder.replyId

	}
	if builder.userIdFlag {
		req.UserId = &builder.userId

	}
	if builder.createTimeFlag {
		req.CreateTime = &builder.createTime

	}
	if builder.updateTimeFlag {
		req.UpdateTime = &builder.updateTime

	}
	if builder.contentFlag {
		req.Content = builder.content
	}
	if builder.extraFlag {
		req.Extra = builder.extra
	}
	return req
}

type FileSubscription struct {
	SubscriptionId   *string `json:"subscription_id,omitempty"`   // 订阅关系ID
	SubscriptionType *string `json:"subscription_type,omitempty"` // 订阅类型
	IsSubcribe       *bool   `json:"is_subcribe,omitempty"`       // 是否订阅
	FileType         *string `json:"file_type,omitempty"`         // 文档类型
}

type FileSubscriptionBuilder struct {
	subscriptionId       string // 订阅关系ID
	subscriptionIdFlag   bool
	subscriptionType     string // 订阅类型
	subscriptionTypeFlag bool
	isSubcribe           bool // 是否订阅
	isSubcribeFlag       bool
	fileType             string // 文档类型
	fileTypeFlag         bool
}

func NewFileSubscriptionBuilder() *FileSubscriptionBuilder {
	builder := &FileSubscriptionBuilder{}
	return builder
}

// 订阅关系ID
//
// 示例值：1234567890987654321
func (builder *FileSubscriptionBuilder) SubscriptionId(subscriptionId string) *FileSubscriptionBuilder {
	builder.subscriptionId = subscriptionId
	builder.subscriptionIdFlag = true
	return builder
}

// 订阅类型
//
// 示例值：comment_update
func (builder *FileSubscriptionBuilder) SubscriptionType(subscriptionType string) *FileSubscriptionBuilder {
	builder.subscriptionType = subscriptionType
	builder.subscriptionTypeFlag = true
	return builder
}

// 是否订阅
//
// 示例值：true
func (builder *FileSubscriptionBuilder) IsSubcribe(isSubcribe bool) *FileSubscriptionBuilder {
	builder.isSubcribe = isSubcribe
	builder.isSubcribeFlag = true
	return builder
}

// 文档类型
//
// 示例值：doc
func (builder *FileSubscriptionBuilder) FileType(fileType string) *FileSubscriptionBuilder {
	builder.fileType = fileType
	builder.fileTypeFlag = true
	return builder
}

func (builder *FileSubscriptionBuilder) Build() *FileSubscription {
	req := &FileSubscription{}
	if builder.subscriptionIdFlag {
		req.SubscriptionId = &builder.subscriptionId

	}
	if builder.subscriptionTypeFlag {
		req.SubscriptionType = &builder.subscriptionType

	}
	if builder.isSubcribeFlag {
		req.IsSubcribe = &builder.isSubcribe

	}
	if builder.fileTypeFlag {
		req.FileType = &builder.fileType

	}
	return req
}

type FileSearch struct {
	DocsToken *string `json:"docs_token,omitempty"` // 文档token
	DocsType  *string `json:"docs_type,omitempty"`  // 文档类型
	Title     *string `json:"title,omitempty"`      // 标题
	OwnerId   *string `json:"owner_id,omitempty"`   // 文件所有者
}

type FileSearchBuilder struct {
	docsToken     string // 文档token
	docsTokenFlag bool
	docsType      string // 文档类型
	docsTypeFlag  bool
	title         string // 标题
	titleFlag     bool
	ownerId       string // 文件所有者
	ownerIdFlag   bool
}

func NewFileSearchBuilder() *FileSearchBuilder {
	builder := &FileSearchBuilder{}
	return builder
}

// 文档token
//
// 示例值：doxbcxcAgzUTcPI5xR7c6nGJDGc
func (builder *FileSearchBuilder) DocsToken(docsToken string) *FileSearchBuilder {
	builder.docsToken = docsToken
	builder.docsTokenFlag = true
	return builder
}

// 文档类型
//
// 示例值：sheet
func (builder *FileSearchBuilder) DocsType(docsType string) *FileSearchBuilder {
	builder.docsType = docsType
	builder.docsTypeFlag = true
	return builder
}

// 标题
//
// 示例值：标题
func (builder *FileSearchBuilder) Title(title string) *FileSearchBuilder {
	builder.title = title
	builder.titleFlag = true
	return builder
}

// 文件所有者
//
// 示例值：ou_6e92f20bb8842c89c0f7e4090b13d57a
func (builder *FileSearchBuilder) OwnerId(ownerId string) *FileSearchBuilder {
	builder.ownerId = ownerId
	builder.ownerIdFlag = true
	return builder
}

func (builder *FileSearchBuilder) Build() *FileSearch {
	req := &FileSearch{}
	if builder.docsTokenFlag {
		req.DocsToken = &builder.docsToken

	}
	if builder.docsTypeFlag {
		req.DocsType = &builder.docsType

	}
	if builder.titleFlag {
		req.Title = &builder.title

	}
	if builder.ownerIdFlag {
		req.OwnerId = &builder.ownerId

	}
	return req
}

type FileStatistics struct {
	Uv        *int `json:"uv,omitempty"`         // 文件历史访问人数，同一用户（user_id）多次访问按一次计算。
	Pv        *int `json:"pv,omitempty"`         // 文件历史访问次数，同一用户（user_id）多次访问按多次计算。（注：同一用户相邻两次访问间隔在半小时内视为一次访问）
	LikeCount *int `json:"like_count,omitempty"` // 文件历史点赞总数，若对应的文档类型不支持点赞，返回 -1
	Timestamp *int `json:"timestamp,omitempty"`  // 时间戳（秒）
}

type FileStatisticsBuilder struct {
	uv            int // 文件历史访问人数，同一用户（user_id）多次访问按一次计算。
	uvFlag        bool
	pv            int // 文件历史访问次数，同一用户（user_id）多次访问按多次计算。（注：同一用户相邻两次访问间隔在半小时内视为一次访问）
	pvFlag        bool
	likeCount     int // 文件历史点赞总数，若对应的文档类型不支持点赞，返回 -1
	likeCountFlag bool
	timestamp     int // 时间戳（秒）
	timestampFlag bool
}

func NewFileStatisticsBuilder() *FileStatisticsBuilder {
	builder := &FileStatisticsBuilder{}
	return builder
}

// 文件历史访问人数，同一用户（user_id）多次访问按一次计算。
//
// 示例值：10
func (builder *FileStatisticsBuilder) Uv(uv int) *FileStatisticsBuilder {
	builder.uv = uv
	builder.uvFlag = true
	return builder
}

// 文件历史访问次数，同一用户（user_id）多次访问按多次计算。（注：同一用户相邻两次访问间隔在半小时内视为一次访问）
//
// 示例值：15
func (builder *FileStatisticsBuilder) Pv(pv int) *FileStatisticsBuilder {
	builder.pv = pv
	builder.pvFlag = true
	return builder
}

// 文件历史点赞总数，若对应的文档类型不支持点赞，返回 -1
//
// 示例值：2
func (builder *FileStatisticsBuilder) LikeCount(likeCount int) *FileStatisticsBuilder {
	builder.likeCount = likeCount
	builder.likeCountFlag = true
	return builder
}

// 时间戳（秒）
//
// 示例值：1627367349
func (builder *FileStatisticsBuilder) Timestamp(timestamp int) *FileStatisticsBuilder {
	builder.timestamp = timestamp
	builder.timestampFlag = true
	return builder
}

func (builder *FileStatisticsBuilder) Build() *FileStatistics {
	req := &FileStatistics{}
	if builder.uvFlag {
		req.Uv = &builder.uv

	}
	if builder.pvFlag {
		req.Pv = &builder.pv

	}
	if builder.likeCountFlag {
		req.LikeCount = &builder.likeCount

	}
	if builder.timestampFlag {
		req.Timestamp = &builder.timestamp

	}
	return req
}

type FileUploadInfo struct {
	FileName   *string `json:"file_name,omitempty"`   // 文件名
	ParentType *string `json:"parent_type,omitempty"` // 上传点类型
	ParentNode *string `json:"parent_node,omitempty"` // 文件夹的token
	Size       *int    `json:"size,omitempty"`        // 文件大小
}

type FileUploadInfoBuilder struct {
	fileName       string // 文件名
	fileNameFlag   bool
	parentType     string // 上传点类型
	parentTypeFlag bool
	parentNode     string // 文件夹的token
	parentNodeFlag bool
	size           int // 文件大小
	sizeFlag       bool
}

func NewFileUploadInfoBuilder() *FileUploadInfoBuilder {
	builder := &FileUploadInfoBuilder{}
	return builder
}

// 文件名
//
// 示例值：test.txt
func (builder *FileUploadInfoBuilder) FileName(fileName string) *FileUploadInfoBuilder {
	builder.fileName = fileName
	builder.fileNameFlag = true
	return builder
}

// 上传点类型
//
// 示例值：explorer
func (builder *FileUploadInfoBuilder) ParentType(parentType string) *FileUploadInfoBuilder {
	builder.parentType = parentType
	builder.parentTypeFlag = true
	return builder
}

// 文件夹的token
//
// 示例值：fldbcO1UuPz8VwnpPx5a92abcef
func (builder *FileUploadInfoBuilder) ParentNode(parentNode string) *FileUploadInfoBuilder {
	builder.parentNode = parentNode
	builder.parentNodeFlag = true
	return builder
}

// 文件大小
//
// 示例值：1024
func (builder *FileUploadInfoBuilder) Size(size int) *FileUploadInfoBuilder {
	builder.size = size
	builder.sizeFlag = true
	return builder
}

func (builder *FileUploadInfoBuilder) Build() *FileUploadInfo {
	req := &FileUploadInfo{}
	if builder.fileNameFlag {
		req.FileName = &builder.fileName

	}
	if builder.parentTypeFlag {
		req.ParentType = &builder.parentType

	}
	if builder.parentNodeFlag {
		req.ParentNode = &builder.parentNode

	}
	if builder.sizeFlag {
		req.Size = &builder.size

	}
	return req
}

type ImportTask struct {
	Ticket        *string               `json:"ticket,omitempty"`         // 任务ID
	FileExtension *string               `json:"file_extension,omitempty"` // 导入文件格式后缀
	FileToken     *string               `json:"file_token,omitempty"`     // 导入文件Drive FileToken
	Type          *string               `json:"type,omitempty"`           // 导入目标云文档格式
	FileName      *string               `json:"file_name,omitempty"`      // 导入目标云文档文件名 ，若为空使用Drive文件名
	Point         *ImportTaskMountPoint `json:"point,omitempty"`          // 挂载点
	JobStatus     *int                  `json:"job_status,omitempty"`     // 任务状态
	JobErrorMsg   *string               `json:"job_error_msg,omitempty"`  // 任务失败原因
	Token         *string               `json:"token,omitempty"`          // 导入云文档Token
	Url           *string               `json:"url,omitempty"`            // 导入云文档URL
	Extra         []string              `json:"extra,omitempty"`          // 任务成功后的提示信息
}

type ImportTaskBuilder struct {
	ticket            string // 任务ID
	ticketFlag        bool
	fileExtension     string // 导入文件格式后缀
	fileExtensionFlag bool
	fileToken         string // 导入文件Drive FileToken
	fileTokenFlag     bool
	type_             string // 导入目标云文档格式
	typeFlag          bool
	fileName          string // 导入目标云文档文件名 ，若为空使用Drive文件名
	fileNameFlag      bool
	point             *ImportTaskMountPoint // 挂载点
	pointFlag         bool
	jobStatus         int // 任务状态
	jobStatusFlag     bool
	jobErrorMsg       string // 任务失败原因
	jobErrorMsgFlag   bool
	token             string // 导入云文档Token
	tokenFlag         bool
	url               string // 导入云文档URL
	urlFlag           bool
	extra             []string // 任务成功后的提示信息
	extraFlag         bool
}

func NewImportTaskBuilder() *ImportTaskBuilder {
	builder := &ImportTaskBuilder{}
	return builder
}

// 任务ID
//
// 示例值：6990281865xxxxxxxx7843
func (builder *ImportTaskBuilder) Ticket(ticket string) *ImportTaskBuilder {
	builder.ticket = ticket
	builder.ticketFlag = true
	return builder
}

// 导入文件格式后缀
//
// 示例值：xlsx
func (builder *ImportTaskBuilder) FileExtension(fileExtension string) *ImportTaskBuilder {
	builder.fileExtension = fileExtension
	builder.fileExtensionFlag = true
	return builder
}

// 导入文件Drive FileToken
//
// 示例值：boxcnxe5OxxxxxxxSNdsJviENsk
func (builder *ImportTaskBuilder) FileToken(fileToken string) *ImportTaskBuilder {
	builder.fileToken = fileToken
	builder.fileTokenFlag = true
	return builder
}

// 导入目标云文档格式
//
// 示例值：sheet
func (builder *ImportTaskBuilder) Type(type_ string) *ImportTaskBuilder {
	builder.type_ = type_
	builder.typeFlag = true
	return builder
}

// 导入目标云文档文件名 ，若为空使用Drive文件名
//
// 示例值：test
func (builder *ImportTaskBuilder) FileName(fileName string) *ImportTaskBuilder {
	builder.fileName = fileName
	builder.fileNameFlag = true
	return builder
}

// 挂载点
//
// 示例值：
func (builder *ImportTaskBuilder) Point(point *ImportTaskMountPoint) *ImportTaskBuilder {
	builder.point = point
	builder.pointFlag = true
	return builder
}

// 任务状态
//
// 示例值：0
func (builder *ImportTaskBuilder) JobStatus(jobStatus int) *ImportTaskBuilder {
	builder.jobStatus = jobStatus
	builder.jobStatusFlag = true
	return builder
}

// 任务失败原因
//
// 示例值：success
func (builder *ImportTaskBuilder) JobErrorMsg(jobErrorMsg string) *ImportTaskBuilder {
	builder.jobErrorMsg = jobErrorMsg
	builder.jobErrorMsgFlag = true
	return builder
}

// 导入云文档Token
//
// 示例值：shtcnVBTG6SuxxxxxxxkM2tUX
func (builder *ImportTaskBuilder) Token(token string) *ImportTaskBuilder {
	builder.token = token
	builder.tokenFlag = true
	return builder
}

// 导入云文档URL
//
// 示例值：https://bytedance.feishu.cn/sheets/shtcnVBTG6SuxxxxxxxkM2tUX
func (builder *ImportTaskBuilder) Url(url string) *ImportTaskBuilder {
	builder.url = url
	builder.urlFlag = true
	return builder
}

// 任务成功后的提示信息
//
// 示例值：
func (builder *ImportTaskBuilder) Extra(extra []string) *ImportTaskBuilder {
	builder.extra = extra
	builder.extraFlag = true
	return builder
}

func (builder *ImportTaskBuilder) Build() *ImportTask {
	req := &ImportTask{}
	if builder.ticketFlag {
		req.Ticket = &builder.ticket

	}
	if builder.fileExtensionFlag {
		req.FileExtension = &builder.fileExtension

	}
	if builder.fileTokenFlag {
		req.FileToken = &builder.fileToken

	}
	if builder.typeFlag {
		req.Type = &builder.type_

	}
	if builder.fileNameFlag {
		req.FileName = &builder.fileName

	}
	if builder.pointFlag {
		req.Point = builder.point
	}
	if builder.jobStatusFlag {
		req.JobStatus = &builder.jobStatus

	}
	if builder.jobErrorMsgFlag {
		req.JobErrorMsg = &builder.jobErrorMsg

	}
	if builder.tokenFlag {
		req.Token = &builder.token

	}
	if builder.urlFlag {
		req.Url = &builder.url

	}
	if builder.extraFlag {
		req.Extra = builder.extra
	}
	return req
}

type ImportTaskMountPoint struct {
	MountType *int    `json:"mount_type,omitempty"` // 挂载类型
	MountKey  *string `json:"mount_key,omitempty"`  // 挂载位置,对于mount_type=1, 云空间目录token，空表示根目录
}

type ImportTaskMountPointBuilder struct {
	mountType     int // 挂载类型
	mountTypeFlag bool
	mountKey      string // 挂载位置,对于mount_type=1, 云空间目录token，空表示根目录
	mountKeyFlag  bool
}

func NewImportTaskMountPointBuilder() *ImportTaskMountPointBuilder {
	builder := &ImportTaskMountPointBuilder{}
	return builder
}

// 挂载类型
//
// 示例值：1
func (builder *ImportTaskMountPointBuilder) MountType(mountType int) *ImportTaskMountPointBuilder {
	builder.mountType = mountType
	builder.mountTypeFlag = true
	return builder
}

// 挂载位置,对于mount_type=1, 云空间目录token，空表示根目录
//
// 示例值：fldxxxxxxxx
func (builder *ImportTaskMountPointBuilder) MountKey(mountKey string) *ImportTaskMountPointBuilder {
	builder.mountKey = mountKey
	builder.mountKeyFlag = true
	return builder
}

func (builder *ImportTaskMountPointBuilder) Build() *ImportTaskMountPoint {
	req := &ImportTaskMountPoint{}
	if builder.mountTypeFlag {
		req.MountType = &builder.mountType

	}
	if builder.mountKeyFlag {
		req.MountKey = &builder.mountKey

	}
	return req
}

type Media struct {
	FileToken *string `json:"file_token,omitempty"` // 文件标识符
	FileName  *string `json:"file_name,omitempty"`  // 文件名
	Size      *int    `json:"size,omitempty"`       // 文件大小
	MimeType  *string `json:"mime_type,omitempty"`  // 文件MIME类型
}

type MediaBuilder struct {
	fileToken     string // 文件标识符
	fileTokenFlag bool
	fileName      string // 文件名
	fileNameFlag  bool
	size          int // 文件大小
	sizeFlag      bool
	mimeType      string // 文件MIME类型
	mimeTypeFlag  bool
}

func NewMediaBuilder() *MediaBuilder {
	builder := &MediaBuilder{}
	return builder
}

// 文件标识符
//
// 示例值：
func (builder *MediaBuilder) FileToken(fileToken string) *MediaBuilder {
	builder.fileToken = fileToken
	builder.fileTokenFlag = true
	return builder
}

// 文件名
//
// 示例值：
func (builder *MediaBuilder) FileName(fileName string) *MediaBuilder {
	builder.fileName = fileName
	builder.fileNameFlag = true
	return builder
}

// 文件大小
//
// 示例值：
func (builder *MediaBuilder) Size(size int) *MediaBuilder {
	builder.size = size
	builder.sizeFlag = true
	return builder
}

// 文件MIME类型
//
// 示例值：
func (builder *MediaBuilder) MimeType(mimeType string) *MediaBuilder {
	builder.mimeType = mimeType
	builder.mimeTypeFlag = true
	return builder
}

func (builder *MediaBuilder) Build() *Media {
	req := &Media{}
	if builder.fileTokenFlag {
		req.FileToken = &builder.fileToken

	}
	if builder.fileNameFlag {
		req.FileName = &builder.fileName

	}
	if builder.sizeFlag {
		req.Size = &builder.size

	}
	if builder.mimeTypeFlag {
		req.MimeType = &builder.mimeType

	}
	return req
}

type MediaUploadInfo struct {
	FileName   *string `json:"file_name,omitempty"`   // 文件名
	ParentType *string `json:"parent_type,omitempty"` // 上传点类型
	ParentNode *string `json:"parent_node,omitempty"` // 上传点的标识符
	Size       *int    `json:"size,omitempty"`        // 文件大小
	Extra      *string `json:"extra,omitempty"`       // 扩展信息(可选)
}

type MediaUploadInfoBuilder struct {
	fileName       string // 文件名
	fileNameFlag   bool
	parentType     string // 上传点类型
	parentTypeFlag bool
	parentNode     string // 上传点的标识符
	parentNodeFlag bool
	size           int // 文件大小
	sizeFlag       bool
	extra          string // 扩展信息(可选)
	extraFlag      bool
}

func NewMediaUploadInfoBuilder() *MediaUploadInfoBuilder {
	builder := &MediaUploadInfoBuilder{}
	return builder
}

// 文件名
//
// 示例值：demo.jpeg
func (builder *MediaUploadInfoBuilder) FileName(fileName string) *MediaUploadInfoBuilder {
	builder.fileName = fileName
	builder.fileNameFlag = true
	return builder
}

// 上传点类型
//
// 示例值：doc_image
func (builder *MediaUploadInfoBuilder) ParentType(parentType string) *MediaUploadInfoBuilder {
	builder.parentType = parentType
	builder.parentTypeFlag = true
	return builder
}

// 上传点的标识符
//
// 示例值：doccnFivLCfJfblZjGZtxgabcef
func (builder *MediaUploadInfoBuilder) ParentNode(parentNode string) *MediaUploadInfoBuilder {
	builder.parentNode = parentNode
	builder.parentNodeFlag = true
	return builder
}

// 文件大小
//
// 示例值：1024
func (builder *MediaUploadInfoBuilder) Size(size int) *MediaUploadInfoBuilder {
	builder.size = size
	builder.sizeFlag = true
	return builder
}

// 扩展信息(可选)
//
// 示例值：{\"test\":\"test\"}
func (builder *MediaUploadInfoBuilder) Extra(extra string) *MediaUploadInfoBuilder {
	builder.extra = extra
	builder.extraFlag = true
	return builder
}

func (builder *MediaUploadInfoBuilder) Build() *MediaUploadInfo {
	req := &MediaUploadInfo{}
	if builder.fileNameFlag {
		req.FileName = &builder.fileName

	}
	if builder.parentTypeFlag {
		req.ParentType = &builder.parentType

	}
	if builder.parentNodeFlag {
		req.ParentNode = &builder.parentNode

	}
	if builder.sizeFlag {
		req.Size = &builder.size

	}
	if builder.extraFlag {
		req.Extra = &builder.extra

	}
	return req
}

type Member struct {
	MemberType    *string `json:"member_type,omitempty"`    // 协作者 ID 类型，与协作者 ID 需要对应
	MemberId      *string `json:"member_id,omitempty"`      // 协作者 ID，与协作者 ID 类型需要对应
	Perm          *string `json:"perm,omitempty"`           // 协作者对应的权限角色
	Type          *string `json:"type,omitempty"`           // 协作者的类型
	Name          *string `json:"name,omitempty"`           // 协作者的名字
	Avatar        *string `json:"avatar,omitempty"`         // 协作者的头像
	ExternalLabel *bool   `json:"external_label,omitempty"` // 协作者的外部标签
}

type MemberBuilder struct {
	memberType        string // 协作者 ID 类型，与协作者 ID 需要对应
	memberTypeFlag    bool
	memberId          string // 协作者 ID，与协作者 ID 类型需要对应
	memberIdFlag      bool
	perm              string // 协作者对应的权限角色
	permFlag          bool
	type_             string // 协作者的类型
	typeFlag          bool
	name              string // 协作者的名字
	nameFlag          bool
	avatar            string // 协作者的头像
	avatarFlag        bool
	externalLabel     bool // 协作者的外部标签
	externalLabelFlag bool
}

func NewMemberBuilder() *MemberBuilder {
	builder := &MemberBuilder{}
	return builder
}

// 协作者 ID 类型，与协作者 ID 需要对应
//
// 示例值：openid
func (builder *MemberBuilder) MemberType(memberType string) *MemberBuilder {
	builder.memberType = memberType
	builder.memberTypeFlag = true
	return builder
}

// 协作者 ID，与协作者 ID 类型需要对应
//
// 示例值：ou_7dab8a3d3cdcc9da365777c7ad535d62
func (builder *MemberBuilder) MemberId(memberId string) *MemberBuilder {
	builder.memberId = memberId
	builder.memberIdFlag = true
	return builder
}

// 协作者对应的权限角色
//
// 示例值：view
func (builder *MemberBuilder) Perm(perm string) *MemberBuilder {
	builder.perm = perm
	builder.permFlag = true
	return builder
}

// 协作者的类型
//
// 示例值：user
func (builder *MemberBuilder) Type(type_ string) *MemberBuilder {
	builder.type_ = type_
	builder.typeFlag = true
	return builder
}

// 协作者的名字
//
// 示例值：zhangsan
func (builder *MemberBuilder) Name(name string) *MemberBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

// 协作者的头像
//
// 示例值：https://foo.icon.com/xxxx
func (builder *MemberBuilder) Avatar(avatar string) *MemberBuilder {
	builder.avatar = avatar
	builder.avatarFlag = true
	return builder
}

// 协作者的外部标签
//
// 示例值：true
func (builder *MemberBuilder) ExternalLabel(externalLabel bool) *MemberBuilder {
	builder.externalLabel = externalLabel
	builder.externalLabelFlag = true
	return builder
}

func (builder *MemberBuilder) Build() *Member {
	req := &Member{}
	if builder.memberTypeFlag {
		req.MemberType = &builder.memberType

	}
	if builder.memberIdFlag {
		req.MemberId = &builder.memberId

	}
	if builder.permFlag {
		req.Perm = &builder.perm

	}
	if builder.typeFlag {
		req.Type = &builder.type_

	}
	if builder.nameFlag {
		req.Name = &builder.name

	}
	if builder.avatarFlag {
		req.Avatar = &builder.avatar

	}
	if builder.externalLabelFlag {
		req.ExternalLabel = &builder.externalLabel

	}
	return req
}

type Meta struct {
	DocToken         *string `json:"doc_token,omitempty"`          // 文件token
	DocType          *string `json:"doc_type,omitempty"`           // 文件类型
	Title            *string `json:"title,omitempty"`              // 标题
	OwnerId          *string `json:"owner_id,omitempty"`           // 文件所有者
	CreateTime       *string `json:"create_time,omitempty"`        // 创建时间（Unix时间戳）
	LatestModifyUser *string `json:"latest_modify_user,omitempty"` // 最后编辑者
	LatestModifyTime *string `json:"latest_modify_time,omitempty"` // 最后编辑时间（Unix时间戳）
	Url              *string `json:"url,omitempty"`                // 文档链接
}

type MetaBuilder struct {
	docToken             string // 文件token
	docTokenFlag         bool
	docType              string // 文件类型
	docTypeFlag          bool
	title                string // 标题
	titleFlag            bool
	ownerId              string // 文件所有者
	ownerIdFlag          bool
	createTime           string // 创建时间（Unix时间戳）
	createTimeFlag       bool
	latestModifyUser     string // 最后编辑者
	latestModifyUserFlag bool
	latestModifyTime     string // 最后编辑时间（Unix时间戳）
	latestModifyTimeFlag bool
	url                  string // 文档链接
	urlFlag              bool
}

func NewMetaBuilder() *MetaBuilder {
	builder := &MetaBuilder{}
	return builder
}

// 文件token
//
// 示例值：doccnfYZzTlvXqZIGTdAHKabcef
func (builder *MetaBuilder) DocToken(docToken string) *MetaBuilder {
	builder.docToken = docToken
	builder.docTokenFlag = true
	return builder
}

// 文件类型
//
// 示例值：doc
func (builder *MetaBuilder) DocType(docType string) *MetaBuilder {
	builder.docType = docType
	builder.docTypeFlag = true
	return builder
}

// 标题
//
// 示例值：sampletitle
func (builder *MetaBuilder) Title(title string) *MetaBuilder {
	builder.title = title
	builder.titleFlag = true
	return builder
}

// 文件所有者
//
// 示例值：ou_b13d41c02edc52ce66aaae67bf1abcef
func (builder *MetaBuilder) OwnerId(ownerId string) *MetaBuilder {
	builder.ownerId = ownerId
	builder.ownerIdFlag = true
	return builder
}

// 创建时间（Unix时间戳）
//
// 示例值：1652066345
func (builder *MetaBuilder) CreateTime(createTime string) *MetaBuilder {
	builder.createTime = createTime
	builder.createTimeFlag = true
	return builder
}

// 最后编辑者
//
// 示例值：ou_b13d41c02edc52ce66aaae67bf1abcef
func (builder *MetaBuilder) LatestModifyUser(latestModifyUser string) *MetaBuilder {
	builder.latestModifyUser = latestModifyUser
	builder.latestModifyUserFlag = true
	return builder
}

// 最后编辑时间（Unix时间戳）
//
// 示例值：1652066345
func (builder *MetaBuilder) LatestModifyTime(latestModifyTime string) *MetaBuilder {
	builder.latestModifyTime = latestModifyTime
	builder.latestModifyTimeFlag = true
	return builder
}

// 文档链接
//
// 示例值：https://sample.feishu.cn/docs/doccnfYZzTlvXqZIGTdAHKabcef
func (builder *MetaBuilder) Url(url string) *MetaBuilder {
	builder.url = url
	builder.urlFlag = true
	return builder
}

func (builder *MetaBuilder) Build() *Meta {
	req := &Meta{}
	if builder.docTokenFlag {
		req.DocToken = &builder.docToken

	}
	if builder.docTypeFlag {
		req.DocType = &builder.docType

	}
	if builder.titleFlag {
		req.Title = &builder.title

	}
	if builder.ownerIdFlag {
		req.OwnerId = &builder.ownerId

	}
	if builder.createTimeFlag {
		req.CreateTime = &builder.createTime

	}
	if builder.latestModifyUserFlag {
		req.LatestModifyUser = &builder.latestModifyUser

	}
	if builder.latestModifyTimeFlag {
		req.LatestModifyTime = &builder.latestModifyTime

	}
	if builder.urlFlag {
		req.Url = &builder.url

	}
	return req
}

type MetaFailed struct {
	Token *string `json:"token,omitempty"` // 获取元数据失败的文档token
	Code  *int    `json:"code,omitempty"`  // 获取元数据失败的错误码
}

type MetaFailedBuilder struct {
	token     string // 获取元数据失败的文档token
	tokenFlag bool
	code      int // 获取元数据失败的错误码
	codeFlag  bool
}

func NewMetaFailedBuilder() *MetaFailedBuilder {
	builder := &MetaFailedBuilder{}
	return builder
}

// 获取元数据失败的文档token
//
// 示例值：boxcnrHpsg1QDqXAAAyachabcef
func (builder *MetaFailedBuilder) Token(token string) *MetaFailedBuilder {
	builder.token = token
	builder.tokenFlag = true
	return builder
}

// 获取元数据失败的错误码
//
// 示例值：970005
func (builder *MetaFailedBuilder) Code(code int) *MetaFailedBuilder {
	builder.code = code
	builder.codeFlag = true
	return builder
}

func (builder *MetaFailedBuilder) Build() *MetaFailed {
	req := &MetaFailed{}
	if builder.tokenFlag {
		req.Token = &builder.token

	}
	if builder.codeFlag {
		req.Code = &builder.code

	}
	return req
}

type MetaRequest struct {
	RequestDocs []*RequestDoc `json:"request_docs,omitempty"` // 请求文档,  一次不超过200个
	WithUrl     *bool         `json:"with_url,omitempty"`     // 是否获取文档链接
}

type MetaRequestBuilder struct {
	requestDocs     []*RequestDoc // 请求文档,  一次不超过200个
	requestDocsFlag bool
	withUrl         bool // 是否获取文档链接
	withUrlFlag     bool
}

func NewMetaRequestBuilder() *MetaRequestBuilder {
	builder := &MetaRequestBuilder{}
	return builder
}

// 请求文档,  一次不超过200个
//
// 示例值：
func (builder *MetaRequestBuilder) RequestDocs(requestDocs []*RequestDoc) *MetaRequestBuilder {
	builder.requestDocs = requestDocs
	builder.requestDocsFlag = true
	return builder
}

// 是否获取文档链接
//
// 示例值：false
func (builder *MetaRequestBuilder) WithUrl(withUrl bool) *MetaRequestBuilder {
	builder.withUrl = withUrl
	builder.withUrlFlag = true
	return builder
}

func (builder *MetaRequestBuilder) Build() *MetaRequest {
	req := &MetaRequest{}
	if builder.requestDocsFlag {
		req.RequestDocs = builder.requestDocs
	}
	if builder.withUrlFlag {
		req.WithUrl = &builder.withUrl

	}
	return req
}

type Owner struct {
	MemberType *string `json:"member_type,omitempty"` // 文档拥有者的ID类型
	MemberId   *string `json:"member_id,omitempty"`   // 文档拥有者的ID，与文档拥有者的ID类型需要对应
}

type OwnerBuilder struct {
	memberType     string // 文档拥有者的ID类型
	memberTypeFlag bool
	memberId       string // 文档拥有者的ID，与文档拥有者的ID类型需要对应
	memberIdFlag   bool
}

func NewOwnerBuilder() *OwnerBuilder {
	builder := &OwnerBuilder{}
	return builder
}

// 文档拥有者的ID类型
//
// 示例值：openid
func (builder *OwnerBuilder) MemberType(memberType string) *OwnerBuilder {
	builder.memberType = memberType
	builder.memberTypeFlag = true
	return builder
}

// 文档拥有者的ID，与文档拥有者的ID类型需要对应
//
// 示例值：string
func (builder *OwnerBuilder) MemberId(memberId string) *OwnerBuilder {
	builder.memberId = memberId
	builder.memberIdFlag = true
	return builder
}

func (builder *OwnerBuilder) Build() *Owner {
	req := &Owner{}
	if builder.memberTypeFlag {
		req.MemberType = &builder.memberType

	}
	if builder.memberIdFlag {
		req.MemberId = &builder.memberId

	}
	return req
}

type PermissionPublic struct {
	ExternalAccess  *bool   `json:"external_access,omitempty"`   // 允许内容被分享到组织外;;**可选值有：** ;- `true`: 允许;- `false`: 不允许
	SecurityEntity  *string `json:"security_entity,omitempty"`   // 谁可以复制内容、创建副本、打印、下载
	CommentEntity   *string `json:"comment_entity,omitempty"`    // 可评论设置
	ShareEntity     *string `json:"share_entity,omitempty"`      // 谁可以添加和管理协作者
	LinkShareEntity *string `json:"link_share_entity,omitempty"` // 链接分享设置
	InviteExternal  *bool   `json:"invite_external,omitempty"`   // 允许非「可管理权限」的人分享到组织外
	LockSwitch      *bool   `json:"lock_switch,omitempty"`       // 节点加锁状态
}

type PermissionPublicBuilder struct {
	externalAccess      bool // 允许内容被分享到组织外;;**可选值有：** ;- `true`: 允许;- `false`: 不允许
	externalAccessFlag  bool
	securityEntity      string // 谁可以复制内容、创建副本、打印、下载
	securityEntityFlag  bool
	commentEntity       string // 可评论设置
	commentEntityFlag   bool
	shareEntity         string // 谁可以添加和管理协作者
	shareEntityFlag     bool
	linkShareEntity     string // 链接分享设置
	linkShareEntityFlag bool
	inviteExternal      bool // 允许非「可管理权限」的人分享到组织外
	inviteExternalFlag  bool
	lockSwitch          bool // 节点加锁状态
	lockSwitchFlag      bool
}

func NewPermissionPublicBuilder() *PermissionPublicBuilder {
	builder := &PermissionPublicBuilder{}
	return builder
}

// 允许内容被分享到组织外;;**可选值有：** ;- `true`: 允许;- `false`: 不允许
//
// 示例值：true
func (builder *PermissionPublicBuilder) ExternalAccess(externalAccess bool) *PermissionPublicBuilder {
	builder.externalAccess = externalAccess
	builder.externalAccessFlag = true
	return builder
}

// 谁可以复制内容、创建副本、打印、下载
//
// 示例值：anyone_can_view
func (builder *PermissionPublicBuilder) SecurityEntity(securityEntity string) *PermissionPublicBuilder {
	builder.securityEntity = securityEntity
	builder.securityEntityFlag = true
	return builder
}

// 可评论设置
//
// 示例值：anyone_can_view
func (builder *PermissionPublicBuilder) CommentEntity(commentEntity string) *PermissionPublicBuilder {
	builder.commentEntity = commentEntity
	builder.commentEntityFlag = true
	return builder
}

// 谁可以添加和管理协作者
//
// 示例值：anyone
func (builder *PermissionPublicBuilder) ShareEntity(shareEntity string) *PermissionPublicBuilder {
	builder.shareEntity = shareEntity
	builder.shareEntityFlag = true
	return builder
}

// 链接分享设置
//
// 示例值：tenant_readable
func (builder *PermissionPublicBuilder) LinkShareEntity(linkShareEntity string) *PermissionPublicBuilder {
	builder.linkShareEntity = linkShareEntity
	builder.linkShareEntityFlag = true
	return builder
}

// 允许非「可管理权限」的人分享到组织外
//
// 示例值：true
func (builder *PermissionPublicBuilder) InviteExternal(inviteExternal bool) *PermissionPublicBuilder {
	builder.inviteExternal = inviteExternal
	builder.inviteExternalFlag = true
	return builder
}

// 节点加锁状态
//
// 示例值：false
func (builder *PermissionPublicBuilder) LockSwitch(lockSwitch bool) *PermissionPublicBuilder {
	builder.lockSwitch = lockSwitch
	builder.lockSwitchFlag = true
	return builder
}

func (builder *PermissionPublicBuilder) Build() *PermissionPublic {
	req := &PermissionPublic{}
	if builder.externalAccessFlag {
		req.ExternalAccess = &builder.externalAccess

	}
	if builder.securityEntityFlag {
		req.SecurityEntity = &builder.securityEntity

	}
	if builder.commentEntityFlag {
		req.CommentEntity = &builder.commentEntity

	}
	if builder.shareEntityFlag {
		req.ShareEntity = &builder.shareEntity

	}
	if builder.linkShareEntityFlag {
		req.LinkShareEntity = &builder.linkShareEntity

	}
	if builder.inviteExternalFlag {
		req.InviteExternal = &builder.inviteExternal

	}
	if builder.lockSwitchFlag {
		req.LockSwitch = &builder.lockSwitch

	}
	return req
}

type PermissionPublicRequest struct {
	ExternalAccess  *bool   `json:"external_access,omitempty"`   // 允许内容被分享到组织外
	SecurityEntity  *string `json:"security_entity,omitempty"`   // 谁可以复制内容、创建副本、打印、下载
	CommentEntity   *string `json:"comment_entity,omitempty"`    // 谁可以评论
	ShareEntity     *string `json:"share_entity,omitempty"`      // 谁可以添加和管理协作者
	LinkShareEntity *string `json:"link_share_entity,omitempty"` // 链接分享设置
	InviteExternal  *bool   `json:"invite_external,omitempty"`   // 允许非「可管理权限」的人分享到组织外
}

type PermissionPublicRequestBuilder struct {
	externalAccess      bool // 允许内容被分享到组织外
	externalAccessFlag  bool
	securityEntity      string // 谁可以复制内容、创建副本、打印、下载
	securityEntityFlag  bool
	commentEntity       string // 谁可以评论
	commentEntityFlag   bool
	shareEntity         string // 谁可以添加和管理协作者
	shareEntityFlag     bool
	linkShareEntity     string // 链接分享设置
	linkShareEntityFlag bool
	inviteExternal      bool // 允许非「可管理权限」的人分享到组织外
	inviteExternalFlag  bool
}

func NewPermissionPublicRequestBuilder() *PermissionPublicRequestBuilder {
	builder := &PermissionPublicRequestBuilder{}
	return builder
}

// 允许内容被分享到组织外
//
// 示例值：true
func (builder *PermissionPublicRequestBuilder) ExternalAccess(externalAccess bool) *PermissionPublicRequestBuilder {
	builder.externalAccess = externalAccess
	builder.externalAccessFlag = true
	return builder
}

// 谁可以复制内容、创建副本、打印、下载
//
// 示例值：anyone_can_view
func (builder *PermissionPublicRequestBuilder) SecurityEntity(securityEntity string) *PermissionPublicRequestBuilder {
	builder.securityEntity = securityEntity
	builder.securityEntityFlag = true
	return builder
}

// 谁可以评论
//
// 示例值：anyone_can_view
func (builder *PermissionPublicRequestBuilder) CommentEntity(commentEntity string) *PermissionPublicRequestBuilder {
	builder.commentEntity = commentEntity
	builder.commentEntityFlag = true
	return builder
}

// 谁可以添加和管理协作者
//
// 示例值：anyone
func (builder *PermissionPublicRequestBuilder) ShareEntity(shareEntity string) *PermissionPublicRequestBuilder {
	builder.shareEntity = shareEntity
	builder.shareEntityFlag = true
	return builder
}

// 链接分享设置
//
// 示例值：tenant_readable
func (builder *PermissionPublicRequestBuilder) LinkShareEntity(linkShareEntity string) *PermissionPublicRequestBuilder {
	builder.linkShareEntity = linkShareEntity
	builder.linkShareEntityFlag = true
	return builder
}

// 允许非「可管理权限」的人分享到组织外
//
// 示例值：true
func (builder *PermissionPublicRequestBuilder) InviteExternal(inviteExternal bool) *PermissionPublicRequestBuilder {
	builder.inviteExternal = inviteExternal
	builder.inviteExternalFlag = true
	return builder
}

func (builder *PermissionPublicRequestBuilder) Build() *PermissionPublicRequest {
	req := &PermissionPublicRequest{}
	if builder.externalAccessFlag {
		req.ExternalAccess = &builder.externalAccess

	}
	if builder.securityEntityFlag {
		req.SecurityEntity = &builder.securityEntity

	}
	if builder.commentEntityFlag {
		req.CommentEntity = &builder.commentEntity

	}
	if builder.shareEntityFlag {
		req.ShareEntity = &builder.shareEntity

	}
	if builder.linkShareEntityFlag {
		req.LinkShareEntity = &builder.linkShareEntity

	}
	if builder.inviteExternalFlag {
		req.InviteExternal = &builder.inviteExternal

	}
	return req
}

type Person struct {
	UserId *string `json:"user_id,omitempty"` // 回复 at联系人
}

type PersonBuilder struct {
	userId     string // 回复 at联系人
	userIdFlag bool
}

func NewPersonBuilder() *PersonBuilder {
	builder := &PersonBuilder{}
	return builder
}

// 回复 at联系人
//
// 示例值：ou_cc19b2bfb93f8a44db4b4d6eababcef
func (builder *PersonBuilder) UserId(userId string) *PersonBuilder {
	builder.userId = userId
	builder.userIdFlag = true
	return builder
}

func (builder *PersonBuilder) Build() *Person {
	req := &Person{}
	if builder.userIdFlag {
		req.UserId = &builder.userId

	}
	return req
}

type Property struct {
	Key   *string `json:"key,omitempty"`   // 自定义属性键对象
	Value *string `json:"value,omitempty"` // 自定义属性值对象
}

type PropertyBuilder struct {
	key       string // 自定义属性键对象
	keyFlag   bool
	value     string // 自定义属性值对象
	valueFlag bool
}

func NewPropertyBuilder() *PropertyBuilder {
	builder := &PropertyBuilder{}
	return builder
}

// 自定义属性键对象
//
// 示例值：target_type
func (builder *PropertyBuilder) Key(key string) *PropertyBuilder {
	builder.key = key
	builder.keyFlag = true
	return builder
}

// 自定义属性值对象
//
// 示例值：docx
func (builder *PropertyBuilder) Value(value string) *PropertyBuilder {
	builder.value = value
	builder.valueFlag = true
	return builder
}

func (builder *PropertyBuilder) Build() *Property {
	req := &Property{}
	if builder.keyFlag {
		req.Key = &builder.key

	}
	if builder.valueFlag {
		req.Value = &builder.value

	}
	return req
}

type ReplyContent struct {
	Elements []*ReplyElement `json:"elements,omitempty"` // 回复的内容
}

type ReplyContentBuilder struct {
	elements     []*ReplyElement // 回复的内容
	elementsFlag bool
}

func NewReplyContentBuilder() *ReplyContentBuilder {
	builder := &ReplyContentBuilder{}
	return builder
}

// 回复的内容
//
// 示例值：
func (builder *ReplyContentBuilder) Elements(elements []*ReplyElement) *ReplyContentBuilder {
	builder.elements = elements
	builder.elementsFlag = true
	return builder
}

func (builder *ReplyContentBuilder) Build() *ReplyContent {
	req := &ReplyContent{}
	if builder.elementsFlag {
		req.Elements = builder.elements
	}
	return req
}

type ReplyElement struct {
	Type     *string   `json:"type,omitempty"`      // 回复的内容元素
	TextRun  *TextRun  `json:"text_run,omitempty"`  // 文本内容
	DocsLink *DocsLink `json:"docs_link,omitempty"` // 文本内容
	Person   *Person   `json:"person,omitempty"`    // 文本内容
}

type ReplyElementBuilder struct {
	type_        string // 回复的内容元素
	typeFlag     bool
	textRun      *TextRun // 文本内容
	textRunFlag  bool
	docsLink     *DocsLink // 文本内容
	docsLinkFlag bool
	person       *Person // 文本内容
	personFlag   bool
}

func NewReplyElementBuilder() *ReplyElementBuilder {
	builder := &ReplyElementBuilder{}
	return builder
}

// 回复的内容元素
//
// 示例值：text_run
func (builder *ReplyElementBuilder) Type(type_ string) *ReplyElementBuilder {
	builder.type_ = type_
	builder.typeFlag = true
	return builder
}

// 文本内容
//
// 示例值：
func (builder *ReplyElementBuilder) TextRun(textRun *TextRun) *ReplyElementBuilder {
	builder.textRun = textRun
	builder.textRunFlag = true
	return builder
}

// 文本内容
//
// 示例值：
func (builder *ReplyElementBuilder) DocsLink(docsLink *DocsLink) *ReplyElementBuilder {
	builder.docsLink = docsLink
	builder.docsLinkFlag = true
	return builder
}

// 文本内容
//
// 示例值：
func (builder *ReplyElementBuilder) Person(person *Person) *ReplyElementBuilder {
	builder.person = person
	builder.personFlag = true
	return builder
}

func (builder *ReplyElementBuilder) Build() *ReplyElement {
	req := &ReplyElement{}
	if builder.typeFlag {
		req.Type = &builder.type_

	}
	if builder.textRunFlag {
		req.TextRun = builder.textRun
	}
	if builder.docsLinkFlag {
		req.DocsLink = builder.docsLink
	}
	if builder.personFlag {
		req.Person = builder.person
	}
	return req
}

type ReplyExtra struct {
	ImageList []string `json:"image_list,omitempty"` // 评论中的图片token list
}

type ReplyExtraBuilder struct {
	imageList     []string // 评论中的图片token list
	imageListFlag bool
}

func NewReplyExtraBuilder() *ReplyExtraBuilder {
	builder := &ReplyExtraBuilder{}
	return builder
}

// 评论中的图片token list
//
// 示例值：["xfsfseewewabcef"]
func (builder *ReplyExtraBuilder) ImageList(imageList []string) *ReplyExtraBuilder {
	builder.imageList = imageList
	builder.imageListFlag = true
	return builder
}

func (builder *ReplyExtraBuilder) Build() *ReplyExtra {
	req := &ReplyExtra{}
	if builder.imageListFlag {
		req.ImageList = builder.imageList
	}
	return req
}

type ReplyList struct {
	Replies []*FileCommentReply `json:"replies,omitempty"` // 回复列表
}

type ReplyListBuilder struct {
	replies     []*FileCommentReply // 回复列表
	repliesFlag bool
}

func NewReplyListBuilder() *ReplyListBuilder {
	builder := &ReplyListBuilder{}
	return builder
}

// 回复列表
//
// 示例值：
func (builder *ReplyListBuilder) Replies(replies []*FileCommentReply) *ReplyListBuilder {
	builder.replies = replies
	builder.repliesFlag = true
	return builder
}

func (builder *ReplyListBuilder) Build() *ReplyList {
	req := &ReplyList{}
	if builder.repliesFlag {
		req.Replies = builder.replies
	}
	return req
}

type RequestDoc struct {
	DocToken *string `json:"doc_token,omitempty"` // 文件的 token，获取方式见[如何获取云文档资源相关 token](https://open.feishu.cn/document/ukTMukTMukTM/uczNzUjL3czM14yN3MTN#08bb5df6)
	DocType  *string `json:"doc_type,omitempty"`  // 文件类型
}

type RequestDocBuilder struct {
	docToken     string // 文件的 token，获取方式见[如何获取云文档资源相关 token](https://open.feishu.cn/document/ukTMukTMukTM/uczNzUjL3czM14yN3MTN#08bb5df6)
	docTokenFlag bool
	docType      string // 文件类型
	docTypeFlag  bool
}

func NewRequestDocBuilder() *RequestDocBuilder {
	builder := &RequestDocBuilder{}
	return builder
}

// 文件的 token，获取方式见[如何获取云文档资源相关 token](https://open.feishu.cn/document/ukTMukTMukTM/uczNzUjL3czM14yN3MTN#08bb5df6)
//
// 示例值：doccnfYZzTlvXqZIGTdAHKabcef
func (builder *RequestDocBuilder) DocToken(docToken string) *RequestDocBuilder {
	builder.docToken = docToken
	builder.docTokenFlag = true
	return builder
}

// 文件类型
//
// 示例值：doc
func (builder *RequestDocBuilder) DocType(docType string) *RequestDocBuilder {
	builder.docType = docType
	builder.docTypeFlag = true
	return builder
}

func (builder *RequestDocBuilder) Build() *RequestDoc {
	req := &RequestDoc{}
	if builder.docTokenFlag {
		req.DocToken = &builder.docToken

	}
	if builder.docTypeFlag {
		req.DocType = &builder.docType

	}
	return req
}

type ShortcutInfo struct {
	TargetType  *string `json:"target_type,omitempty"`  // 快捷方式指向的原文件类型
	TargetToken *string `json:"target_token,omitempty"` // 快捷方式指向的原文件token
}

type ShortcutInfoBuilder struct {
	targetType      string // 快捷方式指向的原文件类型
	targetTypeFlag  bool
	targetToken     string // 快捷方式指向的原文件token
	targetTokenFlag bool
}

func NewShortcutInfoBuilder() *ShortcutInfoBuilder {
	builder := &ShortcutInfoBuilder{}
	return builder
}

// 快捷方式指向的原文件类型
//
// 示例值：
func (builder *ShortcutInfoBuilder) TargetType(targetType string) *ShortcutInfoBuilder {
	builder.targetType = targetType
	builder.targetTypeFlag = true
	return builder
}

// 快捷方式指向的原文件token
//
// 示例值：
func (builder *ShortcutInfoBuilder) TargetToken(targetToken string) *ShortcutInfoBuilder {
	builder.targetToken = targetToken
	builder.targetTokenFlag = true
	return builder
}

func (builder *ShortcutInfoBuilder) Build() *ShortcutInfo {
	req := &ShortcutInfo{}
	if builder.targetTypeFlag {
		req.TargetType = &builder.targetType

	}
	if builder.targetTokenFlag {
		req.TargetToken = &builder.targetToken

	}
	return req
}

type TextRun struct {
	Text *string `json:"text,omitempty"` // 回复 普通文本
}

type TextRunBuilder struct {
	text     string // 回复 普通文本
	textFlag bool
}

func NewTextRunBuilder() *TextRunBuilder {
	builder := &TextRunBuilder{}
	return builder
}

// 回复 普通文本
//
// 示例值：comment text
func (builder *TextRunBuilder) Text(text string) *TextRunBuilder {
	builder.text = text
	builder.textFlag = true
	return builder
}

func (builder *TextRunBuilder) Build() *TextRun {
	req := &TextRun{}
	if builder.textFlag {
		req.Text = &builder.text

	}
	return req
}

type TmpDownloadUrl struct {
	FileToken      *string `json:"file_token,omitempty"`       // 文件标识符
	TmpDownloadUrl *string `json:"tmp_download_url,omitempty"` // 文件临时下载链接
}

type TmpDownloadUrlBuilder struct {
	fileToken          string // 文件标识符
	fileTokenFlag      bool
	tmpDownloadUrl     string // 文件临时下载链接
	tmpDownloadUrlFlag bool
}

func NewTmpDownloadUrlBuilder() *TmpDownloadUrlBuilder {
	builder := &TmpDownloadUrlBuilder{}
	return builder
}

// 文件标识符
//
// 示例值：boxcnrHpsg1QDqXAAAyachabcef
func (builder *TmpDownloadUrlBuilder) FileToken(fileToken string) *TmpDownloadUrlBuilder {
	builder.fileToken = fileToken
	builder.fileTokenFlag = true
	return builder
}

// 文件临时下载链接
//
// 示例值：https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/authcode/?code=ZDA3MzNiNmUwMjE2MGUzZmQ1OGZlOWYzMWQ4YmI0ZjdfMDYzOWNlZjgyMmI1MmY5NTUxZmM0MjJlYWIyMGVjOWZfSUQ6Njk3NjgzMTY0Mjc5OTI5MjQyMl8xNjI0NDMxMDY3OjE2MjQ1MTc0NjdfVjM
func (builder *TmpDownloadUrlBuilder) TmpDownloadUrl(tmpDownloadUrl string) *TmpDownloadUrlBuilder {
	builder.tmpDownloadUrl = tmpDownloadUrl
	builder.tmpDownloadUrlFlag = true
	return builder
}

func (builder *TmpDownloadUrlBuilder) Build() *TmpDownloadUrl {
	req := &TmpDownloadUrl{}
	if builder.fileTokenFlag {
		req.FileToken = &builder.fileToken

	}
	if builder.tmpDownloadUrlFlag {
		req.TmpDownloadUrl = &builder.tmpDownloadUrl

	}
	return req
}

type TokenType struct {
	Token *string `json:"token,omitempty"` // 文件的 token，获取方式见 [对接前说明](https://open.feishu.cn/document/ukTMukTMukTM/uczNzUjL3czM14yN3MTN)的第 4 项
	Type  *string `json:"type,omitempty"`  // 文档类型  "isv"
}

type TokenTypeBuilder struct {
	token     string // 文件的 token，获取方式见 [对接前说明](https://open.feishu.cn/document/ukTMukTMukTM/uczNzUjL3czM14yN3MTN)的第 4 项
	tokenFlag bool
	type_     string // 文档类型  "isv"
	typeFlag  bool
}

func NewTokenTypeBuilder() *TokenTypeBuilder {
	builder := &TokenTypeBuilder{}
	return builder
}

// 文件的 token，获取方式见 [对接前说明](https://open.feishu.cn/document/ukTMukTMukTM/uczNzUjL3czM14yN3MTN)的第 4 项
//
// 示例值：isvcnBKgoMyY5OMbUG6FioTXuBe
func (builder *TokenTypeBuilder) Token(token string) *TokenTypeBuilder {
	builder.token = token
	builder.tokenFlag = true
	return builder
}

// 文档类型  "isv"
//
// 示例值：isv
func (builder *TokenTypeBuilder) Type(type_ string) *TokenTypeBuilder {
	builder.type_ = type_
	builder.typeFlag = true
	return builder
}

func (builder *TokenTypeBuilder) Build() *TokenType {
	req := &TokenType{}
	if builder.tokenFlag {
		req.Token = &builder.token

	}
	if builder.typeFlag {
		req.Type = &builder.type_

	}
	return req
}

type UploadInfo struct {
	FileName   *string `json:"file_name,omitempty"`   // 文件名
	ParentType *string `json:"parent_type,omitempty"` // 父节点类型（父文件系统类型）
	ParentNode *string `json:"parent_node,omitempty"` // 父节点
	Size       *int    `json:"size,omitempty"`        // 文件大小
}

type UploadInfoBuilder struct {
	fileName       string // 文件名
	fileNameFlag   bool
	parentType     string // 父节点类型（父文件系统类型）
	parentTypeFlag bool
	parentNode     string // 父节点
	parentNodeFlag bool
	size           int // 文件大小
	sizeFlag       bool
}

func NewUploadInfoBuilder() *UploadInfoBuilder {
	builder := &UploadInfoBuilder{}
	return builder
}

// 文件名
//
// 示例值：
func (builder *UploadInfoBuilder) FileName(fileName string) *UploadInfoBuilder {
	builder.fileName = fileName
	builder.fileNameFlag = true
	return builder
}

// 父节点类型（父文件系统类型）
//
// 示例值：
func (builder *UploadInfoBuilder) ParentType(parentType string) *UploadInfoBuilder {
	builder.parentType = parentType
	builder.parentTypeFlag = true
	return builder
}

// 父节点
//
// 示例值：
func (builder *UploadInfoBuilder) ParentNode(parentNode string) *UploadInfoBuilder {
	builder.parentNode = parentNode
	builder.parentNodeFlag = true
	return builder
}

// 文件大小
//
// 示例值：
func (builder *UploadInfoBuilder) Size(size int) *UploadInfoBuilder {
	builder.size = size
	builder.sizeFlag = true
	return builder
}

func (builder *UploadInfoBuilder) Build() *UploadInfo {
	req := &UploadInfo{}
	if builder.fileNameFlag {
		req.FileName = &builder.fileName

	}
	if builder.parentTypeFlag {
		req.ParentType = &builder.parentType

	}
	if builder.parentNodeFlag {
		req.ParentNode = &builder.parentNode

	}
	if builder.sizeFlag {
		req.Size = &builder.size

	}
	return req
}

type UserId struct {
	UserId  *string `json:"user_id,omitempty"`  //
	OpenId  *string `json:"open_id,omitempty"`  //
	UnionId *string `json:"union_id,omitempty"` //
}

type UserIdBuilder struct {
	userId      string //
	userIdFlag  bool
	openId      string //
	openIdFlag  bool
	unionId     string //
	unionIdFlag bool
}

func NewUserIdBuilder() *UserIdBuilder {
	builder := &UserIdBuilder{}
	return builder
}

//
//
// 示例值：
func (builder *UserIdBuilder) UserId(userId string) *UserIdBuilder {
	builder.userId = userId
	builder.userIdFlag = true
	return builder
}

//
//
// 示例值：
func (builder *UserIdBuilder) OpenId(openId string) *UserIdBuilder {
	builder.openId = openId
	builder.openIdFlag = true
	return builder
}

//
//
// 示例值：
func (builder *UserIdBuilder) UnionId(unionId string) *UserIdBuilder {
	builder.unionId = unionId
	builder.unionIdFlag = true
	return builder
}

func (builder *UserIdBuilder) Build() *UserId {
	req := &UserId{}
	if builder.userIdFlag {
		req.UserId = &builder.userId

	}
	if builder.openIdFlag {
		req.OpenId = &builder.openId

	}
	if builder.unionIdFlag {
		req.UnionId = &builder.unionId

	}
	return req
}

type Version struct {
	Name        *string `json:"name,omitempty"`         // 版本文档标题
	Version     *string `json:"version,omitempty"`      // 版本文档版本号
	ParentToken *string `json:"parent_token,omitempty"` // shtbcpM2mm3znrLfWnf4browTYp
	OwnerId     *string `json:"owner_id,omitempty"`     // 版本文档所有者id
	CreatorId   *string `json:"creator_id,omitempty"`   // 版本文档创建者id
	CreateTime  *string `json:"create_time,omitempty"`  // 版本文档创建时间
	UpdateTime  *string `json:"update_time,omitempty"`  // 版本文档更新时间
	Status      *string `json:"status,omitempty"`       // 版本文档状态
	ObjType     *string `json:"obj_type,omitempty"`     // 版本文档类型
	ParentType  *string `json:"parent_type,omitempty"`  // 源文档类型
}

type VersionBuilder struct {
	name            string // 版本文档标题
	nameFlag        bool
	version         string // 版本文档版本号
	versionFlag     bool
	parentToken     string // shtbcpM2mm3znrLfWnf4browTYp
	parentTokenFlag bool
	ownerId         string // 版本文档所有者id
	ownerIdFlag     bool
	creatorId       string // 版本文档创建者id
	creatorIdFlag   bool
	createTime      string // 版本文档创建时间
	createTimeFlag  bool
	updateTime      string // 版本文档更新时间
	updateTimeFlag  bool
	status          string // 版本文档状态
	statusFlag      bool
	objType         string // 版本文档类型
	objTypeFlag     bool
	parentType      string // 源文档类型
	parentTypeFlag  bool
}

func NewVersionBuilder() *VersionBuilder {
	builder := &VersionBuilder{}
	return builder
}

// 版本文档标题
//
// 示例值：文档标题
func (builder *VersionBuilder) Name(name string) *VersionBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

// 版本文档版本号
//
// 示例值：version1
func (builder *VersionBuilder) Version(version string) *VersionBuilder {
	builder.version = version
	builder.versionFlag = true
	return builder
}

// shtbcpM2mm3znrLfWnf4browTYp
//
// 示例值：1665739388
func (builder *VersionBuilder) ParentToken(parentToken string) *VersionBuilder {
	builder.parentToken = parentToken
	builder.parentTokenFlag = true
	return builder
}

// 版本文档所有者id
//
// 示例值：694699009591869450
func (builder *VersionBuilder) OwnerId(ownerId string) *VersionBuilder {
	builder.ownerId = ownerId
	builder.ownerIdFlag = true
	return builder
}

// 版本文档创建者id
//
// 示例值：694699009591869451
func (builder *VersionBuilder) CreatorId(creatorId string) *VersionBuilder {
	builder.creatorId = creatorId
	builder.creatorIdFlag = true
	return builder
}

// 版本文档创建时间
//
// 示例值：1660708537
func (builder *VersionBuilder) CreateTime(createTime string) *VersionBuilder {
	builder.createTime = createTime
	builder.createTimeFlag = true
	return builder
}

// 版本文档更新时间
//
// 示例值：1660708537
func (builder *VersionBuilder) UpdateTime(updateTime string) *VersionBuilder {
	builder.updateTime = updateTime
	builder.updateTimeFlag = true
	return builder
}

// 版本文档状态
//
// 示例值：0
func (builder *VersionBuilder) Status(status string) *VersionBuilder {
	builder.status = status
	builder.statusFlag = true
	return builder
}

// 版本文档类型
//
// 示例值：docx
func (builder *VersionBuilder) ObjType(objType string) *VersionBuilder {
	builder.objType = objType
	builder.objTypeFlag = true
	return builder
}

// 源文档类型
//
// 示例值：docx
func (builder *VersionBuilder) ParentType(parentType string) *VersionBuilder {
	builder.parentType = parentType
	builder.parentTypeFlag = true
	return builder
}

func (builder *VersionBuilder) Build() *Version {
	req := &Version{}
	if builder.nameFlag {
		req.Name = &builder.name

	}
	if builder.versionFlag {
		req.Version = &builder.version

	}
	if builder.parentTokenFlag {
		req.ParentToken = &builder.parentToken

	}
	if builder.ownerIdFlag {
		req.OwnerId = &builder.ownerId

	}
	if builder.creatorIdFlag {
		req.CreatorId = &builder.creatorId

	}
	if builder.createTimeFlag {
		req.CreateTime = &builder.createTime

	}
	if builder.updateTimeFlag {
		req.UpdateTime = &builder.updateTime

	}
	if builder.statusFlag {
		req.Status = &builder.status

	}
	if builder.objTypeFlag {
		req.ObjType = &builder.objType

	}
	if builder.parentTypeFlag {
		req.ParentType = &builder.parentType

	}
	return req
}

type CreateExportTaskReqBuilder struct {
	apiReq     *larkcore.ApiReq
	exportTask *ExportTask
}

func NewCreateExportTaskReqBuilder() *CreateExportTaskReqBuilder {
	builder := &CreateExportTaskReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 创建导出任务，将云文件导出为指定格式的本地文件。该接口为异步接口，需要通过轮询 [查询导出任务结果](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/drive-v1/export_task/get) 接口获取任务结果。
func (builder *CreateExportTaskReqBuilder) ExportTask(exportTask *ExportTask) *CreateExportTaskReqBuilder {
	builder.exportTask = exportTask
	return builder
}

func (builder *CreateExportTaskReqBuilder) Build() *CreateExportTaskReq {
	req := &CreateExportTaskReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.Body = builder.exportTask
	return req
}

type CreateExportTaskReq struct {
	apiReq     *larkcore.ApiReq
	ExportTask *ExportTask `body:""`
}

type CreateExportTaskRespData struct {
	Ticket *string `json:"ticket,omitempty"` // 导出任务ID
}

type CreateExportTaskResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *CreateExportTaskRespData `json:"data"` // 业务数据
}

func (resp *CreateExportTaskResp) Success() bool {
	return resp.Code == 0
}

type DownloadExportTaskReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewDownloadExportTaskReqBuilder() *DownloadExportTaskReqBuilder {
	builder := &DownloadExportTaskReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 导出文档token
//
// 示例值：boxcnNAlfwHxxxxxxxxxxSaLSec
func (builder *DownloadExportTaskReqBuilder) FileToken(fileToken string) *DownloadExportTaskReqBuilder {
	builder.apiReq.PathParams.Set("file_token", fmt.Sprint(fileToken))
	return builder
}

func (builder *DownloadExportTaskReqBuilder) Build() *DownloadExportTaskReq {
	req := &DownloadExportTaskReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	return req
}

type DownloadExportTaskReq struct {
	apiReq *larkcore.ApiReq
}

type DownloadExportTaskResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	File     io.Reader `json:"-"`
	FileName string    `json:"-"`
}

func (resp *DownloadExportTaskResp) Success() bool {
	return resp.Code == 0
}

func (resp *DownloadExportTaskResp) WriteFile(fileName string) error {
	bs, err := ioutil.ReadAll(resp.File)
	if err != nil {
		return err
	}

	err = ioutil.WriteFile(fileName, bs, 0666)
	if err != nil {
		return err
	}
	return nil
}

type GetExportTaskReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewGetExportTaskReqBuilder() *GetExportTaskReqBuilder {
	builder := &GetExportTaskReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 导出任务ID，[创建导出任务](/ssl::ttdoc//uAjLw4CM/ukTMukTMukTM/reference/drive-v1/export_task/create) 响应中的 ticket 字段
//
// 示例值：6933093124755423251
func (builder *GetExportTaskReqBuilder) Ticket(ticket string) *GetExportTaskReqBuilder {
	builder.apiReq.PathParams.Set("ticket", fmt.Sprint(ticket))
	return builder
}

// 导出文档的 token;;[如何获取文档 otken](https://open.feishu.cn/document/ukTMukTMukTM/uczNzUjL3czM14yN3MTN#08bb5df6)
//
// 示例值：doccnZVxxxxxxxxxxxxGiyBgYqe
func (builder *GetExportTaskReqBuilder) Token(token string) *GetExportTaskReqBuilder {
	builder.apiReq.QueryParams.Set("token", fmt.Sprint(token))
	return builder
}

func (builder *GetExportTaskReqBuilder) Build() *GetExportTaskReq {
	req := &GetExportTaskReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type GetExportTaskReq struct {
	apiReq *larkcore.ApiReq
}

type GetExportTaskRespData struct {
	Result *ExportTask `json:"result,omitempty"` // 导出任务结果
}

type GetExportTaskResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *GetExportTaskRespData `json:"data"` // 业务数据
}

func (resp *GetExportTaskResp) Success() bool {
	return resp.Code == 0
}

type CopyFileReqBodyBuilder struct {
	name            string // 被复制文件的新名称
	nameFlag        bool
	type_           string // 被复制文件的类型，如果该值为空或者与文件实际类型不匹配，接口会返回失败。
	typeFlag        bool
	folderToken     string // 文件被复制到的目标文件夹token
	folderTokenFlag bool
	extra           []*Property // 用户自定义请求附加参数，用于实现特殊的复制语义
	extraFlag       bool
}

func NewCopyFileReqBodyBuilder() *CopyFileReqBodyBuilder {
	builder := &CopyFileReqBodyBuilder{}
	return builder
}

// 被复制文件的新名称
//
//示例值：test.txt
func (builder *CopyFileReqBodyBuilder) Name(name string) *CopyFileReqBodyBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

// 被复制文件的类型，如果该值为空或者与文件实际类型不匹配，接口会返回失败。
//
//示例值：doc
func (builder *CopyFileReqBodyBuilder) Type(type_ string) *CopyFileReqBodyBuilder {
	builder.type_ = type_
	builder.typeFlag = true
	return builder
}

// 文件被复制到的目标文件夹token
//
//示例值：fldbcO1UuPz8VwnpPx5a92abcef
func (builder *CopyFileReqBodyBuilder) FolderToken(folderToken string) *CopyFileReqBodyBuilder {
	builder.folderToken = folderToken
	builder.folderTokenFlag = true
	return builder
}

// 用户自定义请求附加参数，用于实现特殊的复制语义
//
//示例值：
func (builder *CopyFileReqBodyBuilder) Extra(extra []*Property) *CopyFileReqBodyBuilder {
	builder.extra = extra
	builder.extraFlag = true
	return builder
}

func (builder *CopyFileReqBodyBuilder) Build() *CopyFileReqBody {
	req := &CopyFileReqBody{}
	if builder.nameFlag {
		req.Name = &builder.name
	}
	if builder.typeFlag {
		req.Type = &builder.type_
	}
	if builder.folderTokenFlag {
		req.FolderToken = &builder.folderToken
	}
	if builder.extraFlag {
		req.Extra = builder.extra
	}
	return req
}

type CopyFilePathReqBodyBuilder struct {
	name            string // 被复制文件的新名称
	nameFlag        bool
	type_           string // 被复制文件的类型，如果该值为空或者与文件实际类型不匹配，接口会返回失败。
	typeFlag        bool
	folderToken     string // 文件被复制到的目标文件夹token
	folderTokenFlag bool
	extra           []*Property // 用户自定义请求附加参数，用于实现特殊的复制语义
	extraFlag       bool
}

func NewCopyFilePathReqBodyBuilder() *CopyFilePathReqBodyBuilder {
	builder := &CopyFilePathReqBodyBuilder{}
	return builder
}

// 被复制文件的新名称
//
// 示例值：test.txt
func (builder *CopyFilePathReqBodyBuilder) Name(name string) *CopyFilePathReqBodyBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

// 被复制文件的类型，如果该值为空或者与文件实际类型不匹配，接口会返回失败。
//
// 示例值：doc
func (builder *CopyFilePathReqBodyBuilder) Type(type_ string) *CopyFilePathReqBodyBuilder {
	builder.type_ = type_
	builder.typeFlag = true
	return builder
}

// 文件被复制到的目标文件夹token
//
// 示例值：fldbcO1UuPz8VwnpPx5a92abcef
func (builder *CopyFilePathReqBodyBuilder) FolderToken(folderToken string) *CopyFilePathReqBodyBuilder {
	builder.folderToken = folderToken
	builder.folderTokenFlag = true
	return builder
}

// 用户自定义请求附加参数，用于实现特殊的复制语义
//
// 示例值：
func (builder *CopyFilePathReqBodyBuilder) Extra(extra []*Property) *CopyFilePathReqBodyBuilder {
	builder.extra = extra
	builder.extraFlag = true
	return builder
}

func (builder *CopyFilePathReqBodyBuilder) Build() (*CopyFileReqBody, error) {
	req := &CopyFileReqBody{}
	if builder.nameFlag {
		req.Name = &builder.name
	}
	if builder.typeFlag {
		req.Type = &builder.type_
	}
	if builder.folderTokenFlag {
		req.FolderToken = &builder.folderToken
	}
	if builder.extraFlag {
		req.Extra = builder.extra
	}
	return req, nil
}

type CopyFileReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *CopyFileReqBody
}

func NewCopyFileReqBuilder() *CopyFileReqBuilder {
	builder := &CopyFileReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 被复制的文件token
//
// 示例值：doccngpahSdXrFPIBD4XdIabcef
func (builder *CopyFileReqBuilder) FileToken(fileToken string) *CopyFileReqBuilder {
	builder.apiReq.PathParams.Set("file_token", fmt.Sprint(fileToken))
	return builder
}

// 将文件复制到用户云空间的其他文件夹中。不支持复制文件夹。;;如果目标文件夹是我的空间，则复制的文件会在「**我的空间**」的「**归我所有**」列表里。
func (builder *CopyFileReqBuilder) Body(body *CopyFileReqBody) *CopyFileReqBuilder {
	builder.body = body
	return builder
}

func (builder *CopyFileReqBuilder) Build() *CopyFileReq {
	req := &CopyFileReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.Body = builder.body
	return req
}

type CopyFileReqBody struct {
	Name        *string     `json:"name,omitempty"`         // 被复制文件的新名称
	Type        *string     `json:"type,omitempty"`         // 被复制文件的类型，如果该值为空或者与文件实际类型不匹配，接口会返回失败。
	FolderToken *string     `json:"folder_token,omitempty"` // 文件被复制到的目标文件夹token
	Extra       []*Property `json:"extra,omitempty"`        // 用户自定义请求附加参数，用于实现特殊的复制语义
}

type CopyFileReq struct {
	apiReq *larkcore.ApiReq
	Body   *CopyFileReqBody `body:""`
}

type CopyFileRespData struct {
	File *File `json:"file,omitempty"` // 复制后的文件资源
}

type CopyFileResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *CopyFileRespData `json:"data"` // 业务数据
}

func (resp *CopyFileResp) Success() bool {
	return resp.Code == 0
}

type CreateFolderFileReqBodyBuilder struct {
	name            string // 文件夹名称
	nameFlag        bool
	folderToken     string // 父文件夹token
	folderTokenFlag bool
}

func NewCreateFolderFileReqBodyBuilder() *CreateFolderFileReqBodyBuilder {
	builder := &CreateFolderFileReqBodyBuilder{}
	return builder
}

// 文件夹名称
//
//示例值：New Folder
func (builder *CreateFolderFileReqBodyBuilder) Name(name string) *CreateFolderFileReqBodyBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

// 父文件夹token
//
//示例值：fldbcO1UuPz8VwnpPx5a92abcef
func (builder *CreateFolderFileReqBodyBuilder) FolderToken(folderToken string) *CreateFolderFileReqBodyBuilder {
	builder.folderToken = folderToken
	builder.folderTokenFlag = true
	return builder
}

func (builder *CreateFolderFileReqBodyBuilder) Build() *CreateFolderFileReqBody {
	req := &CreateFolderFileReqBody{}
	if builder.nameFlag {
		req.Name = &builder.name
	}
	if builder.folderTokenFlag {
		req.FolderToken = &builder.folderToken
	}
	return req
}

type CreateFolderFilePathReqBodyBuilder struct {
	name            string // 文件夹名称
	nameFlag        bool
	folderToken     string // 父文件夹token
	folderTokenFlag bool
}

func NewCreateFolderFilePathReqBodyBuilder() *CreateFolderFilePathReqBodyBuilder {
	builder := &CreateFolderFilePathReqBodyBuilder{}
	return builder
}

// 文件夹名称
//
// 示例值：New Folder
func (builder *CreateFolderFilePathReqBodyBuilder) Name(name string) *CreateFolderFilePathReqBodyBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

// 父文件夹token
//
// 示例值：fldbcO1UuPz8VwnpPx5a92abcef
func (builder *CreateFolderFilePathReqBodyBuilder) FolderToken(folderToken string) *CreateFolderFilePathReqBodyBuilder {
	builder.folderToken = folderToken
	builder.folderTokenFlag = true
	return builder
}

func (builder *CreateFolderFilePathReqBodyBuilder) Build() (*CreateFolderFileReqBody, error) {
	req := &CreateFolderFileReqBody{}
	if builder.nameFlag {
		req.Name = &builder.name
	}
	if builder.folderTokenFlag {
		req.FolderToken = &builder.folderToken
	}
	return req, nil
}

type CreateFolderFileReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *CreateFolderFileReqBody
}

func NewCreateFolderFileReqBuilder() *CreateFolderFileReqBuilder {
	builder := &CreateFolderFileReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 在用户云空间的指定文件夹中创建一个新的空文件夹。
func (builder *CreateFolderFileReqBuilder) Body(body *CreateFolderFileReqBody) *CreateFolderFileReqBuilder {
	builder.body = body
	return builder
}

func (builder *CreateFolderFileReqBuilder) Build() *CreateFolderFileReq {
	req := &CreateFolderFileReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.Body = builder.body
	return req
}

type CreateFolderFileReqBody struct {
	Name        *string `json:"name,omitempty"`         // 文件夹名称
	FolderToken *string `json:"folder_token,omitempty"` // 父文件夹token
}

type CreateFolderFileReq struct {
	apiReq *larkcore.ApiReq
	Body   *CreateFolderFileReqBody `body:""`
}

type CreateFolderFileRespData struct {
	Token *string `json:"token,omitempty"` // 创建文件夹的token
	Url   *string `json:"url,omitempty"`   // 创建文件夹的访问url
}

type CreateFolderFileResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *CreateFolderFileRespData `json:"data"` // 业务数据
}

func (resp *CreateFolderFileResp) Success() bool {
	return resp.Code == 0
}

type DeleteFileReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewDeleteFileReqBuilder() *DeleteFileReqBuilder {
	builder := &DeleteFileReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 需要删除的文件token
//
// 示例值：boxcnrHpsg1QDqXAAAyachabcef
func (builder *DeleteFileReqBuilder) FileToken(fileToken string) *DeleteFileReqBuilder {
	builder.apiReq.PathParams.Set("file_token", fmt.Sprint(fileToken))
	return builder
}

// 被删除文件的类型
//
// 示例值：file
func (builder *DeleteFileReqBuilder) Type(type_ string) *DeleteFileReqBuilder {
	builder.apiReq.QueryParams.Set("type", fmt.Sprint(type_))
	return builder
}

func (builder *DeleteFileReqBuilder) Build() *DeleteFileReq {
	req := &DeleteFileReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type DeleteFileReq struct {
	apiReq *larkcore.ApiReq
}

type DeleteFileRespData struct {
	TaskId *string `json:"task_id,omitempty"` // 异步任务id，删除文件夹时返回
}

type DeleteFileResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *DeleteFileRespData `json:"data"` // 业务数据
}

func (resp *DeleteFileResp) Success() bool {
	return resp.Code == 0
}

type DownloadFileReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewDownloadFileReqBuilder() *DownloadFileReqBuilder {
	builder := &DownloadFileReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 文件的 token，获取方式见 [概述](https://open.feishu.cn/document/ukTMukTMukTM/uUDN04SN0QjL1QDN/files/guide/introduction)
//
// 示例值：boxcnabCdefg12345
func (builder *DownloadFileReqBuilder) FileToken(fileToken string) *DownloadFileReqBuilder {
	builder.apiReq.PathParams.Set("file_token", fmt.Sprint(fileToken))
	return builder
}

func (builder *DownloadFileReqBuilder) Build() *DownloadFileReq {
	req := &DownloadFileReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	return req
}

type DownloadFileReq struct {
	apiReq *larkcore.ApiReq
}

type DownloadFileResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	File     io.Reader `json:"-"`
	FileName string    `json:"-"`
}

func (resp *DownloadFileResp) Success() bool {
	return resp.Code == 0
}

func (resp *DownloadFileResp) WriteFile(fileName string) error {
	bs, err := ioutil.ReadAll(resp.File)
	if err != nil {
		return err
	}

	err = ioutil.WriteFile(fileName, bs, 0666)
	if err != nil {
		return err
	}
	return nil
}

type ListFileReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewListFileReqBuilder() *ListFileReqBuilder {
	builder := &ListFileReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 分页大小
//
// 示例值：10
func (builder *ListFileReqBuilder) PageSize(pageSize int) *ListFileReqBuilder {
	builder.apiReq.QueryParams.Set("page_size", fmt.Sprint(pageSize))
	return builder
}

// 分页标记，第一次请求不填，表示从头开始遍历；分页查询结果还有更多项时会同时返回新的 page_token，下次遍历可采用该 page_token 获取查询结果
//
// 示例值：MTY1NTA3MTA1OXw3MTA4NDc2MDc1NzkyOTI0Nabcef
func (builder *ListFileReqBuilder) PageToken(pageToken string) *ListFileReqBuilder {
	builder.apiReq.QueryParams.Set("page_token", fmt.Sprint(pageToken))
	return builder
}

// 文件夹的token（若不填写该参数或填写空字符串，则默认获取用户云空间下的清单，且不支持分页）
//
// 示例值：fldbcO1UuPz8VwnpPx5a9abcef
func (builder *ListFileReqBuilder) FolderToken(folderToken string) *ListFileReqBuilder {
	builder.apiReq.QueryParams.Set("folder_token", fmt.Sprint(folderToken))
	return builder
}

func (builder *ListFileReqBuilder) Build() *ListFileReq {
	req := &ListFileReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type ListFileReq struct {
	apiReq *larkcore.ApiReq
}

type ListFileRespData struct {
	Files         []*File `json:"files,omitempty"`           // 文件夹清单列表
	NextPageToken *string `json:"next_page_token,omitempty"` // 分页标记，当 has_more 为 true 时，会同时返回下一次遍历的page_token，否则则不返回
	HasMore       *bool   `json:"has_more,omitempty"`        //
}

type ListFileResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *ListFileRespData `json:"data"` // 业务数据
}

func (resp *ListFileResp) Success() bool {
	return resp.Code == 0
}

type MoveFileReqBodyBuilder struct {
	type_           string // 文件类型，如果该值为空或者与文件实际类型不匹配，接口会返回失败。
	typeFlag        bool
	folderToken     string // 目标文件夹token
	folderTokenFlag bool
}

func NewMoveFileReqBodyBuilder() *MoveFileReqBodyBuilder {
	builder := &MoveFileReqBodyBuilder{}
	return builder
}

// 文件类型，如果该值为空或者与文件实际类型不匹配，接口会返回失败。
//
//示例值：file
func (builder *MoveFileReqBodyBuilder) Type(type_ string) *MoveFileReqBodyBuilder {
	builder.type_ = type_
	builder.typeFlag = true
	return builder
}

// 目标文件夹token
//
//示例值：fldbcO1UuPz8VwnpPx5a92abcef
func (builder *MoveFileReqBodyBuilder) FolderToken(folderToken string) *MoveFileReqBodyBuilder {
	builder.folderToken = folderToken
	builder.folderTokenFlag = true
	return builder
}

func (builder *MoveFileReqBodyBuilder) Build() *MoveFileReqBody {
	req := &MoveFileReqBody{}
	if builder.typeFlag {
		req.Type = &builder.type_
	}
	if builder.folderTokenFlag {
		req.FolderToken = &builder.folderToken
	}
	return req
}

type MoveFilePathReqBodyBuilder struct {
	type_           string // 文件类型，如果该值为空或者与文件实际类型不匹配，接口会返回失败。
	typeFlag        bool
	folderToken     string // 目标文件夹token
	folderTokenFlag bool
}

func NewMoveFilePathReqBodyBuilder() *MoveFilePathReqBodyBuilder {
	builder := &MoveFilePathReqBodyBuilder{}
	return builder
}

// 文件类型，如果该值为空或者与文件实际类型不匹配，接口会返回失败。
//
// 示例值：file
func (builder *MoveFilePathReqBodyBuilder) Type(type_ string) *MoveFilePathReqBodyBuilder {
	builder.type_ = type_
	builder.typeFlag = true
	return builder
}

// 目标文件夹token
//
// 示例值：fldbcO1UuPz8VwnpPx5a92abcef
func (builder *MoveFilePathReqBodyBuilder) FolderToken(folderToken string) *MoveFilePathReqBodyBuilder {
	builder.folderToken = folderToken
	builder.folderTokenFlag = true
	return builder
}

func (builder *MoveFilePathReqBodyBuilder) Build() (*MoveFileReqBody, error) {
	req := &MoveFileReqBody{}
	if builder.typeFlag {
		req.Type = &builder.type_
	}
	if builder.folderTokenFlag {
		req.FolderToken = &builder.folderToken
	}
	return req, nil
}

type MoveFileReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *MoveFileReqBody
}

func NewMoveFileReqBuilder() *MoveFileReqBuilder {
	builder := &MoveFileReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 需要移动的文件token
//
// 示例值：boxcnrHpsg1QDqXAAAyachabcef
func (builder *MoveFileReqBuilder) FileToken(fileToken string) *MoveFileReqBuilder {
	builder.apiReq.PathParams.Set("file_token", fmt.Sprint(fileToken))
	return builder
}

// 将文件或者文件夹移动到用户云空间的其他位置。
func (builder *MoveFileReqBuilder) Body(body *MoveFileReqBody) *MoveFileReqBuilder {
	builder.body = body
	return builder
}

func (builder *MoveFileReqBuilder) Build() *MoveFileReq {
	req := &MoveFileReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.Body = builder.body
	return req
}

type MoveFileReqBody struct {
	Type        *string `json:"type,omitempty"`         // 文件类型，如果该值为空或者与文件实际类型不匹配，接口会返回失败。
	FolderToken *string `json:"folder_token,omitempty"` // 目标文件夹token
}

type MoveFileReq struct {
	apiReq *larkcore.ApiReq
	Body   *MoveFileReqBody `body:""`
}

type MoveFileRespData struct {
	TaskId *string `json:"task_id,omitempty"` // 异步任务id，移动文件夹时返回
}

type MoveFileResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *MoveFileRespData `json:"data"` // 业务数据
}

func (resp *MoveFileResp) Success() bool {
	return resp.Code == 0
}

type SubscribeFileReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewSubscribeFileReqBuilder() *SubscribeFileReqBuilder {
	builder := &SubscribeFileReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 文档token
//
// 示例值：doccnxxxxxxxxxxxxxxxxxxxxxx
func (builder *SubscribeFileReqBuilder) FileToken(fileToken string) *SubscribeFileReqBuilder {
	builder.apiReq.PathParams.Set("file_token", fmt.Sprint(fileToken))
	return builder
}

// 文档类型
//
// 示例值：doc
func (builder *SubscribeFileReqBuilder) FileType(fileType string) *SubscribeFileReqBuilder {
	builder.apiReq.QueryParams.Set("file_type", fmt.Sprint(fileType))
	return builder
}

func (builder *SubscribeFileReqBuilder) Build() *SubscribeFileReq {
	req := &SubscribeFileReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type SubscribeFileReq struct {
	apiReq *larkcore.ApiReq
}

type SubscribeFileResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *SubscribeFileResp) Success() bool {
	return resp.Code == 0
}

type TaskCheckFileReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewTaskCheckFileReqBuilder() *TaskCheckFileReqBuilder {
	builder := &TaskCheckFileReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 文件相关异步任务id
//
// 示例值：12345
func (builder *TaskCheckFileReqBuilder) TaskId(taskId string) *TaskCheckFileReqBuilder {
	builder.apiReq.QueryParams.Set("task_id", fmt.Sprint(taskId))
	return builder
}

func (builder *TaskCheckFileReqBuilder) Build() *TaskCheckFileReq {
	req := &TaskCheckFileReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type TaskCheckFileReq struct {
	apiReq *larkcore.ApiReq
}

type TaskCheckFileRespData struct {
	Status *string `json:"status,omitempty"` // 异步任务的执行状态，如果任务执行成功则返回success，如果任务执行失败则返回fail，如果任务还在执行中则返回process。
}

type TaskCheckFileResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *TaskCheckFileRespData `json:"data"` // 业务数据
}

func (resp *TaskCheckFileResp) Success() bool {
	return resp.Code == 0
}

type UploadAllFileReqBodyBuilder struct {
	fileName       string // 文件名。
	fileNameFlag   bool
	parentType     string // 上传点类型。
	parentTypeFlag bool
	parentNode     string // 文件夹token，;获取方式见 [概述](https://open.feishu.cn/document/ukTMukTMukTM/uUDN04SN0QjL1QDN/files/guide/introduction)
	parentNodeFlag bool
	size           int // 文件大小（以字节为单位）。
	sizeFlag       bool
	checksum       string // 文件adler32校验和(可选)。
	checksumFlag   bool
	file           io.Reader // 文件二进制内容。
	fileFlag       bool
}

func NewUploadAllFileReqBodyBuilder() *UploadAllFileReqBodyBuilder {
	builder := &UploadAllFileReqBodyBuilder{}
	return builder
}

// 文件名。
//
//示例值：demo.pdf
func (builder *UploadAllFileReqBodyBuilder) FileName(fileName string) *UploadAllFileReqBodyBuilder {
	builder.fileName = fileName
	builder.fileNameFlag = true
	return builder
}

// 上传点类型。
//
//示例值：explorer
func (builder *UploadAllFileReqBodyBuilder) ParentType(parentType string) *UploadAllFileReqBodyBuilder {
	builder.parentType = parentType
	builder.parentTypeFlag = true
	return builder
}

// 文件夹token，;获取方式见 [概述](https://open.feishu.cn/document/ukTMukTMukTM/uUDN04SN0QjL1QDN/files/guide/introduction)
//
//示例值：fldbcO1UuPz8VwnpPx5a92abcef
func (builder *UploadAllFileReqBodyBuilder) ParentNode(parentNode string) *UploadAllFileReqBodyBuilder {
	builder.parentNode = parentNode
	builder.parentNodeFlag = true
	return builder
}

// 文件大小（以字节为单位）。
//
//示例值：1024
func (builder *UploadAllFileReqBodyBuilder) Size(size int) *UploadAllFileReqBodyBuilder {
	builder.size = size
	builder.sizeFlag = true
	return builder
}

// 文件adler32校验和(可选)。
//
//示例值：123423882374238912356
func (builder *UploadAllFileReqBodyBuilder) Checksum(checksum string) *UploadAllFileReqBodyBuilder {
	builder.checksum = checksum
	builder.checksumFlag = true
	return builder
}

// 文件二进制内容。
//
//示例值：file binary
func (builder *UploadAllFileReqBodyBuilder) File(file io.Reader) *UploadAllFileReqBodyBuilder {
	builder.file = file
	builder.fileFlag = true
	return builder
}

func (builder *UploadAllFileReqBodyBuilder) Build() *UploadAllFileReqBody {
	req := &UploadAllFileReqBody{}
	if builder.fileNameFlag {
		req.FileName = &builder.fileName
	}
	if builder.parentTypeFlag {
		req.ParentType = &builder.parentType
	}
	if builder.parentNodeFlag {
		req.ParentNode = &builder.parentNode
	}
	if builder.sizeFlag {
		req.Size = &builder.size
	}
	if builder.checksumFlag {
		req.Checksum = &builder.checksum
	}
	if builder.fileFlag {
		req.File = builder.file
	}
	return req
}

type UploadAllFilePathReqBodyBuilder struct {
	fileName       string // 文件名。
	fileNameFlag   bool
	parentType     string // 上传点类型。
	parentTypeFlag bool
	parentNode     string // 文件夹token，;获取方式见 [概述](https://open.feishu.cn/document/ukTMukTMukTM/uUDN04SN0QjL1QDN/files/guide/introduction)
	parentNodeFlag bool
	size           int // 文件大小（以字节为单位）。
	sizeFlag       bool
	checksum       string // 文件adler32校验和(可选)。
	checksumFlag   bool
	filePath       string // 文件二进制内容。
	filePathFlag   bool
}

func NewUploadAllFilePathReqBodyBuilder() *UploadAllFilePathReqBodyBuilder {
	builder := &UploadAllFilePathReqBodyBuilder{}
	return builder
}

// 文件名。
//
// 示例值：demo.pdf
func (builder *UploadAllFilePathReqBodyBuilder) FileName(fileName string) *UploadAllFilePathReqBodyBuilder {
	builder.fileName = fileName
	builder.fileNameFlag = true
	return builder
}

// 上传点类型。
//
// 示例值：explorer
func (builder *UploadAllFilePathReqBodyBuilder) ParentType(parentType string) *UploadAllFilePathReqBodyBuilder {
	builder.parentType = parentType
	builder.parentTypeFlag = true
	return builder
}

// 文件夹token，;获取方式见 [概述](https://open.feishu.cn/document/ukTMukTMukTM/uUDN04SN0QjL1QDN/files/guide/introduction)
//
// 示例值：fldbcO1UuPz8VwnpPx5a92abcef
func (builder *UploadAllFilePathReqBodyBuilder) ParentNode(parentNode string) *UploadAllFilePathReqBodyBuilder {
	builder.parentNode = parentNode
	builder.parentNodeFlag = true
	return builder
}

// 文件大小（以字节为单位）。
//
// 示例值：1024
func (builder *UploadAllFilePathReqBodyBuilder) Size(size int) *UploadAllFilePathReqBodyBuilder {
	builder.size = size
	builder.sizeFlag = true
	return builder
}

// 文件adler32校验和(可选)。
//
// 示例值：123423882374238912356
func (builder *UploadAllFilePathReqBodyBuilder) Checksum(checksum string) *UploadAllFilePathReqBodyBuilder {
	builder.checksum = checksum
	builder.checksumFlag = true
	return builder
}

// 文件二进制内容。
//
// 示例值：file binary
func (builder *UploadAllFilePathReqBodyBuilder) FilePath(filePath string) *UploadAllFilePathReqBodyBuilder {
	builder.filePath = filePath
	builder.filePathFlag = true
	return builder
}

func (builder *UploadAllFilePathReqBodyBuilder) Build() (*UploadAllFileReqBody, error) {
	req := &UploadAllFileReqBody{}
	if builder.fileNameFlag {
		req.FileName = &builder.fileName
	}
	if builder.parentTypeFlag {
		req.ParentType = &builder.parentType
	}
	if builder.parentNodeFlag {
		req.ParentNode = &builder.parentNode
	}
	if builder.sizeFlag {
		req.Size = &builder.size
	}
	if builder.checksumFlag {
		req.Checksum = &builder.checksum
	}
	if builder.filePathFlag {
		data, err := larkcore.File2Bytes(builder.filePath)
		if err != nil {
			return nil, err
		}
		req.File = bytes.NewBuffer(data)
	}
	return req, nil
}

type UploadAllFileReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *UploadAllFileReqBody
}

func NewUploadAllFileReqBuilder() *UploadAllFileReqBuilder {
	builder := &UploadAllFileReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 向云空间指定目录下上传一个小文件。
func (builder *UploadAllFileReqBuilder) Body(body *UploadAllFileReqBody) *UploadAllFileReqBuilder {
	builder.body = body
	return builder
}

func (builder *UploadAllFileReqBuilder) Build() *UploadAllFileReq {
	req := &UploadAllFileReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.Body = builder.body
	return req
}

type UploadAllFileReqBody struct {
	FileName   *string   `json:"file_name,omitempty"`   // 文件名。
	ParentType *string   `json:"parent_type,omitempty"` // 上传点类型。
	ParentNode *string   `json:"parent_node,omitempty"` // 文件夹token，;获取方式见 [概述](https://open.feishu.cn/document/ukTMukTMukTM/uUDN04SN0QjL1QDN/files/guide/introduction)
	Size       *int      `json:"size,omitempty"`        // 文件大小（以字节为单位）。
	Checksum   *string   `json:"checksum,omitempty"`    // 文件adler32校验和(可选)。
	File       io.Reader `json:"file,omitempty"`        // 文件二进制内容。
}

type UploadAllFileReq struct {
	apiReq *larkcore.ApiReq
	Body   *UploadAllFileReqBody `body:""`
}

type UploadAllFileRespData struct {
	FileToken *string `json:"file_token,omitempty"` // 新创建文件的 token
}

type UploadAllFileResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *UploadAllFileRespData `json:"data"` // 业务数据
}

func (resp *UploadAllFileResp) Success() bool {
	return resp.Code == 0
}

type UploadFinishFileReqBodyBuilder struct {
	uploadId     string // 分片上传事务ID
	uploadIdFlag bool
	blockNum     int // 分片数量
	blockNumFlag bool
}

func NewUploadFinishFileReqBodyBuilder() *UploadFinishFileReqBodyBuilder {
	builder := &UploadFinishFileReqBodyBuilder{}
	return builder
}

// 分片上传事务ID
//
//示例值：7111211691345512356
func (builder *UploadFinishFileReqBodyBuilder) UploadId(uploadId string) *UploadFinishFileReqBodyBuilder {
	builder.uploadId = uploadId
	builder.uploadIdFlag = true
	return builder
}

// 分片数量
//
//示例值：1
func (builder *UploadFinishFileReqBodyBuilder) BlockNum(blockNum int) *UploadFinishFileReqBodyBuilder {
	builder.blockNum = blockNum
	builder.blockNumFlag = true
	return builder
}

func (builder *UploadFinishFileReqBodyBuilder) Build() *UploadFinishFileReqBody {
	req := &UploadFinishFileReqBody{}
	if builder.uploadIdFlag {
		req.UploadId = &builder.uploadId
	}
	if builder.blockNumFlag {
		req.BlockNum = &builder.blockNum
	}
	return req
}

type UploadFinishFilePathReqBodyBuilder struct {
	uploadId     string // 分片上传事务ID
	uploadIdFlag bool
	blockNum     int // 分片数量
	blockNumFlag bool
}

func NewUploadFinishFilePathReqBodyBuilder() *UploadFinishFilePathReqBodyBuilder {
	builder := &UploadFinishFilePathReqBodyBuilder{}
	return builder
}

// 分片上传事务ID
//
// 示例值：7111211691345512356
func (builder *UploadFinishFilePathReqBodyBuilder) UploadId(uploadId string) *UploadFinishFilePathReqBodyBuilder {
	builder.uploadId = uploadId
	builder.uploadIdFlag = true
	return builder
}

// 分片数量
//
// 示例值：1
func (builder *UploadFinishFilePathReqBodyBuilder) BlockNum(blockNum int) *UploadFinishFilePathReqBodyBuilder {
	builder.blockNum = blockNum
	builder.blockNumFlag = true
	return builder
}

func (builder *UploadFinishFilePathReqBodyBuilder) Build() (*UploadFinishFileReqBody, error) {
	req := &UploadFinishFileReqBody{}
	if builder.uploadIdFlag {
		req.UploadId = &builder.uploadId
	}
	if builder.blockNumFlag {
		req.BlockNum = &builder.blockNum
	}
	return req, nil
}

type UploadFinishFileReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *UploadFinishFileReqBody
}

func NewUploadFinishFileReqBuilder() *UploadFinishFileReqBuilder {
	builder := &UploadFinishFileReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 触发完成上传。
func (builder *UploadFinishFileReqBuilder) Body(body *UploadFinishFileReqBody) *UploadFinishFileReqBuilder {
	builder.body = body
	return builder
}

func (builder *UploadFinishFileReqBuilder) Build() *UploadFinishFileReq {
	req := &UploadFinishFileReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.Body = builder.body
	return req
}

type UploadFinishFileReqBody struct {
	UploadId *string `json:"upload_id,omitempty"` // 分片上传事务ID
	BlockNum *int    `json:"block_num,omitempty"` // 分片数量
}

type UploadFinishFileReq struct {
	apiReq *larkcore.ApiReq
	Body   *UploadFinishFileReqBody `body:""`
}

type UploadFinishFileRespData struct {
	FileToken *string `json:"file_token,omitempty"` // 新创建的文件token
}

type UploadFinishFileResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *UploadFinishFileRespData `json:"data"` // 业务数据
}

func (resp *UploadFinishFileResp) Success() bool {
	return resp.Code == 0
}

type UploadPartFileReqBodyBuilder struct {
	uploadId     string // 分片上传事务ID。
	uploadIdFlag bool
	seq          int // 块号，从0开始计数。
	seqFlag      bool
	size         int // 块大小（以字节为单位）。
	sizeFlag     bool
	checksum     string // 文件分块adler32校验和(可选)。
	checksumFlag bool
	file         io.Reader // 文件分片二进制内容。
	fileFlag     bool
}

func NewUploadPartFileReqBodyBuilder() *UploadPartFileReqBodyBuilder {
	builder := &UploadPartFileReqBodyBuilder{}
	return builder
}

// 分片上传事务ID。
//
//示例值：7111211691345512356
func (builder *UploadPartFileReqBodyBuilder) UploadId(uploadId string) *UploadPartFileReqBodyBuilder {
	builder.uploadId = uploadId
	builder.uploadIdFlag = true
	return builder
}

// 块号，从0开始计数。
//
//示例值：0
func (builder *UploadPartFileReqBodyBuilder) Seq(seq int) *UploadPartFileReqBodyBuilder {
	builder.seq = seq
	builder.seqFlag = true
	return builder
}

// 块大小（以字节为单位）。
//
//示例值：4194304
func (builder *UploadPartFileReqBodyBuilder) Size(size int) *UploadPartFileReqBodyBuilder {
	builder.size = size
	builder.sizeFlag = true
	return builder
}

// 文件分块adler32校验和(可选)。
//
//示例值：12342388237783212356
func (builder *UploadPartFileReqBodyBuilder) Checksum(checksum string) *UploadPartFileReqBodyBuilder {
	builder.checksum = checksum
	builder.checksumFlag = true
	return builder
}

// 文件分片二进制内容。
//
//示例值：file binary
func (builder *UploadPartFileReqBodyBuilder) File(file io.Reader) *UploadPartFileReqBodyBuilder {
	builder.file = file
	builder.fileFlag = true
	return builder
}

func (builder *UploadPartFileReqBodyBuilder) Build() *UploadPartFileReqBody {
	req := &UploadPartFileReqBody{}
	if builder.uploadIdFlag {
		req.UploadId = &builder.uploadId
	}
	if builder.seqFlag {
		req.Seq = &builder.seq
	}
	if builder.sizeFlag {
		req.Size = &builder.size
	}
	if builder.checksumFlag {
		req.Checksum = &builder.checksum
	}
	if builder.fileFlag {
		req.File = builder.file
	}
	return req
}

type UploadPartFilePathReqBodyBuilder struct {
	uploadId     string // 分片上传事务ID。
	uploadIdFlag bool
	seq          int // 块号，从0开始计数。
	seqFlag      bool
	size         int // 块大小（以字节为单位）。
	sizeFlag     bool
	checksum     string // 文件分块adler32校验和(可选)。
	checksumFlag bool
	filePath     string // 文件分片二进制内容。
	filePathFlag bool
}

func NewUploadPartFilePathReqBodyBuilder() *UploadPartFilePathReqBodyBuilder {
	builder := &UploadPartFilePathReqBodyBuilder{}
	return builder
}

// 分片上传事务ID。
//
// 示例值：7111211691345512356
func (builder *UploadPartFilePathReqBodyBuilder) UploadId(uploadId string) *UploadPartFilePathReqBodyBuilder {
	builder.uploadId = uploadId
	builder.uploadIdFlag = true
	return builder
}

// 块号，从0开始计数。
//
// 示例值：0
func (builder *UploadPartFilePathReqBodyBuilder) Seq(seq int) *UploadPartFilePathReqBodyBuilder {
	builder.seq = seq
	builder.seqFlag = true
	return builder
}

// 块大小（以字节为单位）。
//
// 示例值：4194304
func (builder *UploadPartFilePathReqBodyBuilder) Size(size int) *UploadPartFilePathReqBodyBuilder {
	builder.size = size
	builder.sizeFlag = true
	return builder
}

// 文件分块adler32校验和(可选)。
//
// 示例值：12342388237783212356
func (builder *UploadPartFilePathReqBodyBuilder) Checksum(checksum string) *UploadPartFilePathReqBodyBuilder {
	builder.checksum = checksum
	builder.checksumFlag = true
	return builder
}

// 文件分片二进制内容。
//
// 示例值：file binary
func (builder *UploadPartFilePathReqBodyBuilder) FilePath(filePath string) *UploadPartFilePathReqBodyBuilder {
	builder.filePath = filePath
	builder.filePathFlag = true
	return builder
}

func (builder *UploadPartFilePathReqBodyBuilder) Build() (*UploadPartFileReqBody, error) {
	req := &UploadPartFileReqBody{}
	if builder.uploadIdFlag {
		req.UploadId = &builder.uploadId
	}
	if builder.seqFlag {
		req.Seq = &builder.seq
	}
	if builder.sizeFlag {
		req.Size = &builder.size
	}
	if builder.checksumFlag {
		req.Checksum = &builder.checksum
	}
	if builder.filePathFlag {
		data, err := larkcore.File2Bytes(builder.filePath)
		if err != nil {
			return nil, err
		}
		req.File = bytes.NewBuffer(data)
	}
	return req, nil
}

type UploadPartFileReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *UploadPartFileReqBody
}

func NewUploadPartFileReqBuilder() *UploadPartFileReqBuilder {
	builder := &UploadPartFileReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 上传对应的文件块。
func (builder *UploadPartFileReqBuilder) Body(body *UploadPartFileReqBody) *UploadPartFileReqBuilder {
	builder.body = body
	return builder
}

func (builder *UploadPartFileReqBuilder) Build() *UploadPartFileReq {
	req := &UploadPartFileReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.Body = builder.body
	return req
}

type UploadPartFileReqBody struct {
	UploadId *string   `json:"upload_id,omitempty"` // 分片上传事务ID。
	Seq      *int      `json:"seq,omitempty"`       // 块号，从0开始计数。
	Size     *int      `json:"size,omitempty"`      // 块大小（以字节为单位）。
	Checksum *string   `json:"checksum,omitempty"`  // 文件分块adler32校验和(可选)。
	File     io.Reader `json:"file,omitempty"`      // 文件分片二进制内容。
}

type UploadPartFileReq struct {
	apiReq *larkcore.ApiReq
	Body   *UploadPartFileReqBody `body:""`
}

type UploadPartFileResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *UploadPartFileResp) Success() bool {
	return resp.Code == 0
}

type UploadPrepareFileReqBuilder struct {
	apiReq         *larkcore.ApiReq
	fileUploadInfo *FileUploadInfo
}

func NewUploadPrepareFileReqBuilder() *UploadPrepareFileReqBuilder {
	builder := &UploadPrepareFileReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 发送初始化请求获取上传事务ID和分块策略，目前是以4MB大小进行定长分片。
func (builder *UploadPrepareFileReqBuilder) FileUploadInfo(fileUploadInfo *FileUploadInfo) *UploadPrepareFileReqBuilder {
	builder.fileUploadInfo = fileUploadInfo
	return builder
}

func (builder *UploadPrepareFileReqBuilder) Build() *UploadPrepareFileReq {
	req := &UploadPrepareFileReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.Body = builder.fileUploadInfo
	return req
}

type UploadPrepareFileReq struct {
	apiReq         *larkcore.ApiReq
	FileUploadInfo *FileUploadInfo `body:""`
}

type UploadPrepareFileRespData struct {
	UploadId  *string `json:"upload_id,omitempty"`  // 分片上传事务ID
	BlockSize *int    `json:"block_size,omitempty"` // 分片大小策略
	BlockNum  *int    `json:"block_num,omitempty"`  // 分片数量
}

type UploadPrepareFileResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *UploadPrepareFileRespData `json:"data"` // 业务数据
}

func (resp *UploadPrepareFileResp) Success() bool {
	return resp.Code == 0
}

type CreateFileCommentReqBuilder struct {
	apiReq      *larkcore.ApiReq
	fileComment *FileComment
}

func NewCreateFileCommentReqBuilder() *CreateFileCommentReqBuilder {
	builder := &CreateFileCommentReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 文档token
//
// 示例值：doccnGp4UK1UskrOEJwBXd3****
func (builder *CreateFileCommentReqBuilder) FileToken(fileToken string) *CreateFileCommentReqBuilder {
	builder.apiReq.PathParams.Set("file_token", fmt.Sprint(fileToken))
	return builder
}

// 文档类型
//
// 示例值：doc
func (builder *CreateFileCommentReqBuilder) FileType(fileType string) *CreateFileCommentReqBuilder {
	builder.apiReq.QueryParams.Set("file_type", fmt.Sprint(fileType))
	return builder
}

// 此次调用中使用的用户ID的类型
//
// 示例值：
func (builder *CreateFileCommentReqBuilder) UserIdType(userIdType string) *CreateFileCommentReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

// 往云文档添加一条全局评论。
func (builder *CreateFileCommentReqBuilder) FileComment(fileComment *FileComment) *CreateFileCommentReqBuilder {
	builder.fileComment = fileComment
	return builder
}

func (builder *CreateFileCommentReqBuilder) Build() *CreateFileCommentReq {
	req := &CreateFileCommentReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.fileComment
	return req
}

type CreateFileCommentReq struct {
	apiReq      *larkcore.ApiReq
	FileComment *FileComment `body:""`
}

type CreateFileCommentRespData struct {
	CommentId    *string    `json:"comment_id,omitempty"`     // 评论ID
	UserId       *string    `json:"user_id,omitempty"`        // 用户ID
	CreateTime   *int       `json:"create_time,omitempty"`    // 创建时间
	UpdateTime   *int       `json:"update_time,omitempty"`    // 更新时间
	IsSolved     *bool      `json:"is_solved,omitempty"`      // 是否已解决
	SolvedTime   *int       `json:"solved_time,omitempty"`    // 解决评论时间
	SolverUserId *string    `json:"solver_user_id,omitempty"` // 解决评论者的用户ID
	HasMore      *bool      `json:"has_more,omitempty"`       // 是否有更多回复
	PageToken    *string    `json:"page_token,omitempty"`     // 回复分页标记
	IsWhole      *bool      `json:"is_whole,omitempty"`       // 是否是全文评论
	Quote        *string    `json:"quote,omitempty"`          // 如果是局部评论，引用字段
	ReplyList    *ReplyList `json:"reply_list,omitempty"`     // 评论里的回复列表
}

type CreateFileCommentResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *CreateFileCommentRespData `json:"data"` // 业务数据
}

func (resp *CreateFileCommentResp) Success() bool {
	return resp.Code == 0
}

type GetFileCommentReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewGetFileCommentReqBuilder() *GetFileCommentReqBuilder {
	builder := &GetFileCommentReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 文档token
//
// 示例值：doccnHh7U87HOFpii5u5G*****
func (builder *GetFileCommentReqBuilder) FileToken(fileToken string) *GetFileCommentReqBuilder {
	builder.apiReq.PathParams.Set("file_token", fmt.Sprint(fileToken))
	return builder
}

// 评论ID
//
// 示例值：6916106822734578184
func (builder *GetFileCommentReqBuilder) CommentId(commentId string) *GetFileCommentReqBuilder {
	builder.apiReq.PathParams.Set("comment_id", fmt.Sprint(commentId))
	return builder
}

// 文档类型
//
// 示例值：doc
func (builder *GetFileCommentReqBuilder) FileType(fileType string) *GetFileCommentReqBuilder {
	builder.apiReq.QueryParams.Set("file_type", fmt.Sprint(fileType))
	return builder
}

// 此次调用中使用的用户ID的类型
//
// 示例值：
func (builder *GetFileCommentReqBuilder) UserIdType(userIdType string) *GetFileCommentReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

func (builder *GetFileCommentReqBuilder) Build() *GetFileCommentReq {
	req := &GetFileCommentReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type GetFileCommentReq struct {
	apiReq *larkcore.ApiReq
}

type GetFileCommentRespData struct {
	CommentId    *string    `json:"comment_id,omitempty"`     // 评论ID
	UserId       *string    `json:"user_id,omitempty"`        // 用户ID
	CreateTime   *int       `json:"create_time,omitempty"`    // 创建时间
	UpdateTime   *int       `json:"update_time,omitempty"`    // 更新时间
	IsSolved     *bool      `json:"is_solved,omitempty"`      // 是否已解决
	SolvedTime   *int       `json:"solved_time,omitempty"`    // 解决评论时间
	SolverUserId *string    `json:"solver_user_id,omitempty"` // 解决评论者的用户ID
	HasMore      *bool      `json:"has_more,omitempty"`       // 是否有更多回复
	PageToken    *string    `json:"page_token,omitempty"`     // 回复分页标记
	IsWhole      *bool      `json:"is_whole,omitempty"`       // 是否是全文评论
	Quote        *string    `json:"quote,omitempty"`          // 如果是局部评论，引用字段
	ReplyList    *ReplyList `json:"reply_list,omitempty"`     // 评论里的回复列表
}

type GetFileCommentResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *GetFileCommentRespData `json:"data"` // 业务数据
}

func (resp *GetFileCommentResp) Success() bool {
	return resp.Code == 0
}

type ListFileCommentReqBuilder struct {
	apiReq *larkcore.ApiReq
	limit  int // 最大返回多少记录，当使用迭代器访问时才有效
}

func NewListFileCommentReqBuilder() *ListFileCommentReqBuilder {
	builder := &ListFileCommentReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 最大返回多少记录，当使用迭代器访问时才有效
func (builder *ListFileCommentReqBuilder) Limit(limit int) *ListFileCommentReqBuilder {
	builder.limit = limit
	return builder
}

// 文档token
//
// 示例值：XIHSdYSI7oMEU1xrsnxc8fabcef
func (builder *ListFileCommentReqBuilder) FileToken(fileToken string) *ListFileCommentReqBuilder {
	builder.apiReq.PathParams.Set("file_token", fmt.Sprint(fileToken))
	return builder
}

// 文档类型
//
// 示例值：doc
func (builder *ListFileCommentReqBuilder) FileType(fileType string) *ListFileCommentReqBuilder {
	builder.apiReq.QueryParams.Set("file_type", fmt.Sprint(fileType))
	return builder
}

// 是否全文评论
//
// 示例值：false
func (builder *ListFileCommentReqBuilder) IsWhole(isWhole bool) *ListFileCommentReqBuilder {
	builder.apiReq.QueryParams.Set("is_whole", fmt.Sprint(isWhole))
	return builder
}

// 是否已解决（可选）
//
// 示例值：false
func (builder *ListFileCommentReqBuilder) IsSolved(isSolved bool) *ListFileCommentReqBuilder {
	builder.apiReq.QueryParams.Set("is_solved", fmt.Sprint(isSolved))
	return builder
}

// 评论分页参数
//
// 示例值：7153511712153412356
func (builder *ListFileCommentReqBuilder) PageToken(pageToken string) *ListFileCommentReqBuilder {
	builder.apiReq.QueryParams.Set("page_token", fmt.Sprint(pageToken))
	return builder
}

// 获取满足 commen_id > page_token 的评论数量
//
// 示例值：10
func (builder *ListFileCommentReqBuilder) PageSize(pageSize string) *ListFileCommentReqBuilder {
	builder.apiReq.QueryParams.Set("page_size", fmt.Sprint(pageSize))
	return builder
}

// 此次调用中使用的用户ID的类型
//
// 示例值：user_id
func (builder *ListFileCommentReqBuilder) UserIdType(userIdType string) *ListFileCommentReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

func (builder *ListFileCommentReqBuilder) Build() *ListFileCommentReq {
	req := &ListFileCommentReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.Limit = builder.limit
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type ListFileCommentReq struct {
	apiReq *larkcore.ApiReq
	Limit  int // 最多返回多少记录，只有在使用迭代器访问时，才有效

}

type ListFileCommentRespData struct {
	HasMore   *bool          `json:"has_more,omitempty"`   // 是否有下一页数据
	PageToken *string        `json:"page_token,omitempty"` // 下一页分页的token
	Items     []*FileComment `json:"items,omitempty"`      // 评论列表
}

type ListFileCommentResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *ListFileCommentRespData `json:"data"` // 业务数据
}

func (resp *ListFileCommentResp) Success() bool {
	return resp.Code == 0
}

type PatchFileCommentReqBodyBuilder struct {
	isSolved     bool // 评论解决标志
	isSolvedFlag bool
}

func NewPatchFileCommentReqBodyBuilder() *PatchFileCommentReqBodyBuilder {
	builder := &PatchFileCommentReqBodyBuilder{}
	return builder
}

// 评论解决标志
//
//示例值：true
func (builder *PatchFileCommentReqBodyBuilder) IsSolved(isSolved bool) *PatchFileCommentReqBodyBuilder {
	builder.isSolved = isSolved
	builder.isSolvedFlag = true
	return builder
}

func (builder *PatchFileCommentReqBodyBuilder) Build() *PatchFileCommentReqBody {
	req := &PatchFileCommentReqBody{}
	if builder.isSolvedFlag {
		req.IsSolved = &builder.isSolved
	}
	return req
}

type PatchFileCommentPathReqBodyBuilder struct {
	isSolved     bool // 评论解决标志
	isSolvedFlag bool
}

func NewPatchFileCommentPathReqBodyBuilder() *PatchFileCommentPathReqBodyBuilder {
	builder := &PatchFileCommentPathReqBodyBuilder{}
	return builder
}

// 评论解决标志
//
// 示例值：true
func (builder *PatchFileCommentPathReqBodyBuilder) IsSolved(isSolved bool) *PatchFileCommentPathReqBodyBuilder {
	builder.isSolved = isSolved
	builder.isSolvedFlag = true
	return builder
}

func (builder *PatchFileCommentPathReqBodyBuilder) Build() (*PatchFileCommentReqBody, error) {
	req := &PatchFileCommentReqBody{}
	if builder.isSolvedFlag {
		req.IsSolved = &builder.isSolved
	}
	return req, nil
}

type PatchFileCommentReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *PatchFileCommentReqBody
}

func NewPatchFileCommentReqBuilder() *PatchFileCommentReqBuilder {
	builder := &PatchFileCommentReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 文档token
//
// 示例值：doccnGp4UK1UskrOEJwBXd3****
func (builder *PatchFileCommentReqBuilder) FileToken(fileToken string) *PatchFileCommentReqBuilder {
	builder.apiReq.PathParams.Set("file_token", fmt.Sprint(fileToken))
	return builder
}

// 评论ID
//
// 示例值：6916106822734578184
func (builder *PatchFileCommentReqBuilder) CommentId(commentId string) *PatchFileCommentReqBuilder {
	builder.apiReq.PathParams.Set("comment_id", fmt.Sprint(commentId))
	return builder
}

// 文档类型
//
// 示例值：doc
func (builder *PatchFileCommentReqBuilder) FileType(fileType string) *PatchFileCommentReqBuilder {
	builder.apiReq.QueryParams.Set("file_type", fmt.Sprint(fileType))
	return builder
}

// 解决或恢复云文档中的评论。
func (builder *PatchFileCommentReqBuilder) Body(body *PatchFileCommentReqBody) *PatchFileCommentReqBuilder {
	builder.body = body
	return builder
}

func (builder *PatchFileCommentReqBuilder) Build() *PatchFileCommentReq {
	req := &PatchFileCommentReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.body
	return req
}

type PatchFileCommentReqBody struct {
	IsSolved *bool `json:"is_solved,omitempty"` // 评论解决标志
}

type PatchFileCommentReq struct {
	apiReq *larkcore.ApiReq
	Body   *PatchFileCommentReqBody `body:""`
}

type PatchFileCommentResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *PatchFileCommentResp) Success() bool {
	return resp.Code == 0
}

type DeleteFileCommentReplyReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewDeleteFileCommentReplyReqBuilder() *DeleteFileCommentReplyReqBuilder {
	builder := &DeleteFileCommentReplyReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 文档token
//
// 示例值：doccnHh7U87HOFpii5u5G*****
func (builder *DeleteFileCommentReplyReqBuilder) FileToken(fileToken string) *DeleteFileCommentReplyReqBuilder {
	builder.apiReq.PathParams.Set("file_token", fmt.Sprint(fileToken))
	return builder
}

// 评论ID
//
// 示例值：6916106822734578184
func (builder *DeleteFileCommentReplyReqBuilder) CommentId(commentId string) *DeleteFileCommentReplyReqBuilder {
	builder.apiReq.PathParams.Set("comment_id", fmt.Sprint(commentId))
	return builder
}

// 回复ID
//
// 示例值：6916106822734594568
func (builder *DeleteFileCommentReplyReqBuilder) ReplyId(replyId string) *DeleteFileCommentReplyReqBuilder {
	builder.apiReq.PathParams.Set("reply_id", fmt.Sprint(replyId))
	return builder
}

// 文档类型
//
// 示例值：doc
func (builder *DeleteFileCommentReplyReqBuilder) FileType(fileType string) *DeleteFileCommentReplyReqBuilder {
	builder.apiReq.QueryParams.Set("file_type", fmt.Sprint(fileType))
	return builder
}

func (builder *DeleteFileCommentReplyReqBuilder) Build() *DeleteFileCommentReplyReq {
	req := &DeleteFileCommentReplyReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type DeleteFileCommentReplyReq struct {
	apiReq *larkcore.ApiReq
}

type DeleteFileCommentReplyResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *DeleteFileCommentReplyResp) Success() bool {
	return resp.Code == 0
}

type UpdateFileCommentReplyReqBodyBuilder struct {
	content     *ReplyContent // 回复内容
	contentFlag bool
}

func NewUpdateFileCommentReplyReqBodyBuilder() *UpdateFileCommentReplyReqBodyBuilder {
	builder := &UpdateFileCommentReplyReqBodyBuilder{}
	return builder
}

// 回复内容
//
//示例值：
func (builder *UpdateFileCommentReplyReqBodyBuilder) Content(content *ReplyContent) *UpdateFileCommentReplyReqBodyBuilder {
	builder.content = content
	builder.contentFlag = true
	return builder
}

func (builder *UpdateFileCommentReplyReqBodyBuilder) Build() *UpdateFileCommentReplyReqBody {
	req := &UpdateFileCommentReplyReqBody{}
	if builder.contentFlag {
		req.Content = builder.content
	}
	return req
}

type UpdateFileCommentReplyPathReqBodyBuilder struct {
	content     *ReplyContent // 回复内容
	contentFlag bool
}

func NewUpdateFileCommentReplyPathReqBodyBuilder() *UpdateFileCommentReplyPathReqBodyBuilder {
	builder := &UpdateFileCommentReplyPathReqBodyBuilder{}
	return builder
}

// 回复内容
//
// 示例值：
func (builder *UpdateFileCommentReplyPathReqBodyBuilder) Content(content *ReplyContent) *UpdateFileCommentReplyPathReqBodyBuilder {
	builder.content = content
	builder.contentFlag = true
	return builder
}

func (builder *UpdateFileCommentReplyPathReqBodyBuilder) Build() (*UpdateFileCommentReplyReqBody, error) {
	req := &UpdateFileCommentReplyReqBody{}
	if builder.contentFlag {
		req.Content = builder.content
	}
	return req, nil
}

type UpdateFileCommentReplyReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *UpdateFileCommentReplyReqBody
}

func NewUpdateFileCommentReplyReqBuilder() *UpdateFileCommentReplyReqBuilder {
	builder := &UpdateFileCommentReplyReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 文档token
//
// 示例值：doccnHh7U87HOFpii5u5G*****
func (builder *UpdateFileCommentReplyReqBuilder) FileToken(fileToken string) *UpdateFileCommentReplyReqBuilder {
	builder.apiReq.PathParams.Set("file_token", fmt.Sprint(fileToken))
	return builder
}

// 评论ID
//
// 示例值：6916106822734578184
func (builder *UpdateFileCommentReplyReqBuilder) CommentId(commentId string) *UpdateFileCommentReplyReqBuilder {
	builder.apiReq.PathParams.Set("comment_id", fmt.Sprint(commentId))
	return builder
}

// 回复ID
//
// 示例值：6916106822734594568
func (builder *UpdateFileCommentReplyReqBuilder) ReplyId(replyId string) *UpdateFileCommentReplyReqBuilder {
	builder.apiReq.PathParams.Set("reply_id", fmt.Sprint(replyId))
	return builder
}

// 文档类型
//
// 示例值：doc
func (builder *UpdateFileCommentReplyReqBuilder) FileType(fileType string) *UpdateFileCommentReplyReqBuilder {
	builder.apiReq.QueryParams.Set("file_type", fmt.Sprint(fileType))
	return builder
}

// 此次调用中使用的用户ID的类型
//
// 示例值：user_id
func (builder *UpdateFileCommentReplyReqBuilder) UserIdType(userIdType string) *UpdateFileCommentReplyReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

// 更新云文档中的某条回复。
func (builder *UpdateFileCommentReplyReqBuilder) Body(body *UpdateFileCommentReplyReqBody) *UpdateFileCommentReplyReqBuilder {
	builder.body = body
	return builder
}

func (builder *UpdateFileCommentReplyReqBuilder) Build() *UpdateFileCommentReplyReq {
	req := &UpdateFileCommentReplyReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.body
	return req
}

type UpdateFileCommentReplyReqBody struct {
	Content *ReplyContent `json:"content,omitempty"` // 回复内容
}

type UpdateFileCommentReplyReq struct {
	apiReq *larkcore.ApiReq
	Body   *UpdateFileCommentReplyReqBody `body:""`
}

type UpdateFileCommentReplyResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *UpdateFileCommentReplyResp) Success() bool {
	return resp.Code == 0
}

type GetFileStatisticsReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewGetFileStatisticsReqBuilder() *GetFileStatisticsReqBuilder {
	builder := &GetFileStatisticsReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 文件 token
//
// 示例值：doccnfYZzTlvXqZIGTdAHKabcef
func (builder *GetFileStatisticsReqBuilder) FileToken(fileToken string) *GetFileStatisticsReqBuilder {
	builder.apiReq.PathParams.Set("file_token", fmt.Sprint(fileToken))
	return builder
}

// 文档类型
//
// 示例值：doc
func (builder *GetFileStatisticsReqBuilder) FileType(fileType string) *GetFileStatisticsReqBuilder {
	builder.apiReq.QueryParams.Set("file_type", fmt.Sprint(fileType))
	return builder
}

func (builder *GetFileStatisticsReqBuilder) Build() *GetFileStatisticsReq {
	req := &GetFileStatisticsReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type GetFileStatisticsReq struct {
	apiReq *larkcore.ApiReq
}

type GetFileStatisticsRespData struct {
	FileToken  *string         `json:"file_token,omitempty"` // 文件 token
	FileType   *string         `json:"file_type,omitempty"`  // 文件类型
	Statistics *FileStatistics `json:"statistics,omitempty"` // 文件统计信息
}

type GetFileStatisticsResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *GetFileStatisticsRespData `json:"data"` // 业务数据
}

func (resp *GetFileStatisticsResp) Success() bool {
	return resp.Code == 0
}

type CreateFileSubscriptionReqBuilder struct {
	apiReq           *larkcore.ApiReq
	fileSubscription *FileSubscription
}

func NewCreateFileSubscriptionReqBuilder() *CreateFileSubscriptionReqBuilder {
	builder := &CreateFileSubscriptionReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 文档token
//
// 示例值：doxcnxxxxxxxxxxxxxxxxxxxxxx
func (builder *CreateFileSubscriptionReqBuilder) FileToken(fileToken string) *CreateFileSubscriptionReqBuilder {
	builder.apiReq.PathParams.Set("file_token", fmt.Sprint(fileToken))
	return builder
}

// 订阅文档中的变更事件，当前支持文档评论订阅，订阅后文档评论更新会有“云文档助手”推送给订阅的用户
func (builder *CreateFileSubscriptionReqBuilder) FileSubscription(fileSubscription *FileSubscription) *CreateFileSubscriptionReqBuilder {
	builder.fileSubscription = fileSubscription
	return builder
}

func (builder *CreateFileSubscriptionReqBuilder) Build() *CreateFileSubscriptionReq {
	req := &CreateFileSubscriptionReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.Body = builder.fileSubscription
	return req
}

type CreateFileSubscriptionReq struct {
	apiReq           *larkcore.ApiReq
	FileSubscription *FileSubscription `body:""`
}

type CreateFileSubscriptionRespData struct {
	Subscription *FileSubscription `json:"subscription,omitempty"` // 本次增加的文档订阅信息
}

type CreateFileSubscriptionResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *CreateFileSubscriptionRespData `json:"data"` // 业务数据
}

func (resp *CreateFileSubscriptionResp) Success() bool {
	return resp.Code == 0
}

type GetFileSubscriptionReqBuilder struct {
	apiReq           *larkcore.ApiReq
	fileSubscription *FileSubscription
}

func NewGetFileSubscriptionReqBuilder() *GetFileSubscriptionReqBuilder {
	builder := &GetFileSubscriptionReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 文档token
//
// 示例值：doxcnxxxxxxxxxxxxxxxxxxxxxx
func (builder *GetFileSubscriptionReqBuilder) FileToken(fileToken string) *GetFileSubscriptionReqBuilder {
	builder.apiReq.PathParams.Set("file_token", fmt.Sprint(fileToken))
	return builder
}

// 订阅关系ID
//
// 示例值：1234567890987654321
func (builder *GetFileSubscriptionReqBuilder) SubscriptionId(subscriptionId string) *GetFileSubscriptionReqBuilder {
	builder.apiReq.PathParams.Set("subscription_id", fmt.Sprint(subscriptionId))
	return builder
}

// 根据订阅ID获取该订阅的状态
func (builder *GetFileSubscriptionReqBuilder) FileSubscription(fileSubscription *FileSubscription) *GetFileSubscriptionReqBuilder {
	builder.fileSubscription = fileSubscription
	return builder
}

func (builder *GetFileSubscriptionReqBuilder) Build() *GetFileSubscriptionReq {
	req := &GetFileSubscriptionReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.Body = builder.fileSubscription
	return req
}

type GetFileSubscriptionReq struct {
	apiReq           *larkcore.ApiReq
	FileSubscription *FileSubscription `body:""`
}

type GetFileSubscriptionRespData struct {
	Subscription *FileSubscription `json:"subscription,omitempty"` // 文档订阅信息
}

type GetFileSubscriptionResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *GetFileSubscriptionRespData `json:"data"` // 业务数据
}

func (resp *GetFileSubscriptionResp) Success() bool {
	return resp.Code == 0
}

type PatchFileSubscriptionReqBodyBuilder struct {
	isSubscribe     bool // 是否订阅
	isSubscribeFlag bool
	fileType        string // 文档类型
	fileTypeFlag    bool
}

func NewPatchFileSubscriptionReqBodyBuilder() *PatchFileSubscriptionReqBodyBuilder {
	builder := &PatchFileSubscriptionReqBodyBuilder{}
	return builder
}

// 是否订阅
//
//示例值：true
func (builder *PatchFileSubscriptionReqBodyBuilder) IsSubscribe(isSubscribe bool) *PatchFileSubscriptionReqBodyBuilder {
	builder.isSubscribe = isSubscribe
	builder.isSubscribeFlag = true
	return builder
}

// 文档类型
//
//示例值：doc
func (builder *PatchFileSubscriptionReqBodyBuilder) FileType(fileType string) *PatchFileSubscriptionReqBodyBuilder {
	builder.fileType = fileType
	builder.fileTypeFlag = true
	return builder
}

func (builder *PatchFileSubscriptionReqBodyBuilder) Build() *PatchFileSubscriptionReqBody {
	req := &PatchFileSubscriptionReqBody{}
	if builder.isSubscribeFlag {
		req.IsSubscribe = &builder.isSubscribe
	}
	if builder.fileTypeFlag {
		req.FileType = &builder.fileType
	}
	return req
}

type PatchFileSubscriptionPathReqBodyBuilder struct {
	isSubscribe     bool // 是否订阅
	isSubscribeFlag bool
	fileType        string // 文档类型
	fileTypeFlag    bool
}

func NewPatchFileSubscriptionPathReqBodyBuilder() *PatchFileSubscriptionPathReqBodyBuilder {
	builder := &PatchFileSubscriptionPathReqBodyBuilder{}
	return builder
}

// 是否订阅
//
// 示例值：true
func (builder *PatchFileSubscriptionPathReqBodyBuilder) IsSubscribe(isSubscribe bool) *PatchFileSubscriptionPathReqBodyBuilder {
	builder.isSubscribe = isSubscribe
	builder.isSubscribeFlag = true
	return builder
}

// 文档类型
//
// 示例值：doc
func (builder *PatchFileSubscriptionPathReqBodyBuilder) FileType(fileType string) *PatchFileSubscriptionPathReqBodyBuilder {
	builder.fileType = fileType
	builder.fileTypeFlag = true
	return builder
}

func (builder *PatchFileSubscriptionPathReqBodyBuilder) Build() (*PatchFileSubscriptionReqBody, error) {
	req := &PatchFileSubscriptionReqBody{}
	if builder.isSubscribeFlag {
		req.IsSubscribe = &builder.isSubscribe
	}
	if builder.fileTypeFlag {
		req.FileType = &builder.fileType
	}
	return req, nil
}

type PatchFileSubscriptionReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *PatchFileSubscriptionReqBody
}

func NewPatchFileSubscriptionReqBuilder() *PatchFileSubscriptionReqBuilder {
	builder := &PatchFileSubscriptionReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 文档token
//
// 示例值：doxcnxxxxxxxxxxxxxxxxxxxxxx
func (builder *PatchFileSubscriptionReqBuilder) FileToken(fileToken string) *PatchFileSubscriptionReqBuilder {
	builder.apiReq.PathParams.Set("file_token", fmt.Sprint(fileToken))
	return builder
}

// 订阅关系ID
//
// 示例值：1234567890987654321
func (builder *PatchFileSubscriptionReqBuilder) SubscriptionId(subscriptionId string) *PatchFileSubscriptionReqBuilder {
	builder.apiReq.PathParams.Set("subscription_id", fmt.Sprint(subscriptionId))
	return builder
}

// 根据订阅ID更新订阅状态
func (builder *PatchFileSubscriptionReqBuilder) Body(body *PatchFileSubscriptionReqBody) *PatchFileSubscriptionReqBuilder {
	builder.body = body
	return builder
}

func (builder *PatchFileSubscriptionReqBuilder) Build() *PatchFileSubscriptionReq {
	req := &PatchFileSubscriptionReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.Body = builder.body
	return req
}

type PatchFileSubscriptionReqBody struct {
	IsSubscribe *bool   `json:"is_subscribe,omitempty"` // 是否订阅
	FileType    *string `json:"file_type,omitempty"`    // 文档类型
}

type PatchFileSubscriptionReq struct {
	apiReq *larkcore.ApiReq
	Body   *PatchFileSubscriptionReqBody `body:""`
}

type PatchFileSubscriptionRespData struct {
	Subscription *FileSubscription `json:"subscription,omitempty"` // 本次修改的文档订阅信息
}

type PatchFileSubscriptionResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *PatchFileSubscriptionRespData `json:"data"` // 业务数据
}

func (resp *PatchFileSubscriptionResp) Success() bool {
	return resp.Code == 0
}

type CreateImportTaskReqBuilder struct {
	apiReq     *larkcore.ApiReq
	importTask *ImportTask
}

func NewCreateImportTaskReqBuilder() *CreateImportTaskReqBuilder {
	builder := &CreateImportTaskReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 创建导入任务。支持导入为 doc、docx、sheet、bitable，参考[导入用户指南](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/drive-v1/import_task/import-user-guide)
func (builder *CreateImportTaskReqBuilder) ImportTask(importTask *ImportTask) *CreateImportTaskReqBuilder {
	builder.importTask = importTask
	return builder
}

func (builder *CreateImportTaskReqBuilder) Build() *CreateImportTaskReq {
	req := &CreateImportTaskReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.Body = builder.importTask
	return req
}

type CreateImportTaskReq struct {
	apiReq     *larkcore.ApiReq
	ImportTask *ImportTask `body:""`
}

type CreateImportTaskRespData struct {
	Ticket *string `json:"ticket,omitempty"` // 导入任务ID
}

type CreateImportTaskResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *CreateImportTaskRespData `json:"data"` // 业务数据
}

func (resp *CreateImportTaskResp) Success() bool {
	return resp.Code == 0
}

type GetImportTaskReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewGetImportTaskReqBuilder() *GetImportTaskReqBuilder {
	builder := &GetImportTaskReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 导入任务ID
//
// 示例值：6990281865xxxxxxxx7843
func (builder *GetImportTaskReqBuilder) Ticket(ticket string) *GetImportTaskReqBuilder {
	builder.apiReq.PathParams.Set("ticket", fmt.Sprint(ticket))
	return builder
}

func (builder *GetImportTaskReqBuilder) Build() *GetImportTaskReq {
	req := &GetImportTaskReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	return req
}

type GetImportTaskReq struct {
	apiReq *larkcore.ApiReq
}

type GetImportTaskRespData struct {
	Result *ImportTask `json:"result,omitempty"` // 导入结果
}

type GetImportTaskResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *GetImportTaskRespData `json:"data"` // 业务数据
}

func (resp *GetImportTaskResp) Success() bool {
	return resp.Code == 0
}

type BatchGetTmpDownloadUrlMediaReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewBatchGetTmpDownloadUrlMediaReqBuilder() *BatchGetTmpDownloadUrlMediaReqBuilder {
	builder := &BatchGetTmpDownloadUrlMediaReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 文件标识符列表
//
// 示例值：boxcnrHpsg1QDqXAAAyachabcef
func (builder *BatchGetTmpDownloadUrlMediaReqBuilder) FileTokens(fileTokens []string) *BatchGetTmpDownloadUrlMediaReqBuilder {
	for _, v := range fileTokens {
		builder.apiReq.QueryParams.Add("file_tokens", fmt.Sprint(v))
	}
	return builder
}

// 拓展信息(可选)
//
// 示例值：[请参考-上传点类型及对应Extra说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/drive-v1/media/introduction)
func (builder *BatchGetTmpDownloadUrlMediaReqBuilder) Extra(extra string) *BatchGetTmpDownloadUrlMediaReqBuilder {
	builder.apiReq.QueryParams.Set("extra", fmt.Sprint(extra))
	return builder
}

func (builder *BatchGetTmpDownloadUrlMediaReqBuilder) Build() *BatchGetTmpDownloadUrlMediaReq {
	req := &BatchGetTmpDownloadUrlMediaReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type BatchGetTmpDownloadUrlMediaReq struct {
	apiReq *larkcore.ApiReq
}

type BatchGetTmpDownloadUrlMediaRespData struct {
	TmpDownloadUrls []*TmpDownloadUrl `json:"tmp_download_urls,omitempty"` // 临时下载列表
}

type BatchGetTmpDownloadUrlMediaResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *BatchGetTmpDownloadUrlMediaRespData `json:"data"` // 业务数据
}

func (resp *BatchGetTmpDownloadUrlMediaResp) Success() bool {
	return resp.Code == 0
}

type DownloadMediaReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewDownloadMediaReqBuilder() *DownloadMediaReqBuilder {
	builder := &DownloadMediaReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 文件的 token，获取方式见 [概述](https://open.feishu.cn/document/ukTMukTMukTM/uUDN04SN0QjL1QDN/files/guide/introduction)
//
// 示例值：boxcnrHpsg1QDqXAAAyachabcef
func (builder *DownloadMediaReqBuilder) FileToken(fileToken string) *DownloadMediaReqBuilder {
	builder.apiReq.PathParams.Set("file_token", fmt.Sprint(fileToken))
	return builder
}

// 扩展信息
//
// 示例值：[请参考-上传点类型及对应Extra说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/drive-v1/media/introduction)
func (builder *DownloadMediaReqBuilder) Extra(extra string) *DownloadMediaReqBuilder {
	builder.apiReq.QueryParams.Set("extra", fmt.Sprint(extra))
	return builder
}

func (builder *DownloadMediaReqBuilder) Build() *DownloadMediaReq {
	req := &DownloadMediaReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type DownloadMediaReq struct {
	apiReq *larkcore.ApiReq
}

type DownloadMediaResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	File     io.Reader `json:"-"`
	FileName string    `json:"-"`
}

func (resp *DownloadMediaResp) Success() bool {
	return resp.Code == 0
}

func (resp *DownloadMediaResp) WriteFile(fileName string) error {
	bs, err := ioutil.ReadAll(resp.File)
	if err != nil {
		return err
	}

	err = ioutil.WriteFile(fileName, bs, 0666)
	if err != nil {
		return err
	}
	return nil
}

type UploadAllMediaReqBodyBuilder struct {
	fileName       string // 文件名。
	fileNameFlag   bool
	parentType     string // 上传点类型。
	parentTypeFlag bool
	parentNode     string // 上传点的token。
	parentNodeFlag bool
	size           int // 文件大小（以字节为单位）。
	sizeFlag       bool
	checksum       string // 文件adler32校验和（可选）。
	checksumFlag   bool
	extra          string // 扩展信息(可选)。
	extraFlag      bool
	file           io.Reader // 文件二进制内容。
	fileFlag       bool
}

func NewUploadAllMediaReqBodyBuilder() *UploadAllMediaReqBodyBuilder {
	builder := &UploadAllMediaReqBodyBuilder{}
	return builder
}

// 文件名。
//
//示例值：demo.jpeg
func (builder *UploadAllMediaReqBodyBuilder) FileName(fileName string) *UploadAllMediaReqBodyBuilder {
	builder.fileName = fileName
	builder.fileNameFlag = true
	return builder
}

// 上传点类型。
//
//示例值：doc_image
func (builder *UploadAllMediaReqBodyBuilder) ParentType(parentType string) *UploadAllMediaReqBodyBuilder {
	builder.parentType = parentType
	builder.parentTypeFlag = true
	return builder
}

// 上传点的token。
//
//示例值：doccnFivLCfJfblZjGZtxgabcef
func (builder *UploadAllMediaReqBodyBuilder) ParentNode(parentNode string) *UploadAllMediaReqBodyBuilder {
	builder.parentNode = parentNode
	builder.parentNodeFlag = true
	return builder
}

// 文件大小（以字节为单位）。
//
//示例值：1024
func (builder *UploadAllMediaReqBodyBuilder) Size(size int) *UploadAllMediaReqBodyBuilder {
	builder.size = size
	builder.sizeFlag = true
	return builder
}

// 文件adler32校验和（可选）。
//
//示例值：12345678
func (builder *UploadAllMediaReqBodyBuilder) Checksum(checksum string) *UploadAllMediaReqBodyBuilder {
	builder.checksum = checksum
	builder.checksumFlag = true
	return builder
}

// 扩展信息(可选)。
//
//示例值：{"test":"test"}
func (builder *UploadAllMediaReqBodyBuilder) Extra(extra string) *UploadAllMediaReqBodyBuilder {
	builder.extra = extra
	builder.extraFlag = true
	return builder
}

// 文件二进制内容。
//
//示例值：file binary
func (builder *UploadAllMediaReqBodyBuilder) File(file io.Reader) *UploadAllMediaReqBodyBuilder {
	builder.file = file
	builder.fileFlag = true
	return builder
}

func (builder *UploadAllMediaReqBodyBuilder) Build() *UploadAllMediaReqBody {
	req := &UploadAllMediaReqBody{}
	if builder.fileNameFlag {
		req.FileName = &builder.fileName
	}
	if builder.parentTypeFlag {
		req.ParentType = &builder.parentType
	}
	if builder.parentNodeFlag {
		req.ParentNode = &builder.parentNode
	}
	if builder.sizeFlag {
		req.Size = &builder.size
	}
	if builder.checksumFlag {
		req.Checksum = &builder.checksum
	}
	if builder.extraFlag {
		req.Extra = &builder.extra
	}
	if builder.fileFlag {
		req.File = builder.file
	}
	return req
}

type UploadAllMediaPathReqBodyBuilder struct {
	fileName       string // 文件名。
	fileNameFlag   bool
	parentType     string // 上传点类型。
	parentTypeFlag bool
	parentNode     string // 上传点的token。
	parentNodeFlag bool
	size           int // 文件大小（以字节为单位）。
	sizeFlag       bool
	checksum       string // 文件adler32校验和（可选）。
	checksumFlag   bool
	extra          string // 扩展信息(可选)。
	extraFlag      bool
	filePath       string // 文件二进制内容。
	filePathFlag   bool
}

func NewUploadAllMediaPathReqBodyBuilder() *UploadAllMediaPathReqBodyBuilder {
	builder := &UploadAllMediaPathReqBodyBuilder{}
	return builder
}

// 文件名。
//
// 示例值：demo.jpeg
func (builder *UploadAllMediaPathReqBodyBuilder) FileName(fileName string) *UploadAllMediaPathReqBodyBuilder {
	builder.fileName = fileName
	builder.fileNameFlag = true
	return builder
}

// 上传点类型。
//
// 示例值：doc_image
func (builder *UploadAllMediaPathReqBodyBuilder) ParentType(parentType string) *UploadAllMediaPathReqBodyBuilder {
	builder.parentType = parentType
	builder.parentTypeFlag = true
	return builder
}

// 上传点的token。
//
// 示例值：doccnFivLCfJfblZjGZtxgabcef
func (builder *UploadAllMediaPathReqBodyBuilder) ParentNode(parentNode string) *UploadAllMediaPathReqBodyBuilder {
	builder.parentNode = parentNode
	builder.parentNodeFlag = true
	return builder
}

// 文件大小（以字节为单位）。
//
// 示例值：1024
func (builder *UploadAllMediaPathReqBodyBuilder) Size(size int) *UploadAllMediaPathReqBodyBuilder {
	builder.size = size
	builder.sizeFlag = true
	return builder
}

// 文件adler32校验和（可选）。
//
// 示例值：12345678
func (builder *UploadAllMediaPathReqBodyBuilder) Checksum(checksum string) *UploadAllMediaPathReqBodyBuilder {
	builder.checksum = checksum
	builder.checksumFlag = true
	return builder
}

// 扩展信息(可选)。
//
// 示例值：{"test":"test"}
func (builder *UploadAllMediaPathReqBodyBuilder) Extra(extra string) *UploadAllMediaPathReqBodyBuilder {
	builder.extra = extra
	builder.extraFlag = true
	return builder
}

// 文件二进制内容。
//
// 示例值：file binary
func (builder *UploadAllMediaPathReqBodyBuilder) FilePath(filePath string) *UploadAllMediaPathReqBodyBuilder {
	builder.filePath = filePath
	builder.filePathFlag = true
	return builder
}

func (builder *UploadAllMediaPathReqBodyBuilder) Build() (*UploadAllMediaReqBody, error) {
	req := &UploadAllMediaReqBody{}
	if builder.fileNameFlag {
		req.FileName = &builder.fileName
	}
	if builder.parentTypeFlag {
		req.ParentType = &builder.parentType
	}
	if builder.parentNodeFlag {
		req.ParentNode = &builder.parentNode
	}
	if builder.sizeFlag {
		req.Size = &builder.size
	}
	if builder.checksumFlag {
		req.Checksum = &builder.checksum
	}
	if builder.extraFlag {
		req.Extra = &builder.extra
	}
	if builder.filePathFlag {
		data, err := larkcore.File2Bytes(builder.filePath)
		if err != nil {
			return nil, err
		}
		req.File = bytes.NewBuffer(data)
	}
	return req, nil
}

type UploadAllMediaReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *UploadAllMediaReqBody
}

func NewUploadAllMediaReqBuilder() *UploadAllMediaReqBuilder {
	builder := &UploadAllMediaReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 将文件、图片、视频等素材文件上传到指定云文档中。素材文件在云空间中不会显示，只会显示在对应云文档中。
func (builder *UploadAllMediaReqBuilder) Body(body *UploadAllMediaReqBody) *UploadAllMediaReqBuilder {
	builder.body = body
	return builder
}

func (builder *UploadAllMediaReqBuilder) Build() *UploadAllMediaReq {
	req := &UploadAllMediaReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.Body = builder.body
	return req
}

type UploadAllMediaReqBody struct {
	FileName   *string   `json:"file_name,omitempty"`   // 文件名。
	ParentType *string   `json:"parent_type,omitempty"` // 上传点类型。
	ParentNode *string   `json:"parent_node,omitempty"` // 上传点的token。
	Size       *int      `json:"size,omitempty"`        // 文件大小（以字节为单位）。
	Checksum   *string   `json:"checksum,omitempty"`    // 文件adler32校验和（可选）。
	Extra      *string   `json:"extra,omitempty"`       // 扩展信息(可选)。
	File       io.Reader `json:"file,omitempty"`        // 文件二进制内容。
}

type UploadAllMediaReq struct {
	apiReq *larkcore.ApiReq
	Body   *UploadAllMediaReqBody `body:""`
}

type UploadAllMediaRespData struct {
	FileToken *string `json:"file_token,omitempty"` // 素材文件的 token。
}

type UploadAllMediaResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *UploadAllMediaRespData `json:"data"` // 业务数据
}

func (resp *UploadAllMediaResp) Success() bool {
	return resp.Code == 0
}

type UploadFinishMediaReqBodyBuilder struct {
	uploadId     string // 分片上传事务ID
	uploadIdFlag bool
	blockNum     int // 分片数量
	blockNumFlag bool
}

func NewUploadFinishMediaReqBodyBuilder() *UploadFinishMediaReqBodyBuilder {
	builder := &UploadFinishMediaReqBodyBuilder{}
	return builder
}

// 分片上传事务ID
//
//示例值：7111211691345512356
func (builder *UploadFinishMediaReqBodyBuilder) UploadId(uploadId string) *UploadFinishMediaReqBodyBuilder {
	builder.uploadId = uploadId
	builder.uploadIdFlag = true
	return builder
}

// 分片数量
//
//示例值：1
func (builder *UploadFinishMediaReqBodyBuilder) BlockNum(blockNum int) *UploadFinishMediaReqBodyBuilder {
	builder.blockNum = blockNum
	builder.blockNumFlag = true
	return builder
}

func (builder *UploadFinishMediaReqBodyBuilder) Build() *UploadFinishMediaReqBody {
	req := &UploadFinishMediaReqBody{}
	if builder.uploadIdFlag {
		req.UploadId = &builder.uploadId
	}
	if builder.blockNumFlag {
		req.BlockNum = &builder.blockNum
	}
	return req
}

type UploadFinishMediaPathReqBodyBuilder struct {
	uploadId     string // 分片上传事务ID
	uploadIdFlag bool
	blockNum     int // 分片数量
	blockNumFlag bool
}

func NewUploadFinishMediaPathReqBodyBuilder() *UploadFinishMediaPathReqBodyBuilder {
	builder := &UploadFinishMediaPathReqBodyBuilder{}
	return builder
}

// 分片上传事务ID
//
// 示例值：7111211691345512356
func (builder *UploadFinishMediaPathReqBodyBuilder) UploadId(uploadId string) *UploadFinishMediaPathReqBodyBuilder {
	builder.uploadId = uploadId
	builder.uploadIdFlag = true
	return builder
}

// 分片数量
//
// 示例值：1
func (builder *UploadFinishMediaPathReqBodyBuilder) BlockNum(blockNum int) *UploadFinishMediaPathReqBodyBuilder {
	builder.blockNum = blockNum
	builder.blockNumFlag = true
	return builder
}

func (builder *UploadFinishMediaPathReqBodyBuilder) Build() (*UploadFinishMediaReqBody, error) {
	req := &UploadFinishMediaReqBody{}
	if builder.uploadIdFlag {
		req.UploadId = &builder.uploadId
	}
	if builder.blockNumFlag {
		req.BlockNum = &builder.blockNum
	}
	return req, nil
}

type UploadFinishMediaReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *UploadFinishMediaReqBody
}

func NewUploadFinishMediaReqBuilder() *UploadFinishMediaReqBuilder {
	builder := &UploadFinishMediaReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 触发完成上传。
func (builder *UploadFinishMediaReqBuilder) Body(body *UploadFinishMediaReqBody) *UploadFinishMediaReqBuilder {
	builder.body = body
	return builder
}

func (builder *UploadFinishMediaReqBuilder) Build() *UploadFinishMediaReq {
	req := &UploadFinishMediaReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.Body = builder.body
	return req
}

type UploadFinishMediaReqBody struct {
	UploadId *string `json:"upload_id,omitempty"` // 分片上传事务ID
	BlockNum *int    `json:"block_num,omitempty"` // 分片数量
}

type UploadFinishMediaReq struct {
	apiReq *larkcore.ApiReq
	Body   *UploadFinishMediaReqBody `body:""`
}

type UploadFinishMediaRespData struct {
	FileToken *string `json:"file_token,omitempty"` // 新创建文件的 token
}

type UploadFinishMediaResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *UploadFinishMediaRespData `json:"data"` // 业务数据
}

func (resp *UploadFinishMediaResp) Success() bool {
	return resp.Code == 0
}

type UploadPartMediaReqBodyBuilder struct {
	uploadId     string // 分片上传事务ID。
	uploadIdFlag bool
	seq          int // 块号，从0开始计数。
	seqFlag      bool
	size         int // 块大小（以字节为单位）。
	sizeFlag     bool
	checksum     string // 文件分块adler32校验和(可选)。
	checksumFlag bool
	file         io.Reader // 文件分片二进制内容。
	fileFlag     bool
}

func NewUploadPartMediaReqBodyBuilder() *UploadPartMediaReqBodyBuilder {
	builder := &UploadPartMediaReqBodyBuilder{}
	return builder
}

// 分片上传事务ID。
//
//示例值：7111211691345512356
func (builder *UploadPartMediaReqBodyBuilder) UploadId(uploadId string) *UploadPartMediaReqBodyBuilder {
	builder.uploadId = uploadId
	builder.uploadIdFlag = true
	return builder
}

// 块号，从0开始计数。
//
//示例值：0
func (builder *UploadPartMediaReqBodyBuilder) Seq(seq int) *UploadPartMediaReqBodyBuilder {
	builder.seq = seq
	builder.seqFlag = true
	return builder
}

// 块大小（以字节为单位）。
//
//示例值：4194304
func (builder *UploadPartMediaReqBodyBuilder) Size(size int) *UploadPartMediaReqBodyBuilder {
	builder.size = size
	builder.sizeFlag = true
	return builder
}

// 文件分块adler32校验和(可选)。
//
//示例值：12345678
func (builder *UploadPartMediaReqBodyBuilder) Checksum(checksum string) *UploadPartMediaReqBodyBuilder {
	builder.checksum = checksum
	builder.checksumFlag = true
	return builder
}

// 文件分片二进制内容。
//
//示例值：file binary
func (builder *UploadPartMediaReqBodyBuilder) File(file io.Reader) *UploadPartMediaReqBodyBuilder {
	builder.file = file
	builder.fileFlag = true
	return builder
}

func (builder *UploadPartMediaReqBodyBuilder) Build() *UploadPartMediaReqBody {
	req := &UploadPartMediaReqBody{}
	if builder.uploadIdFlag {
		req.UploadId = &builder.uploadId
	}
	if builder.seqFlag {
		req.Seq = &builder.seq
	}
	if builder.sizeFlag {
		req.Size = &builder.size
	}
	if builder.checksumFlag {
		req.Checksum = &builder.checksum
	}
	if builder.fileFlag {
		req.File = builder.file
	}
	return req
}

type UploadPartMediaPathReqBodyBuilder struct {
	uploadId     string // 分片上传事务ID。
	uploadIdFlag bool
	seq          int // 块号，从0开始计数。
	seqFlag      bool
	size         int // 块大小（以字节为单位）。
	sizeFlag     bool
	checksum     string // 文件分块adler32校验和(可选)。
	checksumFlag bool
	filePath     string // 文件分片二进制内容。
	filePathFlag bool
}

func NewUploadPartMediaPathReqBodyBuilder() *UploadPartMediaPathReqBodyBuilder {
	builder := &UploadPartMediaPathReqBodyBuilder{}
	return builder
}

// 分片上传事务ID。
//
// 示例值：7111211691345512356
func (builder *UploadPartMediaPathReqBodyBuilder) UploadId(uploadId string) *UploadPartMediaPathReqBodyBuilder {
	builder.uploadId = uploadId
	builder.uploadIdFlag = true
	return builder
}

// 块号，从0开始计数。
//
// 示例值：0
func (builder *UploadPartMediaPathReqBodyBuilder) Seq(seq int) *UploadPartMediaPathReqBodyBuilder {
	builder.seq = seq
	builder.seqFlag = true
	return builder
}

// 块大小（以字节为单位）。
//
// 示例值：4194304
func (builder *UploadPartMediaPathReqBodyBuilder) Size(size int) *UploadPartMediaPathReqBodyBuilder {
	builder.size = size
	builder.sizeFlag = true
	return builder
}

// 文件分块adler32校验和(可选)。
//
// 示例值：12345678
func (builder *UploadPartMediaPathReqBodyBuilder) Checksum(checksum string) *UploadPartMediaPathReqBodyBuilder {
	builder.checksum = checksum
	builder.checksumFlag = true
	return builder
}

// 文件分片二进制内容。
//
// 示例值：file binary
func (builder *UploadPartMediaPathReqBodyBuilder) FilePath(filePath string) *UploadPartMediaPathReqBodyBuilder {
	builder.filePath = filePath
	builder.filePathFlag = true
	return builder
}

func (builder *UploadPartMediaPathReqBodyBuilder) Build() (*UploadPartMediaReqBody, error) {
	req := &UploadPartMediaReqBody{}
	if builder.uploadIdFlag {
		req.UploadId = &builder.uploadId
	}
	if builder.seqFlag {
		req.Seq = &builder.seq
	}
	if builder.sizeFlag {
		req.Size = &builder.size
	}
	if builder.checksumFlag {
		req.Checksum = &builder.checksum
	}
	if builder.filePathFlag {
		data, err := larkcore.File2Bytes(builder.filePath)
		if err != nil {
			return nil, err
		}
		req.File = bytes.NewBuffer(data)
	}
	return req, nil
}

type UploadPartMediaReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *UploadPartMediaReqBody
}

func NewUploadPartMediaReqBuilder() *UploadPartMediaReqBuilder {
	builder := &UploadPartMediaReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 上传对应的文件块。
func (builder *UploadPartMediaReqBuilder) Body(body *UploadPartMediaReqBody) *UploadPartMediaReqBuilder {
	builder.body = body
	return builder
}

func (builder *UploadPartMediaReqBuilder) Build() *UploadPartMediaReq {
	req := &UploadPartMediaReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.Body = builder.body
	return req
}

type UploadPartMediaReqBody struct {
	UploadId *string   `json:"upload_id,omitempty"` // 分片上传事务ID。
	Seq      *int      `json:"seq,omitempty"`       // 块号，从0开始计数。
	Size     *int      `json:"size,omitempty"`      // 块大小（以字节为单位）。
	Checksum *string   `json:"checksum,omitempty"`  // 文件分块adler32校验和(可选)。
	File     io.Reader `json:"file,omitempty"`      // 文件分片二进制内容。
}

type UploadPartMediaReq struct {
	apiReq *larkcore.ApiReq
	Body   *UploadPartMediaReqBody `body:""`
}

type UploadPartMediaResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *UploadPartMediaResp) Success() bool {
	return resp.Code == 0
}

type UploadPrepareMediaReqBuilder struct {
	apiReq          *larkcore.ApiReq
	mediaUploadInfo *MediaUploadInfo
}

func NewUploadPrepareMediaReqBuilder() *UploadPrepareMediaReqBuilder {
	builder := &UploadPrepareMediaReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 发送初始化请求获取上传事务ID和分块策略，目前是以4MB大小进行定长分片。
func (builder *UploadPrepareMediaReqBuilder) MediaUploadInfo(mediaUploadInfo *MediaUploadInfo) *UploadPrepareMediaReqBuilder {
	builder.mediaUploadInfo = mediaUploadInfo
	return builder
}

func (builder *UploadPrepareMediaReqBuilder) Build() *UploadPrepareMediaReq {
	req := &UploadPrepareMediaReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.Body = builder.mediaUploadInfo
	return req
}

type UploadPrepareMediaReq struct {
	apiReq          *larkcore.ApiReq
	MediaUploadInfo *MediaUploadInfo `body:""`
}

type UploadPrepareMediaRespData struct {
	UploadId  *string `json:"upload_id,omitempty"`  // 分片上传事务ID
	BlockSize *int    `json:"block_size,omitempty"` // 分片大小策略
	BlockNum  *int    `json:"block_num,omitempty"`  // 分片数量
}

type UploadPrepareMediaResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *UploadPrepareMediaRespData `json:"data"` // 业务数据
}

func (resp *UploadPrepareMediaResp) Success() bool {
	return resp.Code == 0
}

type BatchQueryMetaReqBuilder struct {
	apiReq      *larkcore.ApiReq
	metaRequest *MetaRequest
}

func NewBatchQueryMetaReqBuilder() *BatchQueryMetaReqBuilder {
	builder := &BatchQueryMetaReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 此次调用中使用的用户ID的类型
//
// 示例值：user_id
func (builder *BatchQueryMetaReqBuilder) UserIdType(userIdType string) *BatchQueryMetaReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

// 该接口用于根据 token 获取各类文件的元数据
func (builder *BatchQueryMetaReqBuilder) MetaRequest(metaRequest *MetaRequest) *BatchQueryMetaReqBuilder {
	builder.metaRequest = metaRequest
	return builder
}

func (builder *BatchQueryMetaReqBuilder) Build() *BatchQueryMetaReq {
	req := &BatchQueryMetaReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.metaRequest
	return req
}

type BatchQueryMetaReq struct {
	apiReq      *larkcore.ApiReq
	MetaRequest *MetaRequest `body:""`
}

type BatchQueryMetaRespData struct {
	Metas      []*Meta       `json:"metas,omitempty"`       // 文档元数据列表
	FailedList []*MetaFailed `json:"failed_list,omitempty"` // 无法获取元数据的文档列表
}

type BatchQueryMetaResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *BatchQueryMetaRespData `json:"data"` // 业务数据
}

func (resp *BatchQueryMetaResp) Success() bool {
	return resp.Code == 0
}

type CreatePermissionMemberReqBuilder struct {
	apiReq     *larkcore.ApiReq
	baseMember *BaseMember
}

func NewCreatePermissionMemberReqBuilder() *CreatePermissionMemberReqBuilder {
	builder := &CreatePermissionMemberReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 文件的 token，获取方式见 [如何获取云文档资源相关 token](https://open.feishu.cn/document/ukTMukTMukTM/uczNzUjL3czM14yN3MTN#08bb5df6)
//
// 示例值：doccnBKgoMyY5OMbUG6FioTXuBe
func (builder *CreatePermissionMemberReqBuilder) Token(token string) *CreatePermissionMemberReqBuilder {
	builder.apiReq.PathParams.Set("token", fmt.Sprint(token))
	return builder
}

// 文件类型，需要与文件的 token 相匹配
//
// 示例值：doc
func (builder *CreatePermissionMemberReqBuilder) Type(type_ string) *CreatePermissionMemberReqBuilder {
	builder.apiReq.QueryParams.Set("type", fmt.Sprint(type_))
	return builder
}

// 添加权限后是否通知对方
//
// 示例值：false
func (builder *CreatePermissionMemberReqBuilder) NeedNotification(needNotification bool) *CreatePermissionMemberReqBuilder {
	builder.apiReq.QueryParams.Set("need_notification", fmt.Sprint(needNotification))
	return builder
}

// 该接口用于根据 filetoken 给用户增加文档的权限。
func (builder *CreatePermissionMemberReqBuilder) BaseMember(baseMember *BaseMember) *CreatePermissionMemberReqBuilder {
	builder.baseMember = baseMember
	return builder
}

func (builder *CreatePermissionMemberReqBuilder) Build() *CreatePermissionMemberReq {
	req := &CreatePermissionMemberReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.baseMember
	return req
}

type CreatePermissionMemberReq struct {
	apiReq     *larkcore.ApiReq
	BaseMember *BaseMember `body:""`
}

type CreatePermissionMemberRespData struct {
	Member *BaseMember `json:"member,omitempty"` // 本次添加权限的用户信息
}

type CreatePermissionMemberResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *CreatePermissionMemberRespData `json:"data"` // 业务数据
}

func (resp *CreatePermissionMemberResp) Success() bool {
	return resp.Code == 0
}

type DeletePermissionMemberReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewDeletePermissionMemberReqBuilder() *DeletePermissionMemberReqBuilder {
	builder := &DeletePermissionMemberReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 文件的 token，获取方式见 [如何获取云文档资源相关 token](https://open.feishu.cn/document/ukTMukTMukTM/uczNzUjL3czM14yN3MTN#08bb5df6)
//
// 示例值：doccnBKgoMyY5OMbUG6FioTXuBe
func (builder *DeletePermissionMemberReqBuilder) Token(token string) *DeletePermissionMemberReqBuilder {
	builder.apiReq.PathParams.Set("token", fmt.Sprint(token))
	return builder
}

// 协作者 ID，与协作者 ID 类型需要对应
//
// 示例值：ou_7dab8a3d3cdcc9da365777c7ad535d62
func (builder *DeletePermissionMemberReqBuilder) MemberId(memberId string) *DeletePermissionMemberReqBuilder {
	builder.apiReq.PathParams.Set("member_id", fmt.Sprint(memberId))
	return builder
}

// 文件类型，需要与文件的 token 相匹配
//
// 示例值：doc
func (builder *DeletePermissionMemberReqBuilder) Type(type_ string) *DeletePermissionMemberReqBuilder {
	builder.apiReq.QueryParams.Set("type", fmt.Sprint(type_))
	return builder
}

// 协作者 ID 类型，与协作者 ID 需要对应
//
// 示例值：openid
func (builder *DeletePermissionMemberReqBuilder) MemberType(memberType string) *DeletePermissionMemberReqBuilder {
	builder.apiReq.QueryParams.Set("member_type", fmt.Sprint(memberType))
	return builder
}

func (builder *DeletePermissionMemberReqBuilder) Build() *DeletePermissionMemberReq {
	req := &DeletePermissionMemberReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type DeletePermissionMemberReq struct {
	apiReq *larkcore.ApiReq
}

type DeletePermissionMemberResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *DeletePermissionMemberResp) Success() bool {
	return resp.Code == 0
}

type ListPermissionMemberReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewListPermissionMemberReqBuilder() *ListPermissionMemberReqBuilder {
	builder := &ListPermissionMemberReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 文件的 token，获取方式见 [如何获取云文档资源相关 token](https://open.feishu.cn/document/ukTMukTMukTM/uczNzUjL3czM14yN3MTN#08bb5df6)
//
// 示例值：doccnBKgoMyY5OMbUG6FioTXuBe
func (builder *ListPermissionMemberReqBuilder) Token(token string) *ListPermissionMemberReqBuilder {
	builder.apiReq.PathParams.Set("token", fmt.Sprint(token))
	return builder
}

// 文件类型，需要与文件的 token 相匹配
//
// 示例值：doc
func (builder *ListPermissionMemberReqBuilder) Type(type_ string) *ListPermissionMemberReqBuilder {
	builder.apiReq.QueryParams.Set("type", fmt.Sprint(type_))
	return builder
}

// 指定返回的协作者字段信息，如无指定则默认不返回;;**可选值有：** ;- `name`：协作者名;- `type`：协作者类型;- `avatar`：头像;- `external_label`：外部标签;;**注意：** ;- 你可以使用特殊值`*`指定返回目前支持的所有字段;- 你可以使用`,`分隔若干个你想指定返回的字段，如：`name,avatar`;- 按需指定返回字段接口性能更好
//
// 示例值：*
func (builder *ListPermissionMemberReqBuilder) Fields(fields string) *ListPermissionMemberReqBuilder {
	builder.apiReq.QueryParams.Set("fields", fmt.Sprint(fields))
	return builder
}

func (builder *ListPermissionMemberReqBuilder) Build() *ListPermissionMemberReq {
	req := &ListPermissionMemberReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type ListPermissionMemberReq struct {
	apiReq *larkcore.ApiReq
}

type ListPermissionMemberRespData struct {
	Items []*Member `json:"items,omitempty"` // 返回的列表数据
}

type ListPermissionMemberResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *ListPermissionMemberRespData `json:"data"` // 业务数据
}

func (resp *ListPermissionMemberResp) Success() bool {
	return resp.Code == 0
}

type UpdatePermissionMemberReqBuilder struct {
	apiReq     *larkcore.ApiReq
	baseMember *BaseMember
}

func NewUpdatePermissionMemberReqBuilder() *UpdatePermissionMemberReqBuilder {
	builder := &UpdatePermissionMemberReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 文件的 token，获取方式见 [如何获取云文档资源相关 token](https://open.feishu.cn/document/ukTMukTMukTM/uczNzUjL3czM14yN3MTN#08bb5df6)
//
// 示例值：doccnBKgoMyY5OMbUG6FioTXuBe
func (builder *UpdatePermissionMemberReqBuilder) Token(token string) *UpdatePermissionMemberReqBuilder {
	builder.apiReq.PathParams.Set("token", fmt.Sprint(token))
	return builder
}

// 协作者 ID，与协作者 ID 类型需要对应
//
// 示例值：ou_7dab8a3d3cdcc9da365777c7ad535d62
func (builder *UpdatePermissionMemberReqBuilder) MemberId(memberId string) *UpdatePermissionMemberReqBuilder {
	builder.apiReq.PathParams.Set("member_id", fmt.Sprint(memberId))
	return builder
}

// 更新权限后是否通知对方;;**注意：** 使用`tenant_access_token`访问不支持该参数
//
// 示例值：false
func (builder *UpdatePermissionMemberReqBuilder) NeedNotification(needNotification bool) *UpdatePermissionMemberReqBuilder {
	builder.apiReq.QueryParams.Set("need_notification", fmt.Sprint(needNotification))
	return builder
}

// 文件类型，需要与文件的 token 相匹配
//
// 示例值：doc
func (builder *UpdatePermissionMemberReqBuilder) Type(type_ string) *UpdatePermissionMemberReqBuilder {
	builder.apiReq.QueryParams.Set("type", fmt.Sprint(type_))
	return builder
}

// 该接口用于根据 filetoken 更新文档协作者的权限。
func (builder *UpdatePermissionMemberReqBuilder) BaseMember(baseMember *BaseMember) *UpdatePermissionMemberReqBuilder {
	builder.baseMember = baseMember
	return builder
}

func (builder *UpdatePermissionMemberReqBuilder) Build() *UpdatePermissionMemberReq {
	req := &UpdatePermissionMemberReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.baseMember
	return req
}

type UpdatePermissionMemberReq struct {
	apiReq     *larkcore.ApiReq
	BaseMember *BaseMember `body:""`
}

type UpdatePermissionMemberRespData struct {
	Member *BaseMember `json:"member,omitempty"` // 本次更新权限的用户信息
}

type UpdatePermissionMemberResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *UpdatePermissionMemberRespData `json:"data"` // 业务数据
}

func (resp *UpdatePermissionMemberResp) Success() bool {
	return resp.Code == 0
}

type GetPermissionPublicReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewGetPermissionPublicReqBuilder() *GetPermissionPublicReqBuilder {
	builder := &GetPermissionPublicReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 文件的 token，获取方式见 [如何获取云文档资源相关 token](https://open.feishu.cn/document/ukTMukTMukTM/uczNzUjL3czM14yN3MTN#08bb5df6)
//
// 示例值：doccnBKgoMyY5OMbUG6FioTXuBe
func (builder *GetPermissionPublicReqBuilder) Token(token string) *GetPermissionPublicReqBuilder {
	builder.apiReq.PathParams.Set("token", fmt.Sprint(token))
	return builder
}

// 文件类型，需要与文件的 token 相匹配
//
// 示例值：doc
func (builder *GetPermissionPublicReqBuilder) Type(type_ string) *GetPermissionPublicReqBuilder {
	builder.apiReq.QueryParams.Set("type", fmt.Sprint(type_))
	return builder
}

func (builder *GetPermissionPublicReqBuilder) Build() *GetPermissionPublicReq {
	req := &GetPermissionPublicReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type GetPermissionPublicReq struct {
	apiReq *larkcore.ApiReq
}

type GetPermissionPublicRespData struct {
	PermissionPublic *PermissionPublic `json:"permission_public,omitempty"` // 返回的文档权限设置
}

type GetPermissionPublicResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *GetPermissionPublicRespData `json:"data"` // 业务数据
}

func (resp *GetPermissionPublicResp) Success() bool {
	return resp.Code == 0
}

type PatchPermissionPublicReqBuilder struct {
	apiReq                  *larkcore.ApiReq
	permissionPublicRequest *PermissionPublicRequest
}

func NewPatchPermissionPublicReqBuilder() *PatchPermissionPublicReqBuilder {
	builder := &PatchPermissionPublicReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 文件的 token，获取方式见 [如何获取云文档资源相关 token](https://open.feishu.cn/document/ukTMukTMukTM/uczNzUjL3czM14yN3MTN#08bb5df6)
//
// 示例值：doccnBKgoMyY5OMbUG6FioTXuBe
func (builder *PatchPermissionPublicReqBuilder) Token(token string) *PatchPermissionPublicReqBuilder {
	builder.apiReq.PathParams.Set("token", fmt.Sprint(token))
	return builder
}

// 文件类型，需要与文件的 token 相匹配
//
// 示例值：doc
func (builder *PatchPermissionPublicReqBuilder) Type(type_ string) *PatchPermissionPublicReqBuilder {
	builder.apiReq.QueryParams.Set("type", fmt.Sprint(type_))
	return builder
}

// 该接口用于根据 filetoken 更新云文档的权限设置。
func (builder *PatchPermissionPublicReqBuilder) PermissionPublicRequest(permissionPublicRequest *PermissionPublicRequest) *PatchPermissionPublicReqBuilder {
	builder.permissionPublicRequest = permissionPublicRequest
	return builder
}

func (builder *PatchPermissionPublicReqBuilder) Build() *PatchPermissionPublicReq {
	req := &PatchPermissionPublicReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.permissionPublicRequest
	return req
}

type PatchPermissionPublicReq struct {
	apiReq                  *larkcore.ApiReq
	PermissionPublicRequest *PermissionPublicRequest `body:""`
}

type PatchPermissionPublicRespData struct {
	PermissionPublic *PermissionPublic `json:"permission_public,omitempty"` // 本次更新后的文档权限设置
}

type PatchPermissionPublicResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *PatchPermissionPublicRespData `json:"data"` // 业务数据
}

func (resp *PatchPermissionPublicResp) Success() bool {
	return resp.Code == 0
}

type P2FileBitableFieldChangedV1Data struct {
	FileType         *string                    `json:"file_type,omitempty"`          // 文档类型
	FileToken        *string                    `json:"file_token,omitempty"`         // 文档token
	TableId          *string                    `json:"table_id,omitempty"`           // 多维表格数据表ID
	OperatorId       *UserId                    `json:"operator_id,omitempty"`        // 用户 ID
	ActionList       []*BitableTableFieldAction `json:"action_list,omitempty"`        // 字段变更操作列表
	Revision         *int                       `json:"revision,omitempty"`           // 多维表格数据表的版本号
	SubscriberIdList []*UserId                  `json:"subscriber_id_list,omitempty"` // 订阅用户id列表
	UpdateTime       *int                       `json:"update_time,omitempty"`        // 字段变更时间
}

type P2FileBitableFieldChangedV1 struct {
	*larkevent.EventV2Base                                  // 事件基础数据
	*larkevent.EventReq                                     // 请求原生数据
	Event                  *P2FileBitableFieldChangedV1Data `json:"event"` // 事件内容
}

func (m *P2FileBitableFieldChangedV1) RawReq(req *larkevent.EventReq) {
	m.EventReq = req
}

type P2FileDeletedV1Data struct {
	FileType         *string   `json:"file_type,omitempty"`          // 文档类型
	FileToken        *string   `json:"file_token,omitempty"`         // 文档token
	OperatorId       *UserId   `json:"operator_id,omitempty"`        // 操作者id
	SubscriberIdList []*UserId `json:"subscriber_id_list,omitempty"` // 订阅用户id列表
}

type P2FileDeletedV1 struct {
	*larkevent.EventV2Base                      // 事件基础数据
	*larkevent.EventReq                         // 请求原生数据
	Event                  *P2FileDeletedV1Data `json:"event"` // 事件内容
}

func (m *P2FileDeletedV1) RawReq(req *larkevent.EventReq) {
	m.EventReq = req
}

type P2FileEditV1Data struct {
	FileType         *string   `json:"file_type,omitempty"`          // 文档类型
	FileToken        *string   `json:"file_token,omitempty"`         // 文档token
	OperatorIdList   []*UserId `json:"operator_id_list,omitempty"`   // 操作者id列表
	SubscriberIdList []*UserId `json:"subscriber_id_list,omitempty"` // 订阅用户id列表
	SheetId          *string   `json:"sheet_id,omitempty"`           // 表格的子SheetID，当表格发生编辑时将会包含此字段
}

type P2FileEditV1 struct {
	*larkevent.EventV2Base                   // 事件基础数据
	*larkevent.EventReq                      // 请求原生数据
	Event                  *P2FileEditV1Data `json:"event"` // 事件内容
}

func (m *P2FileEditV1) RawReq(req *larkevent.EventReq) {
	m.EventReq = req
}

type P2FilePermissionMemberAddedV1Data struct {
	FileType             *string   `json:"file_type,omitempty"`               // 文档类型
	FileToken            *string   `json:"file_token,omitempty"`              // 文档token
	OperatorId           *UserId   `json:"operator_id,omitempty"`             // 操作者id
	UserList             []*UserId `json:"user_list,omitempty"`               // 添加的用户列表
	ChatList             []string  `json:"chat_list,omitempty"`               //
	OpenDepartmentIdList []string  `json:"open_department_id_list,omitempty"` //
	SubscriberIdList     []*UserId `json:"subscriber_id_list,omitempty"`      // 订阅用户id列表
}

type P2FilePermissionMemberAddedV1 struct {
	*larkevent.EventV2Base                                    // 事件基础数据
	*larkevent.EventReq                                       // 请求原生数据
	Event                  *P2FilePermissionMemberAddedV1Data `json:"event"` // 事件内容
}

func (m *P2FilePermissionMemberAddedV1) RawReq(req *larkevent.EventReq) {
	m.EventReq = req
}

type P2FilePermissionMemberRemovedV1Data struct {
	FileType             *string   `json:"file_type,omitempty"`               // 文档类型
	FileToken            *string   `json:"file_token,omitempty"`              // 文档token
	OperatorId           *UserId   `json:"operator_id,omitempty"`             // 操作者id
	UserList             []*UserId `json:"user_list,omitempty"`               // 移除的用户列表
	ChatList             []string  `json:"chat_list,omitempty"`               //
	OpenDepartmentIdList []string  `json:"open_department_id_list,omitempty"` //
	SubscriberIdList     []*UserId `json:"subscriber_id_list,omitempty"`      // 订阅用户id列表
}

type P2FilePermissionMemberRemovedV1 struct {
	*larkevent.EventV2Base                                      // 事件基础数据
	*larkevent.EventReq                                         // 请求原生数据
	Event                  *P2FilePermissionMemberRemovedV1Data `json:"event"` // 事件内容
}

func (m *P2FilePermissionMemberRemovedV1) RawReq(req *larkevent.EventReq) {
	m.EventReq = req
}

type P2FileReadV1Data struct {
	FileType         *string   `json:"file_type,omitempty"`          // 文档类型
	FileToken        *string   `json:"file_token,omitempty"`         // 文档token
	OperatorIdList   []*UserId `json:"operator_id_list,omitempty"`   // 操作者id列表
	SubscriberIdList []*UserId `json:"subscriber_id_list,omitempty"` // 订阅用户id列表
}

type P2FileReadV1 struct {
	*larkevent.EventV2Base                   // 事件基础数据
	*larkevent.EventReq                      // 请求原生数据
	Event                  *P2FileReadV1Data `json:"event"` // 事件内容
}

func (m *P2FileReadV1) RawReq(req *larkevent.EventReq) {
	m.EventReq = req
}

type P2FileTitleUpdatedV1Data struct {
	FileType         *string   `json:"file_type,omitempty"`          // 文档类型
	FileToken        *string   `json:"file_token,omitempty"`         // 文档token
	OperatorId       *UserId   `json:"operator_id,omitempty"`        // 操作者id
	SubscriberIdList []*UserId `json:"subscriber_id_list,omitempty"` // 订阅用户id列表
}

type P2FileTitleUpdatedV1 struct {
	*larkevent.EventV2Base                           // 事件基础数据
	*larkevent.EventReq                              // 请求原生数据
	Event                  *P2FileTitleUpdatedV1Data `json:"event"` // 事件内容
}

func (m *P2FileTitleUpdatedV1) RawReq(req *larkevent.EventReq) {
	m.EventReq = req
}

type P2FileTrashedV1Data struct {
	FileType         *string   `json:"file_type,omitempty"`          // 文档类型
	FileToken        *string   `json:"file_token,omitempty"`         // 文档token
	OperatorId       *UserId   `json:"operator_id,omitempty"`        // 操作者id
	SubscriberIdList []*UserId `json:"subscriber_id_list,omitempty"` // 订阅用户id列表
}

type P2FileTrashedV1 struct {
	*larkevent.EventV2Base                      // 事件基础数据
	*larkevent.EventReq                         // 请求原生数据
	Event                  *P2FileTrashedV1Data `json:"event"` // 事件内容
}

func (m *P2FileTrashedV1) RawReq(req *larkevent.EventReq) {
	m.EventReq = req
}

type ListFileCommentIterator struct {
	nextPageToken *string
	items         []*FileComment
	index         int
	limit         int
	ctx           context.Context
	req           *ListFileCommentReq
	listFunc      func(ctx context.Context, req *ListFileCommentReq, options ...larkcore.RequestOptionFunc) (*ListFileCommentResp, error)
	options       []larkcore.RequestOptionFunc
	curlNum       int
}

func (iterator *ListFileCommentIterator) Next() (bool, *FileComment, error) {
	// 达到最大量，则返回
	if iterator.limit > 0 && iterator.curlNum >= iterator.limit {
		return false, nil, nil
	}

	// 为0则拉取数据
	if iterator.index == 0 || iterator.index >= len(iterator.items) {
		if iterator.index != 0 && iterator.nextPageToken == nil {
			return false, nil, nil
		}
		if iterator.nextPageToken != nil {
			iterator.req.apiReq.QueryParams.Set("page_token", *iterator.nextPageToken)
		}
		resp, err := iterator.listFunc(iterator.ctx, iterator.req, iterator.options...)
		if err != nil {
			return false, nil, err
		}

		if resp.Code != 0 {
			return false, nil, errors.New(fmt.Sprintf("Code:%d,Msg:%s", resp.Code, resp.Msg))
		}

		if len(resp.Data.Items) == 0 {
			return false, nil, nil
		}

		iterator.nextPageToken = resp.Data.PageToken
		iterator.items = resp.Data.Items
		iterator.index = 0
	}

	block := iterator.items[iterator.index]
	iterator.index++
	iterator.curlNum++
	return true, block, nil
}

func (iterator *ListFileCommentIterator) NextPageToken() *string {
	return iterator.nextPageToken
}
