// Package tenant code generated by oapi sdk gen
/*
 * MIT License
 *
 * Copyright (c) 2022 Lark Technologies Pte. Ltd.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice, shall be included in all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */

package larktenant

import (
	"github.com/larksuite/oapi-sdk-go/v3/core"
)

type Avatar struct {
	AvatarOrigin *string `json:"avatar_origin,omitempty"` // 企业头像
	Avatar72     *string `json:"avatar_72,omitempty"`     // 企业头像 72x72
	Avatar240    *string `json:"avatar_240,omitempty"`    // 企业头像 240x240
	Avatar640    *string `json:"avatar_640,omitempty"`    // 企业头像 640x640
}

type AvatarBuilder struct {
	avatarOrigin     string // 企业头像
	avatarOriginFlag bool
	avatar72         string // 企业头像 72x72
	avatar72Flag     bool
	avatar240        string // 企业头像 240x240
	avatar240Flag    bool
	avatar640        string // 企业头像 640x640
	avatar640Flag    bool
}

func NewAvatarBuilder() *AvatarBuilder {
	builder := &AvatarBuilder{}
	return builder
}

// 企业头像
//
// 示例值：https://foo.icon.com/xxxx
func (builder *AvatarBuilder) AvatarOrigin(avatarOrigin string) *AvatarBuilder {
	builder.avatarOrigin = avatarOrigin
	builder.avatarOriginFlag = true
	return builder
}

// 企业头像 72x72
//
// 示例值：https://foo.icon.com/xxxx
func (builder *AvatarBuilder) Avatar72(avatar72 string) *AvatarBuilder {
	builder.avatar72 = avatar72
	builder.avatar72Flag = true
	return builder
}

// 企业头像 240x240
//
// 示例值：https://foo.icon.com/xxxx
func (builder *AvatarBuilder) Avatar240(avatar240 string) *AvatarBuilder {
	builder.avatar240 = avatar240
	builder.avatar240Flag = true
	return builder
}

// 企业头像 640x640
//
// 示例值：https://foo.icon.com/xxxx
func (builder *AvatarBuilder) Avatar640(avatar640 string) *AvatarBuilder {
	builder.avatar640 = avatar640
	builder.avatar640Flag = true
	return builder
}

func (builder *AvatarBuilder) Build() *Avatar {
	req := &Avatar{}
	if builder.avatarOriginFlag {
		req.AvatarOrigin = &builder.avatarOrigin

	}
	if builder.avatar72Flag {
		req.Avatar72 = &builder.avatar72

	}
	if builder.avatar240Flag {
		req.Avatar240 = &builder.avatar240

	}
	if builder.avatar640Flag {
		req.Avatar640 = &builder.avatar640

	}
	return req
}

type Tenant struct {
	Name      *string `json:"name,omitempty"`       // 企业名称
	DisplayId *string `json:"display_id,omitempty"` // 企业编号，平台内唯一
	TenantTag *int    `json:"tenant_tag,omitempty"` // 个人版/团队版标志
	TenantKey *string `json:"tenant_key,omitempty"` // 企业标识
	Avatar    *Avatar `json:"avatar,omitempty"`     // 企业头像
}

type TenantBuilder struct {
	name          string // 企业名称
	nameFlag      bool
	displayId     string // 企业编号，平台内唯一
	displayIdFlag bool
	tenantTag     int // 个人版/团队版标志
	tenantTagFlag bool
	tenantKey     string // 企业标识
	tenantKeyFlag bool
	avatar        *Avatar // 企业头像
	avatarFlag    bool
}

func NewTenantBuilder() *TenantBuilder {
	builder := &TenantBuilder{}
	return builder
}

// 企业名称
//
// 示例值：企业名称
func (builder *TenantBuilder) Name(name string) *TenantBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

// 企业编号，平台内唯一
//
// 示例值：F123456789
func (builder *TenantBuilder) DisplayId(displayId string) *TenantBuilder {
	builder.displayId = displayId
	builder.displayIdFlag = true
	return builder
}

// 个人版/团队版标志
//
// 示例值：0
func (builder *TenantBuilder) TenantTag(tenantTag int) *TenantBuilder {
	builder.tenantTag = tenantTag
	builder.tenantTagFlag = true
	return builder
}

// 企业标识
//
// 示例值：abcdefghi
func (builder *TenantBuilder) TenantKey(tenantKey string) *TenantBuilder {
	builder.tenantKey = tenantKey
	builder.tenantKeyFlag = true
	return builder
}

// 企业头像
//
// 示例值：
func (builder *TenantBuilder) Avatar(avatar *Avatar) *TenantBuilder {
	builder.avatar = avatar
	builder.avatarFlag = true
	return builder
}

func (builder *TenantBuilder) Build() *Tenant {
	req := &Tenant{}
	if builder.nameFlag {
		req.Name = &builder.name

	}
	if builder.displayIdFlag {
		req.DisplayId = &builder.displayId

	}
	if builder.tenantTagFlag {
		req.TenantTag = &builder.tenantTag

	}
	if builder.tenantKeyFlag {
		req.TenantKey = &builder.tenantKey

	}
	if builder.avatarFlag {
		req.Avatar = builder.avatar
	}
	return req
}

type QueryTenantRespData struct {
	Tenant *Tenant `json:"tenant,omitempty"` // 企业信息
}

type QueryTenantResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *QueryTenantRespData `json:"data"` // 业务数据
}

func (resp *QueryTenantResp) Success() bool {
	return resp.Code == 0
}
