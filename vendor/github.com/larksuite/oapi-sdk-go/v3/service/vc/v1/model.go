// Package vc code generated by oapi sdk gen
/*
 * MIT License
 *
 * Copyright (c) 2022 Lark Technologies Pte. Ltd.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice, shall be included in all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */

package larkvc

import (
	"io"

	"io/ioutil"

	"fmt"

	"context"
	"errors"

	"github.com/larksuite/oapi-sdk-go/v3/event"

	"github.com/larksuite/oapi-sdk-go/v3/core"
)

const (
	QueryTypeRoom = 1 // 会议室
	QueryTypeErc  = 2 // erc

)

const (
	UserIdTypeUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypeUnionId = "union_id" // 以union_id来识别用户
	UserIdTypeOpenId  = "open_id"  // 以open_id来识别用户（推荐）
)

const (
	UserIdTypeParticipantListExportUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypeParticipantListExportUnionId = "union_id" // 以union_id来识别用户
	UserIdTypeParticipantListExportOpenId  = "open_id"  // 以open_id来识别用户（推荐）
)

const (
	UserIdTypeParticipantQualityListExportUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypeParticipantQualityListExportUnionId = "union_id" // 以union_id来识别用户
	UserIdTypeParticipantQualityListExportOpenId  = "open_id"  // 以open_id来识别用户（推荐）
)

const (
	UserIdTypeGetMeetingUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypeGetMeetingUnionId = "union_id" // 以union_id来识别用户
	UserIdTypeGetMeetingOpenId  = "open_id"  // 以open_id来识别用户（推荐）
)

const (
	UserIdTypeInviteMeetingUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypeInviteMeetingUnionId = "union_id" // 以union_id来识别用户
	UserIdTypeInviteMeetingOpenId  = "open_id"  // 以open_id来识别用户（推荐）
)

const (
	UserIdTypeKickoutMeetingUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypeKickoutMeetingUnionId = "union_id" // 以union_id来识别用户
	UserIdTypeKickoutMeetingOpenId  = "open_id"  // 以open_id来识别用户（推荐）
)

const (
	UserIdTypeSetHostMeetingUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypeSetHostMeetingUnionId = "union_id" // 以union_id来识别用户
	UserIdTypeSetHostMeetingOpenId  = "open_id"  // 以open_id来识别用户（推荐）
)

const (
	UserIdTypeSetPermissionMeetingRecordingUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypeSetPermissionMeetingRecordingUnionId = "union_id" // 以union_id来识别用户
	UserIdTypeSetPermissionMeetingRecordingOpenId  = "open_id"  // 以open_id来识别用户（推荐）
)

const (
	TopUserOrderByMeetingCount    = 1 // 会议数量
	TopUserOrderByMeetingDuration = 2 // 会议时长

)

const (
	UserIdTypeGetTopUserReportUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypeGetTopUserReportUnionId = "union_id" // 以union_id来识别用户
	UserIdTypeGetTopUserReportOpenId  = "open_id"  // 以open_id来识别用户
)

const (
	UserIdTypeApplyReserveUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypeApplyReserveUnionId = "union_id" // 以union_id来识别用户
	UserIdTypeApplyReserveOpenId  = "open_id"  // 以open_id来识别用户（推荐）
)

const (
	UserIdTypeGetReserveUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypeGetReserveUnionId = "union_id" // 以union_id来识别用户
	UserIdTypeGetReserveOpenId  = "open_id"  // 以open_id来识别用户
)

const (
	UserIdTypeGetActiveMeetingReserveUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypeGetActiveMeetingReserveUnionId = "union_id" // 以union_id来识别用户
	UserIdTypeGetActiveMeetingReserveOpenId  = "open_id"  // 以open_id来识别用户
)

const (
	UserIdTypeUpdateReserveUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypeUpdateReserveUnionId = "union_id" // 以union_id来识别用户
	UserIdTypeUpdateReserveOpenId  = "open_id"  // 以open_id来识别用户（推荐）
)

const (
	UserIdTypePatchReserveConfigUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypePatchReserveConfigUnionId = "union_id" // 以union_id来识别用户
	UserIdTypePatchReserveConfigOpenId  = "open_id"  // 以open_id来识别用户
)

const (
	UserIdTypeReserveScopeReserveConfigUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypeReserveScopeReserveConfigUnionId = "union_id" // 以union_id来识别用户
	UserIdTypeReserveScopeReserveConfigOpenId  = "open_id"  // 以open_id来识别用户
)

const (
	UserIdTypeCreateRoomUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypeCreateRoomUnionId = "union_id" // 以union_id来识别用户
	UserIdTypeCreateRoomOpenId  = "open_id"  // 以open_id来识别用户（推荐）
)

const (
	UserIdTypeGetRoomUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypeGetRoomUnionId = "union_id" // 以union_id来识别用户
	UserIdTypeGetRoomOpenId  = "open_id"  // 以open_id来识别用户（推荐）
)

const (
	UserIdTypeListRoomUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypeListRoomUnionId = "union_id" // 以union_id来识别用户
	UserIdTypeListRoomOpenId  = "open_id"  // 以open_id来识别用户（推荐）
)

const (
	UserIdTypeMgetRoomUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypeMgetRoomUnionId = "union_id" // 以union_id来识别用户
	UserIdTypeMgetRoomOpenId  = "open_id"  // 以open_id来识别用户（推荐）
)

const (
	UserIdTypePatchRoomUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypePatchRoomUnionId = "union_id" // 以union_id来识别用户
	UserIdTypePatchRoomOpenId  = "open_id"  // 以open_id来识别用户（推荐）
)

const (
	UserIdTypeSearchRoomUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypeSearchRoomUnionId = "union_id" // 以union_id来识别用户
	UserIdTypeSearchRoomOpenId  = "open_id"  // 以open_id来识别用户（推荐）
)

const (
	NodeScopeTenant          = 1 // 租户
	NodeScopeCountryDistrict = 2 // 国家/地区
	NodeScopeCity            = 3 // 城市
	NodeScopeBuilding        = 4 // 建筑
	NodeScopeFloor           = 5 // 楼层
	NodeScopeRoom            = 6 // 会议室

)

const (
	UserIdTypeQueryRoomConfigUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypeQueryRoomConfigUnionId = "union_id" // 以union_id来识别用户
	UserIdTypeQueryRoomConfigOpenId  = "open_id"  // 以open_id来识别用户（推荐）
)

const (
	NodeScopeSetRoomConfigTenant          = 1 // 租户
	NodeScopeSetRoomConfigCountryDistrict = 2 // 国家/地区
	NodeScopeSetRoomConfigCity            = 3 // 城市
	NodeScopeSetRoomConfigBuilding        = 4 // 建筑
	NodeScopeSetRoomConfigFloor           = 5 // 楼层
	NodeScopeSetRoomConfigRoom            = 6 // 会议室

)

const (
	UserIdTypeSetRoomConfigUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypeSetRoomConfigUnionId = "union_id" // 以union_id来识别用户
	UserIdTypeSetRoomConfigOpenId  = "open_id"  // 以open_id来识别用户（推荐）
)

const (
	ScopeTypeRoomLevel = 1 // 会议室层级
	ScopeTypeRoom      = 2 // 会议室

)

const (
	UserIdTypeCreateScopeConfigUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypeCreateScopeConfigUnionId = "union_id" // 以union_id来识别用户
	UserIdTypeCreateScopeConfigOpenId  = "open_id"  // 以open_id来识别用户（推荐）
)

const (
	ScopeTypeGetScopeConfigRoomLevel = 1 // 会议室层级
	ScopeTypeGetScopeConfigRoom      = 2 // 会议室

)

const (
	UserIdTypeGetScopeConfigUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypeGetScopeConfigUnionId = "union_id" // 以union_id来识别用户
	UserIdTypeGetScopeConfigOpenId  = "open_id"  // 以open_id来识别用户（推荐）
)

type Alert struct {
	AlertId       *string    `json:"alert_id,omitempty"`       // 告警ID
	ResourceScope *string    `json:"resource_scope,omitempty"` // 触发告警规则的会议室/服务器具体的名称
	MonitorTarget *int       `json:"monitor_target,omitempty"` // 触发告警规则的监控对象
	AlertStrategy *string    `json:"alert_strategy,omitempty"` // 告警规则的规则描述
	AlertTime     *string    `json:"alert_time,omitempty"`     // 告警通知发生时间（unix时间，单位sec）
	AlertLevel    *int       `json:"alert_level,omitempty"`    // 告警等级：严重/警告/提醒
	Contacts      []*Contact `json:"contacts,omitempty"`       // 告警联系人
	NotifyMethods []int      `json:"notifyMethods,omitempty"`  // 通知方式
	AlertRule     *string    `json:"alertRule,omitempty"`      // 规则名称
}

type AlertBuilder struct {
	alertId           string // 告警ID
	alertIdFlag       bool
	resourceScope     string // 触发告警规则的会议室/服务器具体的名称
	resourceScopeFlag bool
	monitorTarget     int // 触发告警规则的监控对象
	monitorTargetFlag bool
	alertStrategy     string // 告警规则的规则描述
	alertStrategyFlag bool
	alertTime         string // 告警通知发生时间（unix时间，单位sec）
	alertTimeFlag     bool
	alertLevel        int // 告警等级：严重/警告/提醒
	alertLevelFlag    bool
	contacts          []*Contact // 告警联系人
	contactsFlag      bool
	notifyMethods     []int // 通知方式
	notifyMethodsFlag bool
	alertRule         string // 规则名称
	alertRuleFlag     bool
}

func NewAlertBuilder() *AlertBuilder {
	builder := &AlertBuilder{}
	return builder
}

// 告警ID
//
// 示例值：7115030004018184212
func (builder *AlertBuilder) AlertId(alertId string) *AlertBuilder {
	builder.alertId = alertId
	builder.alertIdFlag = true
	return builder
}

// 触发告警规则的会议室/服务器具体的名称
//
// 示例值：XX层级
func (builder *AlertBuilder) ResourceScope(resourceScope string) *AlertBuilder {
	builder.resourceScope = resourceScope
	builder.resourceScopeFlag = true
	return builder
}

// 触发告警规则的监控对象
//
// 示例值：2
func (builder *AlertBuilder) MonitorTarget(monitorTarget int) *AlertBuilder {
	builder.monitorTarget = monitorTarget
	builder.monitorTargetFlag = true
	return builder
}

// 告警规则的规则描述
//
// 示例值：连续1个周期（共1分钟），控制器电量 < 50%，则告警
func (builder *AlertBuilder) AlertStrategy(alertStrategy string) *AlertBuilder {
	builder.alertStrategy = alertStrategy
	builder.alertStrategyFlag = true
	return builder
}

// 告警通知发生时间（unix时间，单位sec）
//
// 示例值：1656914944
func (builder *AlertBuilder) AlertTime(alertTime string) *AlertBuilder {
	builder.alertTime = alertTime
	builder.alertTimeFlag = true
	return builder
}

// 告警等级：严重/警告/提醒
//
// 示例值：2
func (builder *AlertBuilder) AlertLevel(alertLevel int) *AlertBuilder {
	builder.alertLevel = alertLevel
	builder.alertLevelFlag = true
	return builder
}

// 告警联系人
//
// 示例值：
func (builder *AlertBuilder) Contacts(contacts []*Contact) *AlertBuilder {
	builder.contacts = contacts
	builder.contactsFlag = true
	return builder
}

// 通知方式
//
// 示例值：[0,1]
func (builder *AlertBuilder) NotifyMethods(notifyMethods []int) *AlertBuilder {
	builder.notifyMethods = notifyMethods
	builder.notifyMethodsFlag = true
	return builder
}

// 规则名称
//
// 示例值：签到板断开连接
func (builder *AlertBuilder) AlertRule(alertRule string) *AlertBuilder {
	builder.alertRule = alertRule
	builder.alertRuleFlag = true
	return builder
}

func (builder *AlertBuilder) Build() *Alert {
	req := &Alert{}
	if builder.alertIdFlag {
		req.AlertId = &builder.alertId

	}
	if builder.resourceScopeFlag {
		req.ResourceScope = &builder.resourceScope

	}
	if builder.monitorTargetFlag {
		req.MonitorTarget = &builder.monitorTarget

	}
	if builder.alertStrategyFlag {
		req.AlertStrategy = &builder.alertStrategy

	}
	if builder.alertTimeFlag {
		req.AlertTime = &builder.alertTime

	}
	if builder.alertLevelFlag {
		req.AlertLevel = &builder.alertLevel

	}
	if builder.contactsFlag {
		req.Contacts = builder.contacts
	}
	if builder.notifyMethodsFlag {
		req.NotifyMethods = builder.notifyMethods
	}
	if builder.alertRuleFlag {
		req.AlertRule = &builder.alertRule

	}
	return req
}

type ApprovalConfig struct {
	ApprovalSwitch    *int             `json:"approval_switch,omitempty"`    // 预定审批开关：0 代表关闭，1 代表打开。;<b>说明</b>：;1.  未设置值时不更新原开关的值，但此时必填  approval_condition;2.  设置值为 1 时，必填  approval_condition<br>								 ;3.  设置值为 0 时整个 ;approval_config 其他字段均可省略。
	ApprovalCondition *int             `json:"approval_condition,omitempty"` // 预定审批条件：0 代表所有预定均需审批，1 代表满足条件的需审批;<b>说明</b>：为 1 时必填 meeting_duration
	MeetingDuration   *float64         `json:"meeting_duration,omitempty"`   // 超过 meeting_duration;的预定需要审批（单位：小时，取值范围[0.1-99]）;;<b>说明</b>：;1.  当 approval_condition ; 为 0 ，更新时如果未设置值，默认更新为 99 .;2.  传入的值小数点后超过 2 位，自动四舍五入保留两位。
	Approvers         []*SubscribeUser `json:"approvers,omitempty"`          // 审批人列表，当打开审批开关时，至少需要设置一位审批人
}

type ApprovalConfigBuilder struct {
	approvalSwitch        int // 预定审批开关：0 代表关闭，1 代表打开。;<b>说明</b>：;1.  未设置值时不更新原开关的值，但此时必填  approval_condition;2.  设置值为 1 时，必填  approval_condition<br>								 ;3.  设置值为 0 时整个 ;approval_config 其他字段均可省略。
	approvalSwitchFlag    bool
	approvalCondition     int // 预定审批条件：0 代表所有预定均需审批，1 代表满足条件的需审批;<b>说明</b>：为 1 时必填 meeting_duration
	approvalConditionFlag bool
	meetingDuration       float64 // 超过 meeting_duration;的预定需要审批（单位：小时，取值范围[0.1-99]）;;<b>说明</b>：;1.  当 approval_condition ; 为 0 ，更新时如果未设置值，默认更新为 99 .;2.  传入的值小数点后超过 2 位，自动四舍五入保留两位。
	meetingDurationFlag   bool
	approvers             []*SubscribeUser // 审批人列表，当打开审批开关时，至少需要设置一位审批人
	approversFlag         bool
}

func NewApprovalConfigBuilder() *ApprovalConfigBuilder {
	builder := &ApprovalConfigBuilder{}
	return builder
}

// 预定审批开关：0 代表关闭，1 代表打开。;<b>说明</b>：;1.  未设置值时不更新原开关的值，但此时必填  approval_condition;2.  设置值为 1 时，必填  approval_condition<br>								 ;3.  设置值为 0 时整个 ;approval_config 其他字段均可省略。
//
// 示例值：1
func (builder *ApprovalConfigBuilder) ApprovalSwitch(approvalSwitch int) *ApprovalConfigBuilder {
	builder.approvalSwitch = approvalSwitch
	builder.approvalSwitchFlag = true
	return builder
}

// 预定审批条件：0 代表所有预定均需审批，1 代表满足条件的需审批;<b>说明</b>：为 1 时必填 meeting_duration
//
// 示例值：1
func (builder *ApprovalConfigBuilder) ApprovalCondition(approvalCondition int) *ApprovalConfigBuilder {
	builder.approvalCondition = approvalCondition
	builder.approvalConditionFlag = true
	return builder
}

// 超过 meeting_duration;的预定需要审批（单位：小时，取值范围[0.1-99]）;;<b>说明</b>：;1.  当 approval_condition ; 为 0 ，更新时如果未设置值，默认更新为 99 .;2.  传入的值小数点后超过 2 位，自动四舍五入保留两位。
//
// 示例值：3
func (builder *ApprovalConfigBuilder) MeetingDuration(meetingDuration float64) *ApprovalConfigBuilder {
	builder.meetingDuration = meetingDuration
	builder.meetingDurationFlag = true
	return builder
}

// 审批人列表，当打开审批开关时，至少需要设置一位审批人
//
// 示例值：[{user_id:"ou_e8bce6c3935ef1fc1b432992fd9d3db8"}]
func (builder *ApprovalConfigBuilder) Approvers(approvers []*SubscribeUser) *ApprovalConfigBuilder {
	builder.approvers = approvers
	builder.approversFlag = true
	return builder
}

func (builder *ApprovalConfigBuilder) Build() *ApprovalConfig {
	req := &ApprovalConfig{}
	if builder.approvalSwitchFlag {
		req.ApprovalSwitch = &builder.approvalSwitch

	}
	if builder.approvalConditionFlag {
		req.ApprovalCondition = &builder.approvalCondition

	}
	if builder.meetingDurationFlag {
		req.MeetingDuration = &builder.meetingDuration

	}
	if builder.approversFlag {
		req.Approvers = builder.approvers
	}
	return req
}

type Contact struct {
	ContactType *int    `json:"contact_type,omitempty"` // 联系人类型
	ContactName *string `json:"contact_name,omitempty"` // 联系人名
}

type ContactBuilder struct {
	contactType     int // 联系人类型
	contactTypeFlag bool
	contactName     string // 联系人名
	contactNameFlag bool
}

func NewContactBuilder() *ContactBuilder {
	builder := &ContactBuilder{}
	return builder
}

// 联系人类型
//
// 示例值：1
func (builder *ContactBuilder) ContactType(contactType int) *ContactBuilder {
	builder.contactType = contactType
	builder.contactTypeFlag = true
	return builder
}

// 联系人名
//
// 示例值：张三
func (builder *ContactBuilder) ContactName(contactName string) *ContactBuilder {
	builder.contactName = contactName
	builder.contactNameFlag = true
	return builder
}

func (builder *ContactBuilder) Build() *Contact {
	req := &Contact{}
	if builder.contactTypeFlag {
		req.ContactType = &builder.contactType

	}
	if builder.contactNameFlag {
		req.ContactName = &builder.contactName

	}
	return req
}

type Material struct {
	Name           *string `json:"name,omitempty"`            // 素材名称
	FileToken      *string `json:"file_token,omitempty"`      // 文件上传drive后的token
	FileSize       *int    `json:"file_size,omitempty"`       // 文件大小(KB)
	DeviceType     *int    `json:"device_type,omitempty"`     // 素材适用设备类型
	MaterialType   *int    `json:"material_type,omitempty"`   // 素材类型
	ReviewResult   *int    `json:"review_result,omitempty"`   // 审核结果
	MaterialSource *int    `json:"material_source,omitempty"` // 素材来源
}

type MaterialBuilder struct {
	name               string // 素材名称
	nameFlag           bool
	fileToken          string // 文件上传drive后的token
	fileTokenFlag      bool
	fileSize           int // 文件大小(KB)
	fileSizeFlag       bool
	deviceType         int // 素材适用设备类型
	deviceTypeFlag     bool
	materialType       int // 素材类型
	materialTypeFlag   bool
	reviewResult       int // 审核结果
	reviewResultFlag   bool
	materialSource     int // 素材来源
	materialSourceFlag bool
}

func NewMaterialBuilder() *MaterialBuilder {
	builder := &MaterialBuilder{}
	return builder
}

// 素材名称
//
// 示例值：green
func (builder *MaterialBuilder) Name(name string) *MaterialBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

// 文件上传drive后的token
//
// 示例值：u8ajdjadau8wqu
func (builder *MaterialBuilder) FileToken(fileToken string) *MaterialBuilder {
	builder.fileToken = fileToken
	builder.fileTokenFlag = true
	return builder
}

// 文件大小(KB)
//
// 示例值：1024
func (builder *MaterialBuilder) FileSize(fileSize int) *MaterialBuilder {
	builder.fileSize = fileSize
	builder.fileSizeFlag = true
	return builder
}

// 素材适用设备类型
//
// 示例值：2
func (builder *MaterialBuilder) DeviceType(deviceType int) *MaterialBuilder {
	builder.deviceType = deviceType
	builder.deviceTypeFlag = true
	return builder
}

// 素材类型
//
// 示例值：2
func (builder *MaterialBuilder) MaterialType(materialType int) *MaterialBuilder {
	builder.materialType = materialType
	builder.materialTypeFlag = true
	return builder
}

// 审核结果
//
// 示例值：2
func (builder *MaterialBuilder) ReviewResult(reviewResult int) *MaterialBuilder {
	builder.reviewResult = reviewResult
	builder.reviewResultFlag = true
	return builder
}

// 素材来源
//
// 示例值：2
func (builder *MaterialBuilder) MaterialSource(materialSource int) *MaterialBuilder {
	builder.materialSource = materialSource
	builder.materialSourceFlag = true
	return builder
}

func (builder *MaterialBuilder) Build() *Material {
	req := &Material{}
	if builder.nameFlag {
		req.Name = &builder.name

	}
	if builder.fileTokenFlag {
		req.FileToken = &builder.fileToken

	}
	if builder.fileSizeFlag {
		req.FileSize = &builder.fileSize

	}
	if builder.deviceTypeFlag {
		req.DeviceType = &builder.deviceType

	}
	if builder.materialTypeFlag {
		req.MaterialType = &builder.materialType

	}
	if builder.reviewResultFlag {
		req.ReviewResult = &builder.reviewResult

	}
	if builder.materialSourceFlag {
		req.MaterialSource = &builder.materialSource

	}
	return req
}

type MaterialDeleteResult struct {
	FileToken *string `json:"file_token,omitempty"` // 文件上传drive后的token
	Result    *int    `json:"result,omitempty"`     // 删除结果
}

type MaterialDeleteResultBuilder struct {
	fileToken     string // 文件上传drive后的token
	fileTokenFlag bool
	result        int // 删除结果
	resultFlag    bool
}

func NewMaterialDeleteResultBuilder() *MaterialDeleteResultBuilder {
	builder := &MaterialDeleteResultBuilder{}
	return builder
}

// 文件上传drive后的token
//
// 示例值：u8ajdjadau8wqu
func (builder *MaterialDeleteResultBuilder) FileToken(fileToken string) *MaterialDeleteResultBuilder {
	builder.fileToken = fileToken
	builder.fileTokenFlag = true
	return builder
}

// 删除结果
//
// 示例值：1
func (builder *MaterialDeleteResultBuilder) Result(result int) *MaterialDeleteResultBuilder {
	builder.result = result
	builder.resultFlag = true
	return builder
}

func (builder *MaterialDeleteResultBuilder) Build() *MaterialDeleteResult {
	req := &MaterialDeleteResult{}
	if builder.fileTokenFlag {
		req.FileToken = &builder.fileToken

	}
	if builder.resultFlag {
		req.Result = &builder.result

	}
	return req
}

type MaterialReviewResult struct {
	FileToken *string `json:"file_token,omitempty"` // 文件上传drive后的token
	Result    *int    `json:"result,omitempty"`     // 审核结果
}

type MaterialReviewResultBuilder struct {
	fileToken     string // 文件上传drive后的token
	fileTokenFlag bool
	result        int // 审核结果
	resultFlag    bool
}

func NewMaterialReviewResultBuilder() *MaterialReviewResultBuilder {
	builder := &MaterialReviewResultBuilder{}
	return builder
}

// 文件上传drive后的token
//
// 示例值：u8ajdjadau8wqu
func (builder *MaterialReviewResultBuilder) FileToken(fileToken string) *MaterialReviewResultBuilder {
	builder.fileToken = fileToken
	builder.fileTokenFlag = true
	return builder
}

// 审核结果
//
// 示例值：1
func (builder *MaterialReviewResultBuilder) Result(result int) *MaterialReviewResultBuilder {
	builder.result = result
	builder.resultFlag = true
	return builder
}

func (builder *MaterialReviewResultBuilder) Build() *MaterialReviewResult {
	req := &MaterialReviewResult{}
	if builder.fileTokenFlag {
		req.FileToken = &builder.fileToken

	}
	if builder.resultFlag {
		req.Result = &builder.result

	}
	return req
}

type MaterialUploadResult struct {
	FileToken *string `json:"file_token,omitempty"` // 文件上传drive后的token
	Result    *int    `json:"result,omitempty"`     // 上传结果
}

type MaterialUploadResultBuilder struct {
	fileToken     string // 文件上传drive后的token
	fileTokenFlag bool
	result        int // 上传结果
	resultFlag    bool
}

func NewMaterialUploadResultBuilder() *MaterialUploadResultBuilder {
	builder := &MaterialUploadResultBuilder{}
	return builder
}

// 文件上传drive后的token
//
// 示例值：u8ajdjadau8wqu
func (builder *MaterialUploadResultBuilder) FileToken(fileToken string) *MaterialUploadResultBuilder {
	builder.fileToken = fileToken
	builder.fileTokenFlag = true
	return builder
}

// 上传结果
//
// 示例值：1
func (builder *MaterialUploadResultBuilder) Result(result int) *MaterialUploadResultBuilder {
	builder.result = result
	builder.resultFlag = true
	return builder
}

func (builder *MaterialUploadResultBuilder) Build() *MaterialUploadResult {
	req := &MaterialUploadResult{}
	if builder.fileTokenFlag {
		req.FileToken = &builder.fileToken

	}
	if builder.resultFlag {
		req.Result = &builder.result

	}
	return req
}

type Meeting struct {
	Id                          *string               `json:"id,omitempty"`                            // 会议ID（视频会议的唯一标识，视频会议开始后才会产生）
	Topic                       *string               `json:"topic,omitempty"`                         // 会议主题
	Url                         *string               `json:"url,omitempty"`                           // 会议链接（飞书用户可通过点击会议链接快捷入会）
	MeetingNo                   *string               `json:"meeting_no,omitempty"`                    // 会议号
	CreateTime                  *string               `json:"create_time,omitempty"`                   // 会议创建时间（unix时间，单位sec）
	StartTime                   *string               `json:"start_time,omitempty"`                    // 会议开始时间（unix时间，单位sec）
	EndTime                     *string               `json:"end_time,omitempty"`                      // 会议结束时间（unix时间，单位sec）
	HostUser                    *MeetingUser          `json:"host_user,omitempty"`                     // 主持人
	Status                      *int                  `json:"status,omitempty"`                        // 会议状态
	ParticipantCount            *string               `json:"participant_count,omitempty"`             // 参会人数
	ParticipantCountAccumulated *string               `json:"participant_count_accumulated,omitempty"` // 累计参会人数
	Participants                []*MeetingParticipant `json:"participants,omitempty"`                  // 参会人列表
	Ability                     *MeetingAbility       `json:"ability,omitempty"`                       // 会中使用的能力
}

type MeetingBuilder struct {
	id                              string // 会议ID（视频会议的唯一标识，视频会议开始后才会产生）
	idFlag                          bool
	topic                           string // 会议主题
	topicFlag                       bool
	url                             string // 会议链接（飞书用户可通过点击会议链接快捷入会）
	urlFlag                         bool
	meetingNo                       string // 会议号
	meetingNoFlag                   bool
	createTime                      string // 会议创建时间（unix时间，单位sec）
	createTimeFlag                  bool
	startTime                       string // 会议开始时间（unix时间，单位sec）
	startTimeFlag                   bool
	endTime                         string // 会议结束时间（unix时间，单位sec）
	endTimeFlag                     bool
	hostUser                        *MeetingUser // 主持人
	hostUserFlag                    bool
	status                          int // 会议状态
	statusFlag                      bool
	participantCount                string // 参会人数
	participantCountFlag            bool
	participantCountAccumulated     string // 累计参会人数
	participantCountAccumulatedFlag bool
	participants                    []*MeetingParticipant // 参会人列表
	participantsFlag                bool
	ability                         *MeetingAbility // 会中使用的能力
	abilityFlag                     bool
}

func NewMeetingBuilder() *MeetingBuilder {
	builder := &MeetingBuilder{}
	return builder
}

// 会议ID（视频会议的唯一标识，视频会议开始后才会产生）
//
// 示例值：6911188411934433028
func (builder *MeetingBuilder) Id(id string) *MeetingBuilder {
	builder.id = id
	builder.idFlag = true
	return builder
}

// 会议主题
//
// 示例值：my meeting
func (builder *MeetingBuilder) Topic(topic string) *MeetingBuilder {
	builder.topic = topic
	builder.topicFlag = true
	return builder
}

// 会议链接（飞书用户可通过点击会议链接快捷入会）
//
// 示例值：https://vc.feishu.cn/j/337736498
func (builder *MeetingBuilder) Url(url string) *MeetingBuilder {
	builder.url = url
	builder.urlFlag = true
	return builder
}

// 会议号
//
// 示例值：123456789
func (builder *MeetingBuilder) MeetingNo(meetingNo string) *MeetingBuilder {
	builder.meetingNo = meetingNo
	builder.meetingNoFlag = true
	return builder
}

// 会议创建时间（unix时间，单位sec）
//
// 示例值：1608885566
func (builder *MeetingBuilder) CreateTime(createTime string) *MeetingBuilder {
	builder.createTime = createTime
	builder.createTimeFlag = true
	return builder
}

// 会议开始时间（unix时间，单位sec）
//
// 示例值：1608883322
func (builder *MeetingBuilder) StartTime(startTime string) *MeetingBuilder {
	builder.startTime = startTime
	builder.startTimeFlag = true
	return builder
}

// 会议结束时间（unix时间，单位sec）
//
// 示例值：1608888867
func (builder *MeetingBuilder) EndTime(endTime string) *MeetingBuilder {
	builder.endTime = endTime
	builder.endTimeFlag = true
	return builder
}

// 主持人
//
// 示例值：
func (builder *MeetingBuilder) HostUser(hostUser *MeetingUser) *MeetingBuilder {
	builder.hostUser = hostUser
	builder.hostUserFlag = true
	return builder
}

// 会议状态
//
// 示例值：2
func (builder *MeetingBuilder) Status(status int) *MeetingBuilder {
	builder.status = status
	builder.statusFlag = true
	return builder
}

// 参会人数
//
// 示例值：10
func (builder *MeetingBuilder) ParticipantCount(participantCount string) *MeetingBuilder {
	builder.participantCount = participantCount
	builder.participantCountFlag = true
	return builder
}

// 累计参会人数
//
// 示例值：10
func (builder *MeetingBuilder) ParticipantCountAccumulated(participantCountAccumulated string) *MeetingBuilder {
	builder.participantCountAccumulated = participantCountAccumulated
	builder.participantCountAccumulatedFlag = true
	return builder
}

// 参会人列表
//
// 示例值：
func (builder *MeetingBuilder) Participants(participants []*MeetingParticipant) *MeetingBuilder {
	builder.participants = participants
	builder.participantsFlag = true
	return builder
}

// 会中使用的能力
//
// 示例值：
func (builder *MeetingBuilder) Ability(ability *MeetingAbility) *MeetingBuilder {
	builder.ability = ability
	builder.abilityFlag = true
	return builder
}

func (builder *MeetingBuilder) Build() *Meeting {
	req := &Meeting{}
	if builder.idFlag {
		req.Id = &builder.id

	}
	if builder.topicFlag {
		req.Topic = &builder.topic

	}
	if builder.urlFlag {
		req.Url = &builder.url

	}
	if builder.meetingNoFlag {
		req.MeetingNo = &builder.meetingNo

	}
	if builder.createTimeFlag {
		req.CreateTime = &builder.createTime

	}
	if builder.startTimeFlag {
		req.StartTime = &builder.startTime

	}
	if builder.endTimeFlag {
		req.EndTime = &builder.endTime

	}
	if builder.hostUserFlag {
		req.HostUser = builder.hostUser
	}
	if builder.statusFlag {
		req.Status = &builder.status

	}
	if builder.participantCountFlag {
		req.ParticipantCount = &builder.participantCount

	}
	if builder.participantCountAccumulatedFlag {
		req.ParticipantCountAccumulated = &builder.participantCountAccumulated

	}
	if builder.participantsFlag {
		req.Participants = builder.participants
	}
	if builder.abilityFlag {
		req.Ability = builder.ability
	}
	return req
}

type MeetingRecording struct {
	Id        *string `json:"id,omitempty"`         // 录制ID
	MeetingId *string `json:"meeting_id,omitempty"` // 会议ID
	Url       *string `json:"url,omitempty"`        // 录制文件URL
	Duration  *string `json:"duration,omitempty"`   // 录制总时长（单位msec）
}

type MeetingRecordingBuilder struct {
	id            string // 录制ID
	idFlag        bool
	meetingId     string // 会议ID
	meetingIdFlag bool
	url           string // 录制文件URL
	urlFlag       bool
	duration      string // 录制总时长（单位msec）
	durationFlag  bool
}

func NewMeetingRecordingBuilder() *MeetingRecordingBuilder {
	builder := &MeetingRecordingBuilder{}
	return builder
}

// 录制ID
//
// 示例值：6911188411932033028
func (builder *MeetingRecordingBuilder) Id(id string) *MeetingRecordingBuilder {
	builder.id = id
	builder.idFlag = true
	return builder
}

// 会议ID
//
// 示例值：6911188411932033028
func (builder *MeetingRecordingBuilder) MeetingId(meetingId string) *MeetingRecordingBuilder {
	builder.meetingId = meetingId
	builder.meetingIdFlag = true
	return builder
}

// 录制文件URL
//
// 示例值：https://meetings.feishu.cn/minutes/obcn37dxcftoc3656rgyejm7
func (builder *MeetingRecordingBuilder) Url(url string) *MeetingRecordingBuilder {
	builder.url = url
	builder.urlFlag = true
	return builder
}

// 录制总时长（单位msec）
//
// 示例值：30000
func (builder *MeetingRecordingBuilder) Duration(duration string) *MeetingRecordingBuilder {
	builder.duration = duration
	builder.durationFlag = true
	return builder
}

func (builder *MeetingRecordingBuilder) Build() *MeetingRecording {
	req := &MeetingRecording{}
	if builder.idFlag {
		req.Id = &builder.id

	}
	if builder.meetingIdFlag {
		req.MeetingId = &builder.meetingId

	}
	if builder.urlFlag {
		req.Url = &builder.url

	}
	if builder.durationFlag {
		req.Duration = &builder.duration

	}
	return req
}

type MeetingAbility struct {
	UseVideo        *bool `json:"use_video,omitempty"`         // 是否使用视频
	UseAudio        *bool `json:"use_audio,omitempty"`         // 是否使用音频
	UseShareScreen  *bool `json:"use_share_screen,omitempty"`  // 是否使用共享屏幕
	UseFollowScreen *bool `json:"use_follow_screen,omitempty"` // 是否使用妙享（magic share）
	UseRecording    *bool `json:"use_recording,omitempty"`     // 是否使用录制
	UsePstn         *bool `json:"use_pstn,omitempty"`          // 是否使用PSTN
}

type MeetingAbilityBuilder struct {
	useVideo            bool // 是否使用视频
	useVideoFlag        bool
	useAudio            bool // 是否使用音频
	useAudioFlag        bool
	useShareScreen      bool // 是否使用共享屏幕
	useShareScreenFlag  bool
	useFollowScreen     bool // 是否使用妙享（magic share）
	useFollowScreenFlag bool
	useRecording        bool // 是否使用录制
	useRecordingFlag    bool
	usePstn             bool // 是否使用PSTN
	usePstnFlag         bool
}

func NewMeetingAbilityBuilder() *MeetingAbilityBuilder {
	builder := &MeetingAbilityBuilder{}
	return builder
}

// 是否使用视频
//
// 示例值：true
func (builder *MeetingAbilityBuilder) UseVideo(useVideo bool) *MeetingAbilityBuilder {
	builder.useVideo = useVideo
	builder.useVideoFlag = true
	return builder
}

// 是否使用音频
//
// 示例值：true
func (builder *MeetingAbilityBuilder) UseAudio(useAudio bool) *MeetingAbilityBuilder {
	builder.useAudio = useAudio
	builder.useAudioFlag = true
	return builder
}

// 是否使用共享屏幕
//
// 示例值：true
func (builder *MeetingAbilityBuilder) UseShareScreen(useShareScreen bool) *MeetingAbilityBuilder {
	builder.useShareScreen = useShareScreen
	builder.useShareScreenFlag = true
	return builder
}

// 是否使用妙享（magic share）
//
// 示例值：true
func (builder *MeetingAbilityBuilder) UseFollowScreen(useFollowScreen bool) *MeetingAbilityBuilder {
	builder.useFollowScreen = useFollowScreen
	builder.useFollowScreenFlag = true
	return builder
}

// 是否使用录制
//
// 示例值：true
func (builder *MeetingAbilityBuilder) UseRecording(useRecording bool) *MeetingAbilityBuilder {
	builder.useRecording = useRecording
	builder.useRecordingFlag = true
	return builder
}

// 是否使用PSTN
//
// 示例值：true
func (builder *MeetingAbilityBuilder) UsePstn(usePstn bool) *MeetingAbilityBuilder {
	builder.usePstn = usePstn
	builder.usePstnFlag = true
	return builder
}

func (builder *MeetingAbilityBuilder) Build() *MeetingAbility {
	req := &MeetingAbility{}
	if builder.useVideoFlag {
		req.UseVideo = &builder.useVideo

	}
	if builder.useAudioFlag {
		req.UseAudio = &builder.useAudio

	}
	if builder.useShareScreenFlag {
		req.UseShareScreen = &builder.useShareScreen

	}
	if builder.useFollowScreenFlag {
		req.UseFollowScreen = &builder.useFollowScreen

	}
	if builder.useRecordingFlag {
		req.UseRecording = &builder.useRecording

	}
	if builder.usePstnFlag {
		req.UsePstn = &builder.usePstn

	}
	return req
}

type MeetingEventMeeting struct {
	Id        *string           `json:"id,omitempty"`         // 会议ID（视频会议的唯一标识，视频会议开始后才会产生）
	Topic     *string           `json:"topic,omitempty"`      // 会议主题
	MeetingNo *string           `json:"meeting_no,omitempty"` // 9位会议号（飞书用户可通过输入9位会议号快捷入会）
	StartTime *string           `json:"start_time,omitempty"` // 会议开始时间（unix时间，单位sec）
	EndTime   *string           `json:"end_time,omitempty"`   // 会议结束时间（unix时间，单位sec）
	HostUser  *MeetingEventUser `json:"host_user,omitempty"`  // 会议主持人
	Owner     *MeetingEventUser `json:"owner,omitempty"`      // 会议拥有者
}

type MeetingEventMeetingBuilder struct {
	id            string // 会议ID（视频会议的唯一标识，视频会议开始后才会产生）
	idFlag        bool
	topic         string // 会议主题
	topicFlag     bool
	meetingNo     string // 9位会议号（飞书用户可通过输入9位会议号快捷入会）
	meetingNoFlag bool
	startTime     string // 会议开始时间（unix时间，单位sec）
	startTimeFlag bool
	endTime       string // 会议结束时间（unix时间，单位sec）
	endTimeFlag   bool
	hostUser      *MeetingEventUser // 会议主持人
	hostUserFlag  bool
	owner         *MeetingEventUser // 会议拥有者
	ownerFlag     bool
}

func NewMeetingEventMeetingBuilder() *MeetingEventMeetingBuilder {
	builder := &MeetingEventMeetingBuilder{}
	return builder
}

// 会议ID（视频会议的唯一标识，视频会议开始后才会产生）
//
// 示例值：6911188411934433028
func (builder *MeetingEventMeetingBuilder) Id(id string) *MeetingEventMeetingBuilder {
	builder.id = id
	builder.idFlag = true
	return builder
}

// 会议主题
//
// 示例值：my meeting
func (builder *MeetingEventMeetingBuilder) Topic(topic string) *MeetingEventMeetingBuilder {
	builder.topic = topic
	builder.topicFlag = true
	return builder
}

// 9位会议号（飞书用户可通过输入9位会议号快捷入会）
//
// 示例值：235812466
func (builder *MeetingEventMeetingBuilder) MeetingNo(meetingNo string) *MeetingEventMeetingBuilder {
	builder.meetingNo = meetingNo
	builder.meetingNoFlag = true
	return builder
}

// 会议开始时间（unix时间，单位sec）
//
// 示例值：1608883322
func (builder *MeetingEventMeetingBuilder) StartTime(startTime string) *MeetingEventMeetingBuilder {
	builder.startTime = startTime
	builder.startTimeFlag = true
	return builder
}

// 会议结束时间（unix时间，单位sec）
//
// 示例值：1608883899
func (builder *MeetingEventMeetingBuilder) EndTime(endTime string) *MeetingEventMeetingBuilder {
	builder.endTime = endTime
	builder.endTimeFlag = true
	return builder
}

// 会议主持人
//
// 示例值：
func (builder *MeetingEventMeetingBuilder) HostUser(hostUser *MeetingEventUser) *MeetingEventMeetingBuilder {
	builder.hostUser = hostUser
	builder.hostUserFlag = true
	return builder
}

// 会议拥有者
//
// 示例值：
func (builder *MeetingEventMeetingBuilder) Owner(owner *MeetingEventUser) *MeetingEventMeetingBuilder {
	builder.owner = owner
	builder.ownerFlag = true
	return builder
}

func (builder *MeetingEventMeetingBuilder) Build() *MeetingEventMeeting {
	req := &MeetingEventMeeting{}
	if builder.idFlag {
		req.Id = &builder.id

	}
	if builder.topicFlag {
		req.Topic = &builder.topic

	}
	if builder.meetingNoFlag {
		req.MeetingNo = &builder.meetingNo

	}
	if builder.startTimeFlag {
		req.StartTime = &builder.startTime

	}
	if builder.endTimeFlag {
		req.EndTime = &builder.endTime

	}
	if builder.hostUserFlag {
		req.HostUser = builder.hostUser
	}
	if builder.ownerFlag {
		req.Owner = builder.owner
	}
	return req
}

type MeetingEventUser struct {
	Id       *UserId `json:"id,omitempty"`        // 用户 ID
	UserRole *int    `json:"user_role,omitempty"` // 用户会中角色
	UserType *int    `json:"user_type,omitempty"` // 用户类型
}

type MeetingEventUserBuilder struct {
	id           *UserId // 用户 ID
	idFlag       bool
	userRole     int // 用户会中角色
	userRoleFlag bool
	userType     int // 用户类型
	userTypeFlag bool
}

func NewMeetingEventUserBuilder() *MeetingEventUserBuilder {
	builder := &MeetingEventUserBuilder{}
	return builder
}

// 用户 ID
//
// 示例值：ou_3ec3f6a28a0d08c45d895276e8e5e19b
func (builder *MeetingEventUserBuilder) Id(id *UserId) *MeetingEventUserBuilder {
	builder.id = id
	builder.idFlag = true
	return builder
}

// 用户会中角色
//
// 示例值：1
func (builder *MeetingEventUserBuilder) UserRole(userRole int) *MeetingEventUserBuilder {
	builder.userRole = userRole
	builder.userRoleFlag = true
	return builder
}

// 用户类型
//
// 示例值：1
func (builder *MeetingEventUserBuilder) UserType(userType int) *MeetingEventUserBuilder {
	builder.userType = userType
	builder.userTypeFlag = true
	return builder
}

func (builder *MeetingEventUserBuilder) Build() *MeetingEventUser {
	req := &MeetingEventUser{}
	if builder.idFlag {
		req.Id = builder.id
	}
	if builder.userRoleFlag {
		req.UserRole = &builder.userRole

	}
	if builder.userTypeFlag {
		req.UserType = &builder.userType

	}
	return req
}

type MeetingInviteStatus struct {
	Id       *string `json:"id,omitempty"`        // 用户ID
	UserType *int    `json:"user_type,omitempty"` // 用户类型
	Status   *int    `json:"status,omitempty"`    // 邀请结果
}

type MeetingInviteStatusBuilder struct {
	id           string // 用户ID
	idFlag       bool
	userType     int // 用户类型
	userTypeFlag bool
	status       int // 邀请结果
	statusFlag   bool
}

func NewMeetingInviteStatusBuilder() *MeetingInviteStatusBuilder {
	builder := &MeetingInviteStatusBuilder{}
	return builder
}

// 用户ID
//
// 示例值：ou_3ec3f6a28a0d08c45d895276e8e5e19b
func (builder *MeetingInviteStatusBuilder) Id(id string) *MeetingInviteStatusBuilder {
	builder.id = id
	builder.idFlag = true
	return builder
}

// 用户类型
//
// 示例值：1
func (builder *MeetingInviteStatusBuilder) UserType(userType int) *MeetingInviteStatusBuilder {
	builder.userType = userType
	builder.userTypeFlag = true
	return builder
}

// 邀请结果
//
// 示例值：1
func (builder *MeetingInviteStatusBuilder) Status(status int) *MeetingInviteStatusBuilder {
	builder.status = status
	builder.statusFlag = true
	return builder
}

func (builder *MeetingInviteStatusBuilder) Build() *MeetingInviteStatus {
	req := &MeetingInviteStatus{}
	if builder.idFlag {
		req.Id = &builder.id

	}
	if builder.userTypeFlag {
		req.UserType = &builder.userType

	}
	if builder.statusFlag {
		req.Status = &builder.status

	}
	return req
}

type MeetingParticipant struct {
	Id                *string `json:"id,omitempty"`                  // 用户ID
	FirstJoinTime     *string `json:"first_join_time,omitempty"`     // 首次入会时间，秒级Unix时间戳
	FinalLeaveTime    *string `json:"final_leave_time,omitempty"`    // 最终离会时间，秒级Unix时间戳
	InMeetingDuration *string `json:"in_meeting_duration,omitempty"` // 累计在会中时间，时间单位：秒
	UserType          *int    `json:"user_type,omitempty"`           // 用户类型
	IsHost            *bool   `json:"is_host,omitempty"`             // 是否为主持人
	IsCohost          *bool   `json:"is_cohost,omitempty"`           // 是否为联席主持人
	IsExternal        *bool   `json:"is_external,omitempty"`         // 是否为外部参会人
	Status            *int    `json:"status,omitempty"`              // 参会人状态
}

type MeetingParticipantBuilder struct {
	id                    string // 用户ID
	idFlag                bool
	firstJoinTime         string // 首次入会时间，秒级Unix时间戳
	firstJoinTimeFlag     bool
	finalLeaveTime        string // 最终离会时间，秒级Unix时间戳
	finalLeaveTimeFlag    bool
	inMeetingDuration     string // 累计在会中时间，时间单位：秒
	inMeetingDurationFlag bool
	userType              int // 用户类型
	userTypeFlag          bool
	isHost                bool // 是否为主持人
	isHostFlag            bool
	isCohost              bool // 是否为联席主持人
	isCohostFlag          bool
	isExternal            bool // 是否为外部参会人
	isExternalFlag        bool
	status                int // 参会人状态
	statusFlag            bool
}

func NewMeetingParticipantBuilder() *MeetingParticipantBuilder {
	builder := &MeetingParticipantBuilder{}
	return builder
}

// 用户ID
//
// 示例值：ou_3ec3f6a28a0d08c45d895276e8e5e19b
func (builder *MeetingParticipantBuilder) Id(id string) *MeetingParticipantBuilder {
	builder.id = id
	builder.idFlag = true
	return builder
}

// 首次入会时间，秒级Unix时间戳
//
// 示例值：1624438144
func (builder *MeetingParticipantBuilder) FirstJoinTime(firstJoinTime string) *MeetingParticipantBuilder {
	builder.firstJoinTime = firstJoinTime
	builder.firstJoinTimeFlag = true
	return builder
}

// 最终离会时间，秒级Unix时间戳
//
// 示例值：1624438144
func (builder *MeetingParticipantBuilder) FinalLeaveTime(finalLeaveTime string) *MeetingParticipantBuilder {
	builder.finalLeaveTime = finalLeaveTime
	builder.finalLeaveTimeFlag = true
	return builder
}

// 累计在会中时间，时间单位：秒
//
// 示例值：123
func (builder *MeetingParticipantBuilder) InMeetingDuration(inMeetingDuration string) *MeetingParticipantBuilder {
	builder.inMeetingDuration = inMeetingDuration
	builder.inMeetingDurationFlag = true
	return builder
}

// 用户类型
//
// 示例值：1
func (builder *MeetingParticipantBuilder) UserType(userType int) *MeetingParticipantBuilder {
	builder.userType = userType
	builder.userTypeFlag = true
	return builder
}

// 是否为主持人
//
// 示例值：true
func (builder *MeetingParticipantBuilder) IsHost(isHost bool) *MeetingParticipantBuilder {
	builder.isHost = isHost
	builder.isHostFlag = true
	return builder
}

// 是否为联席主持人
//
// 示例值：false
func (builder *MeetingParticipantBuilder) IsCohost(isCohost bool) *MeetingParticipantBuilder {
	builder.isCohost = isCohost
	builder.isCohostFlag = true
	return builder
}

// 是否为外部参会人
//
// 示例值：false
func (builder *MeetingParticipantBuilder) IsExternal(isExternal bool) *MeetingParticipantBuilder {
	builder.isExternal = isExternal
	builder.isExternalFlag = true
	return builder
}

// 参会人状态
//
// 示例值：2
func (builder *MeetingParticipantBuilder) Status(status int) *MeetingParticipantBuilder {
	builder.status = status
	builder.statusFlag = true
	return builder
}

func (builder *MeetingParticipantBuilder) Build() *MeetingParticipant {
	req := &MeetingParticipant{}
	if builder.idFlag {
		req.Id = &builder.id

	}
	if builder.firstJoinTimeFlag {
		req.FirstJoinTime = &builder.firstJoinTime

	}
	if builder.finalLeaveTimeFlag {
		req.FinalLeaveTime = &builder.finalLeaveTime

	}
	if builder.inMeetingDurationFlag {
		req.InMeetingDuration = &builder.inMeetingDuration

	}
	if builder.userTypeFlag {
		req.UserType = &builder.userType

	}
	if builder.isHostFlag {
		req.IsHost = &builder.isHost

	}
	if builder.isCohostFlag {
		req.IsCohost = &builder.isCohost

	}
	if builder.isExternalFlag {
		req.IsExternal = &builder.isExternal

	}
	if builder.statusFlag {
		req.Status = &builder.status

	}
	return req
}

type MeetingParticipantResult struct {
	Id       *string `json:"id,omitempty"`        // 用户ID
	UserType *int    `json:"user_type,omitempty"` // 用户类型
	Result   *int    `json:"result,omitempty"`    // 移除结果
}

type MeetingParticipantResultBuilder struct {
	id           string // 用户ID
	idFlag       bool
	userType     int // 用户类型
	userTypeFlag bool
	result       int // 移除结果
	resultFlag   bool
}

func NewMeetingParticipantResultBuilder() *MeetingParticipantResultBuilder {
	builder := &MeetingParticipantResultBuilder{}
	return builder
}

// 用户ID
//
// 示例值：ou_3ec3f6a28a0d08c45d895276e8e5e19b
func (builder *MeetingParticipantResultBuilder) Id(id string) *MeetingParticipantResultBuilder {
	builder.id = id
	builder.idFlag = true
	return builder
}

// 用户类型
//
// 示例值：1
func (builder *MeetingParticipantResultBuilder) UserType(userType int) *MeetingParticipantResultBuilder {
	builder.userType = userType
	builder.userTypeFlag = true
	return builder
}

// 移除结果
//
// 示例值：1
func (builder *MeetingParticipantResultBuilder) Result(result int) *MeetingParticipantResultBuilder {
	builder.result = result
	builder.resultFlag = true
	return builder
}

func (builder *MeetingParticipantResultBuilder) Build() *MeetingParticipantResult {
	req := &MeetingParticipantResult{}
	if builder.idFlag {
		req.Id = &builder.id

	}
	if builder.userTypeFlag {
		req.UserType = &builder.userType

	}
	if builder.resultFlag {
		req.Result = &builder.result

	}
	return req
}

type MeetingUser struct {
	Id       *string `json:"id,omitempty"`        // 用户ID
	UserType *int    `json:"user_type,omitempty"` // 用户类型
}

type MeetingUserBuilder struct {
	id           string // 用户ID
	idFlag       bool
	userType     int // 用户类型
	userTypeFlag bool
}

func NewMeetingUserBuilder() *MeetingUserBuilder {
	builder := &MeetingUserBuilder{}
	return builder
}

// 用户ID
//
// 示例值：ou_3ec3f6a28a0d08c45d895276e8e5e19b
func (builder *MeetingUserBuilder) Id(id string) *MeetingUserBuilder {
	builder.id = id
	builder.idFlag = true
	return builder
}

// 用户类型
//
// 示例值：1
func (builder *MeetingUserBuilder) UserType(userType int) *MeetingUserBuilder {
	builder.userType = userType
	builder.userTypeFlag = true
	return builder
}

func (builder *MeetingUserBuilder) Build() *MeetingUser {
	req := &MeetingUser{}
	if builder.idFlag {
		req.Id = &builder.id

	}
	if builder.userTypeFlag {
		req.UserType = &builder.userType

	}
	return req
}

type PstnSipInfo struct {
	Nickname    *string `json:"nickname,omitempty"`     // 给pstn/sip用户设置的临时昵称
	MainAddress *string `json:"main_address,omitempty"` // pstn/sip主机号，格式为：[国际冠字]-[电话区号][电话号码]，当前仅支持国内手机及固定电话号码
}

type PstnSipInfoBuilder struct {
	nickname        string // 给pstn/sip用户设置的临时昵称
	nicknameFlag    bool
	mainAddress     string // pstn/sip主机号，格式为：[国际冠字]-[电话区号][电话号码]，当前仅支持国内手机及固定电话号码
	mainAddressFlag bool
}

func NewPstnSipInfoBuilder() *PstnSipInfoBuilder {
	builder := &PstnSipInfoBuilder{}
	return builder
}

// 给pstn/sip用户设置的临时昵称
//
// 示例值：dodo
func (builder *PstnSipInfoBuilder) Nickname(nickname string) *PstnSipInfoBuilder {
	builder.nickname = nickname
	builder.nicknameFlag = true
	return builder
}

// pstn/sip主机号，格式为：[国际冠字]-[电话区号][电话号码]，当前仅支持国内手机及固定电话号码
//
// 示例值：+86-02187654321
func (builder *PstnSipInfoBuilder) MainAddress(mainAddress string) *PstnSipInfoBuilder {
	builder.mainAddress = mainAddress
	builder.mainAddressFlag = true
	return builder
}

func (builder *PstnSipInfoBuilder) Build() *PstnSipInfo {
	req := &PstnSipInfo{}
	if builder.nicknameFlag {
		req.Nickname = &builder.nickname

	}
	if builder.mainAddressFlag {
		req.MainAddress = &builder.mainAddress

	}
	return req
}

type RecordingPermissionObject struct {
	Id         *string `json:"id,omitempty"`         // 授权对象ID
	Type       *int    `json:"type,omitempty"`       // 授权对象类型
	Permission *int    `json:"permission,omitempty"` // 权限
}

type RecordingPermissionObjectBuilder struct {
	id             string // 授权对象ID
	idFlag         bool
	type_          int // 授权对象类型
	typeFlag       bool
	permission     int // 权限
	permissionFlag bool
}

func NewRecordingPermissionObjectBuilder() *RecordingPermissionObjectBuilder {
	builder := &RecordingPermissionObjectBuilder{}
	return builder
}

// 授权对象ID
//
// 示例值：ou_3ec3f6a28a0d08c45d895276e8e5e19b
func (builder *RecordingPermissionObjectBuilder) Id(id string) *RecordingPermissionObjectBuilder {
	builder.id = id
	builder.idFlag = true
	return builder
}

// 授权对象类型
//
// 示例值：1
func (builder *RecordingPermissionObjectBuilder) Type(type_ int) *RecordingPermissionObjectBuilder {
	builder.type_ = type_
	builder.typeFlag = true
	return builder
}

// 权限
//
// 示例值：1
func (builder *RecordingPermissionObjectBuilder) Permission(permission int) *RecordingPermissionObjectBuilder {
	builder.permission = permission
	builder.permissionFlag = true
	return builder
}

func (builder *RecordingPermissionObjectBuilder) Build() *RecordingPermissionObject {
	req := &RecordingPermissionObject{}
	if builder.idFlag {
		req.Id = &builder.id

	}
	if builder.typeFlag {
		req.Type = &builder.type_

	}
	if builder.permissionFlag {
		req.Permission = &builder.permission

	}
	return req
}

type Report struct {
	TotalMeetingCount     *string               `json:"total_meeting_count,omitempty"`     // 总会议数量
	TotalMeetingDuration  *string               `json:"total_meeting_duration,omitempty"`  // 总会议时长（单位sec）
	TotalParticipantCount *string               `json:"total_participant_count,omitempty"` // 总参会人数
	DailyReport           []*ReportMeetingDaily `json:"daily_report,omitempty"`            // 每日会议报告列表
}

type ReportBuilder struct {
	totalMeetingCount         string // 总会议数量
	totalMeetingCountFlag     bool
	totalMeetingDuration      string // 总会议时长（单位sec）
	totalMeetingDurationFlag  bool
	totalParticipantCount     string // 总参会人数
	totalParticipantCountFlag bool
	dailyReport               []*ReportMeetingDaily // 每日会议报告列表
	dailyReportFlag           bool
}

func NewReportBuilder() *ReportBuilder {
	builder := &ReportBuilder{}
	return builder
}

// 总会议数量
//
// 示例值：100
func (builder *ReportBuilder) TotalMeetingCount(totalMeetingCount string) *ReportBuilder {
	builder.totalMeetingCount = totalMeetingCount
	builder.totalMeetingCountFlag = true
	return builder
}

// 总会议时长（单位sec）
//
// 示例值：300000
func (builder *ReportBuilder) TotalMeetingDuration(totalMeetingDuration string) *ReportBuilder {
	builder.totalMeetingDuration = totalMeetingDuration
	builder.totalMeetingDurationFlag = true
	return builder
}

// 总参会人数
//
// 示例值：20000
func (builder *ReportBuilder) TotalParticipantCount(totalParticipantCount string) *ReportBuilder {
	builder.totalParticipantCount = totalParticipantCount
	builder.totalParticipantCountFlag = true
	return builder
}

// 每日会议报告列表
//
// 示例值：
func (builder *ReportBuilder) DailyReport(dailyReport []*ReportMeetingDaily) *ReportBuilder {
	builder.dailyReport = dailyReport
	builder.dailyReportFlag = true
	return builder
}

func (builder *ReportBuilder) Build() *Report {
	req := &Report{}
	if builder.totalMeetingCountFlag {
		req.TotalMeetingCount = &builder.totalMeetingCount

	}
	if builder.totalMeetingDurationFlag {
		req.TotalMeetingDuration = &builder.totalMeetingDuration

	}
	if builder.totalParticipantCountFlag {
		req.TotalParticipantCount = &builder.totalParticipantCount

	}
	if builder.dailyReportFlag {
		req.DailyReport = builder.dailyReport
	}
	return req
}

type ReportMeetingDaily struct {
	Date             *string `json:"date,omitempty"`              // 日期（unix时间，单位sec）
	MeetingCount     *string `json:"meeting_count,omitempty"`     // 会议数量
	MeetingDuration  *string `json:"meeting_duration,omitempty"`  // 会议时长（单位sec）
	ParticipantCount *string `json:"participant_count,omitempty"` // 参会人数
}

type ReportMeetingDailyBuilder struct {
	date                 string // 日期（unix时间，单位sec）
	dateFlag             bool
	meetingCount         string // 会议数量
	meetingCountFlag     bool
	meetingDuration      string // 会议时长（单位sec）
	meetingDurationFlag  bool
	participantCount     string // 参会人数
	participantCountFlag bool
}

func NewReportMeetingDailyBuilder() *ReportMeetingDailyBuilder {
	builder := &ReportMeetingDailyBuilder{}
	return builder
}

// 日期（unix时间，单位sec）
//
// 示例值：1609113600
func (builder *ReportMeetingDailyBuilder) Date(date string) *ReportMeetingDailyBuilder {
	builder.date = date
	builder.dateFlag = true
	return builder
}

// 会议数量
//
// 示例值：100
func (builder *ReportMeetingDailyBuilder) MeetingCount(meetingCount string) *ReportMeetingDailyBuilder {
	builder.meetingCount = meetingCount
	builder.meetingCountFlag = true
	return builder
}

// 会议时长（单位sec）
//
// 示例值：147680
func (builder *ReportMeetingDailyBuilder) MeetingDuration(meetingDuration string) *ReportMeetingDailyBuilder {
	builder.meetingDuration = meetingDuration
	builder.meetingDurationFlag = true
	return builder
}

// 参会人数
//
// 示例值：2000
func (builder *ReportMeetingDailyBuilder) ParticipantCount(participantCount string) *ReportMeetingDailyBuilder {
	builder.participantCount = participantCount
	builder.participantCountFlag = true
	return builder
}

func (builder *ReportMeetingDailyBuilder) Build() *ReportMeetingDaily {
	req := &ReportMeetingDaily{}
	if builder.dateFlag {
		req.Date = &builder.date

	}
	if builder.meetingCountFlag {
		req.MeetingCount = &builder.meetingCount

	}
	if builder.meetingDurationFlag {
		req.MeetingDuration = &builder.meetingDuration

	}
	if builder.participantCountFlag {
		req.ParticipantCount = &builder.participantCount

	}
	return req
}

type ReportTopUser struct {
	Id              *string `json:"id,omitempty"`               // 用户ID
	Name            *string `json:"name,omitempty"`             // 用户名
	UserType        *int    `json:"user_type,omitempty"`        // 用户类型
	MeetingCount    *string `json:"meeting_count,omitempty"`    // 会议数量
	MeetingDuration *string `json:"meeting_duration,omitempty"` // 会议时长（单位min）
}

type ReportTopUserBuilder struct {
	id                  string // 用户ID
	idFlag              bool
	name                string // 用户名
	nameFlag            bool
	userType            int // 用户类型
	userTypeFlag        bool
	meetingCount        string // 会议数量
	meetingCountFlag    bool
	meetingDuration     string // 会议时长（单位min）
	meetingDurationFlag bool
}

func NewReportTopUserBuilder() *ReportTopUserBuilder {
	builder := &ReportTopUserBuilder{}
	return builder
}

// 用户ID
//
// 示例值：ou_3ec3f6a28a0d08c45d895276e8e5e19b
func (builder *ReportTopUserBuilder) Id(id string) *ReportTopUserBuilder {
	builder.id = id
	builder.idFlag = true
	return builder
}

// 用户名
//
// 示例值：name
func (builder *ReportTopUserBuilder) Name(name string) *ReportTopUserBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

// 用户类型
//
// 示例值：1
func (builder *ReportTopUserBuilder) UserType(userType int) *ReportTopUserBuilder {
	builder.userType = userType
	builder.userTypeFlag = true
	return builder
}

// 会议数量
//
// 示例值：100
func (builder *ReportTopUserBuilder) MeetingCount(meetingCount string) *ReportTopUserBuilder {
	builder.meetingCount = meetingCount
	builder.meetingCountFlag = true
	return builder
}

// 会议时长（单位min）
//
// 示例值：3000
func (builder *ReportTopUserBuilder) MeetingDuration(meetingDuration string) *ReportTopUserBuilder {
	builder.meetingDuration = meetingDuration
	builder.meetingDurationFlag = true
	return builder
}

func (builder *ReportTopUserBuilder) Build() *ReportTopUser {
	req := &ReportTopUser{}
	if builder.idFlag {
		req.Id = &builder.id

	}
	if builder.nameFlag {
		req.Name = &builder.name

	}
	if builder.userTypeFlag {
		req.UserType = &builder.userType

	}
	if builder.meetingCountFlag {
		req.MeetingCount = &builder.meetingCount

	}
	if builder.meetingDurationFlag {
		req.MeetingDuration = &builder.meetingDuration

	}
	return req
}

type Reserve struct {
	Id              *string                `json:"id,omitempty"`               // 预约ID（预约的唯一标识）
	MeetingNo       *string                `json:"meeting_no,omitempty"`       // 9位会议号（飞书用户可通过输入9位会议号快捷入会）
	Url             *string                `json:"url,omitempty"`              // 会议链接（飞书用户可通过点击会议链接快捷入会）
	AppLink         *string                `json:"app_link,omitempty"`         // APPLink用于唤起飞书APP入会。"{?}"为占位符，用于配置入会参数，使用时需替换具体值：0表示关闭，1表示打开。preview为入会前的设置页，mic为麦克风，speaker为扬声器，camera为摄像头
	LiveLink        *string                `json:"live_link,omitempty"`        // 直播链接
	EndTime         *string                `json:"end_time,omitempty"`         // 预约到期时间（unix时间，单位sec）
	ExpireStatus    *int                   `json:"expire_status,omitempty"`    // 过期状态
	ReserveUserId   *string                `json:"reserve_user_id,omitempty"`  // 预约人ID
	MeetingSettings *ReserveMeetingSetting `json:"meeting_settings,omitempty"` // 会议设置
}

type ReserveBuilder struct {
	id                  string // 预约ID（预约的唯一标识）
	idFlag              bool
	meetingNo           string // 9位会议号（飞书用户可通过输入9位会议号快捷入会）
	meetingNoFlag       bool
	url                 string // 会议链接（飞书用户可通过点击会议链接快捷入会）
	urlFlag             bool
	appLink             string // APPLink用于唤起飞书APP入会。"{?}"为占位符，用于配置入会参数，使用时需替换具体值：0表示关闭，1表示打开。preview为入会前的设置页，mic为麦克风，speaker为扬声器，camera为摄像头
	appLinkFlag         bool
	liveLink            string // 直播链接
	liveLinkFlag        bool
	endTime             string // 预约到期时间（unix时间，单位sec）
	endTimeFlag         bool
	expireStatus        int // 过期状态
	expireStatusFlag    bool
	reserveUserId       string // 预约人ID
	reserveUserIdFlag   bool
	meetingSettings     *ReserveMeetingSetting // 会议设置
	meetingSettingsFlag bool
}

func NewReserveBuilder() *ReserveBuilder {
	builder := &ReserveBuilder{}
	return builder
}

// 预约ID（预约的唯一标识）
//
// 示例值：6911188411934973028
func (builder *ReserveBuilder) Id(id string) *ReserveBuilder {
	builder.id = id
	builder.idFlag = true
	return builder
}

// 9位会议号（飞书用户可通过输入9位会议号快捷入会）
//
// 示例值：112000358
func (builder *ReserveBuilder) MeetingNo(meetingNo string) *ReserveBuilder {
	builder.meetingNo = meetingNo
	builder.meetingNoFlag = true
	return builder
}

// 会议链接（飞书用户可通过点击会议链接快捷入会）
//
// 示例值：https://vc.feishu.cn/j/337736498
func (builder *ReserveBuilder) Url(url string) *ReserveBuilder {
	builder.url = url
	builder.urlFlag = true
	return builder
}

// APPLink用于唤起飞书APP入会。"{?}"为占位符，用于配置入会参数，使用时需替换具体值：0表示关闭，1表示打开。preview为入会前的设置页，mic为麦克风，speaker为扬声器，camera为摄像头
//
// 示例值：https://applink.feishu.cn/client/videochat/open?source=openplatform&action=join&idtype=reservationid&id={?}&preview={?}&mic={?}&speaker={?}&camera={?}
func (builder *ReserveBuilder) AppLink(appLink string) *ReserveBuilder {
	builder.appLink = appLink
	builder.appLinkFlag = true
	return builder
}

// 直播链接
//
// 示例值：https://meetings.feishu.cn/s/1gub381l4gglv
func (builder *ReserveBuilder) LiveLink(liveLink string) *ReserveBuilder {
	builder.liveLink = liveLink
	builder.liveLinkFlag = true
	return builder
}

// 预约到期时间（unix时间，单位sec）
//
// 示例值：1608883322
func (builder *ReserveBuilder) EndTime(endTime string) *ReserveBuilder {
	builder.endTime = endTime
	builder.endTimeFlag = true
	return builder
}

// 过期状态
//
// 示例值：0
func (builder *ReserveBuilder) ExpireStatus(expireStatus int) *ReserveBuilder {
	builder.expireStatus = expireStatus
	builder.expireStatusFlag = true
	return builder
}

// 预约人ID
//
// 示例值：ou_3ec3f6a28a0d08c45d895276e8e5e19b
func (builder *ReserveBuilder) ReserveUserId(reserveUserId string) *ReserveBuilder {
	builder.reserveUserId = reserveUserId
	builder.reserveUserIdFlag = true
	return builder
}

// 会议设置
//
// 示例值：
func (builder *ReserveBuilder) MeetingSettings(meetingSettings *ReserveMeetingSetting) *ReserveBuilder {
	builder.meetingSettings = meetingSettings
	builder.meetingSettingsFlag = true
	return builder
}

func (builder *ReserveBuilder) Build() *Reserve {
	req := &Reserve{}
	if builder.idFlag {
		req.Id = &builder.id

	}
	if builder.meetingNoFlag {
		req.MeetingNo = &builder.meetingNo

	}
	if builder.urlFlag {
		req.Url = &builder.url

	}
	if builder.appLinkFlag {
		req.AppLink = &builder.appLink

	}
	if builder.liveLinkFlag {
		req.LiveLink = &builder.liveLink

	}
	if builder.endTimeFlag {
		req.EndTime = &builder.endTime

	}
	if builder.expireStatusFlag {
		req.ExpireStatus = &builder.expireStatus

	}
	if builder.reserveUserIdFlag {
		req.ReserveUserId = &builder.reserveUserId

	}
	if builder.meetingSettingsFlag {
		req.MeetingSettings = builder.meetingSettings
	}
	return req
}

type ReserveActionPermission struct {
	Permission         *int                        `json:"permission,omitempty"`          // 权限项
	PermissionCheckers []*ReservePermissionChecker `json:"permission_checkers,omitempty"` // 权限检查器列表，权限检查器之间为"逻辑或"的关系（即 有一个为true则拥有该权限）
}

type ReserveActionPermissionBuilder struct {
	permission             int // 权限项
	permissionFlag         bool
	permissionCheckers     []*ReservePermissionChecker // 权限检查器列表，权限检查器之间为"逻辑或"的关系（即 有一个为true则拥有该权限）
	permissionCheckersFlag bool
}

func NewReserveActionPermissionBuilder() *ReserveActionPermissionBuilder {
	builder := &ReserveActionPermissionBuilder{}
	return builder
}

// 权限项
//
// 示例值：1
func (builder *ReserveActionPermissionBuilder) Permission(permission int) *ReserveActionPermissionBuilder {
	builder.permission = permission
	builder.permissionFlag = true
	return builder
}

// 权限检查器列表，权限检查器之间为"逻辑或"的关系（即 有一个为true则拥有该权限）
//
// 示例值：
func (builder *ReserveActionPermissionBuilder) PermissionCheckers(permissionCheckers []*ReservePermissionChecker) *ReserveActionPermissionBuilder {
	builder.permissionCheckers = permissionCheckers
	builder.permissionCheckersFlag = true
	return builder
}

func (builder *ReserveActionPermissionBuilder) Build() *ReserveActionPermission {
	req := &ReserveActionPermission{}
	if builder.permissionFlag {
		req.Permission = &builder.permission

	}
	if builder.permissionCheckersFlag {
		req.PermissionCheckers = builder.permissionCheckers
	}
	return req
}

type ReserveAssignHost struct {
	UserType *int    `json:"user_type,omitempty"` // 用户类型，仅支持设置同租户下的 Lark 用户
	Id       *string `json:"id,omitempty"`        // 用户ID
}

type ReserveAssignHostBuilder struct {
	userType     int // 用户类型，仅支持设置同租户下的 Lark 用户
	userTypeFlag bool
	id           string // 用户ID
	idFlag       bool
}

func NewReserveAssignHostBuilder() *ReserveAssignHostBuilder {
	builder := &ReserveAssignHostBuilder{}
	return builder
}

// 用户类型，仅支持设置同租户下的 Lark 用户
//
// 示例值：1
func (builder *ReserveAssignHostBuilder) UserType(userType int) *ReserveAssignHostBuilder {
	builder.userType = userType
	builder.userTypeFlag = true
	return builder
}

// 用户ID
//
// 示例值：ou_3ec3f6a28a0d08c45d895276e8e5e19b
func (builder *ReserveAssignHostBuilder) Id(id string) *ReserveAssignHostBuilder {
	builder.id = id
	builder.idFlag = true
	return builder
}

func (builder *ReserveAssignHostBuilder) Build() *ReserveAssignHost {
	req := &ReserveAssignHost{}
	if builder.userTypeFlag {
		req.UserType = &builder.userType

	}
	if builder.idFlag {
		req.Id = &builder.id

	}
	return req
}

type ReserveCallSetting struct {
	Callee *ReserveCallee `json:"callee,omitempty"` // 被呼叫的用户
}

type ReserveCallSettingBuilder struct {
	callee     *ReserveCallee // 被呼叫的用户
	calleeFlag bool
}

func NewReserveCallSettingBuilder() *ReserveCallSettingBuilder {
	builder := &ReserveCallSettingBuilder{}
	return builder
}

// 被呼叫的用户
//
// 示例值：
func (builder *ReserveCallSettingBuilder) Callee(callee *ReserveCallee) *ReserveCallSettingBuilder {
	builder.callee = callee
	builder.calleeFlag = true
	return builder
}

func (builder *ReserveCallSettingBuilder) Build() *ReserveCallSetting {
	req := &ReserveCallSetting{}
	if builder.calleeFlag {
		req.Callee = builder.callee
	}
	return req
}

type ReserveCallee struct {
	Id          *string      `json:"id,omitempty"`            // 用户ID
	UserType    *int         `json:"user_type,omitempty"`     // 用户类型，当前仅支持用户类型6(pstn用户)
	PstnSipInfo *PstnSipInfo `json:"pstn_sip_info,omitempty"` // pstn/sip信息
}

type ReserveCalleeBuilder struct {
	id              string // 用户ID
	idFlag          bool
	userType        int // 用户类型，当前仅支持用户类型6(pstn用户)
	userTypeFlag    bool
	pstnSipInfo     *PstnSipInfo // pstn/sip信息
	pstnSipInfoFlag bool
}

func NewReserveCalleeBuilder() *ReserveCalleeBuilder {
	builder := &ReserveCalleeBuilder{}
	return builder
}

// 用户ID
//
// 示例值：ou_3ec3f6a28a0d08c45d895276e8e5e19b
func (builder *ReserveCalleeBuilder) Id(id string) *ReserveCalleeBuilder {
	builder.id = id
	builder.idFlag = true
	return builder
}

// 用户类型，当前仅支持用户类型6(pstn用户)
//
// 示例值：1
func (builder *ReserveCalleeBuilder) UserType(userType int) *ReserveCalleeBuilder {
	builder.userType = userType
	builder.userTypeFlag = true
	return builder
}

// pstn/sip信息
//
// 示例值：
func (builder *ReserveCalleeBuilder) PstnSipInfo(pstnSipInfo *PstnSipInfo) *ReserveCalleeBuilder {
	builder.pstnSipInfo = pstnSipInfo
	builder.pstnSipInfoFlag = true
	return builder
}

func (builder *ReserveCalleeBuilder) Build() *ReserveCallee {
	req := &ReserveCallee{}
	if builder.idFlag {
		req.Id = &builder.id

	}
	if builder.userTypeFlag {
		req.UserType = &builder.userType

	}
	if builder.pstnSipInfoFlag {
		req.PstnSipInfo = builder.pstnSipInfo
	}
	return req
}

type ReserveCorrectionCheckInfo struct {
	InvalidHostIdList []string `json:"invalid_host_id_list,omitempty"` // 指定主持人无效id列表
}

type ReserveCorrectionCheckInfoBuilder struct {
	invalidHostIdList     []string // 指定主持人无效id列表
	invalidHostIdListFlag bool
}

func NewReserveCorrectionCheckInfoBuilder() *ReserveCorrectionCheckInfoBuilder {
	builder := &ReserveCorrectionCheckInfoBuilder{}
	return builder
}

// 指定主持人无效id列表
//
// 示例值：
func (builder *ReserveCorrectionCheckInfoBuilder) InvalidHostIdList(invalidHostIdList []string) *ReserveCorrectionCheckInfoBuilder {
	builder.invalidHostIdList = invalidHostIdList
	builder.invalidHostIdListFlag = true
	return builder
}

func (builder *ReserveCorrectionCheckInfoBuilder) Build() *ReserveCorrectionCheckInfo {
	req := &ReserveCorrectionCheckInfo{}
	if builder.invalidHostIdListFlag {
		req.InvalidHostIdList = builder.invalidHostIdList
	}
	return req
}

type ReserveMeetingSetting struct {
	Topic              *string                    `json:"topic,omitempty"`                // 会议主题
	ActionPermissions  []*ReserveActionPermission `json:"action_permissions,omitempty"`   // 会议权限配置列表，如果存在相同的权限配置项则它们之间为"逻辑或"的关系（即 有一个为true则拥有该权限）
	MeetingInitialType *int                       `json:"meeting_initial_type,omitempty"` // 会议初始类型
	CallSetting        *ReserveCallSetting        `json:"call_setting,omitempty"`         // 1v1呼叫相关参数
	AutoRecord         *bool                      `json:"auto_record,omitempty"`          // 使用飞书视频会议时，是否开启自动录制，默认false
	AssignHostList     []*ReserveAssignHost       `json:"assign_host_list,omitempty"`     // 指定主持人列表
}

type ReserveMeetingSettingBuilder struct {
	topic                  string // 会议主题
	topicFlag              bool
	actionPermissions      []*ReserveActionPermission // 会议权限配置列表，如果存在相同的权限配置项则它们之间为"逻辑或"的关系（即 有一个为true则拥有该权限）
	actionPermissionsFlag  bool
	meetingInitialType     int // 会议初始类型
	meetingInitialTypeFlag bool
	callSetting            *ReserveCallSetting // 1v1呼叫相关参数
	callSettingFlag        bool
	autoRecord             bool // 使用飞书视频会议时，是否开启自动录制，默认false
	autoRecordFlag         bool
	assignHostList         []*ReserveAssignHost // 指定主持人列表
	assignHostListFlag     bool
}

func NewReserveMeetingSettingBuilder() *ReserveMeetingSettingBuilder {
	builder := &ReserveMeetingSettingBuilder{}
	return builder
}

// 会议主题
//
// 示例值：my meeting
func (builder *ReserveMeetingSettingBuilder) Topic(topic string) *ReserveMeetingSettingBuilder {
	builder.topic = topic
	builder.topicFlag = true
	return builder
}

// 会议权限配置列表，如果存在相同的权限配置项则它们之间为"逻辑或"的关系（即 有一个为true则拥有该权限）
//
// 示例值：
func (builder *ReserveMeetingSettingBuilder) ActionPermissions(actionPermissions []*ReserveActionPermission) *ReserveMeetingSettingBuilder {
	builder.actionPermissions = actionPermissions
	builder.actionPermissionsFlag = true
	return builder
}

// 会议初始类型
//
// 示例值：1
func (builder *ReserveMeetingSettingBuilder) MeetingInitialType(meetingInitialType int) *ReserveMeetingSettingBuilder {
	builder.meetingInitialType = meetingInitialType
	builder.meetingInitialTypeFlag = true
	return builder
}

// 1v1呼叫相关参数
//
// 示例值：
func (builder *ReserveMeetingSettingBuilder) CallSetting(callSetting *ReserveCallSetting) *ReserveMeetingSettingBuilder {
	builder.callSetting = callSetting
	builder.callSettingFlag = true
	return builder
}

// 使用飞书视频会议时，是否开启自动录制，默认false
//
// 示例值：true
func (builder *ReserveMeetingSettingBuilder) AutoRecord(autoRecord bool) *ReserveMeetingSettingBuilder {
	builder.autoRecord = autoRecord
	builder.autoRecordFlag = true
	return builder
}

// 指定主持人列表
//
// 示例值：
func (builder *ReserveMeetingSettingBuilder) AssignHostList(assignHostList []*ReserveAssignHost) *ReserveMeetingSettingBuilder {
	builder.assignHostList = assignHostList
	builder.assignHostListFlag = true
	return builder
}

func (builder *ReserveMeetingSettingBuilder) Build() *ReserveMeetingSetting {
	req := &ReserveMeetingSetting{}
	if builder.topicFlag {
		req.Topic = &builder.topic

	}
	if builder.actionPermissionsFlag {
		req.ActionPermissions = builder.actionPermissions
	}
	if builder.meetingInitialTypeFlag {
		req.MeetingInitialType = &builder.meetingInitialType

	}
	if builder.callSettingFlag {
		req.CallSetting = builder.callSetting
	}
	if builder.autoRecordFlag {
		req.AutoRecord = &builder.autoRecord

	}
	if builder.assignHostListFlag {
		req.AssignHostList = builder.assignHostList
	}
	return req
}

type ReservePermissionChecker struct {
	CheckField *int     `json:"check_field,omitempty"` // 检查字段类型
	CheckMode  *int     `json:"check_mode,omitempty"`  // 检查方式
	CheckList  []string `json:"check_list,omitempty"`  // 检查字段列表
}

type ReservePermissionCheckerBuilder struct {
	checkField     int // 检查字段类型
	checkFieldFlag bool
	checkMode      int // 检查方式
	checkModeFlag  bool
	checkList      []string // 检查字段列表
	checkListFlag  bool
}

func NewReservePermissionCheckerBuilder() *ReservePermissionCheckerBuilder {
	builder := &ReservePermissionCheckerBuilder{}
	return builder
}

// 检查字段类型
//
// 示例值：1
func (builder *ReservePermissionCheckerBuilder) CheckField(checkField int) *ReservePermissionCheckerBuilder {
	builder.checkField = checkField
	builder.checkFieldFlag = true
	return builder
}

// 检查方式
//
// 示例值：1
func (builder *ReservePermissionCheckerBuilder) CheckMode(checkMode int) *ReservePermissionCheckerBuilder {
	builder.checkMode = checkMode
	builder.checkModeFlag = true
	return builder
}

// 检查字段列表
//
// 示例值：123
func (builder *ReservePermissionCheckerBuilder) CheckList(checkList []string) *ReservePermissionCheckerBuilder {
	builder.checkList = checkList
	builder.checkListFlag = true
	return builder
}

func (builder *ReservePermissionCheckerBuilder) Build() *ReservePermissionChecker {
	req := &ReservePermissionChecker{}
	if builder.checkFieldFlag {
		req.CheckField = &builder.checkField

	}
	if builder.checkModeFlag {
		req.CheckMode = &builder.checkMode

	}
	if builder.checkListFlag {
		req.CheckList = builder.checkList
	}
	return req
}

type ReserveScopeConfig struct {
	AllowAllUsers *int                   `json:"allow_all_users,omitempty"` // 可预定成员范围：0 代表部分成员，1 代表全部成员。;<b>说明</b>：;1.  此值必填。;2.  当设置为 0 时，至少需要 1 个预定部门或预定人
	AllowUsers    []*SubscribeUser       `json:"allow_users,omitempty"`     // 可预定成员列表
	AllowDepts    []*SubscribeDepartment `json:"allow_depts,omitempty"`     // 可预定部门列表
}

type ReserveScopeConfigBuilder struct {
	allowAllUsers     int // 可预定成员范围：0 代表部分成员，1 代表全部成员。;<b>说明</b>：;1.  此值必填。;2.  当设置为 0 时，至少需要 1 个预定部门或预定人
	allowAllUsersFlag bool
	allowUsers        []*SubscribeUser // 可预定成员列表
	allowUsersFlag    bool
	allowDepts        []*SubscribeDepartment // 可预定部门列表
	allowDeptsFlag    bool
}

func NewReserveScopeConfigBuilder() *ReserveScopeConfigBuilder {
	builder := &ReserveScopeConfigBuilder{}
	return builder
}

// 可预定成员范围：0 代表部分成员，1 代表全部成员。;<b>说明</b>：;1.  此值必填。;2.  当设置为 0 时，至少需要 1 个预定部门或预定人
//
// 示例值：0
func (builder *ReserveScopeConfigBuilder) AllowAllUsers(allowAllUsers int) *ReserveScopeConfigBuilder {
	builder.allowAllUsers = allowAllUsers
	builder.allowAllUsersFlag = true
	return builder
}

// 可预定成员列表
//
// 示例值：[{user_id:"ou_e8bce6c3935ef1fc1b432992fd9d3db8"}]
func (builder *ReserveScopeConfigBuilder) AllowUsers(allowUsers []*SubscribeUser) *ReserveScopeConfigBuilder {
	builder.allowUsers = allowUsers
	builder.allowUsersFlag = true
	return builder
}

// 可预定部门列表
//
// 示例值：[{department_id:"od-5c07f0c117cf8795f25610a69363ce31"}]
func (builder *ReserveScopeConfigBuilder) AllowDepts(allowDepts []*SubscribeDepartment) *ReserveScopeConfigBuilder {
	builder.allowDepts = allowDepts
	builder.allowDeptsFlag = true
	return builder
}

func (builder *ReserveScopeConfigBuilder) Build() *ReserveScopeConfig {
	req := &ReserveScopeConfig{}
	if builder.allowAllUsersFlag {
		req.AllowAllUsers = &builder.allowAllUsers

	}
	if builder.allowUsersFlag {
		req.AllowUsers = builder.allowUsers
	}
	if builder.allowDeptsFlag {
		req.AllowDepts = builder.allowDepts
	}
	return req
}

type Room struct {
	RoomId       *string     `json:"room_id,omitempty"`        // 会议室ID
	Name         *string     `json:"name,omitempty"`           // 会议室名称
	Capacity     *int        `json:"capacity,omitempty"`       // 会议室能容纳的人数
	Description  *string     `json:"description,omitempty"`    // 会议室的相关描述
	DisplayId    *string     `json:"display_id,omitempty"`     // 会议室的展示ID
	CustomRoomId *string     `json:"custom_room_id,omitempty"` // 自定义的会议室ID
	RoomLevelId  *string     `json:"room_level_id,omitempty"`  // 层级ID
	Path         []string    `json:"path,omitempty"`           // 层级路径
	RoomStatus   *RoomStatus `json:"room_status,omitempty"`    // 会议室状态
}

type RoomBuilder struct {
	roomId           string // 会议室ID
	roomIdFlag       bool
	name             string // 会议室名称
	nameFlag         bool
	capacity         int // 会议室能容纳的人数
	capacityFlag     bool
	description      string // 会议室的相关描述
	descriptionFlag  bool
	displayId        string // 会议室的展示ID
	displayIdFlag    bool
	customRoomId     string // 自定义的会议室ID
	customRoomIdFlag bool
	roomLevelId      string // 层级ID
	roomLevelIdFlag  bool
	path             []string // 层级路径
	pathFlag         bool
	roomStatus       *RoomStatus // 会议室状态
	roomStatusFlag   bool
}

func NewRoomBuilder() *RoomBuilder {
	builder := &RoomBuilder{}
	return builder
}

// 会议室ID
//
// 示例值：omm_4de32cf10a4358788ff4e09e37ebbf9b
func (builder *RoomBuilder) RoomId(roomId string) *RoomBuilder {
	builder.roomId = roomId
	builder.roomIdFlag = true
	return builder
}

// 会议室名称
//
// 示例值：测试会议室
func (builder *RoomBuilder) Name(name string) *RoomBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

// 会议室能容纳的人数
//
// 示例值：10
func (builder *RoomBuilder) Capacity(capacity int) *RoomBuilder {
	builder.capacity = capacity
	builder.capacityFlag = true
	return builder
}

// 会议室的相关描述
//
// 示例值：测试会议室描述
func (builder *RoomBuilder) Description(description string) *RoomBuilder {
	builder.description = description
	builder.descriptionFlag = true
	return builder
}

// 会议室的展示ID
//
// 示例值：LM134742334
func (builder *RoomBuilder) DisplayId(displayId string) *RoomBuilder {
	builder.displayId = displayId
	builder.displayIdFlag = true
	return builder
}

// 自定义的会议室ID
//
// 示例值：1234
func (builder *RoomBuilder) CustomRoomId(customRoomId string) *RoomBuilder {
	builder.customRoomId = customRoomId
	builder.customRoomIdFlag = true
	return builder
}

// 层级ID
//
// 示例值：omb_4ad1a2c7a2fbc5fc9570f38456931293
func (builder *RoomBuilder) RoomLevelId(roomLevelId string) *RoomBuilder {
	builder.roomLevelId = roomLevelId
	builder.roomLevelIdFlag = true
	return builder
}

// 层级路径
//
// 示例值：[omb_8d020b12fe49e82847c2af3c193d5754,omb_8d020b12fe49e82847c2af3c193d5754]
func (builder *RoomBuilder) Path(path []string) *RoomBuilder {
	builder.path = path
	builder.pathFlag = true
	return builder
}

// 会议室状态
//
// 示例值：
func (builder *RoomBuilder) RoomStatus(roomStatus *RoomStatus) *RoomBuilder {
	builder.roomStatus = roomStatus
	builder.roomStatusFlag = true
	return builder
}

func (builder *RoomBuilder) Build() *Room {
	req := &Room{}
	if builder.roomIdFlag {
		req.RoomId = &builder.roomId

	}
	if builder.nameFlag {
		req.Name = &builder.name

	}
	if builder.capacityFlag {
		req.Capacity = &builder.capacity

	}
	if builder.descriptionFlag {
		req.Description = &builder.description

	}
	if builder.displayIdFlag {
		req.DisplayId = &builder.displayId

	}
	if builder.customRoomIdFlag {
		req.CustomRoomId = &builder.customRoomId

	}
	if builder.roomLevelIdFlag {
		req.RoomLevelId = &builder.roomLevelId

	}
	if builder.pathFlag {
		req.Path = builder.path
	}
	if builder.roomStatusFlag {
		req.RoomStatus = builder.roomStatus
	}
	return req
}

type RoomConfig struct {
	RoomBackground        *string             `json:"room_background,omitempty"`          // 飞书会议室背景图
	DisplayBackground     *string             `json:"display_background,omitempty"`       // 飞书签到板背景图
	DigitalSignage        *RoomDigitalSignage `json:"digital_signage,omitempty"`          // 飞书会议室数字标牌
	RoomBoxDigitalSignage *RoomDigitalSignage `json:"room_box_digital_signage,omitempty"` // 飞书投屏盒子数字标牌
	RoomStatus            *RoomStatus         `json:"room_status,omitempty"`              // 会议室状态
}

type RoomConfigBuilder struct {
	roomBackground            string // 飞书会议室背景图
	roomBackgroundFlag        bool
	displayBackground         string // 飞书签到板背景图
	displayBackgroundFlag     bool
	digitalSignage            *RoomDigitalSignage // 飞书会议室数字标牌
	digitalSignageFlag        bool
	roomBoxDigitalSignage     *RoomDigitalSignage // 飞书投屏盒子数字标牌
	roomBoxDigitalSignageFlag bool
	roomStatus                *RoomStatus // 会议室状态
	roomStatusFlag            bool
}

func NewRoomConfigBuilder() *RoomConfigBuilder {
	builder := &RoomConfigBuilder{}
	return builder
}

// 飞书会议室背景图
//
// 示例值：https://lf1-ttcdn-tos.pstatp.com/obj/xxx
func (builder *RoomConfigBuilder) RoomBackground(roomBackground string) *RoomConfigBuilder {
	builder.roomBackground = roomBackground
	builder.roomBackgroundFlag = true
	return builder
}

// 飞书签到板背景图
//
// 示例值：https://lf1-ttcdn-tos.pstatp.com/obj/xxx
func (builder *RoomConfigBuilder) DisplayBackground(displayBackground string) *RoomConfigBuilder {
	builder.displayBackground = displayBackground
	builder.displayBackgroundFlag = true
	return builder
}

// 飞书会议室数字标牌
//
// 示例值：
func (builder *RoomConfigBuilder) DigitalSignage(digitalSignage *RoomDigitalSignage) *RoomConfigBuilder {
	builder.digitalSignage = digitalSignage
	builder.digitalSignageFlag = true
	return builder
}

// 飞书投屏盒子数字标牌
//
// 示例值：
func (builder *RoomConfigBuilder) RoomBoxDigitalSignage(roomBoxDigitalSignage *RoomDigitalSignage) *RoomConfigBuilder {
	builder.roomBoxDigitalSignage = roomBoxDigitalSignage
	builder.roomBoxDigitalSignageFlag = true
	return builder
}

// 会议室状态
//
// 示例值：
func (builder *RoomConfigBuilder) RoomStatus(roomStatus *RoomStatus) *RoomConfigBuilder {
	builder.roomStatus = roomStatus
	builder.roomStatusFlag = true
	return builder
}

func (builder *RoomConfigBuilder) Build() *RoomConfig {
	req := &RoomConfig{}
	if builder.roomBackgroundFlag {
		req.RoomBackground = &builder.roomBackground

	}
	if builder.displayBackgroundFlag {
		req.DisplayBackground = &builder.displayBackground

	}
	if builder.digitalSignageFlag {
		req.DigitalSignage = builder.digitalSignage
	}
	if builder.roomBoxDigitalSignageFlag {
		req.RoomBoxDigitalSignage = builder.roomBoxDigitalSignage
	}
	if builder.roomStatusFlag {
		req.RoomStatus = builder.roomStatus
	}
	return req
}

type RoomDigitalSignage struct {
	Enable       *bool                         `json:"enable,omitempty"`        // 是否开启数字标牌功能
	Mute         *bool                         `json:"mute,omitempty"`          // 是否静音播放
	StartDisplay *int                          `json:"start_display,omitempty"` // 日程会议开始前n分钟结束播放
	StopDisplay  *int                          `json:"stop_display,omitempty"`  // 会议结束后n分钟开始播放
	Materials    []*RoomDigitalSignageMaterial `json:"materials,omitempty"`     // 素材列表
}

type RoomDigitalSignageBuilder struct {
	enable           bool // 是否开启数字标牌功能
	enableFlag       bool
	mute             bool // 是否静音播放
	muteFlag         bool
	startDisplay     int // 日程会议开始前n分钟结束播放
	startDisplayFlag bool
	stopDisplay      int // 会议结束后n分钟开始播放
	stopDisplayFlag  bool
	materials        []*RoomDigitalSignageMaterial // 素材列表
	materialsFlag    bool
}

func NewRoomDigitalSignageBuilder() *RoomDigitalSignageBuilder {
	builder := &RoomDigitalSignageBuilder{}
	return builder
}

// 是否开启数字标牌功能
//
// 示例值：true
func (builder *RoomDigitalSignageBuilder) Enable(enable bool) *RoomDigitalSignageBuilder {
	builder.enable = enable
	builder.enableFlag = true
	return builder
}

// 是否静音播放
//
// 示例值：true
func (builder *RoomDigitalSignageBuilder) Mute(mute bool) *RoomDigitalSignageBuilder {
	builder.mute = mute
	builder.muteFlag = true
	return builder
}

// 日程会议开始前n分钟结束播放
//
// 示例值：3
func (builder *RoomDigitalSignageBuilder) StartDisplay(startDisplay int) *RoomDigitalSignageBuilder {
	builder.startDisplay = startDisplay
	builder.startDisplayFlag = true
	return builder
}

// 会议结束后n分钟开始播放
//
// 示例值：3
func (builder *RoomDigitalSignageBuilder) StopDisplay(stopDisplay int) *RoomDigitalSignageBuilder {
	builder.stopDisplay = stopDisplay
	builder.stopDisplayFlag = true
	return builder
}

// 素材列表
//
// 示例值：
func (builder *RoomDigitalSignageBuilder) Materials(materials []*RoomDigitalSignageMaterial) *RoomDigitalSignageBuilder {
	builder.materials = materials
	builder.materialsFlag = true
	return builder
}

func (builder *RoomDigitalSignageBuilder) Build() *RoomDigitalSignage {
	req := &RoomDigitalSignage{}
	if builder.enableFlag {
		req.Enable = &builder.enable

	}
	if builder.muteFlag {
		req.Mute = &builder.mute

	}
	if builder.startDisplayFlag {
		req.StartDisplay = &builder.startDisplay

	}
	if builder.stopDisplayFlag {
		req.StopDisplay = &builder.stopDisplay

	}
	if builder.materialsFlag {
		req.Materials = builder.materials
	}
	return req
}

type RoomDigitalSignageMaterial struct {
	Id           *string `json:"id,omitempty"`            // 素材ID，当设置新素材时，无需传递该字段
	Name         *string `json:"name,omitempty"`          // 素材名称
	MaterialType *int    `json:"material_type,omitempty"` // 素材类型
	Url          *string `json:"url,omitempty"`           // 素材url
	Duration     *int    `json:"duration,omitempty"`      // 播放时长（单位sec）
	Cover        *string `json:"cover,omitempty"`         // 素材封面url
	Md5          *string `json:"md5,omitempty"`           // 素材文件md5
	Vid          *string `json:"vid,omitempty"`           // 素材文件vid
	Size         *string `json:"size,omitempty"`          // 素材文件大小（单位byte）
}

type RoomDigitalSignageMaterialBuilder struct {
	id               string // 素材ID，当设置新素材时，无需传递该字段
	idFlag           bool
	name             string // 素材名称
	nameFlag         bool
	materialType     int // 素材类型
	materialTypeFlag bool
	url              string // 素材url
	urlFlag          bool
	duration         int // 播放时长（单位sec）
	durationFlag     bool
	cover            string // 素材封面url
	coverFlag        bool
	md5              string // 素材文件md5
	md5Flag          bool
	vid              string // 素材文件vid
	vidFlag          bool
	size             string // 素材文件大小（单位byte）
	sizeFlag         bool
}

func NewRoomDigitalSignageMaterialBuilder() *RoomDigitalSignageMaterialBuilder {
	builder := &RoomDigitalSignageMaterialBuilder{}
	return builder
}

// 素材ID，当设置新素材时，无需传递该字段
//
// 示例值：7847784676276
func (builder *RoomDigitalSignageMaterialBuilder) Id(id string) *RoomDigitalSignageMaterialBuilder {
	builder.id = id
	builder.idFlag = true
	return builder
}

// 素材名称
//
// 示例值：name
func (builder *RoomDigitalSignageMaterialBuilder) Name(name string) *RoomDigitalSignageMaterialBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

// 素材类型
//
// 示例值：0
func (builder *RoomDigitalSignageMaterialBuilder) MaterialType(materialType int) *RoomDigitalSignageMaterialBuilder {
	builder.materialType = materialType
	builder.materialTypeFlag = true
	return builder
}

// 素材url
//
// 示例值：url
func (builder *RoomDigitalSignageMaterialBuilder) Url(url string) *RoomDigitalSignageMaterialBuilder {
	builder.url = url
	builder.urlFlag = true
	return builder
}

// 播放时长（单位sec）
//
// 示例值：15
func (builder *RoomDigitalSignageMaterialBuilder) Duration(duration int) *RoomDigitalSignageMaterialBuilder {
	builder.duration = duration
	builder.durationFlag = true
	return builder
}

// 素材封面url
//
// 示例值：url
func (builder *RoomDigitalSignageMaterialBuilder) Cover(cover string) *RoomDigitalSignageMaterialBuilder {
	builder.cover = cover
	builder.coverFlag = true
	return builder
}

// 素材文件md5
//
// 示例值：md5
func (builder *RoomDigitalSignageMaterialBuilder) Md5(md5 string) *RoomDigitalSignageMaterialBuilder {
	builder.md5 = md5
	builder.md5Flag = true
	return builder
}

// 素材文件vid
//
// 示例值：vid
func (builder *RoomDigitalSignageMaterialBuilder) Vid(vid string) *RoomDigitalSignageMaterialBuilder {
	builder.vid = vid
	builder.vidFlag = true
	return builder
}

// 素材文件大小（单位byte）
//
// 示例值：100
func (builder *RoomDigitalSignageMaterialBuilder) Size(size string) *RoomDigitalSignageMaterialBuilder {
	builder.size = size
	builder.sizeFlag = true
	return builder
}

func (builder *RoomDigitalSignageMaterialBuilder) Build() *RoomDigitalSignageMaterial {
	req := &RoomDigitalSignageMaterial{}
	if builder.idFlag {
		req.Id = &builder.id

	}
	if builder.nameFlag {
		req.Name = &builder.name

	}
	if builder.materialTypeFlag {
		req.MaterialType = &builder.materialType

	}
	if builder.urlFlag {
		req.Url = &builder.url

	}
	if builder.durationFlag {
		req.Duration = &builder.duration

	}
	if builder.coverFlag {
		req.Cover = &builder.cover

	}
	if builder.md5Flag {
		req.Md5 = &builder.md5

	}
	if builder.vidFlag {
		req.Vid = &builder.vid

	}
	if builder.sizeFlag {
		req.Size = &builder.size

	}
	return req
}

type RoomEvent struct {
	RoomId       *string          `json:"room_id,omitempty"`        // 会议室ID
	Name         *string          `json:"name,omitempty"`           // 会议室名称
	Capacity     *int             `json:"capacity,omitempty"`       // 会议室能容纳的人数
	Description  *string          `json:"description,omitempty"`    // 会议室的相关描述
	DisplayId    *string          `json:"display_id,omitempty"`     // 会议室的展示ID
	CustomRoomId *string          `json:"custom_room_id,omitempty"` // 自定义的会议室ID
	RoomLevelId  *string          `json:"room_level_id,omitempty"`  // 层级ID
	Path         []string         `json:"path,omitempty"`           // 层级路径
	RoomStatus   *RoomStatusEvent `json:"room_status,omitempty"`    // 会议室状态
}

type RoomEventBuilder struct {
	roomId           string // 会议室ID
	roomIdFlag       bool
	name             string // 会议室名称
	nameFlag         bool
	capacity         int // 会议室能容纳的人数
	capacityFlag     bool
	description      string // 会议室的相关描述
	descriptionFlag  bool
	displayId        string // 会议室的展示ID
	displayIdFlag    bool
	customRoomId     string // 自定义的会议室ID
	customRoomIdFlag bool
	roomLevelId      string // 层级ID
	roomLevelIdFlag  bool
	path             []string // 层级路径
	pathFlag         bool
	roomStatus       *RoomStatusEvent // 会议室状态
	roomStatusFlag   bool
}

func NewRoomEventBuilder() *RoomEventBuilder {
	builder := &RoomEventBuilder{}
	return builder
}

// 会议室ID
//
// 示例值：omm_4de32cf10a4358788ff4e09e37ebbf9b
func (builder *RoomEventBuilder) RoomId(roomId string) *RoomEventBuilder {
	builder.roomId = roomId
	builder.roomIdFlag = true
	return builder
}

// 会议室名称
//
// 示例值：测试会议室
func (builder *RoomEventBuilder) Name(name string) *RoomEventBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

// 会议室能容纳的人数
//
// 示例值：10
func (builder *RoomEventBuilder) Capacity(capacity int) *RoomEventBuilder {
	builder.capacity = capacity
	builder.capacityFlag = true
	return builder
}

// 会议室的相关描述
//
// 示例值：测试会议室描述
func (builder *RoomEventBuilder) Description(description string) *RoomEventBuilder {
	builder.description = description
	builder.descriptionFlag = true
	return builder
}

// 会议室的展示ID
//
// 示例值：LM134742334
func (builder *RoomEventBuilder) DisplayId(displayId string) *RoomEventBuilder {
	builder.displayId = displayId
	builder.displayIdFlag = true
	return builder
}

// 自定义的会议室ID
//
// 示例值：1234
func (builder *RoomEventBuilder) CustomRoomId(customRoomId string) *RoomEventBuilder {
	builder.customRoomId = customRoomId
	builder.customRoomIdFlag = true
	return builder
}

// 层级ID
//
// 示例值：omb_4ad1a2c7a2fbc5fc9570f38456931293
func (builder *RoomEventBuilder) RoomLevelId(roomLevelId string) *RoomEventBuilder {
	builder.roomLevelId = roomLevelId
	builder.roomLevelIdFlag = true
	return builder
}

// 层级路径
//
// 示例值：[omb_8d020b12fe49e82847c2af3c193d5754,omb_8d020b12fe49e82847c2af3c193d5754]
func (builder *RoomEventBuilder) Path(path []string) *RoomEventBuilder {
	builder.path = path
	builder.pathFlag = true
	return builder
}

// 会议室状态
//
// 示例值：
func (builder *RoomEventBuilder) RoomStatus(roomStatus *RoomStatusEvent) *RoomEventBuilder {
	builder.roomStatus = roomStatus
	builder.roomStatusFlag = true
	return builder
}

func (builder *RoomEventBuilder) Build() *RoomEvent {
	req := &RoomEvent{}
	if builder.roomIdFlag {
		req.RoomId = &builder.roomId

	}
	if builder.nameFlag {
		req.Name = &builder.name

	}
	if builder.capacityFlag {
		req.Capacity = &builder.capacity

	}
	if builder.descriptionFlag {
		req.Description = &builder.description

	}
	if builder.displayIdFlag {
		req.DisplayId = &builder.displayId

	}
	if builder.customRoomIdFlag {
		req.CustomRoomId = &builder.customRoomId

	}
	if builder.roomLevelIdFlag {
		req.RoomLevelId = &builder.roomLevelId

	}
	if builder.pathFlag {
		req.Path = builder.path
	}
	if builder.roomStatusFlag {
		req.RoomStatus = builder.roomStatus
	}
	return req
}

type RoomLevel struct {
	RoomLevelId   *string  `json:"room_level_id,omitempty"`   // 层级ID
	Name          *string  `json:"name,omitempty"`            // 层级名称
	ParentId      *string  `json:"parent_id,omitempty"`       // 父层级ID
	Path          []string `json:"path,omitempty"`            // 层级路径
	HasChild      *bool    `json:"has_child,omitempty"`       // 是否有子层级
	CustomGroupId *string  `json:"custom_group_id,omitempty"` // 自定义层级ID
}

type RoomLevelBuilder struct {
	roomLevelId       string // 层级ID
	roomLevelIdFlag   bool
	name              string // 层级名称
	nameFlag          bool
	parentId          string // 父层级ID
	parentIdFlag      bool
	path              []string // 层级路径
	pathFlag          bool
	hasChild          bool // 是否有子层级
	hasChildFlag      bool
	customGroupId     string // 自定义层级ID
	customGroupIdFlag bool
}

func NewRoomLevelBuilder() *RoomLevelBuilder {
	builder := &RoomLevelBuilder{}
	return builder
}

// 层级ID
//
// 示例值：层级ID
func (builder *RoomLevelBuilder) RoomLevelId(roomLevelId string) *RoomLevelBuilder {
	builder.roomLevelId = roomLevelId
	builder.roomLevelIdFlag = true
	return builder
}

// 层级名称
//
// 示例值：测试层级
func (builder *RoomLevelBuilder) Name(name string) *RoomLevelBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

// 父层级ID
//
// 示例值：omb_4ad1a2c7a2fbc5fc9570f38456931293
func (builder *RoomLevelBuilder) ParentId(parentId string) *RoomLevelBuilder {
	builder.parentId = parentId
	builder.parentIdFlag = true
	return builder
}

// 层级路径
//
// 示例值：[omb_8d020b12fe49e82847c2af3c193d5754, omb_8d020b12fe49e82847c2af3c193d5754]
func (builder *RoomLevelBuilder) Path(path []string) *RoomLevelBuilder {
	builder.path = path
	builder.pathFlag = true
	return builder
}

// 是否有子层级
//
// 示例值：false
func (builder *RoomLevelBuilder) HasChild(hasChild bool) *RoomLevelBuilder {
	builder.hasChild = hasChild
	builder.hasChildFlag = true
	return builder
}

// 自定义层级ID
//
// 示例值：10000
func (builder *RoomLevelBuilder) CustomGroupId(customGroupId string) *RoomLevelBuilder {
	builder.customGroupId = customGroupId
	builder.customGroupIdFlag = true
	return builder
}

func (builder *RoomLevelBuilder) Build() *RoomLevel {
	req := &RoomLevel{}
	if builder.roomLevelIdFlag {
		req.RoomLevelId = &builder.roomLevelId

	}
	if builder.nameFlag {
		req.Name = &builder.name

	}
	if builder.parentIdFlag {
		req.ParentId = &builder.parentId

	}
	if builder.pathFlag {
		req.Path = builder.path
	}
	if builder.hasChildFlag {
		req.HasChild = &builder.hasChild

	}
	if builder.customGroupIdFlag {
		req.CustomGroupId = &builder.customGroupId

	}
	return req
}

type RoomStatus struct {
	Status           *bool    `json:"status,omitempty"`             // 是否启用会议室
	ScheduleStatus   *bool    `json:"schedule_status,omitempty"`    // 会议室未来状态为启用或禁用
	DisableStartTime *string  `json:"disable_start_time,omitempty"` // 禁用开始时间（unix时间，单位sec）
	DisableEndTime   *string  `json:"disable_end_time,omitempty"`   // 禁用结束时间（unix时间，单位sec，数值0表示永久禁用）
	DisableReason    *string  `json:"disable_reason,omitempty"`     // 禁用原因
	ContactIds       []string `json:"contact_ids,omitempty"`        // 联系人列表，id类型由user_id_type参数决定
	DisableNotice    *bool    `json:"disable_notice,omitempty"`     // 是否在禁用时发送通知给预定了该会议室的员工
	ResumeNotice     *bool    `json:"resume_notice,omitempty"`      // 是否在恢复启用时发送通知给联系人
}

type RoomStatusBuilder struct {
	status               bool // 是否启用会议室
	statusFlag           bool
	scheduleStatus       bool // 会议室未来状态为启用或禁用
	scheduleStatusFlag   bool
	disableStartTime     string // 禁用开始时间（unix时间，单位sec）
	disableStartTimeFlag bool
	disableEndTime       string // 禁用结束时间（unix时间，单位sec，数值0表示永久禁用）
	disableEndTimeFlag   bool
	disableReason        string // 禁用原因
	disableReasonFlag    bool
	contactIds           []string // 联系人列表，id类型由user_id_type参数决定
	contactIdsFlag       bool
	disableNotice        bool // 是否在禁用时发送通知给预定了该会议室的员工
	disableNoticeFlag    bool
	resumeNotice         bool // 是否在恢复启用时发送通知给联系人
	resumeNoticeFlag     bool
}

func NewRoomStatusBuilder() *RoomStatusBuilder {
	builder := &RoomStatusBuilder{}
	return builder
}

// 是否启用会议室
//
// 示例值：true
func (builder *RoomStatusBuilder) Status(status bool) *RoomStatusBuilder {
	builder.status = status
	builder.statusFlag = true
	return builder
}

// 会议室未来状态为启用或禁用
//
// 示例值：true
func (builder *RoomStatusBuilder) ScheduleStatus(scheduleStatus bool) *RoomStatusBuilder {
	builder.scheduleStatus = scheduleStatus
	builder.scheduleStatusFlag = true
	return builder
}

// 禁用开始时间（unix时间，单位sec）
//
// 示例值：1652356050
func (builder *RoomStatusBuilder) DisableStartTime(disableStartTime string) *RoomStatusBuilder {
	builder.disableStartTime = disableStartTime
	builder.disableStartTimeFlag = true
	return builder
}

// 禁用结束时间（unix时间，单位sec，数值0表示永久禁用）
//
// 示例值：1652442450
func (builder *RoomStatusBuilder) DisableEndTime(disableEndTime string) *RoomStatusBuilder {
	builder.disableEndTime = disableEndTime
	builder.disableEndTimeFlag = true
	return builder
}

// 禁用原因
//
// 示例值：测试占用
func (builder *RoomStatusBuilder) DisableReason(disableReason string) *RoomStatusBuilder {
	builder.disableReason = disableReason
	builder.disableReasonFlag = true
	return builder
}

// 联系人列表，id类型由user_id_type参数决定
//
// 示例值：["ou_3ec3f6a28a0d08c45d895276e8e5e19b"]
func (builder *RoomStatusBuilder) ContactIds(contactIds []string) *RoomStatusBuilder {
	builder.contactIds = contactIds
	builder.contactIdsFlag = true
	return builder
}

// 是否在禁用时发送通知给预定了该会议室的员工
//
// 示例值：true
func (builder *RoomStatusBuilder) DisableNotice(disableNotice bool) *RoomStatusBuilder {
	builder.disableNotice = disableNotice
	builder.disableNoticeFlag = true
	return builder
}

// 是否在恢复启用时发送通知给联系人
//
// 示例值：true
func (builder *RoomStatusBuilder) ResumeNotice(resumeNotice bool) *RoomStatusBuilder {
	builder.resumeNotice = resumeNotice
	builder.resumeNoticeFlag = true
	return builder
}

func (builder *RoomStatusBuilder) Build() *RoomStatus {
	req := &RoomStatus{}
	if builder.statusFlag {
		req.Status = &builder.status

	}
	if builder.scheduleStatusFlag {
		req.ScheduleStatus = &builder.scheduleStatus

	}
	if builder.disableStartTimeFlag {
		req.DisableStartTime = &builder.disableStartTime

	}
	if builder.disableEndTimeFlag {
		req.DisableEndTime = &builder.disableEndTime

	}
	if builder.disableReasonFlag {
		req.DisableReason = &builder.disableReason

	}
	if builder.contactIdsFlag {
		req.ContactIds = builder.contactIds
	}
	if builder.disableNoticeFlag {
		req.DisableNotice = &builder.disableNotice

	}
	if builder.resumeNoticeFlag {
		req.ResumeNotice = &builder.resumeNotice

	}
	return req
}

type RoomStatusEvent struct {
	Status           *bool     `json:"status,omitempty"`             // 是否启用会议室
	ScheduleStatus   *bool     `json:"schedule_status,omitempty"`    // 会议室未来状态为启用或禁用
	DisableStartTime *string   `json:"disable_start_time,omitempty"` // 禁用开始时间（unix时间，单位sec）
	DisableEndTime   *string   `json:"disable_end_time,omitempty"`   // 禁用结束时间（unix时间，单位sec，数值0表示永久禁用）
	DisableReason    *string   `json:"disable_reason,omitempty"`     // 禁用原因
	ContactIds       []*UserId `json:"contact_ids,omitempty"`        // 联系人列表
	DisableNotice    *bool     `json:"disable_notice,omitempty"`     // 是否在禁用时发送通知给预定了该会议室的员工
	ResumeNotice     *bool     `json:"resume_notice,omitempty"`      // 是否在恢复启用时发送通知给预定了该会议室的员工
}

type RoomStatusEventBuilder struct {
	status               bool // 是否启用会议室
	statusFlag           bool
	scheduleStatus       bool // 会议室未来状态为启用或禁用
	scheduleStatusFlag   bool
	disableStartTime     string // 禁用开始时间（unix时间，单位sec）
	disableStartTimeFlag bool
	disableEndTime       string // 禁用结束时间（unix时间，单位sec，数值0表示永久禁用）
	disableEndTimeFlag   bool
	disableReason        string // 禁用原因
	disableReasonFlag    bool
	contactIds           []*UserId // 联系人列表
	contactIdsFlag       bool
	disableNotice        bool // 是否在禁用时发送通知给预定了该会议室的员工
	disableNoticeFlag    bool
	resumeNotice         bool // 是否在恢复启用时发送通知给预定了该会议室的员工
	resumeNoticeFlag     bool
}

func NewRoomStatusEventBuilder() *RoomStatusEventBuilder {
	builder := &RoomStatusEventBuilder{}
	return builder
}

// 是否启用会议室
//
// 示例值：true
func (builder *RoomStatusEventBuilder) Status(status bool) *RoomStatusEventBuilder {
	builder.status = status
	builder.statusFlag = true
	return builder
}

// 会议室未来状态为启用或禁用
//
// 示例值：true
func (builder *RoomStatusEventBuilder) ScheduleStatus(scheduleStatus bool) *RoomStatusEventBuilder {
	builder.scheduleStatus = scheduleStatus
	builder.scheduleStatusFlag = true
	return builder
}

// 禁用开始时间（unix时间，单位sec）
//
// 示例值：1652356050
func (builder *RoomStatusEventBuilder) DisableStartTime(disableStartTime string) *RoomStatusEventBuilder {
	builder.disableStartTime = disableStartTime
	builder.disableStartTimeFlag = true
	return builder
}

// 禁用结束时间（unix时间，单位sec，数值0表示永久禁用）
//
// 示例值：1652442450
func (builder *RoomStatusEventBuilder) DisableEndTime(disableEndTime string) *RoomStatusEventBuilder {
	builder.disableEndTime = disableEndTime
	builder.disableEndTimeFlag = true
	return builder
}

// 禁用原因
//
// 示例值：测试占用
func (builder *RoomStatusEventBuilder) DisableReason(disableReason string) *RoomStatusEventBuilder {
	builder.disableReason = disableReason
	builder.disableReasonFlag = true
	return builder
}

// 联系人列表
//
// 示例值：
func (builder *RoomStatusEventBuilder) ContactIds(contactIds []*UserId) *RoomStatusEventBuilder {
	builder.contactIds = contactIds
	builder.contactIdsFlag = true
	return builder
}

// 是否在禁用时发送通知给预定了该会议室的员工
//
// 示例值：true
func (builder *RoomStatusEventBuilder) DisableNotice(disableNotice bool) *RoomStatusEventBuilder {
	builder.disableNotice = disableNotice
	builder.disableNoticeFlag = true
	return builder
}

// 是否在恢复启用时发送通知给预定了该会议室的员工
//
// 示例值：true
func (builder *RoomStatusEventBuilder) ResumeNotice(resumeNotice bool) *RoomStatusEventBuilder {
	builder.resumeNotice = resumeNotice
	builder.resumeNoticeFlag = true
	return builder
}

func (builder *RoomStatusEventBuilder) Build() *RoomStatusEvent {
	req := &RoomStatusEvent{}
	if builder.statusFlag {
		req.Status = &builder.status

	}
	if builder.scheduleStatusFlag {
		req.ScheduleStatus = &builder.scheduleStatus

	}
	if builder.disableStartTimeFlag {
		req.DisableStartTime = &builder.disableStartTime

	}
	if builder.disableEndTimeFlag {
		req.DisableEndTime = &builder.disableEndTime

	}
	if builder.disableReasonFlag {
		req.DisableReason = &builder.disableReason

	}
	if builder.contactIdsFlag {
		req.ContactIds = builder.contactIds
	}
	if builder.disableNoticeFlag {
		req.DisableNotice = &builder.disableNotice

	}
	if builder.resumeNoticeFlag {
		req.ResumeNotice = &builder.resumeNotice

	}
	return req
}

type ScopeConfig struct {
	ScopeType   *int        `json:"scope_type,omitempty"`   // 查询节点范围
	ScopeId     *string     `json:"scope_id,omitempty"`     // 查询节点ID：如果scope_type为1，则为层级ID，如果scope_type为2，则为会议室ID
	ScopeConfig *RoomConfig `json:"scope_config,omitempty"` // 节点配置
}

type ScopeConfigBuilder struct {
	scopeType       int // 查询节点范围
	scopeTypeFlag   bool
	scopeId         string // 查询节点ID：如果scope_type为1，则为层级ID，如果scope_type为2，则为会议室ID
	scopeIdFlag     bool
	scopeConfig     *RoomConfig // 节点配置
	scopeConfigFlag bool
}

func NewScopeConfigBuilder() *ScopeConfigBuilder {
	builder := &ScopeConfigBuilder{}
	return builder
}

// 查询节点范围
//
// 示例值：1
func (builder *ScopeConfigBuilder) ScopeType(scopeType int) *ScopeConfigBuilder {
	builder.scopeType = scopeType
	builder.scopeTypeFlag = true
	return builder
}

// 查询节点ID：如果scope_type为1，则为层级ID，如果scope_type为2，则为会议室ID
//
// 示例值：omm_608d34d82d531b27fa993902d350a307
func (builder *ScopeConfigBuilder) ScopeId(scopeId string) *ScopeConfigBuilder {
	builder.scopeId = scopeId
	builder.scopeIdFlag = true
	return builder
}

// 节点配置
//
// 示例值：
func (builder *ScopeConfigBuilder) ScopeConfig(scopeConfig *RoomConfig) *ScopeConfigBuilder {
	builder.scopeConfig = scopeConfig
	builder.scopeConfigFlag = true
	return builder
}

func (builder *ScopeConfigBuilder) Build() *ScopeConfig {
	req := &ScopeConfig{}
	if builder.scopeTypeFlag {
		req.ScopeType = &builder.scopeType

	}
	if builder.scopeIdFlag {
		req.ScopeId = &builder.scopeId

	}
	if builder.scopeConfigFlag {
		req.ScopeConfig = builder.scopeConfig
	}
	return req
}

type SubscribeDepartment struct {
	DepartmentId   *string `json:"department_id,omitempty"`   // 可预定部门id
	DepartmentName *string `json:"department_name,omitempty"` // 预定部门名称
}

type SubscribeDepartmentBuilder struct {
	departmentId       string // 可预定部门id
	departmentIdFlag   bool
	departmentName     string // 预定部门名称
	departmentNameFlag bool
}

func NewSubscribeDepartmentBuilder() *SubscribeDepartmentBuilder {
	builder := &SubscribeDepartmentBuilder{}
	return builder
}

// 可预定部门id
//
// 示例值：od-47d8b570b0a011e9679a755efcc5f61a
func (builder *SubscribeDepartmentBuilder) DepartmentId(departmentId string) *SubscribeDepartmentBuilder {
	builder.departmentId = departmentId
	builder.departmentIdFlag = true
	return builder
}

// 预定部门名称
//
// 示例值：
func (builder *SubscribeDepartmentBuilder) DepartmentName(departmentName string) *SubscribeDepartmentBuilder {
	builder.departmentName = departmentName
	builder.departmentNameFlag = true
	return builder
}

func (builder *SubscribeDepartmentBuilder) Build() *SubscribeDepartment {
	req := &SubscribeDepartment{}
	if builder.departmentIdFlag {
		req.DepartmentId = &builder.departmentId

	}
	if builder.departmentNameFlag {
		req.DepartmentName = &builder.departmentName

	}
	return req
}

type SubscribeUser struct {
	UserId   *string `json:"user_id,omitempty"`   // 审批人/预定人id
	UserName *string `json:"user_name,omitempty"` // 预订人姓名
}

type SubscribeUserBuilder struct {
	userId       string // 审批人/预定人id
	userIdFlag   bool
	userName     string // 预订人姓名
	userNameFlag bool
}

func NewSubscribeUserBuilder() *SubscribeUserBuilder {
	builder := &SubscribeUserBuilder{}
	return builder
}

// 审批人/预定人id
//
// 示例值：ou_a27b07a9071d90577c0177bcec98f856
func (builder *SubscribeUserBuilder) UserId(userId string) *SubscribeUserBuilder {
	builder.userId = userId
	builder.userIdFlag = true
	return builder
}

// 预订人姓名
//
// 示例值：
func (builder *SubscribeUserBuilder) UserName(userName string) *SubscribeUserBuilder {
	builder.userName = userName
	builder.userNameFlag = true
	return builder
}

func (builder *SubscribeUserBuilder) Build() *SubscribeUser {
	req := &SubscribeUser{}
	if builder.userIdFlag {
		req.UserId = &builder.userId

	}
	if builder.userNameFlag {
		req.UserName = &builder.userName

	}
	return req
}

type TimeConfig struct {
	TimeSwitch    *int    `json:"time_switch,omitempty"`     // 预定时间开关：0 代表关闭，1 代表开启
	DaysInAdvance *int    `json:"days_in_advance,omitempty"` // 最早可提前 ; days_in_advance 预定会议室（单位：天，取值范围[1-730]）;<b>说明</b>：不填写时，默认更新为 365
	OpeningHour   *string `json:"opening_hour,omitempty"`    // 开放当天可于 ; opening_hour 开始预定（单位：秒，取值范围[0,86400]）;<b>说明</b>：;1.  不填写时默认更新为 ; 28800 ;2.  如果填写的值不是 60 ; 的倍数，则自动会更新为离其最近的 60 整数倍的值。
	StartTime     *string `json:"start_time,omitempty"`      // 每日可预定时间范围的开始时间（单位：秒，取值范围[0,86400]）;<b>说明</b>：;1.  不填写时，默认更新为 0 ，此时填写的  end_time 不得小于 30。;2.  当 start_time 与;  end_time 均填写时，; end_time 至少超过 ; start_time 30 。;3.  如果填写的值不是 60 的倍数，则自动会更新为离其最近的 60 整数倍的值。
	EndTime       *string `json:"end_time,omitempty"`        // 每日可预定时间范围结束时间（单位：秒，取值范围[0,86400]）;<b>说明</b>：;1.  不填写时，默认更新为 86400 ，此时填写的; start_time 不得大于等于 86370 。;2.  当 start_time 与;  end_time 均填写时，; end_time 至少要超过;  start_time 30。;3.  如果填写的值不是  60 的倍数，则自动会更新为离其最近的 60 整数倍的值。
	MaxDuration   *int    `json:"max_duration,omitempty"`    // 单次会议室可预定时长上限（单位：小时，取值范围[1,99]）;<b>说明</b>：不填写时默认更新为 2
}

type TimeConfigBuilder struct {
	timeSwitch        int // 预定时间开关：0 代表关闭，1 代表开启
	timeSwitchFlag    bool
	daysInAdvance     int // 最早可提前 ; days_in_advance 预定会议室（单位：天，取值范围[1-730]）;<b>说明</b>：不填写时，默认更新为 365
	daysInAdvanceFlag bool
	openingHour       string // 开放当天可于 ; opening_hour 开始预定（单位：秒，取值范围[0,86400]）;<b>说明</b>：;1.  不填写时默认更新为 ; 28800 ;2.  如果填写的值不是 60 ; 的倍数，则自动会更新为离其最近的 60 整数倍的值。
	openingHourFlag   bool
	startTime         string // 每日可预定时间范围的开始时间（单位：秒，取值范围[0,86400]）;<b>说明</b>：;1.  不填写时，默认更新为 0 ，此时填写的  end_time 不得小于 30。;2.  当 start_time 与;  end_time 均填写时，; end_time 至少超过 ; start_time 30 。;3.  如果填写的值不是 60 的倍数，则自动会更新为离其最近的 60 整数倍的值。
	startTimeFlag     bool
	endTime           string // 每日可预定时间范围结束时间（单位：秒，取值范围[0,86400]）;<b>说明</b>：;1.  不填写时，默认更新为 86400 ，此时填写的; start_time 不得大于等于 86370 。;2.  当 start_time 与;  end_time 均填写时，; end_time 至少要超过;  start_time 30。;3.  如果填写的值不是  60 的倍数，则自动会更新为离其最近的 60 整数倍的值。
	endTimeFlag       bool
	maxDuration       int // 单次会议室可预定时长上限（单位：小时，取值范围[1,99]）;<b>说明</b>：不填写时默认更新为 2
	maxDurationFlag   bool
}

func NewTimeConfigBuilder() *TimeConfigBuilder {
	builder := &TimeConfigBuilder{}
	return builder
}

// 预定时间开关：0 代表关闭，1 代表开启
//
// 示例值：1
func (builder *TimeConfigBuilder) TimeSwitch(timeSwitch int) *TimeConfigBuilder {
	builder.timeSwitch = timeSwitch
	builder.timeSwitchFlag = true
	return builder
}

// 最早可提前 ; days_in_advance 预定会议室（单位：天，取值范围[1-730]）;<b>说明</b>：不填写时，默认更新为 365
//
// 示例值：30
func (builder *TimeConfigBuilder) DaysInAdvance(daysInAdvance int) *TimeConfigBuilder {
	builder.daysInAdvance = daysInAdvance
	builder.daysInAdvanceFlag = true
	return builder
}

// 开放当天可于 ; opening_hour 开始预定（单位：秒，取值范围[0,86400]）;<b>说明</b>：;1.  不填写时默认更新为 ; 28800 ;2.  如果填写的值不是 60 ; 的倍数，则自动会更新为离其最近的 60 整数倍的值。
//
// 示例值：27900
func (builder *TimeConfigBuilder) OpeningHour(openingHour string) *TimeConfigBuilder {
	builder.openingHour = openingHour
	builder.openingHourFlag = true
	return builder
}

// 每日可预定时间范围的开始时间（单位：秒，取值范围[0,86400]）;<b>说明</b>：;1.  不填写时，默认更新为 0 ，此时填写的  end_time 不得小于 30。;2.  当 start_time 与;  end_time 均填写时，; end_time 至少超过 ; start_time 30 。;3.  如果填写的值不是 60 的倍数，则自动会更新为离其最近的 60 整数倍的值。
//
// 示例值：0
func (builder *TimeConfigBuilder) StartTime(startTime string) *TimeConfigBuilder {
	builder.startTime = startTime
	builder.startTimeFlag = true
	return builder
}

// 每日可预定时间范围结束时间（单位：秒，取值范围[0,86400]）;<b>说明</b>：;1.  不填写时，默认更新为 86400 ，此时填写的; start_time 不得大于等于 86370 。;2.  当 start_time 与;  end_time 均填写时，; end_time 至少要超过;  start_time 30。;3.  如果填写的值不是  60 的倍数，则自动会更新为离其最近的 60 整数倍的值。
//
// 示例值：86400
func (builder *TimeConfigBuilder) EndTime(endTime string) *TimeConfigBuilder {
	builder.endTime = endTime
	builder.endTimeFlag = true
	return builder
}

// 单次会议室可预定时长上限（单位：小时，取值范围[1,99]）;<b>说明</b>：不填写时默认更新为 2
//
// 示例值：24
func (builder *TimeConfigBuilder) MaxDuration(maxDuration int) *TimeConfigBuilder {
	builder.maxDuration = maxDuration
	builder.maxDurationFlag = true
	return builder
}

func (builder *TimeConfigBuilder) Build() *TimeConfig {
	req := &TimeConfig{}
	if builder.timeSwitchFlag {
		req.TimeSwitch = &builder.timeSwitch

	}
	if builder.daysInAdvanceFlag {
		req.DaysInAdvance = &builder.daysInAdvance

	}
	if builder.openingHourFlag {
		req.OpeningHour = &builder.openingHour

	}
	if builder.startTimeFlag {
		req.StartTime = &builder.startTime

	}
	if builder.endTimeFlag {
		req.EndTime = &builder.endTime

	}
	if builder.maxDurationFlag {
		req.MaxDuration = &builder.maxDuration

	}
	return req
}

type UserId struct {
	UserId  *string `json:"user_id,omitempty"`  //
	OpenId  *string `json:"open_id,omitempty"`  //
	UnionId *string `json:"union_id,omitempty"` //
}

type UserIdBuilder struct {
	userId      string //
	userIdFlag  bool
	openId      string //
	openIdFlag  bool
	unionId     string //
	unionIdFlag bool
}

func NewUserIdBuilder() *UserIdBuilder {
	builder := &UserIdBuilder{}
	return builder
}

//
//
// 示例值：
func (builder *UserIdBuilder) UserId(userId string) *UserIdBuilder {
	builder.userId = userId
	builder.userIdFlag = true
	return builder
}

//
//
// 示例值：
func (builder *UserIdBuilder) OpenId(openId string) *UserIdBuilder {
	builder.openId = openId
	builder.openIdFlag = true
	return builder
}

//
//
// 示例值：
func (builder *UserIdBuilder) UnionId(unionId string) *UserIdBuilder {
	builder.unionId = unionId
	builder.unionIdFlag = true
	return builder
}

func (builder *UserIdBuilder) Build() *UserId {
	req := &UserId{}
	if builder.userIdFlag {
		req.UserId = &builder.userId

	}
	if builder.openIdFlag {
		req.OpenId = &builder.openId

	}
	if builder.unionIdFlag {
		req.UnionId = &builder.unionId

	}
	return req
}

type ListAlertReqBuilder struct {
	apiReq *larkcore.ApiReq
	limit  int // 最大返回多少记录，当使用迭代器访问时才有效
}

func NewListAlertReqBuilder() *ListAlertReqBuilder {
	builder := &ListAlertReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 最大返回多少记录，当使用迭代器访问时才有效
func (builder *ListAlertReqBuilder) Limit(limit int) *ListAlertReqBuilder {
	builder.limit = limit
	return builder
}

// 开始时间（unix时间，单位sec）
//
// 示例值：1608888867
func (builder *ListAlertReqBuilder) StartTime(startTime string) *ListAlertReqBuilder {
	builder.apiReq.QueryParams.Set("start_time", fmt.Sprint(startTime))
	return builder
}

// 结束时间（unix时间，单位sec）
//
// 示例值：1608888867
func (builder *ListAlertReqBuilder) EndTime(endTime string) *ListAlertReqBuilder {
	builder.apiReq.QueryParams.Set("end_time", fmt.Sprint(endTime))
	return builder
}

// 查询对象类型，不填返回所有
//
// 示例值：1
func (builder *ListAlertReqBuilder) QueryType(queryType int) *ListAlertReqBuilder {
	builder.apiReq.QueryParams.Set("query_type", fmt.Sprint(queryType))
	return builder
}

// 查询对象ID
//
// 示例值：6911188411932033028
func (builder *ListAlertReqBuilder) QueryValue(queryValue string) *ListAlertReqBuilder {
	builder.apiReq.QueryParams.Set("query_value", fmt.Sprint(queryValue))
	return builder
}

// 请求期望返回的告警记录数量，不足则返回全部，该值默认为 100，最大为 1000
//
// 示例值：100
func (builder *ListAlertReqBuilder) PageSize(pageSize int) *ListAlertReqBuilder {
	builder.apiReq.QueryParams.Set("page_size", fmt.Sprint(pageSize))
	return builder
}

// 分页标记，第一次请求不填，表示从头开始遍历；分页查询结果还有更多项时会同时返回新的 page_token，下次遍历可采用该 page_token 获取查询结果
//
// 示例值：100
func (builder *ListAlertReqBuilder) PageToken(pageToken string) *ListAlertReqBuilder {
	builder.apiReq.QueryParams.Set("page_token", fmt.Sprint(pageToken))
	return builder
}

func (builder *ListAlertReqBuilder) Build() *ListAlertReq {
	req := &ListAlertReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.Limit = builder.limit
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type ListAlertReq struct {
	apiReq *larkcore.ApiReq
	Limit  int // 最多返回多少记录，只有在使用迭代器访问时，才有效

}

type ListAlertRespData struct {
	HasMore   *bool    `json:"has_more,omitempty"`   // 是否还有数据
	PageToken *string  `json:"page_token,omitempty"` // 下一页分页的token，下次请求时传入
	Items     []*Alert `json:"items,omitempty"`      // 告警记录
}

type ListAlertResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *ListAlertRespData `json:"data"` // 业务数据
}

func (resp *ListAlertResp) Success() bool {
	return resp.Code == 0
}

type DownloadExportReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewDownloadExportReqBuilder() *DownloadExportReqBuilder {
	builder := &DownloadExportReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 文档token
//
// 示例值：6yHu7Igp7Igy62Ez6fLr6IJz7j9i5WMe6fHq5yZeY2Jz6yLqYAMAY46fZfEz64Lr5fYyYQ==
func (builder *DownloadExportReqBuilder) FileToken(fileToken string) *DownloadExportReqBuilder {
	builder.apiReq.QueryParams.Set("file_token", fmt.Sprint(fileToken))
	return builder
}

func (builder *DownloadExportReqBuilder) Build() *DownloadExportReq {
	req := &DownloadExportReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type DownloadExportReq struct {
	apiReq *larkcore.ApiReq
}

type DownloadExportResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	File     io.Reader `json:"-"`
	FileName string    `json:"-"`
}

func (resp *DownloadExportResp) Success() bool {
	return resp.Code == 0
}

func (resp *DownloadExportResp) WriteFile(fileName string) error {
	bs, err := ioutil.ReadAll(resp.File)
	if err != nil {
		return err
	}

	err = ioutil.WriteFile(fileName, bs, 0666)
	if err != nil {
		return err
	}
	return nil
}

type GetExportReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewGetExportReqBuilder() *GetExportReqBuilder {
	builder := &GetExportReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 任务id
//
// 示例值：7108646852144136212
func (builder *GetExportReqBuilder) TaskId(taskId string) *GetExportReqBuilder {
	builder.apiReq.PathParams.Set("task_id", fmt.Sprint(taskId))
	return builder
}

func (builder *GetExportReqBuilder) Build() *GetExportReq {
	req := &GetExportReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	return req
}

type GetExportReq struct {
	apiReq *larkcore.ApiReq
}

type GetExportRespData struct {
	Status    *int    `json:"status,omitempty"`     // 任务状态
	Url       *string `json:"url,omitempty"`        // 文件下载地址
	FileToken *string `json:"file_token,omitempty"` // 文件token
	FailMsg   *string `json:"fail_msg,omitempty"`   // 失败信息
}

type GetExportResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *GetExportRespData `json:"data"` // 业务数据
}

func (resp *GetExportResp) Success() bool {
	return resp.Code == 0
}

type MeetingListExportReqBodyBuilder struct {
	startTime     string // 查询开始时间（unix时间，单位sec）
	startTimeFlag bool
	endTime       string // 查询结束时间（unix时间，单位sec）
	endTimeFlag   bool
	meetingNo     string // 按9位会议号筛选（最多一个筛选条件）
	meetingNoFlag bool
	userId        string // 按参会Lark用户筛选（最多一个筛选条件）
	userIdFlag    bool
	roomId        string // 按参会Rooms筛选（最多一个筛选条件）
	roomIdFlag    bool
}

func NewMeetingListExportReqBodyBuilder() *MeetingListExportReqBodyBuilder {
	builder := &MeetingListExportReqBodyBuilder{}
	return builder
}

// 查询开始时间（unix时间，单位sec）
//
//示例值：1655276858
func (builder *MeetingListExportReqBodyBuilder) StartTime(startTime string) *MeetingListExportReqBodyBuilder {
	builder.startTime = startTime
	builder.startTimeFlag = true
	return builder
}

// 查询结束时间（unix时间，单位sec）
//
//示例值：1655276858
func (builder *MeetingListExportReqBodyBuilder) EndTime(endTime string) *MeetingListExportReqBodyBuilder {
	builder.endTime = endTime
	builder.endTimeFlag = true
	return builder
}

// 按9位会议号筛选（最多一个筛选条件）
//
//示例值：123456789
func (builder *MeetingListExportReqBodyBuilder) MeetingNo(meetingNo string) *MeetingListExportReqBodyBuilder {
	builder.meetingNo = meetingNo
	builder.meetingNoFlag = true
	return builder
}

// 按参会Lark用户筛选（最多一个筛选条件）
//
//示例值：ou_3ec3f6a28a0d08c45d895276e8e5e19b
func (builder *MeetingListExportReqBodyBuilder) UserId(userId string) *MeetingListExportReqBodyBuilder {
	builder.userId = userId
	builder.userIdFlag = true
	return builder
}

// 按参会Rooms筛选（最多一个筛选条件）
//
//示例值：omm_eada1d61a550955240c28757e7dec3af
func (builder *MeetingListExportReqBodyBuilder) RoomId(roomId string) *MeetingListExportReqBodyBuilder {
	builder.roomId = roomId
	builder.roomIdFlag = true
	return builder
}

func (builder *MeetingListExportReqBodyBuilder) Build() *MeetingListExportReqBody {
	req := &MeetingListExportReqBody{}
	if builder.startTimeFlag {
		req.StartTime = &builder.startTime
	}
	if builder.endTimeFlag {
		req.EndTime = &builder.endTime
	}
	if builder.meetingNoFlag {
		req.MeetingNo = &builder.meetingNo
	}
	if builder.userIdFlag {
		req.UserId = &builder.userId
	}
	if builder.roomIdFlag {
		req.RoomId = &builder.roomId
	}
	return req
}

type MeetingListExportPathReqBodyBuilder struct {
	startTime     string // 查询开始时间（unix时间，单位sec）
	startTimeFlag bool
	endTime       string // 查询结束时间（unix时间，单位sec）
	endTimeFlag   bool
	meetingNo     string // 按9位会议号筛选（最多一个筛选条件）
	meetingNoFlag bool
	userId        string // 按参会Lark用户筛选（最多一个筛选条件）
	userIdFlag    bool
	roomId        string // 按参会Rooms筛选（最多一个筛选条件）
	roomIdFlag    bool
}

func NewMeetingListExportPathReqBodyBuilder() *MeetingListExportPathReqBodyBuilder {
	builder := &MeetingListExportPathReqBodyBuilder{}
	return builder
}

// 查询开始时间（unix时间，单位sec）
//
// 示例值：1655276858
func (builder *MeetingListExportPathReqBodyBuilder) StartTime(startTime string) *MeetingListExportPathReqBodyBuilder {
	builder.startTime = startTime
	builder.startTimeFlag = true
	return builder
}

// 查询结束时间（unix时间，单位sec）
//
// 示例值：1655276858
func (builder *MeetingListExportPathReqBodyBuilder) EndTime(endTime string) *MeetingListExportPathReqBodyBuilder {
	builder.endTime = endTime
	builder.endTimeFlag = true
	return builder
}

// 按9位会议号筛选（最多一个筛选条件）
//
// 示例值：123456789
func (builder *MeetingListExportPathReqBodyBuilder) MeetingNo(meetingNo string) *MeetingListExportPathReqBodyBuilder {
	builder.meetingNo = meetingNo
	builder.meetingNoFlag = true
	return builder
}

// 按参会Lark用户筛选（最多一个筛选条件）
//
// 示例值：ou_3ec3f6a28a0d08c45d895276e8e5e19b
func (builder *MeetingListExportPathReqBodyBuilder) UserId(userId string) *MeetingListExportPathReqBodyBuilder {
	builder.userId = userId
	builder.userIdFlag = true
	return builder
}

// 按参会Rooms筛选（最多一个筛选条件）
//
// 示例值：omm_eada1d61a550955240c28757e7dec3af
func (builder *MeetingListExportPathReqBodyBuilder) RoomId(roomId string) *MeetingListExportPathReqBodyBuilder {
	builder.roomId = roomId
	builder.roomIdFlag = true
	return builder
}

func (builder *MeetingListExportPathReqBodyBuilder) Build() (*MeetingListExportReqBody, error) {
	req := &MeetingListExportReqBody{}
	if builder.startTimeFlag {
		req.StartTime = &builder.startTime
	}
	if builder.endTimeFlag {
		req.EndTime = &builder.endTime
	}
	if builder.meetingNoFlag {
		req.MeetingNo = &builder.meetingNo
	}
	if builder.userIdFlag {
		req.UserId = &builder.userId
	}
	if builder.roomIdFlag {
		req.RoomId = &builder.roomId
	}
	return req, nil
}

type MeetingListExportReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *MeetingListExportReqBody
}

func NewMeetingListExportReqBuilder() *MeetingListExportReqBuilder {
	builder := &MeetingListExportReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 此次调用中使用的用户ID的类型，默认使用open_id可不填
//
// 示例值：
func (builder *MeetingListExportReqBuilder) UserIdType(userIdType string) *MeetingListExportReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

// 导出会议明细，具体权限要求请参考「导出概述」
func (builder *MeetingListExportReqBuilder) Body(body *MeetingListExportReqBody) *MeetingListExportReqBuilder {
	builder.body = body
	return builder
}

func (builder *MeetingListExportReqBuilder) Build() *MeetingListExportReq {
	req := &MeetingListExportReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.body
	return req
}

type MeetingListExportReqBody struct {
	StartTime *string `json:"start_time,omitempty"` // 查询开始时间（unix时间，单位sec）
	EndTime   *string `json:"end_time,omitempty"`   // 查询结束时间（unix时间，单位sec）
	MeetingNo *string `json:"meeting_no,omitempty"` // 按9位会议号筛选（最多一个筛选条件）
	UserId    *string `json:"user_id,omitempty"`    // 按参会Lark用户筛选（最多一个筛选条件）
	RoomId    *string `json:"room_id,omitempty"`    // 按参会Rooms筛选（最多一个筛选条件）
}

type MeetingListExportReq struct {
	apiReq *larkcore.ApiReq
	Body   *MeetingListExportReqBody `body:""`
}

type MeetingListExportRespData struct {
	TaskId *string `json:"task_id,omitempty"` // 任务id
}

type MeetingListExportResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *MeetingListExportRespData `json:"data"` // 业务数据
}

func (resp *MeetingListExportResp) Success() bool {
	return resp.Code == 0
}

type ParticipantListExportReqBodyBuilder struct {
	meetingStartTime     string // 会议开始时间（unix时间，单位sec）
	meetingStartTimeFlag bool
	meetingEndTime       string // 会议结束时间（unix时间，单位sec）
	meetingEndTimeFlag   bool
	meetingNo            string // 9位会议号
	meetingNoFlag        bool
	userId               string // 按参会Lark用户筛选（最多一个筛选条件）
	userIdFlag           bool
	roomId               string // 按参会Rooms筛选（最多一个筛选条件）
	roomIdFlag           bool
}

func NewParticipantListExportReqBodyBuilder() *ParticipantListExportReqBodyBuilder {
	builder := &ParticipantListExportReqBodyBuilder{}
	return builder
}

// 会议开始时间（unix时间，单位sec）
//
//示例值：1655276858
func (builder *ParticipantListExportReqBodyBuilder) MeetingStartTime(meetingStartTime string) *ParticipantListExportReqBodyBuilder {
	builder.meetingStartTime = meetingStartTime
	builder.meetingStartTimeFlag = true
	return builder
}

// 会议结束时间（unix时间，单位sec）
//
//示例值：1655276858
func (builder *ParticipantListExportReqBodyBuilder) MeetingEndTime(meetingEndTime string) *ParticipantListExportReqBodyBuilder {
	builder.meetingEndTime = meetingEndTime
	builder.meetingEndTimeFlag = true
	return builder
}

// 9位会议号
//
//示例值：123456789
func (builder *ParticipantListExportReqBodyBuilder) MeetingNo(meetingNo string) *ParticipantListExportReqBodyBuilder {
	builder.meetingNo = meetingNo
	builder.meetingNoFlag = true
	return builder
}

// 按参会Lark用户筛选（最多一个筛选条件）
//
//示例值：ou_3ec3f6a28a0d08c45d895276e8e5e19b
func (builder *ParticipantListExportReqBodyBuilder) UserId(userId string) *ParticipantListExportReqBodyBuilder {
	builder.userId = userId
	builder.userIdFlag = true
	return builder
}

// 按参会Rooms筛选（最多一个筛选条件）
//
//示例值：omm_eada1d61a550955240c28757e7dec3af
func (builder *ParticipantListExportReqBodyBuilder) RoomId(roomId string) *ParticipantListExportReqBodyBuilder {
	builder.roomId = roomId
	builder.roomIdFlag = true
	return builder
}

func (builder *ParticipantListExportReqBodyBuilder) Build() *ParticipantListExportReqBody {
	req := &ParticipantListExportReqBody{}
	if builder.meetingStartTimeFlag {
		req.MeetingStartTime = &builder.meetingStartTime
	}
	if builder.meetingEndTimeFlag {
		req.MeetingEndTime = &builder.meetingEndTime
	}
	if builder.meetingNoFlag {
		req.MeetingNo = &builder.meetingNo
	}
	if builder.userIdFlag {
		req.UserId = &builder.userId
	}
	if builder.roomIdFlag {
		req.RoomId = &builder.roomId
	}
	return req
}

type ParticipantListExportPathReqBodyBuilder struct {
	meetingStartTime     string // 会议开始时间（unix时间，单位sec）
	meetingStartTimeFlag bool
	meetingEndTime       string // 会议结束时间（unix时间，单位sec）
	meetingEndTimeFlag   bool
	meetingNo            string // 9位会议号
	meetingNoFlag        bool
	userId               string // 按参会Lark用户筛选（最多一个筛选条件）
	userIdFlag           bool
	roomId               string // 按参会Rooms筛选（最多一个筛选条件）
	roomIdFlag           bool
}

func NewParticipantListExportPathReqBodyBuilder() *ParticipantListExportPathReqBodyBuilder {
	builder := &ParticipantListExportPathReqBodyBuilder{}
	return builder
}

// 会议开始时间（unix时间，单位sec）
//
// 示例值：1655276858
func (builder *ParticipantListExportPathReqBodyBuilder) MeetingStartTime(meetingStartTime string) *ParticipantListExportPathReqBodyBuilder {
	builder.meetingStartTime = meetingStartTime
	builder.meetingStartTimeFlag = true
	return builder
}

// 会议结束时间（unix时间，单位sec）
//
// 示例值：1655276858
func (builder *ParticipantListExportPathReqBodyBuilder) MeetingEndTime(meetingEndTime string) *ParticipantListExportPathReqBodyBuilder {
	builder.meetingEndTime = meetingEndTime
	builder.meetingEndTimeFlag = true
	return builder
}

// 9位会议号
//
// 示例值：123456789
func (builder *ParticipantListExportPathReqBodyBuilder) MeetingNo(meetingNo string) *ParticipantListExportPathReqBodyBuilder {
	builder.meetingNo = meetingNo
	builder.meetingNoFlag = true
	return builder
}

// 按参会Lark用户筛选（最多一个筛选条件）
//
// 示例值：ou_3ec3f6a28a0d08c45d895276e8e5e19b
func (builder *ParticipantListExportPathReqBodyBuilder) UserId(userId string) *ParticipantListExportPathReqBodyBuilder {
	builder.userId = userId
	builder.userIdFlag = true
	return builder
}

// 按参会Rooms筛选（最多一个筛选条件）
//
// 示例值：omm_eada1d61a550955240c28757e7dec3af
func (builder *ParticipantListExportPathReqBodyBuilder) RoomId(roomId string) *ParticipantListExportPathReqBodyBuilder {
	builder.roomId = roomId
	builder.roomIdFlag = true
	return builder
}

func (builder *ParticipantListExportPathReqBodyBuilder) Build() (*ParticipantListExportReqBody, error) {
	req := &ParticipantListExportReqBody{}
	if builder.meetingStartTimeFlag {
		req.MeetingStartTime = &builder.meetingStartTime
	}
	if builder.meetingEndTimeFlag {
		req.MeetingEndTime = &builder.meetingEndTime
	}
	if builder.meetingNoFlag {
		req.MeetingNo = &builder.meetingNo
	}
	if builder.userIdFlag {
		req.UserId = &builder.userId
	}
	if builder.roomIdFlag {
		req.RoomId = &builder.roomId
	}
	return req, nil
}

type ParticipantListExportReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *ParticipantListExportReqBody
}

func NewParticipantListExportReqBuilder() *ParticipantListExportReqBuilder {
	builder := &ParticipantListExportReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 此次调用中使用的用户ID的类型，默认使用open_id可不填
//
// 示例值：
func (builder *ParticipantListExportReqBuilder) UserIdType(userIdType string) *ParticipantListExportReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

// 导出某个会议的参会人详情列表，具体权限要求请参考「导出概述」
func (builder *ParticipantListExportReqBuilder) Body(body *ParticipantListExportReqBody) *ParticipantListExportReqBuilder {
	builder.body = body
	return builder
}

func (builder *ParticipantListExportReqBuilder) Build() *ParticipantListExportReq {
	req := &ParticipantListExportReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.body
	return req
}

type ParticipantListExportReqBody struct {
	MeetingStartTime *string `json:"meeting_start_time,omitempty"` // 会议开始时间（unix时间，单位sec）
	MeetingEndTime   *string `json:"meeting_end_time,omitempty"`   // 会议结束时间（unix时间，单位sec）
	MeetingNo        *string `json:"meeting_no,omitempty"`         // 9位会议号
	UserId           *string `json:"user_id,omitempty"`            // 按参会Lark用户筛选（最多一个筛选条件）
	RoomId           *string `json:"room_id,omitempty"`            // 按参会Rooms筛选（最多一个筛选条件）
}

type ParticipantListExportReq struct {
	apiReq *larkcore.ApiReq
	Body   *ParticipantListExportReqBody `body:""`
}

type ParticipantListExportRespData struct {
	TaskId *string `json:"task_id,omitempty"` // 任务id
}

type ParticipantListExportResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *ParticipantListExportRespData `json:"data"` // 业务数据
}

func (resp *ParticipantListExportResp) Success() bool {
	return resp.Code == 0
}

type ParticipantQualityListExportReqBodyBuilder struct {
	meetingStartTime     string // 会议开始时间（unix时间，单位sec）
	meetingStartTimeFlag bool
	meetingEndTime       string // 会议结束时间（unix时间，单位sec）
	meetingEndTimeFlag   bool
	meetingNo            string // 9位会议号
	meetingNoFlag        bool
	joinTime             string // 参会人入会时间（unix时间，单位sec）
	joinTimeFlag         bool
	userId               string // 参会人为Lark用户时填入，room_id和user_id必须只填一个
	userIdFlag           bool
	roomId               string // 参会人为Rooms时填入，room_id和user_id必须只填一个
	roomIdFlag           bool
}

func NewParticipantQualityListExportReqBodyBuilder() *ParticipantQualityListExportReqBodyBuilder {
	builder := &ParticipantQualityListExportReqBodyBuilder{}
	return builder
}

// 会议开始时间（unix时间，单位sec）
//
//示例值：1655276858
func (builder *ParticipantQualityListExportReqBodyBuilder) MeetingStartTime(meetingStartTime string) *ParticipantQualityListExportReqBodyBuilder {
	builder.meetingStartTime = meetingStartTime
	builder.meetingStartTimeFlag = true
	return builder
}

// 会议结束时间（unix时间，单位sec）
//
//示例值：1655276858
func (builder *ParticipantQualityListExportReqBodyBuilder) MeetingEndTime(meetingEndTime string) *ParticipantQualityListExportReqBodyBuilder {
	builder.meetingEndTime = meetingEndTime
	builder.meetingEndTimeFlag = true
	return builder
}

// 9位会议号
//
//示例值：123456789
func (builder *ParticipantQualityListExportReqBodyBuilder) MeetingNo(meetingNo string) *ParticipantQualityListExportReqBodyBuilder {
	builder.meetingNo = meetingNo
	builder.meetingNoFlag = true
	return builder
}

// 参会人入会时间（unix时间，单位sec）
//
//示例值：1655276858
func (builder *ParticipantQualityListExportReqBodyBuilder) JoinTime(joinTime string) *ParticipantQualityListExportReqBodyBuilder {
	builder.joinTime = joinTime
	builder.joinTimeFlag = true
	return builder
}

// 参会人为Lark用户时填入，room_id和user_id必须只填一个
//
//示例值：ou_3ec3f6a28a0d08c45d895276e8e5e19b
func (builder *ParticipantQualityListExportReqBodyBuilder) UserId(userId string) *ParticipantQualityListExportReqBodyBuilder {
	builder.userId = userId
	builder.userIdFlag = true
	return builder
}

// 参会人为Rooms时填入，room_id和user_id必须只填一个
//
//示例值：omm_eada1d61a550955240c28757e7dec3af
func (builder *ParticipantQualityListExportReqBodyBuilder) RoomId(roomId string) *ParticipantQualityListExportReqBodyBuilder {
	builder.roomId = roomId
	builder.roomIdFlag = true
	return builder
}

func (builder *ParticipantQualityListExportReqBodyBuilder) Build() *ParticipantQualityListExportReqBody {
	req := &ParticipantQualityListExportReqBody{}
	if builder.meetingStartTimeFlag {
		req.MeetingStartTime = &builder.meetingStartTime
	}
	if builder.meetingEndTimeFlag {
		req.MeetingEndTime = &builder.meetingEndTime
	}
	if builder.meetingNoFlag {
		req.MeetingNo = &builder.meetingNo
	}
	if builder.joinTimeFlag {
		req.JoinTime = &builder.joinTime
	}
	if builder.userIdFlag {
		req.UserId = &builder.userId
	}
	if builder.roomIdFlag {
		req.RoomId = &builder.roomId
	}
	return req
}

type ParticipantQualityListExportPathReqBodyBuilder struct {
	meetingStartTime     string // 会议开始时间（unix时间，单位sec）
	meetingStartTimeFlag bool
	meetingEndTime       string // 会议结束时间（unix时间，单位sec）
	meetingEndTimeFlag   bool
	meetingNo            string // 9位会议号
	meetingNoFlag        bool
	joinTime             string // 参会人入会时间（unix时间，单位sec）
	joinTimeFlag         bool
	userId               string // 参会人为Lark用户时填入，room_id和user_id必须只填一个
	userIdFlag           bool
	roomId               string // 参会人为Rooms时填入，room_id和user_id必须只填一个
	roomIdFlag           bool
}

func NewParticipantQualityListExportPathReqBodyBuilder() *ParticipantQualityListExportPathReqBodyBuilder {
	builder := &ParticipantQualityListExportPathReqBodyBuilder{}
	return builder
}

// 会议开始时间（unix时间，单位sec）
//
// 示例值：1655276858
func (builder *ParticipantQualityListExportPathReqBodyBuilder) MeetingStartTime(meetingStartTime string) *ParticipantQualityListExportPathReqBodyBuilder {
	builder.meetingStartTime = meetingStartTime
	builder.meetingStartTimeFlag = true
	return builder
}

// 会议结束时间（unix时间，单位sec）
//
// 示例值：1655276858
func (builder *ParticipantQualityListExportPathReqBodyBuilder) MeetingEndTime(meetingEndTime string) *ParticipantQualityListExportPathReqBodyBuilder {
	builder.meetingEndTime = meetingEndTime
	builder.meetingEndTimeFlag = true
	return builder
}

// 9位会议号
//
// 示例值：123456789
func (builder *ParticipantQualityListExportPathReqBodyBuilder) MeetingNo(meetingNo string) *ParticipantQualityListExportPathReqBodyBuilder {
	builder.meetingNo = meetingNo
	builder.meetingNoFlag = true
	return builder
}

// 参会人入会时间（unix时间，单位sec）
//
// 示例值：1655276858
func (builder *ParticipantQualityListExportPathReqBodyBuilder) JoinTime(joinTime string) *ParticipantQualityListExportPathReqBodyBuilder {
	builder.joinTime = joinTime
	builder.joinTimeFlag = true
	return builder
}

// 参会人为Lark用户时填入，room_id和user_id必须只填一个
//
// 示例值：ou_3ec3f6a28a0d08c45d895276e8e5e19b
func (builder *ParticipantQualityListExportPathReqBodyBuilder) UserId(userId string) *ParticipantQualityListExportPathReqBodyBuilder {
	builder.userId = userId
	builder.userIdFlag = true
	return builder
}

// 参会人为Rooms时填入，room_id和user_id必须只填一个
//
// 示例值：omm_eada1d61a550955240c28757e7dec3af
func (builder *ParticipantQualityListExportPathReqBodyBuilder) RoomId(roomId string) *ParticipantQualityListExportPathReqBodyBuilder {
	builder.roomId = roomId
	builder.roomIdFlag = true
	return builder
}

func (builder *ParticipantQualityListExportPathReqBodyBuilder) Build() (*ParticipantQualityListExportReqBody, error) {
	req := &ParticipantQualityListExportReqBody{}
	if builder.meetingStartTimeFlag {
		req.MeetingStartTime = &builder.meetingStartTime
	}
	if builder.meetingEndTimeFlag {
		req.MeetingEndTime = &builder.meetingEndTime
	}
	if builder.meetingNoFlag {
		req.MeetingNo = &builder.meetingNo
	}
	if builder.joinTimeFlag {
		req.JoinTime = &builder.joinTime
	}
	if builder.userIdFlag {
		req.UserId = &builder.userId
	}
	if builder.roomIdFlag {
		req.RoomId = &builder.roomId
	}
	return req, nil
}

type ParticipantQualityListExportReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *ParticipantQualityListExportReqBody
}

func NewParticipantQualityListExportReqBuilder() *ParticipantQualityListExportReqBuilder {
	builder := &ParticipantQualityListExportReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 此次调用中使用的用户ID的类型，默认使用open_id可不填
//
// 示例值：
func (builder *ParticipantQualityListExportReqBuilder) UserIdType(userIdType string) *ParticipantQualityListExportReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

// 导出某场会议某个参会人的音视频&共享质量数据;，具体权限要求请参考「导出概述」
func (builder *ParticipantQualityListExportReqBuilder) Body(body *ParticipantQualityListExportReqBody) *ParticipantQualityListExportReqBuilder {
	builder.body = body
	return builder
}

func (builder *ParticipantQualityListExportReqBuilder) Build() *ParticipantQualityListExportReq {
	req := &ParticipantQualityListExportReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.body
	return req
}

type ParticipantQualityListExportReqBody struct {
	MeetingStartTime *string `json:"meeting_start_time,omitempty"` // 会议开始时间（unix时间，单位sec）
	MeetingEndTime   *string `json:"meeting_end_time,omitempty"`   // 会议结束时间（unix时间，单位sec）
	MeetingNo        *string `json:"meeting_no,omitempty"`         // 9位会议号
	JoinTime         *string `json:"join_time,omitempty"`          // 参会人入会时间（unix时间，单位sec）
	UserId           *string `json:"user_id,omitempty"`            // 参会人为Lark用户时填入，room_id和user_id必须只填一个
	RoomId           *string `json:"room_id,omitempty"`            // 参会人为Rooms时填入，room_id和user_id必须只填一个
}

type ParticipantQualityListExportReq struct {
	apiReq *larkcore.ApiReq
	Body   *ParticipantQualityListExportReqBody `body:""`
}

type ParticipantQualityListExportRespData struct {
	TaskId *string `json:"task_id,omitempty"` // 任务id
}

type ParticipantQualityListExportResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *ParticipantQualityListExportRespData `json:"data"` // 业务数据
}

func (resp *ParticipantQualityListExportResp) Success() bool {
	return resp.Code == 0
}

type ResourceReservationListExportReqBodyBuilder struct {
	roomLevelId     string // 会议室层级id
	roomLevelIdFlag bool
	needTopic       bool // 是否展示会议主题
	needTopicFlag   bool
	startTime       string // 查询开始时间（unix时间，单位sec）
	startTimeFlag   bool
	endTime         string // 查询结束时间（unix时间，单位sec）
	endTimeFlag     bool
	roomIds         []string // 待筛选的会议室id列表
	roomIdsFlag     bool
	isExclude       bool // 若为true表示导出room_ids范围外的会议室，默认为false
	isExcludeFlag   bool
}

func NewResourceReservationListExportReqBodyBuilder() *ResourceReservationListExportReqBodyBuilder {
	builder := &ResourceReservationListExportReqBodyBuilder{}
	return builder
}

// 会议室层级id
//
//示例值：omm_608d34d82d531b27fa993902d350a307
func (builder *ResourceReservationListExportReqBodyBuilder) RoomLevelId(roomLevelId string) *ResourceReservationListExportReqBodyBuilder {
	builder.roomLevelId = roomLevelId
	builder.roomLevelIdFlag = true
	return builder
}

// 是否展示会议主题
//
//示例值：true
func (builder *ResourceReservationListExportReqBodyBuilder) NeedTopic(needTopic bool) *ResourceReservationListExportReqBodyBuilder {
	builder.needTopic = needTopic
	builder.needTopicFlag = true
	return builder
}

// 查询开始时间（unix时间，单位sec）
//
//示例值：1655276858
func (builder *ResourceReservationListExportReqBodyBuilder) StartTime(startTime string) *ResourceReservationListExportReqBodyBuilder {
	builder.startTime = startTime
	builder.startTimeFlag = true
	return builder
}

// 查询结束时间（unix时间，单位sec）
//
//示例值：1655276858
func (builder *ResourceReservationListExportReqBodyBuilder) EndTime(endTime string) *ResourceReservationListExportReqBodyBuilder {
	builder.endTime = endTime
	builder.endTimeFlag = true
	return builder
}

// 待筛选的会议室id列表
//
//示例值：["omm_eada1d61a550955240c28757e7dec3af"]
func (builder *ResourceReservationListExportReqBodyBuilder) RoomIds(roomIds []string) *ResourceReservationListExportReqBodyBuilder {
	builder.roomIds = roomIds
	builder.roomIdsFlag = true
	return builder
}

// 若为true表示导出room_ids范围外的会议室，默认为false
//
//示例值：false
func (builder *ResourceReservationListExportReqBodyBuilder) IsExclude(isExclude bool) *ResourceReservationListExportReqBodyBuilder {
	builder.isExclude = isExclude
	builder.isExcludeFlag = true
	return builder
}

func (builder *ResourceReservationListExportReqBodyBuilder) Build() *ResourceReservationListExportReqBody {
	req := &ResourceReservationListExportReqBody{}
	if builder.roomLevelIdFlag {
		req.RoomLevelId = &builder.roomLevelId
	}
	if builder.needTopicFlag {
		req.NeedTopic = &builder.needTopic
	}
	if builder.startTimeFlag {
		req.StartTime = &builder.startTime
	}
	if builder.endTimeFlag {
		req.EndTime = &builder.endTime
	}
	if builder.roomIdsFlag {
		req.RoomIds = builder.roomIds
	}
	if builder.isExcludeFlag {
		req.IsExclude = &builder.isExclude
	}
	return req
}

type ResourceReservationListExportPathReqBodyBuilder struct {
	roomLevelId     string // 会议室层级id
	roomLevelIdFlag bool
	needTopic       bool // 是否展示会议主题
	needTopicFlag   bool
	startTime       string // 查询开始时间（unix时间，单位sec）
	startTimeFlag   bool
	endTime         string // 查询结束时间（unix时间，单位sec）
	endTimeFlag     bool
	roomIds         []string // 待筛选的会议室id列表
	roomIdsFlag     bool
	isExclude       bool // 若为true表示导出room_ids范围外的会议室，默认为false
	isExcludeFlag   bool
}

func NewResourceReservationListExportPathReqBodyBuilder() *ResourceReservationListExportPathReqBodyBuilder {
	builder := &ResourceReservationListExportPathReqBodyBuilder{}
	return builder
}

// 会议室层级id
//
// 示例值：omm_608d34d82d531b27fa993902d350a307
func (builder *ResourceReservationListExportPathReqBodyBuilder) RoomLevelId(roomLevelId string) *ResourceReservationListExportPathReqBodyBuilder {
	builder.roomLevelId = roomLevelId
	builder.roomLevelIdFlag = true
	return builder
}

// 是否展示会议主题
//
// 示例值：true
func (builder *ResourceReservationListExportPathReqBodyBuilder) NeedTopic(needTopic bool) *ResourceReservationListExportPathReqBodyBuilder {
	builder.needTopic = needTopic
	builder.needTopicFlag = true
	return builder
}

// 查询开始时间（unix时间，单位sec）
//
// 示例值：1655276858
func (builder *ResourceReservationListExportPathReqBodyBuilder) StartTime(startTime string) *ResourceReservationListExportPathReqBodyBuilder {
	builder.startTime = startTime
	builder.startTimeFlag = true
	return builder
}

// 查询结束时间（unix时间，单位sec）
//
// 示例值：1655276858
func (builder *ResourceReservationListExportPathReqBodyBuilder) EndTime(endTime string) *ResourceReservationListExportPathReqBodyBuilder {
	builder.endTime = endTime
	builder.endTimeFlag = true
	return builder
}

// 待筛选的会议室id列表
//
// 示例值：["omm_eada1d61a550955240c28757e7dec3af"]
func (builder *ResourceReservationListExportPathReqBodyBuilder) RoomIds(roomIds []string) *ResourceReservationListExportPathReqBodyBuilder {
	builder.roomIds = roomIds
	builder.roomIdsFlag = true
	return builder
}

// 若为true表示导出room_ids范围外的会议室，默认为false
//
// 示例值：false
func (builder *ResourceReservationListExportPathReqBodyBuilder) IsExclude(isExclude bool) *ResourceReservationListExportPathReqBodyBuilder {
	builder.isExclude = isExclude
	builder.isExcludeFlag = true
	return builder
}

func (builder *ResourceReservationListExportPathReqBodyBuilder) Build() (*ResourceReservationListExportReqBody, error) {
	req := &ResourceReservationListExportReqBody{}
	if builder.roomLevelIdFlag {
		req.RoomLevelId = &builder.roomLevelId
	}
	if builder.needTopicFlag {
		req.NeedTopic = &builder.needTopic
	}
	if builder.startTimeFlag {
		req.StartTime = &builder.startTime
	}
	if builder.endTimeFlag {
		req.EndTime = &builder.endTime
	}
	if builder.roomIdsFlag {
		req.RoomIds = builder.roomIds
	}
	if builder.isExcludeFlag {
		req.IsExclude = &builder.isExclude
	}
	return req, nil
}

type ResourceReservationListExportReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *ResourceReservationListExportReqBody
}

func NewResourceReservationListExportReqBuilder() *ResourceReservationListExportReqBuilder {
	builder := &ResourceReservationListExportReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 导出会议室预定数据，具体权限要求请参考「导出概述」
func (builder *ResourceReservationListExportReqBuilder) Body(body *ResourceReservationListExportReqBody) *ResourceReservationListExportReqBuilder {
	builder.body = body
	return builder
}

func (builder *ResourceReservationListExportReqBuilder) Build() *ResourceReservationListExportReq {
	req := &ResourceReservationListExportReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.Body = builder.body
	return req
}

type ResourceReservationListExportReqBody struct {
	RoomLevelId *string  `json:"room_level_id,omitempty"` // 会议室层级id
	NeedTopic   *bool    `json:"need_topic,omitempty"`    // 是否展示会议主题
	StartTime   *string  `json:"start_time,omitempty"`    // 查询开始时间（unix时间，单位sec）
	EndTime     *string  `json:"end_time,omitempty"`      // 查询结束时间（unix时间，单位sec）
	RoomIds     []string `json:"room_ids,omitempty"`      // 待筛选的会议室id列表
	IsExclude   *bool    `json:"is_exclude,omitempty"`    // 若为true表示导出room_ids范围外的会议室，默认为false
}

type ResourceReservationListExportReq struct {
	apiReq *larkcore.ApiReq
	Body   *ResourceReservationListExportReqBody `body:""`
}

type ResourceReservationListExportRespData struct {
	TaskId *string `json:"task_id,omitempty"` // 任务id
}

type ResourceReservationListExportResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *ResourceReservationListExportRespData `json:"data"` // 业务数据
}

func (resp *ResourceReservationListExportResp) Success() bool {
	return resp.Code == 0
}

type EndMeetingReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewEndMeetingReqBuilder() *EndMeetingReqBuilder {
	builder := &EndMeetingReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 会议ID（视频会议的唯一标识，视频会议开始后才会产生）
//
// 示例值：6911188411932033028
func (builder *EndMeetingReqBuilder) MeetingId(meetingId string) *EndMeetingReqBuilder {
	builder.apiReq.PathParams.Set("meeting_id", fmt.Sprint(meetingId))
	return builder
}

func (builder *EndMeetingReqBuilder) Build() *EndMeetingReq {
	req := &EndMeetingReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	return req
}

type EndMeetingReq struct {
	apiReq *larkcore.ApiReq
}

type EndMeetingResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *EndMeetingResp) Success() bool {
	return resp.Code == 0
}

type GetMeetingReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewGetMeetingReqBuilder() *GetMeetingReqBuilder {
	builder := &GetMeetingReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 会议ID（视频会议的唯一标识，视频会议开始后才会产生）
//
// 示例值：6911188411932033028
func (builder *GetMeetingReqBuilder) MeetingId(meetingId string) *GetMeetingReqBuilder {
	builder.apiReq.PathParams.Set("meeting_id", fmt.Sprint(meetingId))
	return builder
}

// 是否需要参会人列表
//
// 示例值：false
func (builder *GetMeetingReqBuilder) WithParticipants(withParticipants bool) *GetMeetingReqBuilder {
	builder.apiReq.QueryParams.Set("with_participants", fmt.Sprint(withParticipants))
	return builder
}

// 是否需要会中使用能力统计（仅限tenant_access_token）
//
// 示例值：false
func (builder *GetMeetingReqBuilder) WithMeetingAbility(withMeetingAbility bool) *GetMeetingReqBuilder {
	builder.apiReq.QueryParams.Set("with_meeting_ability", fmt.Sprint(withMeetingAbility))
	return builder
}

// 此次调用中使用的用户ID的类型，默认使用open_id可不填
//
// 示例值：
func (builder *GetMeetingReqBuilder) UserIdType(userIdType string) *GetMeetingReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

func (builder *GetMeetingReqBuilder) Build() *GetMeetingReq {
	req := &GetMeetingReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type GetMeetingReq struct {
	apiReq *larkcore.ApiReq
}

type GetMeetingRespData struct {
	Meeting *Meeting `json:"meeting,omitempty"` // 会议数据
}

type GetMeetingResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *GetMeetingRespData `json:"data"` // 业务数据
}

func (resp *GetMeetingResp) Success() bool {
	return resp.Code == 0
}

type InviteMeetingReqBodyBuilder struct {
	invitees     []*MeetingUser // 被邀请的用户列表
	inviteesFlag bool
}

func NewInviteMeetingReqBodyBuilder() *InviteMeetingReqBodyBuilder {
	builder := &InviteMeetingReqBodyBuilder{}
	return builder
}

// 被邀请的用户列表
//
//示例值：
func (builder *InviteMeetingReqBodyBuilder) Invitees(invitees []*MeetingUser) *InviteMeetingReqBodyBuilder {
	builder.invitees = invitees
	builder.inviteesFlag = true
	return builder
}

func (builder *InviteMeetingReqBodyBuilder) Build() *InviteMeetingReqBody {
	req := &InviteMeetingReqBody{}
	if builder.inviteesFlag {
		req.Invitees = builder.invitees
	}
	return req
}

type InviteMeetingPathReqBodyBuilder struct {
	invitees     []*MeetingUser // 被邀请的用户列表
	inviteesFlag bool
}

func NewInviteMeetingPathReqBodyBuilder() *InviteMeetingPathReqBodyBuilder {
	builder := &InviteMeetingPathReqBodyBuilder{}
	return builder
}

// 被邀请的用户列表
//
// 示例值：
func (builder *InviteMeetingPathReqBodyBuilder) Invitees(invitees []*MeetingUser) *InviteMeetingPathReqBodyBuilder {
	builder.invitees = invitees
	builder.inviteesFlag = true
	return builder
}

func (builder *InviteMeetingPathReqBodyBuilder) Build() (*InviteMeetingReqBody, error) {
	req := &InviteMeetingReqBody{}
	if builder.inviteesFlag {
		req.Invitees = builder.invitees
	}
	return req, nil
}

type InviteMeetingReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *InviteMeetingReqBody
}

func NewInviteMeetingReqBuilder() *InviteMeetingReqBuilder {
	builder := &InviteMeetingReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 会议ID（视频会议的唯一标识，视频会议开始后才会产生）
//
// 示例值：6911188411932033028
func (builder *InviteMeetingReqBuilder) MeetingId(meetingId string) *InviteMeetingReqBuilder {
	builder.apiReq.PathParams.Set("meeting_id", fmt.Sprint(meetingId))
	return builder
}

// 此次调用中使用的用户ID的类型，默认使用open_id可不填
//
// 示例值：
func (builder *InviteMeetingReqBuilder) UserIdType(userIdType string) *InviteMeetingReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

// 邀请参会人进入会议
func (builder *InviteMeetingReqBuilder) Body(body *InviteMeetingReqBody) *InviteMeetingReqBuilder {
	builder.body = body
	return builder
}

func (builder *InviteMeetingReqBuilder) Build() *InviteMeetingReq {
	req := &InviteMeetingReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.body
	return req
}

type InviteMeetingReqBody struct {
	Invitees []*MeetingUser `json:"invitees,omitempty"` // 被邀请的用户列表
}

type InviteMeetingReq struct {
	apiReq *larkcore.ApiReq
	Body   *InviteMeetingReqBody `body:""`
}

type InviteMeetingRespData struct {
	InviteResults []*MeetingInviteStatus `json:"invite_results,omitempty"` // 邀请结果
}

type InviteMeetingResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *InviteMeetingRespData `json:"data"` // 业务数据
}

func (resp *InviteMeetingResp) Success() bool {
	return resp.Code == 0
}

type KickoutMeetingReqBodyBuilder struct {
	kickoutUsers     []*MeetingUser // 需移除的用户列表
	kickoutUsersFlag bool
}

func NewKickoutMeetingReqBodyBuilder() *KickoutMeetingReqBodyBuilder {
	builder := &KickoutMeetingReqBodyBuilder{}
	return builder
}

// 需移除的用户列表
//
//示例值：
func (builder *KickoutMeetingReqBodyBuilder) KickoutUsers(kickoutUsers []*MeetingUser) *KickoutMeetingReqBodyBuilder {
	builder.kickoutUsers = kickoutUsers
	builder.kickoutUsersFlag = true
	return builder
}

func (builder *KickoutMeetingReqBodyBuilder) Build() *KickoutMeetingReqBody {
	req := &KickoutMeetingReqBody{}
	if builder.kickoutUsersFlag {
		req.KickoutUsers = builder.kickoutUsers
	}
	return req
}

type KickoutMeetingPathReqBodyBuilder struct {
	kickoutUsers     []*MeetingUser // 需移除的用户列表
	kickoutUsersFlag bool
}

func NewKickoutMeetingPathReqBodyBuilder() *KickoutMeetingPathReqBodyBuilder {
	builder := &KickoutMeetingPathReqBodyBuilder{}
	return builder
}

// 需移除的用户列表
//
// 示例值：
func (builder *KickoutMeetingPathReqBodyBuilder) KickoutUsers(kickoutUsers []*MeetingUser) *KickoutMeetingPathReqBodyBuilder {
	builder.kickoutUsers = kickoutUsers
	builder.kickoutUsersFlag = true
	return builder
}

func (builder *KickoutMeetingPathReqBodyBuilder) Build() (*KickoutMeetingReqBody, error) {
	req := &KickoutMeetingReqBody{}
	if builder.kickoutUsersFlag {
		req.KickoutUsers = builder.kickoutUsers
	}
	return req, nil
}

type KickoutMeetingReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *KickoutMeetingReqBody
}

func NewKickoutMeetingReqBuilder() *KickoutMeetingReqBuilder {
	builder := &KickoutMeetingReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 会议ID
//
// 示例值：6911188411932033028
func (builder *KickoutMeetingReqBuilder) MeetingId(meetingId string) *KickoutMeetingReqBuilder {
	builder.apiReq.PathParams.Set("meeting_id", fmt.Sprint(meetingId))
	return builder
}

// 此次调用中使用的用户ID的类型，默认使用open_id可不填
//
// 示例值：
func (builder *KickoutMeetingReqBuilder) UserIdType(userIdType string) *KickoutMeetingReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

// 将参会人从会议中移除
func (builder *KickoutMeetingReqBuilder) Body(body *KickoutMeetingReqBody) *KickoutMeetingReqBuilder {
	builder.body = body
	return builder
}

func (builder *KickoutMeetingReqBuilder) Build() *KickoutMeetingReq {
	req := &KickoutMeetingReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.body
	return req
}

type KickoutMeetingReqBody struct {
	KickoutUsers []*MeetingUser `json:"kickout_users,omitempty"` // 需移除的用户列表
}

type KickoutMeetingReq struct {
	apiReq *larkcore.ApiReq
	Body   *KickoutMeetingReqBody `body:""`
}

type KickoutMeetingRespData struct {
	KickoutResults []*MeetingParticipantResult `json:"kickout_results,omitempty"` // 移除结果
}

type KickoutMeetingResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *KickoutMeetingRespData `json:"data"` // 业务数据
}

func (resp *KickoutMeetingResp) Success() bool {
	return resp.Code == 0
}

type ListByNoMeetingReqBuilder struct {
	apiReq *larkcore.ApiReq
	limit  int // 最大返回多少记录，当使用迭代器访问时才有效
}

func NewListByNoMeetingReqBuilder() *ListByNoMeetingReqBuilder {
	builder := &ListByNoMeetingReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 最大返回多少记录，当使用迭代器访问时才有效
func (builder *ListByNoMeetingReqBuilder) Limit(limit int) *ListByNoMeetingReqBuilder {
	builder.limit = limit
	return builder
}

// 9位会议号
//
// 示例值：123456789
func (builder *ListByNoMeetingReqBuilder) MeetingNo(meetingNo string) *ListByNoMeetingReqBuilder {
	builder.apiReq.QueryParams.Set("meeting_no", fmt.Sprint(meetingNo))
	return builder
}

// 查询开始时间（unix时间，单位sec）
//
// 示例值：1608888867
func (builder *ListByNoMeetingReqBuilder) StartTime(startTime string) *ListByNoMeetingReqBuilder {
	builder.apiReq.QueryParams.Set("start_time", fmt.Sprint(startTime))
	return builder
}

// 查询结束时间（unix时间，单位sec）
//
// 示例值：1608888867
func (builder *ListByNoMeetingReqBuilder) EndTime(endTime string) *ListByNoMeetingReqBuilder {
	builder.apiReq.QueryParams.Set("end_time", fmt.Sprint(endTime))
	return builder
}

//
//
// 示例值：5
func (builder *ListByNoMeetingReqBuilder) PageToken(pageToken string) *ListByNoMeetingReqBuilder {
	builder.apiReq.QueryParams.Set("page_token", fmt.Sprint(pageToken))
	return builder
}

//
//
// 示例值：10
func (builder *ListByNoMeetingReqBuilder) PageSize(pageSize int) *ListByNoMeetingReqBuilder {
	builder.apiReq.QueryParams.Set("page_size", fmt.Sprint(pageSize))
	return builder
}

func (builder *ListByNoMeetingReqBuilder) Build() *ListByNoMeetingReq {
	req := &ListByNoMeetingReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.Limit = builder.limit
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type ListByNoMeetingReq struct {
	apiReq *larkcore.ApiReq
	Limit  int // 最多返回多少记录，只有在使用迭代器访问时，才有效

}

type ListByNoMeetingRespData struct {
	HasMore       *bool      `json:"has_more,omitempty"`       // 是否还有数据
	PageToken     *string    `json:"page_token,omitempty"`     // 下一页分页的token，下次请求时传入
	MeetingBriefs []*Meeting `json:"meeting_briefs,omitempty"` // 会议简要信息列表
}

type ListByNoMeetingResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *ListByNoMeetingRespData `json:"data"` // 业务数据
}

func (resp *ListByNoMeetingResp) Success() bool {
	return resp.Code == 0
}

type SetHostMeetingReqBodyBuilder struct {
	hostUser        *MeetingUser // 将要设置的主持人
	hostUserFlag    bool
	oldHostUser     *MeetingUser // 当前主持人（CAS并发安全：如果和会中当前主持人不符则会设置失败，可使用返回的最新数据重新设置）
	oldHostUserFlag bool
}

func NewSetHostMeetingReqBodyBuilder() *SetHostMeetingReqBodyBuilder {
	builder := &SetHostMeetingReqBodyBuilder{}
	return builder
}

// 将要设置的主持人
//
//示例值：
func (builder *SetHostMeetingReqBodyBuilder) HostUser(hostUser *MeetingUser) *SetHostMeetingReqBodyBuilder {
	builder.hostUser = hostUser
	builder.hostUserFlag = true
	return builder
}

// 当前主持人（CAS并发安全：如果和会中当前主持人不符则会设置失败，可使用返回的最新数据重新设置）
//
//示例值：
func (builder *SetHostMeetingReqBodyBuilder) OldHostUser(oldHostUser *MeetingUser) *SetHostMeetingReqBodyBuilder {
	builder.oldHostUser = oldHostUser
	builder.oldHostUserFlag = true
	return builder
}

func (builder *SetHostMeetingReqBodyBuilder) Build() *SetHostMeetingReqBody {
	req := &SetHostMeetingReqBody{}
	if builder.hostUserFlag {
		req.HostUser = builder.hostUser
	}
	if builder.oldHostUserFlag {
		req.OldHostUser = builder.oldHostUser
	}
	return req
}

type SetHostMeetingPathReqBodyBuilder struct {
	hostUser        *MeetingUser // 将要设置的主持人
	hostUserFlag    bool
	oldHostUser     *MeetingUser // 当前主持人（CAS并发安全：如果和会中当前主持人不符则会设置失败，可使用返回的最新数据重新设置）
	oldHostUserFlag bool
}

func NewSetHostMeetingPathReqBodyBuilder() *SetHostMeetingPathReqBodyBuilder {
	builder := &SetHostMeetingPathReqBodyBuilder{}
	return builder
}

// 将要设置的主持人
//
// 示例值：
func (builder *SetHostMeetingPathReqBodyBuilder) HostUser(hostUser *MeetingUser) *SetHostMeetingPathReqBodyBuilder {
	builder.hostUser = hostUser
	builder.hostUserFlag = true
	return builder
}

// 当前主持人（CAS并发安全：如果和会中当前主持人不符则会设置失败，可使用返回的最新数据重新设置）
//
// 示例值：
func (builder *SetHostMeetingPathReqBodyBuilder) OldHostUser(oldHostUser *MeetingUser) *SetHostMeetingPathReqBodyBuilder {
	builder.oldHostUser = oldHostUser
	builder.oldHostUserFlag = true
	return builder
}

func (builder *SetHostMeetingPathReqBodyBuilder) Build() (*SetHostMeetingReqBody, error) {
	req := &SetHostMeetingReqBody{}
	if builder.hostUserFlag {
		req.HostUser = builder.hostUser
	}
	if builder.oldHostUserFlag {
		req.OldHostUser = builder.oldHostUser
	}
	return req, nil
}

type SetHostMeetingReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *SetHostMeetingReqBody
}

func NewSetHostMeetingReqBuilder() *SetHostMeetingReqBuilder {
	builder := &SetHostMeetingReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 会议ID（视频会议的唯一标识，视频会议开始后才会产生）
//
// 示例值：6911188411932033028
func (builder *SetHostMeetingReqBuilder) MeetingId(meetingId string) *SetHostMeetingReqBuilder {
	builder.apiReq.PathParams.Set("meeting_id", fmt.Sprint(meetingId))
	return builder
}

// 此次调用中使用的用户ID的类型，默认使用open_id可不填
//
// 示例值：
func (builder *SetHostMeetingReqBuilder) UserIdType(userIdType string) *SetHostMeetingReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

// 设置会议的主持人
func (builder *SetHostMeetingReqBuilder) Body(body *SetHostMeetingReqBody) *SetHostMeetingReqBuilder {
	builder.body = body
	return builder
}

func (builder *SetHostMeetingReqBuilder) Build() *SetHostMeetingReq {
	req := &SetHostMeetingReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.body
	return req
}

type SetHostMeetingReqBody struct {
	HostUser    *MeetingUser `json:"host_user,omitempty"`     // 将要设置的主持人
	OldHostUser *MeetingUser `json:"old_host_user,omitempty"` // 当前主持人（CAS并发安全：如果和会中当前主持人不符则会设置失败，可使用返回的最新数据重新设置）
}

type SetHostMeetingReq struct {
	apiReq *larkcore.ApiReq
	Body   *SetHostMeetingReqBody `body:""`
}

type SetHostMeetingRespData struct {
	HostUser *MeetingUser `json:"host_user,omitempty"` // 会中当前主持人
}

type SetHostMeetingResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *SetHostMeetingRespData `json:"data"` // 业务数据
}

func (resp *SetHostMeetingResp) Success() bool {
	return resp.Code == 0
}

type GetMeetingRecordingReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewGetMeetingRecordingReqBuilder() *GetMeetingRecordingReqBuilder {
	builder := &GetMeetingRecordingReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 会议ID（视频会议的唯一标识，视频会议开始后才会产生）
//
// 示例值：6911188411932033028
func (builder *GetMeetingRecordingReqBuilder) MeetingId(meetingId string) *GetMeetingRecordingReqBuilder {
	builder.apiReq.PathParams.Set("meeting_id", fmt.Sprint(meetingId))
	return builder
}

func (builder *GetMeetingRecordingReqBuilder) Build() *GetMeetingRecordingReq {
	req := &GetMeetingRecordingReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	return req
}

type GetMeetingRecordingReq struct {
	apiReq *larkcore.ApiReq
}

type GetMeetingRecordingRespData struct {
	Recording *MeetingRecording `json:"recording,omitempty"` // 录制文件数据
}

type GetMeetingRecordingResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *GetMeetingRecordingRespData `json:"data"` // 业务数据
}

func (resp *GetMeetingRecordingResp) Success() bool {
	return resp.Code == 0
}

type SetPermissionMeetingRecordingReqBodyBuilder struct {
	permissionObjects     []*RecordingPermissionObject // 授权对象列表
	permissionObjectsFlag bool
}

func NewSetPermissionMeetingRecordingReqBodyBuilder() *SetPermissionMeetingRecordingReqBodyBuilder {
	builder := &SetPermissionMeetingRecordingReqBodyBuilder{}
	return builder
}

// 授权对象列表
//
//示例值：
func (builder *SetPermissionMeetingRecordingReqBodyBuilder) PermissionObjects(permissionObjects []*RecordingPermissionObject) *SetPermissionMeetingRecordingReqBodyBuilder {
	builder.permissionObjects = permissionObjects
	builder.permissionObjectsFlag = true
	return builder
}

func (builder *SetPermissionMeetingRecordingReqBodyBuilder) Build() *SetPermissionMeetingRecordingReqBody {
	req := &SetPermissionMeetingRecordingReqBody{}
	if builder.permissionObjectsFlag {
		req.PermissionObjects = builder.permissionObjects
	}
	return req
}

type SetPermissionMeetingRecordingPathReqBodyBuilder struct {
	permissionObjects     []*RecordingPermissionObject // 授权对象列表
	permissionObjectsFlag bool
}

func NewSetPermissionMeetingRecordingPathReqBodyBuilder() *SetPermissionMeetingRecordingPathReqBodyBuilder {
	builder := &SetPermissionMeetingRecordingPathReqBodyBuilder{}
	return builder
}

// 授权对象列表
//
// 示例值：
func (builder *SetPermissionMeetingRecordingPathReqBodyBuilder) PermissionObjects(permissionObjects []*RecordingPermissionObject) *SetPermissionMeetingRecordingPathReqBodyBuilder {
	builder.permissionObjects = permissionObjects
	builder.permissionObjectsFlag = true
	return builder
}

func (builder *SetPermissionMeetingRecordingPathReqBodyBuilder) Build() (*SetPermissionMeetingRecordingReqBody, error) {
	req := &SetPermissionMeetingRecordingReqBody{}
	if builder.permissionObjectsFlag {
		req.PermissionObjects = builder.permissionObjects
	}
	return req, nil
}

type SetPermissionMeetingRecordingReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *SetPermissionMeetingRecordingReqBody
}

func NewSetPermissionMeetingRecordingReqBuilder() *SetPermissionMeetingRecordingReqBuilder {
	builder := &SetPermissionMeetingRecordingReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 会议ID（视频会议的唯一标识，视频会议开始后才会产生）
//
// 示例值：6911188411932033028
func (builder *SetPermissionMeetingRecordingReqBuilder) MeetingId(meetingId string) *SetPermissionMeetingRecordingReqBuilder {
	builder.apiReq.PathParams.Set("meeting_id", fmt.Sprint(meetingId))
	return builder
}

// 此次调用中使用的用户ID的类型，默认使用open_id可不填
//
// 示例值：
func (builder *SetPermissionMeetingRecordingReqBuilder) UserIdType(userIdType string) *SetPermissionMeetingRecordingReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

// 将一个会议的录制文件授权给组织、用户或公开到公网
func (builder *SetPermissionMeetingRecordingReqBuilder) Body(body *SetPermissionMeetingRecordingReqBody) *SetPermissionMeetingRecordingReqBuilder {
	builder.body = body
	return builder
}

func (builder *SetPermissionMeetingRecordingReqBuilder) Build() *SetPermissionMeetingRecordingReq {
	req := &SetPermissionMeetingRecordingReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.body
	return req
}

type SetPermissionMeetingRecordingReqBody struct {
	PermissionObjects []*RecordingPermissionObject `json:"permission_objects,omitempty"` // 授权对象列表
}

type SetPermissionMeetingRecordingReq struct {
	apiReq *larkcore.ApiReq
	Body   *SetPermissionMeetingRecordingReqBody `body:""`
}

type SetPermissionMeetingRecordingResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *SetPermissionMeetingRecordingResp) Success() bool {
	return resp.Code == 0
}

type StartMeetingRecordingReqBodyBuilder struct {
	timezone     int // 录制文件时间显示使用的时区[-12,12]
	timezoneFlag bool
}

func NewStartMeetingRecordingReqBodyBuilder() *StartMeetingRecordingReqBodyBuilder {
	builder := &StartMeetingRecordingReqBodyBuilder{}
	return builder
}

// 录制文件时间显示使用的时区[-12,12]
//
//示例值：8
func (builder *StartMeetingRecordingReqBodyBuilder) Timezone(timezone int) *StartMeetingRecordingReqBodyBuilder {
	builder.timezone = timezone
	builder.timezoneFlag = true
	return builder
}

func (builder *StartMeetingRecordingReqBodyBuilder) Build() *StartMeetingRecordingReqBody {
	req := &StartMeetingRecordingReqBody{}
	if builder.timezoneFlag {
		req.Timezone = &builder.timezone
	}
	return req
}

type StartMeetingRecordingPathReqBodyBuilder struct {
	timezone     int // 录制文件时间显示使用的时区[-12,12]
	timezoneFlag bool
}

func NewStartMeetingRecordingPathReqBodyBuilder() *StartMeetingRecordingPathReqBodyBuilder {
	builder := &StartMeetingRecordingPathReqBodyBuilder{}
	return builder
}

// 录制文件时间显示使用的时区[-12,12]
//
// 示例值：8
func (builder *StartMeetingRecordingPathReqBodyBuilder) Timezone(timezone int) *StartMeetingRecordingPathReqBodyBuilder {
	builder.timezone = timezone
	builder.timezoneFlag = true
	return builder
}

func (builder *StartMeetingRecordingPathReqBodyBuilder) Build() (*StartMeetingRecordingReqBody, error) {
	req := &StartMeetingRecordingReqBody{}
	if builder.timezoneFlag {
		req.Timezone = &builder.timezone
	}
	return req, nil
}

type StartMeetingRecordingReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *StartMeetingRecordingReqBody
}

func NewStartMeetingRecordingReqBuilder() *StartMeetingRecordingReqBuilder {
	builder := &StartMeetingRecordingReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 会议ID（视频会议的唯一标识，视频会议开始后才会产生）
//
// 示例值：6911188411932033028
func (builder *StartMeetingRecordingReqBuilder) MeetingId(meetingId string) *StartMeetingRecordingReqBuilder {
	builder.apiReq.PathParams.Set("meeting_id", fmt.Sprint(meetingId))
	return builder
}

// 在会议中开始录制。
func (builder *StartMeetingRecordingReqBuilder) Body(body *StartMeetingRecordingReqBody) *StartMeetingRecordingReqBuilder {
	builder.body = body
	return builder
}

func (builder *StartMeetingRecordingReqBuilder) Build() *StartMeetingRecordingReq {
	req := &StartMeetingRecordingReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.Body = builder.body
	return req
}

type StartMeetingRecordingReqBody struct {
	Timezone *int `json:"timezone,omitempty"` // 录制文件时间显示使用的时区[-12,12]
}

type StartMeetingRecordingReq struct {
	apiReq *larkcore.ApiReq
	Body   *StartMeetingRecordingReqBody `body:""`
}

type StartMeetingRecordingResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *StartMeetingRecordingResp) Success() bool {
	return resp.Code == 0
}

type StopMeetingRecordingReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewStopMeetingRecordingReqBuilder() *StopMeetingRecordingReqBuilder {
	builder := &StopMeetingRecordingReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 会议ID（视频会议的唯一标识，视频会议开始后才会产生）
//
// 示例值：6911188411932033028
func (builder *StopMeetingRecordingReqBuilder) MeetingId(meetingId string) *StopMeetingRecordingReqBuilder {
	builder.apiReq.PathParams.Set("meeting_id", fmt.Sprint(meetingId))
	return builder
}

func (builder *StopMeetingRecordingReqBuilder) Build() *StopMeetingRecordingReq {
	req := &StopMeetingRecordingReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	return req
}

type StopMeetingRecordingReq struct {
	apiReq *larkcore.ApiReq
}

type StopMeetingRecordingResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *StopMeetingRecordingResp) Success() bool {
	return resp.Code == 0
}

type GetDailyReportReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewGetDailyReportReqBuilder() *GetDailyReportReqBuilder {
	builder := &GetDailyReportReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 开始时间（unix时间，单位sec）
//
// 示例值：1608888867
func (builder *GetDailyReportReqBuilder) StartTime(startTime string) *GetDailyReportReqBuilder {
	builder.apiReq.QueryParams.Set("start_time", fmt.Sprint(startTime))
	return builder
}

// 结束时间（unix时间，单位sec）
//
// 示例值：1608888966
func (builder *GetDailyReportReqBuilder) EndTime(endTime string) *GetDailyReportReqBuilder {
	builder.apiReq.QueryParams.Set("end_time", fmt.Sprint(endTime))
	return builder
}

func (builder *GetDailyReportReqBuilder) Build() *GetDailyReportReq {
	req := &GetDailyReportReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type GetDailyReportReq struct {
	apiReq *larkcore.ApiReq
}

type GetDailyReportRespData struct {
	MeetingReport *Report `json:"meeting_report,omitempty"` // 会议报告
}

type GetDailyReportResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *GetDailyReportRespData `json:"data"` // 业务数据
}

func (resp *GetDailyReportResp) Success() bool {
	return resp.Code == 0
}

type GetTopUserReportReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewGetTopUserReportReqBuilder() *GetTopUserReportReqBuilder {
	builder := &GetTopUserReportReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 开始时间（unix时间，单位sec）
//
// 示例值：1608888867
func (builder *GetTopUserReportReqBuilder) StartTime(startTime string) *GetTopUserReportReqBuilder {
	builder.apiReq.QueryParams.Set("start_time", fmt.Sprint(startTime))
	return builder
}

// 结束时间（unix时间，单位sec）
//
// 示例值：1608889966
func (builder *GetTopUserReportReqBuilder) EndTime(endTime string) *GetTopUserReportReqBuilder {
	builder.apiReq.QueryParams.Set("end_time", fmt.Sprint(endTime))
	return builder
}

// 取前多少位
//
// 示例值：10
func (builder *GetTopUserReportReqBuilder) Limit(limit int) *GetTopUserReportReqBuilder {
	builder.apiReq.QueryParams.Set("limit", fmt.Sprint(limit))
	return builder
}

// 排序依据（降序）
//
// 示例值：1
func (builder *GetTopUserReportReqBuilder) OrderBy(orderBy int) *GetTopUserReportReqBuilder {
	builder.apiReq.QueryParams.Set("order_by", fmt.Sprint(orderBy))
	return builder
}

// 此次调用中使用的用户ID的类型
//
// 示例值：user_id
func (builder *GetTopUserReportReqBuilder) UserIdType(userIdType string) *GetTopUserReportReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

func (builder *GetTopUserReportReqBuilder) Build() *GetTopUserReportReq {
	req := &GetTopUserReportReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type GetTopUserReportReq struct {
	apiReq *larkcore.ApiReq
}

type GetTopUserReportRespData struct {
	TopUserReport []*ReportTopUser `json:"top_user_report,omitempty"` // top用户列表
}

type GetTopUserReportResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *GetTopUserReportRespData `json:"data"` // 业务数据
}

func (resp *GetTopUserReportResp) Success() bool {
	return resp.Code == 0
}

type ApplyReserveReqBodyBuilder struct {
	endTime             string // 预约到期时间（unix时间，单位sec），多人会议必填
	endTimeFlag         bool
	ownerId             string // 指定会议归属人，使用tenant_access_token时生效且必传，使用user_access_token时不生效，必须指定为同租户下的合法lark用户
	ownerIdFlag         bool
	meetingSettings     *ReserveMeetingSetting // 会议设置
	meetingSettingsFlag bool
}

func NewApplyReserveReqBodyBuilder() *ApplyReserveReqBodyBuilder {
	builder := &ApplyReserveReqBodyBuilder{}
	return builder
}

// 预约到期时间（unix时间，单位sec），多人会议必填
//
//示例值：1608888867
func (builder *ApplyReserveReqBodyBuilder) EndTime(endTime string) *ApplyReserveReqBodyBuilder {
	builder.endTime = endTime
	builder.endTimeFlag = true
	return builder
}

// 指定会议归属人，使用tenant_access_token时生效且必传，使用user_access_token时不生效，必须指定为同租户下的合法lark用户
//
//示例值：ou_3ec3f6a28a0d08c45d895276e8e5e19b
func (builder *ApplyReserveReqBodyBuilder) OwnerId(ownerId string) *ApplyReserveReqBodyBuilder {
	builder.ownerId = ownerId
	builder.ownerIdFlag = true
	return builder
}

// 会议设置
//
//示例值：
func (builder *ApplyReserveReqBodyBuilder) MeetingSettings(meetingSettings *ReserveMeetingSetting) *ApplyReserveReqBodyBuilder {
	builder.meetingSettings = meetingSettings
	builder.meetingSettingsFlag = true
	return builder
}

func (builder *ApplyReserveReqBodyBuilder) Build() *ApplyReserveReqBody {
	req := &ApplyReserveReqBody{}
	if builder.endTimeFlag {
		req.EndTime = &builder.endTime
	}
	if builder.ownerIdFlag {
		req.OwnerId = &builder.ownerId
	}
	if builder.meetingSettingsFlag {
		req.MeetingSettings = builder.meetingSettings
	}
	return req
}

type ApplyReservePathReqBodyBuilder struct {
	endTime             string // 预约到期时间（unix时间，单位sec），多人会议必填
	endTimeFlag         bool
	ownerId             string // 指定会议归属人，使用tenant_access_token时生效且必传，使用user_access_token时不生效，必须指定为同租户下的合法lark用户
	ownerIdFlag         bool
	meetingSettings     *ReserveMeetingSetting // 会议设置
	meetingSettingsFlag bool
}

func NewApplyReservePathReqBodyBuilder() *ApplyReservePathReqBodyBuilder {
	builder := &ApplyReservePathReqBodyBuilder{}
	return builder
}

// 预约到期时间（unix时间，单位sec），多人会议必填
//
// 示例值：1608888867
func (builder *ApplyReservePathReqBodyBuilder) EndTime(endTime string) *ApplyReservePathReqBodyBuilder {
	builder.endTime = endTime
	builder.endTimeFlag = true
	return builder
}

// 指定会议归属人，使用tenant_access_token时生效且必传，使用user_access_token时不生效，必须指定为同租户下的合法lark用户
//
// 示例值：ou_3ec3f6a28a0d08c45d895276e8e5e19b
func (builder *ApplyReservePathReqBodyBuilder) OwnerId(ownerId string) *ApplyReservePathReqBodyBuilder {
	builder.ownerId = ownerId
	builder.ownerIdFlag = true
	return builder
}

// 会议设置
//
// 示例值：
func (builder *ApplyReservePathReqBodyBuilder) MeetingSettings(meetingSettings *ReserveMeetingSetting) *ApplyReservePathReqBodyBuilder {
	builder.meetingSettings = meetingSettings
	builder.meetingSettingsFlag = true
	return builder
}

func (builder *ApplyReservePathReqBodyBuilder) Build() (*ApplyReserveReqBody, error) {
	req := &ApplyReserveReqBody{}
	if builder.endTimeFlag {
		req.EndTime = &builder.endTime
	}
	if builder.ownerIdFlag {
		req.OwnerId = &builder.ownerId
	}
	if builder.meetingSettingsFlag {
		req.MeetingSettings = builder.meetingSettings
	}
	return req, nil
}

type ApplyReserveReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *ApplyReserveReqBody
}

func NewApplyReserveReqBuilder() *ApplyReserveReqBuilder {
	builder := &ApplyReserveReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 此次调用中使用的用户ID的类型，默认使用open_id可不填
//
// 示例值：
func (builder *ApplyReserveReqBuilder) UserIdType(userIdType string) *ApplyReserveReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

// 创建一个会议预约。
func (builder *ApplyReserveReqBuilder) Body(body *ApplyReserveReqBody) *ApplyReserveReqBuilder {
	builder.body = body
	return builder
}

func (builder *ApplyReserveReqBuilder) Build() *ApplyReserveReq {
	req := &ApplyReserveReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.body
	return req
}

type ApplyReserveReqBody struct {
	EndTime         *string                `json:"end_time,omitempty"`         // 预约到期时间（unix时间，单位sec），多人会议必填
	OwnerId         *string                `json:"owner_id,omitempty"`         // 指定会议归属人，使用tenant_access_token时生效且必传，使用user_access_token时不生效，必须指定为同租户下的合法lark用户
	MeetingSettings *ReserveMeetingSetting `json:"meeting_settings,omitempty"` // 会议设置
}

type ApplyReserveReq struct {
	apiReq *larkcore.ApiReq
	Body   *ApplyReserveReqBody `body:""`
}

type ApplyReserveRespData struct {
	Reserve                    *Reserve                    `json:"reserve,omitempty"`                       // 预约数据
	ReserveCorrectionCheckInfo *ReserveCorrectionCheckInfo `json:"reserve_correction_check_info,omitempty"` // 预约参数检查信息
}

type ApplyReserveResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *ApplyReserveRespData `json:"data"` // 业务数据
}

func (resp *ApplyReserveResp) Success() bool {
	return resp.Code == 0
}

type DeleteReserveReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewDeleteReserveReqBuilder() *DeleteReserveReqBuilder {
	builder := &DeleteReserveReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 预约ID（预约的唯一标识）
//
// 示例值：6911188411932033028
func (builder *DeleteReserveReqBuilder) ReserveId(reserveId string) *DeleteReserveReqBuilder {
	builder.apiReq.PathParams.Set("reserve_id", fmt.Sprint(reserveId))
	return builder
}

func (builder *DeleteReserveReqBuilder) Build() *DeleteReserveReq {
	req := &DeleteReserveReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	return req
}

type DeleteReserveReq struct {
	apiReq *larkcore.ApiReq
}

type DeleteReserveResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *DeleteReserveResp) Success() bool {
	return resp.Code == 0
}

type GetReserveReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewGetReserveReqBuilder() *GetReserveReqBuilder {
	builder := &GetReserveReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 预约ID（预约的唯一标识）
//
// 示例值：6911188411932033028
func (builder *GetReserveReqBuilder) ReserveId(reserveId string) *GetReserveReqBuilder {
	builder.apiReq.PathParams.Set("reserve_id", fmt.Sprint(reserveId))
	return builder
}

// 此次调用中使用的用户ID的类型
//
// 示例值：user_id
func (builder *GetReserveReqBuilder) UserIdType(userIdType string) *GetReserveReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

func (builder *GetReserveReqBuilder) Build() *GetReserveReq {
	req := &GetReserveReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type GetReserveReq struct {
	apiReq *larkcore.ApiReq
}

type GetReserveRespData struct {
	Reserve *Reserve `json:"reserve,omitempty"` // 预约数据
}

type GetReserveResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *GetReserveRespData `json:"data"` // 业务数据
}

func (resp *GetReserveResp) Success() bool {
	return resp.Code == 0
}

type GetActiveMeetingReserveReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewGetActiveMeetingReserveReqBuilder() *GetActiveMeetingReserveReqBuilder {
	builder := &GetActiveMeetingReserveReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 预约ID（预约的唯一标识）
//
// 示例值：6911188411932033028
func (builder *GetActiveMeetingReserveReqBuilder) ReserveId(reserveId string) *GetActiveMeetingReserveReqBuilder {
	builder.apiReq.PathParams.Set("reserve_id", fmt.Sprint(reserveId))
	return builder
}

// 是否需要参会人列表，默认为false
//
// 示例值：false
func (builder *GetActiveMeetingReserveReqBuilder) WithParticipants(withParticipants bool) *GetActiveMeetingReserveReqBuilder {
	builder.apiReq.QueryParams.Set("with_participants", fmt.Sprint(withParticipants))
	return builder
}

// 此次调用中使用的用户ID的类型
//
// 示例值：user_id
func (builder *GetActiveMeetingReserveReqBuilder) UserIdType(userIdType string) *GetActiveMeetingReserveReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

func (builder *GetActiveMeetingReserveReqBuilder) Build() *GetActiveMeetingReserveReq {
	req := &GetActiveMeetingReserveReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type GetActiveMeetingReserveReq struct {
	apiReq *larkcore.ApiReq
}

type GetActiveMeetingReserveRespData struct {
	Meeting *Meeting `json:"meeting,omitempty"` // 会议数据
}

type GetActiveMeetingReserveResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *GetActiveMeetingReserveRespData `json:"data"` // 业务数据
}

func (resp *GetActiveMeetingReserveResp) Success() bool {
	return resp.Code == 0
}

type UpdateReserveReqBodyBuilder struct {
	endTime             string // 预约到期时间（unix时间，单位sec）
	endTimeFlag         bool
	meetingSettings     *ReserveMeetingSetting // 会议设置
	meetingSettingsFlag bool
}

func NewUpdateReserveReqBodyBuilder() *UpdateReserveReqBodyBuilder {
	builder := &UpdateReserveReqBodyBuilder{}
	return builder
}

// 预约到期时间（unix时间，单位sec）
//
//示例值：1608888867
func (builder *UpdateReserveReqBodyBuilder) EndTime(endTime string) *UpdateReserveReqBodyBuilder {
	builder.endTime = endTime
	builder.endTimeFlag = true
	return builder
}

// 会议设置
//
//示例值：
func (builder *UpdateReserveReqBodyBuilder) MeetingSettings(meetingSettings *ReserveMeetingSetting) *UpdateReserveReqBodyBuilder {
	builder.meetingSettings = meetingSettings
	builder.meetingSettingsFlag = true
	return builder
}

func (builder *UpdateReserveReqBodyBuilder) Build() *UpdateReserveReqBody {
	req := &UpdateReserveReqBody{}
	if builder.endTimeFlag {
		req.EndTime = &builder.endTime
	}
	if builder.meetingSettingsFlag {
		req.MeetingSettings = builder.meetingSettings
	}
	return req
}

type UpdateReservePathReqBodyBuilder struct {
	endTime             string // 预约到期时间（unix时间，单位sec）
	endTimeFlag         bool
	meetingSettings     *ReserveMeetingSetting // 会议设置
	meetingSettingsFlag bool
}

func NewUpdateReservePathReqBodyBuilder() *UpdateReservePathReqBodyBuilder {
	builder := &UpdateReservePathReqBodyBuilder{}
	return builder
}

// 预约到期时间（unix时间，单位sec）
//
// 示例值：1608888867
func (builder *UpdateReservePathReqBodyBuilder) EndTime(endTime string) *UpdateReservePathReqBodyBuilder {
	builder.endTime = endTime
	builder.endTimeFlag = true
	return builder
}

// 会议设置
//
// 示例值：
func (builder *UpdateReservePathReqBodyBuilder) MeetingSettings(meetingSettings *ReserveMeetingSetting) *UpdateReservePathReqBodyBuilder {
	builder.meetingSettings = meetingSettings
	builder.meetingSettingsFlag = true
	return builder
}

func (builder *UpdateReservePathReqBodyBuilder) Build() (*UpdateReserveReqBody, error) {
	req := &UpdateReserveReqBody{}
	if builder.endTimeFlag {
		req.EndTime = &builder.endTime
	}
	if builder.meetingSettingsFlag {
		req.MeetingSettings = builder.meetingSettings
	}
	return req, nil
}

type UpdateReserveReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *UpdateReserveReqBody
}

func NewUpdateReserveReqBuilder() *UpdateReserveReqBuilder {
	builder := &UpdateReserveReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 预约ID（预约的唯一标识）
//
// 示例值：6911188411932033028
func (builder *UpdateReserveReqBuilder) ReserveId(reserveId string) *UpdateReserveReqBuilder {
	builder.apiReq.PathParams.Set("reserve_id", fmt.Sprint(reserveId))
	return builder
}

// 此次调用中使用的用户ID的类型，默认使用open_id可不填
//
// 示例值：
func (builder *UpdateReserveReqBuilder) UserIdType(userIdType string) *UpdateReserveReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

// 更新一个预约
func (builder *UpdateReserveReqBuilder) Body(body *UpdateReserveReqBody) *UpdateReserveReqBuilder {
	builder.body = body
	return builder
}

func (builder *UpdateReserveReqBuilder) Build() *UpdateReserveReq {
	req := &UpdateReserveReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.body
	return req
}

type UpdateReserveReqBody struct {
	EndTime         *string                `json:"end_time,omitempty"`         // 预约到期时间（unix时间，单位sec）
	MeetingSettings *ReserveMeetingSetting `json:"meeting_settings,omitempty"` // 会议设置
}

type UpdateReserveReq struct {
	apiReq *larkcore.ApiReq
	Body   *UpdateReserveReqBody `body:""`
}

type UpdateReserveRespData struct {
	Reserve                    *Reserve                    `json:"reserve,omitempty"`                       // 预约数据
	ReserveCorrectionCheckInfo *ReserveCorrectionCheckInfo `json:"reserve_correction_check_info,omitempty"` // 预约参数检查信息
}

type UpdateReserveResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *UpdateReserveRespData `json:"data"` // 业务数据
}

func (resp *UpdateReserveResp) Success() bool {
	return resp.Code == 0
}

type PatchReserveConfigReqBodyBuilder struct {
	scopeType              string // 1 代表层级，2 代表会议室
	scopeTypeFlag          bool
	approvalConfig         *ApprovalConfig // 预定审批设置
	approvalConfigFlag     bool
	timeConfig             *TimeConfig // 预定时间设置
	timeConfigFlag         bool
	reserveScopeConfig     *ReserveScopeConfig // 预定范围设置
	reserveScopeConfigFlag bool
}

func NewPatchReserveConfigReqBodyBuilder() *PatchReserveConfigReqBodyBuilder {
	builder := &PatchReserveConfigReqBodyBuilder{}
	return builder
}

// 1 代表层级，2 代表会议室
//
//示例值：2
func (builder *PatchReserveConfigReqBodyBuilder) ScopeType(scopeType string) *PatchReserveConfigReqBodyBuilder {
	builder.scopeType = scopeType
	builder.scopeTypeFlag = true
	return builder
}

// 预定审批设置
//
//示例值：
func (builder *PatchReserveConfigReqBodyBuilder) ApprovalConfig(approvalConfig *ApprovalConfig) *PatchReserveConfigReqBodyBuilder {
	builder.approvalConfig = approvalConfig
	builder.approvalConfigFlag = true
	return builder
}

// 预定时间设置
//
//示例值：
func (builder *PatchReserveConfigReqBodyBuilder) TimeConfig(timeConfig *TimeConfig) *PatchReserveConfigReqBodyBuilder {
	builder.timeConfig = timeConfig
	builder.timeConfigFlag = true
	return builder
}

// 预定范围设置
//
//示例值：
func (builder *PatchReserveConfigReqBodyBuilder) ReserveScopeConfig(reserveScopeConfig *ReserveScopeConfig) *PatchReserveConfigReqBodyBuilder {
	builder.reserveScopeConfig = reserveScopeConfig
	builder.reserveScopeConfigFlag = true
	return builder
}

func (builder *PatchReserveConfigReqBodyBuilder) Build() *PatchReserveConfigReqBody {
	req := &PatchReserveConfigReqBody{}
	if builder.scopeTypeFlag {
		req.ScopeType = &builder.scopeType
	}
	if builder.approvalConfigFlag {
		req.ApprovalConfig = builder.approvalConfig
	}
	if builder.timeConfigFlag {
		req.TimeConfig = builder.timeConfig
	}
	if builder.reserveScopeConfigFlag {
		req.ReserveScopeConfig = builder.reserveScopeConfig
	}
	return req
}

type PatchReserveConfigPathReqBodyBuilder struct {
	scopeType              string // 1 代表层级，2 代表会议室
	scopeTypeFlag          bool
	approvalConfig         *ApprovalConfig // 预定审批设置
	approvalConfigFlag     bool
	timeConfig             *TimeConfig // 预定时间设置
	timeConfigFlag         bool
	reserveScopeConfig     *ReserveScopeConfig // 预定范围设置
	reserveScopeConfigFlag bool
}

func NewPatchReserveConfigPathReqBodyBuilder() *PatchReserveConfigPathReqBodyBuilder {
	builder := &PatchReserveConfigPathReqBodyBuilder{}
	return builder
}

// 1 代表层级，2 代表会议室
//
// 示例值：2
func (builder *PatchReserveConfigPathReqBodyBuilder) ScopeType(scopeType string) *PatchReserveConfigPathReqBodyBuilder {
	builder.scopeType = scopeType
	builder.scopeTypeFlag = true
	return builder
}

// 预定审批设置
//
// 示例值：
func (builder *PatchReserveConfigPathReqBodyBuilder) ApprovalConfig(approvalConfig *ApprovalConfig) *PatchReserveConfigPathReqBodyBuilder {
	builder.approvalConfig = approvalConfig
	builder.approvalConfigFlag = true
	return builder
}

// 预定时间设置
//
// 示例值：
func (builder *PatchReserveConfigPathReqBodyBuilder) TimeConfig(timeConfig *TimeConfig) *PatchReserveConfigPathReqBodyBuilder {
	builder.timeConfig = timeConfig
	builder.timeConfigFlag = true
	return builder
}

// 预定范围设置
//
// 示例值：
func (builder *PatchReserveConfigPathReqBodyBuilder) ReserveScopeConfig(reserveScopeConfig *ReserveScopeConfig) *PatchReserveConfigPathReqBodyBuilder {
	builder.reserveScopeConfig = reserveScopeConfig
	builder.reserveScopeConfigFlag = true
	return builder
}

func (builder *PatchReserveConfigPathReqBodyBuilder) Build() (*PatchReserveConfigReqBody, error) {
	req := &PatchReserveConfigReqBody{}
	if builder.scopeTypeFlag {
		req.ScopeType = &builder.scopeType
	}
	if builder.approvalConfigFlag {
		req.ApprovalConfig = builder.approvalConfig
	}
	if builder.timeConfigFlag {
		req.TimeConfig = builder.timeConfig
	}
	if builder.reserveScopeConfigFlag {
		req.ReserveScopeConfig = builder.reserveScopeConfig
	}
	return req, nil
}

type PatchReserveConfigReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *PatchReserveConfigReqBody
}

func NewPatchReserveConfigReqBuilder() *PatchReserveConfigReqBuilder {
	builder := &PatchReserveConfigReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 会议室或层级id
//
// 示例值：omm_3c5dd7e09bac0c1758fcf9511bd1a771
func (builder *PatchReserveConfigReqBuilder) ReserveConfigId(reserveConfigId string) *PatchReserveConfigReqBuilder {
	builder.apiReq.PathParams.Set("reserve_config_id", fmt.Sprint(reserveConfigId))
	return builder
}

// 此次调用中使用的用户ID的类型
//
// 示例值：user_id
func (builder *PatchReserveConfigReqBuilder) UserIdType(userIdType string) *PatchReserveConfigReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

// 更新会议室预定范围
func (builder *PatchReserveConfigReqBuilder) Body(body *PatchReserveConfigReqBody) *PatchReserveConfigReqBuilder {
	builder.body = body
	return builder
}

func (builder *PatchReserveConfigReqBuilder) Build() *PatchReserveConfigReq {
	req := &PatchReserveConfigReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.body
	return req
}

type PatchReserveConfigReqBody struct {
	ScopeType          *string             `json:"scope_type,omitempty"`           // 1 代表层级，2 代表会议室
	ApprovalConfig     *ApprovalConfig     `json:"approval_config,omitempty"`      // 预定审批设置
	TimeConfig         *TimeConfig         `json:"time_config,omitempty"`          // 预定时间设置
	ReserveScopeConfig *ReserveScopeConfig `json:"reserve_scope_config,omitempty"` // 预定范围设置
}

type PatchReserveConfigReq struct {
	apiReq *larkcore.ApiReq
	Body   *PatchReserveConfigReqBody `body:""`
}

type PatchReserveConfigResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *PatchReserveConfigResp) Success() bool {
	return resp.Code == 0
}

type ReserveScopeReserveConfigReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewReserveScopeReserveConfigReqBuilder() *ReserveScopeReserveConfigReqBuilder {
	builder := &ReserveScopeReserveConfigReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 会议室或层级id
//
// 示例值：omm_3c5dd7e09bac0c1758fcf9511bd1a771
func (builder *ReserveScopeReserveConfigReqBuilder) ScopeId(scopeId string) *ReserveScopeReserveConfigReqBuilder {
	builder.apiReq.QueryParams.Set("scope_id", fmt.Sprint(scopeId))
	return builder
}

// 1 代表层级，2 代表会议室
//
// 示例值：2
func (builder *ReserveScopeReserveConfigReqBuilder) ScopeType(scopeType string) *ReserveScopeReserveConfigReqBuilder {
	builder.apiReq.QueryParams.Set("scope_type", fmt.Sprint(scopeType))
	return builder
}

// 此次调用中使用的用户ID的类型
//
// 示例值：user_id
func (builder *ReserveScopeReserveConfigReqBuilder) UserIdType(userIdType string) *ReserveScopeReserveConfigReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

func (builder *ReserveScopeReserveConfigReqBuilder) Build() *ReserveScopeReserveConfigReq {
	req := &ReserveScopeReserveConfigReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type ReserveScopeReserveConfigReq struct {
	apiReq *larkcore.ApiReq
}

type ReserveScopeReserveConfigRespData struct {
	ApproveConfig      *ApprovalConfig     `json:"approve_config,omitempty"`       // 预定审批设置
	TimeConfig         *TimeConfig         `json:"time_config,omitempty"`          // 预定时间设置
	ReserveScopeConfig *ReserveScopeConfig `json:"reserve_scope_config,omitempty"` // 预定范围设置
}

type ReserveScopeReserveConfigResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *ReserveScopeReserveConfigRespData `json:"data"` // 业务数据
}

func (resp *ReserveScopeReserveConfigResp) Success() bool {
	return resp.Code == 0
}

type CreateRoomReqBuilder struct {
	apiReq *larkcore.ApiReq
	room   *Room
}

func NewCreateRoomReqBuilder() *CreateRoomReqBuilder {
	builder := &CreateRoomReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 此次调用中使用的用户ID的类型，默认使用open_id可不填
//
// 示例值：open_id
func (builder *CreateRoomReqBuilder) UserIdType(userIdType string) *CreateRoomReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

// 该接口用于创建会议室
func (builder *CreateRoomReqBuilder) Room(room *Room) *CreateRoomReqBuilder {
	builder.room = room
	return builder
}

func (builder *CreateRoomReqBuilder) Build() *CreateRoomReq {
	req := &CreateRoomReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.room
	return req
}

type CreateRoomReq struct {
	apiReq *larkcore.ApiReq
	Room   *Room `body:""`
}

type CreateRoomRespData struct {
	Room *Room `json:"room,omitempty"` // 会议室详情
}

type CreateRoomResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *CreateRoomRespData `json:"data"` // 业务数据
}

func (resp *CreateRoomResp) Success() bool {
	return resp.Code == 0
}

type DeleteRoomReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewDeleteRoomReqBuilder() *DeleteRoomReqBuilder {
	builder := &DeleteRoomReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 会议室ID
//
// 示例值：omm_4de32cf10a4358788ff4e09e37ebbf9b
func (builder *DeleteRoomReqBuilder) RoomId(roomId string) *DeleteRoomReqBuilder {
	builder.apiReq.PathParams.Set("room_id", fmt.Sprint(roomId))
	return builder
}

func (builder *DeleteRoomReqBuilder) Build() *DeleteRoomReq {
	req := &DeleteRoomReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	return req
}

type DeleteRoomReq struct {
	apiReq *larkcore.ApiReq
}

type DeleteRoomResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *DeleteRoomResp) Success() bool {
	return resp.Code == 0
}

type GetRoomReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewGetRoomReqBuilder() *GetRoomReqBuilder {
	builder := &GetRoomReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 会议室ID
//
// 示例值：omm_4de32cf10a4358788ff4e09e37ebbf9c
func (builder *GetRoomReqBuilder) RoomId(roomId string) *GetRoomReqBuilder {
	builder.apiReq.PathParams.Set("room_id", fmt.Sprint(roomId))
	return builder
}

// 此次调用中使用的用户ID的类型，默认使用open_id可不填
//
// 示例值：
func (builder *GetRoomReqBuilder) UserIdType(userIdType string) *GetRoomReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

func (builder *GetRoomReqBuilder) Build() *GetRoomReq {
	req := &GetRoomReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type GetRoomReq struct {
	apiReq *larkcore.ApiReq
}

type GetRoomRespData struct {
	Room *Room `json:"room,omitempty"` // 会议室详情
}

type GetRoomResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *GetRoomRespData `json:"data"` // 业务数据
}

func (resp *GetRoomResp) Success() bool {
	return resp.Code == 0
}

type ListRoomReqBuilder struct {
	apiReq *larkcore.ApiReq
	limit  int // 最大返回多少记录，当使用迭代器访问时才有效
}

func NewListRoomReqBuilder() *ListRoomReqBuilder {
	builder := &ListRoomReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 最大返回多少记录，当使用迭代器访问时才有效
func (builder *ListRoomReqBuilder) Limit(limit int) *ListRoomReqBuilder {
	builder.limit = limit
	return builder
}

// 分页大小
//
// 示例值：10
func (builder *ListRoomReqBuilder) PageSize(pageSize int) *ListRoomReqBuilder {
	builder.apiReq.QueryParams.Set("page_size", fmt.Sprint(pageSize))
	return builder
}

// 分页标记，第一次请求不填，表示从头开始遍历；分页查询结果还有更多项时会同时返回新的 page_token，下次遍历可采用该 page_token 获取查询结果
//
// 示例值：10
func (builder *ListRoomReqBuilder) PageToken(pageToken string) *ListRoomReqBuilder {
	builder.apiReq.QueryParams.Set("page_token", fmt.Sprint(pageToken))
	return builder
}

// 层级ID，当需要获取租户下会议室列表时，room_level_id可传空
//
// 示例值：omb_4ad1a2c7a2fbc5fc9570f38456931293
func (builder *ListRoomReqBuilder) RoomLevelId(roomLevelId string) *ListRoomReqBuilder {
	builder.apiReq.QueryParams.Set("room_level_id", fmt.Sprint(roomLevelId))
	return builder
}

// 此次调用中使用的用户ID的类型，默认使用open_id可不填
//
// 示例值：
func (builder *ListRoomReqBuilder) UserIdType(userIdType string) *ListRoomReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

func (builder *ListRoomReqBuilder) Build() *ListRoomReq {
	req := &ListRoomReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.Limit = builder.limit
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type ListRoomReq struct {
	apiReq *larkcore.ApiReq
	Limit  int // 最多返回多少记录，只有在使用迭代器访问时，才有效

}

type ListRoomRespData struct {
	Rooms     []*Room `json:"rooms,omitempty"`      // 会议室列表
	PageToken *string `json:"page_token,omitempty"` // 下一页分页的token，下次请求时传入
	HasMore   *bool   `json:"has_more,omitempty"`   // 是否还有数据
}

type ListRoomResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *ListRoomRespData `json:"data"` // 业务数据
}

func (resp *ListRoomResp) Success() bool {
	return resp.Code == 0
}

type MgetRoomReqBodyBuilder struct {
	roomIds     []string // 会议室id列表
	roomIdsFlag bool
}

func NewMgetRoomReqBodyBuilder() *MgetRoomReqBodyBuilder {
	builder := &MgetRoomReqBodyBuilder{}
	return builder
}

// 会议室id列表
//
//示例值：["omm_4de32cf10a4358788ff4e09e37ebbf9b","omm_3c5dd7e09bac0c1758fcf9511bd1a771"]
func (builder *MgetRoomReqBodyBuilder) RoomIds(roomIds []string) *MgetRoomReqBodyBuilder {
	builder.roomIds = roomIds
	builder.roomIdsFlag = true
	return builder
}

func (builder *MgetRoomReqBodyBuilder) Build() *MgetRoomReqBody {
	req := &MgetRoomReqBody{}
	if builder.roomIdsFlag {
		req.RoomIds = builder.roomIds
	}
	return req
}

type MgetRoomPathReqBodyBuilder struct {
	roomIds     []string // 会议室id列表
	roomIdsFlag bool
}

func NewMgetRoomPathReqBodyBuilder() *MgetRoomPathReqBodyBuilder {
	builder := &MgetRoomPathReqBodyBuilder{}
	return builder
}

// 会议室id列表
//
// 示例值：["omm_4de32cf10a4358788ff4e09e37ebbf9b","omm_3c5dd7e09bac0c1758fcf9511bd1a771"]
func (builder *MgetRoomPathReqBodyBuilder) RoomIds(roomIds []string) *MgetRoomPathReqBodyBuilder {
	builder.roomIds = roomIds
	builder.roomIdsFlag = true
	return builder
}

func (builder *MgetRoomPathReqBodyBuilder) Build() (*MgetRoomReqBody, error) {
	req := &MgetRoomReqBody{}
	if builder.roomIdsFlag {
		req.RoomIds = builder.roomIds
	}
	return req, nil
}

type MgetRoomReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *MgetRoomReqBody
}

func NewMgetRoomReqBuilder() *MgetRoomReqBuilder {
	builder := &MgetRoomReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 此次调用中使用的用户ID的类型，默认使用open_id可不填
//
// 示例值：
func (builder *MgetRoomReqBuilder) UserIdType(userIdType string) *MgetRoomReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

// 该接口可以使用会议室ID批量查询会议室详情
func (builder *MgetRoomReqBuilder) Body(body *MgetRoomReqBody) *MgetRoomReqBuilder {
	builder.body = body
	return builder
}

func (builder *MgetRoomReqBuilder) Build() *MgetRoomReq {
	req := &MgetRoomReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.body
	return req
}

type MgetRoomReqBody struct {
	RoomIds []string `json:"room_ids,omitempty"` // 会议室id列表
}

type MgetRoomReq struct {
	apiReq *larkcore.ApiReq
	Body   *MgetRoomReqBody `body:""`
}

type MgetRoomRespData struct {
	Items []*Room `json:"items,omitempty"` // 会议室列表
}

type MgetRoomResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *MgetRoomRespData `json:"data"` // 业务数据
}

func (resp *MgetRoomResp) Success() bool {
	return resp.Code == 0
}

type PatchRoomReqBuilder struct {
	apiReq *larkcore.ApiReq
	room   *Room
}

func NewPatchRoomReqBuilder() *PatchRoomReqBuilder {
	builder := &PatchRoomReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 会议室ID
//
// 示例值：omm_4de32cf10a4358788ff4e09e37ebbf9b
func (builder *PatchRoomReqBuilder) RoomId(roomId string) *PatchRoomReqBuilder {
	builder.apiReq.PathParams.Set("room_id", fmt.Sprint(roomId))
	return builder
}

// 此次调用中使用的用户ID的类型，默认使用open_id可不填
//
// 示例值：open_id
func (builder *PatchRoomReqBuilder) UserIdType(userIdType string) *PatchRoomReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

// 该接口可以用来更新某个会议室的信息
func (builder *PatchRoomReqBuilder) Room(room *Room) *PatchRoomReqBuilder {
	builder.room = room
	return builder
}

func (builder *PatchRoomReqBuilder) Build() *PatchRoomReq {
	req := &PatchRoomReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.room
	return req
}

type PatchRoomReq struct {
	apiReq *larkcore.ApiReq
	Room   *Room `body:""`
}

type PatchRoomResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *PatchRoomResp) Success() bool {
	return resp.Code == 0
}

type SearchRoomReqBodyBuilder struct {
	customRoomIds       []string // 用于查询指定会议室的租户自定义会议室ID列表，优先使用该字段进行查询
	customRoomIdsFlag   bool
	keyword             string // 会议室搜索关键词（当custom_room_ids为空时，使用该字段进行查询）
	keywordFlag         bool
	roomLevelId         string // 在该会议室层级下进行搜索（当custom_room_ids为空时，使用该字段进行查询）
	roomLevelIdFlag     bool
	searchLevelName     bool // 搜索会议室是否包括层级名称（当custom_room_ids为空时，使用该字段进行查询）
	searchLevelNameFlag bool
	pageSize            int // 分页大小，该值默认为10，最大为100（当custom_room_ids为空时，使用该字段进行查询）
	pageSizeFlag        bool
	pageToken           string // 分页标记，第一次请求不填，表示从头开始遍历；分页查询结果还有更多项时会同时返回新的 page_token，下次遍历可采用该 page_token 获取查询结果（当custom_room_ids为空时，使用该字段进行查询）
	pageTokenFlag       bool
}

func NewSearchRoomReqBodyBuilder() *SearchRoomReqBodyBuilder {
	builder := &SearchRoomReqBodyBuilder{}
	return builder
}

// 用于查询指定会议室的租户自定义会议室ID列表，优先使用该字段进行查询
//
//示例值：["10001"]
func (builder *SearchRoomReqBodyBuilder) CustomRoomIds(customRoomIds []string) *SearchRoomReqBodyBuilder {
	builder.customRoomIds = customRoomIds
	builder.customRoomIdsFlag = true
	return builder
}

// 会议室搜索关键词（当custom_room_ids为空时，使用该字段进行查询）
//
//示例值：测试会议室
func (builder *SearchRoomReqBodyBuilder) Keyword(keyword string) *SearchRoomReqBodyBuilder {
	builder.keyword = keyword
	builder.keywordFlag = true
	return builder
}

// 在该会议室层级下进行搜索（当custom_room_ids为空时，使用该字段进行查询）
//
//示例值：omb_4ad1a2c7a2fbc5fc9570f38456931293
func (builder *SearchRoomReqBodyBuilder) RoomLevelId(roomLevelId string) *SearchRoomReqBodyBuilder {
	builder.roomLevelId = roomLevelId
	builder.roomLevelIdFlag = true
	return builder
}

// 搜索会议室是否包括层级名称（当custom_room_ids为空时，使用该字段进行查询）
//
//示例值：true
func (builder *SearchRoomReqBodyBuilder) SearchLevelName(searchLevelName bool) *SearchRoomReqBodyBuilder {
	builder.searchLevelName = searchLevelName
	builder.searchLevelNameFlag = true
	return builder
}

// 分页大小，该值默认为10，最大为100（当custom_room_ids为空时，使用该字段进行查询）
//
//示例值：10
func (builder *SearchRoomReqBodyBuilder) PageSize(pageSize int) *SearchRoomReqBodyBuilder {
	builder.pageSize = pageSize
	builder.pageSizeFlag = true
	return builder
}

// 分页标记，第一次请求不填，表示从头开始遍历；分页查询结果还有更多项时会同时返回新的 page_token，下次遍历可采用该 page_token 获取查询结果（当custom_room_ids为空时，使用该字段进行查询）
//
//示例值：0
func (builder *SearchRoomReqBodyBuilder) PageToken(pageToken string) *SearchRoomReqBodyBuilder {
	builder.pageToken = pageToken
	builder.pageTokenFlag = true
	return builder
}

func (builder *SearchRoomReqBodyBuilder) Build() *SearchRoomReqBody {
	req := &SearchRoomReqBody{}
	if builder.customRoomIdsFlag {
		req.CustomRoomIds = builder.customRoomIds
	}
	if builder.keywordFlag {
		req.Keyword = &builder.keyword
	}
	if builder.roomLevelIdFlag {
		req.RoomLevelId = &builder.roomLevelId
	}
	if builder.searchLevelNameFlag {
		req.SearchLevelName = &builder.searchLevelName
	}
	if builder.pageSizeFlag {
		req.PageSize = &builder.pageSize
	}
	if builder.pageTokenFlag {
		req.PageToken = &builder.pageToken
	}
	return req
}

type SearchRoomPathReqBodyBuilder struct {
	customRoomIds       []string // 用于查询指定会议室的租户自定义会议室ID列表，优先使用该字段进行查询
	customRoomIdsFlag   bool
	keyword             string // 会议室搜索关键词（当custom_room_ids为空时，使用该字段进行查询）
	keywordFlag         bool
	roomLevelId         string // 在该会议室层级下进行搜索（当custom_room_ids为空时，使用该字段进行查询）
	roomLevelIdFlag     bool
	searchLevelName     bool // 搜索会议室是否包括层级名称（当custom_room_ids为空时，使用该字段进行查询）
	searchLevelNameFlag bool
	pageSize            int // 分页大小，该值默认为10，最大为100（当custom_room_ids为空时，使用该字段进行查询）
	pageSizeFlag        bool
	pageToken           string // 分页标记，第一次请求不填，表示从头开始遍历；分页查询结果还有更多项时会同时返回新的 page_token，下次遍历可采用该 page_token 获取查询结果（当custom_room_ids为空时，使用该字段进行查询）
	pageTokenFlag       bool
}

func NewSearchRoomPathReqBodyBuilder() *SearchRoomPathReqBodyBuilder {
	builder := &SearchRoomPathReqBodyBuilder{}
	return builder
}

// 用于查询指定会议室的租户自定义会议室ID列表，优先使用该字段进行查询
//
// 示例值：["10001"]
func (builder *SearchRoomPathReqBodyBuilder) CustomRoomIds(customRoomIds []string) *SearchRoomPathReqBodyBuilder {
	builder.customRoomIds = customRoomIds
	builder.customRoomIdsFlag = true
	return builder
}

// 会议室搜索关键词（当custom_room_ids为空时，使用该字段进行查询）
//
// 示例值：测试会议室
func (builder *SearchRoomPathReqBodyBuilder) Keyword(keyword string) *SearchRoomPathReqBodyBuilder {
	builder.keyword = keyword
	builder.keywordFlag = true
	return builder
}

// 在该会议室层级下进行搜索（当custom_room_ids为空时，使用该字段进行查询）
//
// 示例值：omb_4ad1a2c7a2fbc5fc9570f38456931293
func (builder *SearchRoomPathReqBodyBuilder) RoomLevelId(roomLevelId string) *SearchRoomPathReqBodyBuilder {
	builder.roomLevelId = roomLevelId
	builder.roomLevelIdFlag = true
	return builder
}

// 搜索会议室是否包括层级名称（当custom_room_ids为空时，使用该字段进行查询）
//
// 示例值：true
func (builder *SearchRoomPathReqBodyBuilder) SearchLevelName(searchLevelName bool) *SearchRoomPathReqBodyBuilder {
	builder.searchLevelName = searchLevelName
	builder.searchLevelNameFlag = true
	return builder
}

// 分页大小，该值默认为10，最大为100（当custom_room_ids为空时，使用该字段进行查询）
//
// 示例值：10
func (builder *SearchRoomPathReqBodyBuilder) PageSize(pageSize int) *SearchRoomPathReqBodyBuilder {
	builder.pageSize = pageSize
	builder.pageSizeFlag = true
	return builder
}

// 分页标记，第一次请求不填，表示从头开始遍历；分页查询结果还有更多项时会同时返回新的 page_token，下次遍历可采用该 page_token 获取查询结果（当custom_room_ids为空时，使用该字段进行查询）
//
// 示例值：0
func (builder *SearchRoomPathReqBodyBuilder) PageToken(pageToken string) *SearchRoomPathReqBodyBuilder {
	builder.pageToken = pageToken
	builder.pageTokenFlag = true
	return builder
}

func (builder *SearchRoomPathReqBodyBuilder) Build() (*SearchRoomReqBody, error) {
	req := &SearchRoomReqBody{}
	if builder.customRoomIdsFlag {
		req.CustomRoomIds = builder.customRoomIds
	}
	if builder.keywordFlag {
		req.Keyword = &builder.keyword
	}
	if builder.roomLevelIdFlag {
		req.RoomLevelId = &builder.roomLevelId
	}
	if builder.searchLevelNameFlag {
		req.SearchLevelName = &builder.searchLevelName
	}
	if builder.pageSizeFlag {
		req.PageSize = &builder.pageSize
	}
	if builder.pageTokenFlag {
		req.PageToken = &builder.pageToken
	}
	return req, nil
}

type SearchRoomReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *SearchRoomReqBody
}

func NewSearchRoomReqBuilder() *SearchRoomReqBuilder {
	builder := &SearchRoomReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 此次调用中使用的用户ID的类型，默认使用open_id可不填
//
// 示例值：
func (builder *SearchRoomReqBuilder) UserIdType(userIdType string) *SearchRoomReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

// 该接口可以用来搜索会议室，支持使用关键词进行搜索，也支持使用自定义会议室ID进行查询
func (builder *SearchRoomReqBuilder) Body(body *SearchRoomReqBody) *SearchRoomReqBuilder {
	builder.body = body
	return builder
}

func (builder *SearchRoomReqBuilder) Build() *SearchRoomReq {
	req := &SearchRoomReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.body
	return req
}

type SearchRoomReqBody struct {
	CustomRoomIds   []string `json:"custom_room_ids,omitempty"`   // 用于查询指定会议室的租户自定义会议室ID列表，优先使用该字段进行查询
	Keyword         *string  `json:"keyword,omitempty"`           // 会议室搜索关键词（当custom_room_ids为空时，使用该字段进行查询）
	RoomLevelId     *string  `json:"room_level_id,omitempty"`     // 在该会议室层级下进行搜索（当custom_room_ids为空时，使用该字段进行查询）
	SearchLevelName *bool    `json:"search_level_name,omitempty"` // 搜索会议室是否包括层级名称（当custom_room_ids为空时，使用该字段进行查询）
	PageSize        *int     `json:"page_size,omitempty"`         // 分页大小，该值默认为10，最大为100（当custom_room_ids为空时，使用该字段进行查询）
	PageToken       *string  `json:"page_token,omitempty"`        // 分页标记，第一次请求不填，表示从头开始遍历；分页查询结果还有更多项时会同时返回新的 page_token，下次遍历可采用该 page_token 获取查询结果（当custom_room_ids为空时，使用该字段进行查询）
}

type SearchRoomReq struct {
	apiReq *larkcore.ApiReq
	Body   *SearchRoomReqBody `body:""`
}

type SearchRoomRespData struct {
	Rooms     []*Room `json:"rooms,omitempty"`      // 会议室列表
	PageToken *string `json:"page_token,omitempty"` // 下一页分页的token，下次请求时传入
	HasMore   *bool   `json:"has_more,omitempty"`   // 是否还有数据
}

type SearchRoomResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *SearchRoomRespData `json:"data"` // 业务数据
}

func (resp *SearchRoomResp) Success() bool {
	return resp.Code == 0
}

type QueryRoomConfigReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewQueryRoomConfigReqBuilder() *QueryRoomConfigReqBuilder {
	builder := &QueryRoomConfigReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 查询节点范围
//
// 示例值：5
func (builder *QueryRoomConfigReqBuilder) Scope(scope int) *QueryRoomConfigReqBuilder {
	builder.apiReq.QueryParams.Set("scope", fmt.Sprint(scope))
	return builder
}

// 国家/地区ID scope为2，3时需要此参数
//
// 示例值：1
func (builder *QueryRoomConfigReqBuilder) CountryId(countryId string) *QueryRoomConfigReqBuilder {
	builder.apiReq.QueryParams.Set("country_id", fmt.Sprint(countryId))
	return builder
}

// 城市ID scope为3时需要此参数
//
// 示例值：2
func (builder *QueryRoomConfigReqBuilder) DistrictId(districtId string) *QueryRoomConfigReqBuilder {
	builder.apiReq.QueryParams.Set("district_id", fmt.Sprint(districtId))
	return builder
}

// 建筑ID scope为4，5时需要此参数
//
// 示例值：3
func (builder *QueryRoomConfigReqBuilder) BuildingId(buildingId string) *QueryRoomConfigReqBuilder {
	builder.apiReq.QueryParams.Set("building_id", fmt.Sprint(buildingId))
	return builder
}

// 楼层 scope为5时需要此参数
//
// 示例值：4
func (builder *QueryRoomConfigReqBuilder) FloorName(floorName string) *QueryRoomConfigReqBuilder {
	builder.apiReq.QueryParams.Set("floor_name", fmt.Sprint(floorName))
	return builder
}

// 会议室ID scope为6时需要此参数
//
// 示例值：6383786266263
func (builder *QueryRoomConfigReqBuilder) RoomId(roomId string) *QueryRoomConfigReqBuilder {
	builder.apiReq.QueryParams.Set("room_id", fmt.Sprint(roomId))
	return builder
}

// 此次调用中使用的用户ID的类型，默认使用open_id可不填
//
// 示例值：
func (builder *QueryRoomConfigReqBuilder) UserIdType(userIdType string) *QueryRoomConfigReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

func (builder *QueryRoomConfigReqBuilder) Build() *QueryRoomConfigReq {
	req := &QueryRoomConfigReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type QueryRoomConfigReq struct {
	apiReq *larkcore.ApiReq
}

type QueryRoomConfigRespData struct {
	RoomBackground        *string             `json:"room_background,omitempty"`          // 飞书会议室背景图
	DisplayBackground     *string             `json:"display_background,omitempty"`       // 飞书签到板背景图
	DigitalSignage        *RoomDigitalSignage `json:"digital_signage,omitempty"`          // 飞书会议室数字标牌
	RoomBoxDigitalSignage *RoomDigitalSignage `json:"room_box_digital_signage,omitempty"` // 飞书投屏盒子数字标牌
	RoomStatus            *RoomStatus         `json:"room_status,omitempty"`              // 会议室状态
}

type QueryRoomConfigResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *QueryRoomConfigRespData `json:"data"` // 业务数据
}

func (resp *QueryRoomConfigResp) Success() bool {
	return resp.Code == 0
}

type SetRoomConfigReqBodyBuilder struct {
	scope          int // 设置节点范围
	scopeFlag      bool
	countryId      string // 国家/地区ID scope为2，3时需要此参数
	countryIdFlag  bool
	districtId     string // 城市ID scope为3时需要此参数
	districtIdFlag bool
	buildingId     string // 建筑ID scope为4，5时需要此参数
	buildingIdFlag bool
	floorName      string // 楼层 scope为5时需要此参数
	floorNameFlag  bool
	roomId         string // 会议室ID scope为6时需要此参数
	roomIdFlag     bool
	roomConfig     *RoomConfig // 会议室设置
	roomConfigFlag bool
}

func NewSetRoomConfigReqBodyBuilder() *SetRoomConfigReqBodyBuilder {
	builder := &SetRoomConfigReqBodyBuilder{}
	return builder
}

// 设置节点范围
//
//示例值：5
func (builder *SetRoomConfigReqBodyBuilder) Scope(scope int) *SetRoomConfigReqBodyBuilder {
	builder.scope = scope
	builder.scopeFlag = true
	return builder
}

// 国家/地区ID scope为2，3时需要此参数
//
//示例值：1
func (builder *SetRoomConfigReqBodyBuilder) CountryId(countryId string) *SetRoomConfigReqBodyBuilder {
	builder.countryId = countryId
	builder.countryIdFlag = true
	return builder
}

// 城市ID scope为3时需要此参数
//
//示例值：2
func (builder *SetRoomConfigReqBodyBuilder) DistrictId(districtId string) *SetRoomConfigReqBodyBuilder {
	builder.districtId = districtId
	builder.districtIdFlag = true
	return builder
}

// 建筑ID scope为4，5时需要此参数
//
//示例值：3
func (builder *SetRoomConfigReqBodyBuilder) BuildingId(buildingId string) *SetRoomConfigReqBodyBuilder {
	builder.buildingId = buildingId
	builder.buildingIdFlag = true
	return builder
}

// 楼层 scope为5时需要此参数
//
//示例值：4
func (builder *SetRoomConfigReqBodyBuilder) FloorName(floorName string) *SetRoomConfigReqBodyBuilder {
	builder.floorName = floorName
	builder.floorNameFlag = true
	return builder
}

// 会议室ID scope为6时需要此参数
//
//示例值：67687262867363
func (builder *SetRoomConfigReqBodyBuilder) RoomId(roomId string) *SetRoomConfigReqBodyBuilder {
	builder.roomId = roomId
	builder.roomIdFlag = true
	return builder
}

// 会议室设置
//
//示例值：
func (builder *SetRoomConfigReqBodyBuilder) RoomConfig(roomConfig *RoomConfig) *SetRoomConfigReqBodyBuilder {
	builder.roomConfig = roomConfig
	builder.roomConfigFlag = true
	return builder
}

func (builder *SetRoomConfigReqBodyBuilder) Build() *SetRoomConfigReqBody {
	req := &SetRoomConfigReqBody{}
	if builder.scopeFlag {
		req.Scope = &builder.scope
	}
	if builder.countryIdFlag {
		req.CountryId = &builder.countryId
	}
	if builder.districtIdFlag {
		req.DistrictId = &builder.districtId
	}
	if builder.buildingIdFlag {
		req.BuildingId = &builder.buildingId
	}
	if builder.floorNameFlag {
		req.FloorName = &builder.floorName
	}
	if builder.roomIdFlag {
		req.RoomId = &builder.roomId
	}
	if builder.roomConfigFlag {
		req.RoomConfig = builder.roomConfig
	}
	return req
}

type SetRoomConfigPathReqBodyBuilder struct {
	scope          int // 设置节点范围
	scopeFlag      bool
	countryId      string // 国家/地区ID scope为2，3时需要此参数
	countryIdFlag  bool
	districtId     string // 城市ID scope为3时需要此参数
	districtIdFlag bool
	buildingId     string // 建筑ID scope为4，5时需要此参数
	buildingIdFlag bool
	floorName      string // 楼层 scope为5时需要此参数
	floorNameFlag  bool
	roomId         string // 会议室ID scope为6时需要此参数
	roomIdFlag     bool
	roomConfig     *RoomConfig // 会议室设置
	roomConfigFlag bool
}

func NewSetRoomConfigPathReqBodyBuilder() *SetRoomConfigPathReqBodyBuilder {
	builder := &SetRoomConfigPathReqBodyBuilder{}
	return builder
}

// 设置节点范围
//
// 示例值：5
func (builder *SetRoomConfigPathReqBodyBuilder) Scope(scope int) *SetRoomConfigPathReqBodyBuilder {
	builder.scope = scope
	builder.scopeFlag = true
	return builder
}

// 国家/地区ID scope为2，3时需要此参数
//
// 示例值：1
func (builder *SetRoomConfigPathReqBodyBuilder) CountryId(countryId string) *SetRoomConfigPathReqBodyBuilder {
	builder.countryId = countryId
	builder.countryIdFlag = true
	return builder
}

// 城市ID scope为3时需要此参数
//
// 示例值：2
func (builder *SetRoomConfigPathReqBodyBuilder) DistrictId(districtId string) *SetRoomConfigPathReqBodyBuilder {
	builder.districtId = districtId
	builder.districtIdFlag = true
	return builder
}

// 建筑ID scope为4，5时需要此参数
//
// 示例值：3
func (builder *SetRoomConfigPathReqBodyBuilder) BuildingId(buildingId string) *SetRoomConfigPathReqBodyBuilder {
	builder.buildingId = buildingId
	builder.buildingIdFlag = true
	return builder
}

// 楼层 scope为5时需要此参数
//
// 示例值：4
func (builder *SetRoomConfigPathReqBodyBuilder) FloorName(floorName string) *SetRoomConfigPathReqBodyBuilder {
	builder.floorName = floorName
	builder.floorNameFlag = true
	return builder
}

// 会议室ID scope为6时需要此参数
//
// 示例值：67687262867363
func (builder *SetRoomConfigPathReqBodyBuilder) RoomId(roomId string) *SetRoomConfigPathReqBodyBuilder {
	builder.roomId = roomId
	builder.roomIdFlag = true
	return builder
}

// 会议室设置
//
// 示例值：
func (builder *SetRoomConfigPathReqBodyBuilder) RoomConfig(roomConfig *RoomConfig) *SetRoomConfigPathReqBodyBuilder {
	builder.roomConfig = roomConfig
	builder.roomConfigFlag = true
	return builder
}

func (builder *SetRoomConfigPathReqBodyBuilder) Build() (*SetRoomConfigReqBody, error) {
	req := &SetRoomConfigReqBody{}
	if builder.scopeFlag {
		req.Scope = &builder.scope
	}
	if builder.countryIdFlag {
		req.CountryId = &builder.countryId
	}
	if builder.districtIdFlag {
		req.DistrictId = &builder.districtId
	}
	if builder.buildingIdFlag {
		req.BuildingId = &builder.buildingId
	}
	if builder.floorNameFlag {
		req.FloorName = &builder.floorName
	}
	if builder.roomIdFlag {
		req.RoomId = &builder.roomId
	}
	if builder.roomConfigFlag {
		req.RoomConfig = builder.roomConfig
	}
	return req, nil
}

type SetRoomConfigReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *SetRoomConfigReqBody
}

func NewSetRoomConfigReqBuilder() *SetRoomConfigReqBuilder {
	builder := &SetRoomConfigReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 此次调用中使用的用户ID的类型，默认使用open_id可不填
//
// 示例值：
func (builder *SetRoomConfigReqBuilder) UserIdType(userIdType string) *SetRoomConfigReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

//
func (builder *SetRoomConfigReqBuilder) Body(body *SetRoomConfigReqBody) *SetRoomConfigReqBuilder {
	builder.body = body
	return builder
}

func (builder *SetRoomConfigReqBuilder) Build() *SetRoomConfigReq {
	req := &SetRoomConfigReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.body
	return req
}

type SetRoomConfigReqBody struct {
	Scope      *int        `json:"scope,omitempty"`       // 设置节点范围
	CountryId  *string     `json:"country_id,omitempty"`  // 国家/地区ID scope为2，3时需要此参数
	DistrictId *string     `json:"district_id,omitempty"` // 城市ID scope为3时需要此参数
	BuildingId *string     `json:"building_id,omitempty"` // 建筑ID scope为4，5时需要此参数
	FloorName  *string     `json:"floor_name,omitempty"`  // 楼层 scope为5时需要此参数
	RoomId     *string     `json:"room_id,omitempty"`     // 会议室ID scope为6时需要此参数
	RoomConfig *RoomConfig `json:"room_config,omitempty"` // 会议室设置
}

type SetRoomConfigReq struct {
	apiReq *larkcore.ApiReq
	Body   *SetRoomConfigReqBody `body:""`
}

type SetRoomConfigResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *SetRoomConfigResp) Success() bool {
	return resp.Code == 0
}

type CreateRoomLevelReqBuilder struct {
	apiReq    *larkcore.ApiReq
	roomLevel *RoomLevel
}

func NewCreateRoomLevelReqBuilder() *CreateRoomLevelReqBuilder {
	builder := &CreateRoomLevelReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 该接口用于创建会议室层级
func (builder *CreateRoomLevelReqBuilder) RoomLevel(roomLevel *RoomLevel) *CreateRoomLevelReqBuilder {
	builder.roomLevel = roomLevel
	return builder
}

func (builder *CreateRoomLevelReqBuilder) Build() *CreateRoomLevelReq {
	req := &CreateRoomLevelReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.Body = builder.roomLevel
	return req
}

type CreateRoomLevelReq struct {
	apiReq    *larkcore.ApiReq
	RoomLevel *RoomLevel `body:""`
}

type CreateRoomLevelRespData struct {
	RoomLevel *RoomLevel `json:"room_level,omitempty"` // 层级详情
}

type CreateRoomLevelResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *CreateRoomLevelRespData `json:"data"` // 业务数据
}

func (resp *CreateRoomLevelResp) Success() bool {
	return resp.Code == 0
}

type DelRoomLevelReqBodyBuilder struct {
	roomLevelId     string // 层级ID
	roomLevelIdFlag bool
	deleteChild     bool // 是否删除所有子层级
	deleteChildFlag bool
}

func NewDelRoomLevelReqBodyBuilder() *DelRoomLevelReqBodyBuilder {
	builder := &DelRoomLevelReqBodyBuilder{}
	return builder
}

// 层级ID
//
//示例值：omb_4ad1a2c7a2fbc5fc9570f38456931293
func (builder *DelRoomLevelReqBodyBuilder) RoomLevelId(roomLevelId string) *DelRoomLevelReqBodyBuilder {
	builder.roomLevelId = roomLevelId
	builder.roomLevelIdFlag = true
	return builder
}

// 是否删除所有子层级
//
//示例值：false
func (builder *DelRoomLevelReqBodyBuilder) DeleteChild(deleteChild bool) *DelRoomLevelReqBodyBuilder {
	builder.deleteChild = deleteChild
	builder.deleteChildFlag = true
	return builder
}

func (builder *DelRoomLevelReqBodyBuilder) Build() *DelRoomLevelReqBody {
	req := &DelRoomLevelReqBody{}
	if builder.roomLevelIdFlag {
		req.RoomLevelId = &builder.roomLevelId
	}
	if builder.deleteChildFlag {
		req.DeleteChild = &builder.deleteChild
	}
	return req
}

type DelRoomLevelPathReqBodyBuilder struct {
	roomLevelId     string // 层级ID
	roomLevelIdFlag bool
	deleteChild     bool // 是否删除所有子层级
	deleteChildFlag bool
}

func NewDelRoomLevelPathReqBodyBuilder() *DelRoomLevelPathReqBodyBuilder {
	builder := &DelRoomLevelPathReqBodyBuilder{}
	return builder
}

// 层级ID
//
// 示例值：omb_4ad1a2c7a2fbc5fc9570f38456931293
func (builder *DelRoomLevelPathReqBodyBuilder) RoomLevelId(roomLevelId string) *DelRoomLevelPathReqBodyBuilder {
	builder.roomLevelId = roomLevelId
	builder.roomLevelIdFlag = true
	return builder
}

// 是否删除所有子层级
//
// 示例值：false
func (builder *DelRoomLevelPathReqBodyBuilder) DeleteChild(deleteChild bool) *DelRoomLevelPathReqBodyBuilder {
	builder.deleteChild = deleteChild
	builder.deleteChildFlag = true
	return builder
}

func (builder *DelRoomLevelPathReqBodyBuilder) Build() (*DelRoomLevelReqBody, error) {
	req := &DelRoomLevelReqBody{}
	if builder.roomLevelIdFlag {
		req.RoomLevelId = &builder.roomLevelId
	}
	if builder.deleteChildFlag {
		req.DeleteChild = &builder.deleteChild
	}
	return req, nil
}

type DelRoomLevelReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *DelRoomLevelReqBody
}

func NewDelRoomLevelReqBuilder() *DelRoomLevelReqBuilder {
	builder := &DelRoomLevelReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 该接口可以用来删除某个会议室层级
func (builder *DelRoomLevelReqBuilder) Body(body *DelRoomLevelReqBody) *DelRoomLevelReqBuilder {
	builder.body = body
	return builder
}

func (builder *DelRoomLevelReqBuilder) Build() *DelRoomLevelReq {
	req := &DelRoomLevelReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.Body = builder.body
	return req
}

type DelRoomLevelReqBody struct {
	RoomLevelId *string `json:"room_level_id,omitempty"` // 层级ID
	DeleteChild *bool   `json:"delete_child,omitempty"`  // 是否删除所有子层级
}

type DelRoomLevelReq struct {
	apiReq *larkcore.ApiReq
	Body   *DelRoomLevelReqBody `body:""`
}

type DelRoomLevelResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *DelRoomLevelResp) Success() bool {
	return resp.Code == 0
}

type GetRoomLevelReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewGetRoomLevelReqBuilder() *GetRoomLevelReqBuilder {
	builder := &GetRoomLevelReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 层级ID，查询租户层级可传0
//
// 示例值：omb_57c9cc7d9a81e27e54c8fabfd02759e7
func (builder *GetRoomLevelReqBuilder) RoomLevelId(roomLevelId string) *GetRoomLevelReqBuilder {
	builder.apiReq.PathParams.Set("room_level_id", fmt.Sprint(roomLevelId))
	return builder
}

func (builder *GetRoomLevelReqBuilder) Build() *GetRoomLevelReq {
	req := &GetRoomLevelReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	return req
}

type GetRoomLevelReq struct {
	apiReq *larkcore.ApiReq
}

type GetRoomLevelRespData struct {
	RoomLevel *RoomLevel `json:"room_level,omitempty"` // 会议室层级详情
}

type GetRoomLevelResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *GetRoomLevelRespData `json:"data"` // 业务数据
}

func (resp *GetRoomLevelResp) Success() bool {
	return resp.Code == 0
}

type ListRoomLevelReqBuilder struct {
	apiReq *larkcore.ApiReq
	limit  int // 最大返回多少记录，当使用迭代器访问时才有效
}

func NewListRoomLevelReqBuilder() *ListRoomLevelReqBuilder {
	builder := &ListRoomLevelReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 最大返回多少记录，当使用迭代器访问时才有效
func (builder *ListRoomLevelReqBuilder) Limit(limit int) *ListRoomLevelReqBuilder {
	builder.limit = limit
	return builder
}

// 层级ID，当需要获取租户下层级列表时，room_level_id可传空
//
// 示例值：omb_4ad1a2c7a2fbc5fc9570f38456931293
func (builder *ListRoomLevelReqBuilder) RoomLevelId(roomLevelId string) *ListRoomLevelReqBuilder {
	builder.apiReq.QueryParams.Set("room_level_id", fmt.Sprint(roomLevelId))
	return builder
}

// 分页尺寸大小
//
// 示例值：10
func (builder *ListRoomLevelReqBuilder) PageSize(pageSize int) *ListRoomLevelReqBuilder {
	builder.apiReq.QueryParams.Set("page_size", fmt.Sprint(pageSize))
	return builder
}

// 分页标记,第一次请求不填,表示从头开始遍历.下次遍历可采用该 page_token获取查询结果
//
// 示例值：
func (builder *ListRoomLevelReqBuilder) PageToken(pageToken string) *ListRoomLevelReqBuilder {
	builder.apiReq.QueryParams.Set("page_token", fmt.Sprint(pageToken))
	return builder
}

func (builder *ListRoomLevelReqBuilder) Build() *ListRoomLevelReq {
	req := &ListRoomLevelReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.Limit = builder.limit
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type ListRoomLevelReq struct {
	apiReq *larkcore.ApiReq
	Limit  int // 最多返回多少记录，只有在使用迭代器访问时，才有效

}

type ListRoomLevelRespData struct {
	Items     []*RoomLevel `json:"items,omitempty"`      // 会议室层级列表
	PageToken *string      `json:"page_token,omitempty"` //
	HasMore   *bool        `json:"has_more,omitempty"`   //
}

type ListRoomLevelResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *ListRoomLevelRespData `json:"data"` // 业务数据
}

func (resp *ListRoomLevelResp) Success() bool {
	return resp.Code == 0
}

type MgetRoomLevelReqBodyBuilder struct {
	levelIds     []string // 层级ID列表
	levelIdsFlag bool
}

func NewMgetRoomLevelReqBodyBuilder() *MgetRoomLevelReqBodyBuilder {
	builder := &MgetRoomLevelReqBodyBuilder{}
	return builder
}

// 层级ID列表
//
//示例值：["omb_4ad1a2c7a2fbc5fc9570f38456931293"]
func (builder *MgetRoomLevelReqBodyBuilder) LevelIds(levelIds []string) *MgetRoomLevelReqBodyBuilder {
	builder.levelIds = levelIds
	builder.levelIdsFlag = true
	return builder
}

func (builder *MgetRoomLevelReqBodyBuilder) Build() *MgetRoomLevelReqBody {
	req := &MgetRoomLevelReqBody{}
	if builder.levelIdsFlag {
		req.LevelIds = builder.levelIds
	}
	return req
}

type MgetRoomLevelPathReqBodyBuilder struct {
	levelIds     []string // 层级ID列表
	levelIdsFlag bool
}

func NewMgetRoomLevelPathReqBodyBuilder() *MgetRoomLevelPathReqBodyBuilder {
	builder := &MgetRoomLevelPathReqBodyBuilder{}
	return builder
}

// 层级ID列表
//
// 示例值：["omb_4ad1a2c7a2fbc5fc9570f38456931293"]
func (builder *MgetRoomLevelPathReqBodyBuilder) LevelIds(levelIds []string) *MgetRoomLevelPathReqBodyBuilder {
	builder.levelIds = levelIds
	builder.levelIdsFlag = true
	return builder
}

func (builder *MgetRoomLevelPathReqBodyBuilder) Build() (*MgetRoomLevelReqBody, error) {
	req := &MgetRoomLevelReqBody{}
	if builder.levelIdsFlag {
		req.LevelIds = builder.levelIds
	}
	return req, nil
}

type MgetRoomLevelReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *MgetRoomLevelReqBody
}

func NewMgetRoomLevelReqBuilder() *MgetRoomLevelReqBuilder {
	builder := &MgetRoomLevelReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 该接口可以使用会议室层级ID批量查询会议室层级详情
func (builder *MgetRoomLevelReqBuilder) Body(body *MgetRoomLevelReqBody) *MgetRoomLevelReqBuilder {
	builder.body = body
	return builder
}

func (builder *MgetRoomLevelReqBuilder) Build() *MgetRoomLevelReq {
	req := &MgetRoomLevelReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.Body = builder.body
	return req
}

type MgetRoomLevelReqBody struct {
	LevelIds []string `json:"level_ids,omitempty"` // 层级ID列表
}

type MgetRoomLevelReq struct {
	apiReq *larkcore.ApiReq
	Body   *MgetRoomLevelReqBody `body:""`
}

type MgetRoomLevelRespData struct {
	Items []*RoomLevel `json:"items,omitempty"` // 会议室层级列表
}

type MgetRoomLevelResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *MgetRoomLevelRespData `json:"data"` // 业务数据
}

func (resp *MgetRoomLevelResp) Success() bool {
	return resp.Code == 0
}

type PatchRoomLevelReqBuilder struct {
	apiReq    *larkcore.ApiReq
	roomLevel *RoomLevel
}

func NewPatchRoomLevelReqBuilder() *PatchRoomLevelReqBuilder {
	builder := &PatchRoomLevelReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 层级ID
//
// 示例值：omb_4ad1a2c7a2fbc5fc9570f38456931293
func (builder *PatchRoomLevelReqBuilder) RoomLevelId(roomLevelId string) *PatchRoomLevelReqBuilder {
	builder.apiReq.PathParams.Set("room_level_id", fmt.Sprint(roomLevelId))
	return builder
}

// 该接口可以用来更新某个会议室层级的信息
func (builder *PatchRoomLevelReqBuilder) RoomLevel(roomLevel *RoomLevel) *PatchRoomLevelReqBuilder {
	builder.roomLevel = roomLevel
	return builder
}

func (builder *PatchRoomLevelReqBuilder) Build() *PatchRoomLevelReq {
	req := &PatchRoomLevelReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.Body = builder.roomLevel
	return req
}

type PatchRoomLevelReq struct {
	apiReq    *larkcore.ApiReq
	RoomLevel *RoomLevel `body:""`
}

type PatchRoomLevelResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *PatchRoomLevelResp) Success() bool {
	return resp.Code == 0
}

type SearchRoomLevelReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewSearchRoomLevelReqBuilder() *SearchRoomLevelReqBuilder {
	builder := &SearchRoomLevelReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 用于查询指定会议室层级的自定义会议室层级ID
//
// 示例值：1000,1001
func (builder *SearchRoomLevelReqBuilder) CustomLevelIds(customLevelIds string) *SearchRoomLevelReqBuilder {
	builder.apiReq.QueryParams.Set("custom_level_ids", fmt.Sprint(customLevelIds))
	return builder
}

func (builder *SearchRoomLevelReqBuilder) Build() *SearchRoomLevelReq {
	req := &SearchRoomLevelReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type SearchRoomLevelReq struct {
	apiReq *larkcore.ApiReq
}

type SearchRoomLevelRespData struct {
	LevelIds []string `json:"level_ids,omitempty"` // 层级ID列表
}

type SearchRoomLevelResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *SearchRoomLevelRespData `json:"data"` // 业务数据
}

func (resp *SearchRoomLevelResp) Success() bool {
	return resp.Code == 0
}

type CreateScopeConfigReqBuilder struct {
	apiReq      *larkcore.ApiReq
	scopeConfig *ScopeConfig
}

func NewCreateScopeConfigReqBuilder() *CreateScopeConfigReqBuilder {
	builder := &CreateScopeConfigReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 此次调用中使用的用户ID的类型，默认使用open_id可不填
//
// 示例值：open_id
func (builder *CreateScopeConfigReqBuilder) UserIdType(userIdType string) *CreateScopeConfigReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

// 该接口可以用来设置某个会议层级范围下或者某个会议室的配置
func (builder *CreateScopeConfigReqBuilder) ScopeConfig(scopeConfig *ScopeConfig) *CreateScopeConfigReqBuilder {
	builder.scopeConfig = scopeConfig
	return builder
}

func (builder *CreateScopeConfigReqBuilder) Build() *CreateScopeConfigReq {
	req := &CreateScopeConfigReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.scopeConfig
	return req
}

type CreateScopeConfigReq struct {
	apiReq      *larkcore.ApiReq
	ScopeConfig *ScopeConfig `body:""`
}

type CreateScopeConfigResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *CreateScopeConfigResp) Success() bool {
	return resp.Code == 0
}

type GetScopeConfigReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewGetScopeConfigReqBuilder() *GetScopeConfigReqBuilder {
	builder := &GetScopeConfigReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 查询节点范围
//
// 示例值：1
func (builder *GetScopeConfigReqBuilder) ScopeType(scopeType int) *GetScopeConfigReqBuilder {
	builder.apiReq.QueryParams.Set("scope_type", fmt.Sprint(scopeType))
	return builder
}

// 查询节点ID：如果scope_type为1，则为层级ID，如果scope_type为2，则为会议室ID
//
// 示例值：omm_608d34d82d531b27fa993902d350a307
func (builder *GetScopeConfigReqBuilder) ScopeId(scopeId string) *GetScopeConfigReqBuilder {
	builder.apiReq.QueryParams.Set("scope_id", fmt.Sprint(scopeId))
	return builder
}

// 此次调用中使用的用户ID的类型，默认使用open_id可不填
//
// 示例值：
func (builder *GetScopeConfigReqBuilder) UserIdType(userIdType string) *GetScopeConfigReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

func (builder *GetScopeConfigReqBuilder) Build() *GetScopeConfigReq {
	req := &GetScopeConfigReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type GetScopeConfigReq struct {
	apiReq *larkcore.ApiReq
}

type GetScopeConfigRespData struct {
	CurrentConfig *ScopeConfig   `json:"current_config,omitempty"` // 当前节点的配置，根据层级顺序从底向上进行合并计算后的结果；如果当前节点某个值已配置，则取该节点的值，否则会从该节点的父层级节点获取，如果父节点依然未配置，则继续向上递归获取；若所有节点均未配置，则该值返回为空
	OriginConfigs []*ScopeConfig `json:"origin_configs,omitempty"` // 所有节点的原始配置，按照层级顺序从底向上返回；如果某节点某个值未配置，则该值返回为空
}

type GetScopeConfigResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *GetScopeConfigRespData `json:"data"` // 业务数据
}

func (resp *GetScopeConfigResp) Success() bool {
	return resp.Code == 0
}

type P2MeetingJoinMeetingV1Data struct {
	Meeting  *MeetingEventMeeting `json:"meeting,omitempty"`  // 会议数据
	Operator *MeetingEventUser    `json:"operator,omitempty"` // 事件操作人
}

type P2MeetingJoinMeetingV1 struct {
	*larkevent.EventV2Base                             // 事件基础数据
	*larkevent.EventReq                                // 请求原生数据
	Event                  *P2MeetingJoinMeetingV1Data `json:"event"` // 事件内容
}

func (m *P2MeetingJoinMeetingV1) RawReq(req *larkevent.EventReq) {
	m.EventReq = req
}

type P2MeetingLeaveMeetingV1Data struct {
	Meeting     *MeetingEventMeeting `json:"meeting,omitempty"`      // 会议数据
	Operator    *MeetingEventUser    `json:"operator,omitempty"`     // 事件操作人
	LeaveReason *int                 `json:"leave_reason,omitempty"` // 离开会议原因
}

type P2MeetingLeaveMeetingV1 struct {
	*larkevent.EventV2Base                              // 事件基础数据
	*larkevent.EventReq                                 // 请求原生数据
	Event                  *P2MeetingLeaveMeetingV1Data `json:"event"` // 事件内容
}

func (m *P2MeetingLeaveMeetingV1) RawReq(req *larkevent.EventReq) {
	m.EventReq = req
}

type P2MeetingEndedV1Data struct {
	Meeting  *MeetingEventMeeting `json:"meeting,omitempty"`  // 会议数据
	Operator *MeetingEventUser    `json:"operator,omitempty"` // 事件操作人
}

type P2MeetingEndedV1 struct {
	*larkevent.EventV2Base                       // 事件基础数据
	*larkevent.EventReq                          // 请求原生数据
	Event                  *P2MeetingEndedV1Data `json:"event"` // 事件内容
}

func (m *P2MeetingEndedV1) RawReq(req *larkevent.EventReq) {
	m.EventReq = req
}

type P2MeetingStartedV1Data struct {
	Meeting  *MeetingEventMeeting `json:"meeting,omitempty"`  // 会议数据
	Operator *MeetingEventUser    `json:"operator,omitempty"` // 事件操作人
}

type P2MeetingStartedV1 struct {
	*larkevent.EventV2Base                         // 事件基础数据
	*larkevent.EventReq                            // 请求原生数据
	Event                  *P2MeetingStartedV1Data `json:"event"` // 事件内容
}

func (m *P2MeetingStartedV1) RawReq(req *larkevent.EventReq) {
	m.EventReq = req
}

type P2MeetingRecordingEndedV1Data struct {
	Meeting  *MeetingEventMeeting `json:"meeting,omitempty"`  // 会议数据
	Operator *MeetingEventUser    `json:"operator,omitempty"` // 事件操作人
}

type P2MeetingRecordingEndedV1 struct {
	*larkevent.EventV2Base                                // 事件基础数据
	*larkevent.EventReq                                   // 请求原生数据
	Event                  *P2MeetingRecordingEndedV1Data `json:"event"` // 事件内容
}

func (m *P2MeetingRecordingEndedV1) RawReq(req *larkevent.EventReq) {
	m.EventReq = req
}

type P2MeetingRecordingReadyV1Data struct {
	Meeting  *MeetingEventMeeting `json:"meeting,omitempty"`  // 会议数据
	Url      *string              `json:"url,omitempty"`      // 会议录制链接
	Duration *string              `json:"duration,omitempty"` // 录制总时长（单位msec）
}

type P2MeetingRecordingReadyV1 struct {
	*larkevent.EventV2Base                                // 事件基础数据
	*larkevent.EventReq                                   // 请求原生数据
	Event                  *P2MeetingRecordingReadyV1Data `json:"event"` // 事件内容
}

func (m *P2MeetingRecordingReadyV1) RawReq(req *larkevent.EventReq) {
	m.EventReq = req
}

type P2MeetingRecordingStartedV1Data struct {
	Meeting  *MeetingEventMeeting `json:"meeting,omitempty"`  // 会议数据
	Operator *MeetingEventUser    `json:"operator,omitempty"` // 事件操作人
}

type P2MeetingRecordingStartedV1 struct {
	*larkevent.EventV2Base                                  // 事件基础数据
	*larkevent.EventReq                                     // 请求原生数据
	Event                  *P2MeetingRecordingStartedV1Data `json:"event"` // 事件内容
}

func (m *P2MeetingRecordingStartedV1) RawReq(req *larkevent.EventReq) {
	m.EventReq = req
}

type P2MeetingShareEndedV1Data struct {
	Meeting  *MeetingEventMeeting `json:"meeting,omitempty"`  // 会议数据
	Operator *MeetingEventUser    `json:"operator,omitempty"` // 事件操作人
}

type P2MeetingShareEndedV1 struct {
	*larkevent.EventV2Base                            // 事件基础数据
	*larkevent.EventReq                               // 请求原生数据
	Event                  *P2MeetingShareEndedV1Data `json:"event"` // 事件内容
}

func (m *P2MeetingShareEndedV1) RawReq(req *larkevent.EventReq) {
	m.EventReq = req
}

type P2MeetingShareStartedV1Data struct {
	Meeting  *MeetingEventMeeting `json:"meeting,omitempty"`  // 会议数据
	Operator *MeetingEventUser    `json:"operator,omitempty"` // 事件操作人
}

type P2MeetingShareStartedV1 struct {
	*larkevent.EventV2Base                              // 事件基础数据
	*larkevent.EventReq                                 // 请求原生数据
	Event                  *P2MeetingShareStartedV1Data `json:"event"` // 事件内容
}

func (m *P2MeetingShareStartedV1) RawReq(req *larkevent.EventReq) {
	m.EventReq = req
}

type P2RoomCreatedV1Data struct {
	Room *RoomEvent `json:"room,omitempty"` // 会议室信息
}

type P2RoomCreatedV1 struct {
	*larkevent.EventV2Base                      // 事件基础数据
	*larkevent.EventReq                         // 请求原生数据
	Event                  *P2RoomCreatedV1Data `json:"event"` // 事件内容
}

func (m *P2RoomCreatedV1) RawReq(req *larkevent.EventReq) {
	m.EventReq = req
}

type P2RoomDeletedV1Data struct {
	Room *RoomEvent `json:"room,omitempty"` // 会议室信息
}

type P2RoomDeletedV1 struct {
	*larkevent.EventV2Base                      // 事件基础数据
	*larkevent.EventReq                         // 请求原生数据
	Event                  *P2RoomDeletedV1Data `json:"event"` // 事件内容
}

func (m *P2RoomDeletedV1) RawReq(req *larkevent.EventReq) {
	m.EventReq = req
}

type P2RoomUpdatedV1Data struct {
	Room *RoomEvent `json:"room,omitempty"` // 会议室详情
}

type P2RoomUpdatedV1 struct {
	*larkevent.EventV2Base                      // 事件基础数据
	*larkevent.EventReq                         // 请求原生数据
	Event                  *P2RoomUpdatedV1Data `json:"event"` // 事件内容
}

func (m *P2RoomUpdatedV1) RawReq(req *larkevent.EventReq) {
	m.EventReq = req
}

type ListAlertIterator struct {
	nextPageToken *string
	items         []*Alert
	index         int
	limit         int
	ctx           context.Context
	req           *ListAlertReq
	listFunc      func(ctx context.Context, req *ListAlertReq, options ...larkcore.RequestOptionFunc) (*ListAlertResp, error)
	options       []larkcore.RequestOptionFunc
	curlNum       int
}

func (iterator *ListAlertIterator) Next() (bool, *Alert, error) {
	// 达到最大量，则返回
	if iterator.limit > 0 && iterator.curlNum >= iterator.limit {
		return false, nil, nil
	}

	// 为0则拉取数据
	if iterator.index == 0 || iterator.index >= len(iterator.items) {
		if iterator.index != 0 && iterator.nextPageToken == nil {
			return false, nil, nil
		}
		if iterator.nextPageToken != nil {
			iterator.req.apiReq.QueryParams.Set("page_token", *iterator.nextPageToken)
		}
		resp, err := iterator.listFunc(iterator.ctx, iterator.req, iterator.options...)
		if err != nil {
			return false, nil, err
		}

		if resp.Code != 0 {
			return false, nil, errors.New(fmt.Sprintf("Code:%d,Msg:%s", resp.Code, resp.Msg))
		}

		if len(resp.Data.Items) == 0 {
			return false, nil, nil
		}

		iterator.nextPageToken = resp.Data.PageToken
		iterator.items = resp.Data.Items
		iterator.index = 0
	}

	block := iterator.items[iterator.index]
	iterator.index++
	iterator.curlNum++
	return true, block, nil
}

func (iterator *ListAlertIterator) NextPageToken() *string {
	return iterator.nextPageToken
}

type ListByNoMeetingIterator struct {
	nextPageToken *string
	items         []*Meeting
	index         int
	limit         int
	ctx           context.Context
	req           *ListByNoMeetingReq
	listFunc      func(ctx context.Context, req *ListByNoMeetingReq, options ...larkcore.RequestOptionFunc) (*ListByNoMeetingResp, error)
	options       []larkcore.RequestOptionFunc
	curlNum       int
}

func (iterator *ListByNoMeetingIterator) Next() (bool, *Meeting, error) {
	// 达到最大量，则返回
	if iterator.limit > 0 && iterator.curlNum >= iterator.limit {
		return false, nil, nil
	}

	// 为0则拉取数据
	if iterator.index == 0 || iterator.index >= len(iterator.items) {
		if iterator.index != 0 && iterator.nextPageToken == nil {
			return false, nil, nil
		}
		if iterator.nextPageToken != nil {
			iterator.req.apiReq.QueryParams.Set("page_token", *iterator.nextPageToken)
		}
		resp, err := iterator.listFunc(iterator.ctx, iterator.req, iterator.options...)
		if err != nil {
			return false, nil, err
		}

		if resp.Code != 0 {
			return false, nil, errors.New(fmt.Sprintf("Code:%d,Msg:%s", resp.Code, resp.Msg))
		}

		if len(resp.Data.MeetingBriefs) == 0 {
			return false, nil, nil
		}

		iterator.nextPageToken = resp.Data.PageToken
		iterator.items = resp.Data.MeetingBriefs
		iterator.index = 0
	}

	block := iterator.items[iterator.index]
	iterator.index++
	iterator.curlNum++
	return true, block, nil
}

func (iterator *ListByNoMeetingIterator) NextPageToken() *string {
	return iterator.nextPageToken
}

type ListRoomIterator struct {
	nextPageToken *string
	items         []*Room
	index         int
	limit         int
	ctx           context.Context
	req           *ListRoomReq
	listFunc      func(ctx context.Context, req *ListRoomReq, options ...larkcore.RequestOptionFunc) (*ListRoomResp, error)
	options       []larkcore.RequestOptionFunc
	curlNum       int
}

func (iterator *ListRoomIterator) Next() (bool, *Room, error) {
	// 达到最大量，则返回
	if iterator.limit > 0 && iterator.curlNum >= iterator.limit {
		return false, nil, nil
	}

	// 为0则拉取数据
	if iterator.index == 0 || iterator.index >= len(iterator.items) {
		if iterator.index != 0 && iterator.nextPageToken == nil {
			return false, nil, nil
		}
		if iterator.nextPageToken != nil {
			iterator.req.apiReq.QueryParams.Set("page_token", *iterator.nextPageToken)
		}
		resp, err := iterator.listFunc(iterator.ctx, iterator.req, iterator.options...)
		if err != nil {
			return false, nil, err
		}

		if resp.Code != 0 {
			return false, nil, errors.New(fmt.Sprintf("Code:%d,Msg:%s", resp.Code, resp.Msg))
		}

		if len(resp.Data.Rooms) == 0 {
			return false, nil, nil
		}

		iterator.nextPageToken = resp.Data.PageToken
		iterator.items = resp.Data.Rooms
		iterator.index = 0
	}

	block := iterator.items[iterator.index]
	iterator.index++
	iterator.curlNum++
	return true, block, nil
}

func (iterator *ListRoomIterator) NextPageToken() *string {
	return iterator.nextPageToken
}

type ListRoomLevelIterator struct {
	nextPageToken *string
	items         []*RoomLevel
	index         int
	limit         int
	ctx           context.Context
	req           *ListRoomLevelReq
	listFunc      func(ctx context.Context, req *ListRoomLevelReq, options ...larkcore.RequestOptionFunc) (*ListRoomLevelResp, error)
	options       []larkcore.RequestOptionFunc
	curlNum       int
}

func (iterator *ListRoomLevelIterator) Next() (bool, *RoomLevel, error) {
	// 达到最大量，则返回
	if iterator.limit > 0 && iterator.curlNum >= iterator.limit {
		return false, nil, nil
	}

	// 为0则拉取数据
	if iterator.index == 0 || iterator.index >= len(iterator.items) {
		if iterator.index != 0 && iterator.nextPageToken == nil {
			return false, nil, nil
		}
		if iterator.nextPageToken != nil {
			iterator.req.apiReq.QueryParams.Set("page_token", *iterator.nextPageToken)
		}
		resp, err := iterator.listFunc(iterator.ctx, iterator.req, iterator.options...)
		if err != nil {
			return false, nil, err
		}

		if resp.Code != 0 {
			return false, nil, errors.New(fmt.Sprintf("Code:%d,Msg:%s", resp.Code, resp.Msg))
		}

		if len(resp.Data.Items) == 0 {
			return false, nil, nil
		}

		iterator.nextPageToken = resp.Data.PageToken
		iterator.items = resp.Data.Items
		iterator.index = 0
	}

	block := iterator.items[iterator.index]
	iterator.index++
	iterator.curlNum++
	return true, block, nil
}

func (iterator *ListRoomLevelIterator) NextPageToken() *string {
	return iterator.nextPageToken
}
