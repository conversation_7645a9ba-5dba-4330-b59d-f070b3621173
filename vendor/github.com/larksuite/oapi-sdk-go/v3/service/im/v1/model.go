// Package im code generated by oapi sdk gen
/*
 * MIT License
 *
 * Copyright (c) 2022 Lark Technologies Pte. Ltd.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice, shall be included in all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */

package larkim

import (
	"io"

	"bytes"

	"io/ioutil"

	"fmt"

	"context"
	"errors"

	"github.com/larksuite/oapi-sdk-go/v3/event"

	"github.com/larksuite/oapi-sdk-go/v3/core"
)

const (
	UserIdTypeUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypeUnionId = "union_id" // 以union_id来识别用户
	UserIdTypeOpenId  = "open_id"  // 以open_id来识别用户
)

const (
	UserIdTypeGetChatUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypeGetChatUnionId = "union_id" // 以union_id来识别用户
	UserIdTypeGetChatOpenId  = "open_id"  // 以open_id来识别用户
)

const (
	ValidityPeriodOneWeek     = "week"        // 有效期7天
	ValidityPeriodOneYear     = "year"        // 有效期1年
	ValidityPeriodPermanently = "permanently" // 永久有效
)

const (
	UserIdTypeListChatUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypeListChatUnionId = "union_id" // 以union_id来识别用户
	UserIdTypeListChatOpenId  = "open_id"  // 以open_id来识别用户
)

const (
	UserIdTypeSearchChatUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypeSearchChatUnionId = "union_id" // 以union_id来识别用户
	UserIdTypeSearchChatOpenId  = "open_id"  // 以open_id来识别用户
)

const (
	UserIdTypeUpdateChatUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypeUpdateChatUnionId = "union_id" // 以union_id来识别用户
	UserIdTypeUpdateChatOpenId  = "open_id"  // 以open_id来识别用户
)

const (
	UserIdTypeGetChatAnnouncementUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypeGetChatAnnouncementUnionId = "union_id" // 以union_id来识别用户
	UserIdTypeGetChatAnnouncementOpenId  = "open_id"  // 以open_id来识别用户
)

const (
	MemberIdTypeUserId  = "user_id"  // 以user_id来识别成员
	MemberIdTypeUnionId = "union_id" // 以union_id来识别成员
	MemberIdTypeOpenId  = "open_id"  // 以open_id来识别成员
	MemberIdTypeAppId   = "app_id"   // 以app_id来识别成员
)

const (
	MemberIdTypeDeleteManagersChatManagersUserId  = "user_id"  // 以user_id来识别成员
	MemberIdTypeDeleteManagersChatManagersUnionId = "union_id" // 以union_id来识别成员
	MemberIdTypeDeleteManagersChatManagersOpenId  = "open_id"  // 以open_id来识别成员
	MemberIdTypeDeleteManagersChatManagersAppId   = "app_id"   // 以app_id来识别成员
)

const (
	MemberIdTypeCreateChatMembersUserId  = "user_id"  // 以user_id来识别成员
	MemberIdTypeCreateChatMembersUnionId = "union_id" // 以union_id来识别成员
	MemberIdTypeCreateChatMembersOpenId  = "open_id"  // 以open_id来识别成员
	MemberIdTypeCreateChatMembersAppId   = "app_id"   // 以app_id来识别成员
)

const (
	SucceedType0 = 0 // 保持以前的策略，存在不可用的 ID 会拉群失败，返回错误响应
	SucceedType1 = 1 // 将可用 ID 全部拉入群，返回拉群成功的响应，并展示不可用的
	SucceedType2 = 2 // 存在不可用的 ID 会拉群失败，返回错误响应，并展示出不可用的 ID

)

const (
	MemberIdTypeDeleteChatMembersUserId  = "user_id"  // 以user_id来识别成员
	MemberIdTypeDeleteChatMembersUnionId = "union_id" // 以union_id来识别成员
	MemberIdTypeDeleteChatMembersOpenId  = "open_id"  // 以open_id来识别成员
	MemberIdTypeDeleteChatMembersAppId   = "app_id"   // 以app_id来识别成员
)

const (
	MemberIdTypeGetChatMembersUserId  = "user_id"  // 以user_id来识别成员
	MemberIdTypeGetChatMembersUnionId = "union_id" // 以union_id来识别成员
	MemberIdTypeGetChatMembersOpenId  = "open_id"  // 以open_id来识别成员
)

const (
	UserIdTypeGetChatModerationUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypeGetChatModerationUnionId = "union_id" // 以union_id来识别用户
	UserIdTypeGetChatModerationOpenId  = "open_id"  // 以open_id来识别用户
)

const (
	UserIdTypeUpdateChatModerationUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypeUpdateChatModerationUnionId = "union_id" // 以union_id来识别用户
	UserIdTypeUpdateChatModerationOpenId  = "open_id"  // 以open_id来识别用户
)

const (
	FileTypeOpus   = "opus"   // 上传opus音频文件
	FileTypeMp4    = "mp4"    // 上传mp4视频文件
	FileTypePdf    = "pdf"    // 上传pdf格式文件
	FileTypeDoc    = "doc"    // 上传doc格式文件
	FileTypeXls    = "xls"    // 上传xls格式文件
	FileTypePpt    = "ppt"    // 上传ppt格式文件
	FileTypeStream = "stream" // 上传stream格式文件
)

const (
	ImageTypeMessage = "message" // 用于发送消息
	ImageTypeAvatar  = "avatar"  // 用于设置头像
)

const (
	ReceiveIdTypeOpenId  = "open_id"  // 以open_id来识别用户
	ReceiveIdTypeUserId  = "user_id"  // 以user_id来识别用户
	ReceiveIdTypeUnionId = "union_id" // 以union_id来识别用户
	ReceiveIdTypeEmail   = "email"    // 以email来识别用户
	ReceiveIdTypeChatId  = "chat_id"  // 以chat_id来识别群聊
)

const (
	UserIdTypeReadUsersMessageUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypeReadUsersMessageUnionId = "union_id" // 以union_id来识别用户
	UserIdTypeReadUsersMessageOpenId  = "open_id"  // 以open_id来识别用户
)

const (
	UserIdTypeUrgentAppMessageUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypeUrgentAppMessageUnionId = "union_id" // 以union_id来识别用户
	UserIdTypeUrgentAppMessageOpenId  = "open_id"  // 以open_id来识别用户
)

const (
	UserIdTypeUrgentPhoneMessageUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypeUrgentPhoneMessageUnionId = "union_id" // 以union_id来识别用户
	UserIdTypeUrgentPhoneMessageOpenId  = "open_id"  // 以open_id来识别用户
)

const (
	UserIdTypeUrgentSmsMessageUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypeUrgentSmsMessageUnionId = "union_id" // 以union_id来识别用户
	UserIdTypeUrgentSmsMessageOpenId  = "open_id"  // 以open_id来识别用户
)

const (
	UserIdTypeListMessageReactionOpenId  = "open_id"  //
	UserIdTypeListMessageReactionUnionId = "union_id" //
	UserIdTypeListMessageReactionUserId  = "user_id"  //
)

type BatchMessage struct {
	BatchMessageId      *string              `json:"batch_message_id,omitempty"`      // 批量发消息的批次号，代表某次批量发送消息的唯一标识
	BatchSendProgress   *BatchSendProgress   `json:"batch_send_progress,omitempty"`   // 发送进度
	BatchRecallProgress *BatchRecallProgress `json:"batch_recall_progress,omitempty"` // 撤回进度
}

type BatchMessageBuilder struct {
	batchMessageId          string // 批量发消息的批次号，代表某次批量发送消息的唯一标识
	batchMessageIdFlag      bool
	batchSendProgress       *BatchSendProgress // 发送进度
	batchSendProgressFlag   bool
	batchRecallProgress     *BatchRecallProgress // 撤回进度
	batchRecallProgressFlag bool
}

func NewBatchMessageBuilder() *BatchMessageBuilder {
	builder := &BatchMessageBuilder{}
	return builder
}

// 批量发消息的批次号，代表某次批量发送消息的唯一标识
//
// 示例值：bm_dc13264520392913993dd051dba21dcf
func (builder *BatchMessageBuilder) BatchMessageId(batchMessageId string) *BatchMessageBuilder {
	builder.batchMessageId = batchMessageId
	builder.batchMessageIdFlag = true
	return builder
}

// 发送进度
//
// 示例值：json结构
func (builder *BatchMessageBuilder) BatchSendProgress(batchSendProgress *BatchSendProgress) *BatchMessageBuilder {
	builder.batchSendProgress = batchSendProgress
	builder.batchSendProgressFlag = true
	return builder
}

// 撤回进度
//
// 示例值：json结构
func (builder *BatchMessageBuilder) BatchRecallProgress(batchRecallProgress *BatchRecallProgress) *BatchMessageBuilder {
	builder.batchRecallProgress = batchRecallProgress
	builder.batchRecallProgressFlag = true
	return builder
}

func (builder *BatchMessageBuilder) Build() *BatchMessage {
	req := &BatchMessage{}
	if builder.batchMessageIdFlag {
		req.BatchMessageId = &builder.batchMessageId

	}
	if builder.batchSendProgressFlag {
		req.BatchSendProgress = builder.batchSendProgress
	}
	if builder.batchRecallProgressFlag {
		req.BatchRecallProgress = builder.batchRecallProgress
	}
	return req
}

type BatchMessageReadUser struct {
	ReadCount  *string `json:"read_count,omitempty"`  // 已读的人数
	TotalCount *string `json:"total_count,omitempty"` // 推送的总人数
}

type BatchMessageReadUserBuilder struct {
	readCount      string // 已读的人数
	readCountFlag  bool
	totalCount     string // 推送的总人数
	totalCountFlag bool
}

func NewBatchMessageReadUserBuilder() *BatchMessageReadUserBuilder {
	builder := &BatchMessageReadUserBuilder{}
	return builder
}

// 已读的人数
//
// 示例值：
func (builder *BatchMessageReadUserBuilder) ReadCount(readCount string) *BatchMessageReadUserBuilder {
	builder.readCount = readCount
	builder.readCountFlag = true
	return builder
}

// 推送的总人数
//
// 示例值：
func (builder *BatchMessageReadUserBuilder) TotalCount(totalCount string) *BatchMessageReadUserBuilder {
	builder.totalCount = totalCount
	builder.totalCountFlag = true
	return builder
}

func (builder *BatchMessageReadUserBuilder) Build() *BatchMessageReadUser {
	req := &BatchMessageReadUser{}
	if builder.readCountFlag {
		req.ReadCount = &builder.readCount

	}
	if builder.totalCountFlag {
		req.TotalCount = &builder.totalCount

	}
	return req
}

type BatchMessageRecallProgress struct {
	Recall      *bool `json:"recall,omitempty"`       // 该条批量消息是否被执行过撤回操作
	RecallCount *int  `json:"recall_count,omitempty"` // 已经成功撤回的消息数量
}

type BatchMessageRecallProgressBuilder struct {
	recall          bool // 该条批量消息是否被执行过撤回操作
	recallFlag      bool
	recallCount     int // 已经成功撤回的消息数量
	recallCountFlag bool
}

func NewBatchMessageRecallProgressBuilder() *BatchMessageRecallProgressBuilder {
	builder := &BatchMessageRecallProgressBuilder{}
	return builder
}

// 该条批量消息是否被执行过撤回操作
//
// 示例值：true
func (builder *BatchMessageRecallProgressBuilder) Recall(recall bool) *BatchMessageRecallProgressBuilder {
	builder.recall = recall
	builder.recallFlag = true
	return builder
}

// 已经成功撤回的消息数量
//
// 示例值：100
func (builder *BatchMessageRecallProgressBuilder) RecallCount(recallCount int) *BatchMessageRecallProgressBuilder {
	builder.recallCount = recallCount
	builder.recallCountFlag = true
	return builder
}

func (builder *BatchMessageRecallProgressBuilder) Build() *BatchMessageRecallProgress {
	req := &BatchMessageRecallProgress{}
	if builder.recallFlag {
		req.Recall = &builder.recall

	}
	if builder.recallCountFlag {
		req.RecallCount = &builder.recallCount

	}
	return req
}

type BatchMessageSendProgress struct {
	ValidUserIdsCount   *int `json:"valid_user_ids_count,omitempty"`   // 批量请求中有效的userid数量(包含机器人不可见用户);;;;**注意**： ;当valid_user_ids_count为0有两种情况：;* 批量任务还没有开始被调度（请等待一会再调用该接口）;* 批量发送消息时传入的所有openIDs、employeID、departmentiIDs都不包含有效的用户
	SuccessUserIdsCount *int `json:"success_user_ids_count,omitempty"` // 已经成功给用户发送成功的消息数量;;;;**注意**：最终success_user_ids_count不一定等于valid_user_ids_count, 因为valid_user_ids_count包含了对机器人不可见的用户
	ReadUserIdsCount    *int `json:"read_user_ids_count,omitempty"`    // 已读信息用户数量
}

type BatchMessageSendProgressBuilder struct {
	validUserIdsCount       int // 批量请求中有效的userid数量(包含机器人不可见用户);;;;**注意**： ;当valid_user_ids_count为0有两种情况：;* 批量任务还没有开始被调度（请等待一会再调用该接口）;* 批量发送消息时传入的所有openIDs、employeID、departmentiIDs都不包含有效的用户
	validUserIdsCountFlag   bool
	successUserIdsCount     int // 已经成功给用户发送成功的消息数量;;;;**注意**：最终success_user_ids_count不一定等于valid_user_ids_count, 因为valid_user_ids_count包含了对机器人不可见的用户
	successUserIdsCountFlag bool
	readUserIdsCount        int // 已读信息用户数量
	readUserIdsCountFlag    bool
}

func NewBatchMessageSendProgressBuilder() *BatchMessageSendProgressBuilder {
	builder := &BatchMessageSendProgressBuilder{}
	return builder
}

// 批量请求中有效的userid数量(包含机器人不可见用户);;;;**注意**： ;当valid_user_ids_count为0有两种情况：;* 批量任务还没有开始被调度（请等待一会再调用该接口）;* 批量发送消息时传入的所有openIDs、employeID、departmentiIDs都不包含有效的用户
//
// 示例值：204
func (builder *BatchMessageSendProgressBuilder) ValidUserIdsCount(validUserIdsCount int) *BatchMessageSendProgressBuilder {
	builder.validUserIdsCount = validUserIdsCount
	builder.validUserIdsCountFlag = true
	return builder
}

// 已经成功给用户发送成功的消息数量;;;;**注意**：最终success_user_ids_count不一定等于valid_user_ids_count, 因为valid_user_ids_count包含了对机器人不可见的用户
//
// 示例值：200
func (builder *BatchMessageSendProgressBuilder) SuccessUserIdsCount(successUserIdsCount int) *BatchMessageSendProgressBuilder {
	builder.successUserIdsCount = successUserIdsCount
	builder.successUserIdsCountFlag = true
	return builder
}

// 已读信息用户数量
//
// 示例值：150
func (builder *BatchMessageSendProgressBuilder) ReadUserIdsCount(readUserIdsCount int) *BatchMessageSendProgressBuilder {
	builder.readUserIdsCount = readUserIdsCount
	builder.readUserIdsCountFlag = true
	return builder
}

func (builder *BatchMessageSendProgressBuilder) Build() *BatchMessageSendProgress {
	req := &BatchMessageSendProgress{}
	if builder.validUserIdsCountFlag {
		req.ValidUserIdsCount = &builder.validUserIdsCount

	}
	if builder.successUserIdsCountFlag {
		req.SuccessUserIdsCount = &builder.successUserIdsCount

	}
	if builder.readUserIdsCountFlag {
		req.ReadUserIdsCount = &builder.readUserIdsCount

	}
	return req
}

type BatchRecallProgress struct {
	RecallCount      *string `json:"recall_count,omitempty"`       // 撤回成功的消息条数
	TotalRecallCount *string `json:"total_recall_count,omitempty"` // 计划撤回的消息条数
}

type BatchRecallProgressBuilder struct {
	recallCount          string // 撤回成功的消息条数
	recallCountFlag      bool
	totalRecallCount     string // 计划撤回的消息条数
	totalRecallCountFlag bool
}

func NewBatchRecallProgressBuilder() *BatchRecallProgressBuilder {
	builder := &BatchRecallProgressBuilder{}
	return builder
}

// 撤回成功的消息条数
//
// 示例值：
func (builder *BatchRecallProgressBuilder) RecallCount(recallCount string) *BatchRecallProgressBuilder {
	builder.recallCount = recallCount
	builder.recallCountFlag = true
	return builder
}

// 计划撤回的消息条数
//
// 示例值：
func (builder *BatchRecallProgressBuilder) TotalRecallCount(totalRecallCount string) *BatchRecallProgressBuilder {
	builder.totalRecallCount = totalRecallCount
	builder.totalRecallCountFlag = true
	return builder
}

func (builder *BatchRecallProgressBuilder) Build() *BatchRecallProgress {
	req := &BatchRecallProgress{}
	if builder.recallCountFlag {
		req.RecallCount = &builder.recallCount

	}
	if builder.totalRecallCountFlag {
		req.TotalRecallCount = &builder.totalRecallCount

	}
	return req
}

type BatchSendProgress struct {
	SendCount      *string `json:"send_count,omitempty"`       // 发送成功的消息条数
	TotalSendCount *string `json:"total_send_count,omitempty"` // 总的计划发送的消息条数
}

type BatchSendProgressBuilder struct {
	sendCount          string // 发送成功的消息条数
	sendCountFlag      bool
	totalSendCount     string // 总的计划发送的消息条数
	totalSendCountFlag bool
}

func NewBatchSendProgressBuilder() *BatchSendProgressBuilder {
	builder := &BatchSendProgressBuilder{}
	return builder
}

// 发送成功的消息条数
//
// 示例值：
func (builder *BatchSendProgressBuilder) SendCount(sendCount string) *BatchSendProgressBuilder {
	builder.sendCount = sendCount
	builder.sendCountFlag = true
	return builder
}

// 总的计划发送的消息条数
//
// 示例值：
func (builder *BatchSendProgressBuilder) TotalSendCount(totalSendCount string) *BatchSendProgressBuilder {
	builder.totalSendCount = totalSendCount
	builder.totalSendCountFlag = true
	return builder
}

func (builder *BatchSendProgressBuilder) Build() *BatchSendProgress {
	req := &BatchSendProgress{}
	if builder.sendCountFlag {
		req.SendCount = &builder.sendCount

	}
	if builder.totalSendCountFlag {
		req.TotalSendCount = &builder.totalSendCount

	}
	return req
}

type Chat struct {
	ChatId         *string    `json:"chat_id,omitempty"`           // chat_id of the conversation
	Avatar         *string    `json:"avatar,omitempty"`            // avatar of the conversation
	Name           *string    `json:"name,omitempty"`              // name of the conversation
	Description    *string    `json:"description,omitempty"`       // description of the conversation
	I18nNames      *I18nNames `json:"i18n_names,omitempty"`        // i18_names of the conversation
	OnlyOwnerAdd   *bool      `json:"only_owner_add,omitempty"`    // whether only the owner of the converation can add others
	ShareAllowed   *bool      `json:"share_allowed,omitempty"`     // whether the converation can be shared
	OnlyOwnerAtAll *bool      `json:"only_owner_at_all,omitempty"` // whether only the owner of the converation can @all
	OnlyOwnerEdit  *bool      `json:"only_owner_edit,omitempty"`   // whether only the owner of the converation can edit the converation info
	OwnerUserId    *string    `json:"owner_user_id,omitempty"`     // owner_id
	Type           *string    `json:"type,omitempty"`              // type

}

type ChatBuilder struct {
	chatId             string // chat_id of the conversation
	chatIdFlag         bool
	avatar             string // avatar of the conversation
	avatarFlag         bool
	name               string // name of the conversation
	nameFlag           bool
	description        string // description of the conversation
	descriptionFlag    bool
	i18nNames          *I18nNames // i18_names of the conversation
	i18nNamesFlag      bool
	onlyOwnerAdd       bool // whether only the owner of the converation can add others
	onlyOwnerAddFlag   bool
	shareAllowed       bool // whether the converation can be shared
	shareAllowedFlag   bool
	onlyOwnerAtAll     bool // whether only the owner of the converation can @all
	onlyOwnerAtAllFlag bool
	onlyOwnerEdit      bool // whether only the owner of the converation can edit the converation info
	onlyOwnerEditFlag  bool
	ownerUserId        string // owner_id
	ownerUserIdFlag    bool
	type_              string // type
	typeFlag           bool
}

func NewChatBuilder() *ChatBuilder {
	builder := &ChatBuilder{}
	return builder
}

// chat_id of the conversation
//
// 示例值：
func (builder *ChatBuilder) ChatId(chatId string) *ChatBuilder {
	builder.chatId = chatId
	builder.chatIdFlag = true
	return builder
}

// avatar of the conversation
//
// 示例值：
func (builder *ChatBuilder) Avatar(avatar string) *ChatBuilder {
	builder.avatar = avatar
	builder.avatarFlag = true
	return builder
}

// name of the conversation
//
// 示例值：
func (builder *ChatBuilder) Name(name string) *ChatBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

// description of the conversation
//
// 示例值：
func (builder *ChatBuilder) Description(description string) *ChatBuilder {
	builder.description = description
	builder.descriptionFlag = true
	return builder
}

// i18_names of the conversation
//
// 示例值：
func (builder *ChatBuilder) I18nNames(i18nNames *I18nNames) *ChatBuilder {
	builder.i18nNames = i18nNames
	builder.i18nNamesFlag = true
	return builder
}

// whether only the owner of the converation can add others
//
// 示例值：
func (builder *ChatBuilder) OnlyOwnerAdd(onlyOwnerAdd bool) *ChatBuilder {
	builder.onlyOwnerAdd = onlyOwnerAdd
	builder.onlyOwnerAddFlag = true
	return builder
}

// whether the converation can be shared
//
// 示例值：
func (builder *ChatBuilder) ShareAllowed(shareAllowed bool) *ChatBuilder {
	builder.shareAllowed = shareAllowed
	builder.shareAllowedFlag = true
	return builder
}

// whether only the owner of the converation can @all
//
// 示例值：
func (builder *ChatBuilder) OnlyOwnerAtAll(onlyOwnerAtAll bool) *ChatBuilder {
	builder.onlyOwnerAtAll = onlyOwnerAtAll
	builder.onlyOwnerAtAllFlag = true
	return builder
}

// whether only the owner of the converation can edit the converation info
//
// 示例值：
func (builder *ChatBuilder) OnlyOwnerEdit(onlyOwnerEdit bool) *ChatBuilder {
	builder.onlyOwnerEdit = onlyOwnerEdit
	builder.onlyOwnerEditFlag = true
	return builder
}

// owner_id
//
// 示例值：
func (builder *ChatBuilder) OwnerUserId(ownerUserId string) *ChatBuilder {
	builder.ownerUserId = ownerUserId
	builder.ownerUserIdFlag = true
	return builder
}

// type
//
// 示例值：
func (builder *ChatBuilder) Type(type_ string) *ChatBuilder {
	builder.type_ = type_
	builder.typeFlag = true
	return builder
}

func (builder *ChatBuilder) Build() *Chat {
	req := &Chat{}
	if builder.chatIdFlag {
		req.ChatId = &builder.chatId

	}
	if builder.avatarFlag {
		req.Avatar = &builder.avatar

	}
	if builder.nameFlag {
		req.Name = &builder.name

	}
	if builder.descriptionFlag {
		req.Description = &builder.description

	}
	if builder.i18nNamesFlag {
		req.I18nNames = builder.i18nNames
	}
	if builder.onlyOwnerAddFlag {
		req.OnlyOwnerAdd = &builder.onlyOwnerAdd

	}
	if builder.shareAllowedFlag {
		req.ShareAllowed = &builder.shareAllowed

	}
	if builder.onlyOwnerAtAllFlag {
		req.OnlyOwnerAtAll = &builder.onlyOwnerAtAll

	}
	if builder.onlyOwnerEditFlag {
		req.OnlyOwnerEdit = &builder.onlyOwnerEdit

	}
	if builder.ownerUserIdFlag {
		req.OwnerUserId = &builder.ownerUserId

	}
	if builder.typeFlag {
		req.Type = &builder.type_

	}

	return req
}

type ChatAnnouncement struct {
}

type ChatManagers struct {
	ManagerId *string `json:"manager_id,omitempty"` // 群管理员ID
}

type ChatManagersBuilder struct {
	managerId     string // 群管理员ID
	managerIdFlag bool
}

func NewChatManagersBuilder() *ChatManagersBuilder {
	builder := &ChatManagersBuilder{}
	return builder
}

// 群管理员ID
//
// 示例值：
func (builder *ChatManagersBuilder) ManagerId(managerId string) *ChatManagersBuilder {
	builder.managerId = managerId
	builder.managerIdFlag = true
	return builder
}

func (builder *ChatManagersBuilder) Build() *ChatManagers {
	req := &ChatManagers{}
	if builder.managerIdFlag {
		req.ManagerId = &builder.managerId

	}
	return req
}

type ChatMember struct {
	UserId *string `json:"user_id,omitempty"` // user_id
}

type ChatMemberBuilder struct {
	userId     string // user_id
	userIdFlag bool
}

func NewChatMemberBuilder() *ChatMemberBuilder {
	builder := &ChatMemberBuilder{}
	return builder
}

// user_id
//
// 示例值：
func (builder *ChatMemberBuilder) UserId(userId string) *ChatMemberBuilder {
	builder.userId = userId
	builder.userIdFlag = true
	return builder
}

func (builder *ChatMemberBuilder) Build() *ChatMember {
	req := &ChatMember{}
	if builder.userIdFlag {
		req.UserId = &builder.userId

	}
	return req
}

type ChatMemberBot struct {
	BotId *string `json:"bot_id,omitempty"` // bot_id
}

type ChatMemberBotBuilder struct {
	botId     string // bot_id
	botIdFlag bool
}

func NewChatMemberBotBuilder() *ChatMemberBotBuilder {
	builder := &ChatMemberBotBuilder{}
	return builder
}

// bot_id
//
// 示例值：
func (builder *ChatMemberBotBuilder) BotId(botId string) *ChatMemberBotBuilder {
	builder.botId = botId
	builder.botIdFlag = true
	return builder
}

func (builder *ChatMemberBotBuilder) Build() *ChatMemberBot {
	req := &ChatMemberBot{}
	if builder.botIdFlag {
		req.BotId = &builder.botId

	}
	return req
}

type ChatMemberUser struct {
	Name      *string `json:"name,omitempty"`       // 用户名字
	TenantKey *string `json:"tenant_key,omitempty"` // 租户Key，为租户在飞书上的唯一标识，用来换取对应的tenant_access_token，也可以用作租户在应用中的唯一标识
	UserId    *UserId `json:"user_id,omitempty"`    // 用户 ID
}

type ChatMemberUserBuilder struct {
	name          string // 用户名字
	nameFlag      bool
	tenantKey     string // 租户Key，为租户在飞书上的唯一标识，用来换取对应的tenant_access_token，也可以用作租户在应用中的唯一标识
	tenantKeyFlag bool
	userId        *UserId // 用户 ID
	userIdFlag    bool
}

func NewChatMemberUserBuilder() *ChatMemberUserBuilder {
	builder := &ChatMemberUserBuilder{}
	return builder
}

// 用户名字
//
// 示例值：user name
func (builder *ChatMemberUserBuilder) Name(name string) *ChatMemberUserBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

// 租户Key，为租户在飞书上的唯一标识，用来换取对应的tenant_access_token，也可以用作租户在应用中的唯一标识
//
// 示例值：3774yuu3743
func (builder *ChatMemberUserBuilder) TenantKey(tenantKey string) *ChatMemberUserBuilder {
	builder.tenantKey = tenantKey
	builder.tenantKeyFlag = true
	return builder
}

// 用户 ID
//
// 示例值：
func (builder *ChatMemberUserBuilder) UserId(userId *UserId) *ChatMemberUserBuilder {
	builder.userId = userId
	builder.userIdFlag = true
	return builder
}

func (builder *ChatMemberUserBuilder) Build() *ChatMemberUser {
	req := &ChatMemberUser{}
	if builder.nameFlag {
		req.Name = &builder.name

	}
	if builder.tenantKeyFlag {
		req.TenantKey = &builder.tenantKey

	}
	if builder.userIdFlag {
		req.UserId = builder.userId
	}
	return req
}

type ChatMembers struct {
	UserId *string `json:"user_id,omitempty"` // user_id
}

type ChatMembersBuilder struct {
	userId     string // user_id
	userIdFlag bool
}

func NewChatMembersBuilder() *ChatMembersBuilder {
	builder := &ChatMembersBuilder{}
	return builder
}

// user_id
//
// 示例值：
func (builder *ChatMembersBuilder) UserId(userId string) *ChatMembersBuilder {
	builder.userId = userId
	builder.userIdFlag = true
	return builder
}

func (builder *ChatMembersBuilder) Build() *ChatMembers {
	req := &ChatMembers{}
	if builder.userIdFlag {
		req.UserId = &builder.userId

	}
	return req
}

type ChatMenuTree struct {
	ChatMenuTopLevels []*ChatMenuTopLevel `json:"chat_menu_top_levels,omitempty"` // 一级菜单列表
}

type ChatMenuTreeBuilder struct {
	chatMenuTopLevels     []*ChatMenuTopLevel // 一级菜单列表
	chatMenuTopLevelsFlag bool
}

func NewChatMenuTreeBuilder() *ChatMenuTreeBuilder {
	builder := &ChatMenuTreeBuilder{}
	return builder
}

// 一级菜单列表
//
// 示例值：
func (builder *ChatMenuTreeBuilder) ChatMenuTopLevels(chatMenuTopLevels []*ChatMenuTopLevel) *ChatMenuTreeBuilder {
	builder.chatMenuTopLevels = chatMenuTopLevels
	builder.chatMenuTopLevelsFlag = true
	return builder
}

func (builder *ChatMenuTreeBuilder) Build() *ChatMenuTree {
	req := &ChatMenuTree{}
	if builder.chatMenuTopLevelsFlag {
		req.ChatMenuTopLevels = builder.chatMenuTopLevels
	}
	return req
}

type ChatTab struct {
	TabId      *string         `json:"tab_id,omitempty"`      // Tab ID
	TabName    *string         `json:"tab_name,omitempty"`    // Tab名称;;**注意**：会话标签页的名称不能超过30个字符
	TabType    *string         `json:"tab_type,omitempty"`    // Tab类型
	TabContent *ChatTabContent `json:"tab_content,omitempty"` // Tab内容
	TabConfig  *ChatTabConfig  `json:"tab_config,omitempty"`  // Tab的配置
}

type ChatTabBuilder struct {
	tabId          string // Tab ID
	tabIdFlag      bool
	tabName        string // Tab名称;;**注意**：会话标签页的名称不能超过30个字符
	tabNameFlag    bool
	tabType        string // Tab类型
	tabTypeFlag    bool
	tabContent     *ChatTabContent // Tab内容
	tabContentFlag bool
	tabConfig      *ChatTabConfig // Tab的配置
	tabConfigFlag  bool
}

func NewChatTabBuilder() *ChatTabBuilder {
	builder := &ChatTabBuilder{}
	return builder
}

// Tab ID
//
// 示例值：7101214603622940671
func (builder *ChatTabBuilder) TabId(tabId string) *ChatTabBuilder {
	builder.tabId = tabId
	builder.tabIdFlag = true
	return builder
}

// Tab名称;;**注意**：会话标签页的名称不能超过30个字符
//
// 示例值：文档
func (builder *ChatTabBuilder) TabName(tabName string) *ChatTabBuilder {
	builder.tabName = tabName
	builder.tabNameFlag = true
	return builder
}

// Tab类型
//
// 示例值：doc
func (builder *ChatTabBuilder) TabType(tabType string) *ChatTabBuilder {
	builder.tabType = tabType
	builder.tabTypeFlag = true
	return builder
}

// Tab内容
//
// 示例值：
func (builder *ChatTabBuilder) TabContent(tabContent *ChatTabContent) *ChatTabBuilder {
	builder.tabContent = tabContent
	builder.tabContentFlag = true
	return builder
}

// Tab的配置
//
// 示例值：
func (builder *ChatTabBuilder) TabConfig(tabConfig *ChatTabConfig) *ChatTabBuilder {
	builder.tabConfig = tabConfig
	builder.tabConfigFlag = true
	return builder
}

func (builder *ChatTabBuilder) Build() *ChatTab {
	req := &ChatTab{}
	if builder.tabIdFlag {
		req.TabId = &builder.tabId

	}
	if builder.tabNameFlag {
		req.TabName = &builder.tabName

	}
	if builder.tabTypeFlag {
		req.TabType = &builder.tabType

	}
	if builder.tabContentFlag {
		req.TabContent = builder.tabContent
	}
	if builder.tabConfigFlag {
		req.TabConfig = builder.tabConfig
	}
	return req
}

type ChatTopNotice struct {
	ActionType *string `json:"action_type,omitempty"` // 置顶的类型;;**注意**：;- 选择 ==消息类型== 时必须填写`message_id`字段;- 选择 ==群公告类型== 时填写的`message_id`将被忽略
	MessageId  *string `json:"message_id,omitempty"`  // 消息ID，详情参见[消息ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/message/intro#ac79c1c2)
}

type ChatTopNoticeBuilder struct {
	actionType     string // 置顶的类型;;**注意**：;- 选择 ==消息类型== 时必须填写`message_id`字段;- 选择 ==群公告类型== 时填写的`message_id`将被忽略
	actionTypeFlag bool
	messageId      string // 消息ID，详情参见[消息ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/message/intro#ac79c1c2)
	messageIdFlag  bool
}

func NewChatTopNoticeBuilder() *ChatTopNoticeBuilder {
	builder := &ChatTopNoticeBuilder{}
	return builder
}

// 置顶的类型;;**注意**：;- 选择 ==消息类型== 时必须填写`message_id`字段;- 选择 ==群公告类型== 时填写的`message_id`将被忽略
//
// 示例值：2
func (builder *ChatTopNoticeBuilder) ActionType(actionType string) *ChatTopNoticeBuilder {
	builder.actionType = actionType
	builder.actionTypeFlag = true
	return builder
}

// 消息ID，详情参见[消息ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/message/intro#ac79c1c2)
//
// 示例值：om_dc13264520392913993dd051dba21dcf
func (builder *ChatTopNoticeBuilder) MessageId(messageId string) *ChatTopNoticeBuilder {
	builder.messageId = messageId
	builder.messageIdFlag = true
	return builder
}

func (builder *ChatTopNoticeBuilder) Build() *ChatTopNotice {
	req := &ChatTopNotice{}
	if builder.actionTypeFlag {
		req.ActionType = &builder.actionType

	}
	if builder.messageIdFlag {
		req.MessageId = &builder.messageId

	}
	return req
}

type ChatChange struct {
	Avatar                 *string    `json:"avatar,omitempty"`                   // 群头像
	Name                   *string    `json:"name,omitempty"`                     // 群名称
	Description            *string    `json:"description,omitempty"`              // 群描述
	I18nNames              *I18nNames `json:"i18n_names,omitempty"`               // 群国际化名称
	AddMemberPermission    *string    `json:"add_member_permission,omitempty"`    // 加人入群权限(all_members/only_owner/unknown)
	ShareCardPermission    *string    `json:"share_card_permission,omitempty"`    // 群分享权限(allowed/not_allowed/unknown)
	AtAllPermission        *string    `json:"at_all_permission,omitempty"`        // at 所有人权限(all_members/only_owner/unknown)
	EditPermission         *string    `json:"edit_permission,omitempty"`          // 群编辑权限(all_members/only_owner/unknown)
	MembershipApproval     *string    `json:"membership_approval,omitempty"`      // 加群审批(no_approval_required/approval_required)
	JoinMessageVisibility  *string    `json:"join_message_visibility,omitempty"`  // 入群消息可见性(only_owner/all_members/not_anyone)
	LeaveMessageVisibility *string    `json:"leave_message_visibility,omitempty"` // 出群消息可见性(only_owner/all_members/not_anyone)
	ModerationPermission   *string    `json:"moderation_permission,omitempty"`    // 发言权限(all_members/only_owner)
	OwnerId                *UserId    `json:"owner_id,omitempty"`                 // 用户 ID

}

type ChatChangeBuilder struct {
	avatar                     string // 群头像
	avatarFlag                 bool
	name                       string // 群名称
	nameFlag                   bool
	description                string // 群描述
	descriptionFlag            bool
	i18nNames                  *I18nNames // 群国际化名称
	i18nNamesFlag              bool
	addMemberPermission        string // 加人入群权限(all_members/only_owner/unknown)
	addMemberPermissionFlag    bool
	shareCardPermission        string // 群分享权限(allowed/not_allowed/unknown)
	shareCardPermissionFlag    bool
	atAllPermission            string // at 所有人权限(all_members/only_owner/unknown)
	atAllPermissionFlag        bool
	editPermission             string // 群编辑权限(all_members/only_owner/unknown)
	editPermissionFlag         bool
	membershipApproval         string // 加群审批(no_approval_required/approval_required)
	membershipApprovalFlag     bool
	joinMessageVisibility      string // 入群消息可见性(only_owner/all_members/not_anyone)
	joinMessageVisibilityFlag  bool
	leaveMessageVisibility     string // 出群消息可见性(only_owner/all_members/not_anyone)
	leaveMessageVisibilityFlag bool
	moderationPermission       string // 发言权限(all_members/only_owner)
	moderationPermissionFlag   bool
	ownerId                    *UserId // 用户 ID
	ownerIdFlag                bool
}

func NewChatChangeBuilder() *ChatChangeBuilder {
	builder := &ChatChangeBuilder{}
	return builder
}

// 群头像
//
// 示例值：default-avatar_0cda3662-875a-4354-94d2-83e7393c7123
func (builder *ChatChangeBuilder) Avatar(avatar string) *ChatChangeBuilder {
	builder.avatar = avatar
	builder.avatarFlag = true
	return builder
}

// 群名称
//
// 示例值：群名称测试
func (builder *ChatChangeBuilder) Name(name string) *ChatChangeBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

// 群描述
//
// 示例值：群描述测试
func (builder *ChatChangeBuilder) Description(description string) *ChatChangeBuilder {
	builder.description = description
	builder.descriptionFlag = true
	return builder
}

// 群国际化名称
//
// 示例值：
func (builder *ChatChangeBuilder) I18nNames(i18nNames *I18nNames) *ChatChangeBuilder {
	builder.i18nNames = i18nNames
	builder.i18nNamesFlag = true
	return builder
}

// 加人入群权限(all_members/only_owner/unknown)
//
// 示例值：all_members
func (builder *ChatChangeBuilder) AddMemberPermission(addMemberPermission string) *ChatChangeBuilder {
	builder.addMemberPermission = addMemberPermission
	builder.addMemberPermissionFlag = true
	return builder
}

// 群分享权限(allowed/not_allowed/unknown)
//
// 示例值：allowed
func (builder *ChatChangeBuilder) ShareCardPermission(shareCardPermission string) *ChatChangeBuilder {
	builder.shareCardPermission = shareCardPermission
	builder.shareCardPermissionFlag = true
	return builder
}

// at 所有人权限(all_members/only_owner/unknown)
//
// 示例值：only_owner
func (builder *ChatChangeBuilder) AtAllPermission(atAllPermission string) *ChatChangeBuilder {
	builder.atAllPermission = atAllPermission
	builder.atAllPermissionFlag = true
	return builder
}

// 群编辑权限(all_members/only_owner/unknown)
//
// 示例值：all_members
func (builder *ChatChangeBuilder) EditPermission(editPermission string) *ChatChangeBuilder {
	builder.editPermission = editPermission
	builder.editPermissionFlag = true
	return builder
}

// 加群审批(no_approval_required/approval_required)
//
// 示例值：approval_required
func (builder *ChatChangeBuilder) MembershipApproval(membershipApproval string) *ChatChangeBuilder {
	builder.membershipApproval = membershipApproval
	builder.membershipApprovalFlag = true
	return builder
}

// 入群消息可见性(only_owner/all_members/not_anyone)
//
// 示例值：all_members
func (builder *ChatChangeBuilder) JoinMessageVisibility(joinMessageVisibility string) *ChatChangeBuilder {
	builder.joinMessageVisibility = joinMessageVisibility
	builder.joinMessageVisibilityFlag = true
	return builder
}

// 出群消息可见性(only_owner/all_members/not_anyone)
//
// 示例值：all_members
func (builder *ChatChangeBuilder) LeaveMessageVisibility(leaveMessageVisibility string) *ChatChangeBuilder {
	builder.leaveMessageVisibility = leaveMessageVisibility
	builder.leaveMessageVisibilityFlag = true
	return builder
}

// 发言权限(all_members/only_owner)
//
// 示例值：all_members
func (builder *ChatChangeBuilder) ModerationPermission(moderationPermission string) *ChatChangeBuilder {
	builder.moderationPermission = moderationPermission
	builder.moderationPermissionFlag = true
	return builder
}

// 用户 ID
//
// 示例值：
func (builder *ChatChangeBuilder) OwnerId(ownerId *UserId) *ChatChangeBuilder {
	builder.ownerId = ownerId
	builder.ownerIdFlag = true
	return builder
}

func (builder *ChatChangeBuilder) Build() *ChatChange {
	req := &ChatChange{}
	if builder.avatarFlag {
		req.Avatar = &builder.avatar

	}
	if builder.nameFlag {
		req.Name = &builder.name

	}
	if builder.descriptionFlag {
		req.Description = &builder.description

	}
	if builder.i18nNamesFlag {
		req.I18nNames = builder.i18nNames
	}
	if builder.addMemberPermissionFlag {
		req.AddMemberPermission = &builder.addMemberPermission

	}
	if builder.shareCardPermissionFlag {
		req.ShareCardPermission = &builder.shareCardPermission

	}
	if builder.atAllPermissionFlag {
		req.AtAllPermission = &builder.atAllPermission

	}
	if builder.editPermissionFlag {
		req.EditPermission = &builder.editPermission

	}
	if builder.membershipApprovalFlag {
		req.MembershipApproval = &builder.membershipApproval

	}
	if builder.joinMessageVisibilityFlag {
		req.JoinMessageVisibility = &builder.joinMessageVisibility

	}
	if builder.leaveMessageVisibilityFlag {
		req.LeaveMessageVisibility = &builder.leaveMessageVisibility

	}
	if builder.moderationPermissionFlag {
		req.ModerationPermission = &builder.moderationPermission

	}
	if builder.ownerIdFlag {
		req.OwnerId = builder.ownerId
	}

	return req
}

type ChatMenuItem struct {
	ActionType   *string                   `json:"action_type,omitempty"`   // 菜单类型
	RedirectLink *ChatMenuItemRedirectLink `json:"redirect_link,omitempty"` // 跳转链接
	ImageKey     *string                   `json:"image_key,omitempty"`     // image_key
	Name         *string                   `json:"name,omitempty"`          // 名称
	I18nNames    *I18nNames                `json:"i18n_names,omitempty"`    // 国际化名称
}

type ChatMenuItemBuilder struct {
	actionType       string // 菜单类型
	actionTypeFlag   bool
	redirectLink     *ChatMenuItemRedirectLink // 跳转链接
	redirectLinkFlag bool
	imageKey         string // image_key
	imageKeyFlag     bool
	name             string // 名称
	nameFlag         bool
	i18nNames        *I18nNames // 国际化名称
	i18nNamesFlag    bool
}

func NewChatMenuItemBuilder() *ChatMenuItemBuilder {
	builder := &ChatMenuItemBuilder{}
	return builder
}

// 菜单类型
//
// 示例值：NONE
func (builder *ChatMenuItemBuilder) ActionType(actionType string) *ChatMenuItemBuilder {
	builder.actionType = actionType
	builder.actionTypeFlag = true
	return builder
}

// 跳转链接
//
// 示例值：
func (builder *ChatMenuItemBuilder) RedirectLink(redirectLink *ChatMenuItemRedirectLink) *ChatMenuItemBuilder {
	builder.redirectLink = redirectLink
	builder.redirectLinkFlag = true
	return builder
}

// image_key
//
// 示例值：img_v2_b0fbe905-7988-4282-b882-82edd010336j
func (builder *ChatMenuItemBuilder) ImageKey(imageKey string) *ChatMenuItemBuilder {
	builder.imageKey = imageKey
	builder.imageKeyFlag = true
	return builder
}

// 名称
//
// 示例值：评审报名
func (builder *ChatMenuItemBuilder) Name(name string) *ChatMenuItemBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

// 国际化名称
//
// 示例值：
func (builder *ChatMenuItemBuilder) I18nNames(i18nNames *I18nNames) *ChatMenuItemBuilder {
	builder.i18nNames = i18nNames
	builder.i18nNamesFlag = true
	return builder
}

func (builder *ChatMenuItemBuilder) Build() *ChatMenuItem {
	req := &ChatMenuItem{}
	if builder.actionTypeFlag {
		req.ActionType = &builder.actionType

	}
	if builder.redirectLinkFlag {
		req.RedirectLink = builder.redirectLink
	}
	if builder.imageKeyFlag {
		req.ImageKey = &builder.imageKey

	}
	if builder.nameFlag {
		req.Name = &builder.name

	}
	if builder.i18nNamesFlag {
		req.I18nNames = builder.i18nNames
	}
	return req
}

type ChatMenuItemRedirectLink struct {
	CommonUrl  *string `json:"common_url,omitempty"`  //
	IosUrl     *string `json:"ios_url,omitempty"`     //
	AndroidUrl *string `json:"android_url,omitempty"` //
	PcUrl      *string `json:"pc_url,omitempty"`      //
	WebUrl     *string `json:"web_url,omitempty"`     //
}

type ChatMenuItemRedirectLinkBuilder struct {
	commonUrl      string //
	commonUrlFlag  bool
	iosUrl         string //
	iosUrlFlag     bool
	androidUrl     string //
	androidUrlFlag bool
	pcUrl          string //
	pcUrlFlag      bool
	webUrl         string //
	webUrlFlag     bool
}

func NewChatMenuItemRedirectLinkBuilder() *ChatMenuItemRedirectLinkBuilder {
	builder := &ChatMenuItemRedirectLinkBuilder{}
	return builder
}

//
//
// 示例值：https://open.feishu.cn/
func (builder *ChatMenuItemRedirectLinkBuilder) CommonUrl(commonUrl string) *ChatMenuItemRedirectLinkBuilder {
	builder.commonUrl = commonUrl
	builder.commonUrlFlag = true
	return builder
}

//
//
// 示例值：https://open.feishu.cn/
func (builder *ChatMenuItemRedirectLinkBuilder) IosUrl(iosUrl string) *ChatMenuItemRedirectLinkBuilder {
	builder.iosUrl = iosUrl
	builder.iosUrlFlag = true
	return builder
}

//
//
// 示例值：https://open.feishu.cn/
func (builder *ChatMenuItemRedirectLinkBuilder) AndroidUrl(androidUrl string) *ChatMenuItemRedirectLinkBuilder {
	builder.androidUrl = androidUrl
	builder.androidUrlFlag = true
	return builder
}

//
//
// 示例值：https://open.feishu.cn/
func (builder *ChatMenuItemRedirectLinkBuilder) PcUrl(pcUrl string) *ChatMenuItemRedirectLinkBuilder {
	builder.pcUrl = pcUrl
	builder.pcUrlFlag = true
	return builder
}

//
//
// 示例值：https://open.feishu.cn/
func (builder *ChatMenuItemRedirectLinkBuilder) WebUrl(webUrl string) *ChatMenuItemRedirectLinkBuilder {
	builder.webUrl = webUrl
	builder.webUrlFlag = true
	return builder
}

func (builder *ChatMenuItemRedirectLinkBuilder) Build() *ChatMenuItemRedirectLink {
	req := &ChatMenuItemRedirectLink{}
	if builder.commonUrlFlag {
		req.CommonUrl = &builder.commonUrl

	}
	if builder.iosUrlFlag {
		req.IosUrl = &builder.iosUrl

	}
	if builder.androidUrlFlag {
		req.AndroidUrl = &builder.androidUrl

	}
	if builder.pcUrlFlag {
		req.PcUrl = &builder.pcUrl

	}
	if builder.webUrlFlag {
		req.WebUrl = &builder.webUrl

	}
	return req
}

type ChatMenuSecondLevel struct {
	ChatMenuSecondLevelId *string       `json:"chat_menu_second_level_id,omitempty"` // 二级菜单ID
	ChatMenuItem          *ChatMenuItem `json:"chat_menu_item,omitempty"`            // 二级菜单信息
}

type ChatMenuSecondLevelBuilder struct {
	chatMenuSecondLevelId     string // 二级菜单ID
	chatMenuSecondLevelIdFlag bool
	chatMenuItem              *ChatMenuItem // 二级菜单信息
	chatMenuItemFlag          bool
}

func NewChatMenuSecondLevelBuilder() *ChatMenuSecondLevelBuilder {
	builder := &ChatMenuSecondLevelBuilder{}
	return builder
}

// 二级菜单ID
//
// 示例值：7039638308221468675
func (builder *ChatMenuSecondLevelBuilder) ChatMenuSecondLevelId(chatMenuSecondLevelId string) *ChatMenuSecondLevelBuilder {
	builder.chatMenuSecondLevelId = chatMenuSecondLevelId
	builder.chatMenuSecondLevelIdFlag = true
	return builder
}

// 二级菜单信息
//
// 示例值：
func (builder *ChatMenuSecondLevelBuilder) ChatMenuItem(chatMenuItem *ChatMenuItem) *ChatMenuSecondLevelBuilder {
	builder.chatMenuItem = chatMenuItem
	builder.chatMenuItemFlag = true
	return builder
}

func (builder *ChatMenuSecondLevelBuilder) Build() *ChatMenuSecondLevel {
	req := &ChatMenuSecondLevel{}
	if builder.chatMenuSecondLevelIdFlag {
		req.ChatMenuSecondLevelId = &builder.chatMenuSecondLevelId

	}
	if builder.chatMenuItemFlag {
		req.ChatMenuItem = builder.chatMenuItem
	}
	return req
}

type ChatMenuTopLevel struct {
	ChatMenuTopLevelId *string                `json:"chat_menu_top_level_id,omitempty"` // 一级菜单ID
	ChatMenuItem       *ChatMenuItem          `json:"chat_menu_item,omitempty"`         // 一级菜单信息
	Children           []*ChatMenuSecondLevel `json:"children,omitempty"`               // 二级菜单列表
}

type ChatMenuTopLevelBuilder struct {
	chatMenuTopLevelId     string // 一级菜单ID
	chatMenuTopLevelIdFlag bool
	chatMenuItem           *ChatMenuItem // 一级菜单信息
	chatMenuItemFlag       bool
	children               []*ChatMenuSecondLevel // 二级菜单列表
	childrenFlag           bool
}

func NewChatMenuTopLevelBuilder() *ChatMenuTopLevelBuilder {
	builder := &ChatMenuTopLevelBuilder{}
	return builder
}

// 一级菜单ID
//
// 示例值：7117116451961487361
func (builder *ChatMenuTopLevelBuilder) ChatMenuTopLevelId(chatMenuTopLevelId string) *ChatMenuTopLevelBuilder {
	builder.chatMenuTopLevelId = chatMenuTopLevelId
	builder.chatMenuTopLevelIdFlag = true
	return builder
}

// 一级菜单信息
//
// 示例值：
func (builder *ChatMenuTopLevelBuilder) ChatMenuItem(chatMenuItem *ChatMenuItem) *ChatMenuTopLevelBuilder {
	builder.chatMenuItem = chatMenuItem
	builder.chatMenuItemFlag = true
	return builder
}

// 二级菜单列表
//
// 示例值：
func (builder *ChatMenuTopLevelBuilder) Children(children []*ChatMenuSecondLevel) *ChatMenuTopLevelBuilder {
	builder.children = children
	builder.childrenFlag = true
	return builder
}

func (builder *ChatMenuTopLevelBuilder) Build() *ChatMenuTopLevel {
	req := &ChatMenuTopLevel{}
	if builder.chatMenuTopLevelIdFlag {
		req.ChatMenuTopLevelId = &builder.chatMenuTopLevelId

	}
	if builder.chatMenuItemFlag {
		req.ChatMenuItem = builder.chatMenuItem
	}
	if builder.childrenFlag {
		req.Children = builder.children
	}
	return req
}

type ChatTabConfig struct {
	IconKey   *string `json:"icon_key,omitempty"`    // 群Tab图标
	IsBuiltIn *bool   `json:"is_built_in,omitempty"` // 群tab是否App内嵌打开
}

type ChatTabConfigBuilder struct {
	iconKey       string // 群Tab图标
	iconKeyFlag   bool
	isBuiltIn     bool // 群tab是否App内嵌打开
	isBuiltInFlag bool
}

func NewChatTabConfigBuilder() *ChatTabConfigBuilder {
	builder := &ChatTabConfigBuilder{}
	return builder
}

// 群Tab图标
//
// 示例值：img_v2_b99741-7628-4abd-aad0-b881e4db83ig
func (builder *ChatTabConfigBuilder) IconKey(iconKey string) *ChatTabConfigBuilder {
	builder.iconKey = iconKey
	builder.iconKeyFlag = true
	return builder
}

// 群tab是否App内嵌打开
//
// 示例值：false
func (builder *ChatTabConfigBuilder) IsBuiltIn(isBuiltIn bool) *ChatTabConfigBuilder {
	builder.isBuiltIn = isBuiltIn
	builder.isBuiltInFlag = true
	return builder
}

func (builder *ChatTabConfigBuilder) Build() *ChatTabConfig {
	req := &ChatTabConfig{}
	if builder.iconKeyFlag {
		req.IconKey = &builder.iconKey

	}
	if builder.isBuiltInFlag {
		req.IsBuiltIn = &builder.isBuiltIn

	}
	return req
}

type ChatTabContent struct {
	Url           *string `json:"url,omitempty"`            // URL类型
	Doc           *string `json:"doc,omitempty"`            // Doc链接
	MeetingMinute *string `json:"meeting_minute,omitempty"` // 会议纪要
}

type ChatTabContentBuilder struct {
	url               string // URL类型
	urlFlag           bool
	doc               string // Doc链接
	docFlag           bool
	meetingMinute     string // 会议纪要
	meetingMinuteFlag bool
}

func NewChatTabContentBuilder() *ChatTabContentBuilder {
	builder := &ChatTabContentBuilder{}
	return builder
}

// URL类型
//
// 示例值：https://www.feishu.cn
func (builder *ChatTabContentBuilder) Url(url string) *ChatTabContentBuilder {
	builder.url = url
	builder.urlFlag = true
	return builder
}

// Doc链接
//
// 示例值：https://bytedance.feishu.cn/wiki/wikcnPIcqWjJQwkwDzrB9t40123xz
func (builder *ChatTabContentBuilder) Doc(doc string) *ChatTabContentBuilder {
	builder.doc = doc
	builder.docFlag = true
	return builder
}

// 会议纪要
//
// 示例值：https://bytedance.feishu.cn/docs/doccnvIXbV22i6hSD3utar4123dx
func (builder *ChatTabContentBuilder) MeetingMinute(meetingMinute string) *ChatTabContentBuilder {
	builder.meetingMinute = meetingMinute
	builder.meetingMinuteFlag = true
	return builder
}

func (builder *ChatTabContentBuilder) Build() *ChatTabContent {
	req := &ChatTabContent{}
	if builder.urlFlag {
		req.Url = &builder.url

	}
	if builder.docFlag {
		req.Doc = &builder.doc

	}
	if builder.meetingMinuteFlag {
		req.MeetingMinute = &builder.meetingMinute

	}
	return req
}

type Emoji struct {
	EmojiType *string `json:"emoji_type,omitempty"` // emoji类型 [emoji类型列举](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/message-reaction/emojis-introduce)
}

type EmojiBuilder struct {
	emojiType     string // emoji类型 [emoji类型列举](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/message-reaction/emojis-introduce)
	emojiTypeFlag bool
}

func NewEmojiBuilder() *EmojiBuilder {
	builder := &EmojiBuilder{}
	return builder
}

// emoji类型 [emoji类型列举](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/message-reaction/emojis-introduce)
//
// 示例值：SMILE
func (builder *EmojiBuilder) EmojiType(emojiType string) *EmojiBuilder {
	builder.emojiType = emojiType
	builder.emojiTypeFlag = true
	return builder
}

func (builder *EmojiBuilder) Build() *Emoji {
	req := &Emoji{}
	if builder.emojiTypeFlag {
		req.EmojiType = &builder.emojiType

	}
	return req
}

type EventMessage struct {
	MessageId   *string         `json:"message_id,omitempty"`   // 消息的open_message_id，说明参见：[消息ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/message/intro#ac79c1c2)
	RootId      *string         `json:"root_id,omitempty"`      // 根消息id，用于回复消息场景，说明参见：[消息ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/message/intro#ac79c1c2)
	ParentId    *string         `json:"parent_id,omitempty"`    // 父消息的id，用于回复消息场景，说明参见：[消息ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/message/intro#ac79c1c2)
	CreateTime  *string         `json:"create_time,omitempty"`  // 消息发送时间（毫秒）
	ChatId      *string         `json:"chat_id,omitempty"`      // 消息所在的群组 ID
	ChatType    *string         `json:"chat_type,omitempty"`    // 消息所在的群组类型;;**可选值有**：;- `p2p`：单聊;- `group`： 群组;- `topic_group`：话题群
	MessageType *string         `json:"message_type,omitempty"` // 消息类型
	Content     *string         `json:"content,omitempty"`      // 消息内容, json 格式 ;[各类型消息Content](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/im-v1/message/events/message_content)
	Mentions    []*MentionEvent `json:"mentions,omitempty"`     // 被提及用户的信息
}

type EventMessageBuilder struct {
	messageId       string // 消息的open_message_id，说明参见：[消息ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/message/intro#ac79c1c2)
	messageIdFlag   bool
	rootId          string // 根消息id，用于回复消息场景，说明参见：[消息ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/message/intro#ac79c1c2)
	rootIdFlag      bool
	parentId        string // 父消息的id，用于回复消息场景，说明参见：[消息ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/message/intro#ac79c1c2)
	parentIdFlag    bool
	createTime      string // 消息发送时间（毫秒）
	createTimeFlag  bool
	chatId          string // 消息所在的群组 ID
	chatIdFlag      bool
	chatType        string // 消息所在的群组类型;;**可选值有**：;- `p2p`：单聊;- `group`： 群组;- `topic_group`：话题群
	chatTypeFlag    bool
	messageType     string // 消息类型
	messageTypeFlag bool
	content         string // 消息内容, json 格式 ;[各类型消息Content](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/im-v1/message/events/message_content)
	contentFlag     bool
	mentions        []*MentionEvent // 被提及用户的信息
	mentionsFlag    bool
}

func NewEventMessageBuilder() *EventMessageBuilder {
	builder := &EventMessageBuilder{}
	return builder
}

// 消息的open_message_id，说明参见：[消息ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/message/intro#ac79c1c2)
//
// 示例值：om_5ce6d572455d361153b7cb51da133945
func (builder *EventMessageBuilder) MessageId(messageId string) *EventMessageBuilder {
	builder.messageId = messageId
	builder.messageIdFlag = true
	return builder
}

// 根消息id，用于回复消息场景，说明参见：[消息ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/message/intro#ac79c1c2)
//
// 示例值：om_5ce6d572455d361153b7cb5xxfsdfsdfdsf
func (builder *EventMessageBuilder) RootId(rootId string) *EventMessageBuilder {
	builder.rootId = rootId
	builder.rootIdFlag = true
	return builder
}

// 父消息的id，用于回复消息场景，说明参见：[消息ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/message/intro#ac79c1c2)
//
// 示例值：om_5ce6d572455d361153b7cb5xxfsdfsdfdsf
func (builder *EventMessageBuilder) ParentId(parentId string) *EventMessageBuilder {
	builder.parentId = parentId
	builder.parentIdFlag = true
	return builder
}

// 消息发送时间（毫秒）
//
// 示例值：1609073151345
func (builder *EventMessageBuilder) CreateTime(createTime string) *EventMessageBuilder {
	builder.createTime = createTime
	builder.createTimeFlag = true
	return builder
}

// 消息所在的群组 ID
//
// 示例值：oc_5ce6d572455d361153b7xx51da133945
func (builder *EventMessageBuilder) ChatId(chatId string) *EventMessageBuilder {
	builder.chatId = chatId
	builder.chatIdFlag = true
	return builder
}

// 消息所在的群组类型;;**可选值有**：;- `p2p`：单聊;- `group`： 群组;- `topic_group`：话题群
//
// 示例值：group
func (builder *EventMessageBuilder) ChatType(chatType string) *EventMessageBuilder {
	builder.chatType = chatType
	builder.chatTypeFlag = true
	return builder
}

// 消息类型
//
// 示例值：text
func (builder *EventMessageBuilder) MessageType(messageType string) *EventMessageBuilder {
	builder.messageType = messageType
	builder.messageTypeFlag = true
	return builder
}

// 消息内容, json 格式 ;[各类型消息Content](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/im-v1/message/events/message_content)
//
// 示例值：{\"text\":\"@_user_1 hello\"}
func (builder *EventMessageBuilder) Content(content string) *EventMessageBuilder {
	builder.content = content
	builder.contentFlag = true
	return builder
}

// 被提及用户的信息
//
// 示例值：
func (builder *EventMessageBuilder) Mentions(mentions []*MentionEvent) *EventMessageBuilder {
	builder.mentions = mentions
	builder.mentionsFlag = true
	return builder
}

func (builder *EventMessageBuilder) Build() *EventMessage {
	req := &EventMessage{}
	if builder.messageIdFlag {
		req.MessageId = &builder.messageId

	}
	if builder.rootIdFlag {
		req.RootId = &builder.rootId

	}
	if builder.parentIdFlag {
		req.ParentId = &builder.parentId

	}
	if builder.createTimeFlag {
		req.CreateTime = &builder.createTime

	}
	if builder.chatIdFlag {
		req.ChatId = &builder.chatId

	}
	if builder.chatTypeFlag {
		req.ChatType = &builder.chatType

	}
	if builder.messageTypeFlag {
		req.MessageType = &builder.messageType

	}
	if builder.contentFlag {
		req.Content = &builder.content

	}
	if builder.mentionsFlag {
		req.Mentions = builder.mentions
	}
	return req
}

type EventMessageReader struct {
	ReaderId  *UserId `json:"reader_id,omitempty"`  // 用户 ID
	ReadTime  *string `json:"read_time,omitempty"`  // 阅读时间
	TenantKey *string `json:"tenant_key,omitempty"` // 租户key，为租户在飞书上的唯一标识，用来换取对应的tenant_access_token，也可以用作租户在应用里面的唯一标识
}

type EventMessageReaderBuilder struct {
	readerId      *UserId // 用户 ID
	readerIdFlag  bool
	readTime      string // 阅读时间
	readTimeFlag  bool
	tenantKey     string // 租户key，为租户在飞书上的唯一标识，用来换取对应的tenant_access_token，也可以用作租户在应用里面的唯一标识
	tenantKeyFlag bool
}

func NewEventMessageReaderBuilder() *EventMessageReaderBuilder {
	builder := &EventMessageReaderBuilder{}
	return builder
}

// 用户 ID
//
// 示例值：ou_5ad573a6411d72b8305fda3a9c15c70e
func (builder *EventMessageReaderBuilder) ReaderId(readerId *UserId) *EventMessageReaderBuilder {
	builder.readerId = readerId
	builder.readerIdFlag = true
	return builder
}

// 阅读时间
//
// 示例值：1609484183000
func (builder *EventMessageReaderBuilder) ReadTime(readTime string) *EventMessageReaderBuilder {
	builder.readTime = readTime
	builder.readTimeFlag = true
	return builder
}

// 租户key，为租户在飞书上的唯一标识，用来换取对应的tenant_access_token，也可以用作租户在应用里面的唯一标识
//
// 示例值：736588c9260f175e
func (builder *EventMessageReaderBuilder) TenantKey(tenantKey string) *EventMessageReaderBuilder {
	builder.tenantKey = tenantKey
	builder.tenantKeyFlag = true
	return builder
}

func (builder *EventMessageReaderBuilder) Build() *EventMessageReader {
	req := &EventMessageReader{}
	if builder.readerIdFlag {
		req.ReaderId = builder.readerId
	}
	if builder.readTimeFlag {
		req.ReadTime = &builder.readTime

	}
	if builder.tenantKeyFlag {
		req.TenantKey = &builder.tenantKey

	}
	return req
}

type EventSender struct {
	SenderId   *UserId `json:"sender_id,omitempty"`   // 用户 ID
	SenderType *string `json:"sender_type,omitempty"` // 消息发送者类型。目前只支持用户(user)发送的消息。
	TenantKey  *string `json:"tenant_key,omitempty"`  // tenant key，为租户在飞书上的唯一标识，用来换取对应的tenant_access_token，也可以用作租户在应用里面的唯一标识
}

type EventSenderBuilder struct {
	senderId       *UserId // 用户 ID
	senderIdFlag   bool
	senderType     string // 消息发送者类型。目前只支持用户(user)发送的消息。
	senderTypeFlag bool
	tenantKey      string // tenant key，为租户在飞书上的唯一标识，用来换取对应的tenant_access_token，也可以用作租户在应用里面的唯一标识
	tenantKeyFlag  bool
}

func NewEventSenderBuilder() *EventSenderBuilder {
	builder := &EventSenderBuilder{}
	return builder
}

// 用户 ID
//
// 示例值：ou_5ad573a6411d72b8305fda3a9c15c70e
func (builder *EventSenderBuilder) SenderId(senderId *UserId) *EventSenderBuilder {
	builder.senderId = senderId
	builder.senderIdFlag = true
	return builder
}

// 消息发送者类型。目前只支持用户(user)发送的消息。
//
// 示例值：user
func (builder *EventSenderBuilder) SenderType(senderType string) *EventSenderBuilder {
	builder.senderType = senderType
	builder.senderTypeFlag = true
	return builder
}

// tenant key，为租户在飞书上的唯一标识，用来换取对应的tenant_access_token，也可以用作租户在应用里面的唯一标识
//
// 示例值：736588c9260f175e
func (builder *EventSenderBuilder) TenantKey(tenantKey string) *EventSenderBuilder {
	builder.tenantKey = tenantKey
	builder.tenantKeyFlag = true
	return builder
}

func (builder *EventSenderBuilder) Build() *EventSender {
	req := &EventSender{}
	if builder.senderIdFlag {
		req.SenderId = builder.senderId
	}
	if builder.senderTypeFlag {
		req.SenderType = &builder.senderType

	}
	if builder.tenantKeyFlag {
		req.TenantKey = &builder.tenantKey

	}
	return req
}

type File struct {
}

type I18nNames struct {
	ZhCn *string `json:"zh_cn,omitempty"` // 中文名
	EnUs *string `json:"en_us,omitempty"` // 英文名
	JaJp *string `json:"ja_jp,omitempty"` // 日文名
}

type I18nNamesBuilder struct {
	zhCn     string // 中文名
	zhCnFlag bool
	enUs     string // 英文名
	enUsFlag bool
	jaJp     string // 日文名
	jaJpFlag bool
}

func NewI18nNamesBuilder() *I18nNamesBuilder {
	builder := &I18nNamesBuilder{}
	return builder
}

// 中文名
//
// 示例值：群聊
func (builder *I18nNamesBuilder) ZhCn(zhCn string) *I18nNamesBuilder {
	builder.zhCn = zhCn
	builder.zhCnFlag = true
	return builder
}

// 英文名
//
// 示例值：group chat
func (builder *I18nNamesBuilder) EnUs(enUs string) *I18nNamesBuilder {
	builder.enUs = enUs
	builder.enUsFlag = true
	return builder
}

// 日文名
//
// 示例值：グループチャット
func (builder *I18nNamesBuilder) JaJp(jaJp string) *I18nNamesBuilder {
	builder.jaJp = jaJp
	builder.jaJpFlag = true
	return builder
}

func (builder *I18nNamesBuilder) Build() *I18nNames {
	req := &I18nNames{}
	if builder.zhCnFlag {
		req.ZhCn = &builder.zhCn

	}
	if builder.enUsFlag {
		req.EnUs = &builder.enUs

	}
	if builder.jaJpFlag {
		req.JaJp = &builder.jaJp

	}
	return req
}

type ImDepthData struct {
	PDate                   *string  `json:"p_date,omitempty"`                      // 分区日期
	DepartmentId            *string  `json:"department_id,omitempty"`               // 部门id
	DepartmentPath          *string  `json:"department_path,omitempty"`             // 部门路径
	SendMsgRate             *float64 `json:"send_msg_rate,omitempty"`               // 发消息用户渗透率
	AvgSendMsgCnt           *float64 `json:"avg_send_msg_cnt,omitempty"`            // 人均发消息数量
	PcSendMsgRate           *float64 `json:"pc_send_msg_rate,omitempty"`            // 仅 PC 端的发消息用户渗透率
	PcAvgSendMsgCnt         *float64 `json:"pc_avg_send_msg_cnt,omitempty"`         // 仅 PC 端的人均发消息数量
	MobileSendMsgRate       *float64 `json:"mobile_send_msg_rate,omitempty"`        // 仅移动端的发消息用户渗透率
	MobileAvgSendMsgCnt     *float64 `json:"mobile_avg_send_msg_cnt,omitempty"`     // 仅移动端的人均发消息数量
	MeetingGroupSendMsgRate *float64 `json:"meeting_group_send_msg_rate,omitempty"` // 会议群发消息用户渗透率
	TenantGroupSendMsgRate  *float64 `json:"tenant_group_send_msg_rate,omitempty"`  // 全员群发消息用户渗透率
	DeptGroupSendMsgRate    *float64 `json:"dept_group_send_msg_rate,omitempty"`    // 部门群发消息用户渗透率
	TopicGroupSendMsgRate   *float64 `json:"topic_group_send_msg_rate,omitempty"`   // 话题群发消息用户渗透率
	GroupAtMsgRate          *float64 `json:"group_at_msg_rate,omitempty"`           // 群聊中 @ 消息占比
	GroupReplyMsgRate       *float64 `json:"group_reply_msg_rate,omitempty"`        // 群聊中回复消息占比
	ReactionRate            *float64 `json:"reaction_rate,omitempty"`               // 发送 reaction 用户渗透率
	P2pSendMsgRate          *float64 `json:"p2p_send_msg_rate,omitempty"`           // 发送单聊消息占比
	ImgSendMsgRate          *float64 `json:"img_send_msg_rate,omitempty"`           // 图片消息用户渗透率
	FileSendMsgRate         *float64 `json:"file_send_msg_rate,omitempty"`          // 文件消息用户渗透率
	StickerSendMsgRate      *float64 `json:"sticker_send_msg_rate,omitempty"`       // 表情包消息用户渗透率
	PostSendMsgRate         *float64 `json:"post_send_msg_rate,omitempty"`          // 富文本消息用户渗透率
}

type ImDepthDataBuilder struct {
	pDate                       string // 分区日期
	pDateFlag                   bool
	departmentId                string // 部门id
	departmentIdFlag            bool
	departmentPath              string // 部门路径
	departmentPathFlag          bool
	sendMsgRate                 float64 // 发消息用户渗透率
	sendMsgRateFlag             bool
	avgSendMsgCnt               float64 // 人均发消息数量
	avgSendMsgCntFlag           bool
	pcSendMsgRate               float64 // 仅 PC 端的发消息用户渗透率
	pcSendMsgRateFlag           bool
	pcAvgSendMsgCnt             float64 // 仅 PC 端的人均发消息数量
	pcAvgSendMsgCntFlag         bool
	mobileSendMsgRate           float64 // 仅移动端的发消息用户渗透率
	mobileSendMsgRateFlag       bool
	mobileAvgSendMsgCnt         float64 // 仅移动端的人均发消息数量
	mobileAvgSendMsgCntFlag     bool
	meetingGroupSendMsgRate     float64 // 会议群发消息用户渗透率
	meetingGroupSendMsgRateFlag bool
	tenantGroupSendMsgRate      float64 // 全员群发消息用户渗透率
	tenantGroupSendMsgRateFlag  bool
	deptGroupSendMsgRate        float64 // 部门群发消息用户渗透率
	deptGroupSendMsgRateFlag    bool
	topicGroupSendMsgRate       float64 // 话题群发消息用户渗透率
	topicGroupSendMsgRateFlag   bool
	groupAtMsgRate              float64 // 群聊中 @ 消息占比
	groupAtMsgRateFlag          bool
	groupReplyMsgRate           float64 // 群聊中回复消息占比
	groupReplyMsgRateFlag       bool
	reactionRate                float64 // 发送 reaction 用户渗透率
	reactionRateFlag            bool
	p2pSendMsgRate              float64 // 发送单聊消息占比
	p2pSendMsgRateFlag          bool
	imgSendMsgRate              float64 // 图片消息用户渗透率
	imgSendMsgRateFlag          bool
	fileSendMsgRate             float64 // 文件消息用户渗透率
	fileSendMsgRateFlag         bool
	stickerSendMsgRate          float64 // 表情包消息用户渗透率
	stickerSendMsgRateFlag      bool
	postSendMsgRate             float64 // 富文本消息用户渗透率
	postSendMsgRateFlag         bool
}

func NewImDepthDataBuilder() *ImDepthDataBuilder {
	builder := &ImDepthDataBuilder{}
	return builder
}

// 分区日期
//
// 示例值：2022-02-02
func (builder *ImDepthDataBuilder) PDate(pDate string) *ImDepthDataBuilder {
	builder.pDate = pDate
	builder.pDateFlag = true
	return builder
}

// 部门id
//
// 示例值：123456
func (builder *ImDepthDataBuilder) DepartmentId(departmentId string) *ImDepthDataBuilder {
	builder.departmentId = departmentId
	builder.departmentIdFlag = true
	return builder
}

// 部门路径
//
// 示例值：test/subtest
func (builder *ImDepthDataBuilder) DepartmentPath(departmentPath string) *ImDepthDataBuilder {
	builder.departmentPath = departmentPath
	builder.departmentPathFlag = true
	return builder
}

// 发消息用户渗透率
//
// 示例值：0.50
func (builder *ImDepthDataBuilder) SendMsgRate(sendMsgRate float64) *ImDepthDataBuilder {
	builder.sendMsgRate = sendMsgRate
	builder.sendMsgRateFlag = true
	return builder
}

// 人均发消息数量
//
// 示例值：50
func (builder *ImDepthDataBuilder) AvgSendMsgCnt(avgSendMsgCnt float64) *ImDepthDataBuilder {
	builder.avgSendMsgCnt = avgSendMsgCnt
	builder.avgSendMsgCntFlag = true
	return builder
}

// 仅 PC 端的发消息用户渗透率
//
// 示例值：0.50
func (builder *ImDepthDataBuilder) PcSendMsgRate(pcSendMsgRate float64) *ImDepthDataBuilder {
	builder.pcSendMsgRate = pcSendMsgRate
	builder.pcSendMsgRateFlag = true
	return builder
}

// 仅 PC 端的人均发消息数量
//
// 示例值：50
func (builder *ImDepthDataBuilder) PcAvgSendMsgCnt(pcAvgSendMsgCnt float64) *ImDepthDataBuilder {
	builder.pcAvgSendMsgCnt = pcAvgSendMsgCnt
	builder.pcAvgSendMsgCntFlag = true
	return builder
}

// 仅移动端的发消息用户渗透率
//
// 示例值：0.50
func (builder *ImDepthDataBuilder) MobileSendMsgRate(mobileSendMsgRate float64) *ImDepthDataBuilder {
	builder.mobileSendMsgRate = mobileSendMsgRate
	builder.mobileSendMsgRateFlag = true
	return builder
}

// 仅移动端的人均发消息数量
//
// 示例值：50
func (builder *ImDepthDataBuilder) MobileAvgSendMsgCnt(mobileAvgSendMsgCnt float64) *ImDepthDataBuilder {
	builder.mobileAvgSendMsgCnt = mobileAvgSendMsgCnt
	builder.mobileAvgSendMsgCntFlag = true
	return builder
}

// 会议群发消息用户渗透率
//
// 示例值：0.50
func (builder *ImDepthDataBuilder) MeetingGroupSendMsgRate(meetingGroupSendMsgRate float64) *ImDepthDataBuilder {
	builder.meetingGroupSendMsgRate = meetingGroupSendMsgRate
	builder.meetingGroupSendMsgRateFlag = true
	return builder
}

// 全员群发消息用户渗透率
//
// 示例值：0.50
func (builder *ImDepthDataBuilder) TenantGroupSendMsgRate(tenantGroupSendMsgRate float64) *ImDepthDataBuilder {
	builder.tenantGroupSendMsgRate = tenantGroupSendMsgRate
	builder.tenantGroupSendMsgRateFlag = true
	return builder
}

// 部门群发消息用户渗透率
//
// 示例值：0.50
func (builder *ImDepthDataBuilder) DeptGroupSendMsgRate(deptGroupSendMsgRate float64) *ImDepthDataBuilder {
	builder.deptGroupSendMsgRate = deptGroupSendMsgRate
	builder.deptGroupSendMsgRateFlag = true
	return builder
}

// 话题群发消息用户渗透率
//
// 示例值：0.50
func (builder *ImDepthDataBuilder) TopicGroupSendMsgRate(topicGroupSendMsgRate float64) *ImDepthDataBuilder {
	builder.topicGroupSendMsgRate = topicGroupSendMsgRate
	builder.topicGroupSendMsgRateFlag = true
	return builder
}

// 群聊中 @ 消息占比
//
// 示例值：0.50
func (builder *ImDepthDataBuilder) GroupAtMsgRate(groupAtMsgRate float64) *ImDepthDataBuilder {
	builder.groupAtMsgRate = groupAtMsgRate
	builder.groupAtMsgRateFlag = true
	return builder
}

// 群聊中回复消息占比
//
// 示例值：0.50
func (builder *ImDepthDataBuilder) GroupReplyMsgRate(groupReplyMsgRate float64) *ImDepthDataBuilder {
	builder.groupReplyMsgRate = groupReplyMsgRate
	builder.groupReplyMsgRateFlag = true
	return builder
}

// 发送 reaction 用户渗透率
//
// 示例值：0.50
func (builder *ImDepthDataBuilder) ReactionRate(reactionRate float64) *ImDepthDataBuilder {
	builder.reactionRate = reactionRate
	builder.reactionRateFlag = true
	return builder
}

// 发送单聊消息占比
//
// 示例值：0.50
func (builder *ImDepthDataBuilder) P2pSendMsgRate(p2pSendMsgRate float64) *ImDepthDataBuilder {
	builder.p2pSendMsgRate = p2pSendMsgRate
	builder.p2pSendMsgRateFlag = true
	return builder
}

// 图片消息用户渗透率
//
// 示例值：0.50
func (builder *ImDepthDataBuilder) ImgSendMsgRate(imgSendMsgRate float64) *ImDepthDataBuilder {
	builder.imgSendMsgRate = imgSendMsgRate
	builder.imgSendMsgRateFlag = true
	return builder
}

// 文件消息用户渗透率
//
// 示例值：0.50
func (builder *ImDepthDataBuilder) FileSendMsgRate(fileSendMsgRate float64) *ImDepthDataBuilder {
	builder.fileSendMsgRate = fileSendMsgRate
	builder.fileSendMsgRateFlag = true
	return builder
}

// 表情包消息用户渗透率
//
// 示例值：0.50
func (builder *ImDepthDataBuilder) StickerSendMsgRate(stickerSendMsgRate float64) *ImDepthDataBuilder {
	builder.stickerSendMsgRate = stickerSendMsgRate
	builder.stickerSendMsgRateFlag = true
	return builder
}

// 富文本消息用户渗透率
//
// 示例值：0.50
func (builder *ImDepthDataBuilder) PostSendMsgRate(postSendMsgRate float64) *ImDepthDataBuilder {
	builder.postSendMsgRate = postSendMsgRate
	builder.postSendMsgRateFlag = true
	return builder
}

func (builder *ImDepthDataBuilder) Build() *ImDepthData {
	req := &ImDepthData{}
	if builder.pDateFlag {
		req.PDate = &builder.pDate

	}
	if builder.departmentIdFlag {
		req.DepartmentId = &builder.departmentId

	}
	if builder.departmentPathFlag {
		req.DepartmentPath = &builder.departmentPath

	}
	if builder.sendMsgRateFlag {
		req.SendMsgRate = &builder.sendMsgRate

	}
	if builder.avgSendMsgCntFlag {
		req.AvgSendMsgCnt = &builder.avgSendMsgCnt

	}
	if builder.pcSendMsgRateFlag {
		req.PcSendMsgRate = &builder.pcSendMsgRate

	}
	if builder.pcAvgSendMsgCntFlag {
		req.PcAvgSendMsgCnt = &builder.pcAvgSendMsgCnt

	}
	if builder.mobileSendMsgRateFlag {
		req.MobileSendMsgRate = &builder.mobileSendMsgRate

	}
	if builder.mobileAvgSendMsgCntFlag {
		req.MobileAvgSendMsgCnt = &builder.mobileAvgSendMsgCnt

	}
	if builder.meetingGroupSendMsgRateFlag {
		req.MeetingGroupSendMsgRate = &builder.meetingGroupSendMsgRate

	}
	if builder.tenantGroupSendMsgRateFlag {
		req.TenantGroupSendMsgRate = &builder.tenantGroupSendMsgRate

	}
	if builder.deptGroupSendMsgRateFlag {
		req.DeptGroupSendMsgRate = &builder.deptGroupSendMsgRate

	}
	if builder.topicGroupSendMsgRateFlag {
		req.TopicGroupSendMsgRate = &builder.topicGroupSendMsgRate

	}
	if builder.groupAtMsgRateFlag {
		req.GroupAtMsgRate = &builder.groupAtMsgRate

	}
	if builder.groupReplyMsgRateFlag {
		req.GroupReplyMsgRate = &builder.groupReplyMsgRate

	}
	if builder.reactionRateFlag {
		req.ReactionRate = &builder.reactionRate

	}
	if builder.p2pSendMsgRateFlag {
		req.P2pSendMsgRate = &builder.p2pSendMsgRate

	}
	if builder.imgSendMsgRateFlag {
		req.ImgSendMsgRate = &builder.imgSendMsgRate

	}
	if builder.fileSendMsgRateFlag {
		req.FileSendMsgRate = &builder.fileSendMsgRate

	}
	if builder.stickerSendMsgRateFlag {
		req.StickerSendMsgRate = &builder.stickerSendMsgRate

	}
	if builder.postSendMsgRateFlag {
		req.PostSendMsgRate = &builder.postSendMsgRate

	}
	return req
}

type Image struct {
}

type ListChat struct {
	ChatId      *string `json:"chat_id,omitempty"`       // 群组 ID，详情参见[群ID 说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-id-description)
	Avatar      *string `json:"avatar,omitempty"`        // 群头像 URL
	Name        *string `json:"name,omitempty"`          // 群名称
	Description *string `json:"description,omitempty"`   // 群描述
	OwnerId     *string `json:"owner_id,omitempty"`      // 群主 ID
	OwnerIdType *string `json:"owner_id_type,omitempty"` // 群主 ID 类型
	External    *bool   `json:"external,omitempty"`      // 是否是外部群
	TenantKey   *string `json:"tenant_key,omitempty"`    // 租户Key，为租户在飞书上的唯一标识，用来换取对应的tenant_access_token，也可以用作租户在应用中的唯一标识

}

type ListChatBuilder struct {
	chatId          string // 群组 ID，详情参见[群ID 说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-id-description)
	chatIdFlag      bool
	avatar          string // 群头像 URL
	avatarFlag      bool
	name            string // 群名称
	nameFlag        bool
	description     string // 群描述
	descriptionFlag bool
	ownerId         string // 群主 ID
	ownerIdFlag     bool
	ownerIdType     string // 群主 ID 类型
	ownerIdTypeFlag bool
	external        bool // 是否是外部群
	externalFlag    bool
	tenantKey       string // 租户Key，为租户在飞书上的唯一标识，用来换取对应的tenant_access_token，也可以用作租户在应用中的唯一标识
	tenantKeyFlag   bool
}

func NewListChatBuilder() *ListChatBuilder {
	builder := &ListChatBuilder{}
	return builder
}

// 群组 ID，详情参见[群ID 说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-id-description)
//
// 示例值：oc_a0553eda9014c201e6969b478895c230
func (builder *ListChatBuilder) ChatId(chatId string) *ListChatBuilder {
	builder.chatId = chatId
	builder.chatIdFlag = true
	return builder
}

// 群头像 URL
//
// 示例值：https://p3-lark-file.byteimg.com/img/lark-avatar-staging/default-avatar_44ae0ca3-e140-494b-956f-78091e348435~100x100.jpg
func (builder *ListChatBuilder) Avatar(avatar string) *ListChatBuilder {
	builder.avatar = avatar
	builder.avatarFlag = true
	return builder
}

// 群名称
//
// 示例值：测试群名称
func (builder *ListChatBuilder) Name(name string) *ListChatBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

// 群描述
//
// 示例值：测试群描述
func (builder *ListChatBuilder) Description(description string) *ListChatBuilder {
	builder.description = description
	builder.descriptionFlag = true
	return builder
}

// 群主 ID
//
// 示例值：4d7a3c6g
func (builder *ListChatBuilder) OwnerId(ownerId string) *ListChatBuilder {
	builder.ownerId = ownerId
	builder.ownerIdFlag = true
	return builder
}

// 群主 ID 类型
//
// 示例值：user_id
func (builder *ListChatBuilder) OwnerIdType(ownerIdType string) *ListChatBuilder {
	builder.ownerIdType = ownerIdType
	builder.ownerIdTypeFlag = true
	return builder
}

// 是否是外部群
//
// 示例值：false
func (builder *ListChatBuilder) External(external bool) *ListChatBuilder {
	builder.external = external
	builder.externalFlag = true
	return builder
}

// 租户Key，为租户在飞书上的唯一标识，用来换取对应的tenant_access_token，也可以用作租户在应用中的唯一标识
//
// 示例值：736588c9260f175e
func (builder *ListChatBuilder) TenantKey(tenantKey string) *ListChatBuilder {
	builder.tenantKey = tenantKey
	builder.tenantKeyFlag = true
	return builder
}

func (builder *ListChatBuilder) Build() *ListChat {
	req := &ListChat{}
	if builder.chatIdFlag {
		req.ChatId = &builder.chatId

	}
	if builder.avatarFlag {
		req.Avatar = &builder.avatar

	}
	if builder.nameFlag {
		req.Name = &builder.name

	}
	if builder.descriptionFlag {
		req.Description = &builder.description

	}
	if builder.ownerIdFlag {
		req.OwnerId = &builder.ownerId

	}
	if builder.ownerIdTypeFlag {
		req.OwnerIdType = &builder.ownerIdType

	}
	if builder.externalFlag {
		req.External = &builder.external

	}
	if builder.tenantKeyFlag {
		req.TenantKey = &builder.tenantKey

	}

	return req
}

type ListEventModerator struct {
	TenantKey *string `json:"tenant_key,omitempty"` // 租户 Key
	UserId    *UserId `json:"user_id,omitempty"`    // 用户 ID
}

type ListEventModeratorBuilder struct {
	tenantKey     string // 租户 Key
	tenantKeyFlag bool
	userId        *UserId // 用户 ID
	userIdFlag    bool
}

func NewListEventModeratorBuilder() *ListEventModeratorBuilder {
	builder := &ListEventModeratorBuilder{}
	return builder
}

// 租户 Key
//
// 示例值：86gwe65
func (builder *ListEventModeratorBuilder) TenantKey(tenantKey string) *ListEventModeratorBuilder {
	builder.tenantKey = tenantKey
	builder.tenantKeyFlag = true
	return builder
}

// 用户 ID
//
// 示例值：
func (builder *ListEventModeratorBuilder) UserId(userId *UserId) *ListEventModeratorBuilder {
	builder.userId = userId
	builder.userIdFlag = true
	return builder
}

func (builder *ListEventModeratorBuilder) Build() *ListEventModerator {
	req := &ListEventModerator{}
	if builder.tenantKeyFlag {
		req.TenantKey = &builder.tenantKey

	}
	if builder.userIdFlag {
		req.UserId = builder.userId
	}
	return req
}

type ListMember struct {
	MemberIdType *string `json:"member_id_type,omitempty"` // 成员的用户 ID 类型，与查询参数中的 member_id_type 相同。取值为：`open_id`、`user_id`、`union_id`其中之一。
	MemberId     *string `json:"member_id,omitempty"`      // 成员的用户ID，ID值与查询参数中的 member_id_type 对应。;;不同 ID 的说明参见 [用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction)
	Name         *string `json:"name,omitempty"`           // 名字
	TenantKey    *string `json:"tenant_key,omitempty"`     // 租户Key，为租户在飞书上的唯一标识，用来换取对应的tenant_access_token，也可以用作租户在应用中的唯一标识
}

type ListMemberBuilder struct {
	memberIdType     string // 成员的用户 ID 类型，与查询参数中的 member_id_type 相同。取值为：`open_id`、`user_id`、`union_id`其中之一。
	memberIdTypeFlag bool
	memberId         string // 成员的用户ID，ID值与查询参数中的 member_id_type 对应。;;不同 ID 的说明参见 [用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction)
	memberIdFlag     bool
	name             string // 名字
	nameFlag         bool
	tenantKey        string // 租户Key，为租户在飞书上的唯一标识，用来换取对应的tenant_access_token，也可以用作租户在应用中的唯一标识
	tenantKeyFlag    bool
}

func NewListMemberBuilder() *ListMemberBuilder {
	builder := &ListMemberBuilder{}
	return builder
}

// 成员的用户 ID 类型，与查询参数中的 member_id_type 相同。取值为：`open_id`、`user_id`、`union_id`其中之一。
//
// 示例值：user_id
func (builder *ListMemberBuilder) MemberIdType(memberIdType string) *ListMemberBuilder {
	builder.memberIdType = memberIdType
	builder.memberIdTypeFlag = true
	return builder
}

// 成员的用户ID，ID值与查询参数中的 member_id_type 对应。;;不同 ID 的说明参见 [用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction)
//
// 示例值：4d7a3c6g
func (builder *ListMemberBuilder) MemberId(memberId string) *ListMemberBuilder {
	builder.memberId = memberId
	builder.memberIdFlag = true
	return builder
}

// 名字
//
// 示例值：张三
func (builder *ListMemberBuilder) Name(name string) *ListMemberBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

// 租户Key，为租户在飞书上的唯一标识，用来换取对应的tenant_access_token，也可以用作租户在应用中的唯一标识
//
// 示例值：
func (builder *ListMemberBuilder) TenantKey(tenantKey string) *ListMemberBuilder {
	builder.tenantKey = tenantKey
	builder.tenantKeyFlag = true
	return builder
}

func (builder *ListMemberBuilder) Build() *ListMember {
	req := &ListMember{}
	if builder.memberIdTypeFlag {
		req.MemberIdType = &builder.memberIdType

	}
	if builder.memberIdFlag {
		req.MemberId = &builder.memberId

	}
	if builder.nameFlag {
		req.Name = &builder.name

	}
	if builder.tenantKeyFlag {
		req.TenantKey = &builder.tenantKey

	}
	return req
}

type ListModerator struct {
	UserIdType *string `json:"user_id_type,omitempty"` // 可发言用户 ID 类型
	UserId     *string `json:"user_id,omitempty"`      // 可发言用户 ID
	TenantKey  *string `json:"tenant_key,omitempty"`   // 租户Key，为租户在飞书上的唯一标识，用来换取对应的tenant_access_token，也可以用作租户在应用中的唯一标识
}

type ListModeratorBuilder struct {
	userIdType     string // 可发言用户 ID 类型
	userIdTypeFlag bool
	userId         string // 可发言用户 ID
	userIdFlag     bool
	tenantKey      string // 租户Key，为租户在飞书上的唯一标识，用来换取对应的tenant_access_token，也可以用作租户在应用中的唯一标识
	tenantKeyFlag  bool
}

func NewListModeratorBuilder() *ListModeratorBuilder {
	builder := &ListModeratorBuilder{}
	return builder
}

// 可发言用户 ID 类型
//
// 示例值：user_id
func (builder *ListModeratorBuilder) UserIdType(userIdType string) *ListModeratorBuilder {
	builder.userIdType = userIdType
	builder.userIdTypeFlag = true
	return builder
}

// 可发言用户 ID
//
// 示例值：4d7a3c6g
func (builder *ListModeratorBuilder) UserId(userId string) *ListModeratorBuilder {
	builder.userId = userId
	builder.userIdFlag = true
	return builder
}

// 租户Key，为租户在飞书上的唯一标识，用来换取对应的tenant_access_token，也可以用作租户在应用中的唯一标识
//
// 示例值：
func (builder *ListModeratorBuilder) TenantKey(tenantKey string) *ListModeratorBuilder {
	builder.tenantKey = tenantKey
	builder.tenantKeyFlag = true
	return builder
}

func (builder *ListModeratorBuilder) Build() *ListModerator {
	req := &ListModerator{}
	if builder.userIdTypeFlag {
		req.UserIdType = &builder.userIdType

	}
	if builder.userIdFlag {
		req.UserId = &builder.userId

	}
	if builder.tenantKeyFlag {
		req.TenantKey = &builder.tenantKey

	}
	return req
}

type Mention struct {
	Key       *string `json:"key,omitempty"`        // 被@的用户或机器人的序号。例如，第3个被@到的成员，值为“@_user_3”
	Id        *string `json:"id,omitempty"`         // 被@的用户或者机器人的open_id
	IdType    *string `json:"id_type,omitempty"`    // 被@的用户或机器人 id 类型，目前仅支持 `open_id` ([什么是 Open ID？](https://open.feishu.cn/document/home/<USER>/open-id))
	Name      *string `json:"name,omitempty"`       // 被@的用户或机器人的姓名
	TenantKey *string `json:"tenant_key,omitempty"` // 为租户在飞书上的唯一标识，用来换取对应的tenant_access_token，也可以用作租户在应用里面的唯一标识
}

type MentionBuilder struct {
	key           string // 被@的用户或机器人的序号。例如，第3个被@到的成员，值为“@_user_3”
	keyFlag       bool
	id            string // 被@的用户或者机器人的open_id
	idFlag        bool
	idType        string // 被@的用户或机器人 id 类型，目前仅支持 `open_id` ([什么是 Open ID？](https://open.feishu.cn/document/home/<USER>/open-id))
	idTypeFlag    bool
	name          string // 被@的用户或机器人的姓名
	nameFlag      bool
	tenantKey     string // 为租户在飞书上的唯一标识，用来换取对应的tenant_access_token，也可以用作租户在应用里面的唯一标识
	tenantKeyFlag bool
}

func NewMentionBuilder() *MentionBuilder {
	builder := &MentionBuilder{}
	return builder
}

// 被@的用户或机器人的序号。例如，第3个被@到的成员，值为“@_user_3”
//
// 示例值：@_user_1
func (builder *MentionBuilder) Key(key string) *MentionBuilder {
	builder.key = key
	builder.keyFlag = true
	return builder
}

// 被@的用户或者机器人的open_id
//
// 示例值：ou_155184d1e73cbfb8973e5a9e698e74f2
func (builder *MentionBuilder) Id(id string) *MentionBuilder {
	builder.id = id
	builder.idFlag = true
	return builder
}

// 被@的用户或机器人 id 类型，目前仅支持 `open_id` ([什么是 Open ID？](https://open.feishu.cn/document/home/<USER>/open-id))
//
// 示例值：open_id
func (builder *MentionBuilder) IdType(idType string) *MentionBuilder {
	builder.idType = idType
	builder.idTypeFlag = true
	return builder
}

// 被@的用户或机器人的姓名
//
// 示例值：Tom
func (builder *MentionBuilder) Name(name string) *MentionBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

// 为租户在飞书上的唯一标识，用来换取对应的tenant_access_token，也可以用作租户在应用里面的唯一标识
//
// 示例值：736588c9260f175e
func (builder *MentionBuilder) TenantKey(tenantKey string) *MentionBuilder {
	builder.tenantKey = tenantKey
	builder.tenantKeyFlag = true
	return builder
}

func (builder *MentionBuilder) Build() *Mention {
	req := &Mention{}
	if builder.keyFlag {
		req.Key = &builder.key

	}
	if builder.idFlag {
		req.Id = &builder.id

	}
	if builder.idTypeFlag {
		req.IdType = &builder.idType

	}
	if builder.nameFlag {
		req.Name = &builder.name

	}
	if builder.tenantKeyFlag {
		req.TenantKey = &builder.tenantKey

	}
	return req
}

type MentionEvent struct {
	Key       *string `json:"key,omitempty"`        // mention key
	Id        *UserId `json:"id,omitempty"`         // 用户 ID
	Name      *string `json:"name,omitempty"`       // 用户姓名
	TenantKey *string `json:"tenant_key,omitempty"` // tenant key，为租户在飞书上的唯一标识，用来换取对应的tenant_access_token，也可以用作租户在应用里面的唯一标识
}

type MentionEventBuilder struct {
	key           string // mention key
	keyFlag       bool
	id            *UserId // 用户 ID
	idFlag        bool
	name          string // 用户姓名
	nameFlag      bool
	tenantKey     string // tenant key，为租户在飞书上的唯一标识，用来换取对应的tenant_access_token，也可以用作租户在应用里面的唯一标识
	tenantKeyFlag bool
}

func NewMentionEventBuilder() *MentionEventBuilder {
	builder := &MentionEventBuilder{}
	return builder
}

// mention key
//
// 示例值：@_user_1
func (builder *MentionEventBuilder) Key(key string) *MentionEventBuilder {
	builder.key = key
	builder.keyFlag = true
	return builder
}

// 用户 ID
//
// 示例值：
func (builder *MentionEventBuilder) Id(id *UserId) *MentionEventBuilder {
	builder.id = id
	builder.idFlag = true
	return builder
}

// 用户姓名
//
// 示例值：Tom
func (builder *MentionEventBuilder) Name(name string) *MentionEventBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

// tenant key，为租户在飞书上的唯一标识，用来换取对应的tenant_access_token，也可以用作租户在应用里面的唯一标识
//
// 示例值：736588c9260f175e
func (builder *MentionEventBuilder) TenantKey(tenantKey string) *MentionEventBuilder {
	builder.tenantKey = tenantKey
	builder.tenantKeyFlag = true
	return builder
}

func (builder *MentionEventBuilder) Build() *MentionEvent {
	req := &MentionEvent{}
	if builder.keyFlag {
		req.Key = &builder.key

	}
	if builder.idFlag {
		req.Id = builder.id
	}
	if builder.nameFlag {
		req.Name = &builder.name

	}
	if builder.tenantKeyFlag {
		req.TenantKey = &builder.tenantKey

	}
	return req
}

type Message struct {
	MessageId      *string      `json:"message_id,omitempty"`       // 消息id，说明参见：[消息ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/message/intro#ac79c1c2)
	RootId         *string      `json:"root_id,omitempty"`          // 根消息id，用于回复消息场景，说明参见：[消息ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/message/intro#ac79c1c2)
	ParentId       *string      `json:"parent_id,omitempty"`        // 父消息的id，用于回复消息场景，说明参见：[消息ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/message/intro#ac79c1c2)
	MsgType        *string      `json:"msg_type,omitempty"`         // 消息类型 包括：text、post、image、file、audio、media、sticker、interactive、share_chat、share_user等，类型定义请参考[接收消息Content](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/im-v1/message/events/message_content)
	CreateTime     *string      `json:"create_time,omitempty"`      // 消息生成的时间戳（毫秒）
	UpdateTime     *string      `json:"update_time,omitempty"`      // 消息更新的时间戳（毫秒）
	Deleted        *bool        `json:"deleted,omitempty"`          // 消息是否被撤回
	Updated        *bool        `json:"updated,omitempty"`          // 消息是否被更新
	ChatId         *string      `json:"chat_id,omitempty"`          // 所属的群
	Sender         *Sender      `json:"sender,omitempty"`           // 发送者，可以是用户或应用
	Body           *MessageBody `json:"body,omitempty"`             // 消息内容
	Mentions       []*Mention   `json:"mentions,omitempty"`         // 被@的用户或机器人的id列表
	UpperMessageId *string      `json:"upper_message_id,omitempty"` // 合并转发消息中，上一层级的消息id message_id，说明参见：[消息ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/message/intro#ac79c1c2)
}

type MessageBuilder struct {
	messageId          string // 消息id，说明参见：[消息ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/message/intro#ac79c1c2)
	messageIdFlag      bool
	rootId             string // 根消息id，用于回复消息场景，说明参见：[消息ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/message/intro#ac79c1c2)
	rootIdFlag         bool
	parentId           string // 父消息的id，用于回复消息场景，说明参见：[消息ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/message/intro#ac79c1c2)
	parentIdFlag       bool
	msgType            string // 消息类型 包括：text、post、image、file、audio、media、sticker、interactive、share_chat、share_user等，类型定义请参考[接收消息Content](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/im-v1/message/events/message_content)
	msgTypeFlag        bool
	createTime         string // 消息生成的时间戳（毫秒）
	createTimeFlag     bool
	updateTime         string // 消息更新的时间戳（毫秒）
	updateTimeFlag     bool
	deleted            bool // 消息是否被撤回
	deletedFlag        bool
	updated            bool // 消息是否被更新
	updatedFlag        bool
	chatId             string // 所属的群
	chatIdFlag         bool
	sender             *Sender // 发送者，可以是用户或应用
	senderFlag         bool
	body               *MessageBody // 消息内容
	bodyFlag           bool
	mentions           []*Mention // 被@的用户或机器人的id列表
	mentionsFlag       bool
	upperMessageId     string // 合并转发消息中，上一层级的消息id message_id，说明参见：[消息ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/message/intro#ac79c1c2)
	upperMessageIdFlag bool
}

func NewMessageBuilder() *MessageBuilder {
	builder := &MessageBuilder{}
	return builder
}

// 消息id，说明参见：[消息ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/message/intro#ac79c1c2)
//
// 示例值：om_dc13264520392913993dd051dba21dcf
func (builder *MessageBuilder) MessageId(messageId string) *MessageBuilder {
	builder.messageId = messageId
	builder.messageIdFlag = true
	return builder
}

// 根消息id，用于回复消息场景，说明参见：[消息ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/message/intro#ac79c1c2)
//
// 示例值：om_40eb06e7b84dc71c03e009ad3c754195
func (builder *MessageBuilder) RootId(rootId string) *MessageBuilder {
	builder.rootId = rootId
	builder.rootIdFlag = true
	return builder
}

// 父消息的id，用于回复消息场景，说明参见：[消息ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/message/intro#ac79c1c2)
//
// 示例值：om_d4be107c616aed9c1da8ed8068570a9f
func (builder *MessageBuilder) ParentId(parentId string) *MessageBuilder {
	builder.parentId = parentId
	builder.parentIdFlag = true
	return builder
}

// 消息类型 包括：text、post、image、file、audio、media、sticker、interactive、share_chat、share_user等，类型定义请参考[接收消息Content](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/im-v1/message/events/message_content)
//
// 示例值：card
func (builder *MessageBuilder) MsgType(msgType string) *MessageBuilder {
	builder.msgType = msgType
	builder.msgTypeFlag = true
	return builder
}

// 消息生成的时间戳（毫秒）
//
// 示例值：1615380573411
func (builder *MessageBuilder) CreateTime(createTime string) *MessageBuilder {
	builder.createTime = createTime
	builder.createTimeFlag = true
	return builder
}

// 消息更新的时间戳（毫秒）
//
// 示例值：1615380573411
func (builder *MessageBuilder) UpdateTime(updateTime string) *MessageBuilder {
	builder.updateTime = updateTime
	builder.updateTimeFlag = true
	return builder
}

// 消息是否被撤回
//
// 示例值：false
func (builder *MessageBuilder) Deleted(deleted bool) *MessageBuilder {
	builder.deleted = deleted
	builder.deletedFlag = true
	return builder
}

// 消息是否被更新
//
// 示例值：false
func (builder *MessageBuilder) Updated(updated bool) *MessageBuilder {
	builder.updated = updated
	builder.updatedFlag = true
	return builder
}

// 所属的群
//
// 示例值：oc_5ad11d72b830411d72b836c20
func (builder *MessageBuilder) ChatId(chatId string) *MessageBuilder {
	builder.chatId = chatId
	builder.chatIdFlag = true
	return builder
}

// 发送者，可以是用户或应用
//
// 示例值：object
func (builder *MessageBuilder) Sender(sender *Sender) *MessageBuilder {
	builder.sender = sender
	builder.senderFlag = true
	return builder
}

// 消息内容
//
// 示例值：json结构
func (builder *MessageBuilder) Body(body *MessageBody) *MessageBuilder {
	builder.body = body
	builder.bodyFlag = true
	return builder
}

// 被@的用户或机器人的id列表
//
// 示例值：
func (builder *MessageBuilder) Mentions(mentions []*Mention) *MessageBuilder {
	builder.mentions = mentions
	builder.mentionsFlag = true
	return builder
}

// 合并转发消息中，上一层级的消息id message_id，说明参见：[消息ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/message/intro#ac79c1c2)
//
// 示例值：om_40eb06e7b84dc71c03e009ad3c754195
func (builder *MessageBuilder) UpperMessageId(upperMessageId string) *MessageBuilder {
	builder.upperMessageId = upperMessageId
	builder.upperMessageIdFlag = true
	return builder
}

func (builder *MessageBuilder) Build() *Message {
	req := &Message{}
	if builder.messageIdFlag {
		req.MessageId = &builder.messageId

	}
	if builder.rootIdFlag {
		req.RootId = &builder.rootId

	}
	if builder.parentIdFlag {
		req.ParentId = &builder.parentId

	}
	if builder.msgTypeFlag {
		req.MsgType = &builder.msgType

	}
	if builder.createTimeFlag {
		req.CreateTime = &builder.createTime

	}
	if builder.updateTimeFlag {
		req.UpdateTime = &builder.updateTime

	}
	if builder.deletedFlag {
		req.Deleted = &builder.deleted

	}
	if builder.updatedFlag {
		req.Updated = &builder.updated

	}
	if builder.chatIdFlag {
		req.ChatId = &builder.chatId

	}
	if builder.senderFlag {
		req.Sender = builder.sender
	}
	if builder.bodyFlag {
		req.Body = builder.body
	}
	if builder.mentionsFlag {
		req.Mentions = builder.mentions
	}
	if builder.upperMessageIdFlag {
		req.UpperMessageId = &builder.upperMessageId

	}
	return req
}

type MessageReaction struct {
	ReactionId   *string   `json:"reaction_id,omitempty"`   // reaction资源ID
	Operator     *Operator `json:"operator,omitempty"`      // 添加reaction的操作人
	ActionTime   *string   `json:"action_time,omitempty"`   // reaction动作的的unix timestamp(单位:ms)
	ReactionType *Emoji    `json:"reaction_type,omitempty"` // reaction资源类型
}

type MessageReactionBuilder struct {
	reactionId       string // reaction资源ID
	reactionIdFlag   bool
	operator         *Operator // 添加reaction的操作人
	operatorFlag     bool
	actionTime       string // reaction动作的的unix timestamp(单位:ms)
	actionTimeFlag   bool
	reactionType     *Emoji // reaction资源类型
	reactionTypeFlag bool
}

func NewMessageReactionBuilder() *MessageReactionBuilder {
	builder := &MessageReactionBuilder{}
	return builder
}

// reaction资源ID
//
// 示例值：
func (builder *MessageReactionBuilder) ReactionId(reactionId string) *MessageReactionBuilder {
	builder.reactionId = reactionId
	builder.reactionIdFlag = true
	return builder
}

// 添加reaction的操作人
//
// 示例值：
func (builder *MessageReactionBuilder) Operator(operator *Operator) *MessageReactionBuilder {
	builder.operator = operator
	builder.operatorFlag = true
	return builder
}

// reaction动作的的unix timestamp(单位:ms)
//
// 示例值：
func (builder *MessageReactionBuilder) ActionTime(actionTime string) *MessageReactionBuilder {
	builder.actionTime = actionTime
	builder.actionTimeFlag = true
	return builder
}

// reaction资源类型
//
// 示例值：
func (builder *MessageReactionBuilder) ReactionType(reactionType *Emoji) *MessageReactionBuilder {
	builder.reactionType = reactionType
	builder.reactionTypeFlag = true
	return builder
}

func (builder *MessageReactionBuilder) Build() *MessageReaction {
	req := &MessageReaction{}
	if builder.reactionIdFlag {
		req.ReactionId = &builder.reactionId

	}
	if builder.operatorFlag {
		req.Operator = builder.operator
	}
	if builder.actionTimeFlag {
		req.ActionTime = &builder.actionTime

	}
	if builder.reactionTypeFlag {
		req.ReactionType = builder.reactionType
	}
	return req
}

type MessageResource struct {
}

type MessageBody struct {
	Content *string `json:"content,omitempty"` // 消息内容，json结构序列化后的字符串。不同msg_type对应不同内容。消息类型 包括：text、post、image、file、audio、media、sticker、interactive、share_chat、share_user等，类型定义请参考：[发送消息Content](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/im-v1/message/create_json)
}

type MessageBodyBuilder struct {
	content     string // 消息内容，json结构序列化后的字符串。不同msg_type对应不同内容。消息类型 包括：text、post、image、file、audio、media、sticker、interactive、share_chat、share_user等，类型定义请参考：[发送消息Content](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/im-v1/message/create_json)
	contentFlag bool
}

func NewMessageBodyBuilder() *MessageBodyBuilder {
	builder := &MessageBodyBuilder{}
	return builder
}

// 消息内容，json结构序列化后的字符串。不同msg_type对应不同内容。消息类型 包括：text、post、image、file、audio、media、sticker、interactive、share_chat、share_user等，类型定义请参考：[发送消息Content](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/im-v1/message/create_json)
//
// 示例值：text:测试消息
func (builder *MessageBodyBuilder) Content(content string) *MessageBodyBuilder {
	builder.content = content
	builder.contentFlag = true
	return builder
}

func (builder *MessageBodyBuilder) Build() *MessageBody {
	req := &MessageBody{}
	if builder.contentFlag {
		req.Content = &builder.content

	}
	return req
}

type ModeratorList struct {
	AddedMemberList   []*ListEventModerator `json:"added_member_list,omitempty"`   // 被添加进可发言名单的用户列表（列表中一定会有owner）
	RemovedMemberList []*ListEventModerator `json:"removed_member_list,omitempty"` // 被移除出可发言名单的用户列表
}

type ModeratorListBuilder struct {
	addedMemberList       []*ListEventModerator // 被添加进可发言名单的用户列表（列表中一定会有owner）
	addedMemberListFlag   bool
	removedMemberList     []*ListEventModerator // 被移除出可发言名单的用户列表
	removedMemberListFlag bool
}

func NewModeratorListBuilder() *ModeratorListBuilder {
	builder := &ModeratorListBuilder{}
	return builder
}

// 被添加进可发言名单的用户列表（列表中一定会有owner）
//
// 示例值：
func (builder *ModeratorListBuilder) AddedMemberList(addedMemberList []*ListEventModerator) *ModeratorListBuilder {
	builder.addedMemberList = addedMemberList
	builder.addedMemberListFlag = true
	return builder
}

// 被移除出可发言名单的用户列表
//
// 示例值：
func (builder *ModeratorListBuilder) RemovedMemberList(removedMemberList []*ListEventModerator) *ModeratorListBuilder {
	builder.removedMemberList = removedMemberList
	builder.removedMemberListFlag = true
	return builder
}

func (builder *ModeratorListBuilder) Build() *ModeratorList {
	req := &ModeratorList{}
	if builder.addedMemberListFlag {
		req.AddedMemberList = builder.addedMemberList
	}
	if builder.removedMemberListFlag {
		req.RemovedMemberList = builder.removedMemberList
	}
	return req
}

type MsgProcessData struct {
	PDate                   *string  `json:"p_date,omitempty"`                        // 分区日期
	DepartmentId            *string  `json:"department_id,omitempty"`                 // 部门id
	DepartmentPath          *string  `json:"department_path,omitempty"`               // 部门路径
	AvgImpMsgReadRate12h    *float64 `json:"avg_imp_msg_read_rate_12h,omitempty"`     // 人均重要消息 12 小时阅读率
	MsgReadRate12h          *float64 `json:"msg_read_rate_12h,omitempty"`             // 人均 12 小时阅读率
	AvgReceiveMsgCnt        *float64 `json:"avg_receive_msg_cnt,omitempty"`           // 人均接收消息数
	AvgReadMsgCnt           *float64 `json:"avg_read_msg_cnt,omitempty"`              // 人均阅读消息数
	AvgImpReadMsgCnt        *float64 `json:"avg_imp_read_msg_cnt,omitempty"`          // 人均重要消息阅读数
	AvgImpReceiveMsgCnt     *float64 `json:"avg_imp_receive_msg_cnt,omitempty"`       // 人均重要消息接收数
	HighLoadRate            *float64 `json:"high_load_rate,omitempty"`                // 信息高负载员工占比
	BigGroupMsgRate         *float64 `json:"big_group_msg_rate,omitempty"`            // 大群（100人以上）消息占比
	BigGroupTopicMsgRate    *float64 `json:"big_group_topic_msg_rate,omitempty"`      // 大群消息中话题消息占比
	AvgReceiveBotMsgCnt     *float64 `json:"avg_receive_bot_msg_cnt,omitempty"`       // 人均接收 bot 消息数
	AvgBotImpMsgReadRate12h *float64 `json:"avg_bot_imp_msg_read_rate_12h,omitempty"` // 重要 bot 消息 12 小时阅读率
	ReceiveBotMsgRate       *float64 `json:"receive_bot_msg_rate,omitempty"`          // 接收 bot 消息占比
	UseChatBoxRate          *float64 `json:"use_chat_box_rate,omitempty"`             // 会话盒子使用率
	ReceiveMuteMsgRate      *float64 `json:"receive_mute_msg_rate,omitempty"`         // 接收消息中 mute 消息占比
	ReadMuteMsgRate         *float64 `json:"read_mute_msg_rate,omitempty"`            // 阅读消息中 mute 消息占比
	AvgReceiveImpMsgChatCnt *float64 `json:"avg_receive_imp_msg_chat_cnt,omitempty"`  // 人均有重要消息的会话数
}

type MsgProcessDataBuilder struct {
	pDate                       string // 分区日期
	pDateFlag                   bool
	departmentId                string // 部门id
	departmentIdFlag            bool
	departmentPath              string // 部门路径
	departmentPathFlag          bool
	avgImpMsgReadRate12h        float64 // 人均重要消息 12 小时阅读率
	avgImpMsgReadRate12hFlag    bool
	msgReadRate12h              float64 // 人均 12 小时阅读率
	msgReadRate12hFlag          bool
	avgReceiveMsgCnt            float64 // 人均接收消息数
	avgReceiveMsgCntFlag        bool
	avgReadMsgCnt               float64 // 人均阅读消息数
	avgReadMsgCntFlag           bool
	avgImpReadMsgCnt            float64 // 人均重要消息阅读数
	avgImpReadMsgCntFlag        bool
	avgImpReceiveMsgCnt         float64 // 人均重要消息接收数
	avgImpReceiveMsgCntFlag     bool
	highLoadRate                float64 // 信息高负载员工占比
	highLoadRateFlag            bool
	bigGroupMsgRate             float64 // 大群（100人以上）消息占比
	bigGroupMsgRateFlag         bool
	bigGroupTopicMsgRate        float64 // 大群消息中话题消息占比
	bigGroupTopicMsgRateFlag    bool
	avgReceiveBotMsgCnt         float64 // 人均接收 bot 消息数
	avgReceiveBotMsgCntFlag     bool
	avgBotImpMsgReadRate12h     float64 // 重要 bot 消息 12 小时阅读率
	avgBotImpMsgReadRate12hFlag bool
	receiveBotMsgRate           float64 // 接收 bot 消息占比
	receiveBotMsgRateFlag       bool
	useChatBoxRate              float64 // 会话盒子使用率
	useChatBoxRateFlag          bool
	receiveMuteMsgRate          float64 // 接收消息中 mute 消息占比
	receiveMuteMsgRateFlag      bool
	readMuteMsgRate             float64 // 阅读消息中 mute 消息占比
	readMuteMsgRateFlag         bool
	avgReceiveImpMsgChatCnt     float64 // 人均有重要消息的会话数
	avgReceiveImpMsgChatCntFlag bool
}

func NewMsgProcessDataBuilder() *MsgProcessDataBuilder {
	builder := &MsgProcessDataBuilder{}
	return builder
}

// 分区日期
//
// 示例值：2022-02-02
func (builder *MsgProcessDataBuilder) PDate(pDate string) *MsgProcessDataBuilder {
	builder.pDate = pDate
	builder.pDateFlag = true
	return builder
}

// 部门id
//
// 示例值：123456
func (builder *MsgProcessDataBuilder) DepartmentId(departmentId string) *MsgProcessDataBuilder {
	builder.departmentId = departmentId
	builder.departmentIdFlag = true
	return builder
}

// 部门路径
//
// 示例值：test/subtest
func (builder *MsgProcessDataBuilder) DepartmentPath(departmentPath string) *MsgProcessDataBuilder {
	builder.departmentPath = departmentPath
	builder.departmentPathFlag = true
	return builder
}

// 人均重要消息 12 小时阅读率
//
// 示例值：0.50
func (builder *MsgProcessDataBuilder) AvgImpMsgReadRate12h(avgImpMsgReadRate12h float64) *MsgProcessDataBuilder {
	builder.avgImpMsgReadRate12h = avgImpMsgReadRate12h
	builder.avgImpMsgReadRate12hFlag = true
	return builder
}

// 人均 12 小时阅读率
//
// 示例值：0.50
func (builder *MsgProcessDataBuilder) MsgReadRate12h(msgReadRate12h float64) *MsgProcessDataBuilder {
	builder.msgReadRate12h = msgReadRate12h
	builder.msgReadRate12hFlag = true
	return builder
}

// 人均接收消息数
//
// 示例值：50
func (builder *MsgProcessDataBuilder) AvgReceiveMsgCnt(avgReceiveMsgCnt float64) *MsgProcessDataBuilder {
	builder.avgReceiveMsgCnt = avgReceiveMsgCnt
	builder.avgReceiveMsgCntFlag = true
	return builder
}

// 人均阅读消息数
//
// 示例值：50
func (builder *MsgProcessDataBuilder) AvgReadMsgCnt(avgReadMsgCnt float64) *MsgProcessDataBuilder {
	builder.avgReadMsgCnt = avgReadMsgCnt
	builder.avgReadMsgCntFlag = true
	return builder
}

// 人均重要消息阅读数
//
// 示例值：50
func (builder *MsgProcessDataBuilder) AvgImpReadMsgCnt(avgImpReadMsgCnt float64) *MsgProcessDataBuilder {
	builder.avgImpReadMsgCnt = avgImpReadMsgCnt
	builder.avgImpReadMsgCntFlag = true
	return builder
}

// 人均重要消息接收数
//
// 示例值：50
func (builder *MsgProcessDataBuilder) AvgImpReceiveMsgCnt(avgImpReceiveMsgCnt float64) *MsgProcessDataBuilder {
	builder.avgImpReceiveMsgCnt = avgImpReceiveMsgCnt
	builder.avgImpReceiveMsgCntFlag = true
	return builder
}

// 信息高负载员工占比
//
// 示例值：0.50
func (builder *MsgProcessDataBuilder) HighLoadRate(highLoadRate float64) *MsgProcessDataBuilder {
	builder.highLoadRate = highLoadRate
	builder.highLoadRateFlag = true
	return builder
}

// 大群（100人以上）消息占比
//
// 示例值：0.50
func (builder *MsgProcessDataBuilder) BigGroupMsgRate(bigGroupMsgRate float64) *MsgProcessDataBuilder {
	builder.bigGroupMsgRate = bigGroupMsgRate
	builder.bigGroupMsgRateFlag = true
	return builder
}

// 大群消息中话题消息占比
//
// 示例值：0.50
func (builder *MsgProcessDataBuilder) BigGroupTopicMsgRate(bigGroupTopicMsgRate float64) *MsgProcessDataBuilder {
	builder.bigGroupTopicMsgRate = bigGroupTopicMsgRate
	builder.bigGroupTopicMsgRateFlag = true
	return builder
}

// 人均接收 bot 消息数
//
// 示例值：0.50
func (builder *MsgProcessDataBuilder) AvgReceiveBotMsgCnt(avgReceiveBotMsgCnt float64) *MsgProcessDataBuilder {
	builder.avgReceiveBotMsgCnt = avgReceiveBotMsgCnt
	builder.avgReceiveBotMsgCntFlag = true
	return builder
}

// 重要 bot 消息 12 小时阅读率
//
// 示例值：0.50
func (builder *MsgProcessDataBuilder) AvgBotImpMsgReadRate12h(avgBotImpMsgReadRate12h float64) *MsgProcessDataBuilder {
	builder.avgBotImpMsgReadRate12h = avgBotImpMsgReadRate12h
	builder.avgBotImpMsgReadRate12hFlag = true
	return builder
}

// 接收 bot 消息占比
//
// 示例值：0.50
func (builder *MsgProcessDataBuilder) ReceiveBotMsgRate(receiveBotMsgRate float64) *MsgProcessDataBuilder {
	builder.receiveBotMsgRate = receiveBotMsgRate
	builder.receiveBotMsgRateFlag = true
	return builder
}

// 会话盒子使用率
//
// 示例值：0.50
func (builder *MsgProcessDataBuilder) UseChatBoxRate(useChatBoxRate float64) *MsgProcessDataBuilder {
	builder.useChatBoxRate = useChatBoxRate
	builder.useChatBoxRateFlag = true
	return builder
}

// 接收消息中 mute 消息占比
//
// 示例值：0.50
func (builder *MsgProcessDataBuilder) ReceiveMuteMsgRate(receiveMuteMsgRate float64) *MsgProcessDataBuilder {
	builder.receiveMuteMsgRate = receiveMuteMsgRate
	builder.receiveMuteMsgRateFlag = true
	return builder
}

// 阅读消息中 mute 消息占比
//
// 示例值：0.50
func (builder *MsgProcessDataBuilder) ReadMuteMsgRate(readMuteMsgRate float64) *MsgProcessDataBuilder {
	builder.readMuteMsgRate = readMuteMsgRate
	builder.readMuteMsgRateFlag = true
	return builder
}

// 人均有重要消息的会话数
//
// 示例值：50
func (builder *MsgProcessDataBuilder) AvgReceiveImpMsgChatCnt(avgReceiveImpMsgChatCnt float64) *MsgProcessDataBuilder {
	builder.avgReceiveImpMsgChatCnt = avgReceiveImpMsgChatCnt
	builder.avgReceiveImpMsgChatCntFlag = true
	return builder
}

func (builder *MsgProcessDataBuilder) Build() *MsgProcessData {
	req := &MsgProcessData{}
	if builder.pDateFlag {
		req.PDate = &builder.pDate

	}
	if builder.departmentIdFlag {
		req.DepartmentId = &builder.departmentId

	}
	if builder.departmentPathFlag {
		req.DepartmentPath = &builder.departmentPath

	}
	if builder.avgImpMsgReadRate12hFlag {
		req.AvgImpMsgReadRate12h = &builder.avgImpMsgReadRate12h

	}
	if builder.msgReadRate12hFlag {
		req.MsgReadRate12h = &builder.msgReadRate12h

	}
	if builder.avgReceiveMsgCntFlag {
		req.AvgReceiveMsgCnt = &builder.avgReceiveMsgCnt

	}
	if builder.avgReadMsgCntFlag {
		req.AvgReadMsgCnt = &builder.avgReadMsgCnt

	}
	if builder.avgImpReadMsgCntFlag {
		req.AvgImpReadMsgCnt = &builder.avgImpReadMsgCnt

	}
	if builder.avgImpReceiveMsgCntFlag {
		req.AvgImpReceiveMsgCnt = &builder.avgImpReceiveMsgCnt

	}
	if builder.highLoadRateFlag {
		req.HighLoadRate = &builder.highLoadRate

	}
	if builder.bigGroupMsgRateFlag {
		req.BigGroupMsgRate = &builder.bigGroupMsgRate

	}
	if builder.bigGroupTopicMsgRateFlag {
		req.BigGroupTopicMsgRate = &builder.bigGroupTopicMsgRate

	}
	if builder.avgReceiveBotMsgCntFlag {
		req.AvgReceiveBotMsgCnt = &builder.avgReceiveBotMsgCnt

	}
	if builder.avgBotImpMsgReadRate12hFlag {
		req.AvgBotImpMsgReadRate12h = &builder.avgBotImpMsgReadRate12h

	}
	if builder.receiveBotMsgRateFlag {
		req.ReceiveBotMsgRate = &builder.receiveBotMsgRate

	}
	if builder.useChatBoxRateFlag {
		req.UseChatBoxRate = &builder.useChatBoxRate

	}
	if builder.receiveMuteMsgRateFlag {
		req.ReceiveMuteMsgRate = &builder.receiveMuteMsgRate

	}
	if builder.readMuteMsgRateFlag {
		req.ReadMuteMsgRate = &builder.readMuteMsgRate

	}
	if builder.avgReceiveImpMsgChatCntFlag {
		req.AvgReceiveImpMsgChatCnt = &builder.avgReceiveImpMsgChatCnt

	}
	return req
}

type Operator struct {
	OperatorId   *string `json:"operator_id,omitempty"`   // 操作人ID
	OperatorType *string `json:"operator_type,omitempty"` // 操作人身份，用户或应用
}

type OperatorBuilder struct {
	operatorId       string // 操作人ID
	operatorIdFlag   bool
	operatorType     string // 操作人身份，用户或应用
	operatorTypeFlag bool
}

func NewOperatorBuilder() *OperatorBuilder {
	builder := &OperatorBuilder{}
	return builder
}

// 操作人ID
//
// 示例值：ou_ff0b7ba35fb********67dfc8b885136
func (builder *OperatorBuilder) OperatorId(operatorId string) *OperatorBuilder {
	builder.operatorId = operatorId
	builder.operatorIdFlag = true
	return builder
}

// 操作人身份，用户或应用
//
// 示例值：app/user
func (builder *OperatorBuilder) OperatorType(operatorType string) *OperatorBuilder {
	builder.operatorType = operatorType
	builder.operatorTypeFlag = true
	return builder
}

func (builder *OperatorBuilder) Build() *Operator {
	req := &Operator{}
	if builder.operatorIdFlag {
		req.OperatorId = &builder.operatorId

	}
	if builder.operatorTypeFlag {
		req.OperatorType = &builder.operatorType

	}
	return req
}

type Pin struct {
	MessageId      *string `json:"message_id,omitempty"`       // Pin的消息ID
	ChatId         *string `json:"chat_id,omitempty"`          // Pin消息所在的群聊ID
	OperatorId     *string `json:"operator_id,omitempty"`      // Pin的操作人ID
	OperatorIdType *string `json:"operator_id_type,omitempty"` // Pin的操作人ID类型。当Pin的操作人为用户时，为==open_id==；当Pin的操作人为机器人时，为==app_id==
	CreateTime     *string `json:"create_time,omitempty"`      // Pin的创建时间（毫秒级时间戳）
}

type PinBuilder struct {
	messageId          string // Pin的消息ID
	messageIdFlag      bool
	chatId             string // Pin消息所在的群聊ID
	chatIdFlag         bool
	operatorId         string // Pin的操作人ID
	operatorIdFlag     bool
	operatorIdType     string // Pin的操作人ID类型。当Pin的操作人为用户时，为==open_id==；当Pin的操作人为机器人时，为==app_id==
	operatorIdTypeFlag bool
	createTime         string // Pin的创建时间（毫秒级时间戳）
	createTimeFlag     bool
}

func NewPinBuilder() *PinBuilder {
	builder := &PinBuilder{}
	return builder
}

// Pin的消息ID
//
// 示例值：om_dc13264520392913993dd051dba21dcf
func (builder *PinBuilder) MessageId(messageId string) *PinBuilder {
	builder.messageId = messageId
	builder.messageIdFlag = true
	return builder
}

// Pin消息所在的群聊ID
//
// 示例值：oc_a0553eda9014c201e6969b478895c230
func (builder *PinBuilder) ChatId(chatId string) *PinBuilder {
	builder.chatId = chatId
	builder.chatIdFlag = true
	return builder
}

// Pin的操作人ID
//
// 示例值：ou_7d8a6e6df7621556ce0d21922b676706ccs
func (builder *PinBuilder) OperatorId(operatorId string) *PinBuilder {
	builder.operatorId = operatorId
	builder.operatorIdFlag = true
	return builder
}

// Pin的操作人ID类型。当Pin的操作人为用户时，为==open_id==；当Pin的操作人为机器人时，为==app_id==
//
// 示例值：open_id
func (builder *PinBuilder) OperatorIdType(operatorIdType string) *PinBuilder {
	builder.operatorIdType = operatorIdType
	builder.operatorIdTypeFlag = true
	return builder
}

// Pin的创建时间（毫秒级时间戳）
//
// 示例值：1615380573211
func (builder *PinBuilder) CreateTime(createTime string) *PinBuilder {
	builder.createTime = createTime
	builder.createTimeFlag = true
	return builder
}

func (builder *PinBuilder) Build() *Pin {
	req := &Pin{}
	if builder.messageIdFlag {
		req.MessageId = &builder.messageId

	}
	if builder.chatIdFlag {
		req.ChatId = &builder.chatId

	}
	if builder.operatorIdFlag {
		req.OperatorId = &builder.operatorId

	}
	if builder.operatorIdTypeFlag {
		req.OperatorIdType = &builder.operatorIdType

	}
	if builder.createTimeFlag {
		req.CreateTime = &builder.createTime

	}
	return req
}

type PinedMessage struct {
	Pin     *Pin     `json:"pin,omitempty"`     // Pin的操作信息
	Message *Message `json:"message,omitempty"` // 被Pin的消息实体
}

type PinedMessageBuilder struct {
	pin         *Pin // Pin的操作信息
	pinFlag     bool
	message     *Message // 被Pin的消息实体
	messageFlag bool
}

func NewPinedMessageBuilder() *PinedMessageBuilder {
	builder := &PinedMessageBuilder{}
	return builder
}

// Pin的操作信息
//
// 示例值：
func (builder *PinedMessageBuilder) Pin(pin *Pin) *PinedMessageBuilder {
	builder.pin = pin
	builder.pinFlag = true
	return builder
}

// 被Pin的消息实体
//
// 示例值：
func (builder *PinedMessageBuilder) Message(message *Message) *PinedMessageBuilder {
	builder.message = message
	builder.messageFlag = true
	return builder
}

func (builder *PinedMessageBuilder) Build() *PinedMessage {
	req := &PinedMessage{}
	if builder.pinFlag {
		req.Pin = builder.pin
	}
	if builder.messageFlag {
		req.Message = builder.message
	}
	return req
}

type ReadUser struct {
	UserIdType *string `json:"user_id_type,omitempty"` // 用户id类型
	UserId     *string `json:"user_id,omitempty"`      // 用户id
	Timestamp  *string `json:"timestamp,omitempty"`    // 阅读时间
	TenantKey  *string `json:"tenant_key,omitempty"`   // tenant key
}

type ReadUserBuilder struct {
	userIdType     string // 用户id类型
	userIdTypeFlag bool
	userId         string // 用户id
	userIdFlag     bool
	timestamp      string // 阅读时间
	timestampFlag  bool
	tenantKey      string // tenant key
	tenantKeyFlag  bool
}

func NewReadUserBuilder() *ReadUserBuilder {
	builder := &ReadUserBuilder{}
	return builder
}

// 用户id类型
//
// 示例值：open_id
func (builder *ReadUserBuilder) UserIdType(userIdType string) *ReadUserBuilder {
	builder.userIdType = userIdType
	builder.userIdTypeFlag = true
	return builder
}

// 用户id
//
// 示例值：ou_9b851f7b51a9d58d109982337c46f3de
func (builder *ReadUserBuilder) UserId(userId string) *ReadUserBuilder {
	builder.userId = userId
	builder.userIdFlag = true
	return builder
}

// 阅读时间
//
// 示例值：1609484183000
func (builder *ReadUserBuilder) Timestamp(timestamp string) *ReadUserBuilder {
	builder.timestamp = timestamp
	builder.timestampFlag = true
	return builder
}

// tenant key
//
// 示例值：736588c9260f175e
func (builder *ReadUserBuilder) TenantKey(tenantKey string) *ReadUserBuilder {
	builder.tenantKey = tenantKey
	builder.tenantKeyFlag = true
	return builder
}

func (builder *ReadUserBuilder) Build() *ReadUser {
	req := &ReadUser{}
	if builder.userIdTypeFlag {
		req.UserIdType = &builder.userIdType

	}
	if builder.userIdFlag {
		req.UserId = &builder.userId

	}
	if builder.timestampFlag {
		req.Timestamp = &builder.timestamp

	}
	if builder.tenantKeyFlag {
		req.TenantKey = &builder.tenantKey

	}
	return req
}

type Sender struct {
	Id         *string `json:"id,omitempty"`          // 该字段标识发送者的id
	IdType     *string `json:"id_type,omitempty"`     // 该字段标识发送者的id类型
	SenderType *string `json:"sender_type,omitempty"` // 该字段标识发送者的类型
	TenantKey  *string `json:"tenant_key,omitempty"`  // 为租户在飞书上的唯一标识，用来换取对应的tenant_access_token，也可以用作租户在应用里面的唯一标识
}

type SenderBuilder struct {
	id             string // 该字段标识发送者的id
	idFlag         bool
	idType         string // 该字段标识发送者的id类型
	idTypeFlag     bool
	senderType     string // 该字段标识发送者的类型
	senderTypeFlag bool
	tenantKey      string // 为租户在飞书上的唯一标识，用来换取对应的tenant_access_token，也可以用作租户在应用里面的唯一标识
	tenantKeyFlag  bool
}

func NewSenderBuilder() *SenderBuilder {
	builder := &SenderBuilder{}
	return builder
}

// 该字段标识发送者的id
//
// 示例值：cli_9f427eec54ae901b
func (builder *SenderBuilder) Id(id string) *SenderBuilder {
	builder.id = id
	builder.idFlag = true
	return builder
}

// 该字段标识发送者的id类型
//
// 示例值：app_id
func (builder *SenderBuilder) IdType(idType string) *SenderBuilder {
	builder.idType = idType
	builder.idTypeFlag = true
	return builder
}

// 该字段标识发送者的类型
//
// 示例值：app
func (builder *SenderBuilder) SenderType(senderType string) *SenderBuilder {
	builder.senderType = senderType
	builder.senderTypeFlag = true
	return builder
}

// 为租户在飞书上的唯一标识，用来换取对应的tenant_access_token，也可以用作租户在应用里面的唯一标识
//
// 示例值：736588c9260f175e
func (builder *SenderBuilder) TenantKey(tenantKey string) *SenderBuilder {
	builder.tenantKey = tenantKey
	builder.tenantKeyFlag = true
	return builder
}

func (builder *SenderBuilder) Build() *Sender {
	req := &Sender{}
	if builder.idFlag {
		req.Id = &builder.id

	}
	if builder.idTypeFlag {
		req.IdType = &builder.idType

	}
	if builder.senderTypeFlag {
		req.SenderType = &builder.senderType

	}
	if builder.tenantKeyFlag {
		req.TenantKey = &builder.tenantKey

	}
	return req
}

type SpecialFocus struct {
	Id     *string `json:"id,omitempty"`      // 成员ID
	IdType *string `json:"id_type,omitempty"` // 成员ID类型。根据member_id_type参数返回`open_id`、`user_id`或`union_id`类型的用户ID；机器人返回`app_id`
}

type SpecialFocusBuilder struct {
	id         string // 成员ID
	idFlag     bool
	idType     string // 成员ID类型。根据member_id_type参数返回`open_id`、`user_id`或`union_id`类型的用户ID；机器人返回`app_id`
	idTypeFlag bool
}

func NewSpecialFocusBuilder() *SpecialFocusBuilder {
	builder := &SpecialFocusBuilder{}
	return builder
}

// 成员ID
//
// 示例值：ou_155184d1e73cbfb8973e5a9e698e74f2
func (builder *SpecialFocusBuilder) Id(id string) *SpecialFocusBuilder {
	builder.id = id
	builder.idFlag = true
	return builder
}

// 成员ID类型。根据member_id_type参数返回`open_id`、`user_id`或`union_id`类型的用户ID；机器人返回`app_id`
//
// 示例值：
func (builder *SpecialFocusBuilder) IdType(idType string) *SpecialFocusBuilder {
	builder.idType = idType
	builder.idTypeFlag = true
	return builder
}

func (builder *SpecialFocusBuilder) Build() *SpecialFocus {
	req := &SpecialFocus{}
	if builder.idFlag {
		req.Id = &builder.id

	}
	if builder.idTypeFlag {
		req.IdType = &builder.idType

	}
	return req
}

type SpecialFocusUnread struct {
	Id          *string `json:"id,omitempty"`           // 成员ID
	IdType      *string `json:"id_type,omitempty"`      // 成员ID类型。根据 ==member_id_type== 参数返回`open_id`、`user_id`或`union_id`类型的用户ID；机器人返回`app_id`
	UnreadCount *string `json:"unread_count,omitempty"` // 未读数
}

type SpecialFocusUnreadBuilder struct {
	id              string // 成员ID
	idFlag          bool
	idType          string // 成员ID类型。根据 ==member_id_type== 参数返回`open_id`、`user_id`或`union_id`类型的用户ID；机器人返回`app_id`
	idTypeFlag      bool
	unreadCount     string // 未读数
	unreadCountFlag bool
}

func NewSpecialFocusUnreadBuilder() *SpecialFocusUnreadBuilder {
	builder := &SpecialFocusUnreadBuilder{}
	return builder
}

// 成员ID
//
// 示例值：
func (builder *SpecialFocusUnreadBuilder) Id(id string) *SpecialFocusUnreadBuilder {
	builder.id = id
	builder.idFlag = true
	return builder
}

// 成员ID类型。根据 ==member_id_type== 参数返回`open_id`、`user_id`或`union_id`类型的用户ID；机器人返回`app_id`
//
// 示例值：
func (builder *SpecialFocusUnreadBuilder) IdType(idType string) *SpecialFocusUnreadBuilder {
	builder.idType = idType
	builder.idTypeFlag = true
	return builder
}

// 未读数
//
// 示例值：
func (builder *SpecialFocusUnreadBuilder) UnreadCount(unreadCount string) *SpecialFocusUnreadBuilder {
	builder.unreadCount = unreadCount
	builder.unreadCountFlag = true
	return builder
}

func (builder *SpecialFocusUnreadBuilder) Build() *SpecialFocusUnread {
	req := &SpecialFocusUnread{}
	if builder.idFlag {
		req.Id = &builder.id

	}
	if builder.idTypeFlag {
		req.IdType = &builder.idType

	}
	if builder.unreadCountFlag {
		req.UnreadCount = &builder.unreadCount

	}
	return req
}

type Toolkit struct {
	ToolkitId    *string              `json:"toolkit_id,omitempty"`    // 快捷组件ID
	ImageKey     *string              `json:"image_key,omitempty"`     // 快捷组件小图标 ;;上传message类型的图片[上传图片](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/image/create)
	ToolkitName  *string              `json:"toolkit_name,omitempty"`  // 快捷组件名称
	I18nName     *I18nNames           `json:"i18n_name,omitempty"`     // 国际化名称
	ToolkitType  *string              `json:"toolkit_type,omitempty"`  // 快捷组件类型
	RedirectLink *ToolkitRedirectLink `json:"redirect_link,omitempty"` // 跳转类型快捷组件
	Callback     *ToolkitCallback     `json:"callback,omitempty"`      // 回调类型快捷组件
}

type ToolkitBuilder struct {
	toolkitId        string // 快捷组件ID
	toolkitIdFlag    bool
	imageKey         string // 快捷组件小图标 ;;上传message类型的图片[上传图片](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/image/create)
	imageKeyFlag     bool
	toolkitName      string // 快捷组件名称
	toolkitNameFlag  bool
	i18nName         *I18nNames // 国际化名称
	i18nNameFlag     bool
	toolkitType      string // 快捷组件类型
	toolkitTypeFlag  bool
	redirectLink     *ToolkitRedirectLink // 跳转类型快捷组件
	redirectLinkFlag bool
	callback         *ToolkitCallback // 回调类型快捷组件
	callbackFlag     bool
}

func NewToolkitBuilder() *ToolkitBuilder {
	builder := &ToolkitBuilder{}
	return builder
}

// 快捷组件ID
//
// 示例值：7101214603622940671
func (builder *ToolkitBuilder) ToolkitId(toolkitId string) *ToolkitBuilder {
	builder.toolkitId = toolkitId
	builder.toolkitIdFlag = true
	return builder
}

// 快捷组件小图标 ;;上传message类型的图片[上传图片](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/image/create)
//
// 示例值：img_v2_2995edd9-a22c-44ca-9559-71bbc1e661bg
func (builder *ToolkitBuilder) ImageKey(imageKey string) *ToolkitBuilder {
	builder.imageKey = imageKey
	builder.imageKeyFlag = true
	return builder
}

// 快捷组件名称
//
// 示例值：群组
func (builder *ToolkitBuilder) ToolkitName(toolkitName string) *ToolkitBuilder {
	builder.toolkitName = toolkitName
	builder.toolkitNameFlag = true
	return builder
}

// 国际化名称
//
// 示例值：
func (builder *ToolkitBuilder) I18nName(i18nName *I18nNames) *ToolkitBuilder {
	builder.i18nName = i18nName
	builder.i18nNameFlag = true
	return builder
}

// 快捷组件类型
//
// 示例值：redirect_link
func (builder *ToolkitBuilder) ToolkitType(toolkitType string) *ToolkitBuilder {
	builder.toolkitType = toolkitType
	builder.toolkitTypeFlag = true
	return builder
}

// 跳转类型快捷组件
//
// 示例值：
func (builder *ToolkitBuilder) RedirectLink(redirectLink *ToolkitRedirectLink) *ToolkitBuilder {
	builder.redirectLink = redirectLink
	builder.redirectLinkFlag = true
	return builder
}

// 回调类型快捷组件
//
// 示例值：
func (builder *ToolkitBuilder) Callback(callback *ToolkitCallback) *ToolkitBuilder {
	builder.callback = callback
	builder.callbackFlag = true
	return builder
}

func (builder *ToolkitBuilder) Build() *Toolkit {
	req := &Toolkit{}
	if builder.toolkitIdFlag {
		req.ToolkitId = &builder.toolkitId

	}
	if builder.imageKeyFlag {
		req.ImageKey = &builder.imageKey

	}
	if builder.toolkitNameFlag {
		req.ToolkitName = &builder.toolkitName

	}
	if builder.i18nNameFlag {
		req.I18nName = builder.i18nName
	}
	if builder.toolkitTypeFlag {
		req.ToolkitType = &builder.toolkitType

	}
	if builder.redirectLinkFlag {
		req.RedirectLink = builder.redirectLink
	}
	if builder.callbackFlag {
		req.Callback = builder.callback
	}
	return req
}

type ToolkitCallback struct {
	Webhook   *string `json:"webhook,omitempty"`    // 回调地址
	ActionKey *string `json:"action_key,omitempty"` // 开发者自己识别的action_key，标识快捷组件的作用，回调时会把该字段带到回调地址
}

type ToolkitCallbackBuilder struct {
	webhook       string // 回调地址
	webhookFlag   bool
	actionKey     string // 开发者自己识别的action_key，标识快捷组件的作用，回调时会把该字段带到回调地址
	actionKeyFlag bool
}

func NewToolkitCallbackBuilder() *ToolkitCallbackBuilder {
	builder := &ToolkitCallbackBuilder{}
	return builder
}

// 回调地址
//
// 示例值：https://***.com/
func (builder *ToolkitCallbackBuilder) Webhook(webhook string) *ToolkitCallbackBuilder {
	builder.webhook = webhook
	builder.webhookFlag = true
	return builder
}

// 开发者自己识别的action_key，标识快捷组件的作用，回调时会把该字段带到回调地址
//
// 示例值：SendMessage
func (builder *ToolkitCallbackBuilder) ActionKey(actionKey string) *ToolkitCallbackBuilder {
	builder.actionKey = actionKey
	builder.actionKeyFlag = true
	return builder
}

func (builder *ToolkitCallbackBuilder) Build() *ToolkitCallback {
	req := &ToolkitCallback{}
	if builder.webhookFlag {
		req.Webhook = &builder.webhook

	}
	if builder.actionKeyFlag {
		req.ActionKey = &builder.actionKey

	}
	return req
}

type ToolkitRedirectLink struct {
	Url *string `json:"url,omitempty"` // 跳转url
}

type ToolkitRedirectLinkBuilder struct {
	url     string // 跳转url
	urlFlag bool
}

func NewToolkitRedirectLinkBuilder() *ToolkitRedirectLinkBuilder {
	builder := &ToolkitRedirectLinkBuilder{}
	return builder
}

// 跳转url
//
// 示例值：https://applink.feishu.cn/client/calendar/open
func (builder *ToolkitRedirectLinkBuilder) Url(url string) *ToolkitRedirectLinkBuilder {
	builder.url = url
	builder.urlFlag = true
	return builder
}

func (builder *ToolkitRedirectLinkBuilder) Build() *ToolkitRedirectLink {
	req := &ToolkitRedirectLink{}
	if builder.urlFlag {
		req.Url = &builder.url

	}
	return req
}

type UrgentReceivers struct {
	UserIdList []string `json:"user_id_list,omitempty"` // 目标用户的ID，列表不可为空;;**注意**：;请确保所填的用户ID正确，并且用户在加急消息所在的群组中
}

type UrgentReceiversBuilder struct {
	userIdList     []string // 目标用户的ID，列表不可为空;;**注意**：;请确保所填的用户ID正确，并且用户在加急消息所在的群组中
	userIdListFlag bool
}

func NewUrgentReceiversBuilder() *UrgentReceiversBuilder {
	builder := &UrgentReceiversBuilder{}
	return builder
}

// 目标用户的ID，列表不可为空;;**注意**：;请确保所填的用户ID正确，并且用户在加急消息所在的群组中
//
// 示例值：["ou_6yf8af6bgb9100449565764t3382b168"]
func (builder *UrgentReceiversBuilder) UserIdList(userIdList []string) *UrgentReceiversBuilder {
	builder.userIdList = userIdList
	builder.userIdListFlag = true
	return builder
}

func (builder *UrgentReceiversBuilder) Build() *UrgentReceivers {
	req := &UrgentReceivers{}
	if builder.userIdListFlag {
		req.UserIdList = builder.userIdList
	}
	return req
}

type UserId struct {
	UserId  *string `json:"user_id,omitempty"`  //
	OpenId  *string `json:"open_id,omitempty"`  //
	UnionId *string `json:"union_id,omitempty"` //
}

type UserIdBuilder struct {
	userId      string //
	userIdFlag  bool
	openId      string //
	openIdFlag  bool
	unionId     string //
	unionIdFlag bool
}

func NewUserIdBuilder() *UserIdBuilder {
	builder := &UserIdBuilder{}
	return builder
}

//
//
// 示例值：
func (builder *UserIdBuilder) UserId(userId string) *UserIdBuilder {
	builder.userId = userId
	builder.userIdFlag = true
	return builder
}

//
//
// 示例值：
func (builder *UserIdBuilder) OpenId(openId string) *UserIdBuilder {
	builder.openId = openId
	builder.openIdFlag = true
	return builder
}

//
//
// 示例值：
func (builder *UserIdBuilder) UnionId(unionId string) *UserIdBuilder {
	builder.unionId = unionId
	builder.unionIdFlag = true
	return builder
}

func (builder *UserIdBuilder) Build() *UserId {
	req := &UserId{}
	if builder.userIdFlag {
		req.UserId = &builder.userId

	}
	if builder.openIdFlag {
		req.OpenId = &builder.openId

	}
	if builder.unionIdFlag {
		req.UnionId = &builder.unionId

	}
	return req
}

type DeleteBatchMessageReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewDeleteBatchMessageReqBuilder() *DeleteBatchMessageReqBuilder {
	builder := &DeleteBatchMessageReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 待撤回的批量消息的ID，为[批量发送消息](https://open.feishu.cn/document/ukTMukTMukTM/ucDO1EjL3gTNx4yN4UTM)接口返回值中的`message_id`字段，用于标识一次批量发送消息请求。
//
// 示例值：bm-dc13264520392913993dd051dba21dcf
func (builder *DeleteBatchMessageReqBuilder) BatchMessageId(batchMessageId string) *DeleteBatchMessageReqBuilder {
	builder.apiReq.PathParams.Set("batch_message_id", fmt.Sprint(batchMessageId))
	return builder
}

func (builder *DeleteBatchMessageReqBuilder) Build() *DeleteBatchMessageReq {
	req := &DeleteBatchMessageReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	return req
}

type DeleteBatchMessageReq struct {
	apiReq *larkcore.ApiReq
}

type DeleteBatchMessageResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *DeleteBatchMessageResp) Success() bool {
	return resp.Code == 0
}

type GetProgressBatchMessageReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewGetProgressBatchMessageReqBuilder() *GetProgressBatchMessageReqBuilder {
	builder := &GetProgressBatchMessageReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 待查询的批量消息的ID，通过调用[批量发送消息接口](	https://open.feishu.cn/document/ukTMukTMukTM/ucDO1EjL3gTNx4yN4UTM)的返回值中得到
//
// 示例值：bm-0b3d5d1b2df7c6d5dbd1abe2c91e2217
func (builder *GetProgressBatchMessageReqBuilder) BatchMessageId(batchMessageId string) *GetProgressBatchMessageReqBuilder {
	builder.apiReq.PathParams.Set("batch_message_id", fmt.Sprint(batchMessageId))
	return builder
}

func (builder *GetProgressBatchMessageReqBuilder) Build() *GetProgressBatchMessageReq {
	req := &GetProgressBatchMessageReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	return req
}

type GetProgressBatchMessageReq struct {
	apiReq *larkcore.ApiReq
}

type GetProgressBatchMessageRespData struct {
	BatchMessageSendProgress   *BatchMessageSendProgress   `json:"batch_message_send_progress,omitempty"`   // 消息发送进度
	BatchMessageRecallProgress *BatchMessageRecallProgress `json:"batch_message_recall_progress,omitempty"` // 消息撤回进度
}

type GetProgressBatchMessageResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *GetProgressBatchMessageRespData `json:"data"` // 业务数据
}

func (resp *GetProgressBatchMessageResp) Success() bool {
	return resp.Code == 0
}

type ReadUserBatchMessageReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewReadUserBatchMessageReqBuilder() *ReadUserBatchMessageReqBuilder {
	builder := &ReadUserBatchMessageReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 待查询的批量消息的ID，通过调用[批量发送消息接口](	https://open.feishu.cn/document/ukTMukTMukTM/ucDO1EjL3gTNx4yN4UTM)的返回值中得到
//
// 示例值：bm_dc13264520392913993dd051dba21dcf
func (builder *ReadUserBatchMessageReqBuilder) BatchMessageId(batchMessageId string) *ReadUserBatchMessageReqBuilder {
	builder.apiReq.PathParams.Set("batch_message_id", fmt.Sprint(batchMessageId))
	return builder
}

func (builder *ReadUserBatchMessageReqBuilder) Build() *ReadUserBatchMessageReq {
	req := &ReadUserBatchMessageReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	return req
}

type ReadUserBatchMessageReq struct {
	apiReq *larkcore.ApiReq
}

type ReadUserBatchMessageRespData struct {
	ReadUser *BatchMessageReadUser `json:"read_user,omitempty"` // 批量发送消息的用户阅读情况
}

type ReadUserBatchMessageResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *ReadUserBatchMessageRespData `json:"data"` // 业务数据
}

func (resp *ReadUserBatchMessageResp) Success() bool {
	return resp.Code == 0
}

type CreateChatReqBodyBuilder struct {
	avatar                     string // 群头像对应的 Image Key，可通过[上传图片](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/image/create)获取（注意：上传图片的 ==image_type== 需要指定为 ==avatar==）
	avatarFlag                 bool
	name                       string // 群名称;; **注意：** ;- 公开群名称的长度不得少于2个字符;- 私有群若未填写群名称，群名称默认设置为 ”`(无主题)`“
	nameFlag                   bool
	description                string // 群描述
	descriptionFlag            bool
	i18nNames                  *I18nNames // 群国际化名称
	i18nNamesFlag              bool
	ownerId                    string // 创建群时指定的群主，不填时指定建群的机器人为群主。群主 ID值应与查询参数中的 ==user_id_type== 对应，不同 ID 的说明参见[用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction);;**注意**：创建外部群时，必须指定群主
	ownerIdFlag                bool
	userIdList                 []string // 创建群时邀请的群成员，ID 类型在查询参数 ==user_id_type== 中指定;;**注意**：最多同时邀请 50 个用户
	userIdListFlag             bool
	botIdList                  []string // 创建群时邀请的群机器人; ;**注意：** ;- 拉机器人入群请使用`app_id`;- 最多同时邀请5个机器人，并且群组最多容纳 15 个机器人
	botIdListFlag              bool
	chatMode                   string // 群模式;;**可选值有**：;- `group`：群组
	chatModeFlag               bool
	chatType                   string // 群类型;;**可选值有**：;- `private`：私有群;- `public`：公开群
	chatTypeFlag               bool
	external                   bool // 是否是外部群；若群组需要邀请不同租户的用户或机器人，请指定为外部群；
	externalFlag               bool
	joinMessageVisibility      string // 入群消息可见性;;**可选值有**：;- `only_owner`：仅群主和管理员可见;- `all_members`：所有成员可见;- `not_anyone`：任何人均不可见
	joinMessageVisibilityFlag  bool
	leaveMessageVisibility     string // 退群消息可见性;;**可选值有**：;- `only_owner`：仅群主和管理员可见;- `all_members`：所有成员可见;- `not_anyone`：任何人均不可见
	leaveMessageVisibilityFlag bool
	membershipApproval         string // 加群审批;;**可选值有**：;- `no_approval_required`：无需审批;- `approval_required`：需要审批
	membershipApprovalFlag     bool
}

func NewCreateChatReqBodyBuilder() *CreateChatReqBodyBuilder {
	builder := &CreateChatReqBodyBuilder{}
	return builder
}

// 群头像对应的 Image Key，可通过[上传图片](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/image/create)获取（注意：上传图片的 ==image_type== 需要指定为 ==avatar==）
//
//示例值：default-avatar_44ae0ca3-e140-494b-956f-78091e348435
func (builder *CreateChatReqBodyBuilder) Avatar(avatar string) *CreateChatReqBodyBuilder {
	builder.avatar = avatar
	builder.avatarFlag = true
	return builder
}

// 群名称;; **注意：** ;- 公开群名称的长度不得少于2个字符;- 私有群若未填写群名称，群名称默认设置为 ”`(无主题)`“
//
//示例值：测试群名称
func (builder *CreateChatReqBodyBuilder) Name(name string) *CreateChatReqBodyBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

// 群描述
//
//示例值：测试群描述
func (builder *CreateChatReqBodyBuilder) Description(description string) *CreateChatReqBodyBuilder {
	builder.description = description
	builder.descriptionFlag = true
	return builder
}

// 群国际化名称
//
//示例值：
func (builder *CreateChatReqBodyBuilder) I18nNames(i18nNames *I18nNames) *CreateChatReqBodyBuilder {
	builder.i18nNames = i18nNames
	builder.i18nNamesFlag = true
	return builder
}

// 创建群时指定的群主，不填时指定建群的机器人为群主。群主 ID值应与查询参数中的 ==user_id_type== 对应，不同 ID 的说明参见[用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction);;**注意**：创建外部群时，必须指定群主
//
//示例值：4d7a3c6g
func (builder *CreateChatReqBodyBuilder) OwnerId(ownerId string) *CreateChatReqBodyBuilder {
	builder.ownerId = ownerId
	builder.ownerIdFlag = true
	return builder
}

// 创建群时邀请的群成员，ID 类型在查询参数 ==user_id_type== 中指定;;**注意**：最多同时邀请 50 个用户
//
//示例值：["4d7a3c6g"]
func (builder *CreateChatReqBodyBuilder) UserIdList(userIdList []string) *CreateChatReqBodyBuilder {
	builder.userIdList = userIdList
	builder.userIdListFlag = true
	return builder
}

// 创建群时邀请的群机器人; ;**注意：** ;- 拉机器人入群请使用`app_id`;- 最多同时邀请5个机器人，并且群组最多容纳 15 个机器人
//
//示例值：["cli_a10fbf7e94b8d01d"]
func (builder *CreateChatReqBodyBuilder) BotIdList(botIdList []string) *CreateChatReqBodyBuilder {
	builder.botIdList = botIdList
	builder.botIdListFlag = true
	return builder
}

// 群模式;;**可选值有**：;- `group`：群组
//
//示例值：group
func (builder *CreateChatReqBodyBuilder) ChatMode(chatMode string) *CreateChatReqBodyBuilder {
	builder.chatMode = chatMode
	builder.chatModeFlag = true
	return builder
}

// 群类型;;**可选值有**：;- `private`：私有群;- `public`：公开群
//
//示例值：private
func (builder *CreateChatReqBodyBuilder) ChatType(chatType string) *CreateChatReqBodyBuilder {
	builder.chatType = chatType
	builder.chatTypeFlag = true
	return builder
}

// 是否是外部群；若群组需要邀请不同租户的用户或机器人，请指定为外部群；
//
//示例值：false
func (builder *CreateChatReqBodyBuilder) External(external bool) *CreateChatReqBodyBuilder {
	builder.external = external
	builder.externalFlag = true
	return builder
}

// 入群消息可见性;;**可选值有**：;- `only_owner`：仅群主和管理员可见;- `all_members`：所有成员可见;- `not_anyone`：任何人均不可见
//
//示例值：all_members
func (builder *CreateChatReqBodyBuilder) JoinMessageVisibility(joinMessageVisibility string) *CreateChatReqBodyBuilder {
	builder.joinMessageVisibility = joinMessageVisibility
	builder.joinMessageVisibilityFlag = true
	return builder
}

// 退群消息可见性;;**可选值有**：;- `only_owner`：仅群主和管理员可见;- `all_members`：所有成员可见;- `not_anyone`：任何人均不可见
//
//示例值：all_members
func (builder *CreateChatReqBodyBuilder) LeaveMessageVisibility(leaveMessageVisibility string) *CreateChatReqBodyBuilder {
	builder.leaveMessageVisibility = leaveMessageVisibility
	builder.leaveMessageVisibilityFlag = true
	return builder
}

// 加群审批;;**可选值有**：;- `no_approval_required`：无需审批;- `approval_required`：需要审批
//
//示例值：no_approval_required
func (builder *CreateChatReqBodyBuilder) MembershipApproval(membershipApproval string) *CreateChatReqBodyBuilder {
	builder.membershipApproval = membershipApproval
	builder.membershipApprovalFlag = true
	return builder
}

func (builder *CreateChatReqBodyBuilder) Build() *CreateChatReqBody {
	req := &CreateChatReqBody{}
	if builder.avatarFlag {
		req.Avatar = &builder.avatar
	}
	if builder.nameFlag {
		req.Name = &builder.name
	}
	if builder.descriptionFlag {
		req.Description = &builder.description
	}
	if builder.i18nNamesFlag {
		req.I18nNames = builder.i18nNames
	}
	if builder.ownerIdFlag {
		req.OwnerId = &builder.ownerId
	}
	if builder.userIdListFlag {
		req.UserIdList = builder.userIdList
	}
	if builder.botIdListFlag {
		req.BotIdList = builder.botIdList
	}
	if builder.chatModeFlag {
		req.ChatMode = &builder.chatMode
	}
	if builder.chatTypeFlag {
		req.ChatType = &builder.chatType
	}
	if builder.externalFlag {
		req.External = &builder.external
	}
	if builder.joinMessageVisibilityFlag {
		req.JoinMessageVisibility = &builder.joinMessageVisibility
	}
	if builder.leaveMessageVisibilityFlag {
		req.LeaveMessageVisibility = &builder.leaveMessageVisibility
	}
	if builder.membershipApprovalFlag {
		req.MembershipApproval = &builder.membershipApproval
	}
	return req
}

type CreateChatPathReqBodyBuilder struct {
	avatar                     string // 群头像对应的 Image Key，可通过[上传图片](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/image/create)获取（注意：上传图片的 ==image_type== 需要指定为 ==avatar==）
	avatarFlag                 bool
	name                       string // 群名称;; **注意：** ;- 公开群名称的长度不得少于2个字符;- 私有群若未填写群名称，群名称默认设置为 ”`(无主题)`“
	nameFlag                   bool
	description                string // 群描述
	descriptionFlag            bool
	i18nNames                  *I18nNames // 群国际化名称
	i18nNamesFlag              bool
	ownerId                    string // 创建群时指定的群主，不填时指定建群的机器人为群主。群主 ID值应与查询参数中的 ==user_id_type== 对应，不同 ID 的说明参见[用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction);;**注意**：创建外部群时，必须指定群主
	ownerIdFlag                bool
	userIdList                 []string // 创建群时邀请的群成员，ID 类型在查询参数 ==user_id_type== 中指定;;**注意**：最多同时邀请 50 个用户
	userIdListFlag             bool
	botIdList                  []string // 创建群时邀请的群机器人; ;**注意：** ;- 拉机器人入群请使用`app_id`;- 最多同时邀请5个机器人，并且群组最多容纳 15 个机器人
	botIdListFlag              bool
	chatMode                   string // 群模式;;**可选值有**：;- `group`：群组
	chatModeFlag               bool
	chatType                   string // 群类型;;**可选值有**：;- `private`：私有群;- `public`：公开群
	chatTypeFlag               bool
	external                   bool // 是否是外部群；若群组需要邀请不同租户的用户或机器人，请指定为外部群；
	externalFlag               bool
	joinMessageVisibility      string // 入群消息可见性;;**可选值有**：;- `only_owner`：仅群主和管理员可见;- `all_members`：所有成员可见;- `not_anyone`：任何人均不可见
	joinMessageVisibilityFlag  bool
	leaveMessageVisibility     string // 退群消息可见性;;**可选值有**：;- `only_owner`：仅群主和管理员可见;- `all_members`：所有成员可见;- `not_anyone`：任何人均不可见
	leaveMessageVisibilityFlag bool
	membershipApproval         string // 加群审批;;**可选值有**：;- `no_approval_required`：无需审批;- `approval_required`：需要审批
	membershipApprovalFlag     bool
	labels                     []string // 群标签
	labelsFlag                 bool
	toolkitIds                 []string // 群快捷组件列表
	toolkitIdsFlag             bool
}

func NewCreateChatPathReqBodyBuilder() *CreateChatPathReqBodyBuilder {
	builder := &CreateChatPathReqBodyBuilder{}
	return builder
}

// 群头像对应的 Image Key，可通过[上传图片](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/image/create)获取（注意：上传图片的 ==image_type== 需要指定为 ==avatar==）
//
// 示例值：default-avatar_44ae0ca3-e140-494b-956f-78091e348435
func (builder *CreateChatPathReqBodyBuilder) Avatar(avatar string) *CreateChatPathReqBodyBuilder {
	builder.avatar = avatar
	builder.avatarFlag = true
	return builder
}

// 群名称;; **注意：** ;- 公开群名称的长度不得少于2个字符;- 私有群若未填写群名称，群名称默认设置为 ”`(无主题)`“
//
// 示例值：测试群名称
func (builder *CreateChatPathReqBodyBuilder) Name(name string) *CreateChatPathReqBodyBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

// 群描述
//
// 示例值：测试群描述
func (builder *CreateChatPathReqBodyBuilder) Description(description string) *CreateChatPathReqBodyBuilder {
	builder.description = description
	builder.descriptionFlag = true
	return builder
}

// 群国际化名称
//
// 示例值：
func (builder *CreateChatPathReqBodyBuilder) I18nNames(i18nNames *I18nNames) *CreateChatPathReqBodyBuilder {
	builder.i18nNames = i18nNames
	builder.i18nNamesFlag = true
	return builder
}

// 创建群时指定的群主，不填时指定建群的机器人为群主。群主 ID值应与查询参数中的 ==user_id_type== 对应，不同 ID 的说明参见[用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction);;**注意**：创建外部群时，必须指定群主
//
// 示例值：4d7a3c6g
func (builder *CreateChatPathReqBodyBuilder) OwnerId(ownerId string) *CreateChatPathReqBodyBuilder {
	builder.ownerId = ownerId
	builder.ownerIdFlag = true
	return builder
}

// 创建群时邀请的群成员，ID 类型在查询参数 ==user_id_type== 中指定;;**注意**：最多同时邀请 50 个用户
//
// 示例值：["4d7a3c6g"]
func (builder *CreateChatPathReqBodyBuilder) UserIdList(userIdList []string) *CreateChatPathReqBodyBuilder {
	builder.userIdList = userIdList
	builder.userIdListFlag = true
	return builder
}

// 创建群时邀请的群机器人; ;**注意：** ;- 拉机器人入群请使用`app_id`;- 最多同时邀请5个机器人，并且群组最多容纳 15 个机器人
//
// 示例值：["cli_a10fbf7e94b8d01d"]
func (builder *CreateChatPathReqBodyBuilder) BotIdList(botIdList []string) *CreateChatPathReqBodyBuilder {
	builder.botIdList = botIdList
	builder.botIdListFlag = true
	return builder
}

// 群模式;;**可选值有**：;- `group`：群组
//
// 示例值：group
func (builder *CreateChatPathReqBodyBuilder) ChatMode(chatMode string) *CreateChatPathReqBodyBuilder {
	builder.chatMode = chatMode
	builder.chatModeFlag = true
	return builder
}

// 群类型;;**可选值有**：;- `private`：私有群;- `public`：公开群
//
// 示例值：private
func (builder *CreateChatPathReqBodyBuilder) ChatType(chatType string) *CreateChatPathReqBodyBuilder {
	builder.chatType = chatType
	builder.chatTypeFlag = true
	return builder
}

// 是否是外部群；若群组需要邀请不同租户的用户或机器人，请指定为外部群；
//
// 示例值：false
func (builder *CreateChatPathReqBodyBuilder) External(external bool) *CreateChatPathReqBodyBuilder {
	builder.external = external
	builder.externalFlag = true
	return builder
}

// 入群消息可见性;;**可选值有**：;- `only_owner`：仅群主和管理员可见;- `all_members`：所有成员可见;- `not_anyone`：任何人均不可见
//
// 示例值：all_members
func (builder *CreateChatPathReqBodyBuilder) JoinMessageVisibility(joinMessageVisibility string) *CreateChatPathReqBodyBuilder {
	builder.joinMessageVisibility = joinMessageVisibility
	builder.joinMessageVisibilityFlag = true
	return builder
}

// 退群消息可见性;;**可选值有**：;- `only_owner`：仅群主和管理员可见;- `all_members`：所有成员可见;- `not_anyone`：任何人均不可见
//
// 示例值：all_members
func (builder *CreateChatPathReqBodyBuilder) LeaveMessageVisibility(leaveMessageVisibility string) *CreateChatPathReqBodyBuilder {
	builder.leaveMessageVisibility = leaveMessageVisibility
	builder.leaveMessageVisibilityFlag = true
	return builder
}

// 加群审批;;**可选值有**：;- `no_approval_required`：无需审批;- `approval_required`：需要审批
//
// 示例值：no_approval_required
func (builder *CreateChatPathReqBodyBuilder) MembershipApproval(membershipApproval string) *CreateChatPathReqBodyBuilder {
	builder.membershipApproval = membershipApproval
	builder.membershipApprovalFlag = true
	return builder
}

func (builder *CreateChatPathReqBodyBuilder) Build() (*CreateChatReqBody, error) {
	req := &CreateChatReqBody{}
	if builder.avatarFlag {
		req.Avatar = &builder.avatar
	}
	if builder.nameFlag {
		req.Name = &builder.name
	}
	if builder.descriptionFlag {
		req.Description = &builder.description
	}
	if builder.i18nNamesFlag {
		req.I18nNames = builder.i18nNames
	}
	if builder.ownerIdFlag {
		req.OwnerId = &builder.ownerId
	}
	if builder.userIdListFlag {
		req.UserIdList = builder.userIdList
	}
	if builder.botIdListFlag {
		req.BotIdList = builder.botIdList
	}
	if builder.chatModeFlag {
		req.ChatMode = &builder.chatMode
	}
	if builder.chatTypeFlag {
		req.ChatType = &builder.chatType
	}
	if builder.externalFlag {
		req.External = &builder.external
	}
	if builder.joinMessageVisibilityFlag {
		req.JoinMessageVisibility = &builder.joinMessageVisibility
	}
	if builder.leaveMessageVisibilityFlag {
		req.LeaveMessageVisibility = &builder.leaveMessageVisibility
	}
	if builder.membershipApprovalFlag {
		req.MembershipApproval = &builder.membershipApproval
	}
	return req, nil
}

type CreateChatReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *CreateChatReqBody
}

func NewCreateChatReqBuilder() *CreateChatReqBuilder {
	builder := &CreateChatReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 此次调用中使用的用户ID的类型
//
// 示例值：
func (builder *CreateChatReqBuilder) UserIdType(userIdType string) *CreateChatReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

// 如果在请求体的 ==owner_id== 字段指定了某个用户为群主，可以选择是否同时设置创建此群的机器人为管理员，此标志位用于标记是否设置创建群的机器人为管理员
//
// 示例值：false
func (builder *CreateChatReqBuilder) SetBotManager(setBotManager bool) *CreateChatReqBuilder {
	builder.apiReq.QueryParams.Set("set_bot_manager", fmt.Sprint(setBotManager))
	return builder
}

// 由开发者生成的唯一字符串序列，用于创建群组请求去重；持有相同uuid的请求10小时内只可成功创建1个群聊
//
// 示例值：b13g2t38-1jd2-458b-8djf-dtbca5104204
func (builder *CreateChatReqBuilder) Uuid(uuid string) *CreateChatReqBuilder {
	builder.apiReq.QueryParams.Set("uuid", fmt.Sprint(uuid))
	return builder
}

// 创建群并设置群头像、群名、群描述等。
func (builder *CreateChatReqBuilder) Body(body *CreateChatReqBody) *CreateChatReqBuilder {
	builder.body = body
	return builder
}

func (builder *CreateChatReqBuilder) Build() *CreateChatReq {
	req := &CreateChatReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.body
	return req
}

type CreateChatReqBody struct {
	Avatar                 *string    `json:"avatar,omitempty"`                   // 群头像对应的 Image Key，可通过[上传图片](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/image/create)获取（注意：上传图片的 ==image_type== 需要指定为 ==avatar==）
	Name                   *string    `json:"name,omitempty"`                     // 群名称;; **注意：** ;- 公开群名称的长度不得少于2个字符;- 私有群若未填写群名称，群名称默认设置为 ”`(无主题)`“
	Description            *string    `json:"description,omitempty"`              // 群描述
	I18nNames              *I18nNames `json:"i18n_names,omitempty"`               // 群国际化名称
	OwnerId                *string    `json:"owner_id,omitempty"`                 // 创建群时指定的群主，不填时指定建群的机器人为群主。群主 ID值应与查询参数中的 ==user_id_type== 对应，不同 ID 的说明参见[用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction);;**注意**：创建外部群时，必须指定群主
	UserIdList             []string   `json:"user_id_list,omitempty"`             // 创建群时邀请的群成员，ID 类型在查询参数 ==user_id_type== 中指定;;**注意**：最多同时邀请 50 个用户
	BotIdList              []string   `json:"bot_id_list,omitempty"`              // 创建群时邀请的群机器人; ;**注意：** ;- 拉机器人入群请使用`app_id`;- 最多同时邀请5个机器人，并且群组最多容纳 15 个机器人
	ChatMode               *string    `json:"chat_mode,omitempty"`                // 群模式;;**可选值有**：;- `group`：群组
	ChatType               *string    `json:"chat_type,omitempty"`                // 群类型;;**可选值有**：;- `private`：私有群;- `public`：公开群
	External               *bool      `json:"external,omitempty"`                 // 是否是外部群；若群组需要邀请不同租户的用户或机器人，请指定为外部群；
	JoinMessageVisibility  *string    `json:"join_message_visibility,omitempty"`  // 入群消息可见性;;**可选值有**：;- `only_owner`：仅群主和管理员可见;- `all_members`：所有成员可见;- `not_anyone`：任何人均不可见
	LeaveMessageVisibility *string    `json:"leave_message_visibility,omitempty"` // 退群消息可见性;;**可选值有**：;- `only_owner`：仅群主和管理员可见;- `all_members`：所有成员可见;- `not_anyone`：任何人均不可见
	MembershipApproval     *string    `json:"membership_approval,omitempty"`      // 加群审批;;**可选值有**：;- `no_approval_required`：无需审批;- `approval_required`：需要审批

}

type CreateChatReq struct {
	apiReq *larkcore.ApiReq
	Body   *CreateChatReqBody `body:""`
}

type CreateChatRespData struct {
	ChatId                 *string    `json:"chat_id,omitempty"`                  // 群 ID，详情参见：[群ID 说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-id-description)
	Avatar                 *string    `json:"avatar,omitempty"`                   // 群头像 URL
	Name                   *string    `json:"name,omitempty"`                     // 群名称
	Description            *string    `json:"description,omitempty"`              // 群描述
	I18nNames              *I18nNames `json:"i18n_names,omitempty"`               // 群国际化名称
	OwnerId                *string    `json:"owner_id,omitempty"`                 // 群主 ID，ID值与查询参数中的 ==user_id_type== 对应；不同 ID 的说明参见 [用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction);;**注意**：当群主是机器人时，该字段不返回
	OwnerIdType            *string    `json:"owner_id_type,omitempty"`            // 群主 ID 对应的ID类型，与查询参数中的 ==user_id_type== 相同。取值为：`open_id`、`user_id`、`union_id`其中之一;;**注意**：当群主是机器人时，该字段不返回
	AddMemberPermission    *string    `json:"add_member_permission,omitempty"`    // 拉 用户或机器人 入群权限;;**可选值有**：;- `only_owner`：仅群主和管理员;- `all_members`：所有成员
	ShareCardPermission    *string    `json:"share_card_permission,omitempty"`    // 群分享权限;;**可选值有**：;- `allowed`：允许;- `not_allowed`：不允许
	AtAllPermission        *string    `json:"at_all_permission,omitempty"`        // at 所有人权限;;**可选值有**：;- `only_owner`：仅群主和管理员;- `all_members`：所有成员
	EditPermission         *string    `json:"edit_permission,omitempty"`          // 群编辑权限;;**可选值有**：;- `only_owner`：仅群主和管理员;- `all_members`：所有成员
	ChatMode               *string    `json:"chat_mode,omitempty"`                // 群模式;;**可选值有**：;- `group`：群组
	ChatType               *string    `json:"chat_type,omitempty"`                // 群类型;;**可选值有**：;- `private`：私有群;- `public`：公开群
	ChatTag                *string    `json:"chat_tag,omitempty"`                 // 群标签，如有多个，则按照下列顺序返回第一个;;**可选值有**：;- `inner`：内部群;- `tenant`：公司群;- `department`：部门群;- `edu`：教育群;- `meeting`：会议群;- `customer_service`：客服群
	External               *bool      `json:"external,omitempty"`                 // 是否是外部群
	TenantKey              *string    `json:"tenant_key,omitempty"`               // 租户在飞书上的唯一标识，用来换取对应的tenant_access_token，也可以用作租户在应用里面的唯一标识
	JoinMessageVisibility  *string    `json:"join_message_visibility,omitempty"`  // 入群消息可见性;;**可选值有**：;- `only_owner`：仅群主和管理员可见;- `all_members`：所有成员可见;- `not_anyone`：任何人均不可见
	LeaveMessageVisibility *string    `json:"leave_message_visibility,omitempty"` // 出群消息可见性;;**可选值有**：;- `only_owner`：仅群主和管理员可见;- `all_members`：所有成员可见;- `not_anyone`：任何人均不可见
	MembershipApproval     *string    `json:"membership_approval,omitempty"`      // 加群审批;;**可选值有**：;- `no_approval_required`：无需审批;- `approval_required`：需要审批
	ModerationPermission   *string    `json:"moderation_permission,omitempty"`    // 发言权限;;**可选值有**：;- `only_owner`：仅群主和管理员;- `all_members`：所有成员;- `moderator_list`：指定群成员

}

type CreateChatResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *CreateChatRespData `json:"data"` // 业务数据
}

func (resp *CreateChatResp) Success() bool {
	return resp.Code == 0
}

type DeleteChatReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewDeleteChatReqBuilder() *DeleteChatReqBuilder {
	builder := &DeleteChatReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 群 ID，详情参见[群ID 说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-id-description);;**注意**：仅支持群模式为`group`的群组ID
//
// 示例值：oc_a0553eda9014c201e6969b478895c230
func (builder *DeleteChatReqBuilder) ChatId(chatId string) *DeleteChatReqBuilder {
	builder.apiReq.PathParams.Set("chat_id", fmt.Sprint(chatId))
	return builder
}

func (builder *DeleteChatReqBuilder) Build() *DeleteChatReq {
	req := &DeleteChatReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	return req
}

type DeleteChatReq struct {
	apiReq *larkcore.ApiReq
}

type DeleteChatResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *DeleteChatResp) Success() bool {
	return resp.Code == 0
}

type GetChatReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewGetChatReqBuilder() *GetChatReqBuilder {
	builder := &GetChatReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 群 ID，详情参见[群ID 说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-id-description)
//
// 示例值：oc_a0553eda9014c201e6969b478895c230
func (builder *GetChatReqBuilder) ChatId(chatId string) *GetChatReqBuilder {
	builder.apiReq.PathParams.Set("chat_id", fmt.Sprint(chatId))
	return builder
}

// 此次调用中使用的用户ID的类型
//
// 示例值：
func (builder *GetChatReqBuilder) UserIdType(userIdType string) *GetChatReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

func (builder *GetChatReqBuilder) Build() *GetChatReq {
	req := &GetChatReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type GetChatReq struct {
	apiReq *larkcore.ApiReq
}

type GetChatRespData struct {
	Avatar                 *string    `json:"avatar,omitempty"`                   // 群头像 URL
	Name                   *string    `json:"name,omitempty"`                     // 群名称
	Description            *string    `json:"description,omitempty"`              // 群描述
	I18nNames              *I18nNames `json:"i18n_names,omitempty"`               // 群国际化名称
	AddMemberPermission    *string    `json:"add_member_permission,omitempty"`    // 群成员添加权限;;**可选值有**：;- `only_owner`：仅群主和管理员;- `all_members`：所有成员;;**注意**：单聊不返回该字段
	ShareCardPermission    *string    `json:"share_card_permission,omitempty"`    // 群分享权限;;**可选值有**：;- `allowed`：允许;- `not_allowed`：不允许;;**注意**：单聊不返回该字段
	AtAllPermission        *string    `json:"at_all_permission,omitempty"`        // at 所有人权限;;**可选值有**：;- `only_owner`：仅群主和管理员;- `all_members`：所有成员;;**注意**：单聊不返回该字段
	EditPermission         *string    `json:"edit_permission,omitempty"`          // 群编辑权限;;**可选值有**：;- `only_owner`：仅群主和管理员;- `all_members`：所有成员
	OwnerIdType            *string    `json:"owner_id_type,omitempty"`            // 群主 ID 对应的ID类型，与查询参数中的 ==user_id_type== 相同。取值为：`open_id`、`user_id`、`union_id`其中之一;;**注意**：;- 当群主是机器人时不返回该字段;- 单聊不返回该字段
	OwnerId                *string    `json:"owner_id,omitempty"`                 // 群主 ID，ID值与查询参数中的 ==user_id_type== 对应；不同 ID 的说明参见 [用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction);;**注意**：;- 当群主是机器人时不返回该字段;- 单聊不返回该字段
	ChatMode               *string    `json:"chat_mode,omitempty"`                // 群模式;;**可选值有**：;- `group`：群组;- `topic`: 话题;- `p2p`: 单聊
	ChatType               *string    `json:"chat_type,omitempty"`                // 群类型;;**可选值有**：;- `private`：私有群;- `public`：公开群;;**注意**：单聊不返回该字段
	ChatTag                *string    `json:"chat_tag,omitempty"`                 // 群标签，如有多个，则按照下列顺序返回第一个;;**可选值有**：;- `inner`：内部群;- `tenant`：公司群;- `department`：部门群;- `edu`：教育群;- `meeting`：会议群;- `customer_service`：客服群;;**注意**：单聊不返回该字段
	JoinMessageVisibility  *string    `json:"join_message_visibility,omitempty"`  // 入群消息可见性;;**可选值有**：;- `only_owner`：仅群主和管理员可见;- `all_members`：所有成员可见;- `not_anyone`：任何人均不可见;;**注意**：单聊不返回该字段
	LeaveMessageVisibility *string    `json:"leave_message_visibility,omitempty"` // 出群消息可见性;;**可选值有**：;- `only_owner`：仅群主和管理员可见;- `all_members`：所有成员可见;- `not_anyone`：任何人均不可见;;**注意**：单聊不返回该字段
	MembershipApproval     *string    `json:"membership_approval,omitempty"`      // 加群审批;;**可选值有**：;- `no_approval_required`：无需审批;- `approval_required`：需要审批;;**注意**：单聊不返回该字段
	ModerationPermission   *string    `json:"moderation_permission,omitempty"`    // 发言权限;;**可选值有**：;- `only_owner`：仅群主和管理员;- `all_members`：所有成员;- `moderator_list`：指定群成员
	External               *bool      `json:"external,omitempty"`                 // 是否是外部群
	TenantKey              *string    `json:"tenant_key,omitempty"`               // 租户Key，为租户在飞书上的唯一标识，用来换取对应的tenant_access_token，也可以用作租户在应用中的唯一标识
	UserCount              *string    `json:"user_count,omitempty"`               // 群成员人数
	BotCount               *string    `json:"bot_count,omitempty"`                // 群机器人数

}

type GetChatResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *GetChatRespData `json:"data"` // 业务数据
}

func (resp *GetChatResp) Success() bool {
	return resp.Code == 0
}

type LinkChatReqBodyBuilder struct {
	validityPeriod     string // 群分享链接有效时长，可选值week、year、permanently，分别表示7天、1年以及永久有效
	validityPeriodFlag bool
}

func NewLinkChatReqBodyBuilder() *LinkChatReqBodyBuilder {
	builder := &LinkChatReqBodyBuilder{}
	return builder
}

// 群分享链接有效时长，可选值week、year、permanently，分别表示7天、1年以及永久有效
//
//示例值：week
func (builder *LinkChatReqBodyBuilder) ValidityPeriod(validityPeriod string) *LinkChatReqBodyBuilder {
	builder.validityPeriod = validityPeriod
	builder.validityPeriodFlag = true
	return builder
}

func (builder *LinkChatReqBodyBuilder) Build() *LinkChatReqBody {
	req := &LinkChatReqBody{}
	if builder.validityPeriodFlag {
		req.ValidityPeriod = &builder.validityPeriod
	}
	return req
}

type LinkChatPathReqBodyBuilder struct {
	validityPeriod     string // 群分享链接有效时长，可选值week、year、permanently，分别表示7天、1年以及永久有效
	validityPeriodFlag bool
}

func NewLinkChatPathReqBodyBuilder() *LinkChatPathReqBodyBuilder {
	builder := &LinkChatPathReqBodyBuilder{}
	return builder
}

// 群分享链接有效时长，可选值week、year、permanently，分别表示7天、1年以及永久有效
//
// 示例值：week
func (builder *LinkChatPathReqBodyBuilder) ValidityPeriod(validityPeriod string) *LinkChatPathReqBodyBuilder {
	builder.validityPeriod = validityPeriod
	builder.validityPeriodFlag = true
	return builder
}

func (builder *LinkChatPathReqBodyBuilder) Build() (*LinkChatReqBody, error) {
	req := &LinkChatReqBody{}
	if builder.validityPeriodFlag {
		req.ValidityPeriod = &builder.validityPeriod
	}
	return req, nil
}

type LinkChatReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *LinkChatReqBody
}

func NewLinkChatReqBuilder() *LinkChatReqBuilder {
	builder := &LinkChatReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 待获取分享链接的群ID，详情参见[群ID 说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-id-description);;**注意**：单聊、密聊、团队群不支持分享群链接
//
// 示例值：oc_a0553eda9014c201e6969b478895c230
func (builder *LinkChatReqBuilder) ChatId(chatId string) *LinkChatReqBuilder {
	builder.apiReq.PathParams.Set("chat_id", fmt.Sprint(chatId))
	return builder
}

// 获取指定群的分享链接。
func (builder *LinkChatReqBuilder) Body(body *LinkChatReqBody) *LinkChatReqBuilder {
	builder.body = body
	return builder
}

func (builder *LinkChatReqBuilder) Build() *LinkChatReq {
	req := &LinkChatReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.Body = builder.body
	return req
}

type LinkChatReqBody struct {
	ValidityPeriod *string `json:"validity_period,omitempty"` // 群分享链接有效时长，可选值week、year、permanently，分别表示7天、1年以及永久有效
}

type LinkChatReq struct {
	apiReq *larkcore.ApiReq
	Body   *LinkChatReqBody `body:""`
}

type LinkChatRespData struct {
	ShareLink   *string `json:"share_link,omitempty"`   // 群分享链接
	ExpireTime  *string `json:"expire_time,omitempty"`  // 分享链接过期时间戳（秒级）
	IsPermanent *bool   `json:"is_permanent,omitempty"` // 分享链接是否永久有效
}

type LinkChatResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *LinkChatRespData `json:"data"` // 业务数据
}

func (resp *LinkChatResp) Success() bool {
	return resp.Code == 0
}

type ListChatReqBuilder struct {
	apiReq *larkcore.ApiReq
	limit  int // 最大返回多少记录，当使用迭代器访问时才有效
}

func NewListChatReqBuilder() *ListChatReqBuilder {
	builder := &ListChatReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 最大返回多少记录，当使用迭代器访问时才有效
func (builder *ListChatReqBuilder) Limit(limit int) *ListChatReqBuilder {
	builder.limit = limit
	return builder
}

// 此次调用中使用的用户ID的类型
//
// 示例值：
func (builder *ListChatReqBuilder) UserIdType(userIdType string) *ListChatReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

//
//
// 示例值：dmJCRHhpd3JRbGV1VEVNRFFyTitRWDY5ZFkybmYrMEUwMUFYT0VMMWdENEtuYUhsNUxGMDIwemtvdE5ORjBNQQ==
func (builder *ListChatReqBuilder) PageToken(pageToken string) *ListChatReqBuilder {
	builder.apiReq.QueryParams.Set("page_token", fmt.Sprint(pageToken))
	return builder
}

//
//
// 示例值：10
func (builder *ListChatReqBuilder) PageSize(pageSize int) *ListChatReqBuilder {
	builder.apiReq.QueryParams.Set("page_size", fmt.Sprint(pageSize))
	return builder
}

func (builder *ListChatReqBuilder) Build() *ListChatReq {
	req := &ListChatReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.Limit = builder.limit
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type ListChatReq struct {
	apiReq *larkcore.ApiReq
	Limit  int // 最多返回多少记录，只有在使用迭代器访问时，才有效

}

type ListChatRespData struct {
	Items     []*ListChat `json:"items,omitempty"`      // chat 列表
	PageToken *string     `json:"page_token,omitempty"` //
	HasMore   *bool       `json:"has_more,omitempty"`   //
}

type ListChatResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *ListChatRespData `json:"data"` // 业务数据
}

func (resp *ListChatResp) Success() bool {
	return resp.Code == 0
}

type SearchChatReqBuilder struct {
	apiReq *larkcore.ApiReq
	limit  int // 最大返回多少记录，当使用迭代器访问时才有效
}

func NewSearchChatReqBuilder() *SearchChatReqBuilder {
	builder := &SearchChatReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 最大返回多少记录，当使用迭代器访问时才有效
func (builder *SearchChatReqBuilder) Limit(limit int) *SearchChatReqBuilder {
	builder.limit = limit
	return builder
}

// 此次调用中使用的用户ID的类型
//
// 示例值：
func (builder *SearchChatReqBuilder) UserIdType(userIdType string) *SearchChatReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

// 关键词;;**注意事项**：;- 关键词支持匹配群国际化名称、群成员名称;- 支持使用多语种搜索;- 支持拼音、前缀等模糊搜索;- 关键词为空值或长度超过`64`个字符时将返回空的结果
//
// 示例值：abc
func (builder *SearchChatReqBuilder) Query(query string) *SearchChatReqBuilder {
	builder.apiReq.QueryParams.Set("query", fmt.Sprint(query))
	return builder
}

//
//
// 示例值：dmJCRHhpd3JRbGV1VEVNRFFyTitRWDY5ZFkybmYrMEUwMUFYT0VMMWdENEtuYUhsNUxGMDIwemtvdE5ORjBNQQ==
func (builder *SearchChatReqBuilder) PageToken(pageToken string) *SearchChatReqBuilder {
	builder.apiReq.QueryParams.Set("page_token", fmt.Sprint(pageToken))
	return builder
}

//
//
// 示例值：10
func (builder *SearchChatReqBuilder) PageSize(pageSize int) *SearchChatReqBuilder {
	builder.apiReq.QueryParams.Set("page_size", fmt.Sprint(pageSize))
	return builder
}

func (builder *SearchChatReqBuilder) Build() *SearchChatReq {
	req := &SearchChatReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.Limit = builder.limit
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type SearchChatReq struct {
	apiReq *larkcore.ApiReq
	Limit  int // 最多返回多少记录，只有在使用迭代器访问时，才有效

}

type SearchChatRespData struct {
	Items     []*ListChat `json:"items,omitempty"`      // chat 列表
	PageToken *string     `json:"page_token,omitempty"` //
	HasMore   *bool       `json:"has_more,omitempty"`   // 是否还有更多群组
}

type SearchChatResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *SearchChatRespData `json:"data"` // 业务数据
}

func (resp *SearchChatResp) Success() bool {
	return resp.Code == 0
}

type UpdateChatReqBodyBuilder struct {
	avatar                     string // 群头像对应的 Image Key，可通过[上传图片](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/image/create)获取（注意：上传图片的 ==image_type== 需要指定为 ==avatar==）
	avatarFlag                 bool
	name                       string // 群名称
	nameFlag                   bool
	description                string // 群描述
	descriptionFlag            bool
	i18nNames                  *I18nNames // 群国际化名称
	i18nNamesFlag              bool
	addMemberPermission        string // 邀请用户或机器人入群权限;;注意：;- 若值设置为`only_owner`，则share_card_permission只能设置为`not_allowed`;- 若值设置为`all_members`，则share_card_permission只能设置为`allowed`;;**可选值有**：;- `only_owner`：仅群主和管理员;- `all_members`：所有成员
	addMemberPermissionFlag    bool
	shareCardPermission        string // 群分享权限;;**可选值有**：;- `allowed`：允许;- `not_allowed`：不允许
	shareCardPermissionFlag    bool
	atAllPermission            string // at 所有人权限;;**可选值有**：;- `only_owner`：仅群主和管理员;- `all_members`：所有成员
	atAllPermissionFlag        bool
	editPermission             string // 群编辑权限;;**可选值有**：;- `only_owner`：仅群主和管理员;- `all_members`：所有成员
	editPermissionFlag         bool
	ownerId                    string // 新群主 ID
	ownerIdFlag                bool
	joinMessageVisibility      string // 入群消息可见性;;**可选值有**：;- `only_owner`：仅群主和管理员可见;- `all_members`：所有成员可见;- `not_anyone`：任何人均不可见
	joinMessageVisibilityFlag  bool
	leaveMessageVisibility     string // 出群消息可见性;;**可选值有**：;- `only_owner`：仅群主和管理员可见;- `all_members`：所有成员可见;- `not_anyone`：任何人均不可见
	leaveMessageVisibilityFlag bool
	membershipApproval         string // 加群审批;;**可选值有**：;- `no_approval_required`：无需审批;- `approval_required`：需要审批
	membershipApprovalFlag     bool

	chatType     string // 群类型;;**可选值有**：;- `private`：私有群;- `public`：公开群
	chatTypeFlag bool
}

func NewUpdateChatReqBodyBuilder() *UpdateChatReqBodyBuilder {
	builder := &UpdateChatReqBodyBuilder{}
	return builder
}

// 群头像对应的 Image Key，可通过[上传图片](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/image/create)获取（注意：上传图片的 ==image_type== 需要指定为 ==avatar==）
//
//示例值：default-avatar_44ae0ca3-e140-494b-956f-78091e348435
func (builder *UpdateChatReqBodyBuilder) Avatar(avatar string) *UpdateChatReqBodyBuilder {
	builder.avatar = avatar
	builder.avatarFlag = true
	return builder
}

// 群名称
//
//示例值：群聊
func (builder *UpdateChatReqBodyBuilder) Name(name string) *UpdateChatReqBodyBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

// 群描述
//
//示例值：测试群描述
func (builder *UpdateChatReqBodyBuilder) Description(description string) *UpdateChatReqBodyBuilder {
	builder.description = description
	builder.descriptionFlag = true
	return builder
}

// 群国际化名称
//
//示例值：
func (builder *UpdateChatReqBodyBuilder) I18nNames(i18nNames *I18nNames) *UpdateChatReqBodyBuilder {
	builder.i18nNames = i18nNames
	builder.i18nNamesFlag = true
	return builder
}

// 邀请用户或机器人入群权限;;注意：;- 若值设置为`only_owner`，则share_card_permission只能设置为`not_allowed`;- 若值设置为`all_members`，则share_card_permission只能设置为`allowed`;;**可选值有**：;- `only_owner`：仅群主和管理员;- `all_members`：所有成员
//
//示例值：all_members
func (builder *UpdateChatReqBodyBuilder) AddMemberPermission(addMemberPermission string) *UpdateChatReqBodyBuilder {
	builder.addMemberPermission = addMemberPermission
	builder.addMemberPermissionFlag = true
	return builder
}

// 群分享权限;;**可选值有**：;- `allowed`：允许;- `not_allowed`：不允许
//
//示例值：allowed
func (builder *UpdateChatReqBodyBuilder) ShareCardPermission(shareCardPermission string) *UpdateChatReqBodyBuilder {
	builder.shareCardPermission = shareCardPermission
	builder.shareCardPermissionFlag = true
	return builder
}

// at 所有人权限;;**可选值有**：;- `only_owner`：仅群主和管理员;- `all_members`：所有成员
//
//示例值：all_members
func (builder *UpdateChatReqBodyBuilder) AtAllPermission(atAllPermission string) *UpdateChatReqBodyBuilder {
	builder.atAllPermission = atAllPermission
	builder.atAllPermissionFlag = true
	return builder
}

// 群编辑权限;;**可选值有**：;- `only_owner`：仅群主和管理员;- `all_members`：所有成员
//
//示例值：all_members
func (builder *UpdateChatReqBodyBuilder) EditPermission(editPermission string) *UpdateChatReqBodyBuilder {
	builder.editPermission = editPermission
	builder.editPermissionFlag = true
	return builder
}

// 新群主 ID
//
//示例值：4d7a3c6g
func (builder *UpdateChatReqBodyBuilder) OwnerId(ownerId string) *UpdateChatReqBodyBuilder {
	builder.ownerId = ownerId
	builder.ownerIdFlag = true
	return builder
}

// 入群消息可见性;;**可选值有**：;- `only_owner`：仅群主和管理员可见;- `all_members`：所有成员可见;- `not_anyone`：任何人均不可见
//
//示例值：only_owner
func (builder *UpdateChatReqBodyBuilder) JoinMessageVisibility(joinMessageVisibility string) *UpdateChatReqBodyBuilder {
	builder.joinMessageVisibility = joinMessageVisibility
	builder.joinMessageVisibilityFlag = true
	return builder
}

// 出群消息可见性;;**可选值有**：;- `only_owner`：仅群主和管理员可见;- `all_members`：所有成员可见;- `not_anyone`：任何人均不可见
//
//示例值：only_owner
func (builder *UpdateChatReqBodyBuilder) LeaveMessageVisibility(leaveMessageVisibility string) *UpdateChatReqBodyBuilder {
	builder.leaveMessageVisibility = leaveMessageVisibility
	builder.leaveMessageVisibilityFlag = true
	return builder
}

// 加群审批;;**可选值有**：;- `no_approval_required`：无需审批;- `approval_required`：需要审批
//
//示例值：no_approval_required
func (builder *UpdateChatReqBodyBuilder) MembershipApproval(membershipApproval string) *UpdateChatReqBodyBuilder {
	builder.membershipApproval = membershipApproval
	builder.membershipApprovalFlag = true
	return builder
}

// 群类型;;**可选值有**：;- `private`：私有群;- `public`：公开群
//
//示例值：private
func (builder *UpdateChatReqBodyBuilder) ChatType(chatType string) *UpdateChatReqBodyBuilder {
	builder.chatType = chatType
	builder.chatTypeFlag = true
	return builder
}

func (builder *UpdateChatReqBodyBuilder) Build() *UpdateChatReqBody {
	req := &UpdateChatReqBody{}
	if builder.avatarFlag {
		req.Avatar = &builder.avatar
	}
	if builder.nameFlag {
		req.Name = &builder.name
	}
	if builder.descriptionFlag {
		req.Description = &builder.description
	}
	if builder.i18nNamesFlag {
		req.I18nNames = builder.i18nNames
	}
	if builder.addMemberPermissionFlag {
		req.AddMemberPermission = &builder.addMemberPermission
	}
	if builder.shareCardPermissionFlag {
		req.ShareCardPermission = &builder.shareCardPermission
	}
	if builder.atAllPermissionFlag {
		req.AtAllPermission = &builder.atAllPermission
	}
	if builder.editPermissionFlag {
		req.EditPermission = &builder.editPermission
	}
	if builder.ownerIdFlag {
		req.OwnerId = &builder.ownerId
	}
	if builder.joinMessageVisibilityFlag {
		req.JoinMessageVisibility = &builder.joinMessageVisibility
	}
	if builder.leaveMessageVisibilityFlag {
		req.LeaveMessageVisibility = &builder.leaveMessageVisibility
	}
	if builder.membershipApprovalFlag {
		req.MembershipApproval = &builder.membershipApproval
	}
	if builder.chatTypeFlag {
		req.ChatType = &builder.chatType
	}
	return req
}

type UpdateChatPathReqBodyBuilder struct {
	avatar                     string // 群头像对应的 Image Key，可通过[上传图片](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/image/create)获取（注意：上传图片的 ==image_type== 需要指定为 ==avatar==）
	avatarFlag                 bool
	name                       string // 群名称
	nameFlag                   bool
	description                string // 群描述
	descriptionFlag            bool
	i18nNames                  *I18nNames // 群国际化名称
	i18nNamesFlag              bool
	addMemberPermission        string // 邀请用户或机器人入群权限;;注意：;- 若值设置为`only_owner`，则share_card_permission只能设置为`not_allowed`;- 若值设置为`all_members`，则share_card_permission只能设置为`allowed`;;**可选值有**：;- `only_owner`：仅群主和管理员;- `all_members`：所有成员
	addMemberPermissionFlag    bool
	shareCardPermission        string // 群分享权限;;**可选值有**：;- `allowed`：允许;- `not_allowed`：不允许
	shareCardPermissionFlag    bool
	atAllPermission            string // at 所有人权限;;**可选值有**：;- `only_owner`：仅群主和管理员;- `all_members`：所有成员
	atAllPermissionFlag        bool
	editPermission             string // 群编辑权限;;**可选值有**：;- `only_owner`：仅群主和管理员;- `all_members`：所有成员
	editPermissionFlag         bool
	ownerId                    string // 新群主 ID
	ownerIdFlag                bool
	joinMessageVisibility      string // 入群消息可见性;;**可选值有**：;- `only_owner`：仅群主和管理员可见;- `all_members`：所有成员可见;- `not_anyone`：任何人均不可见
	joinMessageVisibilityFlag  bool
	leaveMessageVisibility     string // 出群消息可见性;;**可选值有**：;- `only_owner`：仅群主和管理员可见;- `all_members`：所有成员可见;- `not_anyone`：任何人均不可见
	leaveMessageVisibilityFlag bool
	membershipApproval         string // 加群审批;;**可选值有**：;- `no_approval_required`：无需审批;- `approval_required`：需要审批
	membershipApprovalFlag     bool
	labels                     []string // 群标签
	labelsFlag                 bool
	toolkitIds                 []string // 群快捷组件列表
	toolkitIdsFlag             bool
	chatType                   string // 群类型;;**可选值有**：;- `private`：私有群;- `public`：公开群
	chatTypeFlag               bool
}

func NewUpdateChatPathReqBodyBuilder() *UpdateChatPathReqBodyBuilder {
	builder := &UpdateChatPathReqBodyBuilder{}
	return builder
}

// 群头像对应的 Image Key，可通过[上传图片](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/image/create)获取（注意：上传图片的 ==image_type== 需要指定为 ==avatar==）
//
// 示例值：default-avatar_44ae0ca3-e140-494b-956f-78091e348435
func (builder *UpdateChatPathReqBodyBuilder) Avatar(avatar string) *UpdateChatPathReqBodyBuilder {
	builder.avatar = avatar
	builder.avatarFlag = true
	return builder
}

// 群名称
//
// 示例值：群聊
func (builder *UpdateChatPathReqBodyBuilder) Name(name string) *UpdateChatPathReqBodyBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

// 群描述
//
// 示例值：测试群描述
func (builder *UpdateChatPathReqBodyBuilder) Description(description string) *UpdateChatPathReqBodyBuilder {
	builder.description = description
	builder.descriptionFlag = true
	return builder
}

// 群国际化名称
//
// 示例值：
func (builder *UpdateChatPathReqBodyBuilder) I18nNames(i18nNames *I18nNames) *UpdateChatPathReqBodyBuilder {
	builder.i18nNames = i18nNames
	builder.i18nNamesFlag = true
	return builder
}

// 邀请用户或机器人入群权限;;注意：;- 若值设置为`only_owner`，则share_card_permission只能设置为`not_allowed`;- 若值设置为`all_members`，则share_card_permission只能设置为`allowed`;;**可选值有**：;- `only_owner`：仅群主和管理员;- `all_members`：所有成员
//
// 示例值：all_members
func (builder *UpdateChatPathReqBodyBuilder) AddMemberPermission(addMemberPermission string) *UpdateChatPathReqBodyBuilder {
	builder.addMemberPermission = addMemberPermission
	builder.addMemberPermissionFlag = true
	return builder
}

// 群分享权限;;**可选值有**：;- `allowed`：允许;- `not_allowed`：不允许
//
// 示例值：allowed
func (builder *UpdateChatPathReqBodyBuilder) ShareCardPermission(shareCardPermission string) *UpdateChatPathReqBodyBuilder {
	builder.shareCardPermission = shareCardPermission
	builder.shareCardPermissionFlag = true
	return builder
}

// at 所有人权限;;**可选值有**：;- `only_owner`：仅群主和管理员;- `all_members`：所有成员
//
// 示例值：all_members
func (builder *UpdateChatPathReqBodyBuilder) AtAllPermission(atAllPermission string) *UpdateChatPathReqBodyBuilder {
	builder.atAllPermission = atAllPermission
	builder.atAllPermissionFlag = true
	return builder
}

// 群编辑权限;;**可选值有**：;- `only_owner`：仅群主和管理员;- `all_members`：所有成员
//
// 示例值：all_members
func (builder *UpdateChatPathReqBodyBuilder) EditPermission(editPermission string) *UpdateChatPathReqBodyBuilder {
	builder.editPermission = editPermission
	builder.editPermissionFlag = true
	return builder
}

// 新群主 ID
//
// 示例值：4d7a3c6g
func (builder *UpdateChatPathReqBodyBuilder) OwnerId(ownerId string) *UpdateChatPathReqBodyBuilder {
	builder.ownerId = ownerId
	builder.ownerIdFlag = true
	return builder
}

// 入群消息可见性;;**可选值有**：;- `only_owner`：仅群主和管理员可见;- `all_members`：所有成员可见;- `not_anyone`：任何人均不可见
//
// 示例值：only_owner
func (builder *UpdateChatPathReqBodyBuilder) JoinMessageVisibility(joinMessageVisibility string) *UpdateChatPathReqBodyBuilder {
	builder.joinMessageVisibility = joinMessageVisibility
	builder.joinMessageVisibilityFlag = true
	return builder
}

// 出群消息可见性;;**可选值有**：;- `only_owner`：仅群主和管理员可见;- `all_members`：所有成员可见;- `not_anyone`：任何人均不可见
//
// 示例值：only_owner
func (builder *UpdateChatPathReqBodyBuilder) LeaveMessageVisibility(leaveMessageVisibility string) *UpdateChatPathReqBodyBuilder {
	builder.leaveMessageVisibility = leaveMessageVisibility
	builder.leaveMessageVisibilityFlag = true
	return builder
}

// 加群审批;;**可选值有**：;- `no_approval_required`：无需审批;- `approval_required`：需要审批
//
// 示例值：no_approval_required
func (builder *UpdateChatPathReqBodyBuilder) MembershipApproval(membershipApproval string) *UpdateChatPathReqBodyBuilder {
	builder.membershipApproval = membershipApproval
	builder.membershipApprovalFlag = true
	return builder
}

// 群类型;;**可选值有**：;- `private`：私有群;- `public`：公开群
//
// 示例值：private
func (builder *UpdateChatPathReqBodyBuilder) ChatType(chatType string) *UpdateChatPathReqBodyBuilder {
	builder.chatType = chatType
	builder.chatTypeFlag = true
	return builder
}

func (builder *UpdateChatPathReqBodyBuilder) Build() (*UpdateChatReqBody, error) {
	req := &UpdateChatReqBody{}
	if builder.avatarFlag {
		req.Avatar = &builder.avatar
	}
	if builder.nameFlag {
		req.Name = &builder.name
	}
	if builder.descriptionFlag {
		req.Description = &builder.description
	}
	if builder.i18nNamesFlag {
		req.I18nNames = builder.i18nNames
	}
	if builder.addMemberPermissionFlag {
		req.AddMemberPermission = &builder.addMemberPermission
	}
	if builder.shareCardPermissionFlag {
		req.ShareCardPermission = &builder.shareCardPermission
	}
	if builder.atAllPermissionFlag {
		req.AtAllPermission = &builder.atAllPermission
	}
	if builder.editPermissionFlag {
		req.EditPermission = &builder.editPermission
	}
	if builder.ownerIdFlag {
		req.OwnerId = &builder.ownerId
	}
	if builder.joinMessageVisibilityFlag {
		req.JoinMessageVisibility = &builder.joinMessageVisibility
	}
	if builder.leaveMessageVisibilityFlag {
		req.LeaveMessageVisibility = &builder.leaveMessageVisibility
	}
	if builder.membershipApprovalFlag {
		req.MembershipApproval = &builder.membershipApproval
	}
	if builder.chatTypeFlag {
		req.ChatType = &builder.chatType
	}
	return req, nil
}

type UpdateChatReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *UpdateChatReqBody
}

func NewUpdateChatReqBuilder() *UpdateChatReqBuilder {
	builder := &UpdateChatReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 群 ID，详情参见[群ID 说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-id-description);;**注意**：仅支持群模式为`group`的群组ID
//
// 示例值：oc_a0553eda9014c201e6969b478895c230
func (builder *UpdateChatReqBuilder) ChatId(chatId string) *UpdateChatReqBuilder {
	builder.apiReq.PathParams.Set("chat_id", fmt.Sprint(chatId))
	return builder
}

// 此次调用中使用的用户ID的类型
//
// 示例值：
func (builder *UpdateChatReqBuilder) UserIdType(userIdType string) *UpdateChatReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

// 更新群头像、群名称、群描述、群配置、转让群主等。
func (builder *UpdateChatReqBuilder) Body(body *UpdateChatReqBody) *UpdateChatReqBuilder {
	builder.body = body
	return builder
}

func (builder *UpdateChatReqBuilder) Build() *UpdateChatReq {
	req := &UpdateChatReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.body
	return req
}

type UpdateChatReqBody struct {
	Avatar                 *string    `json:"avatar,omitempty"`                   // 群头像对应的 Image Key，可通过[上传图片](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/image/create)获取（注意：上传图片的 ==image_type== 需要指定为 ==avatar==）
	Name                   *string    `json:"name,omitempty"`                     // 群名称
	Description            *string    `json:"description,omitempty"`              // 群描述
	I18nNames              *I18nNames `json:"i18n_names,omitempty"`               // 群国际化名称
	AddMemberPermission    *string    `json:"add_member_permission,omitempty"`    // 邀请用户或机器人入群权限;;注意：;- 若值设置为`only_owner`，则share_card_permission只能设置为`not_allowed`;- 若值设置为`all_members`，则share_card_permission只能设置为`allowed`;;**可选值有**：;- `only_owner`：仅群主和管理员;- `all_members`：所有成员
	ShareCardPermission    *string    `json:"share_card_permission,omitempty"`    // 群分享权限;;**可选值有**：;- `allowed`：允许;- `not_allowed`：不允许
	AtAllPermission        *string    `json:"at_all_permission,omitempty"`        // at 所有人权限;;**可选值有**：;- `only_owner`：仅群主和管理员;- `all_members`：所有成员
	EditPermission         *string    `json:"edit_permission,omitempty"`          // 群编辑权限;;**可选值有**：;- `only_owner`：仅群主和管理员;- `all_members`：所有成员
	OwnerId                *string    `json:"owner_id,omitempty"`                 // 新群主 ID
	JoinMessageVisibility  *string    `json:"join_message_visibility,omitempty"`  // 入群消息可见性;;**可选值有**：;- `only_owner`：仅群主和管理员可见;- `all_members`：所有成员可见;- `not_anyone`：任何人均不可见
	LeaveMessageVisibility *string    `json:"leave_message_visibility,omitempty"` // 出群消息可见性;;**可选值有**：;- `only_owner`：仅群主和管理员可见;- `all_members`：所有成员可见;- `not_anyone`：任何人均不可见
	MembershipApproval     *string    `json:"membership_approval,omitempty"`      // 加群审批;;**可选值有**：;- `no_approval_required`：无需审批;- `approval_required`：需要审批

	ChatType *string `json:"chat_type,omitempty"` // 群类型;;**可选值有**：;- `private`：私有群;- `public`：公开群
}

type UpdateChatReq struct {
	apiReq *larkcore.ApiReq
	Body   *UpdateChatReqBody `body:""`
}

type UpdateChatResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *UpdateChatResp) Success() bool {
	return resp.Code == 0
}

type GetChatAnnouncementReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewGetChatAnnouncementReqBuilder() *GetChatAnnouncementReqBuilder {
	builder := &GetChatAnnouncementReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 待获取公告的群 ID，详情参见[群ID 说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-id-description);;**注意**：不支持P2P单聊
//
// 示例值：oc_5ad11d72b830411d72b836c20
func (builder *GetChatAnnouncementReqBuilder) ChatId(chatId string) *GetChatAnnouncementReqBuilder {
	builder.apiReq.PathParams.Set("chat_id", fmt.Sprint(chatId))
	return builder
}

// 此次调用中使用的用户ID的类型
//
// 示例值：open_id
func (builder *GetChatAnnouncementReqBuilder) UserIdType(userIdType string) *GetChatAnnouncementReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

func (builder *GetChatAnnouncementReqBuilder) Build() *GetChatAnnouncementReq {
	req := &GetChatAnnouncementReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type GetChatAnnouncementReq struct {
	apiReq *larkcore.ApiReq
}

type GetChatAnnouncementRespData struct {
	Content        *string `json:"content,omitempty"`          // 云文档序列化信息
	Revision       *string `json:"revision,omitempty"`         // 文档当前版本号 纯数字
	CreateTime     *string `json:"create_time,omitempty"`      // 文档生成的时间戳（秒）
	UpdateTime     *string `json:"update_time,omitempty"`      // 文档更新的时间戳（秒）
	OwnerIdType    *string `json:"owner_id_type,omitempty"`    // 文档所有者的 ID 类型;;- 如果所有者是用户，则与查询参数中的user_id_type 相同；取值为`open_id` `user_id` `union_id` 其中之一，不同 ID 的说明参见 [用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction);- 如果所有者是机器人，为机器人应用的 `app_id`，详情参见  [获取应用身份访问凭证](https://open.feishu.cn/document/ukTMukTMukTM/ukDNz4SO0MjL5QzM/g)
	OwnerId        *string `json:"owner_id,omitempty"`         // 文档所有者 ID，ID 值与owner_id_type 中的ID类型对应
	ModifierIdType *string `json:"modifier_id_type,omitempty"` // 文档最新修改者 id 类型;; - 如果修改者是用户，则与查询参数中的user_id_type 相同；取值为`open_id` `user_id` `union_id` 其中之一，不同 ID 的说明参见 [用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction);- 如果修改者是机器人，为机器人应用的 `app_id`，详情参见  [获取应用身份访问凭证](https://open.feishu.cn/document/ukTMukTMukTM/ukDNz4SO0MjL5QzM/g)
	ModifierId     *string `json:"modifier_id,omitempty"`      // 文档最新修改者 ID，ID 值与modifier_id_type 中的ID类型对应
}

type GetChatAnnouncementResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *GetChatAnnouncementRespData `json:"data"` // 业务数据
}

func (resp *GetChatAnnouncementResp) Success() bool {
	return resp.Code == 0
}

type PatchChatAnnouncementReqBodyBuilder struct {
	revision     string // 文档当前版本号 int64 类型，[获取群公告信息](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-announcement/get)接口会返回
	revisionFlag bool
	requests     []string // 修改文档请求的序列化字段;;更新公告信息的格式和更新[云文档](https://open.feishu.cn/document/ukTMukTMukTM/uYDM2YjL2AjN24iNwYjN)格式相同
	requestsFlag bool
}

func NewPatchChatAnnouncementReqBodyBuilder() *PatchChatAnnouncementReqBodyBuilder {
	builder := &PatchChatAnnouncementReqBodyBuilder{}
	return builder
}

// 文档当前版本号 int64 类型，[获取群公告信息](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-announcement/get)接口会返回
//
//示例值：12
func (builder *PatchChatAnnouncementReqBodyBuilder) Revision(revision string) *PatchChatAnnouncementReqBodyBuilder {
	builder.revision = revision
	builder.revisionFlag = true
	return builder
}

// 修改文档请求的序列化字段;;更新公告信息的格式和更新[云文档](https://open.feishu.cn/document/ukTMukTMukTM/uYDM2YjL2AjN24iNwYjN)格式相同
//
//示例值：{\"requestType\":\"InsertBlocksRequestType\",\"insertBlocksRequest\":{\"payload\":\"{\\\"blocks\\\":[{\\\"type\\\":\\\"paragraph\\\",\\\"paragraph\\\":{\\\"elements\\\":[{\\\"type\\\":\\\"textRun\\\",\\\"textRun\\\":{\\\"text\\\":\\\"ylyyyyyDocs API Sample Content\\\",\\\"style\\\":{}}}],\\\"style\\\":{}}}]}\",\"location\":{\"zoneId\":\"0\",\"index\":0, \"endOfZone\": true}}}
func (builder *PatchChatAnnouncementReqBodyBuilder) Requests(requests []string) *PatchChatAnnouncementReqBodyBuilder {
	builder.requests = requests
	builder.requestsFlag = true
	return builder
}

func (builder *PatchChatAnnouncementReqBodyBuilder) Build() *PatchChatAnnouncementReqBody {
	req := &PatchChatAnnouncementReqBody{}
	if builder.revisionFlag {
		req.Revision = &builder.revision
	}
	if builder.requestsFlag {
		req.Requests = builder.requests
	}
	return req
}

type PatchChatAnnouncementPathReqBodyBuilder struct {
	revision     string // 文档当前版本号 int64 类型，[获取群公告信息](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-announcement/get)接口会返回
	revisionFlag bool
	requests     []string // 修改文档请求的序列化字段;;更新公告信息的格式和更新[云文档](https://open.feishu.cn/document/ukTMukTMukTM/uYDM2YjL2AjN24iNwYjN)格式相同
	requestsFlag bool
}

func NewPatchChatAnnouncementPathReqBodyBuilder() *PatchChatAnnouncementPathReqBodyBuilder {
	builder := &PatchChatAnnouncementPathReqBodyBuilder{}
	return builder
}

// 文档当前版本号 int64 类型，[获取群公告信息](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-announcement/get)接口会返回
//
// 示例值：12
func (builder *PatchChatAnnouncementPathReqBodyBuilder) Revision(revision string) *PatchChatAnnouncementPathReqBodyBuilder {
	builder.revision = revision
	builder.revisionFlag = true
	return builder
}

// 修改文档请求的序列化字段;;更新公告信息的格式和更新[云文档](https://open.feishu.cn/document/ukTMukTMukTM/uYDM2YjL2AjN24iNwYjN)格式相同
//
// 示例值：{\"requestType\":\"InsertBlocksRequestType\",\"insertBlocksRequest\":{\"payload\":\"{\\\"blocks\\\":[{\\\"type\\\":\\\"paragraph\\\",\\\"paragraph\\\":{\\\"elements\\\":[{\\\"type\\\":\\\"textRun\\\",\\\"textRun\\\":{\\\"text\\\":\\\"ylyyyyyDocs API Sample Content\\\",\\\"style\\\":{}}}],\\\"style\\\":{}}}]}\",\"location\":{\"zoneId\":\"0\",\"index\":0, \"endOfZone\": true}}}
func (builder *PatchChatAnnouncementPathReqBodyBuilder) Requests(requests []string) *PatchChatAnnouncementPathReqBodyBuilder {
	builder.requests = requests
	builder.requestsFlag = true
	return builder
}

func (builder *PatchChatAnnouncementPathReqBodyBuilder) Build() (*PatchChatAnnouncementReqBody, error) {
	req := &PatchChatAnnouncementReqBody{}
	if builder.revisionFlag {
		req.Revision = &builder.revision
	}
	if builder.requestsFlag {
		req.Requests = builder.requests
	}
	return req, nil
}

type PatchChatAnnouncementReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *PatchChatAnnouncementReqBody
}

func NewPatchChatAnnouncementReqBuilder() *PatchChatAnnouncementReqBuilder {
	builder := &PatchChatAnnouncementReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 待修改公告的群 ID，详情参见[群ID 说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-id-description);;**注意**：不支持P2P单聊
//
// 示例值：oc_5ad11d72b830411d72b836c20
func (builder *PatchChatAnnouncementReqBuilder) ChatId(chatId string) *PatchChatAnnouncementReqBuilder {
	builder.apiReq.PathParams.Set("chat_id", fmt.Sprint(chatId))
	return builder
}

// 更新会话中的群公告信息，更新公告信息的格式和更新[云文档](https://open.feishu.cn/document/ukTMukTMukTM/uAzM5YjLwMTO24CMzkjN)格式相同。
func (builder *PatchChatAnnouncementReqBuilder) Body(body *PatchChatAnnouncementReqBody) *PatchChatAnnouncementReqBuilder {
	builder.body = body
	return builder
}

func (builder *PatchChatAnnouncementReqBuilder) Build() *PatchChatAnnouncementReq {
	req := &PatchChatAnnouncementReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.Body = builder.body
	return req
}

type PatchChatAnnouncementReqBody struct {
	Revision *string  `json:"revision,omitempty"` // 文档当前版本号 int64 类型，[获取群公告信息](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-announcement/get)接口会返回
	Requests []string `json:"requests,omitempty"` // 修改文档请求的序列化字段;;更新公告信息的格式和更新[云文档](https://open.feishu.cn/document/ukTMukTMukTM/uYDM2YjL2AjN24iNwYjN)格式相同
}

type PatchChatAnnouncementReq struct {
	apiReq *larkcore.ApiReq
	Body   *PatchChatAnnouncementReqBody `body:""`
}

type PatchChatAnnouncementResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *PatchChatAnnouncementResp) Success() bool {
	return resp.Code == 0
}

type AddManagersChatManagersReqBodyBuilder struct {
	managerIds     []string // 要增加的 manager_id;;**注意**：;- 对于普通群，最多指定 10 个管理员;- 对于超大群，最多指定 20 个管理员;- 每次请求最多指定 50 个用户或者 5 个机器人
	managerIdsFlag bool
}

func NewAddManagersChatManagersReqBodyBuilder() *AddManagersChatManagersReqBodyBuilder {
	builder := &AddManagersChatManagersReqBodyBuilder{}
	return builder
}

// 要增加的 manager_id;;**注意**：;- 对于普通群，最多指定 10 个管理员;- 对于超大群，最多指定 20 个管理员;- 每次请求最多指定 50 个用户或者 5 个机器人
//
//示例值：["ou_9204a37300b3700d61effaa439f34295"]
func (builder *AddManagersChatManagersReqBodyBuilder) ManagerIds(managerIds []string) *AddManagersChatManagersReqBodyBuilder {
	builder.managerIds = managerIds
	builder.managerIdsFlag = true
	return builder
}

func (builder *AddManagersChatManagersReqBodyBuilder) Build() *AddManagersChatManagersReqBody {
	req := &AddManagersChatManagersReqBody{}
	if builder.managerIdsFlag {
		req.ManagerIds = builder.managerIds
	}
	return req
}

type AddManagersChatManagersPathReqBodyBuilder struct {
	managerIds     []string // 要增加的 manager_id;;**注意**：;- 对于普通群，最多指定 10 个管理员;- 对于超大群，最多指定 20 个管理员;- 每次请求最多指定 50 个用户或者 5 个机器人
	managerIdsFlag bool
}

func NewAddManagersChatManagersPathReqBodyBuilder() *AddManagersChatManagersPathReqBodyBuilder {
	builder := &AddManagersChatManagersPathReqBodyBuilder{}
	return builder
}

// 要增加的 manager_id;;**注意**：;- 对于普通群，最多指定 10 个管理员;- 对于超大群，最多指定 20 个管理员;- 每次请求最多指定 50 个用户或者 5 个机器人
//
// 示例值：["ou_9204a37300b3700d61effaa439f34295"]
func (builder *AddManagersChatManagersPathReqBodyBuilder) ManagerIds(managerIds []string) *AddManagersChatManagersPathReqBodyBuilder {
	builder.managerIds = managerIds
	builder.managerIdsFlag = true
	return builder
}

func (builder *AddManagersChatManagersPathReqBodyBuilder) Build() (*AddManagersChatManagersReqBody, error) {
	req := &AddManagersChatManagersReqBody{}
	if builder.managerIdsFlag {
		req.ManagerIds = builder.managerIds
	}
	return req, nil
}

type AddManagersChatManagersReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *AddManagersChatManagersReqBody
}

func NewAddManagersChatManagersReqBuilder() *AddManagersChatManagersReqBuilder {
	builder := &AddManagersChatManagersReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 群 ID，详情参见[群ID 说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-id-description);;**注意**：仅支持群模式为`group`、`topic`的群组ID
//
// 示例值：oc_a0553eda9014c201e6969b478895c230
func (builder *AddManagersChatManagersReqBuilder) ChatId(chatId string) *AddManagersChatManagersReqBuilder {
	builder.apiReq.PathParams.Set("chat_id", fmt.Sprint(chatId))
	return builder
}

// 群成员 ID 类型 open_id/user_id/union_id/app_id;;**注意**： 指定机器人类型的管理员请使用 ==app_id==
//
// 示例值：open_id
func (builder *AddManagersChatManagersReqBuilder) MemberIdType(memberIdType string) *AddManagersChatManagersReqBuilder {
	builder.apiReq.QueryParams.Set("member_id_type", fmt.Sprint(memberIdType))
	return builder
}

// 将用户或机器人指定为群管理员。
func (builder *AddManagersChatManagersReqBuilder) Body(body *AddManagersChatManagersReqBody) *AddManagersChatManagersReqBuilder {
	builder.body = body
	return builder
}

func (builder *AddManagersChatManagersReqBuilder) Build() *AddManagersChatManagersReq {
	req := &AddManagersChatManagersReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.body
	return req
}

type AddManagersChatManagersReqBody struct {
	ManagerIds []string `json:"manager_ids,omitempty"` // 要增加的 manager_id;;**注意**：;- 对于普通群，最多指定 10 个管理员;- 对于超大群，最多指定 20 个管理员;- 每次请求最多指定 50 个用户或者 5 个机器人
}

type AddManagersChatManagersReq struct {
	apiReq *larkcore.ApiReq
	Body   *AddManagersChatManagersReqBody `body:""`
}

type AddManagersChatManagersRespData struct {
	ChatManagers    []string `json:"chat_managers,omitempty"`     // 群目前用户类型的管理员 id
	ChatBotManagers []string `json:"chat_bot_managers,omitempty"` // 群目前机器人类型的管理员 id
}

type AddManagersChatManagersResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *AddManagersChatManagersRespData `json:"data"` // 业务数据
}

func (resp *AddManagersChatManagersResp) Success() bool {
	return resp.Code == 0
}

type DeleteManagersChatManagersReqBodyBuilder struct {
	managerIds     []string // 要删除的 manager_id;;**注意**：每次请求最多指定 50 个用户或者 5 个机器人
	managerIdsFlag bool
}

func NewDeleteManagersChatManagersReqBodyBuilder() *DeleteManagersChatManagersReqBodyBuilder {
	builder := &DeleteManagersChatManagersReqBodyBuilder{}
	return builder
}

// 要删除的 manager_id;;**注意**：每次请求最多指定 50 个用户或者 5 个机器人
//
//示例值：["ou_9204a37300b3700d61effaa439f34295"]
func (builder *DeleteManagersChatManagersReqBodyBuilder) ManagerIds(managerIds []string) *DeleteManagersChatManagersReqBodyBuilder {
	builder.managerIds = managerIds
	builder.managerIdsFlag = true
	return builder
}

func (builder *DeleteManagersChatManagersReqBodyBuilder) Build() *DeleteManagersChatManagersReqBody {
	req := &DeleteManagersChatManagersReqBody{}
	if builder.managerIdsFlag {
		req.ManagerIds = builder.managerIds
	}
	return req
}

type DeleteManagersChatManagersPathReqBodyBuilder struct {
	managerIds     []string // 要删除的 manager_id;;**注意**：每次请求最多指定 50 个用户或者 5 个机器人
	managerIdsFlag bool
}

func NewDeleteManagersChatManagersPathReqBodyBuilder() *DeleteManagersChatManagersPathReqBodyBuilder {
	builder := &DeleteManagersChatManagersPathReqBodyBuilder{}
	return builder
}

// 要删除的 manager_id;;**注意**：每次请求最多指定 50 个用户或者 5 个机器人
//
// 示例值：["ou_9204a37300b3700d61effaa439f34295"]
func (builder *DeleteManagersChatManagersPathReqBodyBuilder) ManagerIds(managerIds []string) *DeleteManagersChatManagersPathReqBodyBuilder {
	builder.managerIds = managerIds
	builder.managerIdsFlag = true
	return builder
}

func (builder *DeleteManagersChatManagersPathReqBodyBuilder) Build() (*DeleteManagersChatManagersReqBody, error) {
	req := &DeleteManagersChatManagersReqBody{}
	if builder.managerIdsFlag {
		req.ManagerIds = builder.managerIds
	}
	return req, nil
}

type DeleteManagersChatManagersReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *DeleteManagersChatManagersReqBody
}

func NewDeleteManagersChatManagersReqBuilder() *DeleteManagersChatManagersReqBuilder {
	builder := &DeleteManagersChatManagersReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 群 ID，详情参见[群ID 说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-id-description);;**注意**：仅支持群模式为`group`、`topic`的群组ID
//
// 示例值：oc_a0553eda9014c201e6969b478895c230
func (builder *DeleteManagersChatManagersReqBuilder) ChatId(chatId string) *DeleteManagersChatManagersReqBuilder {
	builder.apiReq.PathParams.Set("chat_id", fmt.Sprint(chatId))
	return builder
}

// 群成员 id 类型 open_id/user_id/union_id/app_id;;**注意**：删除机器人类型的管理员请使用 ==app_id==
//
// 示例值：open_id
func (builder *DeleteManagersChatManagersReqBuilder) MemberIdType(memberIdType string) *DeleteManagersChatManagersReqBuilder {
	builder.apiReq.QueryParams.Set("member_id_type", fmt.Sprint(memberIdType))
	return builder
}

// 删除指定的群管理员（用户或机器人）。
func (builder *DeleteManagersChatManagersReqBuilder) Body(body *DeleteManagersChatManagersReqBody) *DeleteManagersChatManagersReqBuilder {
	builder.body = body
	return builder
}

func (builder *DeleteManagersChatManagersReqBuilder) Build() *DeleteManagersChatManagersReq {
	req := &DeleteManagersChatManagersReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.body
	return req
}

type DeleteManagersChatManagersReqBody struct {
	ManagerIds []string `json:"manager_ids,omitempty"` // 要删除的 manager_id;;**注意**：每次请求最多指定 50 个用户或者 5 个机器人
}

type DeleteManagersChatManagersReq struct {
	apiReq *larkcore.ApiReq
	Body   *DeleteManagersChatManagersReqBody `body:""`
}

type DeleteManagersChatManagersRespData struct {
	ChatManagers    []string `json:"chat_managers,omitempty"`     // 群目前用户类型的管理员 id
	ChatBotManagers []string `json:"chat_bot_managers,omitempty"` // 群目前机器人类型的管理员 id
}

type DeleteManagersChatManagersResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *DeleteManagersChatManagersRespData `json:"data"` // 业务数据
}

func (resp *DeleteManagersChatManagersResp) Success() bool {
	return resp.Code == 0
}

type CreateChatMembersReqBodyBuilder struct {
	idList     []string // 成员ID列表，获取ID请参见[如何获得 User ID、Open ID 和 Union ID？](https://open.feishu.cn/document/home/<USER>/how-to-get);;**注意**：;- 成员列表不可为空;- 每次请求最多拉50个用户或者5个机器人，并且群组最多容纳15个机器人;- 列表中填写的成员ID类型应与 ==member_id_type== 参数中选择的类型相对应;- 对于已认证企业的飞书的群人数默认上限：普通群5000人，会议群3000人，话题群5000人。
	idListFlag bool
}

func NewCreateChatMembersReqBodyBuilder() *CreateChatMembersReqBodyBuilder {
	builder := &CreateChatMembersReqBodyBuilder{}
	return builder
}

// 成员ID列表，获取ID请参见[如何获得 User ID、Open ID 和 Union ID？](https://open.feishu.cn/document/home/<USER>/how-to-get);;**注意**：;- 成员列表不可为空;- 每次请求最多拉50个用户或者5个机器人，并且群组最多容纳15个机器人;- 列表中填写的成员ID类型应与 ==member_id_type== 参数中选择的类型相对应;- 对于已认证企业的飞书的群人数默认上限：普通群5000人，会议群3000人，话题群5000人。
//
//示例值：["ou_9204a37300b3700d61effaa439f34295"]
func (builder *CreateChatMembersReqBodyBuilder) IdList(idList []string) *CreateChatMembersReqBodyBuilder {
	builder.idList = idList
	builder.idListFlag = true
	return builder
}

func (builder *CreateChatMembersReqBodyBuilder) Build() *CreateChatMembersReqBody {
	req := &CreateChatMembersReqBody{}
	if builder.idListFlag {
		req.IdList = builder.idList
	}
	return req
}

type CreateChatMembersPathReqBodyBuilder struct {
	idList     []string // 成员ID列表，获取ID请参见[如何获得 User ID、Open ID 和 Union ID？](https://open.feishu.cn/document/home/<USER>/how-to-get);;**注意**：;- 成员列表不可为空;- 每次请求最多拉50个用户或者5个机器人，并且群组最多容纳15个机器人;- 列表中填写的成员ID类型应与 ==member_id_type== 参数中选择的类型相对应;- 对于已认证企业的飞书的群人数默认上限：普通群5000人，会议群3000人，话题群5000人。
	idListFlag bool
}

func NewCreateChatMembersPathReqBodyBuilder() *CreateChatMembersPathReqBodyBuilder {
	builder := &CreateChatMembersPathReqBodyBuilder{}
	return builder
}

// 成员ID列表，获取ID请参见[如何获得 User ID、Open ID 和 Union ID？](https://open.feishu.cn/document/home/<USER>/how-to-get);;**注意**：;- 成员列表不可为空;- 每次请求最多拉50个用户或者5个机器人，并且群组最多容纳15个机器人;- 列表中填写的成员ID类型应与 ==member_id_type== 参数中选择的类型相对应;- 对于已认证企业的飞书的群人数默认上限：普通群5000人，会议群3000人，话题群5000人。
//
// 示例值：["ou_9204a37300b3700d61effaa439f34295"]
func (builder *CreateChatMembersPathReqBodyBuilder) IdList(idList []string) *CreateChatMembersPathReqBodyBuilder {
	builder.idList = idList
	builder.idListFlag = true
	return builder
}

func (builder *CreateChatMembersPathReqBodyBuilder) Build() (*CreateChatMembersReqBody, error) {
	req := &CreateChatMembersReqBody{}
	if builder.idListFlag {
		req.IdList = builder.idList
	}
	return req, nil
}

type CreateChatMembersReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *CreateChatMembersReqBody
}

func NewCreateChatMembersReqBuilder() *CreateChatMembersReqBuilder {
	builder := &CreateChatMembersReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 群 ID，详情参见[群ID 说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-id-description);;**注意**：仅支持群模式为`group`、`topic`的群组ID
//
// 示例值：oc_a0553eda9014c201e6969b478895c230
func (builder *CreateChatMembersReqBuilder) ChatId(chatId string) *CreateChatMembersReqBuilder {
	builder.apiReq.PathParams.Set("chat_id", fmt.Sprint(chatId))
	return builder
}

// 进群成员 ID 类型 open_id/user_id/union_id/app_id;;**注意**：拉机器人入群请使用 ==app_id==
//
// 示例值：open_id
func (builder *CreateChatMembersReqBuilder) MemberIdType(memberIdType string) *CreateChatMembersReqBuilder {
	builder.apiReq.QueryParams.Set("member_id_type", fmt.Sprint(memberIdType))
	return builder
}

// 出现不可用ID后的处理方式 0/1/2
//
// 示例值：0
func (builder *CreateChatMembersReqBuilder) SucceedType(succeedType int) *CreateChatMembersReqBuilder {
	builder.apiReq.QueryParams.Set("succeed_type", fmt.Sprint(succeedType))
	return builder
}

// 将用户或机器人拉入群聊。
func (builder *CreateChatMembersReqBuilder) Body(body *CreateChatMembersReqBody) *CreateChatMembersReqBuilder {
	builder.body = body
	return builder
}

func (builder *CreateChatMembersReqBuilder) Build() *CreateChatMembersReq {
	req := &CreateChatMembersReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.body
	return req
}

type CreateChatMembersReqBody struct {
	IdList []string `json:"id_list,omitempty"` // 成员ID列表，获取ID请参见[如何获得 User ID、Open ID 和 Union ID？](https://open.feishu.cn/document/home/<USER>/how-to-get);;**注意**：;- 成员列表不可为空;- 每次请求最多拉50个用户或者5个机器人，并且群组最多容纳15个机器人;- 列表中填写的成员ID类型应与 ==member_id_type== 参数中选择的类型相对应;- 对于已认证企业的飞书的群人数默认上限：普通群5000人，会议群3000人，话题群5000人。
}

type CreateChatMembersReq struct {
	apiReq *larkcore.ApiReq
	Body   *CreateChatMembersReqBody `body:""`
}

type CreateChatMembersRespData struct {
	InvalidIdList    []string `json:"invalid_id_list,omitempty"`     // 无效成员列表;;**注意**：;- 当`success_type=0`时，`invalid_id_list`只包含已离职的用户ID;- 当`success_type=1`时，`invalid_id_list`中包含已离职的、不可见的、应用未激活的ID
	NotExistedIdList []string `json:"not_existed_id_list,omitempty"` // ID不存在的成员列表
}

type CreateChatMembersResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *CreateChatMembersRespData `json:"data"` // 业务数据
}

func (resp *CreateChatMembersResp) Success() bool {
	return resp.Code == 0
}

type DeleteChatMembersReqBodyBuilder struct {
	idList     []string // 成员列表;;**注意**：;- 成员列表不可为空;- 列表中填写的成员ID类型应与 ==member_id_type== 参数中选择的类型相对应
	idListFlag bool
}

func NewDeleteChatMembersReqBodyBuilder() *DeleteChatMembersReqBodyBuilder {
	builder := &DeleteChatMembersReqBodyBuilder{}
	return builder
}

// 成员列表;;**注意**：;- 成员列表不可为空;- 列表中填写的成员ID类型应与 ==member_id_type== 参数中选择的类型相对应
//
//示例值：["ou_9204a37300b3700d61effaa439f34295"]
func (builder *DeleteChatMembersReqBodyBuilder) IdList(idList []string) *DeleteChatMembersReqBodyBuilder {
	builder.idList = idList
	builder.idListFlag = true
	return builder
}

func (builder *DeleteChatMembersReqBodyBuilder) Build() *DeleteChatMembersReqBody {
	req := &DeleteChatMembersReqBody{}
	if builder.idListFlag {
		req.IdList = builder.idList
	}
	return req
}

type DeleteChatMembersPathReqBodyBuilder struct {
	idList     []string // 成员列表;;**注意**：;- 成员列表不可为空;- 列表中填写的成员ID类型应与 ==member_id_type== 参数中选择的类型相对应
	idListFlag bool
}

func NewDeleteChatMembersPathReqBodyBuilder() *DeleteChatMembersPathReqBodyBuilder {
	builder := &DeleteChatMembersPathReqBodyBuilder{}
	return builder
}

// 成员列表;;**注意**：;- 成员列表不可为空;- 列表中填写的成员ID类型应与 ==member_id_type== 参数中选择的类型相对应
//
// 示例值：["ou_9204a37300b3700d61effaa439f34295"]
func (builder *DeleteChatMembersPathReqBodyBuilder) IdList(idList []string) *DeleteChatMembersPathReqBodyBuilder {
	builder.idList = idList
	builder.idListFlag = true
	return builder
}

func (builder *DeleteChatMembersPathReqBodyBuilder) Build() (*DeleteChatMembersReqBody, error) {
	req := &DeleteChatMembersReqBody{}
	if builder.idListFlag {
		req.IdList = builder.idList
	}
	return req, nil
}

type DeleteChatMembersReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *DeleteChatMembersReqBody
}

func NewDeleteChatMembersReqBuilder() *DeleteChatMembersReqBuilder {
	builder := &DeleteChatMembersReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 群 ID，详情参见[群ID 说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-id-description);;**注意**：仅支持群模式为`group`、`topic`的群组ID
//
// 示例值：oc_a0553eda9014c201e6969b478895c230
func (builder *DeleteChatMembersReqBuilder) ChatId(chatId string) *DeleteChatMembersReqBuilder {
	builder.apiReq.PathParams.Set("chat_id", fmt.Sprint(chatId))
	return builder
}

// 出群成员ID类型;;**注意**：移除机器人请使用 ==app_id==
//
// 示例值：open_id
func (builder *DeleteChatMembersReqBuilder) MemberIdType(memberIdType string) *DeleteChatMembersReqBuilder {
	builder.apiReq.QueryParams.Set("member_id_type", fmt.Sprint(memberIdType))
	return builder
}

// 将用户或机器人移出群聊。
func (builder *DeleteChatMembersReqBuilder) Body(body *DeleteChatMembersReqBody) *DeleteChatMembersReqBuilder {
	builder.body = body
	return builder
}

func (builder *DeleteChatMembersReqBuilder) Build() *DeleteChatMembersReq {
	req := &DeleteChatMembersReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.body
	return req
}

type DeleteChatMembersReqBody struct {
	IdList []string `json:"id_list,omitempty"` // 成员列表;;**注意**：;- 成员列表不可为空;- 列表中填写的成员ID类型应与 ==member_id_type== 参数中选择的类型相对应
}

type DeleteChatMembersReq struct {
	apiReq *larkcore.ApiReq
	Body   *DeleteChatMembersReqBody `body:""`
}

type DeleteChatMembersRespData struct {
	InvalidIdList []string `json:"invalid_id_list,omitempty"` // 无效成员列表
}

type DeleteChatMembersResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *DeleteChatMembersRespData `json:"data"` // 业务数据
}

func (resp *DeleteChatMembersResp) Success() bool {
	return resp.Code == 0
}

type GetChatMembersReqBuilder struct {
	apiReq *larkcore.ApiReq
	limit  int // 最大返回多少记录，当使用迭代器访问时才有效
}

func NewGetChatMembersReqBuilder() *GetChatMembersReqBuilder {
	builder := &GetChatMembersReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 最大返回多少记录，当使用迭代器访问时才有效
func (builder *GetChatMembersReqBuilder) Limit(limit int) *GetChatMembersReqBuilder {
	builder.limit = limit
	return builder
}

// 群 ID，详情参见[群ID 说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-id-description)
//
// 示例值：oc_a0553eda9014c201e6969b478895c230
func (builder *GetChatMembersReqBuilder) ChatId(chatId string) *GetChatMembersReqBuilder {
	builder.apiReq.PathParams.Set("chat_id", fmt.Sprint(chatId))
	return builder
}

// 群成员 用户 ID 类型，详情参见 [用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction)
//
// 示例值：open_id
func (builder *GetChatMembersReqBuilder) MemberIdType(memberIdType string) *GetChatMembersReqBuilder {
	builder.apiReq.QueryParams.Set("member_id_type", fmt.Sprint(memberIdType))
	return builder
}

//
//
// 示例值：dmJCRHhpd3JRbGV1VEVNRFFyTitRWDY5ZFkybmYrMEUwMUFYT0VMMWdENEtuYUhsNUxGMDIwemtvdE5ORjBNQQ==
func (builder *GetChatMembersReqBuilder) PageToken(pageToken string) *GetChatMembersReqBuilder {
	builder.apiReq.QueryParams.Set("page_token", fmt.Sprint(pageToken))
	return builder
}

//
//
// 示例值：10
func (builder *GetChatMembersReqBuilder) PageSize(pageSize int) *GetChatMembersReqBuilder {
	builder.apiReq.QueryParams.Set("page_size", fmt.Sprint(pageSize))
	return builder
}

func (builder *GetChatMembersReqBuilder) Build() *GetChatMembersReq {
	req := &GetChatMembersReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.Limit = builder.limit
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type GetChatMembersReq struct {
	apiReq *larkcore.ApiReq
	Limit  int // 最多返回多少记录，只有在使用迭代器访问时，才有效

}

type GetChatMembersRespData struct {
	Items       []*ListMember `json:"items,omitempty"`        // 成员列表
	PageToken   *string       `json:"page_token,omitempty"`   //
	HasMore     *bool         `json:"has_more,omitempty"`     //
	MemberTotal *int          `json:"member_total,omitempty"` // 成员总数
}

type GetChatMembersResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *GetChatMembersRespData `json:"data"` // 业务数据
}

func (resp *GetChatMembersResp) Success() bool {
	return resp.Code == 0
}

type IsInChatChatMembersReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewIsInChatChatMembersReqBuilder() *IsInChatChatMembersReqBuilder {
	builder := &IsInChatChatMembersReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 群 ID，详情参见[群ID 说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-id-description)
//
// 示例值：oc_a0553eda9014c201e6969b478895c230
func (builder *IsInChatChatMembersReqBuilder) ChatId(chatId string) *IsInChatChatMembersReqBuilder {
	builder.apiReq.PathParams.Set("chat_id", fmt.Sprint(chatId))
	return builder
}

func (builder *IsInChatChatMembersReqBuilder) Build() *IsInChatChatMembersReq {
	req := &IsInChatChatMembersReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	return req
}

type IsInChatChatMembersReq struct {
	apiReq *larkcore.ApiReq
}

type IsInChatChatMembersRespData struct {
	IsInChat *bool `json:"is_in_chat,omitempty"` // 用户或者机器人是否在群中
}

type IsInChatChatMembersResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *IsInChatChatMembersRespData `json:"data"` // 业务数据
}

func (resp *IsInChatChatMembersResp) Success() bool {
	return resp.Code == 0
}

type MeJoinChatMembersReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewMeJoinChatMembersReqBuilder() *MeJoinChatMembersReqBuilder {
	builder := &MeJoinChatMembersReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 群 ID，详情参见[群ID 说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-id-description);;**注意**：;- 仅支持公开（Public）群类型;- 对于已认证企业的飞书的群人数默认上限：普通群5000人，会议群3000人，话题群5000人
//
// 示例值：oc_a0553eda9014c201e6969b478895c230
func (builder *MeJoinChatMembersReqBuilder) ChatId(chatId string) *MeJoinChatMembersReqBuilder {
	builder.apiReq.PathParams.Set("chat_id", fmt.Sprint(chatId))
	return builder
}

func (builder *MeJoinChatMembersReqBuilder) Build() *MeJoinChatMembersReq {
	req := &MeJoinChatMembersReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	return req
}

type MeJoinChatMembersReq struct {
	apiReq *larkcore.ApiReq
}

type MeJoinChatMembersResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *MeJoinChatMembersResp) Success() bool {
	return resp.Code == 0
}

type GetChatModerationReqBuilder struct {
	apiReq *larkcore.ApiReq
	limit  int // 最大返回多少记录，当使用迭代器访问时才有效
}

func NewGetChatModerationReqBuilder() *GetChatModerationReqBuilder {
	builder := &GetChatModerationReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 最大返回多少记录，当使用迭代器访问时才有效
func (builder *GetChatModerationReqBuilder) Limit(limit int) *GetChatModerationReqBuilder {
	builder.limit = limit
	return builder
}

// 群 ID，详情参见[群ID 说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-id-description)
//
// 示例值：oc_a0553eda9014c201e6969b478895c230
func (builder *GetChatModerationReqBuilder) ChatId(chatId string) *GetChatModerationReqBuilder {
	builder.apiReq.PathParams.Set("chat_id", fmt.Sprint(chatId))
	return builder
}

// 此次调用中使用的用户ID的类型
//
// 示例值：
func (builder *GetChatModerationReqBuilder) UserIdType(userIdType string) *GetChatModerationReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

//
//
// 示例值：dmJCRHhpd3JRbGV1VEVNRFFyTitRWDY5ZFkybmYrMEUwMUFYT0VMMWdENEtuYUhsNUxGMDIwemtvdE5ORjBNQQ==
func (builder *GetChatModerationReqBuilder) PageToken(pageToken string) *GetChatModerationReqBuilder {
	builder.apiReq.QueryParams.Set("page_token", fmt.Sprint(pageToken))
	return builder
}

//
//
// 示例值：10
func (builder *GetChatModerationReqBuilder) PageSize(pageSize int) *GetChatModerationReqBuilder {
	builder.apiReq.QueryParams.Set("page_size", fmt.Sprint(pageSize))
	return builder
}

func (builder *GetChatModerationReqBuilder) Build() *GetChatModerationReq {
	req := &GetChatModerationReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.Limit = builder.limit
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type GetChatModerationReq struct {
	apiReq *larkcore.ApiReq
	Limit  int // 最多返回多少记录，只有在使用迭代器访问时，才有效

}

type GetChatModerationRespData struct {
	ModerationSetting *string          `json:"moderation_setting,omitempty"` // 群发言模式（all_members/only_owner/moderator_list，其中 moderator_list 表示部分用户可发言的模式）
	PageToken         *string          `json:"page_token,omitempty"`         //
	HasMore           *bool            `json:"has_more,omitempty"`           //
	Items             []*ListModerator `json:"items,omitempty"`              // 可发言用户列表
}

type GetChatModerationResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *GetChatModerationRespData `json:"data"` // 业务数据
}

func (resp *GetChatModerationResp) Success() bool {
	return resp.Code == 0
}

type UpdateChatModerationReqBodyBuilder struct {
	moderationSetting        string // 群发言模式（all_members/only_owner/moderator_list，其中 moderator_list 表示部分用户可发言的模式）
	moderationSettingFlag    bool
	moderatorAddedList       []string // 选择部分用户可发言模式时，添加的可发言用户列表（自动过滤不在群内的用户）
	moderatorAddedListFlag   bool
	moderatorRemovedList     []string // 选择部分用户可发言模式时，移除的可发言用户列表（自动过滤不在群内的用户）
	moderatorRemovedListFlag bool
}

func NewUpdateChatModerationReqBodyBuilder() *UpdateChatModerationReqBodyBuilder {
	builder := &UpdateChatModerationReqBodyBuilder{}
	return builder
}

// 群发言模式（all_members/only_owner/moderator_list，其中 moderator_list 表示部分用户可发言的模式）
//
//示例值：moderator_list
func (builder *UpdateChatModerationReqBodyBuilder) ModerationSetting(moderationSetting string) *UpdateChatModerationReqBodyBuilder {
	builder.moderationSetting = moderationSetting
	builder.moderationSettingFlag = true
	return builder
}

// 选择部分用户可发言模式时，添加的可发言用户列表（自动过滤不在群内的用户）
//
//示例值：["4d7a3c6g"]
func (builder *UpdateChatModerationReqBodyBuilder) ModeratorAddedList(moderatorAddedList []string) *UpdateChatModerationReqBodyBuilder {
	builder.moderatorAddedList = moderatorAddedList
	builder.moderatorAddedListFlag = true
	return builder
}

// 选择部分用户可发言模式时，移除的可发言用户列表（自动过滤不在群内的用户）
//
//示例值：["4d7a3ih6"]
func (builder *UpdateChatModerationReqBodyBuilder) ModeratorRemovedList(moderatorRemovedList []string) *UpdateChatModerationReqBodyBuilder {
	builder.moderatorRemovedList = moderatorRemovedList
	builder.moderatorRemovedListFlag = true
	return builder
}

func (builder *UpdateChatModerationReqBodyBuilder) Build() *UpdateChatModerationReqBody {
	req := &UpdateChatModerationReqBody{}
	if builder.moderationSettingFlag {
		req.ModerationSetting = &builder.moderationSetting
	}
	if builder.moderatorAddedListFlag {
		req.ModeratorAddedList = builder.moderatorAddedList
	}
	if builder.moderatorRemovedListFlag {
		req.ModeratorRemovedList = builder.moderatorRemovedList
	}
	return req
}

type UpdateChatModerationPathReqBodyBuilder struct {
	moderationSetting        string // 群发言模式（all_members/only_owner/moderator_list，其中 moderator_list 表示部分用户可发言的模式）
	moderationSettingFlag    bool
	moderatorAddedList       []string // 选择部分用户可发言模式时，添加的可发言用户列表（自动过滤不在群内的用户）
	moderatorAddedListFlag   bool
	moderatorRemovedList     []string // 选择部分用户可发言模式时，移除的可发言用户列表（自动过滤不在群内的用户）
	moderatorRemovedListFlag bool
}

func NewUpdateChatModerationPathReqBodyBuilder() *UpdateChatModerationPathReqBodyBuilder {
	builder := &UpdateChatModerationPathReqBodyBuilder{}
	return builder
}

// 群发言模式（all_members/only_owner/moderator_list，其中 moderator_list 表示部分用户可发言的模式）
//
// 示例值：moderator_list
func (builder *UpdateChatModerationPathReqBodyBuilder) ModerationSetting(moderationSetting string) *UpdateChatModerationPathReqBodyBuilder {
	builder.moderationSetting = moderationSetting
	builder.moderationSettingFlag = true
	return builder
}

// 选择部分用户可发言模式时，添加的可发言用户列表（自动过滤不在群内的用户）
//
// 示例值：["4d7a3c6g"]
func (builder *UpdateChatModerationPathReqBodyBuilder) ModeratorAddedList(moderatorAddedList []string) *UpdateChatModerationPathReqBodyBuilder {
	builder.moderatorAddedList = moderatorAddedList
	builder.moderatorAddedListFlag = true
	return builder
}

// 选择部分用户可发言模式时，移除的可发言用户列表（自动过滤不在群内的用户）
//
// 示例值：["4d7a3ih6"]
func (builder *UpdateChatModerationPathReqBodyBuilder) ModeratorRemovedList(moderatorRemovedList []string) *UpdateChatModerationPathReqBodyBuilder {
	builder.moderatorRemovedList = moderatorRemovedList
	builder.moderatorRemovedListFlag = true
	return builder
}

func (builder *UpdateChatModerationPathReqBodyBuilder) Build() (*UpdateChatModerationReqBody, error) {
	req := &UpdateChatModerationReqBody{}
	if builder.moderationSettingFlag {
		req.ModerationSetting = &builder.moderationSetting
	}
	if builder.moderatorAddedListFlag {
		req.ModeratorAddedList = builder.moderatorAddedList
	}
	if builder.moderatorRemovedListFlag {
		req.ModeratorRemovedList = builder.moderatorRemovedList
	}
	return req, nil
}

type UpdateChatModerationReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *UpdateChatModerationReqBody
}

func NewUpdateChatModerationReqBuilder() *UpdateChatModerationReqBuilder {
	builder := &UpdateChatModerationReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 群 ID，详情参见[群ID 说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-id-description)
//
// 示例值：oc_a0553eda9014c201e6969b478895c230
func (builder *UpdateChatModerationReqBuilder) ChatId(chatId string) *UpdateChatModerationReqBuilder {
	builder.apiReq.PathParams.Set("chat_id", fmt.Sprint(chatId))
	return builder
}

// 此次调用中使用的用户ID的类型
//
// 示例值：
func (builder *UpdateChatModerationReqBuilder) UserIdType(userIdType string) *UpdateChatModerationReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

// 更新群组的发言权限设置，可设置为全员可发言、仅管理员可发言  或 指定用户可发言。
func (builder *UpdateChatModerationReqBuilder) Body(body *UpdateChatModerationReqBody) *UpdateChatModerationReqBuilder {
	builder.body = body
	return builder
}

func (builder *UpdateChatModerationReqBuilder) Build() *UpdateChatModerationReq {
	req := &UpdateChatModerationReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.body
	return req
}

type UpdateChatModerationReqBody struct {
	ModerationSetting    *string  `json:"moderation_setting,omitempty"`     // 群发言模式（all_members/only_owner/moderator_list，其中 moderator_list 表示部分用户可发言的模式）
	ModeratorAddedList   []string `json:"moderator_added_list,omitempty"`   // 选择部分用户可发言模式时，添加的可发言用户列表（自动过滤不在群内的用户）
	ModeratorRemovedList []string `json:"moderator_removed_list,omitempty"` // 选择部分用户可发言模式时，移除的可发言用户列表（自动过滤不在群内的用户）
}

type UpdateChatModerationReq struct {
	apiReq *larkcore.ApiReq
	Body   *UpdateChatModerationReqBody `body:""`
}

type UpdateChatModerationResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *UpdateChatModerationResp) Success() bool {
	return resp.Code == 0
}

type CreateChatTabReqBodyBuilder struct {
	chatTabs     []*ChatTab // 会话标签页;;**注意**：一个群内最多只允许添加20个自定义会话标签页
	chatTabsFlag bool
}

func NewCreateChatTabReqBodyBuilder() *CreateChatTabReqBodyBuilder {
	builder := &CreateChatTabReqBodyBuilder{}
	return builder
}

// 会话标签页;;**注意**：一个群内最多只允许添加20个自定义会话标签页
//
//示例值：
func (builder *CreateChatTabReqBodyBuilder) ChatTabs(chatTabs []*ChatTab) *CreateChatTabReqBodyBuilder {
	builder.chatTabs = chatTabs
	builder.chatTabsFlag = true
	return builder
}

func (builder *CreateChatTabReqBodyBuilder) Build() *CreateChatTabReqBody {
	req := &CreateChatTabReqBody{}
	if builder.chatTabsFlag {
		req.ChatTabs = builder.chatTabs
	}
	return req
}

type CreateChatTabPathReqBodyBuilder struct {
	chatTabs     []*ChatTab // 会话标签页;;**注意**：一个群内最多只允许添加20个自定义会话标签页
	chatTabsFlag bool
}

func NewCreateChatTabPathReqBodyBuilder() *CreateChatTabPathReqBodyBuilder {
	builder := &CreateChatTabPathReqBodyBuilder{}
	return builder
}

// 会话标签页;;**注意**：一个群内最多只允许添加20个自定义会话标签页
//
// 示例值：
func (builder *CreateChatTabPathReqBodyBuilder) ChatTabs(chatTabs []*ChatTab) *CreateChatTabPathReqBodyBuilder {
	builder.chatTabs = chatTabs
	builder.chatTabsFlag = true
	return builder
}

func (builder *CreateChatTabPathReqBodyBuilder) Build() (*CreateChatTabReqBody, error) {
	req := &CreateChatTabReqBody{}
	if builder.chatTabsFlag {
		req.ChatTabs = builder.chatTabs
	}
	return req, nil
}

type CreateChatTabReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *CreateChatTabReqBody
}

func NewCreateChatTabReqBuilder() *CreateChatTabReqBuilder {
	builder := &CreateChatTabReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 群ID，详情参见[群ID 说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-id-description);;**注意**：支持群模式为`p2p`与`group`的群ID
//
// 示例值：oc_a0553eda9014c201e6969b478895c230
func (builder *CreateChatTabReqBuilder) ChatId(chatId string) *CreateChatTabReqBuilder {
	builder.apiReq.PathParams.Set("chat_id", fmt.Sprint(chatId))
	return builder
}

// 添加自定义会话标签页。
func (builder *CreateChatTabReqBuilder) Body(body *CreateChatTabReqBody) *CreateChatTabReqBuilder {
	builder.body = body
	return builder
}

func (builder *CreateChatTabReqBuilder) Build() *CreateChatTabReq {
	req := &CreateChatTabReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.Body = builder.body
	return req
}

type CreateChatTabReqBody struct {
	ChatTabs []*ChatTab `json:"chat_tabs,omitempty"` // 会话标签页;;**注意**：一个群内最多只允许添加20个自定义会话标签页
}

type CreateChatTabReq struct {
	apiReq *larkcore.ApiReq
	Body   *CreateChatTabReqBody `body:""`
}

type CreateChatTabRespData struct {
	ChatTabs []*ChatTab `json:"chat_tabs,omitempty"` // 会话标签页
}

type CreateChatTabResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *CreateChatTabRespData `json:"data"` // 业务数据
}

func (resp *CreateChatTabResp) Success() bool {
	return resp.Code == 0
}

type DeleteTabsChatTabReqBodyBuilder struct {
	tabIds     []string // 会话标签页ID列表，Tab ID可以在[添加会话标签页](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-tab/create)与[拉取会话标签页](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-tab/list_tabs)的返回值中获取
	tabIdsFlag bool
}

func NewDeleteTabsChatTabReqBodyBuilder() *DeleteTabsChatTabReqBodyBuilder {
	builder := &DeleteTabsChatTabReqBodyBuilder{}
	return builder
}

// 会话标签页ID列表，Tab ID可以在[添加会话标签页](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-tab/create)与[拉取会话标签页](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-tab/list_tabs)的返回值中获取
//
//示例值：["7101214603622940671", "7101214603622940672"]
func (builder *DeleteTabsChatTabReqBodyBuilder) TabIds(tabIds []string) *DeleteTabsChatTabReqBodyBuilder {
	builder.tabIds = tabIds
	builder.tabIdsFlag = true
	return builder
}

func (builder *DeleteTabsChatTabReqBodyBuilder) Build() *DeleteTabsChatTabReqBody {
	req := &DeleteTabsChatTabReqBody{}
	if builder.tabIdsFlag {
		req.TabIds = builder.tabIds
	}
	return req
}

type DeleteTabsChatTabPathReqBodyBuilder struct {
	tabIds     []string // 会话标签页ID列表，Tab ID可以在[添加会话标签页](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-tab/create)与[拉取会话标签页](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-tab/list_tabs)的返回值中获取
	tabIdsFlag bool
}

func NewDeleteTabsChatTabPathReqBodyBuilder() *DeleteTabsChatTabPathReqBodyBuilder {
	builder := &DeleteTabsChatTabPathReqBodyBuilder{}
	return builder
}

// 会话标签页ID列表，Tab ID可以在[添加会话标签页](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-tab/create)与[拉取会话标签页](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-tab/list_tabs)的返回值中获取
//
// 示例值：["7101214603622940671", "7101214603622940672"]
func (builder *DeleteTabsChatTabPathReqBodyBuilder) TabIds(tabIds []string) *DeleteTabsChatTabPathReqBodyBuilder {
	builder.tabIds = tabIds
	builder.tabIdsFlag = true
	return builder
}

func (builder *DeleteTabsChatTabPathReqBodyBuilder) Build() (*DeleteTabsChatTabReqBody, error) {
	req := &DeleteTabsChatTabReqBody{}
	if builder.tabIdsFlag {
		req.TabIds = builder.tabIds
	}
	return req, nil
}

type DeleteTabsChatTabReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *DeleteTabsChatTabReqBody
}

func NewDeleteTabsChatTabReqBuilder() *DeleteTabsChatTabReqBuilder {
	builder := &DeleteTabsChatTabReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 群ID，详情参见[群ID 说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-id-description);;**注意**：支持群模式为`p2p`与`group`的群ID
//
// 示例值：oc_a0553eda9014c201e6969b478895c230
func (builder *DeleteTabsChatTabReqBuilder) ChatId(chatId string) *DeleteTabsChatTabReqBuilder {
	builder.apiReq.PathParams.Set("chat_id", fmt.Sprint(chatId))
	return builder
}

// 删除会话标签页。
func (builder *DeleteTabsChatTabReqBuilder) Body(body *DeleteTabsChatTabReqBody) *DeleteTabsChatTabReqBuilder {
	builder.body = body
	return builder
}

func (builder *DeleteTabsChatTabReqBuilder) Build() *DeleteTabsChatTabReq {
	req := &DeleteTabsChatTabReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.Body = builder.body
	return req
}

type DeleteTabsChatTabReqBody struct {
	TabIds []string `json:"tab_ids,omitempty"` // 会话标签页ID列表，Tab ID可以在[添加会话标签页](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-tab/create)与[拉取会话标签页](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-tab/list_tabs)的返回值中获取
}

type DeleteTabsChatTabReq struct {
	apiReq *larkcore.ApiReq
	Body   *DeleteTabsChatTabReqBody `body:""`
}

type DeleteTabsChatTabRespData struct {
	ChatTabs []*ChatTab `json:"chat_tabs,omitempty"` // 会话标签页
}

type DeleteTabsChatTabResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *DeleteTabsChatTabRespData `json:"data"` // 业务数据
}

func (resp *DeleteTabsChatTabResp) Success() bool {
	return resp.Code == 0
}

type ListTabsChatTabReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewListTabsChatTabReqBuilder() *ListTabsChatTabReqBuilder {
	builder := &ListTabsChatTabReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 群ID，详情参见[群ID 说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-id-description);;**注意**：支持群模式为`p2p`与`group`的群ID
//
// 示例值：oc_a0553eda9014c201e6969b478895c230
func (builder *ListTabsChatTabReqBuilder) ChatId(chatId string) *ListTabsChatTabReqBuilder {
	builder.apiReq.PathParams.Set("chat_id", fmt.Sprint(chatId))
	return builder
}

func (builder *ListTabsChatTabReqBuilder) Build() *ListTabsChatTabReq {
	req := &ListTabsChatTabReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	return req
}

type ListTabsChatTabReq struct {
	apiReq *larkcore.ApiReq
}

type ListTabsChatTabRespData struct {
	ChatTabs []*ChatTab `json:"chat_tabs,omitempty"` // 会话标签页
}

type ListTabsChatTabResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *ListTabsChatTabRespData `json:"data"` // 业务数据
}

func (resp *ListTabsChatTabResp) Success() bool {
	return resp.Code == 0
}

type SortTabsChatTabReqBodyBuilder struct {
	tabIds     []string // 会话标签页ID列表，Tab ID可以在[添加会话标签页](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-tab/create)与[拉取会话标签页](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-tab/list_tabs)的返回值中获取;;**注意**：必须包含该会话中全部的Tab ID
	tabIdsFlag bool
}

func NewSortTabsChatTabReqBodyBuilder() *SortTabsChatTabReqBodyBuilder {
	builder := &SortTabsChatTabReqBodyBuilder{}
	return builder
}

// 会话标签页ID列表，Tab ID可以在[添加会话标签页](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-tab/create)与[拉取会话标签页](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-tab/list_tabs)的返回值中获取;;**注意**：必须包含该会话中全部的Tab ID
//
//示例值：["7101214603622940671", "7101214603622940672"]
func (builder *SortTabsChatTabReqBodyBuilder) TabIds(tabIds []string) *SortTabsChatTabReqBodyBuilder {
	builder.tabIds = tabIds
	builder.tabIdsFlag = true
	return builder
}

func (builder *SortTabsChatTabReqBodyBuilder) Build() *SortTabsChatTabReqBody {
	req := &SortTabsChatTabReqBody{}
	if builder.tabIdsFlag {
		req.TabIds = builder.tabIds
	}
	return req
}

type SortTabsChatTabPathReqBodyBuilder struct {
	tabIds     []string // 会话标签页ID列表，Tab ID可以在[添加会话标签页](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-tab/create)与[拉取会话标签页](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-tab/list_tabs)的返回值中获取;;**注意**：必须包含该会话中全部的Tab ID
	tabIdsFlag bool
}

func NewSortTabsChatTabPathReqBodyBuilder() *SortTabsChatTabPathReqBodyBuilder {
	builder := &SortTabsChatTabPathReqBodyBuilder{}
	return builder
}

// 会话标签页ID列表，Tab ID可以在[添加会话标签页](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-tab/create)与[拉取会话标签页](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-tab/list_tabs)的返回值中获取;;**注意**：必须包含该会话中全部的Tab ID
//
// 示例值：["7101214603622940671", "7101214603622940672"]
func (builder *SortTabsChatTabPathReqBodyBuilder) TabIds(tabIds []string) *SortTabsChatTabPathReqBodyBuilder {
	builder.tabIds = tabIds
	builder.tabIdsFlag = true
	return builder
}

func (builder *SortTabsChatTabPathReqBodyBuilder) Build() (*SortTabsChatTabReqBody, error) {
	req := &SortTabsChatTabReqBody{}
	if builder.tabIdsFlag {
		req.TabIds = builder.tabIds
	}
	return req, nil
}

type SortTabsChatTabReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *SortTabsChatTabReqBody
}

func NewSortTabsChatTabReqBuilder() *SortTabsChatTabReqBuilder {
	builder := &SortTabsChatTabReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 群ID，详情参见[群ID 说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-id-description);;**注意**：支持群模式为`p2p`与`group`的群ID
//
// 示例值：oc_a0553eda9014c201e6969b478895c230
func (builder *SortTabsChatTabReqBuilder) ChatId(chatId string) *SortTabsChatTabReqBuilder {
	builder.apiReq.PathParams.Set("chat_id", fmt.Sprint(chatId))
	return builder
}

// 会话标签页排序。
func (builder *SortTabsChatTabReqBuilder) Body(body *SortTabsChatTabReqBody) *SortTabsChatTabReqBuilder {
	builder.body = body
	return builder
}

func (builder *SortTabsChatTabReqBuilder) Build() *SortTabsChatTabReq {
	req := &SortTabsChatTabReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.Body = builder.body
	return req
}

type SortTabsChatTabReqBody struct {
	TabIds []string `json:"tab_ids,omitempty"` // 会话标签页ID列表，Tab ID可以在[添加会话标签页](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-tab/create)与[拉取会话标签页](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-tab/list_tabs)的返回值中获取;;**注意**：必须包含该会话中全部的Tab ID
}

type SortTabsChatTabReq struct {
	apiReq *larkcore.ApiReq
	Body   *SortTabsChatTabReqBody `body:""`
}

type SortTabsChatTabRespData struct {
	ChatTabs []*ChatTab `json:"chat_tabs,omitempty"` // 会话标签页
}

type SortTabsChatTabResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *SortTabsChatTabRespData `json:"data"` // 业务数据
}

func (resp *SortTabsChatTabResp) Success() bool {
	return resp.Code == 0
}

type UpdateTabsChatTabReqBodyBuilder struct {
	chatTabs     []*ChatTab // 会话标签页
	chatTabsFlag bool
}

func NewUpdateTabsChatTabReqBodyBuilder() *UpdateTabsChatTabReqBodyBuilder {
	builder := &UpdateTabsChatTabReqBodyBuilder{}
	return builder
}

// 会话标签页
//
//示例值：
func (builder *UpdateTabsChatTabReqBodyBuilder) ChatTabs(chatTabs []*ChatTab) *UpdateTabsChatTabReqBodyBuilder {
	builder.chatTabs = chatTabs
	builder.chatTabsFlag = true
	return builder
}

func (builder *UpdateTabsChatTabReqBodyBuilder) Build() *UpdateTabsChatTabReqBody {
	req := &UpdateTabsChatTabReqBody{}
	if builder.chatTabsFlag {
		req.ChatTabs = builder.chatTabs
	}
	return req
}

type UpdateTabsChatTabPathReqBodyBuilder struct {
	chatTabs     []*ChatTab // 会话标签页
	chatTabsFlag bool
}

func NewUpdateTabsChatTabPathReqBodyBuilder() *UpdateTabsChatTabPathReqBodyBuilder {
	builder := &UpdateTabsChatTabPathReqBodyBuilder{}
	return builder
}

// 会话标签页
//
// 示例值：
func (builder *UpdateTabsChatTabPathReqBodyBuilder) ChatTabs(chatTabs []*ChatTab) *UpdateTabsChatTabPathReqBodyBuilder {
	builder.chatTabs = chatTabs
	builder.chatTabsFlag = true
	return builder
}

func (builder *UpdateTabsChatTabPathReqBodyBuilder) Build() (*UpdateTabsChatTabReqBody, error) {
	req := &UpdateTabsChatTabReqBody{}
	if builder.chatTabsFlag {
		req.ChatTabs = builder.chatTabs
	}
	return req, nil
}

type UpdateTabsChatTabReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *UpdateTabsChatTabReqBody
}

func NewUpdateTabsChatTabReqBuilder() *UpdateTabsChatTabReqBuilder {
	builder := &UpdateTabsChatTabReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 群ID，详情参见[群ID 说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-id-description);;**注意**：支持群模式为`p2p`与`group`的群ID
//
// 示例值：oc_a0553eda9014c201e6969b478895c230
func (builder *UpdateTabsChatTabReqBuilder) ChatId(chatId string) *UpdateTabsChatTabReqBuilder {
	builder.apiReq.PathParams.Set("chat_id", fmt.Sprint(chatId))
	return builder
}

// 更新会话标签页
func (builder *UpdateTabsChatTabReqBuilder) Body(body *UpdateTabsChatTabReqBody) *UpdateTabsChatTabReqBuilder {
	builder.body = body
	return builder
}

func (builder *UpdateTabsChatTabReqBuilder) Build() *UpdateTabsChatTabReq {
	req := &UpdateTabsChatTabReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.Body = builder.body
	return req
}

type UpdateTabsChatTabReqBody struct {
	ChatTabs []*ChatTab `json:"chat_tabs,omitempty"` // 会话标签页
}

type UpdateTabsChatTabReq struct {
	apiReq *larkcore.ApiReq
	Body   *UpdateTabsChatTabReqBody `body:""`
}

type UpdateTabsChatTabRespData struct {
	ChatTabs []*ChatTab `json:"chat_tabs,omitempty"` // 群标签
}

type UpdateTabsChatTabResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *UpdateTabsChatTabRespData `json:"data"` // 业务数据
}

func (resp *UpdateTabsChatTabResp) Success() bool {
	return resp.Code == 0
}

type DeleteTopNoticeChatTopNoticeReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewDeleteTopNoticeChatTopNoticeReqBuilder() *DeleteTopNoticeChatTopNoticeReqBuilder {
	builder := &DeleteTopNoticeChatTopNoticeReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 待撤销置顶的群 ID，详情参见[群ID 说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-id-description)
//
// 示例值：oc_5ad11d72b830411d72b836c20
func (builder *DeleteTopNoticeChatTopNoticeReqBuilder) ChatId(chatId string) *DeleteTopNoticeChatTopNoticeReqBuilder {
	builder.apiReq.PathParams.Set("chat_id", fmt.Sprint(chatId))
	return builder
}

func (builder *DeleteTopNoticeChatTopNoticeReqBuilder) Build() *DeleteTopNoticeChatTopNoticeReq {
	req := &DeleteTopNoticeChatTopNoticeReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	return req
}

type DeleteTopNoticeChatTopNoticeReq struct {
	apiReq *larkcore.ApiReq
}

type DeleteTopNoticeChatTopNoticeResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *DeleteTopNoticeChatTopNoticeResp) Success() bool {
	return resp.Code == 0
}

type PutTopNoticeChatTopNoticeReqBodyBuilder struct {
	chatTopNotice     []*ChatTopNotice // 要进行发布的群置顶
	chatTopNoticeFlag bool
}

func NewPutTopNoticeChatTopNoticeReqBodyBuilder() *PutTopNoticeChatTopNoticeReqBodyBuilder {
	builder := &PutTopNoticeChatTopNoticeReqBodyBuilder{}
	return builder
}

// 要进行发布的群置顶
//
//示例值：
func (builder *PutTopNoticeChatTopNoticeReqBodyBuilder) ChatTopNotice(chatTopNotice []*ChatTopNotice) *PutTopNoticeChatTopNoticeReqBodyBuilder {
	builder.chatTopNotice = chatTopNotice
	builder.chatTopNoticeFlag = true
	return builder
}

func (builder *PutTopNoticeChatTopNoticeReqBodyBuilder) Build() *PutTopNoticeChatTopNoticeReqBody {
	req := &PutTopNoticeChatTopNoticeReqBody{}
	if builder.chatTopNoticeFlag {
		req.ChatTopNotice = builder.chatTopNotice
	}
	return req
}

type PutTopNoticeChatTopNoticePathReqBodyBuilder struct {
	chatTopNotice     []*ChatTopNotice // 要进行发布的群置顶
	chatTopNoticeFlag bool
}

func NewPutTopNoticeChatTopNoticePathReqBodyBuilder() *PutTopNoticeChatTopNoticePathReqBodyBuilder {
	builder := &PutTopNoticeChatTopNoticePathReqBodyBuilder{}
	return builder
}

// 要进行发布的群置顶
//
// 示例值：
func (builder *PutTopNoticeChatTopNoticePathReqBodyBuilder) ChatTopNotice(chatTopNotice []*ChatTopNotice) *PutTopNoticeChatTopNoticePathReqBodyBuilder {
	builder.chatTopNotice = chatTopNotice
	builder.chatTopNoticeFlag = true
	return builder
}

func (builder *PutTopNoticeChatTopNoticePathReqBodyBuilder) Build() (*PutTopNoticeChatTopNoticeReqBody, error) {
	req := &PutTopNoticeChatTopNoticeReqBody{}
	if builder.chatTopNoticeFlag {
		req.ChatTopNotice = builder.chatTopNotice
	}
	return req, nil
}

type PutTopNoticeChatTopNoticeReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *PutTopNoticeChatTopNoticeReqBody
}

func NewPutTopNoticeChatTopNoticeReqBuilder() *PutTopNoticeChatTopNoticeReqBuilder {
	builder := &PutTopNoticeChatTopNoticeReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 待修改置顶的群 ID，详情参见[群ID 说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-id-description)
//
// 示例值：oc_5ad11d72b830411d72b836c20
func (builder *PutTopNoticeChatTopNoticeReqBuilder) ChatId(chatId string) *PutTopNoticeChatTopNoticeReqBuilder {
	builder.apiReq.PathParams.Set("chat_id", fmt.Sprint(chatId))
	return builder
}

// 更新会话中的群置顶信息，可以将群中的某一条消息，或者群公告置顶显示。
func (builder *PutTopNoticeChatTopNoticeReqBuilder) Body(body *PutTopNoticeChatTopNoticeReqBody) *PutTopNoticeChatTopNoticeReqBuilder {
	builder.body = body
	return builder
}

func (builder *PutTopNoticeChatTopNoticeReqBuilder) Build() *PutTopNoticeChatTopNoticeReq {
	req := &PutTopNoticeChatTopNoticeReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.Body = builder.body
	return req
}

type PutTopNoticeChatTopNoticeReqBody struct {
	ChatTopNotice []*ChatTopNotice `json:"chat_top_notice,omitempty"` // 要进行发布的群置顶
}

type PutTopNoticeChatTopNoticeReq struct {
	apiReq *larkcore.ApiReq
	Body   *PutTopNoticeChatTopNoticeReqBody `body:""`
}

type PutTopNoticeChatTopNoticeResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *PutTopNoticeChatTopNoticeResp) Success() bool {
	return resp.Code == 0
}

type CreateFileReqBodyBuilder struct {
	fileType     string // 文件类型
	fileTypeFlag bool
	fileName     string // 带后缀的文件名
	fileNameFlag bool
	duration     int // 文件的时长（视频、音频），单位:毫秒。不填充时无法显示具体时长。
	durationFlag bool
	file         io.Reader // 文件内容
	fileFlag     bool
}

func NewCreateFileReqBodyBuilder() *CreateFileReqBodyBuilder {
	builder := &CreateFileReqBodyBuilder{}
	return builder
}

// 文件类型
//
//示例值：mp4
func (builder *CreateFileReqBodyBuilder) FileType(fileType string) *CreateFileReqBodyBuilder {
	builder.fileType = fileType
	builder.fileTypeFlag = true
	return builder
}

// 带后缀的文件名
//
//示例值：测试视频.mp4
func (builder *CreateFileReqBodyBuilder) FileName(fileName string) *CreateFileReqBodyBuilder {
	builder.fileName = fileName
	builder.fileNameFlag = true
	return builder
}

// 文件的时长（视频、音频），单位:毫秒。不填充时无法显示具体时长。
//
//示例值：3000
func (builder *CreateFileReqBodyBuilder) Duration(duration int) *CreateFileReqBodyBuilder {
	builder.duration = duration
	builder.durationFlag = true
	return builder
}

// 文件内容
//
//示例值：二进制文件
func (builder *CreateFileReqBodyBuilder) File(file io.Reader) *CreateFileReqBodyBuilder {
	builder.file = file
	builder.fileFlag = true
	return builder
}

func (builder *CreateFileReqBodyBuilder) Build() *CreateFileReqBody {
	req := &CreateFileReqBody{}
	if builder.fileTypeFlag {
		req.FileType = &builder.fileType
	}
	if builder.fileNameFlag {
		req.FileName = &builder.fileName
	}
	if builder.durationFlag {
		req.Duration = &builder.duration
	}
	if builder.fileFlag {
		req.File = builder.file
	}
	return req
}

type CreateFilePathReqBodyBuilder struct {
	fileType     string // 文件类型
	fileTypeFlag bool
	fileName     string // 带后缀的文件名
	fileNameFlag bool
	duration     int // 文件的时长（视频、音频），单位:毫秒。不填充时无法显示具体时长。
	durationFlag bool
	filePath     string // 文件内容
	filePathFlag bool
}

func NewCreateFilePathReqBodyBuilder() *CreateFilePathReqBodyBuilder {
	builder := &CreateFilePathReqBodyBuilder{}
	return builder
}

// 文件类型
//
// 示例值：mp4
func (builder *CreateFilePathReqBodyBuilder) FileType(fileType string) *CreateFilePathReqBodyBuilder {
	builder.fileType = fileType
	builder.fileTypeFlag = true
	return builder
}

// 带后缀的文件名
//
// 示例值：测试视频.mp4
func (builder *CreateFilePathReqBodyBuilder) FileName(fileName string) *CreateFilePathReqBodyBuilder {
	builder.fileName = fileName
	builder.fileNameFlag = true
	return builder
}

// 文件的时长（视频、音频），单位:毫秒。不填充时无法显示具体时长。
//
// 示例值：3000
func (builder *CreateFilePathReqBodyBuilder) Duration(duration int) *CreateFilePathReqBodyBuilder {
	builder.duration = duration
	builder.durationFlag = true
	return builder
}

// 文件内容
//
// 示例值：二进制文件
func (builder *CreateFilePathReqBodyBuilder) FilePath(filePath string) *CreateFilePathReqBodyBuilder {
	builder.filePath = filePath
	builder.filePathFlag = true
	return builder
}

func (builder *CreateFilePathReqBodyBuilder) Build() (*CreateFileReqBody, error) {
	req := &CreateFileReqBody{}
	if builder.fileTypeFlag {
		req.FileType = &builder.fileType
	}
	if builder.fileNameFlag {
		req.FileName = &builder.fileName
	}
	if builder.durationFlag {
		req.Duration = &builder.duration
	}
	if builder.filePathFlag {
		data, err := larkcore.File2Bytes(builder.filePath)
		if err != nil {
			return nil, err
		}
		req.File = bytes.NewBuffer(data)
	}
	return req, nil
}

type CreateFileReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *CreateFileReqBody
}

func NewCreateFileReqBuilder() *CreateFileReqBuilder {
	builder := &CreateFileReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 上传文件，可以上传视频，音频和常见的文件类型。
func (builder *CreateFileReqBuilder) Body(body *CreateFileReqBody) *CreateFileReqBuilder {
	builder.body = body
	return builder
}

func (builder *CreateFileReqBuilder) Build() *CreateFileReq {
	req := &CreateFileReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.Body = builder.body
	return req
}

type CreateFileReqBody struct {
	FileType *string   `json:"file_type,omitempty"` // 文件类型
	FileName *string   `json:"file_name,omitempty"` // 带后缀的文件名
	Duration *int      `json:"duration,omitempty"`  // 文件的时长（视频、音频），单位:毫秒。不填充时无法显示具体时长。
	File     io.Reader `json:"file,omitempty"`      // 文件内容
}

type CreateFileReq struct {
	apiReq *larkcore.ApiReq
	Body   *CreateFileReqBody `body:""`
}

type CreateFileRespData struct {
	FileKey *string `json:"file_key,omitempty"` // 文件的key
}

type CreateFileResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *CreateFileRespData `json:"data"` // 业务数据
}

func (resp *CreateFileResp) Success() bool {
	return resp.Code == 0
}

type GetFileReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewGetFileReqBuilder() *GetFileReqBuilder {
	builder := &GetFileReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 文件的key，通过[上传文件](	https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/file/create)接口上传图片后获得
//
// 示例值：file_456a92d6-c6ea-4de4-ac3f-7afcf44ac78g
func (builder *GetFileReqBuilder) FileKey(fileKey string) *GetFileReqBuilder {
	builder.apiReq.PathParams.Set("file_key", fmt.Sprint(fileKey))
	return builder
}

func (builder *GetFileReqBuilder) Build() *GetFileReq {
	req := &GetFileReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	return req
}

type GetFileReq struct {
	apiReq *larkcore.ApiReq
}

type GetFileResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	File     io.Reader `json:"-"`
	FileName string    `json:"-"`
}

func (resp *GetFileResp) Success() bool {
	return resp.Code == 0
}

func (resp *GetFileResp) WriteFile(fileName string) error {
	bs, err := ioutil.ReadAll(resp.File)
	if err != nil {
		return err
	}

	err = ioutil.WriteFile(fileName, bs, 0666)
	if err != nil {
		return err
	}
	return nil
}

type CreateImageReqBodyBuilder struct {
	imageType     string // 图片类型
	imageTypeFlag bool
	image         io.Reader // 图片内容;; **注意：** 上传的图片大小不能超过10MB
	imageFlag     bool
}

func NewCreateImageReqBodyBuilder() *CreateImageReqBodyBuilder {
	builder := &CreateImageReqBodyBuilder{}
	return builder
}

// 图片类型
//
//示例值：message
func (builder *CreateImageReqBodyBuilder) ImageType(imageType string) *CreateImageReqBodyBuilder {
	builder.imageType = imageType
	builder.imageTypeFlag = true
	return builder
}

// 图片内容;; **注意：** 上传的图片大小不能超过10MB
//
//示例值：二进制文件
func (builder *CreateImageReqBodyBuilder) Image(image io.Reader) *CreateImageReqBodyBuilder {
	builder.image = image
	builder.imageFlag = true
	return builder
}

func (builder *CreateImageReqBodyBuilder) Build() *CreateImageReqBody {
	req := &CreateImageReqBody{}
	if builder.imageTypeFlag {
		req.ImageType = &builder.imageType
	}
	if builder.imageFlag {
		req.Image = builder.image
	}
	return req
}

type CreateImagePathReqBodyBuilder struct {
	imageType     string // 图片类型
	imageTypeFlag bool
	imagePath     string // 图片内容;; **注意：** 上传的图片大小不能超过10MB
	imagePathFlag bool
}

func NewCreateImagePathReqBodyBuilder() *CreateImagePathReqBodyBuilder {
	builder := &CreateImagePathReqBodyBuilder{}
	return builder
}

// 图片类型
//
// 示例值：message
func (builder *CreateImagePathReqBodyBuilder) ImageType(imageType string) *CreateImagePathReqBodyBuilder {
	builder.imageType = imageType
	builder.imageTypeFlag = true
	return builder
}

// 图片内容;; **注意：** 上传的图片大小不能超过10MB
//
// 示例值：二进制文件
func (builder *CreateImagePathReqBodyBuilder) ImagePath(imagePath string) *CreateImagePathReqBodyBuilder {
	builder.imagePath = imagePath
	builder.imagePathFlag = true
	return builder
}

func (builder *CreateImagePathReqBodyBuilder) Build() (*CreateImageReqBody, error) {
	req := &CreateImageReqBody{}
	if builder.imageTypeFlag {
		req.ImageType = &builder.imageType
	}
	if builder.imagePathFlag {
		data, err := larkcore.File2Bytes(builder.imagePath)
		if err != nil {
			return nil, err
		}
		req.Image = bytes.NewBuffer(data)
	}
	return req, nil
}

type CreateImageReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *CreateImageReqBody
}

func NewCreateImageReqBuilder() *CreateImageReqBuilder {
	builder := &CreateImageReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 上传图片接口，支持上传 JPEG、PNG、WEBP、GIF、TIFF、BMP、ICO格式图片。
func (builder *CreateImageReqBuilder) Body(body *CreateImageReqBody) *CreateImageReqBuilder {
	builder.body = body
	return builder
}

func (builder *CreateImageReqBuilder) Build() *CreateImageReq {
	req := &CreateImageReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.Body = builder.body
	return req
}

type CreateImageReqBody struct {
	ImageType *string   `json:"image_type,omitempty"` // 图片类型
	Image     io.Reader `json:"image,omitempty"`      // 图片内容;; **注意：** 上传的图片大小不能超过10MB
}

type CreateImageReq struct {
	apiReq *larkcore.ApiReq
	Body   *CreateImageReqBody `body:""`
}

type CreateImageRespData struct {
	ImageKey *string `json:"image_key,omitempty"` // 图片的key
}

type CreateImageResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *CreateImageRespData `json:"data"` // 业务数据
}

func (resp *CreateImageResp) Success() bool {
	return resp.Code == 0
}

type GetImageReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewGetImageReqBuilder() *GetImageReqBuilder {
	builder := &GetImageReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 图片的key，通过[上传图片](	https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/image/create)接口上传图片后获得
//
// 示例值：img_8d5181ca-0aed-40f0-b0d1-b1452132afbg
func (builder *GetImageReqBuilder) ImageKey(imageKey string) *GetImageReqBuilder {
	builder.apiReq.PathParams.Set("image_key", fmt.Sprint(imageKey))
	return builder
}

func (builder *GetImageReqBuilder) Build() *GetImageReq {
	req := &GetImageReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	return req
}

type GetImageReq struct {
	apiReq *larkcore.ApiReq
}

type GetImageResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	File     io.Reader `json:"-"`
	FileName string    `json:"-"`
}

func (resp *GetImageResp) Success() bool {
	return resp.Code == 0
}

func (resp *GetImageResp) WriteFile(fileName string) error {
	bs, err := ioutil.ReadAll(resp.File)
	if err != nil {
		return err
	}

	err = ioutil.WriteFile(fileName, bs, 0666)
	if err != nil {
		return err
	}
	return nil
}

type CreateMessageReqBodyBuilder struct {
	receiveId     string // 依据receive_id_type的值，填写对应的消息接收者id
	receiveIdFlag bool
	msgType       string // 消息类型 包括：text、post、image、file、audio、media、sticker、interactive、share_chat、share_user等，类型定义请参考[发送消息Content](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/im-v1/message/create_json)
	msgTypeFlag   bool
	content       string // 消息内容，json结构序列化后的字符串。不同msg_type对应不同内容。消息类型 包括：text、post、image、file、audio、media、sticker、interactive、share_chat、share_user等，具体格式说明参考：[发送消息Content](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/im-v1/message/create_json);;<b>请求体大小限制</b>：;- 文本消息请求体最大不能超过150KB;- 卡片及富文本消息请求体最大不能超过30KB
	contentFlag   bool
	uuid          string // 由开发者生成的唯一字符串序列，用于发送消息请求去重；持有相同uuid的请求1小时内至多成功执行一次
	uuidFlag      bool
}

func NewCreateMessageReqBodyBuilder() *CreateMessageReqBodyBuilder {
	builder := &CreateMessageReqBodyBuilder{}
	return builder
}

// 依据receive_id_type的值，填写对应的消息接收者id
//
//示例值：ou_7d8a6e6df7621556ce0d21922b676706ccs
func (builder *CreateMessageReqBodyBuilder) ReceiveId(receiveId string) *CreateMessageReqBodyBuilder {
	builder.receiveId = receiveId
	builder.receiveIdFlag = true
	return builder
}

// 消息类型 包括：text、post、image、file、audio、media、sticker、interactive、share_chat、share_user等，类型定义请参考[发送消息Content](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/im-v1/message/create_json)
//
//示例值：text
func (builder *CreateMessageReqBodyBuilder) MsgType(msgType string) *CreateMessageReqBodyBuilder {
	builder.msgType = msgType
	builder.msgTypeFlag = true
	return builder
}

// 消息内容，json结构序列化后的字符串。不同msg_type对应不同内容。消息类型 包括：text、post、image、file、audio、media、sticker、interactive、share_chat、share_user等，具体格式说明参考：[发送消息Content](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/im-v1/message/create_json);;<b>请求体大小限制</b>：;- 文本消息请求体最大不能超过150KB;- 卡片及富文本消息请求体最大不能超过30KB
//
//示例值：{\"text\":\"<at user_id=\\\"ou_155184d1e73cbfb8973e5a9e698e74f2\\\">Tom</at> test content\"}
func (builder *CreateMessageReqBodyBuilder) Content(content string) *CreateMessageReqBodyBuilder {
	builder.content = content
	builder.contentFlag = true
	return builder
}

// 由开发者生成的唯一字符串序列，用于发送消息请求去重；持有相同uuid的请求1小时内至多成功执行一次
//
//示例值：a0d69e20-1dd1-458b-k525-dfeca4015204
func (builder *CreateMessageReqBodyBuilder) Uuid(uuid string) *CreateMessageReqBodyBuilder {
	builder.uuid = uuid
	builder.uuidFlag = true
	return builder
}

func (builder *CreateMessageReqBodyBuilder) Build() *CreateMessageReqBody {
	req := &CreateMessageReqBody{}
	if builder.receiveIdFlag {
		req.ReceiveId = &builder.receiveId
	}
	if builder.msgTypeFlag {
		req.MsgType = &builder.msgType
	}
	if builder.contentFlag {
		req.Content = &builder.content
	}
	if builder.uuidFlag {
		req.Uuid = &builder.uuid
	}
	return req
}

type CreateMessagePathReqBodyBuilder struct {
	receiveId     string // 依据receive_id_type的值，填写对应的消息接收者id
	receiveIdFlag bool
	msgType       string // 消息类型 包括：text、post、image、file、audio、media、sticker、interactive、share_chat、share_user等，类型定义请参考[发送消息Content](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/im-v1/message/create_json)
	msgTypeFlag   bool
	content       string // 消息内容，json结构序列化后的字符串。不同msg_type对应不同内容。消息类型 包括：text、post、image、file、audio、media、sticker、interactive、share_chat、share_user等，具体格式说明参考：[发送消息Content](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/im-v1/message/create_json);;<b>请求体大小限制</b>：;- 文本消息请求体最大不能超过150KB;- 卡片及富文本消息请求体最大不能超过30KB
	contentFlag   bool
	uuid          string // 由开发者生成的唯一字符串序列，用于发送消息请求去重；持有相同uuid的请求1小时内至多成功执行一次
	uuidFlag      bool
}

func NewCreateMessagePathReqBodyBuilder() *CreateMessagePathReqBodyBuilder {
	builder := &CreateMessagePathReqBodyBuilder{}
	return builder
}

// 依据receive_id_type的值，填写对应的消息接收者id
//
// 示例值：ou_7d8a6e6df7621556ce0d21922b676706ccs
func (builder *CreateMessagePathReqBodyBuilder) ReceiveId(receiveId string) *CreateMessagePathReqBodyBuilder {
	builder.receiveId = receiveId
	builder.receiveIdFlag = true
	return builder
}

// 消息类型 包括：text、post、image、file、audio、media、sticker、interactive、share_chat、share_user等，类型定义请参考[发送消息Content](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/im-v1/message/create_json)
//
// 示例值：text
func (builder *CreateMessagePathReqBodyBuilder) MsgType(msgType string) *CreateMessagePathReqBodyBuilder {
	builder.msgType = msgType
	builder.msgTypeFlag = true
	return builder
}

// 消息内容，json结构序列化后的字符串。不同msg_type对应不同内容。消息类型 包括：text、post、image、file、audio、media、sticker、interactive、share_chat、share_user等，具体格式说明参考：[发送消息Content](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/im-v1/message/create_json);;<b>请求体大小限制</b>：;- 文本消息请求体最大不能超过150KB;- 卡片及富文本消息请求体最大不能超过30KB
//
// 示例值：{\"text\":\"<at user_id=\\\"ou_155184d1e73cbfb8973e5a9e698e74f2\\\">Tom</at> test content\"}
func (builder *CreateMessagePathReqBodyBuilder) Content(content string) *CreateMessagePathReqBodyBuilder {
	builder.content = content
	builder.contentFlag = true
	return builder
}

// 由开发者生成的唯一字符串序列，用于发送消息请求去重；持有相同uuid的请求1小时内至多成功执行一次
//
// 示例值：a0d69e20-1dd1-458b-k525-dfeca4015204
func (builder *CreateMessagePathReqBodyBuilder) Uuid(uuid string) *CreateMessagePathReqBodyBuilder {
	builder.uuid = uuid
	builder.uuidFlag = true
	return builder
}

func (builder *CreateMessagePathReqBodyBuilder) Build() (*CreateMessageReqBody, error) {
	req := &CreateMessageReqBody{}
	if builder.receiveIdFlag {
		req.ReceiveId = &builder.receiveId
	}
	if builder.msgTypeFlag {
		req.MsgType = &builder.msgType
	}
	if builder.contentFlag {
		req.Content = &builder.content
	}
	if builder.uuidFlag {
		req.Uuid = &builder.uuid
	}
	return req, nil
}

type CreateMessageReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *CreateMessageReqBody
}

func NewCreateMessageReqBuilder() *CreateMessageReqBuilder {
	builder := &CreateMessageReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 消息接收者id类型 open_id/user_id/union_id/email/chat_id
//
// 示例值：open_id
func (builder *CreateMessageReqBuilder) ReceiveIdType(receiveIdType string) *CreateMessageReqBuilder {
	builder.apiReq.QueryParams.Set("receive_id_type", fmt.Sprint(receiveIdType))
	return builder
}

// 给指定用户或者会话发送消息，支持文本、富文本、可交互的[消息卡片](https://open.feishu.cn/document/ukTMukTMukTM/uczM3QjL3MzN04yNzcDN)、群名片、个人名片、图片、视频、音频、文件、表情包。
func (builder *CreateMessageReqBuilder) Body(body *CreateMessageReqBody) *CreateMessageReqBuilder {
	builder.body = body
	return builder
}

func (builder *CreateMessageReqBuilder) Build() *CreateMessageReq {
	req := &CreateMessageReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.body
	return req
}

type CreateMessageReqBody struct {
	ReceiveId *string `json:"receive_id,omitempty"` // 依据receive_id_type的值，填写对应的消息接收者id
	MsgType   *string `json:"msg_type,omitempty"`   // 消息类型 包括：text、post、image、file、audio、media、sticker、interactive、share_chat、share_user等，类型定义请参考[发送消息Content](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/im-v1/message/create_json)
	Content   *string `json:"content,omitempty"`    // 消息内容，json结构序列化后的字符串。不同msg_type对应不同内容。消息类型 包括：text、post、image、file、audio、media、sticker、interactive、share_chat、share_user等，具体格式说明参考：[发送消息Content](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/im-v1/message/create_json);;<b>请求体大小限制</b>：;- 文本消息请求体最大不能超过150KB;- 卡片及富文本消息请求体最大不能超过30KB
	Uuid      *string `json:"uuid,omitempty"`       // 由开发者生成的唯一字符串序列，用于发送消息请求去重；持有相同uuid的请求1小时内至多成功执行一次
}

type CreateMessageReq struct {
	apiReq *larkcore.ApiReq
	Body   *CreateMessageReqBody `body:""`
}

type CreateMessageRespData struct {
	MessageId      *string      `json:"message_id,omitempty"`       // 消息id open_message_id
	RootId         *string      `json:"root_id,omitempty"`          // 根消息id open_message_id
	ParentId       *string      `json:"parent_id,omitempty"`        // 父消息的id open_message_id
	MsgType        *string      `json:"msg_type,omitempty"`         // 消息类型 text post card image等等
	CreateTime     *string      `json:"create_time,omitempty"`      // 消息生成的时间戳(毫秒)
	UpdateTime     *string      `json:"update_time,omitempty"`      // 消息更新的时间戳
	Deleted        *bool        `json:"deleted,omitempty"`          // 消息是否被撤回
	Updated        *bool        `json:"updated,omitempty"`          // 消息是否被更新
	ChatId         *string      `json:"chat_id,omitempty"`          // 所属的群
	Sender         *Sender      `json:"sender,omitempty"`           // 发送者，可以是用户或应用
	Body           *MessageBody `json:"body,omitempty"`             // 消息内容,json结构
	Mentions       []*Mention   `json:"mentions,omitempty"`         // 被艾特的人或应用的id
	UpperMessageId *string      `json:"upper_message_id,omitempty"` // 合并消息的上一层级消息id open_message_id
}

type CreateMessageResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *CreateMessageRespData `json:"data"` // 业务数据
}

func (resp *CreateMessageResp) Success() bool {
	return resp.Code == 0
}

type DeleteMessageReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewDeleteMessageReqBuilder() *DeleteMessageReqBuilder {
	builder := &DeleteMessageReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 待撤回的消息的ID
//
// 示例值：om_dc13264520392913993dd051dba21dcf
func (builder *DeleteMessageReqBuilder) MessageId(messageId string) *DeleteMessageReqBuilder {
	builder.apiReq.PathParams.Set("message_id", fmt.Sprint(messageId))
	return builder
}

func (builder *DeleteMessageReqBuilder) Build() *DeleteMessageReq {
	req := &DeleteMessageReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	return req
}

type DeleteMessageReq struct {
	apiReq *larkcore.ApiReq
}

type DeleteMessageResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *DeleteMessageResp) Success() bool {
	return resp.Code == 0
}

type GetMessageReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewGetMessageReqBuilder() *GetMessageReqBuilder {
	builder := &GetMessageReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 待获取消息内容的消息的ID
//
// 示例值：om_dc13264520392913993dd051dba21dcf
func (builder *GetMessageReqBuilder) MessageId(messageId string) *GetMessageReqBuilder {
	builder.apiReq.PathParams.Set("message_id", fmt.Sprint(messageId))
	return builder
}

func (builder *GetMessageReqBuilder) Build() *GetMessageReq {
	req := &GetMessageReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	return req
}

type GetMessageReq struct {
	apiReq *larkcore.ApiReq
}

type GetMessageRespData struct {
	Items []*Message `json:"items,omitempty"` // -
}

type GetMessageResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *GetMessageRespData `json:"data"` // 业务数据
}

func (resp *GetMessageResp) Success() bool {
	return resp.Code == 0
}

type ListMessageReqBuilder struct {
	apiReq *larkcore.ApiReq
	limit  int // 最大返回多少记录，当使用迭代器访问时才有效
}

func NewListMessageReqBuilder() *ListMessageReqBuilder {
	builder := &ListMessageReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 最大返回多少记录，当使用迭代器访问时才有效
func (builder *ListMessageReqBuilder) Limit(limit int) *ListMessageReqBuilder {
	builder.limit = limit
	return builder
}

// 容器类型 ，目前可选值仅有"chat"，包含单聊（p2p）和群聊（group）
//
// 示例值：chat
func (builder *ListMessageReqBuilder) ContainerIdType(containerIdType string) *ListMessageReqBuilder {
	builder.apiReq.QueryParams.Set("container_id_type", fmt.Sprint(containerIdType))
	return builder
}

// 容器的id，即chat的id，详情参见[群ID 说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-id-description)
//
// 示例值：oc_234jsi43d3ssi993d43545f
func (builder *ListMessageReqBuilder) ContainerId(containerId string) *ListMessageReqBuilder {
	builder.apiReq.QueryParams.Set("container_id", fmt.Sprint(containerId))
	return builder
}

// 历史信息的起始时间（秒级时间戳）
//
// 示例值：1609296809
func (builder *ListMessageReqBuilder) StartTime(startTime string) *ListMessageReqBuilder {
	builder.apiReq.QueryParams.Set("start_time", fmt.Sprint(startTime))
	return builder
}

// 历史信息的结束时间（秒级时间戳）
//
// 示例值：1608594809
func (builder *ListMessageReqBuilder) EndTime(endTime string) *ListMessageReqBuilder {
	builder.apiReq.QueryParams.Set("end_time", fmt.Sprint(endTime))
	return builder
}

//
//
// 示例值：GxmvlNRvP0NdQZpa7yIqf_Lv_QuBwTQ8tXkX7w-irAghVD_TvuYd1aoJ1LQph86O-XImC4X9j9FhUPhXQDvtrQ==
func (builder *ListMessageReqBuilder) PageToken(pageToken string) *ListMessageReqBuilder {
	builder.apiReq.QueryParams.Set("page_token", fmt.Sprint(pageToken))
	return builder
}

//
//
// 示例值：20
func (builder *ListMessageReqBuilder) PageSize(pageSize int) *ListMessageReqBuilder {
	builder.apiReq.QueryParams.Set("page_size", fmt.Sprint(pageSize))
	return builder
}

func (builder *ListMessageReqBuilder) Build() *ListMessageReq {
	req := &ListMessageReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.Limit = builder.limit
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type ListMessageReq struct {
	apiReq *larkcore.ApiReq
	Limit  int // 最多返回多少记录，只有在使用迭代器访问时，才有效

}

type ListMessageRespData struct {
	HasMore   *bool      `json:"has_more,omitempty"`   // 是否还有后续翻页
	PageToken *string    `json:"page_token,omitempty"` // 下一页分页的token
	Items     []*Message `json:"items,omitempty"`      // message[]
}

type ListMessageResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *ListMessageRespData `json:"data"` // 业务数据
}

func (resp *ListMessageResp) Success() bool {
	return resp.Code == 0
}

type PatchMessageReqBodyBuilder struct {
	content     string // 消息内容 json 格式，[发送消息 content 说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/im-v1/message/create_json)，参考文档中的卡片格式
	contentFlag bool
}

func NewPatchMessageReqBodyBuilder() *PatchMessageReqBodyBuilder {
	builder := &PatchMessageReqBodyBuilder{}
	return builder
}

// 消息内容 json 格式，[发送消息 content 说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/im-v1/message/create_json)，参考文档中的卡片格式
//
//示例值：参考链接
func (builder *PatchMessageReqBodyBuilder) Content(content string) *PatchMessageReqBodyBuilder {
	builder.content = content
	builder.contentFlag = true
	return builder
}

func (builder *PatchMessageReqBodyBuilder) Build() *PatchMessageReqBody {
	req := &PatchMessageReqBody{}
	if builder.contentFlag {
		req.Content = &builder.content
	}
	return req
}

type PatchMessagePathReqBodyBuilder struct {
	content     string // 消息内容 json 格式，[发送消息 content 说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/im-v1/message/create_json)，参考文档中的卡片格式
	contentFlag bool
}

func NewPatchMessagePathReqBodyBuilder() *PatchMessagePathReqBodyBuilder {
	builder := &PatchMessagePathReqBodyBuilder{}
	return builder
}

// 消息内容 json 格式，[发送消息 content 说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/im-v1/message/create_json)，参考文档中的卡片格式
//
// 示例值：参考链接
func (builder *PatchMessagePathReqBodyBuilder) Content(content string) *PatchMessagePathReqBodyBuilder {
	builder.content = content
	builder.contentFlag = true
	return builder
}

func (builder *PatchMessagePathReqBodyBuilder) Build() (*PatchMessageReqBody, error) {
	req := &PatchMessageReqBody{}
	if builder.contentFlag {
		req.Content = &builder.content
	}
	return req, nil
}

type PatchMessageReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *PatchMessageReqBody
}

func NewPatchMessageReqBuilder() *PatchMessageReqBuilder {
	builder := &PatchMessageReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 待更新的消息的ID，详情参见[消息ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/message/intro#ac79c1c2)
//
// 示例值：om_dc13264520392913993dd051dba21dcf
func (builder *PatchMessageReqBuilder) MessageId(messageId string) *PatchMessageReqBuilder {
	builder.apiReq.PathParams.Set("message_id", fmt.Sprint(messageId))
	return builder
}

// 更新应用已发送的消息卡片内容。
func (builder *PatchMessageReqBuilder) Body(body *PatchMessageReqBody) *PatchMessageReqBuilder {
	builder.body = body
	return builder
}

func (builder *PatchMessageReqBuilder) Build() *PatchMessageReq {
	req := &PatchMessageReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.Body = builder.body
	return req
}

type PatchMessageReqBody struct {
	Content *string `json:"content,omitempty"` // 消息内容 json 格式，[发送消息 content 说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/im-v1/message/create_json)，参考文档中的卡片格式
}

type PatchMessageReq struct {
	apiReq *larkcore.ApiReq
	Body   *PatchMessageReqBody `body:""`
}

type PatchMessageResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *PatchMessageResp) Success() bool {
	return resp.Code == 0
}

type ReadUsersMessageReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewReadUsersMessageReqBuilder() *ReadUsersMessageReqBuilder {
	builder := &ReadUsersMessageReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 待查询的消息的ID，说明参见：[消息ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/message/intro#ac79c1c2);;**注意**：不支持查询批量消息
//
// 示例值：om_dc13264520392913993dd051dba21dcf
func (builder *ReadUsersMessageReqBuilder) MessageId(messageId string) *ReadUsersMessageReqBuilder {
	builder.apiReq.PathParams.Set("message_id", fmt.Sprint(messageId))
	return builder
}

// 此次调用中使用的用户ID的类型
//
// 示例值：
func (builder *ReadUsersMessageReqBuilder) UserIdType(userIdType string) *ReadUsersMessageReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

// 此次调用中使用的分页的大小
//
// 示例值：20
func (builder *ReadUsersMessageReqBuilder) PageSize(pageSize int) *ReadUsersMessageReqBuilder {
	builder.apiReq.QueryParams.Set("page_size", fmt.Sprint(pageSize))
	return builder
}

// 下一页分页的token
//
// 示例值：GxmvlNRvP0NdQZpa7yIqf_Lv_QuBwTQ8tXkX7w-irAghVD_TvuYd1aoJ1LQph86O-XImC4X9j9FhUPhXQDvtrQ==
func (builder *ReadUsersMessageReqBuilder) PageToken(pageToken string) *ReadUsersMessageReqBuilder {
	builder.apiReq.QueryParams.Set("page_token", fmt.Sprint(pageToken))
	return builder
}

func (builder *ReadUsersMessageReqBuilder) Build() *ReadUsersMessageReq {
	req := &ReadUsersMessageReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type ReadUsersMessageReq struct {
	apiReq *larkcore.ApiReq
}

type ReadUsersMessageRespData struct {
	Items     []*ReadUser `json:"items,omitempty"`      // -
	HasMore   *bool       `json:"has_more,omitempty"`   // 是否还有下一页
	PageToken *string     `json:"page_token,omitempty"` // 下一页分页的token
}

type ReadUsersMessageResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *ReadUsersMessageRespData `json:"data"` // 业务数据
}

func (resp *ReadUsersMessageResp) Success() bool {
	return resp.Code == 0
}

type ReplyMessageReqBodyBuilder struct {
	content     string // 消息内容 json 格式，格式说明参考: [发送消息Content](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/im-v1/message/create_json)
	contentFlag bool
	msgType     string // 消息类型，包括：text、post、image、file、audio、media、sticker、interactive、share_card、share_user
	msgTypeFlag bool
	uuid        string // 由开发者生成的唯一字符串序列，用于回复消息请求去重；持有相同uuid的请求1小时内至多成功执行一次
	uuidFlag    bool
}

func NewReplyMessageReqBodyBuilder() *ReplyMessageReqBodyBuilder {
	builder := &ReplyMessageReqBodyBuilder{}
	return builder
}

// 消息内容 json 格式，格式说明参考: [发送消息Content](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/im-v1/message/create_json)
//
//示例值：{\"text\":\"<at user_id=\\\"ou_155184d1e73cbfb8973e5a9e698e74f2\\\">Tom </at> test content\"}
func (builder *ReplyMessageReqBodyBuilder) Content(content string) *ReplyMessageReqBodyBuilder {
	builder.content = content
	builder.contentFlag = true
	return builder
}

// 消息类型，包括：text、post、image、file、audio、media、sticker、interactive、share_card、share_user
//
//示例值：text
func (builder *ReplyMessageReqBodyBuilder) MsgType(msgType string) *ReplyMessageReqBodyBuilder {
	builder.msgType = msgType
	builder.msgTypeFlag = true
	return builder
}

// 由开发者生成的唯一字符串序列，用于回复消息请求去重；持有相同uuid的请求1小时内至多成功执行一次
//
//示例值：a0d69e20-1dd1-458b-k525-dfeca4015204
func (builder *ReplyMessageReqBodyBuilder) Uuid(uuid string) *ReplyMessageReqBodyBuilder {
	builder.uuid = uuid
	builder.uuidFlag = true
	return builder
}

func (builder *ReplyMessageReqBodyBuilder) Build() *ReplyMessageReqBody {
	req := &ReplyMessageReqBody{}
	if builder.contentFlag {
		req.Content = &builder.content
	}
	if builder.msgTypeFlag {
		req.MsgType = &builder.msgType
	}
	if builder.uuidFlag {
		req.Uuid = &builder.uuid
	}
	return req
}

type ReplyMessagePathReqBodyBuilder struct {
	content     string // 消息内容 json 格式，格式说明参考: [发送消息Content](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/im-v1/message/create_json)
	contentFlag bool
	msgType     string // 消息类型，包括：text、post、image、file、audio、media、sticker、interactive、share_card、share_user
	msgTypeFlag bool
	uuid        string // 由开发者生成的唯一字符串序列，用于回复消息请求去重；持有相同uuid的请求1小时内至多成功执行一次
	uuidFlag    bool
}

func NewReplyMessagePathReqBodyBuilder() *ReplyMessagePathReqBodyBuilder {
	builder := &ReplyMessagePathReqBodyBuilder{}
	return builder
}

// 消息内容 json 格式，格式说明参考: [发送消息Content](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/im-v1/message/create_json)
//
// 示例值：{\"text\":\"<at user_id=\\\"ou_155184d1e73cbfb8973e5a9e698e74f2\\\">Tom </at> test content\"}
func (builder *ReplyMessagePathReqBodyBuilder) Content(content string) *ReplyMessagePathReqBodyBuilder {
	builder.content = content
	builder.contentFlag = true
	return builder
}

// 消息类型，包括：text、post、image、file、audio、media、sticker、interactive、share_card、share_user
//
// 示例值：text
func (builder *ReplyMessagePathReqBodyBuilder) MsgType(msgType string) *ReplyMessagePathReqBodyBuilder {
	builder.msgType = msgType
	builder.msgTypeFlag = true
	return builder
}

// 由开发者生成的唯一字符串序列，用于回复消息请求去重；持有相同uuid的请求1小时内至多成功执行一次
//
// 示例值：a0d69e20-1dd1-458b-k525-dfeca4015204
func (builder *ReplyMessagePathReqBodyBuilder) Uuid(uuid string) *ReplyMessagePathReqBodyBuilder {
	builder.uuid = uuid
	builder.uuidFlag = true
	return builder
}

func (builder *ReplyMessagePathReqBodyBuilder) Build() (*ReplyMessageReqBody, error) {
	req := &ReplyMessageReqBody{}
	if builder.contentFlag {
		req.Content = &builder.content
	}
	if builder.msgTypeFlag {
		req.MsgType = &builder.msgType
	}
	if builder.uuidFlag {
		req.Uuid = &builder.uuid
	}
	return req, nil
}

type ReplyMessageReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *ReplyMessageReqBody
}

func NewReplyMessageReqBuilder() *ReplyMessageReqBuilder {
	builder := &ReplyMessageReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 待回复的消息的ID，详情参见[消息ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/message/intro#ac79c1c2)
//
// 示例值：om_dc13264520392913993dd051dba21dcf
func (builder *ReplyMessageReqBuilder) MessageId(messageId string) *ReplyMessageReqBuilder {
	builder.apiReq.PathParams.Set("message_id", fmt.Sprint(messageId))
	return builder
}

// 回复指定消息，支持文本、富文本、卡片、群名片、个人名片、图片、视频、文件等多种消息类型。
func (builder *ReplyMessageReqBuilder) Body(body *ReplyMessageReqBody) *ReplyMessageReqBuilder {
	builder.body = body
	return builder
}

func (builder *ReplyMessageReqBuilder) Build() *ReplyMessageReq {
	req := &ReplyMessageReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.Body = builder.body
	return req
}

type ReplyMessageReqBody struct {
	Content *string `json:"content,omitempty"`  // 消息内容 json 格式，格式说明参考: [发送消息Content](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/im-v1/message/create_json)
	MsgType *string `json:"msg_type,omitempty"` // 消息类型，包括：text、post、image、file、audio、media、sticker、interactive、share_card、share_user
	Uuid    *string `json:"uuid,omitempty"`     // 由开发者生成的唯一字符串序列，用于回复消息请求去重；持有相同uuid的请求1小时内至多成功执行一次
}

type ReplyMessageReq struct {
	apiReq *larkcore.ApiReq
	Body   *ReplyMessageReqBody `body:""`
}

type ReplyMessageRespData struct {
	MessageId      *string      `json:"message_id,omitempty"`       // 消息id open_message_id
	RootId         *string      `json:"root_id,omitempty"`          // 根消息id open_message_id
	ParentId       *string      `json:"parent_id,omitempty"`        // 父消息的id open_message_id
	MsgType        *string      `json:"msg_type,omitempty"`         // 消息类型 text post card image等等
	CreateTime     *string      `json:"create_time,omitempty"`      // 消息生成的时间戳(毫秒)
	UpdateTime     *string      `json:"update_time,omitempty"`      // 消息更新的时间戳
	Deleted        *bool        `json:"deleted,omitempty"`          // 消息是否被撤回
	Updated        *bool        `json:"updated,omitempty"`          // 消息是否被更新
	ChatId         *string      `json:"chat_id,omitempty"`          // 所属的群
	Sender         *Sender      `json:"sender,omitempty"`           // 发送者，可以是用户或应用
	Body           *MessageBody `json:"body,omitempty"`             // 消息内容,json结构
	Mentions       []*Mention   `json:"mentions,omitempty"`         // 被艾特的人或应用的id
	UpperMessageId *string      `json:"upper_message_id,omitempty"` // 合并消息的上一层级消息id open_message_id
}

type ReplyMessageResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *ReplyMessageRespData `json:"data"` // 业务数据
}

func (resp *ReplyMessageResp) Success() bool {
	return resp.Code == 0
}

type UrgentAppMessageReqBuilder struct {
	apiReq          *larkcore.ApiReq
	urgentReceivers *UrgentReceivers
}

func NewUrgentAppMessageReqBuilder() *UrgentAppMessageReqBuilder {
	builder := &UrgentAppMessageReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 待加急的消息ID，详情参见[消息ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/message/intro#ac79c1c2);;**注意**：不支持批量消息ID（bm_xxx）
//
// 示例值：om_dc13264520392913993dd051dba21dcf
func (builder *UrgentAppMessageReqBuilder) MessageId(messageId string) *UrgentAppMessageReqBuilder {
	builder.apiReq.PathParams.Set("message_id", fmt.Sprint(messageId))
	return builder
}

// 此次调用中使用的用户ID的类型
//
// 示例值：
func (builder *UrgentAppMessageReqBuilder) UserIdType(userIdType string) *UrgentAppMessageReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

// 对指定消息进行应用内加急。
func (builder *UrgentAppMessageReqBuilder) UrgentReceivers(urgentReceivers *UrgentReceivers) *UrgentAppMessageReqBuilder {
	builder.urgentReceivers = urgentReceivers
	return builder
}

func (builder *UrgentAppMessageReqBuilder) Build() *UrgentAppMessageReq {
	req := &UrgentAppMessageReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.urgentReceivers
	return req
}

type UrgentAppMessageReq struct {
	apiReq          *larkcore.ApiReq
	UrgentReceivers *UrgentReceivers `body:""`
}

type UrgentAppMessageRespData struct {
	InvalidUserIdList []string `json:"invalid_user_id_list,omitempty"` // 无效的用户ID
}

type UrgentAppMessageResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *UrgentAppMessageRespData `json:"data"` // 业务数据
}

func (resp *UrgentAppMessageResp) Success() bool {
	return resp.Code == 0
}

type UrgentPhoneMessageReqBuilder struct {
	apiReq          *larkcore.ApiReq
	urgentReceivers *UrgentReceivers
}

func NewUrgentPhoneMessageReqBuilder() *UrgentPhoneMessageReqBuilder {
	builder := &UrgentPhoneMessageReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 待加急的消息ID，详情参见[消息ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/message/intro#ac79c1c2);;**注意**：不支持批量消息ID（bm_xxx）
//
// 示例值：om_dc13264520392913993dd051dba21dcf
func (builder *UrgentPhoneMessageReqBuilder) MessageId(messageId string) *UrgentPhoneMessageReqBuilder {
	builder.apiReq.PathParams.Set("message_id", fmt.Sprint(messageId))
	return builder
}

// 此次调用中使用的用户ID的类型
//
// 示例值：
func (builder *UrgentPhoneMessageReqBuilder) UserIdType(userIdType string) *UrgentPhoneMessageReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

// 对指定消息进行应用内加急与电话加急。
func (builder *UrgentPhoneMessageReqBuilder) UrgentReceivers(urgentReceivers *UrgentReceivers) *UrgentPhoneMessageReqBuilder {
	builder.urgentReceivers = urgentReceivers
	return builder
}

func (builder *UrgentPhoneMessageReqBuilder) Build() *UrgentPhoneMessageReq {
	req := &UrgentPhoneMessageReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.urgentReceivers
	return req
}

type UrgentPhoneMessageReq struct {
	apiReq          *larkcore.ApiReq
	UrgentReceivers *UrgentReceivers `body:""`
}

type UrgentPhoneMessageRespData struct {
	InvalidUserIdList []string `json:"invalid_user_id_list,omitempty"` // 无效的用户ID
}

type UrgentPhoneMessageResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *UrgentPhoneMessageRespData `json:"data"` // 业务数据
}

func (resp *UrgentPhoneMessageResp) Success() bool {
	return resp.Code == 0
}

type UrgentSmsMessageReqBuilder struct {
	apiReq          *larkcore.ApiReq
	urgentReceivers *UrgentReceivers
}

func NewUrgentSmsMessageReqBuilder() *UrgentSmsMessageReqBuilder {
	builder := &UrgentSmsMessageReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 待加急的消息ID，详情参见[消息ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/message/intro#ac79c1c2);;**注意**：不支持批量消息ID（bm_xxx）
//
// 示例值：om_dc13264520392913993dd051dba21dcf
func (builder *UrgentSmsMessageReqBuilder) MessageId(messageId string) *UrgentSmsMessageReqBuilder {
	builder.apiReq.PathParams.Set("message_id", fmt.Sprint(messageId))
	return builder
}

// 此次调用中使用的用户ID的类型
//
// 示例值：
func (builder *UrgentSmsMessageReqBuilder) UserIdType(userIdType string) *UrgentSmsMessageReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

// 对指定消息进行应用内加急与短信加急。
func (builder *UrgentSmsMessageReqBuilder) UrgentReceivers(urgentReceivers *UrgentReceivers) *UrgentSmsMessageReqBuilder {
	builder.urgentReceivers = urgentReceivers
	return builder
}

func (builder *UrgentSmsMessageReqBuilder) Build() *UrgentSmsMessageReq {
	req := &UrgentSmsMessageReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.urgentReceivers
	return req
}

type UrgentSmsMessageReq struct {
	apiReq          *larkcore.ApiReq
	UrgentReceivers *UrgentReceivers `body:""`
}

type UrgentSmsMessageRespData struct {
	InvalidUserIdList []string `json:"invalid_user_id_list,omitempty"` // 无效的用户ID
}

type UrgentSmsMessageResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *UrgentSmsMessageRespData `json:"data"` // 业务数据
}

func (resp *UrgentSmsMessageResp) Success() bool {
	return resp.Code == 0
}

type CreateMessageReactionReqBodyBuilder struct {
	reactionType     *Emoji // reaction资源类型
	reactionTypeFlag bool
}

func NewCreateMessageReactionReqBodyBuilder() *CreateMessageReactionReqBodyBuilder {
	builder := &CreateMessageReactionReqBodyBuilder{}
	return builder
}

// reaction资源类型
//
//示例值：
func (builder *CreateMessageReactionReqBodyBuilder) ReactionType(reactionType *Emoji) *CreateMessageReactionReqBodyBuilder {
	builder.reactionType = reactionType
	builder.reactionTypeFlag = true
	return builder
}

func (builder *CreateMessageReactionReqBodyBuilder) Build() *CreateMessageReactionReqBody {
	req := &CreateMessageReactionReqBody{}
	if builder.reactionTypeFlag {
		req.ReactionType = builder.reactionType
	}
	return req
}

type CreateMessageReactionPathReqBodyBuilder struct {
	reactionType     *Emoji // reaction资源类型
	reactionTypeFlag bool
}

func NewCreateMessageReactionPathReqBodyBuilder() *CreateMessageReactionPathReqBodyBuilder {
	builder := &CreateMessageReactionPathReqBodyBuilder{}
	return builder
}

// reaction资源类型
//
// 示例值：
func (builder *CreateMessageReactionPathReqBodyBuilder) ReactionType(reactionType *Emoji) *CreateMessageReactionPathReqBodyBuilder {
	builder.reactionType = reactionType
	builder.reactionTypeFlag = true
	return builder
}

func (builder *CreateMessageReactionPathReqBodyBuilder) Build() (*CreateMessageReactionReqBody, error) {
	req := &CreateMessageReactionReqBody{}
	if builder.reactionTypeFlag {
		req.ReactionType = builder.reactionType
	}
	return req, nil
}

type CreateMessageReactionReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *CreateMessageReactionReqBody
}

func NewCreateMessageReactionReqBuilder() *CreateMessageReactionReqBuilder {
	builder := &CreateMessageReactionReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 待添加reaction的消息ID，详情参见[消息ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/message/intro#ac79c1c2)
//
// 示例值：om_a8f2294b************a1a38afaac9d
func (builder *CreateMessageReactionReqBuilder) MessageId(messageId string) *CreateMessageReactionReqBuilder {
	builder.apiReq.PathParams.Set("message_id", fmt.Sprint(messageId))
	return builder
}

// 给指定消息添加指定类型的表情回复（reaction即表情回复，本文档统一用“reaction”代称）。
func (builder *CreateMessageReactionReqBuilder) Body(body *CreateMessageReactionReqBody) *CreateMessageReactionReqBuilder {
	builder.body = body
	return builder
}

func (builder *CreateMessageReactionReqBuilder) Build() *CreateMessageReactionReq {
	req := &CreateMessageReactionReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.Body = builder.body
	return req
}

type CreateMessageReactionReqBody struct {
	ReactionType *Emoji `json:"reaction_type,omitempty"` // reaction资源类型
}

type CreateMessageReactionReq struct {
	apiReq *larkcore.ApiReq
	Body   *CreateMessageReactionReqBody `body:""`
}

type CreateMessageReactionRespData struct {
	ReactionId   *string   `json:"reaction_id,omitempty"`   // reaction资源ID
	Operator     *Operator `json:"operator,omitempty"`      // 添加reaction的操作人
	ActionTime   *string   `json:"action_time,omitempty"`   // reaction动作的的unix timestamp(单位:ms)
	ReactionType *Emoji    `json:"reaction_type,omitempty"` // reaction资源类型
}

type CreateMessageReactionResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *CreateMessageReactionRespData `json:"data"` // 业务数据
}

func (resp *CreateMessageReactionResp) Success() bool {
	return resp.Code == 0
}

type DeleteMessageReactionReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewDeleteMessageReactionReqBuilder() *DeleteMessageReactionReqBuilder {
	builder := &DeleteMessageReactionReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 待删除reaction的消息ID，详情参见[消息ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/message/intro#ac79c1c2)
//
// 示例值：om_8964d1b4*********2b31383276113
func (builder *DeleteMessageReactionReqBuilder) MessageId(messageId string) *DeleteMessageReactionReqBuilder {
	builder.apiReq.PathParams.Set("message_id", fmt.Sprint(messageId))
	return builder
}

// 待删除reaction的资源id，可通过调用[添加消息表情回复](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/message-reaction/create)接口或[获取消息表情回复](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/message-reaction/list)获得
//
// 示例值：ZCaCIjUBVVWSrm5L-3ZTw*************sNa8dHVplEzzSfJVUVLMLcS_
func (builder *DeleteMessageReactionReqBuilder) ReactionId(reactionId string) *DeleteMessageReactionReqBuilder {
	builder.apiReq.PathParams.Set("reaction_id", fmt.Sprint(reactionId))
	return builder
}

func (builder *DeleteMessageReactionReqBuilder) Build() *DeleteMessageReactionReq {
	req := &DeleteMessageReactionReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	return req
}

type DeleteMessageReactionReq struct {
	apiReq *larkcore.ApiReq
}

type DeleteMessageReactionRespData struct {
	ReactionId   *string   `json:"reaction_id,omitempty"`   // reaction资源ID
	Operator     *Operator `json:"operator,omitempty"`      // 添加reaction的操作人
	ActionTime   *string   `json:"action_time,omitempty"`   // reaction动作的的unix timestamp(单位:ms)
	ReactionType *Emoji    `json:"reaction_type,omitempty"` // reaction资源类型
}

type DeleteMessageReactionResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *DeleteMessageReactionRespData `json:"data"` // 业务数据
}

func (resp *DeleteMessageReactionResp) Success() bool {
	return resp.Code == 0
}

type ListMessageReactionReqBuilder struct {
	apiReq *larkcore.ApiReq
	limit  int // 最大返回多少记录，当使用迭代器访问时才有效
}

func NewListMessageReactionReqBuilder() *ListMessageReactionReqBuilder {
	builder := &ListMessageReactionReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 最大返回多少记录，当使用迭代器访问时才有效
func (builder *ListMessageReactionReqBuilder) Limit(limit int) *ListMessageReactionReqBuilder {
	builder.limit = limit
	return builder
}

// 待获取reaction的消息ID，详情参见[消息ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/message/intro#ac79c1c2)
//
// 示例值：om_8964d1b4*********2b31383276113
func (builder *ListMessageReactionReqBuilder) MessageId(messageId string) *ListMessageReactionReqBuilder {
	builder.apiReq.PathParams.Set("message_id", fmt.Sprint(messageId))
	return builder
}

// 待查询消息reaction的类型[emoji类型列举](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/message-reaction/emojis-introduce);;**注意**：不传入该参数，表示拉取所有类型reaction
//
// 示例值：LAUGH
func (builder *ListMessageReactionReqBuilder) ReactionType(reactionType string) *ListMessageReactionReqBuilder {
	builder.apiReq.QueryParams.Set("reaction_type", fmt.Sprint(reactionType))
	return builder
}

// 分页标记，第一次请求不填，表示从头开始遍历；分页查询结果还有更多项时，会同时返回新的 page_token，下次遍历可采用该 page_token 获取查询结果
//
// 示例值：YhljsPiGfUgnVAg9urvRFd-BvSqRL20wMZNAWfa9xXkud6UKCybPuUgQ1vM26dj6
func (builder *ListMessageReactionReqBuilder) PageToken(pageToken string) *ListMessageReactionReqBuilder {
	builder.apiReq.QueryParams.Set("page_token", fmt.Sprint(pageToken))
	return builder
}

// 分页大小
//
// 示例值：10
func (builder *ListMessageReactionReqBuilder) PageSize(pageSize int) *ListMessageReactionReqBuilder {
	builder.apiReq.QueryParams.Set("page_size", fmt.Sprint(pageSize))
	return builder
}

// 当操作人为用户时返回用户ID的类型
//
// 示例值：
func (builder *ListMessageReactionReqBuilder) UserIdType(userIdType string) *ListMessageReactionReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

func (builder *ListMessageReactionReqBuilder) Build() *ListMessageReactionReq {
	req := &ListMessageReactionReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.Limit = builder.limit
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type ListMessageReactionReq struct {
	apiReq *larkcore.ApiReq
	Limit  int // 最多返回多少记录，只有在使用迭代器访问时，才有效

}

type ListMessageReactionRespData struct {
	Items     []*MessageReaction `json:"items,omitempty"`      //  查询指定reaction_type返回的reaction列表
	HasMore   *bool              `json:"has_more,omitempty"`   // 是否还有后续翻页
	PageToken *string            `json:"page_token,omitempty"` //  下一页分页的token
}

type ListMessageReactionResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *ListMessageReactionRespData `json:"data"` // 业务数据
}

func (resp *ListMessageReactionResp) Success() bool {
	return resp.Code == 0
}

type GetMessageResourceReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewGetMessageResourceReqBuilder() *GetMessageResourceReqBuilder {
	builder := &GetMessageResourceReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 待查询资源对应的消息ID
//
// 示例值：om_dc13264520392913993dd051dba21dcf
func (builder *GetMessageResourceReqBuilder) MessageId(messageId string) *GetMessageResourceReqBuilder {
	builder.apiReq.PathParams.Set("message_id", fmt.Sprint(messageId))
	return builder
}

// 待查询资源的key;;**注意**：请求的 file_key 和 message_id 需要匹配
//
// 示例值：file_456a92d6-c6ea-4de4-ac3f-7afcf44ac78g
func (builder *GetMessageResourceReqBuilder) FileKey(fileKey string) *GetMessageResourceReqBuilder {
	builder.apiReq.PathParams.Set("file_key", fmt.Sprint(fileKey))
	return builder
}

// 资源类型，可选"image, file“； image对应消息中的 图片，富文本消息中的图片。  file对应消息中的 文件、音频、视频、（表情包除外）
//
// 示例值：image
func (builder *GetMessageResourceReqBuilder) Type(type_ string) *GetMessageResourceReqBuilder {
	builder.apiReq.QueryParams.Set("type", fmt.Sprint(type_))
	return builder
}

func (builder *GetMessageResourceReqBuilder) Build() *GetMessageResourceReq {
	req := &GetMessageResourceReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type GetMessageResourceReq struct {
	apiReq *larkcore.ApiReq
}

type GetMessageResourceResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	File     io.Reader `json:"-"`
	FileName string    `json:"-"`
}

func (resp *GetMessageResourceResp) Success() bool {
	return resp.Code == 0
}

func (resp *GetMessageResourceResp) WriteFile(fileName string) error {
	bs, err := ioutil.ReadAll(resp.File)
	if err != nil {
		return err
	}

	err = ioutil.WriteFile(fileName, bs, 0666)
	if err != nil {
		return err
	}
	return nil
}

type CreatePinReqBodyBuilder struct {
	messageId     string // 待Pin的消息ID，详情参见[消息ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/message/intro#ac79c1c2)
	messageIdFlag bool
}

func NewCreatePinReqBodyBuilder() *CreatePinReqBodyBuilder {
	builder := &CreatePinReqBodyBuilder{}
	return builder
}

// 待Pin的消息ID，详情参见[消息ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/message/intro#ac79c1c2)
//
//示例值：om_dc13264520392913993dd051dba21dcf
func (builder *CreatePinReqBodyBuilder) MessageId(messageId string) *CreatePinReqBodyBuilder {
	builder.messageId = messageId
	builder.messageIdFlag = true
	return builder
}

func (builder *CreatePinReqBodyBuilder) Build() *CreatePinReqBody {
	req := &CreatePinReqBody{}
	if builder.messageIdFlag {
		req.MessageId = &builder.messageId
	}
	return req
}

type CreatePinPathReqBodyBuilder struct {
	messageId     string // 待Pin的消息ID，详情参见[消息ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/message/intro#ac79c1c2)
	messageIdFlag bool
}

func NewCreatePinPathReqBodyBuilder() *CreatePinPathReqBodyBuilder {
	builder := &CreatePinPathReqBodyBuilder{}
	return builder
}

// 待Pin的消息ID，详情参见[消息ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/message/intro#ac79c1c2)
//
// 示例值：om_dc13264520392913993dd051dba21dcf
func (builder *CreatePinPathReqBodyBuilder) MessageId(messageId string) *CreatePinPathReqBodyBuilder {
	builder.messageId = messageId
	builder.messageIdFlag = true
	return builder
}

func (builder *CreatePinPathReqBodyBuilder) Build() (*CreatePinReqBody, error) {
	req := &CreatePinReqBody{}
	if builder.messageIdFlag {
		req.MessageId = &builder.messageId
	}
	return req, nil
}

type CreatePinReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *CreatePinReqBody
}

func NewCreatePinReqBuilder() *CreatePinReqBuilder {
	builder := &CreatePinReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// Pin一条指定的消息。
func (builder *CreatePinReqBuilder) Body(body *CreatePinReqBody) *CreatePinReqBuilder {
	builder.body = body
	return builder
}

func (builder *CreatePinReqBuilder) Build() *CreatePinReq {
	req := &CreatePinReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.Body = builder.body
	return req
}

type CreatePinReqBody struct {
	MessageId *string `json:"message_id,omitempty"` // 待Pin的消息ID，详情参见[消息ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/message/intro#ac79c1c2)
}

type CreatePinReq struct {
	apiReq *larkcore.ApiReq
	Body   *CreatePinReqBody `body:""`
}

type CreatePinRespData struct {
	Pin *Pin `json:"pin,omitempty"` // Pin的操作信息
}

type CreatePinResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *CreatePinRespData `json:"data"` // 业务数据
}

func (resp *CreatePinResp) Success() bool {
	return resp.Code == 0
}

type DeletePinReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewDeletePinReqBuilder() *DeletePinReqBuilder {
	builder := &DeletePinReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 待移除Pin的消息ID，详情参见[消息ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/message/intro#ac79c1c2)
//
// 示例值：om_dc13264520392913993dd051dba21dcf
func (builder *DeletePinReqBuilder) MessageId(messageId string) *DeletePinReqBuilder {
	builder.apiReq.PathParams.Set("message_id", fmt.Sprint(messageId))
	return builder
}

func (builder *DeletePinReqBuilder) Build() *DeletePinReq {
	req := &DeletePinReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	return req
}

type DeletePinReq struct {
	apiReq *larkcore.ApiReq
}

type DeletePinResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *DeletePinResp) Success() bool {
	return resp.Code == 0
}

type ListPinReqBuilder struct {
	apiReq *larkcore.ApiReq
	limit  int // 最大返回多少记录，当使用迭代器访问时才有效
}

func NewListPinReqBuilder() *ListPinReqBuilder {
	builder := &ListPinReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 最大返回多少记录，当使用迭代器访问时才有效
func (builder *ListPinReqBuilder) Limit(limit int) *ListPinReqBuilder {
	builder.limit = limit
	return builder
}

// 待获取Pin消息的Chat ID，详情参见[群ID 说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-id-description)
//
// 示例值：oc_234jsi43d3ssi993d43545f
func (builder *ListPinReqBuilder) ChatId(chatId string) *ListPinReqBuilder {
	builder.apiReq.QueryParams.Set("chat_id", fmt.Sprint(chatId))
	return builder
}

// Pin信息的起始时间（毫秒级时间戳）。若未填写默认获取到群聊内最早的Pin信息
//
// 示例值：1658632251800
func (builder *ListPinReqBuilder) StartTime(startTime string) *ListPinReqBuilder {
	builder.apiReq.QueryParams.Set("start_time", fmt.Sprint(startTime))
	return builder
}

// Pin信息的结束时间（毫秒级时间戳）。若未填写默认从群聊内最新的Pin信息开始获取;;**注意**：`end_time`值应大于`start_time`值
//
// 示例值：1658731646425
func (builder *ListPinReqBuilder) EndTime(endTime string) *ListPinReqBuilder {
	builder.apiReq.QueryParams.Set("end_time", fmt.Sprint(endTime))
	return builder
}

// 此次调用中使用的分页的大小
//
// 示例值：20
func (builder *ListPinReqBuilder) PageSize(pageSize int) *ListPinReqBuilder {
	builder.apiReq.QueryParams.Set("page_size", fmt.Sprint(pageSize))
	return builder
}

// 下一页分页的token
//
// 示例值：GxmvlNRvP0NdQZpa7yIqf_Lv_QuBwTQ8tXkX7w-irAghVD_TvuYd1aoJ1LQph86O-XImC4X9j9FhUPhXQDvtrQ==
func (builder *ListPinReqBuilder) PageToken(pageToken string) *ListPinReqBuilder {
	builder.apiReq.QueryParams.Set("page_token", fmt.Sprint(pageToken))
	return builder
}

func (builder *ListPinReqBuilder) Build() *ListPinReq {
	req := &ListPinReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.Limit = builder.limit
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type ListPinReq struct {
	apiReq *larkcore.ApiReq
	Limit  int // 最多返回多少记录，只有在使用迭代器访问时，才有效

}

type ListPinRespData struct {
	Items     []*Pin  `json:"items,omitempty"`      // Pin的操作信息
	HasMore   *bool   `json:"has_more,omitempty"`   // 是否还有更多项
	PageToken *string `json:"page_token,omitempty"` // 分页标记，当 has_more 为 true 时，会同时返回新的 page_token，否则不返回 page_token
}

type ListPinResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *ListPinRespData `json:"data"` // 业务数据
}

func (resp *ListPinResp) Success() bool {
	return resp.Code == 0
}

type P2ChatDisbandedV1Data struct {
	ChatId            *string    `json:"chat_id,omitempty"`             // 群组 ID，详情参见[群ID 说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-id-description)
	OperatorId        *UserId    `json:"operator_id,omitempty"`         // 操作者的ID
	External          *bool      `json:"external,omitempty"`            // 被解散的群是否是外部群
	OperatorTenantKey *string    `json:"operator_tenant_key,omitempty"` // 操作者的租户 Key，为租户在飞书上的唯一标识，用来换取对应的tenant_access_token，也可以用作租户在应用中的唯一标识
	Name              *string    `json:"name,omitempty"`                // 群名称
	I18nNames         *I18nNames `json:"i18n_names,omitempty"`          // 群国际化名称
}

type P2ChatDisbandedV1 struct {
	*larkevent.EventV2Base                        // 事件基础数据
	*larkevent.EventReq                           // 请求原生数据
	Event                  *P2ChatDisbandedV1Data `json:"event"` // 事件内容
}

func (m *P2ChatDisbandedV1) RawReq(req *larkevent.EventReq) {
	m.EventReq = req
}

type P2ChatUpdatedV1Data struct {
	ChatId            *string        `json:"chat_id,omitempty"`             // 群组 ID，详情参见[群ID 说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-id-description)
	OperatorId        *UserId        `json:"operator_id,omitempty"`         // 用户 ID
	External          *bool          `json:"external,omitempty"`            // 是否是外部群
	OperatorTenantKey *string        `json:"operator_tenant_key,omitempty"` // 操作者租户 Key
	AfterChange       *ChatChange    `json:"after_change,omitempty"`        // 更新后的群信息
	BeforeChange      *ChatChange    `json:"before_change,omitempty"`       // 更新前的群信息
	ModeratorList     *ModeratorList `json:"moderator_list,omitempty"`      // 群可发言成员名单的变更信息
}

type P2ChatUpdatedV1 struct {
	*larkevent.EventV2Base                      // 事件基础数据
	*larkevent.EventReq                         // 请求原生数据
	Event                  *P2ChatUpdatedV1Data `json:"event"` // 事件内容
}

func (m *P2ChatUpdatedV1) RawReq(req *larkevent.EventReq) {
	m.EventReq = req
}

type P2ChatMemberBotAddedV1Data struct {
	ChatId            *string    `json:"chat_id,omitempty"`             // 群组 ID，详情参见[群ID 说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-id-description)
	OperatorId        *UserId    `json:"operator_id,omitempty"`         // 用户 ID
	External          *bool      `json:"external,omitempty"`            // 是否是外部群
	OperatorTenantKey *string    `json:"operator_tenant_key,omitempty"` // 操作者的租户Key，为租户在飞书上的唯一标识，用来换取对应的tenant_access_token，也可以用作租户在应用中的唯一标识
	Name              *string    `json:"name,omitempty"`                // 群名称
	I18nNames         *I18nNames `json:"i18n_names,omitempty"`          // 群国际化名称
}

type P2ChatMemberBotAddedV1 struct {
	*larkevent.EventV2Base                             // 事件基础数据
	*larkevent.EventReq                                // 请求原生数据
	Event                  *P2ChatMemberBotAddedV1Data `json:"event"` // 事件内容
}

func (m *P2ChatMemberBotAddedV1) RawReq(req *larkevent.EventReq) {
	m.EventReq = req
}

type P2ChatMemberBotDeletedV1Data struct {
	ChatId            *string    `json:"chat_id,omitempty"`             // 群组 ID，详情参见[群ID 说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-id-description)
	OperatorId        *UserId    `json:"operator_id,omitempty"`         // 用户 ID
	External          *bool      `json:"external,omitempty"`            // 是否是外部群
	OperatorTenantKey *string    `json:"operator_tenant_key,omitempty"` // 操作者租户 Key
	Name              *string    `json:"name,omitempty"`                // 群名称
	I18nNames         *I18nNames `json:"i18n_names,omitempty"`          // 群国际化名称
}

type P2ChatMemberBotDeletedV1 struct {
	*larkevent.EventV2Base                               // 事件基础数据
	*larkevent.EventReq                                  // 请求原生数据
	Event                  *P2ChatMemberBotDeletedV1Data `json:"event"` // 事件内容
}

func (m *P2ChatMemberBotDeletedV1) RawReq(req *larkevent.EventReq) {
	m.EventReq = req
}

type P2ChatMemberUserAddedV1Data struct {
	ChatId            *string           `json:"chat_id,omitempty"`             // 群组 ID，详情参见[群ID 说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-id-description)
	OperatorId        *UserId           `json:"operator_id,omitempty"`         // 用户 ID
	External          *bool             `json:"external,omitempty"`            // 是否是外部群
	OperatorTenantKey *string           `json:"operator_tenant_key,omitempty"` // 操作者租户 Key
	Users             []*ChatMemberUser `json:"users,omitempty"`               // 被添加的用户列表
	Name              *string           `json:"name,omitempty"`                // 群名称
	I18nNames         *I18nNames        `json:"i18n_names,omitempty"`          // 群国际化名称
}

type P2ChatMemberUserAddedV1 struct {
	*larkevent.EventV2Base                              // 事件基础数据
	*larkevent.EventReq                                 // 请求原生数据
	Event                  *P2ChatMemberUserAddedV1Data `json:"event"` // 事件内容
}

func (m *P2ChatMemberUserAddedV1) RawReq(req *larkevent.EventReq) {
	m.EventReq = req
}

type P2ChatMemberUserDeletedV1Data struct {
	ChatId            *string           `json:"chat_id,omitempty"`             // 群组 ID，详情参见[群ID 说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-id-description)
	OperatorId        *UserId           `json:"operator_id,omitempty"`         // 用户 ID
	External          *bool             `json:"external,omitempty"`            // 是否是外部群
	OperatorTenantKey *string           `json:"operator_tenant_key,omitempty"` // 操作者的租户Key，为租户在飞书上的唯一标识，用来换取对应的tenant_access_token，也可以用作租户在应用中的唯一标识
	Users             []*ChatMemberUser `json:"users,omitempty"`               // 被移除用户列表
	Name              *string           `json:"name,omitempty"`                // 群名称
	I18nNames         *I18nNames        `json:"i18n_names,omitempty"`          // 群国际化名称
}

type P2ChatMemberUserDeletedV1 struct {
	*larkevent.EventV2Base                                // 事件基础数据
	*larkevent.EventReq                                   // 请求原生数据
	Event                  *P2ChatMemberUserDeletedV1Data `json:"event"` // 事件内容
}

func (m *P2ChatMemberUserDeletedV1) RawReq(req *larkevent.EventReq) {
	m.EventReq = req
}

type P2ChatMemberUserWithdrawnV1Data struct {
	ChatId            *string           `json:"chat_id,omitempty"`             // 群组 ID，详情参见[群ID 说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-id-description)
	OperatorId        *UserId           `json:"operator_id,omitempty"`         // 用户 ID
	External          *bool             `json:"external,omitempty"`            // 是否是外部群
	OperatorTenantKey *string           `json:"operator_tenant_key,omitempty"` // 操作者的租户Key，为租户在飞书上的唯一标识，用来换取对应的tenant_access_token，也可以用作租户在应用中的唯一标识
	Users             []*ChatMemberUser `json:"users,omitempty"`               // 被撤销加群的用户列表
	Name              *string           `json:"name,omitempty"`                // 群名称
	I18nNames         *I18nNames        `json:"i18n_names,omitempty"`          // 群国际化名称
}

type P2ChatMemberUserWithdrawnV1 struct {
	*larkevent.EventV2Base                                  // 事件基础数据
	*larkevent.EventReq                                     // 请求原生数据
	Event                  *P2ChatMemberUserWithdrawnV1Data `json:"event"` // 事件内容
}

func (m *P2ChatMemberUserWithdrawnV1) RawReq(req *larkevent.EventReq) {
	m.EventReq = req
}

type P2MessageReadV1Data struct {
	Reader        *EventMessageReader `json:"reader,omitempty"`          // -
	MessageIdList []string            `json:"message_id_list,omitempty"` // 消息列表
}

type P2MessageReadV1 struct {
	*larkevent.EventV2Base                      // 事件基础数据
	*larkevent.EventReq                         // 请求原生数据
	Event                  *P2MessageReadV1Data `json:"event"` // 事件内容
}

func (m *P2MessageReadV1) RawReq(req *larkevent.EventReq) {
	m.EventReq = req
}

type P2MessageRecalledV1Data struct {
	MessageId  *string `json:"message_id,omitempty"`  // 消息ID
	ChatId     *string `json:"chat_id,omitempty"`     // 群ID
	RecallTime *string `json:"recall_time,omitempty"` // 撤回事件
	RecallType *string `json:"recall_type,omitempty"` // 撤回类型
}

type P2MessageRecalledV1 struct {
	*larkevent.EventV2Base                          // 事件基础数据
	*larkevent.EventReq                             // 请求原生数据
	Event                  *P2MessageRecalledV1Data `json:"event"` // 事件内容
}

func (m *P2MessageRecalledV1) RawReq(req *larkevent.EventReq) {
	m.EventReq = req
}

type P2MessageReceiveV1Data struct {
	Sender  *EventSender  `json:"sender,omitempty"`  // 事件的发送者
	Message *EventMessage `json:"message,omitempty"` // 事件中包含的消息内容
}

type P2MessageReceiveV1 struct {
	*larkevent.EventV2Base                         // 事件基础数据
	*larkevent.EventReq                            // 请求原生数据
	Event                  *P2MessageReceiveV1Data `json:"event"` // 事件内容
}

func (m *P2MessageReceiveV1) RawReq(req *larkevent.EventReq) {
	m.EventReq = req
}

type P2MessageReactionCreatedV1Data struct {
	MessageId    *string `json:"message_id,omitempty"`    // 消息的 open_message_id
	ReactionType *Emoji  `json:"reaction_type,omitempty"` // 表情回复的资源类型
	OperatorType *string `json:"operator_type,omitempty"` // 操作人类型
	UserId       *UserId `json:"user_id,omitempty"`       // 用户 ID
	AppId        *string `json:"app_id,omitempty"`        // 应用 ID
	ActionTime   *string `json:"action_time,omitempty"`   // 添加表情回复时间戳（单位：ms）
}

type P2MessageReactionCreatedV1 struct {
	*larkevent.EventV2Base                                 // 事件基础数据
	*larkevent.EventReq                                    // 请求原生数据
	Event                  *P2MessageReactionCreatedV1Data `json:"event"` // 事件内容
}

func (m *P2MessageReactionCreatedV1) RawReq(req *larkevent.EventReq) {
	m.EventReq = req
}

type P2MessageReactionDeletedV1Data struct {
	MessageId    *string `json:"message_id,omitempty"`    // 消息的 open_message_id
	ReactionType *Emoji  `json:"reaction_type,omitempty"` // 表情回复的资源类型
	OperatorType *string `json:"operator_type,omitempty"` // 操作人类型
	UserId       *UserId `json:"user_id,omitempty"`       // 用户 ID
	AppId        *string `json:"app_id,omitempty"`        // 应用 ID
	ActionTime   *string `json:"action_time,omitempty"`   // 表情回复被添加时的时间戳（单位：ms）
}

type P2MessageReactionDeletedV1 struct {
	*larkevent.EventV2Base                                 // 事件基础数据
	*larkevent.EventReq                                    // 请求原生数据
	Event                  *P2MessageReactionDeletedV1Data `json:"event"` // 事件内容
}

func (m *P2MessageReactionDeletedV1) RawReq(req *larkevent.EventReq) {
	m.EventReq = req
}

type ListChatIterator struct {
	nextPageToken *string
	items         []*ListChat
	index         int
	limit         int
	ctx           context.Context
	req           *ListChatReq
	listFunc      func(ctx context.Context, req *ListChatReq, options ...larkcore.RequestOptionFunc) (*ListChatResp, error)
	options       []larkcore.RequestOptionFunc
	curlNum       int
}

func (iterator *ListChatIterator) Next() (bool, *ListChat, error) {
	// 达到最大量，则返回
	if iterator.limit > 0 && iterator.curlNum >= iterator.limit {
		return false, nil, nil
	}

	// 为0则拉取数据
	if iterator.index == 0 || iterator.index >= len(iterator.items) {
		if iterator.index != 0 && iterator.nextPageToken == nil {
			return false, nil, nil
		}
		if iterator.nextPageToken != nil {
			iterator.req.apiReq.QueryParams.Set("page_token", *iterator.nextPageToken)
		}
		resp, err := iterator.listFunc(iterator.ctx, iterator.req, iterator.options...)
		if err != nil {
			return false, nil, err
		}

		if resp.Code != 0 {
			return false, nil, errors.New(fmt.Sprintf("Code:%d,Msg:%s", resp.Code, resp.Msg))
		}

		if len(resp.Data.Items) == 0 {
			return false, nil, nil
		}

		iterator.nextPageToken = resp.Data.PageToken
		iterator.items = resp.Data.Items
		iterator.index = 0
	}

	block := iterator.items[iterator.index]
	iterator.index++
	iterator.curlNum++
	return true, block, nil
}

func (iterator *ListChatIterator) NextPageToken() *string {
	return iterator.nextPageToken
}

type SearchChatIterator struct {
	nextPageToken *string
	items         []*ListChat
	index         int
	limit         int
	ctx           context.Context
	req           *SearchChatReq
	listFunc      func(ctx context.Context, req *SearchChatReq, options ...larkcore.RequestOptionFunc) (*SearchChatResp, error)
	options       []larkcore.RequestOptionFunc
	curlNum       int
}

func (iterator *SearchChatIterator) Next() (bool, *ListChat, error) {
	// 达到最大量，则返回
	if iterator.limit > 0 && iterator.curlNum >= iterator.limit {
		return false, nil, nil
	}

	// 为0则拉取数据
	if iterator.index == 0 || iterator.index >= len(iterator.items) {
		if iterator.index != 0 && iterator.nextPageToken == nil {
			return false, nil, nil
		}
		if iterator.nextPageToken != nil {
			iterator.req.apiReq.QueryParams.Set("page_token", *iterator.nextPageToken)
		}
		resp, err := iterator.listFunc(iterator.ctx, iterator.req, iterator.options...)
		if err != nil {
			return false, nil, err
		}

		if resp.Code != 0 {
			return false, nil, errors.New(fmt.Sprintf("Code:%d,Msg:%s", resp.Code, resp.Msg))
		}

		if len(resp.Data.Items) == 0 {
			return false, nil, nil
		}

		iterator.nextPageToken = resp.Data.PageToken
		iterator.items = resp.Data.Items
		iterator.index = 0
	}

	block := iterator.items[iterator.index]
	iterator.index++
	iterator.curlNum++
	return true, block, nil
}

func (iterator *SearchChatIterator) NextPageToken() *string {
	return iterator.nextPageToken
}

type GetChatMembersIterator struct {
	nextPageToken *string
	items         []*ListMember
	index         int
	limit         int
	ctx           context.Context
	req           *GetChatMembersReq
	listFunc      func(ctx context.Context, req *GetChatMembersReq, options ...larkcore.RequestOptionFunc) (*GetChatMembersResp, error)
	options       []larkcore.RequestOptionFunc
	curlNum       int
}

func (iterator *GetChatMembersIterator) Next() (bool, *ListMember, error) {
	// 达到最大量，则返回
	if iterator.limit > 0 && iterator.curlNum >= iterator.limit {
		return false, nil, nil
	}

	// 为0则拉取数据
	if iterator.index == 0 || iterator.index >= len(iterator.items) {
		if iterator.index != 0 && iterator.nextPageToken == nil {
			return false, nil, nil
		}
		if iterator.nextPageToken != nil {
			iterator.req.apiReq.QueryParams.Set("page_token", *iterator.nextPageToken)
		}
		resp, err := iterator.listFunc(iterator.ctx, iterator.req, iterator.options...)
		if err != nil {
			return false, nil, err
		}

		if resp.Code != 0 {
			return false, nil, errors.New(fmt.Sprintf("Code:%d,Msg:%s", resp.Code, resp.Msg))
		}

		if len(resp.Data.Items) == 0 {
			return false, nil, nil
		}

		iterator.nextPageToken = resp.Data.PageToken
		iterator.items = resp.Data.Items
		iterator.index = 0
	}

	block := iterator.items[iterator.index]
	iterator.index++
	iterator.curlNum++
	return true, block, nil
}

func (iterator *GetChatMembersIterator) NextPageToken() *string {
	return iterator.nextPageToken
}

type GetChatModerationIterator struct {
	nextPageToken *string
	items         []*ListModerator
	index         int
	limit         int
	ctx           context.Context
	req           *GetChatModerationReq
	listFunc      func(ctx context.Context, req *GetChatModerationReq, options ...larkcore.RequestOptionFunc) (*GetChatModerationResp, error)
	options       []larkcore.RequestOptionFunc
	curlNum       int
}

func (iterator *GetChatModerationIterator) Next() (bool, *ListModerator, error) {
	// 达到最大量，则返回
	if iterator.limit > 0 && iterator.curlNum >= iterator.limit {
		return false, nil, nil
	}

	// 为0则拉取数据
	if iterator.index == 0 || iterator.index >= len(iterator.items) {
		if iterator.index != 0 && iterator.nextPageToken == nil {
			return false, nil, nil
		}
		if iterator.nextPageToken != nil {
			iterator.req.apiReq.QueryParams.Set("page_token", *iterator.nextPageToken)
		}
		resp, err := iterator.listFunc(iterator.ctx, iterator.req, iterator.options...)
		if err != nil {
			return false, nil, err
		}

		if resp.Code != 0 {
			return false, nil, errors.New(fmt.Sprintf("Code:%d,Msg:%s", resp.Code, resp.Msg))
		}

		if len(resp.Data.Items) == 0 {
			return false, nil, nil
		}

		iterator.nextPageToken = resp.Data.PageToken
		iterator.items = resp.Data.Items
		iterator.index = 0
	}

	block := iterator.items[iterator.index]
	iterator.index++
	iterator.curlNum++
	return true, block, nil
}

func (iterator *GetChatModerationIterator) NextPageToken() *string {
	return iterator.nextPageToken
}

type ListMessageIterator struct {
	nextPageToken *string
	items         []*Message
	index         int
	limit         int
	ctx           context.Context
	req           *ListMessageReq
	listFunc      func(ctx context.Context, req *ListMessageReq, options ...larkcore.RequestOptionFunc) (*ListMessageResp, error)
	options       []larkcore.RequestOptionFunc
	curlNum       int
}

func (iterator *ListMessageIterator) Next() (bool, *Message, error) {
	// 达到最大量，则返回
	if iterator.limit > 0 && iterator.curlNum >= iterator.limit {
		return false, nil, nil
	}

	// 为0则拉取数据
	if iterator.index == 0 || iterator.index >= len(iterator.items) {
		if iterator.index != 0 && iterator.nextPageToken == nil {
			return false, nil, nil
		}
		if iterator.nextPageToken != nil {
			iterator.req.apiReq.QueryParams.Set("page_token", *iterator.nextPageToken)
		}
		resp, err := iterator.listFunc(iterator.ctx, iterator.req, iterator.options...)
		if err != nil {
			return false, nil, err
		}

		if resp.Code != 0 {
			return false, nil, errors.New(fmt.Sprintf("Code:%d,Msg:%s", resp.Code, resp.Msg))
		}

		if len(resp.Data.Items) == 0 {
			return false, nil, nil
		}

		iterator.nextPageToken = resp.Data.PageToken
		iterator.items = resp.Data.Items
		iterator.index = 0
	}

	block := iterator.items[iterator.index]
	iterator.index++
	iterator.curlNum++
	return true, block, nil
}

func (iterator *ListMessageIterator) NextPageToken() *string {
	return iterator.nextPageToken
}

type ListMessageReactionIterator struct {
	nextPageToken *string
	items         []*MessageReaction
	index         int
	limit         int
	ctx           context.Context
	req           *ListMessageReactionReq
	listFunc      func(ctx context.Context, req *ListMessageReactionReq, options ...larkcore.RequestOptionFunc) (*ListMessageReactionResp, error)
	options       []larkcore.RequestOptionFunc
	curlNum       int
}

func (iterator *ListMessageReactionIterator) Next() (bool, *MessageReaction, error) {
	// 达到最大量，则返回
	if iterator.limit > 0 && iterator.curlNum >= iterator.limit {
		return false, nil, nil
	}

	// 为0则拉取数据
	if iterator.index == 0 || iterator.index >= len(iterator.items) {
		if iterator.index != 0 && iterator.nextPageToken == nil {
			return false, nil, nil
		}
		if iterator.nextPageToken != nil {
			iterator.req.apiReq.QueryParams.Set("page_token", *iterator.nextPageToken)
		}
		resp, err := iterator.listFunc(iterator.ctx, iterator.req, iterator.options...)
		if err != nil {
			return false, nil, err
		}

		if resp.Code != 0 {
			return false, nil, errors.New(fmt.Sprintf("Code:%d,Msg:%s", resp.Code, resp.Msg))
		}

		if len(resp.Data.Items) == 0 {
			return false, nil, nil
		}

		iterator.nextPageToken = resp.Data.PageToken
		iterator.items = resp.Data.Items
		iterator.index = 0
	}

	block := iterator.items[iterator.index]
	iterator.index++
	iterator.curlNum++
	return true, block, nil
}

func (iterator *ListMessageReactionIterator) NextPageToken() *string {
	return iterator.nextPageToken
}

type ListPinIterator struct {
	nextPageToken *string
	items         []*Pin
	index         int
	limit         int
	ctx           context.Context
	req           *ListPinReq
	listFunc      func(ctx context.Context, req *ListPinReq, options ...larkcore.RequestOptionFunc) (*ListPinResp, error)
	options       []larkcore.RequestOptionFunc
	curlNum       int
}

func (iterator *ListPinIterator) Next() (bool, *Pin, error) {
	// 达到最大量，则返回
	if iterator.limit > 0 && iterator.curlNum >= iterator.limit {
		return false, nil, nil
	}

	// 为0则拉取数据
	if iterator.index == 0 || iterator.index >= len(iterator.items) {
		if iterator.index != 0 && iterator.nextPageToken == nil {
			return false, nil, nil
		}
		if iterator.nextPageToken != nil {
			iterator.req.apiReq.QueryParams.Set("page_token", *iterator.nextPageToken)
		}
		resp, err := iterator.listFunc(iterator.ctx, iterator.req, iterator.options...)
		if err != nil {
			return false, nil, err
		}

		if resp.Code != 0 {
			return false, nil, errors.New(fmt.Sprintf("Code:%d,Msg:%s", resp.Code, resp.Msg))
		}

		if len(resp.Data.Items) == 0 {
			return false, nil, nil
		}

		iterator.nextPageToken = resp.Data.PageToken
		iterator.items = resp.Data.Items
		iterator.index = 0
	}

	block := iterator.items[iterator.index]
	iterator.index++
	iterator.curlNum++
	return true, block, nil
}

func (iterator *ListPinIterator) NextPageToken() *string {
	return iterator.nextPageToken
}
