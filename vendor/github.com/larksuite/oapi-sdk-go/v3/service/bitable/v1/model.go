// Package bitable code generated by oapi sdk gen
/*
 * MIT License
 *
 * Copyright (c) 2022 Lark Technologies Pte. Ltd.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice, shall be included in all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */

package larkbitable

import (
	"fmt"

	"context"
	"errors"

	"github.com/larksuite/oapi-sdk-go/v3/core"
)

const (
	MemberIdTypeOpenID           = "open_id"            // 以open_id来识别协作者
	MemberIdTypeUnionID          = "union_id"           // 以union_id来识别协作者
	MemberIdTypeUserID           = "user_id"            // 以user_id来识别协作者
	MemberIdTypeChatID           = "chat_id"            // 以chat_id来识别协作者
	MemberIdTypeDepartmentID     = "department_id"      // 以department_id来识别协作者
	MemberIdTypeOpenDepartmentID = "open_department_id" // 以open_department_id来识别协作者
)

const (
	MemberIdTypeDeleteAppRoleMemberOpenID           = "open_id"            // 以open_id来识别协作者
	MemberIdTypeDeleteAppRoleMemberUnionID          = "union_id"           // 以union_id来识别协作者
	MemberIdTypeDeleteAppRoleMemberUserID           = "user_id"            // 以user_id来识别协作者
	MemberIdTypeDeleteAppRoleMemberChatID           = "chat_id"            // 以chat_id来识别协作者
	MemberIdTypeDeleteAppRoleMemberDepartmentID     = "department_id"      // 以department_id来识别协作者
	MemberIdTypeDeleteAppRoleMemberOpenDepartmentID = "open_department_id" // 以open_department_id来识别协作者
)

const (
	UserIdTypeUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypeUnionId = "union_id" // 以union_id来识别用户
	UserIdTypeOpenId  = "open_id"  // 以open_id来识别用户
)

const (
	UserIdTypeCreateAppTableUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypeCreateAppTableUnionId = "union_id" // 以union_id来识别用户
	UserIdTypeCreateAppTableOpenId  = "open_id"  // 以open_id来识别用户
)

const (
	TypeText         = 1    // 多行文本
	TypeNumber       = 2    // 数字
	TypeSingleSelect = 3    // 单选
	TypeMultiSelect  = 4    // 多选
	TypeDateTime     = 5    // 日期
	TypeCheckbox     = 7    // 复选框
	TypeUser         = 11   // 人员
	TypeUrl          = 15   // 超链接
	TypeAttachment   = 17   // 附件
	TypeLink         = 18   // 单向关联
	TypeFormula      = 20   // 公式
	TypeDuplexLink   = 21   // 双向关联
	TypeCreatedTime  = 1001 // 创建时间
	TypeModifiedTime = 1002 // 最后更新时间
	TypeCreatedUser  = 1003 // 创建人
	TypeModifiedUser = 1004 // 修改人
	TypeAutoSerial   = 1005 // 自动编号
	TypePhoneNumber  = 13   // 电话号码
	TypeLocation     = 22   // 地理位置

)

const (
	TypeUpdateAppTableFieldText         = 1    // 多行文本
	TypeUpdateAppTableFieldNumber       = 2    // 数字
	TypeUpdateAppTableFieldSingleSelect = 3    // 单选
	TypeUpdateAppTableFieldMultiSelect  = 4    // 多选
	TypeUpdateAppTableFieldDateTime     = 5    // 日期
	TypeUpdateAppTableFieldCheckbox     = 7    // 复选框
	TypeUpdateAppTableFieldUser         = 11   // 人员
	TypeUpdateAppTableFieldUrl          = 15   // 超链接
	TypeUpdateAppTableFieldAttachment   = 17   // 附件
	TypeUpdateAppTableFieldLink         = 18   // 单向关联
	TypeUpdateAppTableFieldFormula      = 20   // 公式
	TypeUpdateAppTableFieldDuplexLink   = 21   // 双向关联
	TypeUpdateAppTableFieldCreatedTime  = 1001 // 创建时间
	TypeUpdateAppTableFieldModifiedTime = 1002 // 最后更新时间
	TypeUpdateAppTableFieldCreatedUser  = 1003 // 创建人
	TypeUpdateAppTableFieldModifiedUser = 1004 // 修改人
	TypeUpdateAppTableFieldAutoSerial   = 1005 // 自动编号
	TypeUpdateAppTableFieldPhoneNumber  = 13   // 电话号码
	TypeUpdateAppTableFieldLocation     = 22   // 地理位置

)

const (
	SharedLimitOff            = "off"             // 仅邀请的人可填写
	SharedLimitTenantEditable = "tenant_editable" // 组织内获得链接的人可填写
	SharedLimitAnyoneEditable = "anyone_editable" // 互联网上获得链接的人可填写
)

const (
	UserIdTypeBatchCreateAppTableRecordUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypeBatchCreateAppTableRecordUnionId = "union_id" // 以union_id来识别用户
	UserIdTypeBatchCreateAppTableRecordOpenId  = "open_id"  // 以open_id来识别用户
)

const (
	UserIdTypeBatchUpdateAppTableRecordUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypeBatchUpdateAppTableRecordUnionId = "union_id" // 以union_id来识别用户
	UserIdTypeBatchUpdateAppTableRecordOpenId  = "open_id"  // 以open_id来识别用户
)

const (
	UserIdTypeCreateAppTableRecordUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypeCreateAppTableRecordUnionId = "union_id" // 以union_id来识别用户
	UserIdTypeCreateAppTableRecordOpenId  = "open_id"  // 以open_id来识别用户
)

const (
	UserIdTypeGetAppTableRecordUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypeGetAppTableRecordUnionId = "union_id" // 以union_id来识别用户
	UserIdTypeGetAppTableRecordOpenId  = "open_id"  // 以open_id来识别用户
)

const (
	UserIdTypeListAppTableRecordUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypeListAppTableRecordUnionId = "union_id" // 以union_id来识别用户
	UserIdTypeListAppTableRecordOpenId  = "open_id"  // 以open_id来识别用户
)

const (
	UserIdTypeUpdateAppTableRecordUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypeUpdateAppTableRecordUnionId = "union_id" // 以union_id来识别用户
	UserIdTypeUpdateAppTableRecordOpenId  = "open_id"  // 以open_id来识别用户
)

const (
	ViewTypeGrid    = "grid"    // 表格视图
	ViewTypeKanban  = "kanban"  // 看板视图
	ViewTypeGallery = "gallery" // 画册视图
	ViewTypeGantt   = "gantt"   // 甘特视图
	ViewTypeForm    = "form"    // 表单视图
)

type App struct {
	AppToken    *string `json:"app_token,omitempty"`    // 多维表格 app token
	Name        *string `json:"name,omitempty"`         // 多维表格 App 名字
	Revision    *int    `json:"revision,omitempty"`     // 多维表格 App 版本号
	FolderToken *string `json:"folder_token,omitempty"` // 多维表格 App 归属文件夹
	Url         *string `json:"url,omitempty"`          // 多维表格 App URL
}

type AppBuilder struct {
	appToken        string // 多维表格 app token
	appTokenFlag    bool
	name            string // 多维表格 App 名字
	nameFlag        bool
	revision        int // 多维表格 App 版本号
	revisionFlag    bool
	folderToken     string // 多维表格 App 归属文件夹
	folderTokenFlag bool
	url             string // 多维表格 App URL
	urlFlag         bool
}

func NewAppBuilder() *AppBuilder {
	builder := &AppBuilder{}
	return builder
}

// 多维表格 app token
//
// 示例值：
func (builder *AppBuilder) AppToken(appToken string) *AppBuilder {
	builder.appToken = appToken
	builder.appTokenFlag = true
	return builder
}

// 多维表格 App 名字
//
// 示例值：
func (builder *AppBuilder) Name(name string) *AppBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

// 多维表格 App 版本号
//
// 示例值：
func (builder *AppBuilder) Revision(revision int) *AppBuilder {
	builder.revision = revision
	builder.revisionFlag = true
	return builder
}

// 多维表格 App 归属文件夹
//
// 示例值：
func (builder *AppBuilder) FolderToken(folderToken string) *AppBuilder {
	builder.folderToken = folderToken
	builder.folderTokenFlag = true
	return builder
}

// 多维表格 App URL
//
// 示例值：
func (builder *AppBuilder) Url(url string) *AppBuilder {
	builder.url = url
	builder.urlFlag = true
	return builder
}

func (builder *AppBuilder) Build() *App {
	req := &App{}
	if builder.appTokenFlag {
		req.AppToken = &builder.appToken

	}
	if builder.nameFlag {
		req.Name = &builder.name

	}
	if builder.revisionFlag {
		req.Revision = &builder.revision

	}
	if builder.folderTokenFlag {
		req.FolderToken = &builder.folderToken

	}
	if builder.urlFlag {
		req.Url = &builder.url

	}
	return req
}

type AppDashboard struct {
	BlockId *string `json:"block_id,omitempty"` // 仪表盘 ID
	Name    *string `json:"name,omitempty"`     // 仪表盘名字
}

type AppDashboardBuilder struct {
	blockId     string // 仪表盘 ID
	blockIdFlag bool
	name        string // 仪表盘名字
	nameFlag    bool
}

func NewAppDashboardBuilder() *AppDashboardBuilder {
	builder := &AppDashboardBuilder{}
	return builder
}

// 仪表盘 ID
//
// 示例值：blknkqrP3RqUkcAW
func (builder *AppDashboardBuilder) BlockId(blockId string) *AppDashboardBuilder {
	builder.blockId = blockId
	builder.blockIdFlag = true
	return builder
}

// 仪表盘名字
//
// 示例值：仪表盘1
func (builder *AppDashboardBuilder) Name(name string) *AppDashboardBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

func (builder *AppDashboardBuilder) Build() *AppDashboard {
	req := &AppDashboard{}
	if builder.blockIdFlag {
		req.BlockId = &builder.blockId

	}
	if builder.nameFlag {
		req.Name = &builder.name

	}
	return req
}

type AppFieldPropertyAutoSerial struct {
	Type    *string                              `json:"type,omitempty"`    // 自动编号类型
	Options []*AppFieldPropertyAutoSerialOptions `json:"options,omitempty"` // 自动编号规则列表
}

type AppFieldPropertyAutoSerialBuilder struct {
	type_       string // 自动编号类型
	typeFlag    bool
	options     []*AppFieldPropertyAutoSerialOptions // 自动编号规则列表
	optionsFlag bool
}

func NewAppFieldPropertyAutoSerialBuilder() *AppFieldPropertyAutoSerialBuilder {
	builder := &AppFieldPropertyAutoSerialBuilder{}
	return builder
}

// 自动编号类型
//
// 示例值：auto_increment_number
func (builder *AppFieldPropertyAutoSerialBuilder) Type(type_ string) *AppFieldPropertyAutoSerialBuilder {
	builder.type_ = type_
	builder.typeFlag = true
	return builder
}

// 自动编号规则列表
//
// 示例值：
func (builder *AppFieldPropertyAutoSerialBuilder) Options(options []*AppFieldPropertyAutoSerialOptions) *AppFieldPropertyAutoSerialBuilder {
	builder.options = options
	builder.optionsFlag = true
	return builder
}

func (builder *AppFieldPropertyAutoSerialBuilder) Build() *AppFieldPropertyAutoSerial {
	req := &AppFieldPropertyAutoSerial{}
	if builder.typeFlag {
		req.Type = &builder.type_

	}
	if builder.optionsFlag {
		req.Options = builder.options
	}
	return req
}

type AppFieldPropertyAutoSerialOptions struct {
	Type  *string `json:"type,omitempty"`  // 自动编号的可选规则项类型
	Value *string `json:"value,omitempty"` // 与自动编号的可选规则项类型相对应的取值
}

type AppFieldPropertyAutoSerialOptionsBuilder struct {
	type_     string // 自动编号的可选规则项类型
	typeFlag  bool
	value     string // 与自动编号的可选规则项类型相对应的取值
	valueFlag bool
}

func NewAppFieldPropertyAutoSerialOptionsBuilder() *AppFieldPropertyAutoSerialOptionsBuilder {
	builder := &AppFieldPropertyAutoSerialOptionsBuilder{}
	return builder
}

// 自动编号的可选规则项类型
//
// 示例值：created_time
func (builder *AppFieldPropertyAutoSerialOptionsBuilder) Type(type_ string) *AppFieldPropertyAutoSerialOptionsBuilder {
	builder.type_ = type_
	builder.typeFlag = true
	return builder
}

// 与自动编号的可选规则项类型相对应的取值
//
// 示例值：yyyyMMdd
func (builder *AppFieldPropertyAutoSerialOptionsBuilder) Value(value string) *AppFieldPropertyAutoSerialOptionsBuilder {
	builder.value = value
	builder.valueFlag = true
	return builder
}

func (builder *AppFieldPropertyAutoSerialOptionsBuilder) Build() *AppFieldPropertyAutoSerialOptions {
	req := &AppFieldPropertyAutoSerialOptions{}
	if builder.typeFlag {
		req.Type = &builder.type_

	}
	if builder.valueFlag {
		req.Value = &builder.value

	}
	return req
}

type AppFieldPropertyLocation struct {
	InputType *string `json:"input_type,omitempty"` // 地理位置输入限制
}

type AppFieldPropertyLocationBuilder struct {
	inputType     string // 地理位置输入限制
	inputTypeFlag bool
}

func NewAppFieldPropertyLocationBuilder() *AppFieldPropertyLocationBuilder {
	builder := &AppFieldPropertyLocationBuilder{}
	return builder
}

// 地理位置输入限制
//
// 示例值：not_limit
func (builder *AppFieldPropertyLocationBuilder) InputType(inputType string) *AppFieldPropertyLocationBuilder {
	builder.inputType = inputType
	builder.inputTypeFlag = true
	return builder
}

func (builder *AppFieldPropertyLocationBuilder) Build() *AppFieldPropertyLocation {
	req := &AppFieldPropertyLocation{}
	if builder.inputTypeFlag {
		req.InputType = &builder.inputType

	}
	return req
}

type AppRole struct {
	RoleName   *string             `json:"role_name,omitempty"`   // 自定义角色的名字
	RoleId     *string             `json:"role_id,omitempty"`     // 自定义角色的id
	TableRoles []*AppRoleTableRole `json:"table_roles,omitempty"` // 数据表角色
	BlockRoles []*AppRoleBlockRole `json:"block_roles,omitempty"` // block权限
}

type AppRoleBuilder struct {
	roleName       string // 自定义角色的名字
	roleNameFlag   bool
	roleId         string // 自定义角色的id
	roleIdFlag     bool
	tableRoles     []*AppRoleTableRole // 数据表角色
	tableRolesFlag bool
	blockRoles     []*AppRoleBlockRole // block权限
	blockRolesFlag bool
}

func NewAppRoleBuilder() *AppRoleBuilder {
	builder := &AppRoleBuilder{}
	return builder
}

// 自定义角色的名字
//
// 示例值：自定义角色1
func (builder *AppRoleBuilder) RoleName(roleName string) *AppRoleBuilder {
	builder.roleName = roleName
	builder.roleNameFlag = true
	return builder
}

// 自定义角色的id
//
// 示例值：roljRpwIUt
func (builder *AppRoleBuilder) RoleId(roleId string) *AppRoleBuilder {
	builder.roleId = roleId
	builder.roleIdFlag = true
	return builder
}

// 数据表角色
//
// 示例值：
func (builder *AppRoleBuilder) TableRoles(tableRoles []*AppRoleTableRole) *AppRoleBuilder {
	builder.tableRoles = tableRoles
	builder.tableRolesFlag = true
	return builder
}

// block权限
//
// 示例值：
func (builder *AppRoleBuilder) BlockRoles(blockRoles []*AppRoleBlockRole) *AppRoleBuilder {
	builder.blockRoles = blockRoles
	builder.blockRolesFlag = true
	return builder
}

func (builder *AppRoleBuilder) Build() *AppRole {
	req := &AppRole{}
	if builder.roleNameFlag {
		req.RoleName = &builder.roleName

	}
	if builder.roleIdFlag {
		req.RoleId = &builder.roleId

	}
	if builder.tableRolesFlag {
		req.TableRoles = builder.tableRoles
	}
	if builder.blockRolesFlag {
		req.BlockRoles = builder.blockRoles
	}
	return req
}

type AppRoleBlockRole struct {
	BlockId   *string `json:"block_id,omitempty"`   // Block 的 ID，例如列出仪表盘接口中的仪表盘 block  id
	BlockType *string `json:"block_type,omitempty"` // Block类型
	BlockPerm *int    `json:"block_perm,omitempty"` // Block权限
}

type AppRoleBlockRoleBuilder struct {
	blockId       string // Block 的 ID，例如列出仪表盘接口中的仪表盘 block  id
	blockIdFlag   bool
	blockType     string // Block类型
	blockTypeFlag bool
	blockPerm     int // Block权限
	blockPermFlag bool
}

func NewAppRoleBlockRoleBuilder() *AppRoleBlockRoleBuilder {
	builder := &AppRoleBlockRoleBuilder{}
	return builder
}

// Block 的 ID，例如列出仪表盘接口中的仪表盘 block  id
//
// 示例值：blknkqrP3RqUkcAW
func (builder *AppRoleBlockRoleBuilder) BlockId(blockId string) *AppRoleBlockRoleBuilder {
	builder.blockId = blockId
	builder.blockIdFlag = true
	return builder
}

// Block类型
//
// 示例值：dashboard
func (builder *AppRoleBlockRoleBuilder) BlockType(blockType string) *AppRoleBlockRoleBuilder {
	builder.blockType = blockType
	builder.blockTypeFlag = true
	return builder
}

// Block权限
//
// 示例值：0
func (builder *AppRoleBlockRoleBuilder) BlockPerm(blockPerm int) *AppRoleBlockRoleBuilder {
	builder.blockPerm = blockPerm
	builder.blockPermFlag = true
	return builder
}

func (builder *AppRoleBlockRoleBuilder) Build() *AppRoleBlockRole {
	req := &AppRoleBlockRole{}
	if builder.blockIdFlag {
		req.BlockId = &builder.blockId

	}
	if builder.blockTypeFlag {
		req.BlockType = &builder.blockType

	}
	if builder.blockPermFlag {
		req.BlockPerm = &builder.blockPerm

	}
	return req
}

type AppRoleMember struct {
	MemberId         *string `json:"member_id,omitempty"`          // 协作者id
	OpenId           *string `json:"open_id,omitempty"`            // 用户的 open_id
	UnionId          *string `json:"union_id,omitempty"`           // 用户的 union_id
	UserId           *string `json:"user_id,omitempty"`            // 用户的 user_id
	ChatId           *string `json:"chat_id,omitempty"`            // 群聊的 chat_id
	DepartmentId     *string `json:"department_id,omitempty"`      // 部门的 department_id
	OpenDepartmentId *string `json:"open_department_id,omitempty"` // 部门的 open_department_id
	MemberName       *string `json:"member_name,omitempty"`        // 协作者名字
	MemberEnName     *string `json:"member_en_name,omitempty"`     // 协作者英文名
	MemberType       *string `json:"member_type,omitempty"`        // 协作者类型
}

type AppRoleMemberBuilder struct {
	memberId             string // 协作者id
	memberIdFlag         bool
	openId               string // 用户的 open_id
	openIdFlag           bool
	unionId              string // 用户的 union_id
	unionIdFlag          bool
	userId               string // 用户的 user_id
	userIdFlag           bool
	chatId               string // 群聊的 chat_id
	chatIdFlag           bool
	departmentId         string // 部门的 department_id
	departmentIdFlag     bool
	openDepartmentId     string // 部门的 open_department_id
	openDepartmentIdFlag bool
	memberName           string // 协作者名字
	memberNameFlag       bool
	memberEnName         string // 协作者英文名
	memberEnNameFlag     bool
	memberType           string // 协作者类型
	memberTypeFlag       bool
}

func NewAppRoleMemberBuilder() *AppRoleMemberBuilder {
	builder := &AppRoleMemberBuilder{}
	return builder
}

// 协作者id
//
// 示例值：ou_7dab8a3d3cdcc9da365777c7ad535d62
func (builder *AppRoleMemberBuilder) MemberId(memberId string) *AppRoleMemberBuilder {
	builder.memberId = memberId
	builder.memberIdFlag = true
	return builder
}

// 用户的 open_id
//
// 示例值：ou_xxxxxxxx
func (builder *AppRoleMemberBuilder) OpenId(openId string) *AppRoleMemberBuilder {
	builder.openId = openId
	builder.openIdFlag = true
	return builder
}

// 用户的 union_id
//
// 示例值：on_xxxxxxxx
func (builder *AppRoleMemberBuilder) UnionId(unionId string) *AppRoleMemberBuilder {
	builder.unionId = unionId
	builder.unionIdFlag = true
	return builder
}

// 用户的 user_id
//
// 示例值：xxxxxxxx
func (builder *AppRoleMemberBuilder) UserId(userId string) *AppRoleMemberBuilder {
	builder.userId = userId
	builder.userIdFlag = true
	return builder
}

// 群聊的 chat_id
//
// 示例值：oc_xxxxxxxx
func (builder *AppRoleMemberBuilder) ChatId(chatId string) *AppRoleMemberBuilder {
	builder.chatId = chatId
	builder.chatIdFlag = true
	return builder
}

// 部门的 department_id
//
// 示例值：xxxxxxxx
func (builder *AppRoleMemberBuilder) DepartmentId(departmentId string) *AppRoleMemberBuilder {
	builder.departmentId = departmentId
	builder.departmentIdFlag = true
	return builder
}

// 部门的 open_department_id
//
// 示例值：od-xxxxxxxx
func (builder *AppRoleMemberBuilder) OpenDepartmentId(openDepartmentId string) *AppRoleMemberBuilder {
	builder.openDepartmentId = openDepartmentId
	builder.openDepartmentIdFlag = true
	return builder
}

// 协作者名字
//
// 示例值：张三
func (builder *AppRoleMemberBuilder) MemberName(memberName string) *AppRoleMemberBuilder {
	builder.memberName = memberName
	builder.memberNameFlag = true
	return builder
}

// 协作者英文名
//
// 示例值：San Zhang
func (builder *AppRoleMemberBuilder) MemberEnName(memberEnName string) *AppRoleMemberBuilder {
	builder.memberEnName = memberEnName
	builder.memberEnNameFlag = true
	return builder
}

// 协作者类型
//
// 示例值：user
func (builder *AppRoleMemberBuilder) MemberType(memberType string) *AppRoleMemberBuilder {
	builder.memberType = memberType
	builder.memberTypeFlag = true
	return builder
}

func (builder *AppRoleMemberBuilder) Build() *AppRoleMember {
	req := &AppRoleMember{}
	if builder.memberIdFlag {
		req.MemberId = &builder.memberId

	}
	if builder.openIdFlag {
		req.OpenId = &builder.openId

	}
	if builder.unionIdFlag {
		req.UnionId = &builder.unionId

	}
	if builder.userIdFlag {
		req.UserId = &builder.userId

	}
	if builder.chatIdFlag {
		req.ChatId = &builder.chatId

	}
	if builder.departmentIdFlag {
		req.DepartmentId = &builder.departmentId

	}
	if builder.openDepartmentIdFlag {
		req.OpenDepartmentId = &builder.openDepartmentId

	}
	if builder.memberNameFlag {
		req.MemberName = &builder.memberName

	}
	if builder.memberEnNameFlag {
		req.MemberEnName = &builder.memberEnName

	}
	if builder.memberTypeFlag {
		req.MemberType = &builder.memberType

	}
	return req
}

type AppRoleMemberId struct {
	Type *string `json:"type,omitempty"` // 协作者 ID 类型
	Id   *string `json:"id,omitempty"`   // 协作者 ID
}

type AppRoleMemberIdBuilder struct {
	type_    string // 协作者 ID 类型
	typeFlag bool
	id       string // 协作者 ID
	idFlag   bool
}

func NewAppRoleMemberIdBuilder() *AppRoleMemberIdBuilder {
	builder := &AppRoleMemberIdBuilder{}
	return builder
}

// 协作者 ID 类型
//
// 示例值：open_id
func (builder *AppRoleMemberIdBuilder) Type(type_ string) *AppRoleMemberIdBuilder {
	builder.type_ = type_
	builder.typeFlag = true
	return builder
}

// 协作者 ID
//
// 示例值：ou_35990a9d9052051a2fae9b2f1afabcef
func (builder *AppRoleMemberIdBuilder) Id(id string) *AppRoleMemberIdBuilder {
	builder.id = id
	builder.idFlag = true
	return builder
}

func (builder *AppRoleMemberIdBuilder) Build() *AppRoleMemberId {
	req := &AppRoleMemberId{}
	if builder.typeFlag {
		req.Type = &builder.type_

	}
	if builder.idFlag {
		req.Id = &builder.id

	}
	return req
}

type AppRoleTableRole struct {
	TableName         *string                    `json:"table_name,omitempty"`          // 数据表名
	TableId           *string                    `json:"table_id,omitempty"`            // 数据表ID
	TablePerm         *int                       `json:"table_perm,omitempty"`          // 数据表权限，`协作者可编辑自己的记录`和`可编辑指定字段`是`可编辑记录`的特殊情况，可通过指定`rec_rule`或`field_perm`参数实现相同的效果
	RecRule           *AppRoleTableRoleRecRule   `json:"rec_rule,omitempty"`            // 记录筛选条件，在table_perm为1或2时有意义，用于指定可编辑或可阅读某些记录
	FieldPerm         *AppRoleTableRoleFieldPerm `json:"field_perm,omitempty"`          // 字段权限，仅在table_perm为2时有意义，设置字段可编辑或可阅读。类型为 map，key 是字段名，value 是字段权限;;**value 枚举值有：**;- `1`：可阅读;- `2`：可编辑
	AllowAddRecord    *bool                      `json:"allow_add_record,omitempty"`    // 新增记录权限，仅在table_perm为2时有意义，用于设置记录是否可以新增。
	AllowDeleteRecord *bool                      `json:"allow_delete_record,omitempty"` // 删除记录权限，仅在table_perm为2时有意义，用于设置记录是否可以删除
}

type AppRoleTableRoleBuilder struct {
	tableName             string // 数据表名
	tableNameFlag         bool
	tableId               string // 数据表ID
	tableIdFlag           bool
	tablePerm             int // 数据表权限，`协作者可编辑自己的记录`和`可编辑指定字段`是`可编辑记录`的特殊情况，可通过指定`rec_rule`或`field_perm`参数实现相同的效果
	tablePermFlag         bool
	recRule               *AppRoleTableRoleRecRule // 记录筛选条件，在table_perm为1或2时有意义，用于指定可编辑或可阅读某些记录
	recRuleFlag           bool
	fieldPerm             *AppRoleTableRoleFieldPerm // 字段权限，仅在table_perm为2时有意义，设置字段可编辑或可阅读。类型为 map，key 是字段名，value 是字段权限;;**value 枚举值有：**;- `1`：可阅读;- `2`：可编辑
	fieldPermFlag         bool
	allowAddRecord        bool // 新增记录权限，仅在table_perm为2时有意义，用于设置记录是否可以新增。
	allowAddRecordFlag    bool
	allowDeleteRecord     bool // 删除记录权限，仅在table_perm为2时有意义，用于设置记录是否可以删除
	allowDeleteRecordFlag bool
}

func NewAppRoleTableRoleBuilder() *AppRoleTableRoleBuilder {
	builder := &AppRoleTableRoleBuilder{}
	return builder
}

// 数据表名
//
// 示例值：数据表1
func (builder *AppRoleTableRoleBuilder) TableName(tableName string) *AppRoleTableRoleBuilder {
	builder.tableName = tableName
	builder.tableNameFlag = true
	return builder
}

// 数据表ID
//
// 示例值：tblKz5D60T4JlfcT
func (builder *AppRoleTableRoleBuilder) TableId(tableId string) *AppRoleTableRoleBuilder {
	builder.tableId = tableId
	builder.tableIdFlag = true
	return builder
}

// 数据表权限，`协作者可编辑自己的记录`和`可编辑指定字段`是`可编辑记录`的特殊情况，可通过指定`rec_rule`或`field_perm`参数实现相同的效果
//
// 示例值：0
func (builder *AppRoleTableRoleBuilder) TablePerm(tablePerm int) *AppRoleTableRoleBuilder {
	builder.tablePerm = tablePerm
	builder.tablePermFlag = true
	return builder
}

// 记录筛选条件，在table_perm为1或2时有意义，用于指定可编辑或可阅读某些记录
//
// 示例值：
func (builder *AppRoleTableRoleBuilder) RecRule(recRule *AppRoleTableRoleRecRule) *AppRoleTableRoleBuilder {
	builder.recRule = recRule
	builder.recRuleFlag = true
	return builder
}

// 字段权限，仅在table_perm为2时有意义，设置字段可编辑或可阅读。类型为 map，key 是字段名，value 是字段权限;;**value 枚举值有：**;- `1`：可阅读;- `2`：可编辑
//
// 示例值：{"姓名": 1, "年龄": 2}
func (builder *AppRoleTableRoleBuilder) FieldPerm(fieldPerm *AppRoleTableRoleFieldPerm) *AppRoleTableRoleBuilder {
	builder.fieldPerm = fieldPerm
	builder.fieldPermFlag = true
	return builder
}

// 新增记录权限，仅在table_perm为2时有意义，用于设置记录是否可以新增。
//
// 示例值：true
func (builder *AppRoleTableRoleBuilder) AllowAddRecord(allowAddRecord bool) *AppRoleTableRoleBuilder {
	builder.allowAddRecord = allowAddRecord
	builder.allowAddRecordFlag = true
	return builder
}

// 删除记录权限，仅在table_perm为2时有意义，用于设置记录是否可以删除
//
// 示例值：true
func (builder *AppRoleTableRoleBuilder) AllowDeleteRecord(allowDeleteRecord bool) *AppRoleTableRoleBuilder {
	builder.allowDeleteRecord = allowDeleteRecord
	builder.allowDeleteRecordFlag = true
	return builder
}

func (builder *AppRoleTableRoleBuilder) Build() *AppRoleTableRole {
	req := &AppRoleTableRole{}
	if builder.tableNameFlag {
		req.TableName = &builder.tableName

	}
	if builder.tableIdFlag {
		req.TableId = &builder.tableId

	}
	if builder.tablePermFlag {
		req.TablePerm = &builder.tablePerm

	}
	if builder.recRuleFlag {
		req.RecRule = builder.recRule
	}
	if builder.fieldPermFlag {
		req.FieldPerm = builder.fieldPerm
	}
	if builder.allowAddRecordFlag {
		req.AllowAddRecord = &builder.allowAddRecord

	}
	if builder.allowDeleteRecordFlag {
		req.AllowDeleteRecord = &builder.allowDeleteRecord

	}
	return req
}

type AppRoleTableRoleFieldPerm struct {
}

type AppRoleTableRoleRecRule struct {
	Conditions  []*AppRoleTableRoleRecRuleCondition `json:"conditions,omitempty"`  // 记录筛选条件
	Conjunction *string                             `json:"conjunction,omitempty"` // 多个筛选条件的关系
	OtherPerm   *int                                `json:"other_perm,omitempty"`  // 其他记录权限，仅在table_perm为2时有意义
}

type AppRoleTableRoleRecRuleBuilder struct {
	conditions      []*AppRoleTableRoleRecRuleCondition // 记录筛选条件
	conditionsFlag  bool
	conjunction     string // 多个筛选条件的关系
	conjunctionFlag bool
	otherPerm       int // 其他记录权限，仅在table_perm为2时有意义
	otherPermFlag   bool
}

func NewAppRoleTableRoleRecRuleBuilder() *AppRoleTableRoleRecRuleBuilder {
	builder := &AppRoleTableRoleRecRuleBuilder{}
	return builder
}

// 记录筛选条件
//
// 示例值：
func (builder *AppRoleTableRoleRecRuleBuilder) Conditions(conditions []*AppRoleTableRoleRecRuleCondition) *AppRoleTableRoleRecRuleBuilder {
	builder.conditions = conditions
	builder.conditionsFlag = true
	return builder
}

// 多个筛选条件的关系
//
// 示例值：and
func (builder *AppRoleTableRoleRecRuleBuilder) Conjunction(conjunction string) *AppRoleTableRoleRecRuleBuilder {
	builder.conjunction = conjunction
	builder.conjunctionFlag = true
	return builder
}

// 其他记录权限，仅在table_perm为2时有意义
//
// 示例值：0
func (builder *AppRoleTableRoleRecRuleBuilder) OtherPerm(otherPerm int) *AppRoleTableRoleRecRuleBuilder {
	builder.otherPerm = otherPerm
	builder.otherPermFlag = true
	return builder
}

func (builder *AppRoleTableRoleRecRuleBuilder) Build() *AppRoleTableRoleRecRule {
	req := &AppRoleTableRoleRecRule{}
	if builder.conditionsFlag {
		req.Conditions = builder.conditions
	}
	if builder.conjunctionFlag {
		req.Conjunction = &builder.conjunction

	}
	if builder.otherPermFlag {
		req.OtherPerm = &builder.otherPerm

	}
	return req
}

type AppRoleTableRoleRecRuleCondition struct {
	FieldName *string  `json:"field_name,omitempty"` // 字段名，记录筛选条件是`创建人包含访问者本人`时，此参数值为""
	Operator  *string  `json:"operator,omitempty"`   // 运算符
	Value     []string `json:"value,omitempty"`      // 单选或多选字段的选项id
	FieldType *int     `json:"field_type,omitempty"` // 字段类型
}

type AppRoleTableRoleRecRuleConditionBuilder struct {
	fieldName     string // 字段名，记录筛选条件是`创建人包含访问者本人`时，此参数值为""
	fieldNameFlag bool
	operator      string // 运算符
	operatorFlag  bool
	value         []string // 单选或多选字段的选项id
	valueFlag     bool
	fieldType     int // 字段类型
	fieldTypeFlag bool
}

func NewAppRoleTableRoleRecRuleConditionBuilder() *AppRoleTableRoleRecRuleConditionBuilder {
	builder := &AppRoleTableRoleRecRuleConditionBuilder{}
	return builder
}

// 字段名，记录筛选条件是`创建人包含访问者本人`时，此参数值为""
//
// 示例值：单选
func (builder *AppRoleTableRoleRecRuleConditionBuilder) FieldName(fieldName string) *AppRoleTableRoleRecRuleConditionBuilder {
	builder.fieldName = fieldName
	builder.fieldNameFlag = true
	return builder
}

// 运算符
//
// 示例值：is
func (builder *AppRoleTableRoleRecRuleConditionBuilder) Operator(operator string) *AppRoleTableRoleRecRuleConditionBuilder {
	builder.operator = operator
	builder.operatorFlag = true
	return builder
}

// 单选或多选字段的选项id
//
// 示例值：["optbdVHf4q", "optrpd3eIJ"]
func (builder *AppRoleTableRoleRecRuleConditionBuilder) Value(value []string) *AppRoleTableRoleRecRuleConditionBuilder {
	builder.value = value
	builder.valueFlag = true
	return builder
}

// 字段类型
//
// 示例值：3
func (builder *AppRoleTableRoleRecRuleConditionBuilder) FieldType(fieldType int) *AppRoleTableRoleRecRuleConditionBuilder {
	builder.fieldType = fieldType
	builder.fieldTypeFlag = true
	return builder
}

func (builder *AppRoleTableRoleRecRuleConditionBuilder) Build() *AppRoleTableRoleRecRuleCondition {
	req := &AppRoleTableRoleRecRuleCondition{}
	if builder.fieldNameFlag {
		req.FieldName = &builder.fieldName

	}
	if builder.operatorFlag {
		req.Operator = &builder.operator

	}
	if builder.valueFlag {
		req.Value = builder.value
	}
	if builder.fieldTypeFlag {
		req.FieldType = &builder.fieldType

	}
	return req
}

type AppTable struct {
	TableId  *string `json:"table_id,omitempty"` // 数据表 id
	Revision *int    `json:"revision,omitempty"` // 数据表的版本号
	Name     *string `json:"name,omitempty"`     // 数据表名字
}

type AppTableBuilder struct {
	tableId      string // 数据表 id
	tableIdFlag  bool
	revision     int // 数据表的版本号
	revisionFlag bool
	name         string // 数据表名字
	nameFlag     bool
}

func NewAppTableBuilder() *AppTableBuilder {
	builder := &AppTableBuilder{}
	return builder
}

// 数据表 id
//
// 示例值：
func (builder *AppTableBuilder) TableId(tableId string) *AppTableBuilder {
	builder.tableId = tableId
	builder.tableIdFlag = true
	return builder
}

// 数据表的版本号
//
// 示例值：
func (builder *AppTableBuilder) Revision(revision int) *AppTableBuilder {
	builder.revision = revision
	builder.revisionFlag = true
	return builder
}

// 数据表名字
//
// 示例值：
func (builder *AppTableBuilder) Name(name string) *AppTableBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

func (builder *AppTableBuilder) Build() *AppTable {
	req := &AppTable{}
	if builder.tableIdFlag {
		req.TableId = &builder.tableId

	}
	if builder.revisionFlag {
		req.Revision = &builder.revision

	}
	if builder.nameFlag {
		req.Name = &builder.name

	}
	return req
}

type AppTableField struct {
	FieldId     *string                   `json:"field_id,omitempty"`    // 多维表格字段 id
	FieldName   *string                   `json:"field_name,omitempty"`  // 多维表格字段名
	Type        *int                      `json:"type,omitempty"`        // 多维表格字段类型
	Property    *AppTableFieldProperty    `json:"property,omitempty"`    // 字段属性，具体参考：[字段编辑指南](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/bitable-v1/app-table-field/guide)
	Description *AppTableFieldDescription `json:"description,omitempty"` // 字段的描述
}

type AppTableFieldBuilder struct {
	fieldId         string // 多维表格字段 id
	fieldIdFlag     bool
	fieldName       string // 多维表格字段名
	fieldNameFlag   bool
	type_           int // 多维表格字段类型
	typeFlag        bool
	property        *AppTableFieldProperty // 字段属性，具体参考：[字段编辑指南](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/bitable-v1/app-table-field/guide)
	propertyFlag    bool
	description     *AppTableFieldDescription // 字段的描述
	descriptionFlag bool
}

func NewAppTableFieldBuilder() *AppTableFieldBuilder {
	builder := &AppTableFieldBuilder{}
	return builder
}

// 多维表格字段 id
//
// 示例值：
func (builder *AppTableFieldBuilder) FieldId(fieldId string) *AppTableFieldBuilder {
	builder.fieldId = fieldId
	builder.fieldIdFlag = true
	return builder
}

// 多维表格字段名
//
// 示例值：多行文本
func (builder *AppTableFieldBuilder) FieldName(fieldName string) *AppTableFieldBuilder {
	builder.fieldName = fieldName
	builder.fieldNameFlag = true
	return builder
}

// 多维表格字段类型
//
// 示例值：1
func (builder *AppTableFieldBuilder) Type(type_ int) *AppTableFieldBuilder {
	builder.type_ = type_
	builder.typeFlag = true
	return builder
}

// 字段属性，具体参考：[字段编辑指南](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/bitable-v1/app-table-field/guide)
//
// 示例值：
func (builder *AppTableFieldBuilder) Property(property *AppTableFieldProperty) *AppTableFieldBuilder {
	builder.property = property
	builder.propertyFlag = true
	return builder
}

// 字段的描述
//
// 示例值：
func (builder *AppTableFieldBuilder) Description(description *AppTableFieldDescription) *AppTableFieldBuilder {
	builder.description = description
	builder.descriptionFlag = true
	return builder
}

func (builder *AppTableFieldBuilder) Build() *AppTableField {
	req := &AppTableField{}
	if builder.fieldIdFlag {
		req.FieldId = &builder.fieldId

	}
	if builder.fieldNameFlag {
		req.FieldName = &builder.fieldName

	}
	if builder.typeFlag {
		req.Type = &builder.type_

	}
	if builder.propertyFlag {
		req.Property = builder.property
	}
	if builder.descriptionFlag {
		req.Description = builder.description
	}
	return req
}

type AppTableFieldDescription struct {
	DisableSync *bool   `json:"disable_sync,omitempty"` // 是否禁止同步，如果为true，表示禁止同步该描述内容到表单的问题描述（只在新增、修改字段时生效）
	Text        *string `json:"text,omitempty"`         // 字段描述内容
}

type AppTableFieldDescriptionBuilder struct {
	disableSync     bool // 是否禁止同步，如果为true，表示禁止同步该描述内容到表单的问题描述（只在新增、修改字段时生效）
	disableSyncFlag bool
	text            string // 字段描述内容
	textFlag        bool
}

func NewAppTableFieldDescriptionBuilder() *AppTableFieldDescriptionBuilder {
	builder := &AppTableFieldDescriptionBuilder{}
	return builder
}

// 是否禁止同步，如果为true，表示禁止同步该描述内容到表单的问题描述（只在新增、修改字段时生效）
//
// 示例值：ture
func (builder *AppTableFieldDescriptionBuilder) DisableSync(disableSync bool) *AppTableFieldDescriptionBuilder {
	builder.disableSync = disableSync
	builder.disableSyncFlag = true
	return builder
}

// 字段描述内容
//
// 示例值：这是一个字段描述
func (builder *AppTableFieldDescriptionBuilder) Text(text string) *AppTableFieldDescriptionBuilder {
	builder.text = text
	builder.textFlag = true
	return builder
}

func (builder *AppTableFieldDescriptionBuilder) Build() *AppTableFieldDescription {
	req := &AppTableFieldDescription{}
	if builder.disableSyncFlag {
		req.DisableSync = &builder.disableSync

	}
	if builder.textFlag {
		req.Text = &builder.text

	}
	return req
}

type AppTableFieldProperty struct {
	Options           []*AppTableFieldPropertyOption `json:"options,omitempty"`            // 单选、多选字段的选项信息
	Formatter         *string                        `json:"formatter,omitempty"`          // 数字、公式字段的显示格式
	DateFormatter     *string                        `json:"date_formatter,omitempty"`     // 日期、创建时间、最后更新时间字段的显示格式
	AutoFill          *bool                          `json:"auto_fill,omitempty"`          // 日期字段中新纪录自动填写创建时间
	Multiple          *bool                          `json:"multiple,omitempty"`           // 人员字段中允许添加多个成员，单向关联、双向关联中允许添加多个记录
	TableId           *string                        `json:"table_id,omitempty"`           // 单向关联、双向关联字段中关联的数据表的id
	TableName         *string                        `json:"table_name,omitempty"`         // 单向关联、双向关联字段中关联的数据表的名字
	BackFieldName     *string                        `json:"back_field_name,omitempty"`    // 双向关联字段中关联的数据表中对应的双向关联字段的名字
	AutoSerial        *AppFieldPropertyAutoSerial    `json:"auto_serial,omitempty"`        // 自动编号类型
	Location          *AppFieldPropertyLocation      `json:"location,omitempty"`           // 地理位置输入方式
	FormulaExpression *string                        `json:"formula_expression,omitempty"` // 公式字段的表达式
}

type AppTableFieldPropertyBuilder struct {
	options               []*AppTableFieldPropertyOption // 单选、多选字段的选项信息
	optionsFlag           bool
	formatter             string // 数字、公式字段的显示格式
	formatterFlag         bool
	dateFormatter         string // 日期、创建时间、最后更新时间字段的显示格式
	dateFormatterFlag     bool
	autoFill              bool // 日期字段中新纪录自动填写创建时间
	autoFillFlag          bool
	multiple              bool // 人员字段中允许添加多个成员，单向关联、双向关联中允许添加多个记录
	multipleFlag          bool
	tableId               string // 单向关联、双向关联字段中关联的数据表的id
	tableIdFlag           bool
	tableName             string // 单向关联、双向关联字段中关联的数据表的名字
	tableNameFlag         bool
	backFieldName         string // 双向关联字段中关联的数据表中对应的双向关联字段的名字
	backFieldNameFlag     bool
	autoSerial            *AppFieldPropertyAutoSerial // 自动编号类型
	autoSerialFlag        bool
	location              *AppFieldPropertyLocation // 地理位置输入方式
	locationFlag          bool
	formulaExpression     string // 公式字段的表达式
	formulaExpressionFlag bool
}

func NewAppTableFieldPropertyBuilder() *AppTableFieldPropertyBuilder {
	builder := &AppTableFieldPropertyBuilder{}
	return builder
}

// 单选、多选字段的选项信息
//
// 示例值：
func (builder *AppTableFieldPropertyBuilder) Options(options []*AppTableFieldPropertyOption) *AppTableFieldPropertyBuilder {
	builder.options = options
	builder.optionsFlag = true
	return builder
}

// 数字、公式字段的显示格式
//
// 示例值：0
func (builder *AppTableFieldPropertyBuilder) Formatter(formatter string) *AppTableFieldPropertyBuilder {
	builder.formatter = formatter
	builder.formatterFlag = true
	return builder
}

// 日期、创建时间、最后更新时间字段的显示格式
//
// 示例值：日期格式
func (builder *AppTableFieldPropertyBuilder) DateFormatter(dateFormatter string) *AppTableFieldPropertyBuilder {
	builder.dateFormatter = dateFormatter
	builder.dateFormatterFlag = true
	return builder
}

// 日期字段中新纪录自动填写创建时间
//
// 示例值：false
func (builder *AppTableFieldPropertyBuilder) AutoFill(autoFill bool) *AppTableFieldPropertyBuilder {
	builder.autoFill = autoFill
	builder.autoFillFlag = true
	return builder
}

// 人员字段中允许添加多个成员，单向关联、双向关联中允许添加多个记录
//
// 示例值：false
func (builder *AppTableFieldPropertyBuilder) Multiple(multiple bool) *AppTableFieldPropertyBuilder {
	builder.multiple = multiple
	builder.multipleFlag = true
	return builder
}

// 单向关联、双向关联字段中关联的数据表的id
//
// 示例值：tblsRc9GRRXKqhvW
func (builder *AppTableFieldPropertyBuilder) TableId(tableId string) *AppTableFieldPropertyBuilder {
	builder.tableId = tableId
	builder.tableIdFlag = true
	return builder
}

// 单向关联、双向关联字段中关联的数据表的名字
//
// 示例值："table2"
func (builder *AppTableFieldPropertyBuilder) TableName(tableName string) *AppTableFieldPropertyBuilder {
	builder.tableName = tableName
	builder.tableNameFlag = true
	return builder
}

// 双向关联字段中关联的数据表中对应的双向关联字段的名字
//
// 示例值："table1-双向关联"
func (builder *AppTableFieldPropertyBuilder) BackFieldName(backFieldName string) *AppTableFieldPropertyBuilder {
	builder.backFieldName = backFieldName
	builder.backFieldNameFlag = true
	return builder
}

// 自动编号类型
//
// 示例值：
func (builder *AppTableFieldPropertyBuilder) AutoSerial(autoSerial *AppFieldPropertyAutoSerial) *AppTableFieldPropertyBuilder {
	builder.autoSerial = autoSerial
	builder.autoSerialFlag = true
	return builder
}

// 地理位置输入方式
//
// 示例值：
func (builder *AppTableFieldPropertyBuilder) Location(location *AppFieldPropertyLocation) *AppTableFieldPropertyBuilder {
	builder.location = location
	builder.locationFlag = true
	return builder
}

// 公式字段的表达式
//
// 示例值：bitable::$table[tblNj92WQBAasdEf].$field[fldMV60rYs]*2
func (builder *AppTableFieldPropertyBuilder) FormulaExpression(formulaExpression string) *AppTableFieldPropertyBuilder {
	builder.formulaExpression = formulaExpression
	builder.formulaExpressionFlag = true
	return builder
}

func (builder *AppTableFieldPropertyBuilder) Build() *AppTableFieldProperty {
	req := &AppTableFieldProperty{}
	if builder.optionsFlag {
		req.Options = builder.options
	}
	if builder.formatterFlag {
		req.Formatter = &builder.formatter

	}
	if builder.dateFormatterFlag {
		req.DateFormatter = &builder.dateFormatter

	}
	if builder.autoFillFlag {
		req.AutoFill = &builder.autoFill

	}
	if builder.multipleFlag {
		req.Multiple = &builder.multiple

	}
	if builder.tableIdFlag {
		req.TableId = &builder.tableId

	}
	if builder.tableNameFlag {
		req.TableName = &builder.tableName

	}
	if builder.backFieldNameFlag {
		req.BackFieldName = &builder.backFieldName

	}
	if builder.autoSerialFlag {
		req.AutoSerial = builder.autoSerial
	}
	if builder.locationFlag {
		req.Location = builder.location
	}
	if builder.formulaExpressionFlag {
		req.FormulaExpression = &builder.formulaExpression

	}
	return req
}

type AppTableFieldPropertyOption struct {
	Name  *string `json:"name,omitempty"`  // 选项名
	Id    *string `json:"id,omitempty"`    // 选项 ID，创建时不允许指定 ID
	Color *int    `json:"color,omitempty"` // 选项颜色
}

type AppTableFieldPropertyOptionBuilder struct {
	name      string // 选项名
	nameFlag  bool
	id        string // 选项 ID，创建时不允许指定 ID
	idFlag    bool
	color     int // 选项颜色
	colorFlag bool
}

func NewAppTableFieldPropertyOptionBuilder() *AppTableFieldPropertyOptionBuilder {
	builder := &AppTableFieldPropertyOptionBuilder{}
	return builder
}

// 选项名
//
// 示例值：红色
func (builder *AppTableFieldPropertyOptionBuilder) Name(name string) *AppTableFieldPropertyOptionBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

// 选项 ID，创建时不允许指定 ID
//
// 示例值：optKl35lnG
func (builder *AppTableFieldPropertyOptionBuilder) Id(id string) *AppTableFieldPropertyOptionBuilder {
	builder.id = id
	builder.idFlag = true
	return builder
}

// 选项颜色
//
// 示例值：0
func (builder *AppTableFieldPropertyOptionBuilder) Color(color int) *AppTableFieldPropertyOptionBuilder {
	builder.color = color
	builder.colorFlag = true
	return builder
}

func (builder *AppTableFieldPropertyOptionBuilder) Build() *AppTableFieldPropertyOption {
	req := &AppTableFieldPropertyOption{}
	if builder.nameFlag {
		req.Name = &builder.name

	}
	if builder.idFlag {
		req.Id = &builder.id

	}
	if builder.colorFlag {
		req.Color = &builder.color

	}
	return req
}

type AppTableForm struct {
	Name            *string `json:"name,omitempty"`              // 表单名称
	Description     *string `json:"description,omitempty"`       // 表单描述
	Shared          *bool   `json:"shared,omitempty"`            // 是否开启共享
	SharedUrl       *string `json:"shared_url,omitempty"`        // 分享 URL
	SharedLimit     *string `json:"shared_limit,omitempty"`      // 分享范围限制
	SubmitLimitOnce *bool   `json:"submit_limit_once,omitempty"` // 填写次数限制一次
}

type AppTableFormBuilder struct {
	name                string // 表单名称
	nameFlag            bool
	description         string // 表单描述
	descriptionFlag     bool
	shared              bool // 是否开启共享
	sharedFlag          bool
	sharedUrl           string // 分享 URL
	sharedUrlFlag       bool
	sharedLimit         string // 分享范围限制
	sharedLimitFlag     bool
	submitLimitOnce     bool // 填写次数限制一次
	submitLimitOnceFlag bool
}

func NewAppTableFormBuilder() *AppTableFormBuilder {
	builder := &AppTableFormBuilder{}
	return builder
}

// 表单名称
//
// 示例值：表单
func (builder *AppTableFormBuilder) Name(name string) *AppTableFormBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

// 表单描述
//
// 示例值：表单描述
func (builder *AppTableFormBuilder) Description(description string) *AppTableFormBuilder {
	builder.description = description
	builder.descriptionFlag = true
	return builder
}

// 是否开启共享
//
// 示例值：true
func (builder *AppTableFormBuilder) Shared(shared bool) *AppTableFormBuilder {
	builder.shared = shared
	builder.sharedFlag = true
	return builder
}

// 分享 URL
//
// 示例值：https://bytedance.feishu.cn/share/base/shrcnCy1KAlpahNotmhRn1abcde
func (builder *AppTableFormBuilder) SharedUrl(sharedUrl string) *AppTableFormBuilder {
	builder.sharedUrl = sharedUrl
	builder.sharedUrlFlag = true
	return builder
}

// 分享范围限制
//
// 示例值：tenant_editable
func (builder *AppTableFormBuilder) SharedLimit(sharedLimit string) *AppTableFormBuilder {
	builder.sharedLimit = sharedLimit
	builder.sharedLimitFlag = true
	return builder
}

// 填写次数限制一次
//
// 示例值：true
func (builder *AppTableFormBuilder) SubmitLimitOnce(submitLimitOnce bool) *AppTableFormBuilder {
	builder.submitLimitOnce = submitLimitOnce
	builder.submitLimitOnceFlag = true
	return builder
}

func (builder *AppTableFormBuilder) Build() *AppTableForm {
	req := &AppTableForm{}
	if builder.nameFlag {
		req.Name = &builder.name

	}
	if builder.descriptionFlag {
		req.Description = &builder.description

	}
	if builder.sharedFlag {
		req.Shared = &builder.shared

	}
	if builder.sharedUrlFlag {
		req.SharedUrl = &builder.sharedUrl

	}
	if builder.sharedLimitFlag {
		req.SharedLimit = &builder.sharedLimit

	}
	if builder.submitLimitOnceFlag {
		req.SubmitLimitOnce = &builder.submitLimitOnce

	}
	return req
}

type AppTableFormField struct {
	FieldId     *string `json:"field_id,omitempty"`    // 表单问题 ID
	Title       *string `json:"title,omitempty"`       // 表单问题
	Description *string `json:"description,omitempty"` // 问题描述
	Required    *bool   `json:"required,omitempty"`    // 是否必填
	Visible     *bool   `json:"visible,omitempty"`     // 是否可见
}

type AppTableFormFieldBuilder struct {
	fieldId         string // 表单问题 ID
	fieldIdFlag     bool
	title           string // 表单问题
	titleFlag       bool
	description     string // 问题描述
	descriptionFlag bool
	required        bool // 是否必填
	requiredFlag    bool
	visible         bool // 是否可见
	visibleFlag     bool
}

func NewAppTableFormFieldBuilder() *AppTableFormFieldBuilder {
	builder := &AppTableFormFieldBuilder{}
	return builder
}

// 表单问题 ID
//
// 示例值：fldjX7dUj5
func (builder *AppTableFormFieldBuilder) FieldId(fieldId string) *AppTableFormFieldBuilder {
	builder.fieldId = fieldId
	builder.fieldIdFlag = true
	return builder
}

// 表单问题
//
// 示例值：多行文本
func (builder *AppTableFormFieldBuilder) Title(title string) *AppTableFormFieldBuilder {
	builder.title = title
	builder.titleFlag = true
	return builder
}

// 问题描述
//
// 示例值：多行文本描述
func (builder *AppTableFormFieldBuilder) Description(description string) *AppTableFormFieldBuilder {
	builder.description = description
	builder.descriptionFlag = true
	return builder
}

// 是否必填
//
// 示例值：true
func (builder *AppTableFormFieldBuilder) Required(required bool) *AppTableFormFieldBuilder {
	builder.required = required
	builder.requiredFlag = true
	return builder
}

// 是否可见
//
// 示例值：true
func (builder *AppTableFormFieldBuilder) Visible(visible bool) *AppTableFormFieldBuilder {
	builder.visible = visible
	builder.visibleFlag = true
	return builder
}

func (builder *AppTableFormFieldBuilder) Build() *AppTableFormField {
	req := &AppTableFormField{}
	if builder.fieldIdFlag {
		req.FieldId = &builder.fieldId

	}
	if builder.titleFlag {
		req.Title = &builder.title

	}
	if builder.descriptionFlag {
		req.Description = &builder.description

	}
	if builder.requiredFlag {
		req.Required = &builder.required

	}
	if builder.visibleFlag {
		req.Visible = &builder.visible

	}
	return req
}

type AppTableFormPatchedField struct {
	PreFieldId  *string `json:"pre_field_id,omitempty"` // 上一个表单问题 ID，用于支持调整表单问题的顺序，通过前一个表单问题的 field_id 来确定位置；如果 pre_field_id 为空字符串，则说明要排到首个表单问题
	Title       *string `json:"title,omitempty"`        // 表单问题
	Description *string `json:"description,omitempty"`  // 问题描述
	Required    *bool   `json:"required,omitempty"`     // 是否必填
	Visible     *bool   `json:"visible,omitempty"`      // 是否可见，当值为 false 时，不允许更新其他字段。
}

type AppTableFormPatchedFieldBuilder struct {
	preFieldId      string // 上一个表单问题 ID，用于支持调整表单问题的顺序，通过前一个表单问题的 field_id 来确定位置；如果 pre_field_id 为空字符串，则说明要排到首个表单问题
	preFieldIdFlag  bool
	title           string // 表单问题
	titleFlag       bool
	description     string // 问题描述
	descriptionFlag bool
	required        bool // 是否必填
	requiredFlag    bool
	visible         bool // 是否可见，当值为 false 时，不允许更新其他字段。
	visibleFlag     bool
}

func NewAppTableFormPatchedFieldBuilder() *AppTableFormPatchedFieldBuilder {
	builder := &AppTableFormPatchedFieldBuilder{}
	return builder
}

// 上一个表单问题 ID，用于支持调整表单问题的顺序，通过前一个表单问题的 field_id 来确定位置；如果 pre_field_id 为空字符串，则说明要排到首个表单问题
//
// 示例值：fldjX7dUj5
func (builder *AppTableFormPatchedFieldBuilder) PreFieldId(preFieldId string) *AppTableFormPatchedFieldBuilder {
	builder.preFieldId = preFieldId
	builder.preFieldIdFlag = true
	return builder
}

// 表单问题
//
// 示例值：多行文本
func (builder *AppTableFormPatchedFieldBuilder) Title(title string) *AppTableFormPatchedFieldBuilder {
	builder.title = title
	builder.titleFlag = true
	return builder
}

// 问题描述
//
// 示例值：多行文本描述
func (builder *AppTableFormPatchedFieldBuilder) Description(description string) *AppTableFormPatchedFieldBuilder {
	builder.description = description
	builder.descriptionFlag = true
	return builder
}

// 是否必填
//
// 示例值：true
func (builder *AppTableFormPatchedFieldBuilder) Required(required bool) *AppTableFormPatchedFieldBuilder {
	builder.required = required
	builder.requiredFlag = true
	return builder
}

// 是否可见，当值为 false 时，不允许更新其他字段。
//
// 示例值：true
func (builder *AppTableFormPatchedFieldBuilder) Visible(visible bool) *AppTableFormPatchedFieldBuilder {
	builder.visible = visible
	builder.visibleFlag = true
	return builder
}

func (builder *AppTableFormPatchedFieldBuilder) Build() *AppTableFormPatchedField {
	req := &AppTableFormPatchedField{}
	if builder.preFieldIdFlag {
		req.PreFieldId = &builder.preFieldId

	}
	if builder.titleFlag {
		req.Title = &builder.title

	}
	if builder.descriptionFlag {
		req.Description = &builder.description

	}
	if builder.requiredFlag {
		req.Required = &builder.required

	}
	if builder.visibleFlag {
		req.Visible = &builder.visible

	}
	return req
}

type AppTableRecord struct {
	RecordId         *string                `json:"record_id,omitempty"`          // 一条记录的唯一标识 id [record_id 参数说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/bitable/notification#15d8db94)
	CreatedBy        *Person                `json:"created_by,omitempty"`         // 该记录的创建人
	CreatedTime      *int                   `json:"created_time,omitempty"`       // 该记录的创建时间
	LastModifiedBy   *Person                `json:"last_modified_by,omitempty"`   // 该记录最新一次更新的修改人
	LastModifiedTime *int                   `json:"last_modified_time,omitempty"` // 该记录最近一次的更新时间
	Fields           map[string]interface{} `json:"fields,omitempty"`             // 数据表的字段，即数据表的列;;当前接口支持的字段类型请参考[接入指南](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/bitable/notification#31f78a3c);;不同类型字段的数据结构请参考[数据结构概述](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/bitable/development-guide/bitable-structure)
}

type AppTableRecordBuilder struct {
	recordId             string // 一条记录的唯一标识 id [record_id 参数说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/bitable/notification#15d8db94)
	recordIdFlag         bool
	createdBy            *Person // 该记录的创建人
	createdByFlag        bool
	createdTime          int // 该记录的创建时间
	createdTimeFlag      bool
	lastModifiedBy       *Person // 该记录最新一次更新的修改人
	lastModifiedByFlag   bool
	lastModifiedTime     int // 该记录最近一次的更新时间
	lastModifiedTimeFlag bool
	fields               map[string]interface{} // 数据表的字段，即数据表的列;;当前接口支持的字段类型请参考[接入指南](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/bitable/notification#31f78a3c);;不同类型字段的数据结构请参考[数据结构概述](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/bitable/development-guide/bitable-structure)
	fieldsFlag           bool
}

func NewAppTableRecordBuilder() *AppTableRecordBuilder {
	builder := &AppTableRecordBuilder{}
	return builder
}

// 一条记录的唯一标识 id [record_id 参数说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/bitable/notification#15d8db94)
//
// 示例值：recqwIwhc6
func (builder *AppTableRecordBuilder) RecordId(recordId string) *AppTableRecordBuilder {
	builder.recordId = recordId
	builder.recordIdFlag = true
	return builder
}

// 该记录的创建人
//
// 示例值：
func (builder *AppTableRecordBuilder) CreatedBy(createdBy *Person) *AppTableRecordBuilder {
	builder.createdBy = createdBy
	builder.createdByFlag = true
	return builder
}

// 该记录的创建时间
//
// 示例值：1610281603
func (builder *AppTableRecordBuilder) CreatedTime(createdTime int) *AppTableRecordBuilder {
	builder.createdTime = createdTime
	builder.createdTimeFlag = true
	return builder
}

// 该记录最新一次更新的修改人
//
// 示例值：
func (builder *AppTableRecordBuilder) LastModifiedBy(lastModifiedBy *Person) *AppTableRecordBuilder {
	builder.lastModifiedBy = lastModifiedBy
	builder.lastModifiedByFlag = true
	return builder
}

// 该记录最近一次的更新时间
//
// 示例值：1610281603
func (builder *AppTableRecordBuilder) LastModifiedTime(lastModifiedTime int) *AppTableRecordBuilder {
	builder.lastModifiedTime = lastModifiedTime
	builder.lastModifiedTimeFlag = true
	return builder
}

// 数据表的字段，即数据表的列;;当前接口支持的字段类型请参考[接入指南](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/bitable/notification#31f78a3c);;不同类型字段的数据结构请参考[数据结构概述](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/bitable/development-guide/bitable-structure)
//
// 示例值：
func (builder *AppTableRecordBuilder) Fields(fields map[string]interface{}) *AppTableRecordBuilder {
	builder.fields = fields
	builder.fieldsFlag = true
	return builder
}

func (builder *AppTableRecordBuilder) Build() *AppTableRecord {
	req := &AppTableRecord{}
	if builder.recordIdFlag {
		req.RecordId = &builder.recordId

	}
	if builder.createdByFlag {
		req.CreatedBy = builder.createdBy
	}
	if builder.createdTimeFlag {
		req.CreatedTime = &builder.createdTime

	}
	if builder.lastModifiedByFlag {
		req.LastModifiedBy = builder.lastModifiedBy
	}
	if builder.lastModifiedTimeFlag {
		req.LastModifiedTime = &builder.lastModifiedTime

	}
	if builder.fieldsFlag {
		req.Fields = builder.fields
	}
	return req
}

type AppTableView struct {
	ViewId   *string               `json:"view_id,omitempty"`   // 视图Id
	ViewName *string               `json:"view_name,omitempty"` // 视图名字
	ViewType *string               `json:"view_type,omitempty"` // 视图类型
	Property *AppTableViewProperty `json:"property,omitempty"`  // 视图属性
}

type AppTableViewBuilder struct {
	viewId       string // 视图Id
	viewIdFlag   bool
	viewName     string // 视图名字
	viewNameFlag bool
	viewType     string // 视图类型
	viewTypeFlag bool
	property     *AppTableViewProperty // 视图属性
	propertyFlag bool
}

func NewAppTableViewBuilder() *AppTableViewBuilder {
	builder := &AppTableViewBuilder{}
	return builder
}

// 视图Id
//
// 示例值：vewTpR1urY
func (builder *AppTableViewBuilder) ViewId(viewId string) *AppTableViewBuilder {
	builder.viewId = viewId
	builder.viewIdFlag = true
	return builder
}

// 视图名字
//
// 示例值：甘特视图1
func (builder *AppTableViewBuilder) ViewName(viewName string) *AppTableViewBuilder {
	builder.viewName = viewName
	builder.viewNameFlag = true
	return builder
}

// 视图类型
//
// 示例值：gantt
func (builder *AppTableViewBuilder) ViewType(viewType string) *AppTableViewBuilder {
	builder.viewType = viewType
	builder.viewTypeFlag = true
	return builder
}

// 视图属性
//
// 示例值：
func (builder *AppTableViewBuilder) Property(property *AppTableViewProperty) *AppTableViewBuilder {
	builder.property = property
	builder.propertyFlag = true
	return builder
}

func (builder *AppTableViewBuilder) Build() *AppTableView {
	req := &AppTableView{}
	if builder.viewIdFlag {
		req.ViewId = &builder.viewId

	}
	if builder.viewNameFlag {
		req.ViewName = &builder.viewName

	}
	if builder.viewTypeFlag {
		req.ViewType = &builder.viewType

	}
	if builder.propertyFlag {
		req.Property = builder.property
	}
	return req
}

type AppTableViewProperty struct {
	FilterInfo   *AppTableViewPropertyFilterInfo `json:"filter_info,omitempty"`   // 过滤条件
	HiddenFields []string                        `json:"hidden_fields,omitempty"` // 隐藏字段ID列表
}

type AppTableViewPropertyBuilder struct {
	filterInfo       *AppTableViewPropertyFilterInfo // 过滤条件
	filterInfoFlag   bool
	hiddenFields     []string // 隐藏字段ID列表
	hiddenFieldsFlag bool
}

func NewAppTableViewPropertyBuilder() *AppTableViewPropertyBuilder {
	builder := &AppTableViewPropertyBuilder{}
	return builder
}

// 过滤条件
//
// 示例值：
func (builder *AppTableViewPropertyBuilder) FilterInfo(filterInfo *AppTableViewPropertyFilterInfo) *AppTableViewPropertyBuilder {
	builder.filterInfo = filterInfo
	builder.filterInfoFlag = true
	return builder
}

// 隐藏字段ID列表
//
// 示例值：["fldCGzANXx", "fldCGzANXx"]
func (builder *AppTableViewPropertyBuilder) HiddenFields(hiddenFields []string) *AppTableViewPropertyBuilder {
	builder.hiddenFields = hiddenFields
	builder.hiddenFieldsFlag = true
	return builder
}

func (builder *AppTableViewPropertyBuilder) Build() *AppTableViewProperty {
	req := &AppTableViewProperty{}
	if builder.filterInfoFlag {
		req.FilterInfo = builder.filterInfo
	}
	if builder.hiddenFieldsFlag {
		req.HiddenFields = builder.hiddenFields
	}
	return req
}

type AppTableViewPropertyFilterInfo struct {
	Conjunction      *string                                    `json:"conjunction,omitempty"`       // 多个筛选条件的关系
	Conditions       []*AppTableViewPropertyFilterInfoCondition `json:"conditions,omitempty"`        // 筛选条件
	ConditionOmitted *bool                                      `json:"condition_omitted,omitempty"` // 筛选条件是否缺省
}

type AppTableViewPropertyFilterInfoBuilder struct {
	conjunction          string // 多个筛选条件的关系
	conjunctionFlag      bool
	conditions           []*AppTableViewPropertyFilterInfoCondition // 筛选条件
	conditionsFlag       bool
	conditionOmitted     bool // 筛选条件是否缺省
	conditionOmittedFlag bool
}

func NewAppTableViewPropertyFilterInfoBuilder() *AppTableViewPropertyFilterInfoBuilder {
	builder := &AppTableViewPropertyFilterInfoBuilder{}
	return builder
}

// 多个筛选条件的关系
//
// 示例值：and
func (builder *AppTableViewPropertyFilterInfoBuilder) Conjunction(conjunction string) *AppTableViewPropertyFilterInfoBuilder {
	builder.conjunction = conjunction
	builder.conjunctionFlag = true
	return builder
}

// 筛选条件
//
// 示例值：
func (builder *AppTableViewPropertyFilterInfoBuilder) Conditions(conditions []*AppTableViewPropertyFilterInfoCondition) *AppTableViewPropertyFilterInfoBuilder {
	builder.conditions = conditions
	builder.conditionsFlag = true
	return builder
}

// 筛选条件是否缺省
//
// 示例值：false
func (builder *AppTableViewPropertyFilterInfoBuilder) ConditionOmitted(conditionOmitted bool) *AppTableViewPropertyFilterInfoBuilder {
	builder.conditionOmitted = conditionOmitted
	builder.conditionOmittedFlag = true
	return builder
}

func (builder *AppTableViewPropertyFilterInfoBuilder) Build() *AppTableViewPropertyFilterInfo {
	req := &AppTableViewPropertyFilterInfo{}
	if builder.conjunctionFlag {
		req.Conjunction = &builder.conjunction

	}
	if builder.conditionsFlag {
		req.Conditions = builder.conditions
	}
	if builder.conditionOmittedFlag {
		req.ConditionOmitted = &builder.conditionOmitted

	}
	return req
}

type AppTableViewPropertyFilterInfoCondition struct {
	FieldId     *string `json:"field_id,omitempty"`     // 用于过滤的字段唯一ID
	Operator    *string `json:"operator,omitempty"`     // 过滤操作的类型
	Value       *string `json:"value,omitempty"`        // 筛选值
	ConditionId *string `json:"condition_id,omitempty"` // 过滤条件的唯一ID
	FieldType   *string `json:"field_type,omitempty"`   // 用于过滤的字段类型
}

type AppTableViewPropertyFilterInfoConditionBuilder struct {
	fieldId         string // 用于过滤的字段唯一ID
	fieldIdFlag     bool
	operator        string // 过滤操作的类型
	operatorFlag    bool
	value           string // 筛选值
	valueFlag       bool
	conditionId     string // 过滤条件的唯一ID
	conditionIdFlag bool
	fieldType       string // 用于过滤的字段类型
	fieldTypeFlag   bool
}

func NewAppTableViewPropertyFilterInfoConditionBuilder() *AppTableViewPropertyFilterInfoConditionBuilder {
	builder := &AppTableViewPropertyFilterInfoConditionBuilder{}
	return builder
}

// 用于过滤的字段唯一ID
//
// 示例值：单选
func (builder *AppTableViewPropertyFilterInfoConditionBuilder) FieldId(fieldId string) *AppTableViewPropertyFilterInfoConditionBuilder {
	builder.fieldId = fieldId
	builder.fieldIdFlag = true
	return builder
}

// 过滤操作的类型
//
// 示例值：is
func (builder *AppTableViewPropertyFilterInfoConditionBuilder) Operator(operator string) *AppTableViewPropertyFilterInfoConditionBuilder {
	builder.operator = operator
	builder.operatorFlag = true
	return builder
}

// 筛选值
//
// 示例值：["optbdVHf4q", "optrpd3eIJ"]
func (builder *AppTableViewPropertyFilterInfoConditionBuilder) Value(value string) *AppTableViewPropertyFilterInfoConditionBuilder {
	builder.value = value
	builder.valueFlag = true
	return builder
}

// 过滤条件的唯一ID
//
// 示例值：conNaOEK6O
func (builder *AppTableViewPropertyFilterInfoConditionBuilder) ConditionId(conditionId string) *AppTableViewPropertyFilterInfoConditionBuilder {
	builder.conditionId = conditionId
	builder.conditionIdFlag = true
	return builder
}

// 用于过滤的字段类型
//
// 示例值：3
func (builder *AppTableViewPropertyFilterInfoConditionBuilder) FieldType(fieldType string) *AppTableViewPropertyFilterInfoConditionBuilder {
	builder.fieldType = fieldType
	builder.fieldTypeFlag = true
	return builder
}

func (builder *AppTableViewPropertyFilterInfoConditionBuilder) Build() *AppTableViewPropertyFilterInfoCondition {
	req := &AppTableViewPropertyFilterInfoCondition{}
	if builder.fieldIdFlag {
		req.FieldId = &builder.fieldId

	}
	if builder.operatorFlag {
		req.Operator = &builder.operator

	}
	if builder.valueFlag {
		req.Value = &builder.value

	}
	if builder.conditionIdFlag {
		req.ConditionId = &builder.conditionId

	}
	if builder.fieldTypeFlag {
		req.FieldType = &builder.fieldType

	}
	return req
}

type Attachment struct {
	FileToken *string `json:"file_token,omitempty"` // attachment token
	Name      *string `json:"name,omitempty"`       // attachment name
	Type      *string `json:"type,omitempty"`       // attachment type
	Size      *int    `json:"size,omitempty"`       // attachment size
	Url       *string `json:"url,omitempty"`        // download url
	TmpUrl    *string `json:"tmp_url,omitempty"`    // temporary download url
}

type AttachmentBuilder struct {
	fileToken     string // attachment token
	fileTokenFlag bool
	name          string // attachment name
	nameFlag      bool
	type_         string // attachment type
	typeFlag      bool
	size          int // attachment size
	sizeFlag      bool
	url           string // download url
	urlFlag       bool
	tmpUrl        string // temporary download url
	tmpUrlFlag    bool
}

func NewAttachmentBuilder() *AttachmentBuilder {
	builder := &AttachmentBuilder{}
	return builder
}

// attachment token
//
// 示例值：
func (builder *AttachmentBuilder) FileToken(fileToken string) *AttachmentBuilder {
	builder.fileToken = fileToken
	builder.fileTokenFlag = true
	return builder
}

// attachment name
//
// 示例值：
func (builder *AttachmentBuilder) Name(name string) *AttachmentBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

// attachment type
//
// 示例值：
func (builder *AttachmentBuilder) Type(type_ string) *AttachmentBuilder {
	builder.type_ = type_
	builder.typeFlag = true
	return builder
}

// attachment size
//
// 示例值：
func (builder *AttachmentBuilder) Size(size int) *AttachmentBuilder {
	builder.size = size
	builder.sizeFlag = true
	return builder
}

// download url
//
// 示例值：
func (builder *AttachmentBuilder) Url(url string) *AttachmentBuilder {
	builder.url = url
	builder.urlFlag = true
	return builder
}

// temporary download url
//
// 示例值：
func (builder *AttachmentBuilder) TmpUrl(tmpUrl string) *AttachmentBuilder {
	builder.tmpUrl = tmpUrl
	builder.tmpUrlFlag = true
	return builder
}

func (builder *AttachmentBuilder) Build() *Attachment {
	req := &Attachment{}
	if builder.fileTokenFlag {
		req.FileToken = &builder.fileToken

	}
	if builder.nameFlag {
		req.Name = &builder.name

	}
	if builder.typeFlag {
		req.Type = &builder.type_

	}
	if builder.sizeFlag {
		req.Size = &builder.size

	}
	if builder.urlFlag {
		req.Url = &builder.url

	}
	if builder.tmpUrlFlag {
		req.TmpUrl = &builder.tmpUrl

	}
	return req
}

type DeleteRecord struct {
	Deleted  *bool   `json:"deleted,omitempty"`   // 是否成功删除
	RecordId *string `json:"record_id,omitempty"` // 删除的记录 ID
}

type DeleteRecordBuilder struct {
	deleted      bool // 是否成功删除
	deletedFlag  bool
	recordId     string // 删除的记录 ID
	recordIdFlag bool
}

func NewDeleteRecordBuilder() *DeleteRecordBuilder {
	builder := &DeleteRecordBuilder{}
	return builder
}

// 是否成功删除
//
// 示例值：true
func (builder *DeleteRecordBuilder) Deleted(deleted bool) *DeleteRecordBuilder {
	builder.deleted = deleted
	builder.deletedFlag = true
	return builder
}

// 删除的记录 ID
//
// 示例值：recpCsf4ME
func (builder *DeleteRecordBuilder) RecordId(recordId string) *DeleteRecordBuilder {
	builder.recordId = recordId
	builder.recordIdFlag = true
	return builder
}

func (builder *DeleteRecordBuilder) Build() *DeleteRecord {
	req := &DeleteRecord{}
	if builder.deletedFlag {
		req.Deleted = &builder.deleted

	}
	if builder.recordIdFlag {
		req.RecordId = &builder.recordId

	}
	return req
}

type DisplayApp struct {
	AppToken   *string `json:"app_token,omitempty"`   // 多维表格的 app_token;[app_token 参数说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/bitable/notification#8121eebe)
	Name       *string `json:"name,omitempty"`        // 多维表格的名字
	Revision   *int    `json:"revision,omitempty"`    // 多维表格的版本号（对多维表格进行修改时更新，如新增、删除数据表，修改数据表名等，初始为1，每次更新+1）
	IsAdvanced *bool   `json:"is_advanced,omitempty"` // 多维表格是否开启了高级权限。取值包括：;- true：表示开启了高级权限;- false：表示关闭了高级权限;;[了解更多：使用多维表格高级权限](https://www.feishu.cn/hc/zh-CN/articles/588604550568)
}

type DisplayAppBuilder struct {
	appToken       string // 多维表格的 app_token;[app_token 参数说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/bitable/notification#8121eebe)
	appTokenFlag   bool
	name           string // 多维表格的名字
	nameFlag       bool
	revision       int // 多维表格的版本号（对多维表格进行修改时更新，如新增、删除数据表，修改数据表名等，初始为1，每次更新+1）
	revisionFlag   bool
	isAdvanced     bool // 多维表格是否开启了高级权限。取值包括：;- true：表示开启了高级权限;- false：表示关闭了高级权限;;[了解更多：使用多维表格高级权限](https://www.feishu.cn/hc/zh-CN/articles/588604550568)
	isAdvancedFlag bool
}

func NewDisplayAppBuilder() *DisplayAppBuilder {
	builder := &DisplayAppBuilder{}
	return builder
}

// 多维表格的 app_token;[app_token 参数说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/bitable/notification#8121eebe)
//
// 示例值：
func (builder *DisplayAppBuilder) AppToken(appToken string) *DisplayAppBuilder {
	builder.appToken = appToken
	builder.appTokenFlag = true
	return builder
}

// 多维表格的名字
//
// 示例值：
func (builder *DisplayAppBuilder) Name(name string) *DisplayAppBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

// 多维表格的版本号（对多维表格进行修改时更新，如新增、删除数据表，修改数据表名等，初始为1，每次更新+1）
//
// 示例值：
func (builder *DisplayAppBuilder) Revision(revision int) *DisplayAppBuilder {
	builder.revision = revision
	builder.revisionFlag = true
	return builder
}

// 多维表格是否开启了高级权限。取值包括：;- true：表示开启了高级权限;- false：表示关闭了高级权限;;[了解更多：使用多维表格高级权限](https://www.feishu.cn/hc/zh-CN/articles/588604550568)
//
// 示例值：
func (builder *DisplayAppBuilder) IsAdvanced(isAdvanced bool) *DisplayAppBuilder {
	builder.isAdvanced = isAdvanced
	builder.isAdvancedFlag = true
	return builder
}

func (builder *DisplayAppBuilder) Build() *DisplayApp {
	req := &DisplayApp{}
	if builder.appTokenFlag {
		req.AppToken = &builder.appToken

	}
	if builder.nameFlag {
		req.Name = &builder.name

	}
	if builder.revisionFlag {
		req.Revision = &builder.revision

	}
	if builder.isAdvancedFlag {
		req.IsAdvanced = &builder.isAdvanced

	}
	return req
}

type DisplayAppV2 struct {
	AppToken   *string `json:"app_token,omitempty"`   // 多维表格的 app_token
	Name       *string `json:"name,omitempty"`        // 多维表格的名字
	IsAdvanced *bool   `json:"is_advanced,omitempty"` // 多维表格是否已开启高级权限
}

type DisplayAppV2Builder struct {
	appToken       string // 多维表格的 app_token
	appTokenFlag   bool
	name           string // 多维表格的名字
	nameFlag       bool
	isAdvanced     bool // 多维表格是否已开启高级权限
	isAdvancedFlag bool
}

func NewDisplayAppV2Builder() *DisplayAppV2Builder {
	builder := &DisplayAppV2Builder{}
	return builder
}

// 多维表格的 app_token
//
// 示例值：
func (builder *DisplayAppV2Builder) AppToken(appToken string) *DisplayAppV2Builder {
	builder.appToken = appToken
	builder.appTokenFlag = true
	return builder
}

// 多维表格的名字
//
// 示例值：
func (builder *DisplayAppV2Builder) Name(name string) *DisplayAppV2Builder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

// 多维表格是否已开启高级权限
//
// 示例值：
func (builder *DisplayAppV2Builder) IsAdvanced(isAdvanced bool) *DisplayAppV2Builder {
	builder.isAdvanced = isAdvanced
	builder.isAdvancedFlag = true
	return builder
}

func (builder *DisplayAppV2Builder) Build() *DisplayAppV2 {
	req := &DisplayAppV2{}
	if builder.appTokenFlag {
		req.AppToken = &builder.appToken

	}
	if builder.nameFlag {
		req.Name = &builder.name

	}
	if builder.isAdvancedFlag {
		req.IsAdvanced = &builder.isAdvanced

	}
	return req
}

type Location struct {
	Location    *string `json:"location,omitempty"`     // 经纬度
	Pname       *string `json:"pname,omitempty"`        // 省
	Cityname    *string `json:"cityname,omitempty"`     // 市
	Adname      *string `json:"adname,omitempty"`       // 区
	Address     *string `json:"address,omitempty"`      // 详细地址
	Name        *string `json:"name,omitempty"`         // 地名
	FullAddress *string `json:"full_address,omitempty"` // 完整地址
}

type LocationBuilder struct {
	location        string // 经纬度
	locationFlag    bool
	pname           string // 省
	pnameFlag       bool
	cityname        string // 市
	citynameFlag    bool
	adname          string // 区
	adnameFlag      bool
	address         string // 详细地址
	addressFlag     bool
	name            string // 地名
	nameFlag        bool
	fullAddress     string // 完整地址
	fullAddressFlag bool
}

func NewLocationBuilder() *LocationBuilder {
	builder := &LocationBuilder{}
	return builder
}

// 经纬度
//
// 示例值：113.946927,22.529146
func (builder *LocationBuilder) Location(location string) *LocationBuilder {
	builder.location = location
	builder.locationFlag = true
	return builder
}

// 省
//
// 示例值：广东省
func (builder *LocationBuilder) Pname(pname string) *LocationBuilder {
	builder.pname = pname
	builder.pnameFlag = true
	return builder
}

// 市
//
// 示例值：深圳市
func (builder *LocationBuilder) Cityname(cityname string) *LocationBuilder {
	builder.cityname = cityname
	builder.citynameFlag = true
	return builder
}

// 区
//
// 示例值：南山区
func (builder *LocationBuilder) Adname(adname string) *LocationBuilder {
	builder.adname = adname
	builder.adnameFlag = true
	return builder
}

// 详细地址
//
// 示例值：深圳湾创新科技中心(科苑地铁站C口步行340米)
func (builder *LocationBuilder) Address(address string) *LocationBuilder {
	builder.address = address
	builder.addressFlag = true
	return builder
}

// 地名
//
// 示例值：字节跳动
func (builder *LocationBuilder) Name(name string) *LocationBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

// 完整地址
//
// 示例值：字节跳动，广东省深圳市南山区深圳湾创新科技中心(科苑地铁站C口步行340米)
func (builder *LocationBuilder) FullAddress(fullAddress string) *LocationBuilder {
	builder.fullAddress = fullAddress
	builder.fullAddressFlag = true
	return builder
}

func (builder *LocationBuilder) Build() *Location {
	req := &Location{}
	if builder.locationFlag {
		req.Location = &builder.location

	}
	if builder.pnameFlag {
		req.Pname = &builder.pname

	}
	if builder.citynameFlag {
		req.Cityname = &builder.cityname

	}
	if builder.adnameFlag {
		req.Adname = &builder.adname

	}
	if builder.addressFlag {
		req.Address = &builder.address

	}
	if builder.nameFlag {
		req.Name = &builder.name

	}
	if builder.fullAddressFlag {
		req.FullAddress = &builder.fullAddress

	}
	return req
}

type Person struct {
	Id     *string `json:"id,omitempty"`      // 用户id，id类型等于user_id_type所指定的类型。
	Name   *string `json:"name,omitempty"`    // 用户的中文名称
	EnName *string `json:"en_name,omitempty"` // 用户的英文名称
	Email  *string `json:"email,omitempty"`   // 用户的邮箱
}

type PersonBuilder struct {
	id         string // 用户id，id类型等于user_id_type所指定的类型。
	idFlag     bool
	name       string // 用户的中文名称
	nameFlag   bool
	enName     string // 用户的英文名称
	enNameFlag bool
	email      string // 用户的邮箱
	emailFlag  bool
}

func NewPersonBuilder() *PersonBuilder {
	builder := &PersonBuilder{}
	return builder
}

// 用户id，id类型等于user_id_type所指定的类型。
//
// 示例值：testesttest
func (builder *PersonBuilder) Id(id string) *PersonBuilder {
	builder.id = id
	builder.idFlag = true
	return builder
}

// 用户的中文名称
//
// 示例值：黄泡泡
func (builder *PersonBuilder) Name(name string) *PersonBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

// 用户的英文名称
//
// 示例值：Paopao Huang
func (builder *PersonBuilder) EnName(enName string) *PersonBuilder {
	builder.enName = enName
	builder.enNameFlag = true
	return builder
}

// 用户的邮箱
//
// 示例值：<EMAIL>
func (builder *PersonBuilder) Email(email string) *PersonBuilder {
	builder.email = email
	builder.emailFlag = true
	return builder
}

func (builder *PersonBuilder) Build() *Person {
	req := &Person{}
	if builder.idFlag {
		req.Id = &builder.id

	}
	if builder.nameFlag {
		req.Name = &builder.name

	}
	if builder.enNameFlag {
		req.EnName = &builder.enName

	}
	if builder.emailFlag {
		req.Email = &builder.email

	}
	return req
}

type ReqApp struct {
	Name        *string `json:"name,omitempty"`         // 多维表格App名字
	FolderToken *string `json:"folder_token,omitempty"` // 多维表格App归属文件夹
}

type ReqAppBuilder struct {
	name            string // 多维表格App名字
	nameFlag        bool
	folderToken     string // 多维表格App归属文件夹
	folderTokenFlag bool
}

func NewReqAppBuilder() *ReqAppBuilder {
	builder := &ReqAppBuilder{}
	return builder
}

// 多维表格App名字
//
// 示例值：一篇新的多维表格
func (builder *ReqAppBuilder) Name(name string) *ReqAppBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

// 多维表格App归属文件夹
//
// 示例值：fldbcoh8O99CIMltVc
func (builder *ReqAppBuilder) FolderToken(folderToken string) *ReqAppBuilder {
	builder.folderToken = folderToken
	builder.folderTokenFlag = true
	return builder
}

func (builder *ReqAppBuilder) Build() *ReqApp {
	req := &ReqApp{}
	if builder.nameFlag {
		req.Name = &builder.name

	}
	if builder.folderTokenFlag {
		req.FolderToken = &builder.folderToken

	}
	return req
}

type ReqTable struct {
	Name *string `json:"name,omitempty"` // 数据表名字，必填字段
}

type ReqTableBuilder struct {
	name     string // 数据表名字，必填字段
	nameFlag bool
}

func NewReqTableBuilder() *ReqTableBuilder {
	builder := &ReqTableBuilder{}
	return builder
}

// 数据表名字，必填字段
//
// 示例值：table1
func (builder *ReqTableBuilder) Name(name string) *ReqTableBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

func (builder *ReqTableBuilder) Build() *ReqTable {
	req := &ReqTable{}
	if builder.nameFlag {
		req.Name = &builder.name

	}
	return req
}

type ReqView struct {
	ViewName *string `json:"view_name,omitempty"` // 视图名字
	ViewType *string `json:"view_type,omitempty"` // 视图类型
}

type ReqViewBuilder struct {
	viewName     string // 视图名字
	viewNameFlag bool
	viewType     string // 视图类型
	viewTypeFlag bool
}

func NewReqViewBuilder() *ReqViewBuilder {
	builder := &ReqViewBuilder{}
	return builder
}

// 视图名字
//
// 示例值：表格视图1
func (builder *ReqViewBuilder) ViewName(viewName string) *ReqViewBuilder {
	builder.viewName = viewName
	builder.viewNameFlag = true
	return builder
}

// 视图类型
//
// 示例值：grid
func (builder *ReqViewBuilder) ViewType(viewType string) *ReqViewBuilder {
	builder.viewType = viewType
	builder.viewTypeFlag = true
	return builder
}

func (builder *ReqViewBuilder) Build() *ReqView {
	req := &ReqView{}
	if builder.viewNameFlag {
		req.ViewName = &builder.viewName

	}
	if builder.viewTypeFlag {
		req.ViewType = &builder.viewType

	}
	return req
}

type Url struct {
	Text *string `json:"text,omitempty"` // url text
	Link *string `json:"link,omitempty"` // url link
}

type UrlBuilder struct {
	text     string // url text
	textFlag bool
	link     string // url link
	linkFlag bool
}

func NewUrlBuilder() *UrlBuilder {
	builder := &UrlBuilder{}
	return builder
}

// url text
//
// 示例值：
func (builder *UrlBuilder) Text(text string) *UrlBuilder {
	builder.text = text
	builder.textFlag = true
	return builder
}

// url link
//
// 示例值：
func (builder *UrlBuilder) Link(link string) *UrlBuilder {
	builder.link = link
	builder.linkFlag = true
	return builder
}

func (builder *UrlBuilder) Build() *Url {
	req := &Url{}
	if builder.textFlag {
		req.Text = &builder.text

	}
	if builder.linkFlag {
		req.Link = &builder.link

	}
	return req
}

type GetAppReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewGetAppReqBuilder() *GetAppReqBuilder {
	builder := &GetAppReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 多维表格的唯一标识符 [app_token 参数说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/bitable/notification#8121eebe)
//
// 示例值：appbcbWCzen6D8dezhoCH2RpMAh
func (builder *GetAppReqBuilder) AppToken(appToken string) *GetAppReqBuilder {
	builder.apiReq.PathParams.Set("app_token", fmt.Sprint(appToken))
	return builder
}

func (builder *GetAppReqBuilder) Build() *GetAppReq {
	req := &GetAppReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	return req
}

type GetAppReq struct {
	apiReq *larkcore.ApiReq
}

type GetAppRespData struct {
	App *DisplayApp `json:"app,omitempty"` // 多维表格元数据
}

type GetAppResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *GetAppRespData `json:"data"` // 业务数据
}

func (resp *GetAppResp) Success() bool {
	return resp.Code == 0
}

type UpdateAppReqBodyBuilder struct {
	name           string // 新的多维表格名字
	nameFlag       bool
	isAdvanced     bool // 多维表格是否开启高级权限
	isAdvancedFlag bool
}

func NewUpdateAppReqBodyBuilder() *UpdateAppReqBodyBuilder {
	builder := &UpdateAppReqBodyBuilder{}
	return builder
}

// 新的多维表格名字
//
//示例值：新的多维表格名字
func (builder *UpdateAppReqBodyBuilder) Name(name string) *UpdateAppReqBodyBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

// 多维表格是否开启高级权限
//
//示例值：true
func (builder *UpdateAppReqBodyBuilder) IsAdvanced(isAdvanced bool) *UpdateAppReqBodyBuilder {
	builder.isAdvanced = isAdvanced
	builder.isAdvancedFlag = true
	return builder
}

func (builder *UpdateAppReqBodyBuilder) Build() *UpdateAppReqBody {
	req := &UpdateAppReqBody{}
	if builder.nameFlag {
		req.Name = &builder.name
	}
	if builder.isAdvancedFlag {
		req.IsAdvanced = &builder.isAdvanced
	}
	return req
}

type UpdateAppPathReqBodyBuilder struct {
	name           string // 新的多维表格名字
	nameFlag       bool
	isAdvanced     bool // 多维表格是否开启高级权限
	isAdvancedFlag bool
}

func NewUpdateAppPathReqBodyBuilder() *UpdateAppPathReqBodyBuilder {
	builder := &UpdateAppPathReqBodyBuilder{}
	return builder
}

// 新的多维表格名字
//
// 示例值：新的多维表格名字
func (builder *UpdateAppPathReqBodyBuilder) Name(name string) *UpdateAppPathReqBodyBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

// 多维表格是否开启高级权限
//
// 示例值：true
func (builder *UpdateAppPathReqBodyBuilder) IsAdvanced(isAdvanced bool) *UpdateAppPathReqBodyBuilder {
	builder.isAdvanced = isAdvanced
	builder.isAdvancedFlag = true
	return builder
}

func (builder *UpdateAppPathReqBodyBuilder) Build() (*UpdateAppReqBody, error) {
	req := &UpdateAppReqBody{}
	if builder.nameFlag {
		req.Name = &builder.name
	}
	if builder.isAdvancedFlag {
		req.IsAdvanced = &builder.isAdvanced
	}
	return req, nil
}

type UpdateAppReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *UpdateAppReqBody
}

func NewUpdateAppReqBuilder() *UpdateAppReqBuilder {
	builder := &UpdateAppReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// bitable app token
//
// 示例值：appbcbWCzen6D8dezhoCH2RpMAh
func (builder *UpdateAppReqBuilder) AppToken(appToken string) *UpdateAppReqBuilder {
	builder.apiReq.PathParams.Set("app_token", fmt.Sprint(appToken))
	return builder
}

// 通过 app_token 更新多维表格元数据
func (builder *UpdateAppReqBuilder) Body(body *UpdateAppReqBody) *UpdateAppReqBuilder {
	builder.body = body
	return builder
}

func (builder *UpdateAppReqBuilder) Build() *UpdateAppReq {
	req := &UpdateAppReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.Body = builder.body
	return req
}

type UpdateAppReqBody struct {
	Name       *string `json:"name,omitempty"`        // 新的多维表格名字
	IsAdvanced *bool   `json:"is_advanced,omitempty"` // 多维表格是否开启高级权限
}

type UpdateAppReq struct {
	apiReq *larkcore.ApiReq
	Body   *UpdateAppReqBody `body:""`
}

type UpdateAppRespData struct {
	App *DisplayAppV2 `json:"app,omitempty"` // 多维表格元数据
}

type UpdateAppResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *UpdateAppRespData `json:"data"` // 业务数据
}

func (resp *UpdateAppResp) Success() bool {
	return resp.Code == 0
}

type ListAppDashboardReqBuilder struct {
	apiReq *larkcore.ApiReq
	limit  int // 最大返回多少记录，当使用迭代器访问时才有效
}

func NewListAppDashboardReqBuilder() *ListAppDashboardReqBuilder {
	builder := &ListAppDashboardReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 最大返回多少记录，当使用迭代器访问时才有效
func (builder *ListAppDashboardReqBuilder) Limit(limit int) *ListAppDashboardReqBuilder {
	builder.limit = limit
	return builder
}

// 多维表格文档 Token
//
// 示例值：bascng7vrxcxpig7geggXiCtadY
func (builder *ListAppDashboardReqBuilder) AppToken(appToken string) *ListAppDashboardReqBuilder {
	builder.apiReq.PathParams.Set("app_token", fmt.Sprint(appToken))
	return builder
}

// 分页大小
//
// 示例值：10
func (builder *ListAppDashboardReqBuilder) PageSize(pageSize int) *ListAppDashboardReqBuilder {
	builder.apiReq.QueryParams.Set("page_size", fmt.Sprint(pageSize))
	return builder
}

// 分页标记，第一次请求不填，表示从头开始遍历；分页查询结果还有更多项时会同时返回新的 page_token，下次遍历可采用该 page_token 获取查询结果
//
// 示例值：blknkqrP3RqUkcAW
func (builder *ListAppDashboardReqBuilder) PageToken(pageToken string) *ListAppDashboardReqBuilder {
	builder.apiReq.QueryParams.Set("page_token", fmt.Sprint(pageToken))
	return builder
}

func (builder *ListAppDashboardReqBuilder) Build() *ListAppDashboardReq {
	req := &ListAppDashboardReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.Limit = builder.limit
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type ListAppDashboardReq struct {
	apiReq *larkcore.ApiReq
	Limit  int // 最多返回多少记录，只有在使用迭代器访问时，才有效

}

type ListAppDashboardRespData struct {
	Dashboards []*AppDashboard `json:"dashboards,omitempty"` // 仪表盘信息
	PageToken  *string         `json:"page_token,omitempty"` // 分页标记，当 has_more 为 true 时，会同时返回新的 page_token，否则不返回 page_token
	HasMore    *bool           `json:"has_more,omitempty"`   // 是否还有更多项
}

type ListAppDashboardResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *ListAppDashboardRespData `json:"data"` // 业务数据
}

func (resp *ListAppDashboardResp) Success() bool {
	return resp.Code == 0
}

type CreateAppRoleReqBuilder struct {
	apiReq  *larkcore.ApiReq
	appRole *AppRole
}

func NewCreateAppRoleReqBuilder() *CreateAppRoleReqBuilder {
	builder := &CreateAppRoleReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// bitable app token
//
// 示例值：appbcbWCzen6D8dezhoCH2RpMAh
func (builder *CreateAppRoleReqBuilder) AppToken(appToken string) *CreateAppRoleReqBuilder {
	builder.apiReq.PathParams.Set("app_token", fmt.Sprint(appToken))
	return builder
}

// 新增自定义角色
func (builder *CreateAppRoleReqBuilder) AppRole(appRole *AppRole) *CreateAppRoleReqBuilder {
	builder.appRole = appRole
	return builder
}

func (builder *CreateAppRoleReqBuilder) Build() *CreateAppRoleReq {
	req := &CreateAppRoleReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.Body = builder.appRole
	return req
}

type CreateAppRoleReq struct {
	apiReq  *larkcore.ApiReq
	AppRole *AppRole `body:""`
}

type CreateAppRoleRespData struct {
	Role *AppRole `json:"role,omitempty"` // 自定义权限
}

type CreateAppRoleResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *CreateAppRoleRespData `json:"data"` // 业务数据
}

func (resp *CreateAppRoleResp) Success() bool {
	return resp.Code == 0
}

type DeleteAppRoleReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewDeleteAppRoleReqBuilder() *DeleteAppRoleReqBuilder {
	builder := &DeleteAppRoleReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// bitable app token
//
// 示例值：appbcbWCzen6D8dezhoCH2RpMAh
func (builder *DeleteAppRoleReqBuilder) AppToken(appToken string) *DeleteAppRoleReqBuilder {
	builder.apiReq.PathParams.Set("app_token", fmt.Sprint(appToken))
	return builder
}

// 自定义角色的id
//
// 示例值：roljRpwIUt
func (builder *DeleteAppRoleReqBuilder) RoleId(roleId string) *DeleteAppRoleReqBuilder {
	builder.apiReq.PathParams.Set("role_id", fmt.Sprint(roleId))
	return builder
}

func (builder *DeleteAppRoleReqBuilder) Build() *DeleteAppRoleReq {
	req := &DeleteAppRoleReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	return req
}

type DeleteAppRoleReq struct {
	apiReq *larkcore.ApiReq
}

type DeleteAppRoleResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *DeleteAppRoleResp) Success() bool {
	return resp.Code == 0
}

type ListAppRoleReqBuilder struct {
	apiReq *larkcore.ApiReq
	limit  int // 最大返回多少记录，当使用迭代器访问时才有效
}

func NewListAppRoleReqBuilder() *ListAppRoleReqBuilder {
	builder := &ListAppRoleReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 最大返回多少记录，当使用迭代器访问时才有效
func (builder *ListAppRoleReqBuilder) Limit(limit int) *ListAppRoleReqBuilder {
	builder.limit = limit
	return builder
}

// bitable app token
//
// 示例值：appbcbWCzen6D8dezhoCH2RpMAh
func (builder *ListAppRoleReqBuilder) AppToken(appToken string) *ListAppRoleReqBuilder {
	builder.apiReq.PathParams.Set("app_token", fmt.Sprint(appToken))
	return builder
}

// 分页大小
//
// 示例值：10
func (builder *ListAppRoleReqBuilder) PageSize(pageSize int) *ListAppRoleReqBuilder {
	builder.apiReq.QueryParams.Set("page_size", fmt.Sprint(pageSize))
	return builder
}

// 分页标记，第一次请求不填，表示从头开始遍历；分页查询结果还有更多项时会同时返回新的 page_token，下次遍历可采用该 page_token 获取查询结果
//
// 示例值：roljRpwIUt
func (builder *ListAppRoleReqBuilder) PageToken(pageToken string) *ListAppRoleReqBuilder {
	builder.apiReq.QueryParams.Set("page_token", fmt.Sprint(pageToken))
	return builder
}

func (builder *ListAppRoleReqBuilder) Build() *ListAppRoleReq {
	req := &ListAppRoleReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.Limit = builder.limit
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type ListAppRoleReq struct {
	apiReq *larkcore.ApiReq
	Limit  int // 最多返回多少记录，只有在使用迭代器访问时，才有效

}

type ListAppRoleRespData struct {
	Items     []*AppRole `json:"items,omitempty"`      // 自定义角色列表
	PageToken *string    `json:"page_token,omitempty"` // 下一页分页的token
	HasMore   *bool      `json:"has_more,omitempty"`   // 是否有下一页数据
	Total     *int       `json:"total,omitempty"`      // 总数
}

type ListAppRoleResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *ListAppRoleRespData `json:"data"` // 业务数据
}

func (resp *ListAppRoleResp) Success() bool {
	return resp.Code == 0
}

type UpdateAppRoleReqBuilder struct {
	apiReq  *larkcore.ApiReq
	appRole *AppRole
}

func NewUpdateAppRoleReqBuilder() *UpdateAppRoleReqBuilder {
	builder := &UpdateAppRoleReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// bitable app token
//
// 示例值：appbcbWCzen6D8dezhoCH2RpMAh
func (builder *UpdateAppRoleReqBuilder) AppToken(appToken string) *UpdateAppRoleReqBuilder {
	builder.apiReq.PathParams.Set("app_token", fmt.Sprint(appToken))
	return builder
}

// 自定义角色的id
//
// 示例值：roljRpwIUt
func (builder *UpdateAppRoleReqBuilder) RoleId(roleId string) *UpdateAppRoleReqBuilder {
	builder.apiReq.PathParams.Set("role_id", fmt.Sprint(roleId))
	return builder
}

// 更新自定义角色
func (builder *UpdateAppRoleReqBuilder) AppRole(appRole *AppRole) *UpdateAppRoleReqBuilder {
	builder.appRole = appRole
	return builder
}

func (builder *UpdateAppRoleReqBuilder) Build() *UpdateAppRoleReq {
	req := &UpdateAppRoleReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.Body = builder.appRole
	return req
}

type UpdateAppRoleReq struct {
	apiReq  *larkcore.ApiReq
	AppRole *AppRole `body:""`
}

type UpdateAppRoleRespData struct {
	Role *AppRole `json:"role,omitempty"` // 自定义角色
}

type UpdateAppRoleResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *UpdateAppRoleRespData `json:"data"` // 业务数据
}

func (resp *UpdateAppRoleResp) Success() bool {
	return resp.Code == 0
}

type BatchCreateAppRoleMemberReqBodyBuilder struct {
	memberList     []*AppRoleMemberId // 协作者列表
	memberListFlag bool
}

func NewBatchCreateAppRoleMemberReqBodyBuilder() *BatchCreateAppRoleMemberReqBodyBuilder {
	builder := &BatchCreateAppRoleMemberReqBodyBuilder{}
	return builder
}

// 协作者列表
//
//示例值：
func (builder *BatchCreateAppRoleMemberReqBodyBuilder) MemberList(memberList []*AppRoleMemberId) *BatchCreateAppRoleMemberReqBodyBuilder {
	builder.memberList = memberList
	builder.memberListFlag = true
	return builder
}

func (builder *BatchCreateAppRoleMemberReqBodyBuilder) Build() *BatchCreateAppRoleMemberReqBody {
	req := &BatchCreateAppRoleMemberReqBody{}
	if builder.memberListFlag {
		req.MemberList = builder.memberList
	}
	return req
}

type BatchCreateAppRoleMemberPathReqBodyBuilder struct {
	memberList     []*AppRoleMemberId // 协作者列表
	memberListFlag bool
}

func NewBatchCreateAppRoleMemberPathReqBodyBuilder() *BatchCreateAppRoleMemberPathReqBodyBuilder {
	builder := &BatchCreateAppRoleMemberPathReqBodyBuilder{}
	return builder
}

// 协作者列表
//
// 示例值：
func (builder *BatchCreateAppRoleMemberPathReqBodyBuilder) MemberList(memberList []*AppRoleMemberId) *BatchCreateAppRoleMemberPathReqBodyBuilder {
	builder.memberList = memberList
	builder.memberListFlag = true
	return builder
}

func (builder *BatchCreateAppRoleMemberPathReqBodyBuilder) Build() (*BatchCreateAppRoleMemberReqBody, error) {
	req := &BatchCreateAppRoleMemberReqBody{}
	if builder.memberListFlag {
		req.MemberList = builder.memberList
	}
	return req, nil
}

type BatchCreateAppRoleMemberReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *BatchCreateAppRoleMemberReqBody
}

func NewBatchCreateAppRoleMemberReqBuilder() *BatchCreateAppRoleMemberReqBuilder {
	builder := &BatchCreateAppRoleMemberReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// Bitable 文档 Token
//
// 示例值：bascnnKKvcoUblgmmhZkYqabcef
func (builder *BatchCreateAppRoleMemberReqBuilder) AppToken(appToken string) *BatchCreateAppRoleMemberReqBuilder {
	builder.apiReq.PathParams.Set("app_token", fmt.Sprint(appToken))
	return builder
}

// 自定义角色 ID
//
// 示例值：rolNGhPqks
func (builder *BatchCreateAppRoleMemberReqBuilder) RoleId(roleId string) *BatchCreateAppRoleMemberReqBuilder {
	builder.apiReq.PathParams.Set("role_id", fmt.Sprint(roleId))
	return builder
}

// 批量新增自定义角色的协作者
func (builder *BatchCreateAppRoleMemberReqBuilder) Body(body *BatchCreateAppRoleMemberReqBody) *BatchCreateAppRoleMemberReqBuilder {
	builder.body = body
	return builder
}

func (builder *BatchCreateAppRoleMemberReqBuilder) Build() *BatchCreateAppRoleMemberReq {
	req := &BatchCreateAppRoleMemberReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.Body = builder.body
	return req
}

type BatchCreateAppRoleMemberReqBody struct {
	MemberList []*AppRoleMemberId `json:"member_list,omitempty"` // 协作者列表
}

type BatchCreateAppRoleMemberReq struct {
	apiReq *larkcore.ApiReq
	Body   *BatchCreateAppRoleMemberReqBody `body:""`
}

type BatchCreateAppRoleMemberResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *BatchCreateAppRoleMemberResp) Success() bool {
	return resp.Code == 0
}

type BatchDeleteAppRoleMemberReqBodyBuilder struct {
	memberList     []*AppRoleMemberId // 协作者列表
	memberListFlag bool
}

func NewBatchDeleteAppRoleMemberReqBodyBuilder() *BatchDeleteAppRoleMemberReqBodyBuilder {
	builder := &BatchDeleteAppRoleMemberReqBodyBuilder{}
	return builder
}

// 协作者列表
//
//示例值：
func (builder *BatchDeleteAppRoleMemberReqBodyBuilder) MemberList(memberList []*AppRoleMemberId) *BatchDeleteAppRoleMemberReqBodyBuilder {
	builder.memberList = memberList
	builder.memberListFlag = true
	return builder
}

func (builder *BatchDeleteAppRoleMemberReqBodyBuilder) Build() *BatchDeleteAppRoleMemberReqBody {
	req := &BatchDeleteAppRoleMemberReqBody{}
	if builder.memberListFlag {
		req.MemberList = builder.memberList
	}
	return req
}

type BatchDeleteAppRoleMemberPathReqBodyBuilder struct {
	memberList     []*AppRoleMemberId // 协作者列表
	memberListFlag bool
}

func NewBatchDeleteAppRoleMemberPathReqBodyBuilder() *BatchDeleteAppRoleMemberPathReqBodyBuilder {
	builder := &BatchDeleteAppRoleMemberPathReqBodyBuilder{}
	return builder
}

// 协作者列表
//
// 示例值：
func (builder *BatchDeleteAppRoleMemberPathReqBodyBuilder) MemberList(memberList []*AppRoleMemberId) *BatchDeleteAppRoleMemberPathReqBodyBuilder {
	builder.memberList = memberList
	builder.memberListFlag = true
	return builder
}

func (builder *BatchDeleteAppRoleMemberPathReqBodyBuilder) Build() (*BatchDeleteAppRoleMemberReqBody, error) {
	req := &BatchDeleteAppRoleMemberReqBody{}
	if builder.memberListFlag {
		req.MemberList = builder.memberList
	}
	return req, nil
}

type BatchDeleteAppRoleMemberReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *BatchDeleteAppRoleMemberReqBody
}

func NewBatchDeleteAppRoleMemberReqBuilder() *BatchDeleteAppRoleMemberReqBuilder {
	builder := &BatchDeleteAppRoleMemberReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 多维表格文档 Token
//
// 示例值：bascnnKKvcoUblgmmhZkYqabcef
func (builder *BatchDeleteAppRoleMemberReqBuilder) AppToken(appToken string) *BatchDeleteAppRoleMemberReqBuilder {
	builder.apiReq.PathParams.Set("app_token", fmt.Sprint(appToken))
	return builder
}

// 自定义角色 ID
//
// 示例值：rolNGhPqks
func (builder *BatchDeleteAppRoleMemberReqBuilder) RoleId(roleId string) *BatchDeleteAppRoleMemberReqBuilder {
	builder.apiReq.PathParams.Set("role_id", fmt.Sprint(roleId))
	return builder
}

// 批量删除自定义角色的协作者
func (builder *BatchDeleteAppRoleMemberReqBuilder) Body(body *BatchDeleteAppRoleMemberReqBody) *BatchDeleteAppRoleMemberReqBuilder {
	builder.body = body
	return builder
}

func (builder *BatchDeleteAppRoleMemberReqBuilder) Build() *BatchDeleteAppRoleMemberReq {
	req := &BatchDeleteAppRoleMemberReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.Body = builder.body
	return req
}

type BatchDeleteAppRoleMemberReqBody struct {
	MemberList []*AppRoleMemberId `json:"member_list,omitempty"` // 协作者列表
}

type BatchDeleteAppRoleMemberReq struct {
	apiReq *larkcore.ApiReq
	Body   *BatchDeleteAppRoleMemberReqBody `body:""`
}

type BatchDeleteAppRoleMemberResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *BatchDeleteAppRoleMemberResp) Success() bool {
	return resp.Code == 0
}

type CreateAppRoleMemberReqBuilder struct {
	apiReq        *larkcore.ApiReq
	appRoleMember *AppRoleMember
}

func NewCreateAppRoleMemberReqBuilder() *CreateAppRoleMemberReqBuilder {
	builder := &CreateAppRoleMemberReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// bitable app token
//
// 示例值：appbcbWCzen6D8dezhoCH2RpMAh
func (builder *CreateAppRoleMemberReqBuilder) AppToken(appToken string) *CreateAppRoleMemberReqBuilder {
	builder.apiReq.PathParams.Set("app_token", fmt.Sprint(appToken))
	return builder
}

// 自定义角色的id
//
// 示例值：roljRpwIUt
func (builder *CreateAppRoleMemberReqBuilder) RoleId(roleId string) *CreateAppRoleMemberReqBuilder {
	builder.apiReq.PathParams.Set("role_id", fmt.Sprint(roleId))
	return builder
}

// 协作者id类型，与请求体中的member_id要对应
//
// 示例值：open_id
func (builder *CreateAppRoleMemberReqBuilder) MemberIdType(memberIdType string) *CreateAppRoleMemberReqBuilder {
	builder.apiReq.QueryParams.Set("member_id_type", fmt.Sprint(memberIdType))
	return builder
}

// 新增自定义角色的协作者
func (builder *CreateAppRoleMemberReqBuilder) AppRoleMember(appRoleMember *AppRoleMember) *CreateAppRoleMemberReqBuilder {
	builder.appRoleMember = appRoleMember
	return builder
}

func (builder *CreateAppRoleMemberReqBuilder) Build() *CreateAppRoleMemberReq {
	req := &CreateAppRoleMemberReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.appRoleMember
	return req
}

type CreateAppRoleMemberReq struct {
	apiReq        *larkcore.ApiReq
	AppRoleMember *AppRoleMember `body:""`
}

type CreateAppRoleMemberResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *CreateAppRoleMemberResp) Success() bool {
	return resp.Code == 0
}

type DeleteAppRoleMemberReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewDeleteAppRoleMemberReqBuilder() *DeleteAppRoleMemberReqBuilder {
	builder := &DeleteAppRoleMemberReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// bitable app token
//
// 示例值：appbcbWCzen6D8dezhoCH2RpMAh
func (builder *DeleteAppRoleMemberReqBuilder) AppToken(appToken string) *DeleteAppRoleMemberReqBuilder {
	builder.apiReq.PathParams.Set("app_token", fmt.Sprint(appToken))
	return builder
}

// 自定义角色的id
//
// 示例值：roljRpwIUt
func (builder *DeleteAppRoleMemberReqBuilder) RoleId(roleId string) *DeleteAppRoleMemberReqBuilder {
	builder.apiReq.PathParams.Set("role_id", fmt.Sprint(roleId))
	return builder
}

// 协作者id
//
// 示例值：ou_7dab8a3d3cdcc9da365777c7ad53uew2
func (builder *DeleteAppRoleMemberReqBuilder) MemberId(memberId string) *DeleteAppRoleMemberReqBuilder {
	builder.apiReq.PathParams.Set("member_id", fmt.Sprint(memberId))
	return builder
}

// 协作者id类型，与请求体中的member_id要对应
//
// 示例值：open_id
func (builder *DeleteAppRoleMemberReqBuilder) MemberIdType(memberIdType string) *DeleteAppRoleMemberReqBuilder {
	builder.apiReq.QueryParams.Set("member_id_type", fmt.Sprint(memberIdType))
	return builder
}

func (builder *DeleteAppRoleMemberReqBuilder) Build() *DeleteAppRoleMemberReq {
	req := &DeleteAppRoleMemberReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type DeleteAppRoleMemberReq struct {
	apiReq *larkcore.ApiReq
}

type DeleteAppRoleMemberResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *DeleteAppRoleMemberResp) Success() bool {
	return resp.Code == 0
}

type ListAppRoleMemberReqBuilder struct {
	apiReq *larkcore.ApiReq
	limit  int // 最大返回多少记录，当使用迭代器访问时才有效
}

func NewListAppRoleMemberReqBuilder() *ListAppRoleMemberReqBuilder {
	builder := &ListAppRoleMemberReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 最大返回多少记录，当使用迭代器访问时才有效
func (builder *ListAppRoleMemberReqBuilder) Limit(limit int) *ListAppRoleMemberReqBuilder {
	builder.limit = limit
	return builder
}

// bitable app token
//
// 示例值：appbcbWCzen6D8dezhoCH2RpMAh
func (builder *ListAppRoleMemberReqBuilder) AppToken(appToken string) *ListAppRoleMemberReqBuilder {
	builder.apiReq.PathParams.Set("app_token", fmt.Sprint(appToken))
	return builder
}

// 自定义角色的id
//
// 示例值：roljRpwIUt
func (builder *ListAppRoleMemberReqBuilder) RoleId(roleId string) *ListAppRoleMemberReqBuilder {
	builder.apiReq.PathParams.Set("role_id", fmt.Sprint(roleId))
	return builder
}

// 分页大小
//
// 示例值：100
func (builder *ListAppRoleMemberReqBuilder) PageSize(pageSize int) *ListAppRoleMemberReqBuilder {
	builder.apiReq.QueryParams.Set("page_size", fmt.Sprint(pageSize))
	return builder
}

// 分页标记，第一次请求不填，表示从头开始遍历；分页查询结果还有更多项时会同时返回新的 page_token，下次遍历可采用该 page_token 获取查询结果
//
// 示例值：xxxxx
func (builder *ListAppRoleMemberReqBuilder) PageToken(pageToken string) *ListAppRoleMemberReqBuilder {
	builder.apiReq.QueryParams.Set("page_token", fmt.Sprint(pageToken))
	return builder
}

func (builder *ListAppRoleMemberReqBuilder) Build() *ListAppRoleMemberReq {
	req := &ListAppRoleMemberReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.Limit = builder.limit
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type ListAppRoleMemberReq struct {
	apiReq *larkcore.ApiReq
	Limit  int // 最多返回多少记录，只有在使用迭代器访问时，才有效

}

type ListAppRoleMemberRespData struct {
	Items     []*AppRoleMember `json:"items,omitempty"`      // 协作者列表
	HasMore   *bool            `json:"has_more,omitempty"`   // 是否有下一页数据
	PageToken *string          `json:"page_token,omitempty"` // 下一页分页的token
	Total     *int             `json:"total,omitempty"`      // 总数
}

type ListAppRoleMemberResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *ListAppRoleMemberRespData `json:"data"` // 业务数据
}

func (resp *ListAppRoleMemberResp) Success() bool {
	return resp.Code == 0
}

type BatchCreateAppTableReqBodyBuilder struct {
	tables     []*ReqTable // tables
	tablesFlag bool
}

func NewBatchCreateAppTableReqBodyBuilder() *BatchCreateAppTableReqBodyBuilder {
	builder := &BatchCreateAppTableReqBodyBuilder{}
	return builder
}

// tables
//
//示例值：
func (builder *BatchCreateAppTableReqBodyBuilder) Tables(tables []*ReqTable) *BatchCreateAppTableReqBodyBuilder {
	builder.tables = tables
	builder.tablesFlag = true
	return builder
}

func (builder *BatchCreateAppTableReqBodyBuilder) Build() *BatchCreateAppTableReqBody {
	req := &BatchCreateAppTableReqBody{}
	if builder.tablesFlag {
		req.Tables = builder.tables
	}
	return req
}

type BatchCreateAppTablePathReqBodyBuilder struct {
	tables     []*ReqTable // tables
	tablesFlag bool
}

func NewBatchCreateAppTablePathReqBodyBuilder() *BatchCreateAppTablePathReqBodyBuilder {
	builder := &BatchCreateAppTablePathReqBodyBuilder{}
	return builder
}

// tables
//
// 示例值：
func (builder *BatchCreateAppTablePathReqBodyBuilder) Tables(tables []*ReqTable) *BatchCreateAppTablePathReqBodyBuilder {
	builder.tables = tables
	builder.tablesFlag = true
	return builder
}

func (builder *BatchCreateAppTablePathReqBodyBuilder) Build() (*BatchCreateAppTableReqBody, error) {
	req := &BatchCreateAppTableReqBody{}
	if builder.tablesFlag {
		req.Tables = builder.tables
	}
	return req, nil
}

type BatchCreateAppTableReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *BatchCreateAppTableReqBody
}

func NewBatchCreateAppTableReqBuilder() *BatchCreateAppTableReqBuilder {
	builder := &BatchCreateAppTableReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// bitable app token
//
// 示例值：appbcbWCzen6D8dezhoCH2RpMAh
func (builder *BatchCreateAppTableReqBuilder) AppToken(appToken string) *BatchCreateAppTableReqBuilder {
	builder.apiReq.PathParams.Set("app_token", fmt.Sprint(appToken))
	return builder
}

// 此次调用中使用的用户ID的类型
//
// 示例值：
func (builder *BatchCreateAppTableReqBuilder) UserIdType(userIdType string) *BatchCreateAppTableReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

// 新增多个数据表
func (builder *BatchCreateAppTableReqBuilder) Body(body *BatchCreateAppTableReqBody) *BatchCreateAppTableReqBuilder {
	builder.body = body
	return builder
}

func (builder *BatchCreateAppTableReqBuilder) Build() *BatchCreateAppTableReq {
	req := &BatchCreateAppTableReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.body
	return req
}

type BatchCreateAppTableReqBody struct {
	Tables []*ReqTable `json:"tables,omitempty"` // tables
}

type BatchCreateAppTableReq struct {
	apiReq *larkcore.ApiReq
	Body   *BatchCreateAppTableReqBody `body:""`
}

type BatchCreateAppTableRespData struct {
	TableIds []string `json:"table_ids,omitempty"` // table ids
}

type BatchCreateAppTableResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *BatchCreateAppTableRespData `json:"data"` // 业务数据
}

func (resp *BatchCreateAppTableResp) Success() bool {
	return resp.Code == 0
}

type BatchDeleteAppTableReqBodyBuilder struct {
	tableIds     []string // 删除的多条tableid列表
	tableIdsFlag bool
}

func NewBatchDeleteAppTableReqBodyBuilder() *BatchDeleteAppTableReqBodyBuilder {
	builder := &BatchDeleteAppTableReqBodyBuilder{}
	return builder
}

// 删除的多条tableid列表
//
//示例值：["tblsRc9GRRXKqhvW"]
func (builder *BatchDeleteAppTableReqBodyBuilder) TableIds(tableIds []string) *BatchDeleteAppTableReqBodyBuilder {
	builder.tableIds = tableIds
	builder.tableIdsFlag = true
	return builder
}

func (builder *BatchDeleteAppTableReqBodyBuilder) Build() *BatchDeleteAppTableReqBody {
	req := &BatchDeleteAppTableReqBody{}
	if builder.tableIdsFlag {
		req.TableIds = builder.tableIds
	}
	return req
}

type BatchDeleteAppTablePathReqBodyBuilder struct {
	tableIds     []string // 删除的多条tableid列表
	tableIdsFlag bool
}

func NewBatchDeleteAppTablePathReqBodyBuilder() *BatchDeleteAppTablePathReqBodyBuilder {
	builder := &BatchDeleteAppTablePathReqBodyBuilder{}
	return builder
}

// 删除的多条tableid列表
//
// 示例值：["tblsRc9GRRXKqhvW"]
func (builder *BatchDeleteAppTablePathReqBodyBuilder) TableIds(tableIds []string) *BatchDeleteAppTablePathReqBodyBuilder {
	builder.tableIds = tableIds
	builder.tableIdsFlag = true
	return builder
}

func (builder *BatchDeleteAppTablePathReqBodyBuilder) Build() (*BatchDeleteAppTableReqBody, error) {
	req := &BatchDeleteAppTableReqBody{}
	if builder.tableIdsFlag {
		req.TableIds = builder.tableIds
	}
	return req, nil
}

type BatchDeleteAppTableReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *BatchDeleteAppTableReqBody
}

func NewBatchDeleteAppTableReqBuilder() *BatchDeleteAppTableReqBuilder {
	builder := &BatchDeleteAppTableReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// bitable app token
//
// 示例值：appbcbWCzen6D8dezhoCH2RpMAh
func (builder *BatchDeleteAppTableReqBuilder) AppToken(appToken string) *BatchDeleteAppTableReqBuilder {
	builder.apiReq.PathParams.Set("app_token", fmt.Sprint(appToken))
	return builder
}

// 删除多个数据表
func (builder *BatchDeleteAppTableReqBuilder) Body(body *BatchDeleteAppTableReqBody) *BatchDeleteAppTableReqBuilder {
	builder.body = body
	return builder
}

func (builder *BatchDeleteAppTableReqBuilder) Build() *BatchDeleteAppTableReq {
	req := &BatchDeleteAppTableReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.Body = builder.body
	return req
}

type BatchDeleteAppTableReqBody struct {
	TableIds []string `json:"table_ids,omitempty"` // 删除的多条tableid列表
}

type BatchDeleteAppTableReq struct {
	apiReq *larkcore.ApiReq
	Body   *BatchDeleteAppTableReqBody `body:""`
}

type BatchDeleteAppTableResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *BatchDeleteAppTableResp) Success() bool {
	return resp.Code == 0
}

type CreateAppTableReqBodyBuilder struct {
	table     *ReqTable // 数据表
	tableFlag bool
}

func NewCreateAppTableReqBodyBuilder() *CreateAppTableReqBodyBuilder {
	builder := &CreateAppTableReqBodyBuilder{}
	return builder
}

// 数据表
//
//示例值：
func (builder *CreateAppTableReqBodyBuilder) Table(table *ReqTable) *CreateAppTableReqBodyBuilder {
	builder.table = table
	builder.tableFlag = true
	return builder
}

func (builder *CreateAppTableReqBodyBuilder) Build() *CreateAppTableReqBody {
	req := &CreateAppTableReqBody{}
	if builder.tableFlag {
		req.Table = builder.table
	}
	return req
}

type CreateAppTablePathReqBodyBuilder struct {
	table     *ReqTable // 数据表
	tableFlag bool
}

func NewCreateAppTablePathReqBodyBuilder() *CreateAppTablePathReqBodyBuilder {
	builder := &CreateAppTablePathReqBodyBuilder{}
	return builder
}

// 数据表
//
// 示例值：
func (builder *CreateAppTablePathReqBodyBuilder) Table(table *ReqTable) *CreateAppTablePathReqBodyBuilder {
	builder.table = table
	builder.tableFlag = true
	return builder
}

func (builder *CreateAppTablePathReqBodyBuilder) Build() (*CreateAppTableReqBody, error) {
	req := &CreateAppTableReqBody{}
	if builder.tableFlag {
		req.Table = builder.table
	}
	return req, nil
}

type CreateAppTableReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *CreateAppTableReqBody
}

func NewCreateAppTableReqBuilder() *CreateAppTableReqBuilder {
	builder := &CreateAppTableReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// bitable app token
//
// 示例值：appbcbWCzen6D8dezhoCH2RpMAh
func (builder *CreateAppTableReqBuilder) AppToken(appToken string) *CreateAppTableReqBuilder {
	builder.apiReq.PathParams.Set("app_token", fmt.Sprint(appToken))
	return builder
}

// 此次调用中使用的用户ID的类型
//
// 示例值：
func (builder *CreateAppTableReqBuilder) UserIdType(userIdType string) *CreateAppTableReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

// 新增一个数据表
func (builder *CreateAppTableReqBuilder) Body(body *CreateAppTableReqBody) *CreateAppTableReqBuilder {
	builder.body = body
	return builder
}

func (builder *CreateAppTableReqBuilder) Build() *CreateAppTableReq {
	req := &CreateAppTableReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.body
	return req
}

type CreateAppTableReqBody struct {
	Table *ReqTable `json:"table,omitempty"` // 数据表
}

type CreateAppTableReq struct {
	apiReq *larkcore.ApiReq
	Body   *CreateAppTableReqBody `body:""`
}

type CreateAppTableRespData struct {
	TableId *string `json:"table_id,omitempty"` // table id
}

type CreateAppTableResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *CreateAppTableRespData `json:"data"` // 业务数据
}

func (resp *CreateAppTableResp) Success() bool {
	return resp.Code == 0
}

type DeleteAppTableReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewDeleteAppTableReqBuilder() *DeleteAppTableReqBuilder {
	builder := &DeleteAppTableReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// bitable app token
//
// 示例值：appbcbWCzen6D8dezhoCH2RpMAh
func (builder *DeleteAppTableReqBuilder) AppToken(appToken string) *DeleteAppTableReqBuilder {
	builder.apiReq.PathParams.Set("app_token", fmt.Sprint(appToken))
	return builder
}

// table id
//
// 示例值：tblsRc9GRRXKqhvW
func (builder *DeleteAppTableReqBuilder) TableId(tableId string) *DeleteAppTableReqBuilder {
	builder.apiReq.PathParams.Set("table_id", fmt.Sprint(tableId))
	return builder
}

func (builder *DeleteAppTableReqBuilder) Build() *DeleteAppTableReq {
	req := &DeleteAppTableReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	return req
}

type DeleteAppTableReq struct {
	apiReq *larkcore.ApiReq
}

type DeleteAppTableResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *DeleteAppTableResp) Success() bool {
	return resp.Code == 0
}

type ListAppTableReqBuilder struct {
	apiReq *larkcore.ApiReq
	limit  int // 最大返回多少记录，当使用迭代器访问时才有效
}

func NewListAppTableReqBuilder() *ListAppTableReqBuilder {
	builder := &ListAppTableReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 最大返回多少记录，当使用迭代器访问时才有效
func (builder *ListAppTableReqBuilder) Limit(limit int) *ListAppTableReqBuilder {
	builder.limit = limit
	return builder
}

// bitable app token
//
// 示例值：appbcbWCzen6D8dezhoCH2RpMAh
func (builder *ListAppTableReqBuilder) AppToken(appToken string) *ListAppTableReqBuilder {
	builder.apiReq.PathParams.Set("app_token", fmt.Sprint(appToken))
	return builder
}

//
//
// 示例值：tblsRc9GRRXKqhvW
func (builder *ListAppTableReqBuilder) PageToken(pageToken string) *ListAppTableReqBuilder {
	builder.apiReq.QueryParams.Set("page_token", fmt.Sprint(pageToken))
	return builder
}

//
//
// 示例值：10
func (builder *ListAppTableReqBuilder) PageSize(pageSize int) *ListAppTableReqBuilder {
	builder.apiReq.QueryParams.Set("page_size", fmt.Sprint(pageSize))
	return builder
}

func (builder *ListAppTableReqBuilder) Build() *ListAppTableReq {
	req := &ListAppTableReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.Limit = builder.limit
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type ListAppTableReq struct {
	apiReq *larkcore.ApiReq
	Limit  int // 最多返回多少记录，只有在使用迭代器访问时，才有效

}

type ListAppTableRespData struct {
	HasMore   *bool       `json:"has_more,omitempty"`   // 是否有下一页数据
	PageToken *string     `json:"page_token,omitempty"` // 下一页分页的token
	Total     *int        `json:"total,omitempty"`      // 总数
	Items     []*AppTable `json:"items,omitempty"`      // 数据表信息
}

type ListAppTableResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *ListAppTableRespData `json:"data"` // 业务数据
}

func (resp *ListAppTableResp) Success() bool {
	return resp.Code == 0
}

type CreateAppTableFieldReqBuilder struct {
	apiReq        *larkcore.ApiReq
	appTableField *AppTableField
}

func NewCreateAppTableFieldReqBuilder() *CreateAppTableFieldReqBuilder {
	builder := &CreateAppTableFieldReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// bitable app token
//
// 示例值：appbcbWCzen6D8dezhoCH2RpMAh
func (builder *CreateAppTableFieldReqBuilder) AppToken(appToken string) *CreateAppTableFieldReqBuilder {
	builder.apiReq.PathParams.Set("app_token", fmt.Sprint(appToken))
	return builder
}

// table id
//
// 示例值：tblsRc9GRRXKqhvW
func (builder *CreateAppTableFieldReqBuilder) TableId(tableId string) *CreateAppTableFieldReqBuilder {
	builder.apiReq.PathParams.Set("table_id", fmt.Sprint(tableId))
	return builder
}

// 该接口用于在数据表中新增一个字段
func (builder *CreateAppTableFieldReqBuilder) AppTableField(appTableField *AppTableField) *CreateAppTableFieldReqBuilder {
	builder.appTableField = appTableField
	return builder
}

func (builder *CreateAppTableFieldReqBuilder) Build() *CreateAppTableFieldReq {
	req := &CreateAppTableFieldReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.Body = builder.appTableField
	return req
}

type CreateAppTableFieldReq struct {
	apiReq        *larkcore.ApiReq
	AppTableField *AppTableField `body:""`
}

type CreateAppTableFieldRespData struct {
	Field *AppTableField `json:"field,omitempty"` // 字段
}

type CreateAppTableFieldResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *CreateAppTableFieldRespData `json:"data"` // 业务数据
}

func (resp *CreateAppTableFieldResp) Success() bool {
	return resp.Code == 0
}

type DeleteAppTableFieldReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewDeleteAppTableFieldReqBuilder() *DeleteAppTableFieldReqBuilder {
	builder := &DeleteAppTableFieldReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// bitable app token
//
// 示例值：appbcbWCzen6D8dezhoCH2RpMAh
func (builder *DeleteAppTableFieldReqBuilder) AppToken(appToken string) *DeleteAppTableFieldReqBuilder {
	builder.apiReq.PathParams.Set("app_token", fmt.Sprint(appToken))
	return builder
}

// table id
//
// 示例值：tblsRc9GRRXKqhvW
func (builder *DeleteAppTableFieldReqBuilder) TableId(tableId string) *DeleteAppTableFieldReqBuilder {
	builder.apiReq.PathParams.Set("table_id", fmt.Sprint(tableId))
	return builder
}

// field id
//
// 示例值：fldPTb0U2y
func (builder *DeleteAppTableFieldReqBuilder) FieldId(fieldId string) *DeleteAppTableFieldReqBuilder {
	builder.apiReq.PathParams.Set("field_id", fmt.Sprint(fieldId))
	return builder
}

func (builder *DeleteAppTableFieldReqBuilder) Build() *DeleteAppTableFieldReq {
	req := &DeleteAppTableFieldReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	return req
}

type DeleteAppTableFieldReq struct {
	apiReq *larkcore.ApiReq
}

type DeleteAppTableFieldRespData struct {
	FieldId *string `json:"field_id,omitempty"` // field id
	Deleted *bool   `json:"deleted,omitempty"`  // 删除标记
}

type DeleteAppTableFieldResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *DeleteAppTableFieldRespData `json:"data"` // 业务数据
}

func (resp *DeleteAppTableFieldResp) Success() bool {
	return resp.Code == 0
}

type ListAppTableFieldReqBuilder struct {
	apiReq *larkcore.ApiReq
	limit  int // 最大返回多少记录，当使用迭代器访问时才有效
}

func NewListAppTableFieldReqBuilder() *ListAppTableFieldReqBuilder {
	builder := &ListAppTableFieldReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 最大返回多少记录，当使用迭代器访问时才有效
func (builder *ListAppTableFieldReqBuilder) Limit(limit int) *ListAppTableFieldReqBuilder {
	builder.limit = limit
	return builder
}

// bitable app token
//
// 示例值：appbcbWCzen6D8dezhoCH2RpMAh
func (builder *ListAppTableFieldReqBuilder) AppToken(appToken string) *ListAppTableFieldReqBuilder {
	builder.apiReq.PathParams.Set("app_token", fmt.Sprint(appToken))
	return builder
}

// table id
//
// 示例值：tblsRc9GRRXKqhvW
func (builder *ListAppTableFieldReqBuilder) TableId(tableId string) *ListAppTableFieldReqBuilder {
	builder.apiReq.PathParams.Set("table_id", fmt.Sprint(tableId))
	return builder
}

// 视图 ID
//
// 示例值：vewOVMEXPF
func (builder *ListAppTableFieldReqBuilder) ViewId(viewId string) *ListAppTableFieldReqBuilder {
	builder.apiReq.QueryParams.Set("view_id", fmt.Sprint(viewId))
	return builder
}

// 控制字段描述（多行文本格式）数据的返回格式, true 表示以数组富文本形式返回
//
// 示例值：true
func (builder *ListAppTableFieldReqBuilder) TextFieldAsArray(textFieldAsArray bool) *ListAppTableFieldReqBuilder {
	builder.apiReq.QueryParams.Set("text_field_as_array", fmt.Sprint(textFieldAsArray))
	return builder
}

//
//
// 示例值：fldwJ4YrtB
func (builder *ListAppTableFieldReqBuilder) PageToken(pageToken string) *ListAppTableFieldReqBuilder {
	builder.apiReq.QueryParams.Set("page_token", fmt.Sprint(pageToken))
	return builder
}

//
//
// 示例值：10
func (builder *ListAppTableFieldReqBuilder) PageSize(pageSize int) *ListAppTableFieldReqBuilder {
	builder.apiReq.QueryParams.Set("page_size", fmt.Sprint(pageSize))
	return builder
}

func (builder *ListAppTableFieldReqBuilder) Build() *ListAppTableFieldReq {
	req := &ListAppTableFieldReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.Limit = builder.limit
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type ListAppTableFieldReq struct {
	apiReq *larkcore.ApiReq
	Limit  int // 最多返回多少记录，只有在使用迭代器访问时，才有效

}

type ListAppTableFieldRespData struct {
	HasMore   *bool            `json:"has_more,omitempty"`   // 是否有下一页数据
	PageToken *string          `json:"page_token,omitempty"` // 下一页分页的token
	Total     *int             `json:"total,omitempty"`      // 总数
	Items     []*AppTableField `json:"items,omitempty"`      // 字段信息
}

type ListAppTableFieldResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *ListAppTableFieldRespData `json:"data"` // 业务数据
}

func (resp *ListAppTableFieldResp) Success() bool {
	return resp.Code == 0
}

type UpdateAppTableFieldReqBuilder struct {
	apiReq        *larkcore.ApiReq
	appTableField *AppTableField
}

func NewUpdateAppTableFieldReqBuilder() *UpdateAppTableFieldReqBuilder {
	builder := &UpdateAppTableFieldReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// bitable app token
//
// 示例值：appbcbWCzen6D8dezhoCH2RpMAh
func (builder *UpdateAppTableFieldReqBuilder) AppToken(appToken string) *UpdateAppTableFieldReqBuilder {
	builder.apiReq.PathParams.Set("app_token", fmt.Sprint(appToken))
	return builder
}

// table id
//
// 示例值：tblsRc9GRRXKqhvW
func (builder *UpdateAppTableFieldReqBuilder) TableId(tableId string) *UpdateAppTableFieldReqBuilder {
	builder.apiReq.PathParams.Set("table_id", fmt.Sprint(tableId))
	return builder
}

// field id
//
// 示例值：fldPTb0U2y
func (builder *UpdateAppTableFieldReqBuilder) FieldId(fieldId string) *UpdateAppTableFieldReqBuilder {
	builder.apiReq.PathParams.Set("field_id", fmt.Sprint(fieldId))
	return builder
}

// 该接口用于在数据表中更新一个字段
func (builder *UpdateAppTableFieldReqBuilder) AppTableField(appTableField *AppTableField) *UpdateAppTableFieldReqBuilder {
	builder.appTableField = appTableField
	return builder
}

func (builder *UpdateAppTableFieldReqBuilder) Build() *UpdateAppTableFieldReq {
	req := &UpdateAppTableFieldReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.Body = builder.appTableField
	return req
}

type UpdateAppTableFieldReq struct {
	apiReq        *larkcore.ApiReq
	AppTableField *AppTableField `body:""`
}

type UpdateAppTableFieldRespData struct {
	Field *AppTableField `json:"field,omitempty"` // 字段
}

type UpdateAppTableFieldResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *UpdateAppTableFieldRespData `json:"data"` // 业务数据
}

func (resp *UpdateAppTableFieldResp) Success() bool {
	return resp.Code == 0
}

type GetAppTableFormReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewGetAppTableFormReqBuilder() *GetAppTableFormReqBuilder {
	builder := &GetAppTableFormReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 多维表格文档 Token
//
// 示例值：bascnv1jIEppJdTCn3jOosabcef
func (builder *GetAppTableFormReqBuilder) AppToken(appToken string) *GetAppTableFormReqBuilder {
	builder.apiReq.PathParams.Set("app_token", fmt.Sprint(appToken))
	return builder
}

// 表格 ID
//
// 示例值：tblz8nadEUdxNMt5
func (builder *GetAppTableFormReqBuilder) TableId(tableId string) *GetAppTableFormReqBuilder {
	builder.apiReq.PathParams.Set("table_id", fmt.Sprint(tableId))
	return builder
}

// 表单 ID
//
// 示例值：vew6oMbAa4
func (builder *GetAppTableFormReqBuilder) FormId(formId string) *GetAppTableFormReqBuilder {
	builder.apiReq.PathParams.Set("form_id", fmt.Sprint(formId))
	return builder
}

func (builder *GetAppTableFormReqBuilder) Build() *GetAppTableFormReq {
	req := &GetAppTableFormReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	return req
}

type GetAppTableFormReq struct {
	apiReq *larkcore.ApiReq
}

type GetAppTableFormRespData struct {
	Form *AppTableForm `json:"form,omitempty"` // 表单元数据信息
}

type GetAppTableFormResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *GetAppTableFormRespData `json:"data"` // 业务数据
}

func (resp *GetAppTableFormResp) Success() bool {
	return resp.Code == 0
}

type PatchAppTableFormReqBuilder struct {
	apiReq       *larkcore.ApiReq
	appTableForm *AppTableForm
}

func NewPatchAppTableFormReqBuilder() *PatchAppTableFormReqBuilder {
	builder := &PatchAppTableFormReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 多维表格文档 Token
//
// 示例值：bascnv1jIEppJdTCn3jOosabcef
func (builder *PatchAppTableFormReqBuilder) AppToken(appToken string) *PatchAppTableFormReqBuilder {
	builder.apiReq.PathParams.Set("app_token", fmt.Sprint(appToken))
	return builder
}

// 表格 ID
//
// 示例值：tblz8nadEUdxNMt5
func (builder *PatchAppTableFormReqBuilder) TableId(tableId string) *PatchAppTableFormReqBuilder {
	builder.apiReq.PathParams.Set("table_id", fmt.Sprint(tableId))
	return builder
}

// 表单 ID
//
// 示例值：vew6oMbAa4
func (builder *PatchAppTableFormReqBuilder) FormId(formId string) *PatchAppTableFormReqBuilder {
	builder.apiReq.PathParams.Set("form_id", fmt.Sprint(formId))
	return builder
}

// 该接口用于更新表单中的元数据项
func (builder *PatchAppTableFormReqBuilder) AppTableForm(appTableForm *AppTableForm) *PatchAppTableFormReqBuilder {
	builder.appTableForm = appTableForm
	return builder
}

func (builder *PatchAppTableFormReqBuilder) Build() *PatchAppTableFormReq {
	req := &PatchAppTableFormReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.Body = builder.appTableForm
	return req
}

type PatchAppTableFormReq struct {
	apiReq       *larkcore.ApiReq
	AppTableForm *AppTableForm `body:""`
}

type PatchAppTableFormRespData struct {
	Form *AppTableForm `json:"form,omitempty"` // 表单元数据信息
}

type PatchAppTableFormResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *PatchAppTableFormRespData `json:"data"` // 业务数据
}

func (resp *PatchAppTableFormResp) Success() bool {
	return resp.Code == 0
}

type ListAppTableFormFieldReqBuilder struct {
	apiReq *larkcore.ApiReq
	limit  int // 最大返回多少记录，当使用迭代器访问时才有效
}

func NewListAppTableFormFieldReqBuilder() *ListAppTableFormFieldReqBuilder {
	builder := &ListAppTableFormFieldReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 最大返回多少记录，当使用迭代器访问时才有效
func (builder *ListAppTableFormFieldReqBuilder) Limit(limit int) *ListAppTableFormFieldReqBuilder {
	builder.limit = limit
	return builder
}

// 多维表格文档 Token
//
// 示例值：bascnCMII2ORej2RItqpZZUNMIe
func (builder *ListAppTableFormFieldReqBuilder) AppToken(appToken string) *ListAppTableFormFieldReqBuilder {
	builder.apiReq.PathParams.Set("app_token", fmt.Sprint(appToken))
	return builder
}

// 表格 ID
//
// 示例值：tblxI2tWaxP5dG7p
func (builder *ListAppTableFormFieldReqBuilder) TableId(tableId string) *ListAppTableFormFieldReqBuilder {
	builder.apiReq.PathParams.Set("table_id", fmt.Sprint(tableId))
	return builder
}

// 表单 ID
//
// 示例值：vewTpR1urY
func (builder *ListAppTableFormFieldReqBuilder) FormId(formId string) *ListAppTableFormFieldReqBuilder {
	builder.apiReq.PathParams.Set("form_id", fmt.Sprint(formId))
	return builder
}

// 分页大小
//
// 示例值：10
func (builder *ListAppTableFormFieldReqBuilder) PageSize(pageSize int) *ListAppTableFormFieldReqBuilder {
	builder.apiReq.QueryParams.Set("page_size", fmt.Sprint(pageSize))
	return builder
}

// 分页标记，第一次请求不填，表示从头开始遍历；分页查询结果还有更多项时会同时返回新的 page_token，下次遍历可采用该 page_token 获取查询结果
//
// 示例值：vewTpR1urY
func (builder *ListAppTableFormFieldReqBuilder) PageToken(pageToken string) *ListAppTableFormFieldReqBuilder {
	builder.apiReq.QueryParams.Set("page_token", fmt.Sprint(pageToken))
	return builder
}

func (builder *ListAppTableFormFieldReqBuilder) Build() *ListAppTableFormFieldReq {
	req := &ListAppTableFormFieldReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.Limit = builder.limit
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type ListAppTableFormFieldReq struct {
	apiReq *larkcore.ApiReq
	Limit  int // 最多返回多少记录，只有在使用迭代器访问时，才有效

}

type ListAppTableFormFieldRespData struct {
	Items     []*AppTableFormField `json:"items,omitempty"`      // 表单问题信息
	PageToken *string              `json:"page_token,omitempty"` // 下一页分页的token
	HasMore   *bool                `json:"has_more,omitempty"`   // 是否有下一页
	Total     *int                 `json:"total,omitempty"`      // 总数
}

type ListAppTableFormFieldResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *ListAppTableFormFieldRespData `json:"data"` // 业务数据
}

func (resp *ListAppTableFormFieldResp) Success() bool {
	return resp.Code == 0
}

type PatchAppTableFormFieldReqBuilder struct {
	apiReq                   *larkcore.ApiReq
	appTableFormPatchedField *AppTableFormPatchedField
}

func NewPatchAppTableFormFieldReqBuilder() *PatchAppTableFormFieldReqBuilder {
	builder := &PatchAppTableFormFieldReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 多维表格文档 Token
//
// 示例值：bascnCMII2ORej2RItqpZZUNMIe
func (builder *PatchAppTableFormFieldReqBuilder) AppToken(appToken string) *PatchAppTableFormFieldReqBuilder {
	builder.apiReq.PathParams.Set("app_token", fmt.Sprint(appToken))
	return builder
}

// 表格 ID
//
// 示例值：tblsRc9GRRXKqhvW
func (builder *PatchAppTableFormFieldReqBuilder) TableId(tableId string) *PatchAppTableFormFieldReqBuilder {
	builder.apiReq.PathParams.Set("table_id", fmt.Sprint(tableId))
	return builder
}

// 表单 ID
//
// 示例值：vewTpR1urY
func (builder *PatchAppTableFormFieldReqBuilder) FormId(formId string) *PatchAppTableFormFieldReqBuilder {
	builder.apiReq.PathParams.Set("form_id", fmt.Sprint(formId))
	return builder
}

// 表单问题 ID
//
// 示例值：fldjX7dUj5
func (builder *PatchAppTableFormFieldReqBuilder) FieldId(fieldId string) *PatchAppTableFormFieldReqBuilder {
	builder.apiReq.PathParams.Set("field_id", fmt.Sprint(fieldId))
	return builder
}

// 该接口用于更新表单中的问题项
func (builder *PatchAppTableFormFieldReqBuilder) AppTableFormPatchedField(appTableFormPatchedField *AppTableFormPatchedField) *PatchAppTableFormFieldReqBuilder {
	builder.appTableFormPatchedField = appTableFormPatchedField
	return builder
}

func (builder *PatchAppTableFormFieldReqBuilder) Build() *PatchAppTableFormFieldReq {
	req := &PatchAppTableFormFieldReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.Body = builder.appTableFormPatchedField
	return req
}

type PatchAppTableFormFieldReq struct {
	apiReq                   *larkcore.ApiReq
	AppTableFormPatchedField *AppTableFormPatchedField `body:""`
}

type PatchAppTableFormFieldRespData struct {
	Field *AppTableFormPatchedField `json:"field,omitempty"` // 更新后的表单问题项
}

type PatchAppTableFormFieldResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *PatchAppTableFormFieldRespData `json:"data"` // 业务数据
}

func (resp *PatchAppTableFormFieldResp) Success() bool {
	return resp.Code == 0
}

type BatchCreateAppTableRecordReqBodyBuilder struct {
	records     []*AppTableRecord // 本次请求将要新增的记录列表
	recordsFlag bool
}

func NewBatchCreateAppTableRecordReqBodyBuilder() *BatchCreateAppTableRecordReqBodyBuilder {
	builder := &BatchCreateAppTableRecordReqBodyBuilder{}
	return builder
}

// 本次请求将要新增的记录列表
//
//示例值：
func (builder *BatchCreateAppTableRecordReqBodyBuilder) Records(records []*AppTableRecord) *BatchCreateAppTableRecordReqBodyBuilder {
	builder.records = records
	builder.recordsFlag = true
	return builder
}

func (builder *BatchCreateAppTableRecordReqBodyBuilder) Build() *BatchCreateAppTableRecordReqBody {
	req := &BatchCreateAppTableRecordReqBody{}
	if builder.recordsFlag {
		req.Records = builder.records
	}
	return req
}

type BatchCreateAppTableRecordPathReqBodyBuilder struct {
	records     []*AppTableRecord // 本次请求将要新增的记录列表
	recordsFlag bool
}

func NewBatchCreateAppTableRecordPathReqBodyBuilder() *BatchCreateAppTableRecordPathReqBodyBuilder {
	builder := &BatchCreateAppTableRecordPathReqBodyBuilder{}
	return builder
}

// 本次请求将要新增的记录列表
//
// 示例值：
func (builder *BatchCreateAppTableRecordPathReqBodyBuilder) Records(records []*AppTableRecord) *BatchCreateAppTableRecordPathReqBodyBuilder {
	builder.records = records
	builder.recordsFlag = true
	return builder
}

func (builder *BatchCreateAppTableRecordPathReqBodyBuilder) Build() (*BatchCreateAppTableRecordReqBody, error) {
	req := &BatchCreateAppTableRecordReqBody{}
	if builder.recordsFlag {
		req.Records = builder.records
	}
	return req, nil
}

type BatchCreateAppTableRecordReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *BatchCreateAppTableRecordReqBody
}

func NewBatchCreateAppTableRecordReqBuilder() *BatchCreateAppTableRecordReqBuilder {
	builder := &BatchCreateAppTableRecordReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 多维表格的唯一标识符 [app_token 参数说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/bitable/notification#8121eebe)
//
// 示例值：appbcbWCzen6D8dezhoCH2RpMAh
func (builder *BatchCreateAppTableRecordReqBuilder) AppToken(appToken string) *BatchCreateAppTableRecordReqBuilder {
	builder.apiReq.PathParams.Set("app_token", fmt.Sprint(appToken))
	return builder
}

// 多维表格数据表的唯一标识符 [table_id 参数说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/bitable/notification#735fe883)
//
// 示例值：tblsRc9GRRXKqhvW
func (builder *BatchCreateAppTableRecordReqBuilder) TableId(tableId string) *BatchCreateAppTableRecordReqBuilder {
	builder.apiReq.PathParams.Set("table_id", fmt.Sprint(tableId))
	return builder
}

// 此次调用中使用的用户ID的类型
//
// 示例值：
func (builder *BatchCreateAppTableRecordReqBuilder) UserIdType(userIdType string) *BatchCreateAppTableRecordReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

// 该接口用于在数据表中新增多条记录，单次调用最多新增 500 条记录。
func (builder *BatchCreateAppTableRecordReqBuilder) Body(body *BatchCreateAppTableRecordReqBody) *BatchCreateAppTableRecordReqBuilder {
	builder.body = body
	return builder
}

func (builder *BatchCreateAppTableRecordReqBuilder) Build() *BatchCreateAppTableRecordReq {
	req := &BatchCreateAppTableRecordReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.body
	return req
}

type BatchCreateAppTableRecordReqBody struct {
	Records []*AppTableRecord `json:"records,omitempty"` // 本次请求将要新增的记录列表
}

type BatchCreateAppTableRecordReq struct {
	apiReq *larkcore.ApiReq
	Body   *BatchCreateAppTableRecordReqBody `body:""`
}

type BatchCreateAppTableRecordRespData struct {
	Records []*AppTableRecord `json:"records,omitempty"` // 本次请求新增的记录列表
}

type BatchCreateAppTableRecordResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *BatchCreateAppTableRecordRespData `json:"data"` // 业务数据
}

func (resp *BatchCreateAppTableRecordResp) Success() bool {
	return resp.Code == 0
}

type BatchDeleteAppTableRecordReqBodyBuilder struct {
	records     []string // 删除的多条记录id列表
	recordsFlag bool
}

func NewBatchDeleteAppTableRecordReqBodyBuilder() *BatchDeleteAppTableRecordReqBodyBuilder {
	builder := &BatchDeleteAppTableRecordReqBodyBuilder{}
	return builder
}

// 删除的多条记录id列表
//
//示例值：[;	"recIcJBbvC",;	"recvmiCORa";]
func (builder *BatchDeleteAppTableRecordReqBodyBuilder) Records(records []string) *BatchDeleteAppTableRecordReqBodyBuilder {
	builder.records = records
	builder.recordsFlag = true
	return builder
}

func (builder *BatchDeleteAppTableRecordReqBodyBuilder) Build() *BatchDeleteAppTableRecordReqBody {
	req := &BatchDeleteAppTableRecordReqBody{}
	if builder.recordsFlag {
		req.Records = builder.records
	}
	return req
}

type BatchDeleteAppTableRecordPathReqBodyBuilder struct {
	records     []string // 删除的多条记录id列表
	recordsFlag bool
}

func NewBatchDeleteAppTableRecordPathReqBodyBuilder() *BatchDeleteAppTableRecordPathReqBodyBuilder {
	builder := &BatchDeleteAppTableRecordPathReqBodyBuilder{}
	return builder
}

// 删除的多条记录id列表
//
// 示例值：[;	"recIcJBbvC",;	"recvmiCORa";]
func (builder *BatchDeleteAppTableRecordPathReqBodyBuilder) Records(records []string) *BatchDeleteAppTableRecordPathReqBodyBuilder {
	builder.records = records
	builder.recordsFlag = true
	return builder
}

func (builder *BatchDeleteAppTableRecordPathReqBodyBuilder) Build() (*BatchDeleteAppTableRecordReqBody, error) {
	req := &BatchDeleteAppTableRecordReqBody{}
	if builder.recordsFlag {
		req.Records = builder.records
	}
	return req, nil
}

type BatchDeleteAppTableRecordReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *BatchDeleteAppTableRecordReqBody
}

func NewBatchDeleteAppTableRecordReqBuilder() *BatchDeleteAppTableRecordReqBuilder {
	builder := &BatchDeleteAppTableRecordReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// bitable app token
//
// 示例值：appbcbWCzen6D8dezhoCH2RpMAh
func (builder *BatchDeleteAppTableRecordReqBuilder) AppToken(appToken string) *BatchDeleteAppTableRecordReqBuilder {
	builder.apiReq.PathParams.Set("app_token", fmt.Sprint(appToken))
	return builder
}

// table id
//
// 示例值：tblsRc9GRRXKqhvW
func (builder *BatchDeleteAppTableRecordReqBuilder) TableId(tableId string) *BatchDeleteAppTableRecordReqBuilder {
	builder.apiReq.PathParams.Set("table_id", fmt.Sprint(tableId))
	return builder
}

// 该接口用于删除数据表中现有的多条记录，单次调用中最多删除 500 条记录。
func (builder *BatchDeleteAppTableRecordReqBuilder) Body(body *BatchDeleteAppTableRecordReqBody) *BatchDeleteAppTableRecordReqBuilder {
	builder.body = body
	return builder
}

func (builder *BatchDeleteAppTableRecordReqBuilder) Build() *BatchDeleteAppTableRecordReq {
	req := &BatchDeleteAppTableRecordReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.Body = builder.body
	return req
}

type BatchDeleteAppTableRecordReqBody struct {
	Records []string `json:"records,omitempty"` // 删除的多条记录id列表
}

type BatchDeleteAppTableRecordReq struct {
	apiReq *larkcore.ApiReq
	Body   *BatchDeleteAppTableRecordReqBody `body:""`
}

type BatchDeleteAppTableRecordRespData struct {
	Records []*DeleteRecord `json:"records,omitempty"` // 记录
}

type BatchDeleteAppTableRecordResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *BatchDeleteAppTableRecordRespData `json:"data"` // 业务数据
}

func (resp *BatchDeleteAppTableRecordResp) Success() bool {
	return resp.Code == 0
}

type BatchUpdateAppTableRecordReqBodyBuilder struct {
	records     []*AppTableRecord // 记录
	recordsFlag bool
}

func NewBatchUpdateAppTableRecordReqBodyBuilder() *BatchUpdateAppTableRecordReqBodyBuilder {
	builder := &BatchUpdateAppTableRecordReqBodyBuilder{}
	return builder
}

// 记录
//
//示例值：
func (builder *BatchUpdateAppTableRecordReqBodyBuilder) Records(records []*AppTableRecord) *BatchUpdateAppTableRecordReqBodyBuilder {
	builder.records = records
	builder.recordsFlag = true
	return builder
}

func (builder *BatchUpdateAppTableRecordReqBodyBuilder) Build() *BatchUpdateAppTableRecordReqBody {
	req := &BatchUpdateAppTableRecordReqBody{}
	if builder.recordsFlag {
		req.Records = builder.records
	}
	return req
}

type BatchUpdateAppTableRecordPathReqBodyBuilder struct {
	records     []*AppTableRecord // 记录
	recordsFlag bool
}

func NewBatchUpdateAppTableRecordPathReqBodyBuilder() *BatchUpdateAppTableRecordPathReqBodyBuilder {
	builder := &BatchUpdateAppTableRecordPathReqBodyBuilder{}
	return builder
}

// 记录
//
// 示例值：
func (builder *BatchUpdateAppTableRecordPathReqBodyBuilder) Records(records []*AppTableRecord) *BatchUpdateAppTableRecordPathReqBodyBuilder {
	builder.records = records
	builder.recordsFlag = true
	return builder
}

func (builder *BatchUpdateAppTableRecordPathReqBodyBuilder) Build() (*BatchUpdateAppTableRecordReqBody, error) {
	req := &BatchUpdateAppTableRecordReqBody{}
	if builder.recordsFlag {
		req.Records = builder.records
	}
	return req, nil
}

type BatchUpdateAppTableRecordReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *BatchUpdateAppTableRecordReqBody
}

func NewBatchUpdateAppTableRecordReqBuilder() *BatchUpdateAppTableRecordReqBuilder {
	builder := &BatchUpdateAppTableRecordReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// bitable app token
//
// 示例值：appbcbWCzen6D8dezhoCH2RpMAh
func (builder *BatchUpdateAppTableRecordReqBuilder) AppToken(appToken string) *BatchUpdateAppTableRecordReqBuilder {
	builder.apiReq.PathParams.Set("app_token", fmt.Sprint(appToken))
	return builder
}

// table id
//
// 示例值：tblsRc9GRRXKqhvW
func (builder *BatchUpdateAppTableRecordReqBuilder) TableId(tableId string) *BatchUpdateAppTableRecordReqBuilder {
	builder.apiReq.PathParams.Set("table_id", fmt.Sprint(tableId))
	return builder
}

// 此次调用中使用的用户ID的类型
//
// 示例值：
func (builder *BatchUpdateAppTableRecordReqBuilder) UserIdType(userIdType string) *BatchUpdateAppTableRecordReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

// 该接口用于更新数据表中的多条记录，单次调用最多更新 500 条记录。
func (builder *BatchUpdateAppTableRecordReqBuilder) Body(body *BatchUpdateAppTableRecordReqBody) *BatchUpdateAppTableRecordReqBuilder {
	builder.body = body
	return builder
}

func (builder *BatchUpdateAppTableRecordReqBuilder) Build() *BatchUpdateAppTableRecordReq {
	req := &BatchUpdateAppTableRecordReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.body
	return req
}

type BatchUpdateAppTableRecordReqBody struct {
	Records []*AppTableRecord `json:"records,omitempty"` // 记录
}

type BatchUpdateAppTableRecordReq struct {
	apiReq *larkcore.ApiReq
	Body   *BatchUpdateAppTableRecordReqBody `body:""`
}

type BatchUpdateAppTableRecordRespData struct {
	Records []*AppTableRecord `json:"records,omitempty"` // 记录
}

type BatchUpdateAppTableRecordResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *BatchUpdateAppTableRecordRespData `json:"data"` // 业务数据
}

func (resp *BatchUpdateAppTableRecordResp) Success() bool {
	return resp.Code == 0
}

type CreateAppTableRecordReqBuilder struct {
	apiReq         *larkcore.ApiReq
	appTableRecord *AppTableRecord
}

func NewCreateAppTableRecordReqBuilder() *CreateAppTableRecordReqBuilder {
	builder := &CreateAppTableRecordReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 多维表格的唯一标识符 [app_token 参数说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/bitable/notification#8121eebe)
//
// 示例值：bascng7vrxcxpig7geggXiCtadY
func (builder *CreateAppTableRecordReqBuilder) AppToken(appToken string) *CreateAppTableRecordReqBuilder {
	builder.apiReq.PathParams.Set("app_token", fmt.Sprint(appToken))
	return builder
}

// 多维表格数据表的唯一标识符 [table_id 参数说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/bitable/notification#735fe883)
//
// 示例值：tblUa9vcYjWQYJCj
func (builder *CreateAppTableRecordReqBuilder) TableId(tableId string) *CreateAppTableRecordReqBuilder {
	builder.apiReq.PathParams.Set("table_id", fmt.Sprint(tableId))
	return builder
}

// 此次调用中使用的用户ID的类型
//
// 示例值：
func (builder *CreateAppTableRecordReqBuilder) UserIdType(userIdType string) *CreateAppTableRecordReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

// 该接口用于在数据表中新增一条记录
func (builder *CreateAppTableRecordReqBuilder) AppTableRecord(appTableRecord *AppTableRecord) *CreateAppTableRecordReqBuilder {
	builder.appTableRecord = appTableRecord
	return builder
}

func (builder *CreateAppTableRecordReqBuilder) Build() *CreateAppTableRecordReq {
	req := &CreateAppTableRecordReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.appTableRecord
	return req
}

type CreateAppTableRecordReq struct {
	apiReq         *larkcore.ApiReq
	AppTableRecord *AppTableRecord `body:""`
}

type CreateAppTableRecordRespData struct {
	Record *AppTableRecord `json:"record,omitempty"` // 新增的记录的内容
}

type CreateAppTableRecordResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *CreateAppTableRecordRespData `json:"data"` // 业务数据
}

func (resp *CreateAppTableRecordResp) Success() bool {
	return resp.Code == 0
}

type DeleteAppTableRecordReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewDeleteAppTableRecordReqBuilder() *DeleteAppTableRecordReqBuilder {
	builder := &DeleteAppTableRecordReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// bitable app token
//
// 示例值：appbcbWCzen6D8dezhoCH2RpMAh
func (builder *DeleteAppTableRecordReqBuilder) AppToken(appToken string) *DeleteAppTableRecordReqBuilder {
	builder.apiReq.PathParams.Set("app_token", fmt.Sprint(appToken))
	return builder
}

// table id
//
// 示例值：tblsRc9GRRXKqhvW
func (builder *DeleteAppTableRecordReqBuilder) TableId(tableId string) *DeleteAppTableRecordReqBuilder {
	builder.apiReq.PathParams.Set("table_id", fmt.Sprint(tableId))
	return builder
}

// 单条记录的Id
//
// 示例值：recpCsf4ME
func (builder *DeleteAppTableRecordReqBuilder) RecordId(recordId string) *DeleteAppTableRecordReqBuilder {
	builder.apiReq.PathParams.Set("record_id", fmt.Sprint(recordId))
	return builder
}

func (builder *DeleteAppTableRecordReqBuilder) Build() *DeleteAppTableRecordReq {
	req := &DeleteAppTableRecordReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	return req
}

type DeleteAppTableRecordReq struct {
	apiReq *larkcore.ApiReq
}

type DeleteAppTableRecordRespData struct {
	Deleted  *bool   `json:"deleted,omitempty"`   // 是否成功删除
	RecordId *string `json:"record_id,omitempty"` // 删除的记录id
}

type DeleteAppTableRecordResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *DeleteAppTableRecordRespData `json:"data"` // 业务数据
}

func (resp *DeleteAppTableRecordResp) Success() bool {
	return resp.Code == 0
}

type GetAppTableRecordReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewGetAppTableRecordReqBuilder() *GetAppTableRecordReqBuilder {
	builder := &GetAppTableRecordReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// bitable app token
//
// 示例值：bascnCMII2ORej2RItqpZZUNMIe
func (builder *GetAppTableRecordReqBuilder) AppToken(appToken string) *GetAppTableRecordReqBuilder {
	builder.apiReq.PathParams.Set("app_token", fmt.Sprint(appToken))
	return builder
}

// table id
//
// 示例值：tblxI2tWaxP5dG7p
func (builder *GetAppTableRecordReqBuilder) TableId(tableId string) *GetAppTableRecordReqBuilder {
	builder.apiReq.PathParams.Set("table_id", fmt.Sprint(tableId))
	return builder
}

// 单条记录的 id
//
// 示例值：recn0hoyXL
func (builder *GetAppTableRecordReqBuilder) RecordId(recordId string) *GetAppTableRecordReqBuilder {
	builder.apiReq.PathParams.Set("record_id", fmt.Sprint(recordId))
	return builder
}

// 控制多行文本字段数据的返回格式, true 表示以数组形式返回
//
// 示例值：true
func (builder *GetAppTableRecordReqBuilder) TextFieldAsArray(textFieldAsArray bool) *GetAppTableRecordReqBuilder {
	builder.apiReq.QueryParams.Set("text_field_as_array", fmt.Sprint(textFieldAsArray))
	return builder
}

// 此次调用中使用的用户ID的类型
//
// 示例值：
func (builder *GetAppTableRecordReqBuilder) UserIdType(userIdType string) *GetAppTableRecordReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

// 控制公式、查找引用是否显示完整的原样返回结果
//
// 示例值：true
func (builder *GetAppTableRecordReqBuilder) DisplayFormulaRef(displayFormulaRef bool) *GetAppTableRecordReqBuilder {
	builder.apiReq.QueryParams.Set("display_formula_ref", fmt.Sprint(displayFormulaRef))
	return builder
}

// 控制是否返回自动计算的字段，例如 `created_by`/`created_time`/`last_modified_by`/`last_modified_time`，true 表示返回
//
// 示例值：true
func (builder *GetAppTableRecordReqBuilder) AutomaticFields(automaticFields bool) *GetAppTableRecordReqBuilder {
	builder.apiReq.QueryParams.Set("automatic_fields", fmt.Sprint(automaticFields))
	return builder
}

func (builder *GetAppTableRecordReqBuilder) Build() *GetAppTableRecordReq {
	req := &GetAppTableRecordReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type GetAppTableRecordReq struct {
	apiReq *larkcore.ApiReq
}

type GetAppTableRecordRespData struct {
	Record *AppTableRecord `json:"record,omitempty"` // 记录
}

type GetAppTableRecordResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *GetAppTableRecordRespData `json:"data"` // 业务数据
}

func (resp *GetAppTableRecordResp) Success() bool {
	return resp.Code == 0
}

type ListAppTableRecordReqBuilder struct {
	apiReq *larkcore.ApiReq
	limit  int // 最大返回多少记录，当使用迭代器访问时才有效
}

func NewListAppTableRecordReqBuilder() *ListAppTableRecordReqBuilder {
	builder := &ListAppTableRecordReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 最大返回多少记录，当使用迭代器访问时才有效
func (builder *ListAppTableRecordReqBuilder) Limit(limit int) *ListAppTableRecordReqBuilder {
	builder.limit = limit
	return builder
}

// 多维表格的唯一标识符 [app_token 参数说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/bitable/notification#8121eebe)
//
// 示例值：bascnCMII2ORej2RItqpZZUNMIe
func (builder *ListAppTableRecordReqBuilder) AppToken(appToken string) *ListAppTableRecordReqBuilder {
	builder.apiReq.PathParams.Set("app_token", fmt.Sprint(appToken))
	return builder
}

// 多维表格数据表的唯一标识符 [table_id 参数说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/bitable/notification#735fe883)
//
// 示例值：tblxI2tWaxP5dG7p
func (builder *ListAppTableRecordReqBuilder) TableId(tableId string) *ListAppTableRecordReqBuilder {
	builder.apiReq.PathParams.Set("table_id", fmt.Sprint(tableId))
	return builder
}

// 视图的唯一标识符，获取指定视图下的记录[view_id 参数说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/bitable/notification#8121eebe);;注意：;当 filter 参数 或 sort 参数不为空时，请求视为对数据表中的全部数据做条件过滤，指定的view_id 会被忽略。
//
// 示例值：vewqhz51lk
func (builder *ListAppTableRecordReqBuilder) ViewId(viewId string) *ListAppTableRecordReqBuilder {
	builder.apiReq.QueryParams.Set("view_id", fmt.Sprint(viewId))
	return builder
}

// 筛选参数，用于指定本次查询的筛选条件;;注意：;;1.不支持对“人员”以及“关联字段”的属性进行过滤筛选，如人员的 OpenID。;;2.指定筛选条件时，参数长度不超过2000个字符。;;;详细请参考[筛选条件支持的公式](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/bitable-v1/filter)
//
// 示例值：AND(CurrentValue.[身高]>180, CurrentValue.[体重]>150)
func (builder *ListAppTableRecordReqBuilder) Filter(filter string) *ListAppTableRecordReqBuilder {
	builder.apiReq.QueryParams.Set("filter", fmt.Sprint(filter))
	return builder
}

// 排序参数，用于指定本次查询返回结果的顺序;;注意：;;1.不支持对带“公式”和“关联字段”的表的使用。;;2.指定排序条件时，参数长度不超过1000字符。;;3.当存在多个排序条件时，数据将根据条件顺序逐层排序
//
// 示例值：["字段1 DESC","字段2 ASC"]
func (builder *ListAppTableRecordReqBuilder) Sort(sort string) *ListAppTableRecordReqBuilder {
	builder.apiReq.QueryParams.Set("sort", fmt.Sprint(sort))
	return builder
}

// 字段名称，用于指定本次查询返回记录中包含的字段
//
// 示例值：["字段1","字段2"]
func (builder *ListAppTableRecordReqBuilder) FieldNames(fieldNames string) *ListAppTableRecordReqBuilder {
	builder.apiReq.QueryParams.Set("field_names", fmt.Sprint(fieldNames))
	return builder
}

// 控制多行文本字段数据的返回格式，true 表示以数组形式返回。;;注意：;;1.多行文本中如果有超链接部分，则会返回链接的 URL。;;2.目前可以返回多行文本中 URL 类型为多维表格链接、飞书 doc、飞书 sheet的URL类型以及@人员的数据结构。
//
// 示例值：true
func (builder *ListAppTableRecordReqBuilder) TextFieldAsArray(textFieldAsArray bool) *ListAppTableRecordReqBuilder {
	builder.apiReq.QueryParams.Set("text_field_as_array", fmt.Sprint(textFieldAsArray))
	return builder
}

// 此次调用中使用的用户ID的类型
//
// 示例值：
func (builder *ListAppTableRecordReqBuilder) UserIdType(userIdType string) *ListAppTableRecordReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

// 默认值为false，返回当前字段的默认类型和结果；当该参数的值为true时，公式 和 查找引用 类型的字段，将会以 被引用字段 的格式返回
//
// 示例值：true
func (builder *ListAppTableRecordReqBuilder) DisplayFormulaRef(displayFormulaRef bool) *ListAppTableRecordReqBuilder {
	builder.apiReq.QueryParams.Set("display_formula_ref", fmt.Sprint(displayFormulaRef))
	return builder
}

// 控制是否返回自动计算的字段，例如 `created_by`/`created_time`/`last_modified_by`/`last_modified_time`，true 表示返回
//
// 示例值：true
func (builder *ListAppTableRecordReqBuilder) AutomaticFields(automaticFields bool) *ListAppTableRecordReqBuilder {
	builder.apiReq.QueryParams.Set("automatic_fields", fmt.Sprint(automaticFields))
	return builder
}

//
//
// 示例值：recn0hoyXL
func (builder *ListAppTableRecordReqBuilder) PageToken(pageToken string) *ListAppTableRecordReqBuilder {
	builder.apiReq.QueryParams.Set("page_token", fmt.Sprint(pageToken))
	return builder
}

//
//
// 示例值：10
func (builder *ListAppTableRecordReqBuilder) PageSize(pageSize int) *ListAppTableRecordReqBuilder {
	builder.apiReq.QueryParams.Set("page_size", fmt.Sprint(pageSize))
	return builder
}

func (builder *ListAppTableRecordReqBuilder) Build() *ListAppTableRecordReq {
	req := &ListAppTableRecordReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.Limit = builder.limit
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type ListAppTableRecordReq struct {
	apiReq *larkcore.ApiReq
	Limit  int // 最多返回多少记录，只有在使用迭代器访问时，才有效

}

type ListAppTableRecordRespData struct {
	HasMore   *bool             `json:"has_more,omitempty"`   // 是否有下一页数据
	PageToken *string           `json:"page_token,omitempty"` // 下一页分页的token
	Total     *int              `json:"total,omitempty"`      // 本次请求返回的总记录数
	Items     []*AppTableRecord `json:"items,omitempty"`      // 本次请求返回的全部记录列表
}

type ListAppTableRecordResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *ListAppTableRecordRespData `json:"data"` // 业务数据
}

func (resp *ListAppTableRecordResp) Success() bool {
	return resp.Code == 0
}

type UpdateAppTableRecordReqBuilder struct {
	apiReq         *larkcore.ApiReq
	appTableRecord *AppTableRecord
}

func NewUpdateAppTableRecordReqBuilder() *UpdateAppTableRecordReqBuilder {
	builder := &UpdateAppTableRecordReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 多维表格的唯一标识符 [app_token 参数说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/bitable/notification#8121eebe)
//
// 示例值：appbcbWCzen6D8dezhoCH2RpMAh
func (builder *UpdateAppTableRecordReqBuilder) AppToken(appToken string) *UpdateAppTableRecordReqBuilder {
	builder.apiReq.PathParams.Set("app_token", fmt.Sprint(appToken))
	return builder
}

// 多维表格数据表的唯一标识符 [table_id 参数说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/bitable/notification#735fe883)
//
// 示例值：tblsRc9GRRXKqhvW
func (builder *UpdateAppTableRecordReqBuilder) TableId(tableId string) *UpdateAppTableRecordReqBuilder {
	builder.apiReq.PathParams.Set("table_id", fmt.Sprint(tableId))
	return builder
}

// 一条记录的唯一标识 id [record_id 参数说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/bitable/notification#15d8db94)
//
// 示例值：recqwIwhc6
func (builder *UpdateAppTableRecordReqBuilder) RecordId(recordId string) *UpdateAppTableRecordReqBuilder {
	builder.apiReq.PathParams.Set("record_id", fmt.Sprint(recordId))
	return builder
}

// 此次调用中使用的用户ID的类型
//
// 示例值：
func (builder *UpdateAppTableRecordReqBuilder) UserIdType(userIdType string) *UpdateAppTableRecordReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

// 该接口用于更新数据表中的一条记录
func (builder *UpdateAppTableRecordReqBuilder) AppTableRecord(appTableRecord *AppTableRecord) *UpdateAppTableRecordReqBuilder {
	builder.appTableRecord = appTableRecord
	return builder
}

func (builder *UpdateAppTableRecordReqBuilder) Build() *UpdateAppTableRecordReq {
	req := &UpdateAppTableRecordReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.appTableRecord
	return req
}

type UpdateAppTableRecordReq struct {
	apiReq         *larkcore.ApiReq
	AppTableRecord *AppTableRecord `body:""`
}

type UpdateAppTableRecordRespData struct {
	Record *AppTableRecord `json:"record,omitempty"` // 记录更新后的内容
}

type UpdateAppTableRecordResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *UpdateAppTableRecordRespData `json:"data"` // 业务数据
}

func (resp *UpdateAppTableRecordResp) Success() bool {
	return resp.Code == 0
}

type CreateAppTableViewReqBuilder struct {
	apiReq  *larkcore.ApiReq
	reqView *ReqView
}

func NewCreateAppTableViewReqBuilder() *CreateAppTableViewReqBuilder {
	builder := &CreateAppTableViewReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// bitable app token
//
// 示例值：appbcbWCzen6D8dezhoCH2RpMAh
func (builder *CreateAppTableViewReqBuilder) AppToken(appToken string) *CreateAppTableViewReqBuilder {
	builder.apiReq.PathParams.Set("app_token", fmt.Sprint(appToken))
	return builder
}

// table id
//
// 示例值：tblsRc9GRRXKqhvW
func (builder *CreateAppTableViewReqBuilder) TableId(tableId string) *CreateAppTableViewReqBuilder {
	builder.apiReq.PathParams.Set("table_id", fmt.Sprint(tableId))
	return builder
}

// 在数据表中新增一个视图
func (builder *CreateAppTableViewReqBuilder) ReqView(reqView *ReqView) *CreateAppTableViewReqBuilder {
	builder.reqView = reqView
	return builder
}

func (builder *CreateAppTableViewReqBuilder) Build() *CreateAppTableViewReq {
	req := &CreateAppTableViewReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.Body = builder.reqView
	return req
}

type CreateAppTableViewReq struct {
	apiReq  *larkcore.ApiReq
	ReqView *ReqView `body:""`
}

type CreateAppTableViewRespData struct {
	View *AppTableView `json:"view,omitempty"` // 视图
}

type CreateAppTableViewResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *CreateAppTableViewRespData `json:"data"` // 业务数据
}

func (resp *CreateAppTableViewResp) Success() bool {
	return resp.Code == 0
}

type DeleteAppTableViewReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewDeleteAppTableViewReqBuilder() *DeleteAppTableViewReqBuilder {
	builder := &DeleteAppTableViewReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// bitable app token
//
// 示例值：appbcbWCzen6D8dezhoCH2RpMAh
func (builder *DeleteAppTableViewReqBuilder) AppToken(appToken string) *DeleteAppTableViewReqBuilder {
	builder.apiReq.PathParams.Set("app_token", fmt.Sprint(appToken))
	return builder
}

// table id
//
// 示例值：tblsRc9GRRXKqhvW
func (builder *DeleteAppTableViewReqBuilder) TableId(tableId string) *DeleteAppTableViewReqBuilder {
	builder.apiReq.PathParams.Set("table_id", fmt.Sprint(tableId))
	return builder
}

// 视图Id
//
// 示例值：vewTpR1urY
func (builder *DeleteAppTableViewReqBuilder) ViewId(viewId string) *DeleteAppTableViewReqBuilder {
	builder.apiReq.PathParams.Set("view_id", fmt.Sprint(viewId))
	return builder
}

func (builder *DeleteAppTableViewReqBuilder) Build() *DeleteAppTableViewReq {
	req := &DeleteAppTableViewReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	return req
}

type DeleteAppTableViewReq struct {
	apiReq *larkcore.ApiReq
}

type DeleteAppTableViewResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *DeleteAppTableViewResp) Success() bool {
	return resp.Code == 0
}

type ListAppTableViewReqBuilder struct {
	apiReq *larkcore.ApiReq
	limit  int // 最大返回多少记录，当使用迭代器访问时才有效
}

func NewListAppTableViewReqBuilder() *ListAppTableViewReqBuilder {
	builder := &ListAppTableViewReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 最大返回多少记录，当使用迭代器访问时才有效
func (builder *ListAppTableViewReqBuilder) Limit(limit int) *ListAppTableViewReqBuilder {
	builder.limit = limit
	return builder
}

// bitable app token
//
// 示例值：appbcbWCzen6D8dezhoCH2RpMAh
func (builder *ListAppTableViewReqBuilder) AppToken(appToken string) *ListAppTableViewReqBuilder {
	builder.apiReq.PathParams.Set("app_token", fmt.Sprint(appToken))
	return builder
}

// table id
//
// 示例值：tblsRc9GRRXKqhvW
func (builder *ListAppTableViewReqBuilder) TableId(tableId string) *ListAppTableViewReqBuilder {
	builder.apiReq.PathParams.Set("table_id", fmt.Sprint(tableId))
	return builder
}

// 分页大小
//
// 示例值：10
func (builder *ListAppTableViewReqBuilder) PageSize(pageSize int) *ListAppTableViewReqBuilder {
	builder.apiReq.QueryParams.Set("page_size", fmt.Sprint(pageSize))
	return builder
}

// 分页标记，第一次请求不填，表示从头开始遍历；分页查询结果还有更多项时会同时返回新的 page_token，下次遍历可采用该 page_token 获取查询结果
//
// 示例值：vewTpR1urY
func (builder *ListAppTableViewReqBuilder) PageToken(pageToken string) *ListAppTableViewReqBuilder {
	builder.apiReq.QueryParams.Set("page_token", fmt.Sprint(pageToken))
	return builder
}

func (builder *ListAppTableViewReqBuilder) Build() *ListAppTableViewReq {
	req := &ListAppTableViewReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.Limit = builder.limit
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type ListAppTableViewReq struct {
	apiReq *larkcore.ApiReq
	Limit  int // 最多返回多少记录，只有在使用迭代器访问时，才有效

}

type ListAppTableViewRespData struct {
	Items     []*AppTableView `json:"items,omitempty"`      // 视图信息
	PageToken *string         `json:"page_token,omitempty"` // 下一页分页的token
	HasMore   *bool           `json:"has_more,omitempty"`   // 是否有下一页数据
	Total     *int            `json:"total,omitempty"`      // 总数
}

type ListAppTableViewResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *ListAppTableViewRespData `json:"data"` // 业务数据
}

func (resp *ListAppTableViewResp) Success() bool {
	return resp.Code == 0
}

type ListAppDashboardIterator struct {
	nextPageToken *string
	items         []*AppDashboard
	index         int
	limit         int
	ctx           context.Context
	req           *ListAppDashboardReq
	listFunc      func(ctx context.Context, req *ListAppDashboardReq, options ...larkcore.RequestOptionFunc) (*ListAppDashboardResp, error)
	options       []larkcore.RequestOptionFunc
	curlNum       int
}

func (iterator *ListAppDashboardIterator) Next() (bool, *AppDashboard, error) {
	// 达到最大量，则返回
	if iterator.limit > 0 && iterator.curlNum >= iterator.limit {
		return false, nil, nil
	}

	// 为0则拉取数据
	if iterator.index == 0 || iterator.index >= len(iterator.items) {
		if iterator.index != 0 && iterator.nextPageToken == nil {
			return false, nil, nil
		}
		if iterator.nextPageToken != nil {
			iterator.req.apiReq.QueryParams.Set("page_token", *iterator.nextPageToken)
		}
		resp, err := iterator.listFunc(iterator.ctx, iterator.req, iterator.options...)
		if err != nil {
			return false, nil, err
		}

		if resp.Code != 0 {
			return false, nil, errors.New(fmt.Sprintf("Code:%d,Msg:%s", resp.Code, resp.Msg))
		}

		if len(resp.Data.Dashboards) == 0 {
			return false, nil, nil
		}

		iterator.nextPageToken = resp.Data.PageToken
		iterator.items = resp.Data.Dashboards
		iterator.index = 0
	}

	block := iterator.items[iterator.index]
	iterator.index++
	iterator.curlNum++
	return true, block, nil
}

func (iterator *ListAppDashboardIterator) NextPageToken() *string {
	return iterator.nextPageToken
}

type ListAppRoleIterator struct {
	nextPageToken *string
	items         []*AppRole
	index         int
	limit         int
	ctx           context.Context
	req           *ListAppRoleReq
	listFunc      func(ctx context.Context, req *ListAppRoleReq, options ...larkcore.RequestOptionFunc) (*ListAppRoleResp, error)
	options       []larkcore.RequestOptionFunc
	curlNum       int
}

func (iterator *ListAppRoleIterator) Next() (bool, *AppRole, error) {
	// 达到最大量，则返回
	if iterator.limit > 0 && iterator.curlNum >= iterator.limit {
		return false, nil, nil
	}

	// 为0则拉取数据
	if iterator.index == 0 || iterator.index >= len(iterator.items) {
		if iterator.index != 0 && iterator.nextPageToken == nil {
			return false, nil, nil
		}
		if iterator.nextPageToken != nil {
			iterator.req.apiReq.QueryParams.Set("page_token", *iterator.nextPageToken)
		}
		resp, err := iterator.listFunc(iterator.ctx, iterator.req, iterator.options...)
		if err != nil {
			return false, nil, err
		}

		if resp.Code != 0 {
			return false, nil, errors.New(fmt.Sprintf("Code:%d,Msg:%s", resp.Code, resp.Msg))
		}

		if len(resp.Data.Items) == 0 {
			return false, nil, nil
		}

		iterator.nextPageToken = resp.Data.PageToken
		iterator.items = resp.Data.Items
		iterator.index = 0
	}

	block := iterator.items[iterator.index]
	iterator.index++
	iterator.curlNum++
	return true, block, nil
}

func (iterator *ListAppRoleIterator) NextPageToken() *string {
	return iterator.nextPageToken
}

type ListAppRoleMemberIterator struct {
	nextPageToken *string
	items         []*AppRoleMember
	index         int
	limit         int
	ctx           context.Context
	req           *ListAppRoleMemberReq
	listFunc      func(ctx context.Context, req *ListAppRoleMemberReq, options ...larkcore.RequestOptionFunc) (*ListAppRoleMemberResp, error)
	options       []larkcore.RequestOptionFunc
	curlNum       int
}

func (iterator *ListAppRoleMemberIterator) Next() (bool, *AppRoleMember, error) {
	// 达到最大量，则返回
	if iterator.limit > 0 && iterator.curlNum >= iterator.limit {
		return false, nil, nil
	}

	// 为0则拉取数据
	if iterator.index == 0 || iterator.index >= len(iterator.items) {
		if iterator.index != 0 && iterator.nextPageToken == nil {
			return false, nil, nil
		}
		if iterator.nextPageToken != nil {
			iterator.req.apiReq.QueryParams.Set("page_token", *iterator.nextPageToken)
		}
		resp, err := iterator.listFunc(iterator.ctx, iterator.req, iterator.options...)
		if err != nil {
			return false, nil, err
		}

		if resp.Code != 0 {
			return false, nil, errors.New(fmt.Sprintf("Code:%d,Msg:%s", resp.Code, resp.Msg))
		}

		if len(resp.Data.Items) == 0 {
			return false, nil, nil
		}

		iterator.nextPageToken = resp.Data.PageToken
		iterator.items = resp.Data.Items
		iterator.index = 0
	}

	block := iterator.items[iterator.index]
	iterator.index++
	iterator.curlNum++
	return true, block, nil
}

func (iterator *ListAppRoleMemberIterator) NextPageToken() *string {
	return iterator.nextPageToken
}

type ListAppTableIterator struct {
	nextPageToken *string
	items         []*AppTable
	index         int
	limit         int
	ctx           context.Context
	req           *ListAppTableReq
	listFunc      func(ctx context.Context, req *ListAppTableReq, options ...larkcore.RequestOptionFunc) (*ListAppTableResp, error)
	options       []larkcore.RequestOptionFunc
	curlNum       int
}

func (iterator *ListAppTableIterator) Next() (bool, *AppTable, error) {
	// 达到最大量，则返回
	if iterator.limit > 0 && iterator.curlNum >= iterator.limit {
		return false, nil, nil
	}

	// 为0则拉取数据
	if iterator.index == 0 || iterator.index >= len(iterator.items) {
		if iterator.index != 0 && iterator.nextPageToken == nil {
			return false, nil, nil
		}
		if iterator.nextPageToken != nil {
			iterator.req.apiReq.QueryParams.Set("page_token", *iterator.nextPageToken)
		}
		resp, err := iterator.listFunc(iterator.ctx, iterator.req, iterator.options...)
		if err != nil {
			return false, nil, err
		}

		if resp.Code != 0 {
			return false, nil, errors.New(fmt.Sprintf("Code:%d,Msg:%s", resp.Code, resp.Msg))
		}

		if len(resp.Data.Items) == 0 {
			return false, nil, nil
		}

		iterator.nextPageToken = resp.Data.PageToken
		iterator.items = resp.Data.Items
		iterator.index = 0
	}

	block := iterator.items[iterator.index]
	iterator.index++
	iterator.curlNum++
	return true, block, nil
}

func (iterator *ListAppTableIterator) NextPageToken() *string {
	return iterator.nextPageToken
}

type ListAppTableFieldIterator struct {
	nextPageToken *string
	items         []*AppTableField
	index         int
	limit         int
	ctx           context.Context
	req           *ListAppTableFieldReq
	listFunc      func(ctx context.Context, req *ListAppTableFieldReq, options ...larkcore.RequestOptionFunc) (*ListAppTableFieldResp, error)
	options       []larkcore.RequestOptionFunc
	curlNum       int
}

func (iterator *ListAppTableFieldIterator) Next() (bool, *AppTableField, error) {
	// 达到最大量，则返回
	if iterator.limit > 0 && iterator.curlNum >= iterator.limit {
		return false, nil, nil
	}

	// 为0则拉取数据
	if iterator.index == 0 || iterator.index >= len(iterator.items) {
		if iterator.index != 0 && iterator.nextPageToken == nil {
			return false, nil, nil
		}
		if iterator.nextPageToken != nil {
			iterator.req.apiReq.QueryParams.Set("page_token", *iterator.nextPageToken)
		}
		resp, err := iterator.listFunc(iterator.ctx, iterator.req, iterator.options...)
		if err != nil {
			return false, nil, err
		}

		if resp.Code != 0 {
			return false, nil, errors.New(fmt.Sprintf("Code:%d,Msg:%s", resp.Code, resp.Msg))
		}

		if len(resp.Data.Items) == 0 {
			return false, nil, nil
		}

		iterator.nextPageToken = resp.Data.PageToken
		iterator.items = resp.Data.Items
		iterator.index = 0
	}

	block := iterator.items[iterator.index]
	iterator.index++
	iterator.curlNum++
	return true, block, nil
}

func (iterator *ListAppTableFieldIterator) NextPageToken() *string {
	return iterator.nextPageToken
}

type ListAppTableFormFieldIterator struct {
	nextPageToken *string
	items         []*AppTableFormField
	index         int
	limit         int
	ctx           context.Context
	req           *ListAppTableFormFieldReq
	listFunc      func(ctx context.Context, req *ListAppTableFormFieldReq, options ...larkcore.RequestOptionFunc) (*ListAppTableFormFieldResp, error)
	options       []larkcore.RequestOptionFunc
	curlNum       int
}

func (iterator *ListAppTableFormFieldIterator) Next() (bool, *AppTableFormField, error) {
	// 达到最大量，则返回
	if iterator.limit > 0 && iterator.curlNum >= iterator.limit {
		return false, nil, nil
	}

	// 为0则拉取数据
	if iterator.index == 0 || iterator.index >= len(iterator.items) {
		if iterator.index != 0 && iterator.nextPageToken == nil {
			return false, nil, nil
		}
		if iterator.nextPageToken != nil {
			iterator.req.apiReq.QueryParams.Set("page_token", *iterator.nextPageToken)
		}
		resp, err := iterator.listFunc(iterator.ctx, iterator.req, iterator.options...)
		if err != nil {
			return false, nil, err
		}

		if resp.Code != 0 {
			return false, nil, errors.New(fmt.Sprintf("Code:%d,Msg:%s", resp.Code, resp.Msg))
		}

		if len(resp.Data.Items) == 0 {
			return false, nil, nil
		}

		iterator.nextPageToken = resp.Data.PageToken
		iterator.items = resp.Data.Items
		iterator.index = 0
	}

	block := iterator.items[iterator.index]
	iterator.index++
	iterator.curlNum++
	return true, block, nil
}

func (iterator *ListAppTableFormFieldIterator) NextPageToken() *string {
	return iterator.nextPageToken
}

type ListAppTableRecordIterator struct {
	nextPageToken *string
	items         []*AppTableRecord
	index         int
	limit         int
	ctx           context.Context
	req           *ListAppTableRecordReq
	listFunc      func(ctx context.Context, req *ListAppTableRecordReq, options ...larkcore.RequestOptionFunc) (*ListAppTableRecordResp, error)
	options       []larkcore.RequestOptionFunc
	curlNum       int
}

func (iterator *ListAppTableRecordIterator) Next() (bool, *AppTableRecord, error) {
	// 达到最大量，则返回
	if iterator.limit > 0 && iterator.curlNum >= iterator.limit {
		return false, nil, nil
	}

	// 为0则拉取数据
	if iterator.index == 0 || iterator.index >= len(iterator.items) {
		if iterator.index != 0 && iterator.nextPageToken == nil {
			return false, nil, nil
		}
		if iterator.nextPageToken != nil {
			iterator.req.apiReq.QueryParams.Set("page_token", *iterator.nextPageToken)
		}
		resp, err := iterator.listFunc(iterator.ctx, iterator.req, iterator.options...)
		if err != nil {
			return false, nil, err
		}

		if resp.Code != 0 {
			return false, nil, errors.New(fmt.Sprintf("Code:%d,Msg:%s", resp.Code, resp.Msg))
		}

		if len(resp.Data.Items) == 0 {
			return false, nil, nil
		}

		iterator.nextPageToken = resp.Data.PageToken
		iterator.items = resp.Data.Items
		iterator.index = 0
	}

	block := iterator.items[iterator.index]
	iterator.index++
	iterator.curlNum++
	return true, block, nil
}

func (iterator *ListAppTableRecordIterator) NextPageToken() *string {
	return iterator.nextPageToken
}

type ListAppTableViewIterator struct {
	nextPageToken *string
	items         []*AppTableView
	index         int
	limit         int
	ctx           context.Context
	req           *ListAppTableViewReq
	listFunc      func(ctx context.Context, req *ListAppTableViewReq, options ...larkcore.RequestOptionFunc) (*ListAppTableViewResp, error)
	options       []larkcore.RequestOptionFunc
	curlNum       int
}

func (iterator *ListAppTableViewIterator) Next() (bool, *AppTableView, error) {
	// 达到最大量，则返回
	if iterator.limit > 0 && iterator.curlNum >= iterator.limit {
		return false, nil, nil
	}

	// 为0则拉取数据
	if iterator.index == 0 || iterator.index >= len(iterator.items) {
		if iterator.index != 0 && iterator.nextPageToken == nil {
			return false, nil, nil
		}
		if iterator.nextPageToken != nil {
			iterator.req.apiReq.QueryParams.Set("page_token", *iterator.nextPageToken)
		}
		resp, err := iterator.listFunc(iterator.ctx, iterator.req, iterator.options...)
		if err != nil {
			return false, nil, err
		}

		if resp.Code != 0 {
			return false, nil, errors.New(fmt.Sprintf("Code:%d,Msg:%s", resp.Code, resp.Msg))
		}

		if len(resp.Data.Items) == 0 {
			return false, nil, nil
		}

		iterator.nextPageToken = resp.Data.PageToken
		iterator.items = resp.Data.Items
		iterator.index = 0
	}

	block := iterator.items[iterator.index]
	iterator.index++
	iterator.curlNum++
	return true, block, nil
}

func (iterator *ListAppTableViewIterator) NextPageToken() *string {
	return iterator.nextPageToken
}
