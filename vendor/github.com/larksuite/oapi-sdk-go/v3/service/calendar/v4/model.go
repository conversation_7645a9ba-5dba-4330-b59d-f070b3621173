// Package calendar code generated by oapi sdk gen
/*
 * MIT License
 *
 * Copyright (c) 2022 Lark Technologies Pte. Ltd.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice, shall be included in all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */

package larkcalendar

import (
	"fmt"

	"context"
	"errors"

	"github.com/larksuite/oapi-sdk-go/v3/event"

	"github.com/larksuite/oapi-sdk-go/v3/core"
)

const (
	EventPermissionsPrivate          = "private"             // 私密
	EventPermissionsShowOnlyFreeBusy = "show_only_free_busy" // 仅展示忙闲信息
	EventPermissionsPublic           = "public"              // 他人可查看日程详情
)

const (
	EventPermissionsPatchCalendarPrivate          = "private"             // 私密
	EventPermissionsPatchCalendarShowOnlyFreeBusy = "show_only_free_busy" // 仅展示忙闲信息
	EventPermissionsPatchCalendarPublic           = "public"              // 他人可查看日程详情
)

const (
	UserIdTypeUserId  = "user_id"  // 以user_id来识别用户id
	UserIdTypeUnionId = "union_id" // 以union_id来识别用户id
	UserIdTypeOpenId  = "open_id"  // 以open_id来识别用户id
)

const (
	CalendarAccessRoleUnkonwn        = "unknown"          // 未知权限
	CalendarAccessRoleFreeBusyReader = "free_busy_reader" // 游客，只能看到忙碌/空闲信息
	CalendarAccessRoleReader         = "reader"           // 订阅者，查看所有日程详情
	CalendarAccessRoleWriter         = "writer"           // 编辑者，创建及修改日程
	CalendarAccessRoleOwner          = "owner"            // 管理员，管理日历及共享设置
)

const (
	UserIdTypeCreateCalendarAclUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypeCreateCalendarAclUnionId = "union_id" // 以union_id来识别用户
	UserIdTypeCreateCalendarAclOpenId  = "open_id"  // 以open_id来识别用户
)

const (
	UserIdTypeListCalendarAclUserId  = "user_id"  // 以user_id来识别用户id
	UserIdTypeListCalendarAclUnionId = "union_id" // 以union_id来识别用户id
	UserIdTypeListCalendarAclOpenId  = "open_id"  // 以open_id来识别用户id
)

const (
	EventVisibilityDefault = "default" // 默认权限，仅向他人显示是否“忙碌”
	EventVisibilityPublic  = "public"  // 公开，显示日程详情
	EventVisibilityPrivate = "private" // 私密，仅自己可见
)

const (
	EventAttendeeAbilityNone            = "none"              // 无法编辑日程、无法邀请其它参与人、无法查看参与人列表
	EventAttendeeAbilityCanSeeOthers    = "can_see_others"    // 无法编辑日程、无法邀请其它参与人、可以查看参与人列表
	EventAttendeeAbilityCanInviteOthers = "can_invite_others" // 无法编辑日程、可以邀请其它参与人、可以查看参与人列表
	EventAttendeeAbilityCanModifyEvent  = "can_modify_event"  // 可以编辑日程、可以邀请其它参与人、可以查看参与人列表
)

const (
	EventFreeBusyStatusBusy = "busy" // 忙碌
	EventFreeBusyStatusFree = "free" // 空闲
)

const (
	NeedNotificationTrue  = true  //
	NeedNotificationFalse = false //

)

const (
	EventVisibilityPatchCalendarEventDefault = "default" // 默认权限，仅向他人显示是否“忙碌”
	EventVisibilityPatchCalendarEventPublic  = "public"  // 公开，显示日程详情
	EventVisibilityPatchCalendarEventPrivate = "private" // 私密，仅自己可见
)

const (
	EventAttendeeAbilityPatchCalendarEventNone            = "none"              // 无法编辑日程、无法邀请其它参与人、无法查看参与人列表
	EventAttendeeAbilityPatchCalendarEventCanSeeOthers    = "can_see_others"    // 无法编辑日程、无法邀请其它参与人、可以查看参与人列表
	EventAttendeeAbilityPatchCalendarEventCanInviteOthers = "can_invite_others" // 无法编辑日程、可以邀请其它参与人、可以查看参与人列表
	EventAttendeeAbilityPatchCalendarEventCanModifyEvent  = "can_modify_event"  // 可以编辑日程、可以邀请其它参与人、可以查看参与人列表
)

const (
	EventFreeBusyStatusPatchCalendarEventBusy = "busy" // 忙碌
	EventFreeBusyStatusPatchCalendarEventFree = "free" // 空闲
)

const (
	UserIdTypeSearchCalendarEventUserId  = "user_id"  // 以user_id来识别用户id
	UserIdTypeSearchCalendarEventUnionId = "union_id" // 以union_id来识别用户id
	UserIdTypeSearchCalendarEventOpenId  = "open_id"  // 以open_id来识别用户id
)

const (
	UserIdTypeBatchDeleteCalendarEventAttendeeUserId  = "user_id"  // 以user_id来识别用户id
	UserIdTypeBatchDeleteCalendarEventAttendeeUnionId = "union_id" // 以union_id来识别用户id
	UserIdTypeBatchDeleteCalendarEventAttendeeOpenId  = "open_id"  // 以open_id来识别用户id
)

const (
	UserIdTypeCreateCalendarEventAttendeeUserId  = "user_id"  // 以user_id来识别用户id
	UserIdTypeCreateCalendarEventAttendeeUnionId = "union_id" // 以union_id来识别用户id
	UserIdTypeCreateCalendarEventAttendeeOpenId  = "open_id"  // 以open_id来识别用户id
)

const (
	UserIdTypeListCalendarEventAttendeeUserId  = "user_id"  // 以user_id来识别用户id
	UserIdTypeListCalendarEventAttendeeUnionId = "union_id" // 以union_id来识别用户id
	UserIdTypeListCalendarEventAttendeeOpenId  = "open_id"  // 以open_id来识别用户id
)

const (
	UserIdTypeListCalendarEventAttendeeChatMemberUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypeListCalendarEventAttendeeChatMemberUnionId = "union_id" // 以union_id来识别用户
	UserIdTypeListCalendarEventAttendeeChatMemberOpenId  = "open_id"  // 以open_id来识别用户
)

const (
	UserIdTypeCreateExchangeBindingUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypeCreateExchangeBindingUnionId = "union_id" // 以union_id来识别用户
	UserIdTypeCreateExchangeBindingOpenId  = "open_id"  // 以open_id来识别用户
)

const (
	UserIdTypeGetExchangeBindingUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypeGetExchangeBindingUnionId = "union_id" // 以union_id来识别用户
	UserIdTypeGetExchangeBindingOpenId  = "open_id"  // 以open_id来识别用户
)

const (
	UserIdTypeListFreebusyUserId  = "user_id"  // 以user_id来识别用户id
	UserIdTypeListFreebusyUnionId = "union_id" // 以union_id来识别用户id
	UserIdTypeListFreebusyOpenId  = "open_id"  // 以open_id来识别用户id
)

const (
	UserIdTypeCreateTimeoffEventUserId  = "user_id"  // 以user_id来识别用户id
	UserIdTypeCreateTimeoffEventUnionId = "union_id" // 以union_id来识别用户id
	UserIdTypeCreateTimeoffEventOpenId  = "open_id"  // 以open_id来识别用户id
)

type AclScope struct {
	Type   *string `json:"type,omitempty"`    // 权限类型，当type为User时，值为open_id/user_id/union_id
	UserId *string `json:"user_id,omitempty"` // 用户ID，参见[用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction)
}

type AclScopeBuilder struct {
	type_      string // 权限类型，当type为User时，值为open_id/user_id/union_id
	typeFlag   bool
	userId     string // 用户ID，参见[用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction)
	userIdFlag bool
}

func NewAclScopeBuilder() *AclScopeBuilder {
	builder := &AclScopeBuilder{}
	return builder
}

// 权限类型，当type为User时，值为open_id/user_id/union_id
//
// 示例值：user
func (builder *AclScopeBuilder) Type(type_ string) *AclScopeBuilder {
	builder.type_ = type_
	builder.typeFlag = true
	return builder
}

// 用户ID，参见[用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction)
//
// 示例值：ou_xxxxxx
func (builder *AclScopeBuilder) UserId(userId string) *AclScopeBuilder {
	builder.userId = userId
	builder.userIdFlag = true
	return builder
}

func (builder *AclScopeBuilder) Build() *AclScope {
	req := &AclScope{}
	if builder.typeFlag {
		req.Type = &builder.type_

	}
	if builder.userIdFlag {
		req.UserId = &builder.userId

	}
	return req
}

type AclScopeEvent struct {
	Type   *string `json:"type,omitempty"`    // 权限类型，当type为User时，值为open_id/user_id/union_id
	UserId *UserId `json:"user_id,omitempty"` // 用户 ID
}

type AclScopeEventBuilder struct {
	type_      string // 权限类型，当type为User时，值为open_id/user_id/union_id
	typeFlag   bool
	userId     *UserId // 用户 ID
	userIdFlag bool
}

func NewAclScopeEventBuilder() *AclScopeEventBuilder {
	builder := &AclScopeEventBuilder{}
	return builder
}

// 权限类型，当type为User时，值为open_id/user_id/union_id
//
// 示例值：user
func (builder *AclScopeEventBuilder) Type(type_ string) *AclScopeEventBuilder {
	builder.type_ = type_
	builder.typeFlag = true
	return builder
}

// 用户 ID
//
// 示例值：
func (builder *AclScopeEventBuilder) UserId(userId *UserId) *AclScopeEventBuilder {
	builder.userId = userId
	builder.userIdFlag = true
	return builder
}

func (builder *AclScopeEventBuilder) Build() *AclScopeEvent {
	req := &AclScopeEvent{}
	if builder.typeFlag {
		req.Type = &builder.type_

	}
	if builder.userIdFlag {
		req.UserId = builder.userId
	}
	return req
}

type AttendeeChatMember struct {
	RsvpStatus  *string `json:"rsvp_status,omitempty"`  // 参与人RSVP状态
	IsOptional  *bool   `json:"is_optional,omitempty"`  // 参与人是否为「可选参加」
	DisplayName *string `json:"display_name,omitempty"` // 参与人名称
	IsOrganizer *bool   `json:"is_organizer,omitempty"` // 参与人是否为日程组织者
	IsExternal  *bool   `json:"is_external,omitempty"`  // 参与人是否为外部参与人
}

type AttendeeChatMemberBuilder struct {
	rsvpStatus      string // 参与人RSVP状态
	rsvpStatusFlag  bool
	isOptional      bool // 参与人是否为「可选参加」
	isOptionalFlag  bool
	displayName     string // 参与人名称
	displayNameFlag bool
	isOrganizer     bool // 参与人是否为日程组织者
	isOrganizerFlag bool
	isExternal      bool // 参与人是否为外部参与人
	isExternalFlag  bool
}

func NewAttendeeChatMemberBuilder() *AttendeeChatMemberBuilder {
	builder := &AttendeeChatMemberBuilder{}
	return builder
}

// 参与人RSVP状态
//
// 示例值：needs_action
func (builder *AttendeeChatMemberBuilder) RsvpStatus(rsvpStatus string) *AttendeeChatMemberBuilder {
	builder.rsvpStatus = rsvpStatus
	builder.rsvpStatusFlag = true
	return builder
}

// 参与人是否为「可选参加」
//
// 示例值：true
func (builder *AttendeeChatMemberBuilder) IsOptional(isOptional bool) *AttendeeChatMemberBuilder {
	builder.isOptional = isOptional
	builder.isOptionalFlag = true
	return builder
}

// 参与人名称
//
// 示例值：Group
func (builder *AttendeeChatMemberBuilder) DisplayName(displayName string) *AttendeeChatMemberBuilder {
	builder.displayName = displayName
	builder.displayNameFlag = true
	return builder
}

// 参与人是否为日程组织者
//
// 示例值：false
func (builder *AttendeeChatMemberBuilder) IsOrganizer(isOrganizer bool) *AttendeeChatMemberBuilder {
	builder.isOrganizer = isOrganizer
	builder.isOrganizerFlag = true
	return builder
}

// 参与人是否为外部参与人
//
// 示例值：false
func (builder *AttendeeChatMemberBuilder) IsExternal(isExternal bool) *AttendeeChatMemberBuilder {
	builder.isExternal = isExternal
	builder.isExternalFlag = true
	return builder
}

func (builder *AttendeeChatMemberBuilder) Build() *AttendeeChatMember {
	req := &AttendeeChatMember{}
	if builder.rsvpStatusFlag {
		req.RsvpStatus = &builder.rsvpStatus

	}
	if builder.isOptionalFlag {
		req.IsOptional = &builder.isOptional

	}
	if builder.displayNameFlag {
		req.DisplayName = &builder.displayName

	}
	if builder.isOrganizerFlag {
		req.IsOrganizer = &builder.isOrganizer

	}
	if builder.isExternalFlag {
		req.IsExternal = &builder.isExternal

	}
	return req
}

type Calendar struct {
	CalendarId   *string `json:"calendar_id,omitempty"`    // 日历ID。参见[日历ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/calendar-v4/calendar/introduction)
	Summary      *string `json:"summary,omitempty"`        // 日历标题
	Description  *string `json:"description,omitempty"`    // 日历描述
	Permissions  *string `json:"permissions,omitempty"`    // 日历公开范围
	Color        *int    `json:"color,omitempty"`          // 日历颜色，颜色RGB值的int32表示。客户端展示时会映射到色板上最接近的一种颜色。仅对当前身份生效
	Type         *string `json:"type,omitempty"`           // 日历类型
	SummaryAlias *string `json:"summary_alias,omitempty"`  // 日历备注名，修改或添加后仅对当前身份生效
	IsDeleted    *bool   `json:"is_deleted,omitempty"`     // 对于当前身份，日历是否已经被标记为删除
	IsThirdParty *bool   `json:"is_third_party,omitempty"` // 当前日历是否是第三方数据；三方日历及日程只支持读，不支持写入
	Role         *string `json:"role,omitempty"`           // 当前身份对于该日历的访问权限
}

type CalendarBuilder struct {
	calendarId       string // 日历ID。参见[日历ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/calendar-v4/calendar/introduction)
	calendarIdFlag   bool
	summary          string // 日历标题
	summaryFlag      bool
	description      string // 日历描述
	descriptionFlag  bool
	permissions      string // 日历公开范围
	permissionsFlag  bool
	color            int // 日历颜色，颜色RGB值的int32表示。客户端展示时会映射到色板上最接近的一种颜色。仅对当前身份生效
	colorFlag        bool
	type_            string // 日历类型
	typeFlag         bool
	summaryAlias     string // 日历备注名，修改或添加后仅对当前身份生效
	summaryAliasFlag bool
	isDeleted        bool // 对于当前身份，日历是否已经被标记为删除
	isDeletedFlag    bool
	isThirdParty     bool // 当前日历是否是第三方数据；三方日历及日程只支持读，不支持写入
	isThirdPartyFlag bool
	role             string // 当前身份对于该日历的访问权限
	roleFlag         bool
}

func NewCalendarBuilder() *CalendarBuilder {
	builder := &CalendarBuilder{}
	return builder
}

// 日历ID。参见[日历ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/calendar-v4/calendar/introduction)
//
// 示例值：<EMAIL>
func (builder *CalendarBuilder) CalendarId(calendarId string) *CalendarBuilder {
	builder.calendarId = calendarId
	builder.calendarIdFlag = true
	return builder
}

// 日历标题
//
// 示例值：测试日历
func (builder *CalendarBuilder) Summary(summary string) *CalendarBuilder {
	builder.summary = summary
	builder.summaryFlag = true
	return builder
}

// 日历描述
//
// 示例值：使用开放接口创建日历
func (builder *CalendarBuilder) Description(description string) *CalendarBuilder {
	builder.description = description
	builder.descriptionFlag = true
	return builder
}

// 日历公开范围
//
// 示例值：private
func (builder *CalendarBuilder) Permissions(permissions string) *CalendarBuilder {
	builder.permissions = permissions
	builder.permissionsFlag = true
	return builder
}

// 日历颜色，颜色RGB值的int32表示。客户端展示时会映射到色板上最接近的一种颜色。仅对当前身份生效
//
// 示例值：-1
func (builder *CalendarBuilder) Color(color int) *CalendarBuilder {
	builder.color = color
	builder.colorFlag = true
	return builder
}

// 日历类型
//
// 示例值：shared
func (builder *CalendarBuilder) Type(type_ string) *CalendarBuilder {
	builder.type_ = type_
	builder.typeFlag = true
	return builder
}

// 日历备注名，修改或添加后仅对当前身份生效
//
// 示例值：日历备注名
func (builder *CalendarBuilder) SummaryAlias(summaryAlias string) *CalendarBuilder {
	builder.summaryAlias = summaryAlias
	builder.summaryAliasFlag = true
	return builder
}

// 对于当前身份，日历是否已经被标记为删除
//
// 示例值：false
func (builder *CalendarBuilder) IsDeleted(isDeleted bool) *CalendarBuilder {
	builder.isDeleted = isDeleted
	builder.isDeletedFlag = true
	return builder
}

// 当前日历是否是第三方数据；三方日历及日程只支持读，不支持写入
//
// 示例值：false
func (builder *CalendarBuilder) IsThirdParty(isThirdParty bool) *CalendarBuilder {
	builder.isThirdParty = isThirdParty
	builder.isThirdPartyFlag = true
	return builder
}

// 当前身份对于该日历的访问权限
//
// 示例值：owner
func (builder *CalendarBuilder) Role(role string) *CalendarBuilder {
	builder.role = role
	builder.roleFlag = true
	return builder
}

func (builder *CalendarBuilder) Build() *Calendar {
	req := &Calendar{}
	if builder.calendarIdFlag {
		req.CalendarId = &builder.calendarId

	}
	if builder.summaryFlag {
		req.Summary = &builder.summary

	}
	if builder.descriptionFlag {
		req.Description = &builder.description

	}
	if builder.permissionsFlag {
		req.Permissions = &builder.permissions

	}
	if builder.colorFlag {
		req.Color = &builder.color

	}
	if builder.typeFlag {
		req.Type = &builder.type_

	}
	if builder.summaryAliasFlag {
		req.SummaryAlias = &builder.summaryAlias

	}
	if builder.isDeletedFlag {
		req.IsDeleted = &builder.isDeleted

	}
	if builder.isThirdPartyFlag {
		req.IsThirdParty = &builder.isThirdParty

	}
	if builder.roleFlag {
		req.Role = &builder.role

	}
	return req
}

type CalendarAcl struct {
	AclId *string   `json:"acl_id,omitempty"` // acl资源ID。参见[ACL ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/calendar-v4/calendar-acl/introduction)
	Role  *string   `json:"role,omitempty"`   // 对日历的访问权限
	Scope *AclScope `json:"scope,omitempty"`  // 权限范围
}

type CalendarAclBuilder struct {
	aclId     string // acl资源ID。参见[ACL ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/calendar-v4/calendar-acl/introduction)
	aclIdFlag bool
	role      string // 对日历的访问权限
	roleFlag  bool
	scope     *AclScope // 权限范围
	scopeFlag bool
}

func NewCalendarAclBuilder() *CalendarAclBuilder {
	builder := &CalendarAclBuilder{}
	return builder
}

// acl资源ID。参见[ACL ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/calendar-v4/calendar-acl/introduction)
//
// 示例值：user_xxxxxx
func (builder *CalendarAclBuilder) AclId(aclId string) *CalendarAclBuilder {
	builder.aclId = aclId
	builder.aclIdFlag = true
	return builder
}

// 对日历的访问权限
//
// 示例值：writer
func (builder *CalendarAclBuilder) Role(role string) *CalendarAclBuilder {
	builder.role = role
	builder.roleFlag = true
	return builder
}

// 权限范围
//
// 示例值：
func (builder *CalendarAclBuilder) Scope(scope *AclScope) *CalendarAclBuilder {
	builder.scope = scope
	builder.scopeFlag = true
	return builder
}

func (builder *CalendarAclBuilder) Build() *CalendarAcl {
	req := &CalendarAcl{}
	if builder.aclIdFlag {
		req.AclId = &builder.aclId

	}
	if builder.roleFlag {
		req.Role = &builder.role

	}
	if builder.scopeFlag {
		req.Scope = builder.scope
	}
	return req
}

type CalendarAclEvent struct {
	AclId      *string        `json:"acl_id,omitempty"`       // acl资源ID
	Role       *string        `json:"role,omitempty"`         // 对日历的访问权限
	Scope      *AclScopeEvent `json:"scope,omitempty"`        // 权限范围
	UserIdList []*UserId      `json:"user_id_list,omitempty"` // 需要推送事件的用户列表
}

type CalendarAclEventBuilder struct {
	aclId          string // acl资源ID
	aclIdFlag      bool
	role           string // 对日历的访问权限
	roleFlag       bool
	scope          *AclScopeEvent // 权限范围
	scopeFlag      bool
	userIdList     []*UserId // 需要推送事件的用户列表
	userIdListFlag bool
}

func NewCalendarAclEventBuilder() *CalendarAclEventBuilder {
	builder := &CalendarAclEventBuilder{}
	return builder
}

// acl资源ID
//
// 示例值：user_xxxxx
func (builder *CalendarAclEventBuilder) AclId(aclId string) *CalendarAclEventBuilder {
	builder.aclId = aclId
	builder.aclIdFlag = true
	return builder
}

// 对日历的访问权限
//
// 示例值：unknown
func (builder *CalendarAclEventBuilder) Role(role string) *CalendarAclEventBuilder {
	builder.role = role
	builder.roleFlag = true
	return builder
}

// 权限范围
//
// 示例值：
func (builder *CalendarAclEventBuilder) Scope(scope *AclScopeEvent) *CalendarAclEventBuilder {
	builder.scope = scope
	builder.scopeFlag = true
	return builder
}

// 需要推送事件的用户列表
//
// 示例值：
func (builder *CalendarAclEventBuilder) UserIdList(userIdList []*UserId) *CalendarAclEventBuilder {
	builder.userIdList = userIdList
	builder.userIdListFlag = true
	return builder
}

func (builder *CalendarAclEventBuilder) Build() *CalendarAclEvent {
	req := &CalendarAclEvent{}
	if builder.aclIdFlag {
		req.AclId = &builder.aclId

	}
	if builder.roleFlag {
		req.Role = &builder.role

	}
	if builder.scopeFlag {
		req.Scope = builder.scope
	}
	if builder.userIdListFlag {
		req.UserIdList = builder.userIdList
	}
	return req
}

type CalendarAttendeeResourceCustomization struct {
	IndexKey     *string                `json:"index_key,omitempty"`     // 每个配置的唯一ID
	InputContent *string                `json:"input_content,omitempty"` // 当type类型为填空时，该参数需要填入
	Options      []*CustomizationOption `json:"options,omitempty"`       // 每个配置的选项
}

type CalendarAttendeeResourceCustomizationBuilder struct {
	indexKey         string // 每个配置的唯一ID
	indexKeyFlag     bool
	inputContent     string // 当type类型为填空时，该参数需要填入
	inputContentFlag bool
	options          []*CustomizationOption // 每个配置的选项
	optionsFlag      bool
}

func NewCalendarAttendeeResourceCustomizationBuilder() *CalendarAttendeeResourceCustomizationBuilder {
	builder := &CalendarAttendeeResourceCustomizationBuilder{}
	return builder
}

// 每个配置的唯一ID
//
// 示例值：16281481596100
func (builder *CalendarAttendeeResourceCustomizationBuilder) IndexKey(indexKey string) *CalendarAttendeeResourceCustomizationBuilder {
	builder.indexKey = indexKey
	builder.indexKeyFlag = true
	return builder
}

// 当type类型为填空时，该参数需要填入
//
// 示例值：xxx
func (builder *CalendarAttendeeResourceCustomizationBuilder) InputContent(inputContent string) *CalendarAttendeeResourceCustomizationBuilder {
	builder.inputContent = inputContent
	builder.inputContentFlag = true
	return builder
}

// 每个配置的选项
//
// 示例值：
func (builder *CalendarAttendeeResourceCustomizationBuilder) Options(options []*CustomizationOption) *CalendarAttendeeResourceCustomizationBuilder {
	builder.options = options
	builder.optionsFlag = true
	return builder
}

func (builder *CalendarAttendeeResourceCustomizationBuilder) Build() *CalendarAttendeeResourceCustomization {
	req := &CalendarAttendeeResourceCustomization{}
	if builder.indexKeyFlag {
		req.IndexKey = &builder.indexKey

	}
	if builder.inputContentFlag {
		req.InputContent = &builder.inputContent

	}
	if builder.optionsFlag {
		req.Options = builder.options
	}
	return req
}

type CalendarEvent struct {
	EventId             *string        `json:"event_id,omitempty"`              // 日程ID。参见[日程ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/calendar-v4/calendar-event/introduction)
	OrganizerCalendarId *string        `json:"organizer_calendar_id,omitempty"` // 日程组织者日历ID。参见[日历ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/calendar-v4/calendar/introduction)
	Summary             *string        `json:"summary,omitempty"`               // 日程标题
	Description         *string        `json:"description,omitempty"`           // 日程描述；目前不支持编辑富文本描述，如果日程描述通过客户端编辑过，更新描述会导致富文本格式丢失
	NeedNotification    *bool          `json:"need_notification,omitempty"`     // 更新日程是否给日程参与人发送bot通知，默认为true
	StartTime           *TimeInfo      `json:"start_time,omitempty"`            // 日程开始时间
	EndTime             *TimeInfo      `json:"end_time,omitempty"`              // 日程结束时间
	Vchat               *Vchat         `json:"vchat,omitempty"`                 // 视频会议信息。
	Visibility          *string        `json:"visibility,omitempty"`            // 日程公开范围，新建日程默认为Default；仅新建日程时对所有参与人生效，之后修改该属性仅对当前身份生效
	AttendeeAbility     *string        `json:"attendee_ability,omitempty"`      // 参与人权限
	FreeBusyStatus      *string        `json:"free_busy_status,omitempty"`      // 日程占用的忙闲状态，新建日程默认为Busy；仅新建日程时对所有参与人生效，之后修改该属性仅对当前身份生效
	Location            *EventLocation `json:"location,omitempty"`              // 日程地点
	Color               *int           `json:"color,omitempty"`                 // 日程颜色，颜色RGB值的int32表示。仅对当前身份生效；客户端展示时会映射到色板上最接近的一种颜色；值为0或-1时默认跟随日历颜色。
	Reminders           []*Reminder    `json:"reminders,omitempty"`             // 日程提醒列表
	Recurrence          *string        `json:"recurrence,omitempty"`            // 重复日程的重复性规则；参考[rfc5545](https://datatracker.ietf.org/doc/html/rfc5545#section-3.3.10)；;- 不支持COUNT和UNTIL同时出现；;- 预定会议室重复日程长度不得超过两年。
	Status              *string        `json:"status,omitempty"`                // 日程状态
	IsException         *bool          `json:"is_exception,omitempty"`          // 日程是否是一个重复日程的例外日程
	RecurringEventId    *string        `json:"recurring_event_id,omitempty"`    // 例外日程的原重复日程的event_id
	CreateTime          *string        `json:"create_time,omitempty"`           // 日程的创建时间（秒级时间戳）
	Schemas             []*Schema      `json:"schemas,omitempty"`               // 日程自定义信息；控制日程详情页的ui展示。
}

type CalendarEventBuilder struct {
	eventId                 string // 日程ID。参见[日程ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/calendar-v4/calendar-event/introduction)
	eventIdFlag             bool
	organizerCalendarId     string // 日程组织者日历ID。参见[日历ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/calendar-v4/calendar/introduction)
	organizerCalendarIdFlag bool
	summary                 string // 日程标题
	summaryFlag             bool
	description             string // 日程描述；目前不支持编辑富文本描述，如果日程描述通过客户端编辑过，更新描述会导致富文本格式丢失
	descriptionFlag         bool
	needNotification        bool // 更新日程是否给日程参与人发送bot通知，默认为true
	needNotificationFlag    bool
	startTime               *TimeInfo // 日程开始时间
	startTimeFlag           bool
	endTime                 *TimeInfo // 日程结束时间
	endTimeFlag             bool
	vchat                   *Vchat // 视频会议信息。
	vchatFlag               bool
	visibility              string // 日程公开范围，新建日程默认为Default；仅新建日程时对所有参与人生效，之后修改该属性仅对当前身份生效
	visibilityFlag          bool
	attendeeAbility         string // 参与人权限
	attendeeAbilityFlag     bool
	freeBusyStatus          string // 日程占用的忙闲状态，新建日程默认为Busy；仅新建日程时对所有参与人生效，之后修改该属性仅对当前身份生效
	freeBusyStatusFlag      bool
	location                *EventLocation // 日程地点
	locationFlag            bool
	color                   int // 日程颜色，颜色RGB值的int32表示。仅对当前身份生效；客户端展示时会映射到色板上最接近的一种颜色；值为0或-1时默认跟随日历颜色。
	colorFlag               bool
	reminders               []*Reminder // 日程提醒列表
	remindersFlag           bool
	recurrence              string // 重复日程的重复性规则；参考[rfc5545](https://datatracker.ietf.org/doc/html/rfc5545#section-3.3.10)；;- 不支持COUNT和UNTIL同时出现；;- 预定会议室重复日程长度不得超过两年。
	recurrenceFlag          bool
	status                  string // 日程状态
	statusFlag              bool
	isException             bool // 日程是否是一个重复日程的例外日程
	isExceptionFlag         bool
	recurringEventId        string // 例外日程的原重复日程的event_id
	recurringEventIdFlag    bool
	createTime              string // 日程的创建时间（秒级时间戳）
	createTimeFlag          bool
	schemas                 []*Schema // 日程自定义信息；控制日程详情页的ui展示。
	schemasFlag             bool
}

func NewCalendarEventBuilder() *CalendarEventBuilder {
	builder := &CalendarEventBuilder{}
	return builder
}

// 日程ID。参见[日程ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/calendar-v4/calendar-event/introduction)
//
// 示例值：00592a0e-7edf-4678-bc9d-1b77383ef08e_0
func (builder *CalendarEventBuilder) EventId(eventId string) *CalendarEventBuilder {
	builder.eventId = eventId
	builder.eventIdFlag = true
	return builder
}

// 日程组织者日历ID。参见[日历ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/calendar-v4/calendar/introduction)
//
// 示例值：<EMAIL>
func (builder *CalendarEventBuilder) OrganizerCalendarId(organizerCalendarId string) *CalendarEventBuilder {
	builder.organizerCalendarId = organizerCalendarId
	builder.organizerCalendarIdFlag = true
	return builder
}

// 日程标题
//
// 示例值：日程标题
func (builder *CalendarEventBuilder) Summary(summary string) *CalendarEventBuilder {
	builder.summary = summary
	builder.summaryFlag = true
	return builder
}

// 日程描述；目前不支持编辑富文本描述，如果日程描述通过客户端编辑过，更新描述会导致富文本格式丢失
//
// 示例值：日程描述
func (builder *CalendarEventBuilder) Description(description string) *CalendarEventBuilder {
	builder.description = description
	builder.descriptionFlag = true
	return builder
}

// 更新日程是否给日程参与人发送bot通知，默认为true
//
// 示例值：false
func (builder *CalendarEventBuilder) NeedNotification(needNotification bool) *CalendarEventBuilder {
	builder.needNotification = needNotification
	builder.needNotificationFlag = true
	return builder
}

// 日程开始时间
//
// 示例值：
func (builder *CalendarEventBuilder) StartTime(startTime *TimeInfo) *CalendarEventBuilder {
	builder.startTime = startTime
	builder.startTimeFlag = true
	return builder
}

// 日程结束时间
//
// 示例值：
func (builder *CalendarEventBuilder) EndTime(endTime *TimeInfo) *CalendarEventBuilder {
	builder.endTime = endTime
	builder.endTimeFlag = true
	return builder
}

// 视频会议信息。
//
// 示例值：
func (builder *CalendarEventBuilder) Vchat(vchat *Vchat) *CalendarEventBuilder {
	builder.vchat = vchat
	builder.vchatFlag = true
	return builder
}

// 日程公开范围，新建日程默认为Default；仅新建日程时对所有参与人生效，之后修改该属性仅对当前身份生效
//
// 示例值：default
func (builder *CalendarEventBuilder) Visibility(visibility string) *CalendarEventBuilder {
	builder.visibility = visibility
	builder.visibilityFlag = true
	return builder
}

// 参与人权限
//
// 示例值：can_see_others
func (builder *CalendarEventBuilder) AttendeeAbility(attendeeAbility string) *CalendarEventBuilder {
	builder.attendeeAbility = attendeeAbility
	builder.attendeeAbilityFlag = true
	return builder
}

// 日程占用的忙闲状态，新建日程默认为Busy；仅新建日程时对所有参与人生效，之后修改该属性仅对当前身份生效
//
// 示例值：busy
func (builder *CalendarEventBuilder) FreeBusyStatus(freeBusyStatus string) *CalendarEventBuilder {
	builder.freeBusyStatus = freeBusyStatus
	builder.freeBusyStatusFlag = true
	return builder
}

// 日程地点
//
// 示例值：
func (builder *CalendarEventBuilder) Location(location *EventLocation) *CalendarEventBuilder {
	builder.location = location
	builder.locationFlag = true
	return builder
}

// 日程颜色，颜色RGB值的int32表示。仅对当前身份生效；客户端展示时会映射到色板上最接近的一种颜色；值为0或-1时默认跟随日历颜色。
//
// 示例值：-1
func (builder *CalendarEventBuilder) Color(color int) *CalendarEventBuilder {
	builder.color = color
	builder.colorFlag = true
	return builder
}

// 日程提醒列表
//
// 示例值：
func (builder *CalendarEventBuilder) Reminders(reminders []*Reminder) *CalendarEventBuilder {
	builder.reminders = reminders
	builder.remindersFlag = true
	return builder
}

// 重复日程的重复性规则；参考[rfc5545](https://datatracker.ietf.org/doc/html/rfc5545#section-3.3.10)；;- 不支持COUNT和UNTIL同时出现；;- 预定会议室重复日程长度不得超过两年。
//
// 示例值：FREQ=DAILY;INTERVAL=1
func (builder *CalendarEventBuilder) Recurrence(recurrence string) *CalendarEventBuilder {
	builder.recurrence = recurrence
	builder.recurrenceFlag = true
	return builder
}

// 日程状态
//
// 示例值：confirmed
func (builder *CalendarEventBuilder) Status(status string) *CalendarEventBuilder {
	builder.status = status
	builder.statusFlag = true
	return builder
}

// 日程是否是一个重复日程的例外日程
//
// 示例值：false
func (builder *CalendarEventBuilder) IsException(isException bool) *CalendarEventBuilder {
	builder.isException = isException
	builder.isExceptionFlag = true
	return builder
}

// 例外日程的原重复日程的event_id
//
// 示例值：1cd45aaa-fa70-4195-80b7-c93b2e208f45
func (builder *CalendarEventBuilder) RecurringEventId(recurringEventId string) *CalendarEventBuilder {
	builder.recurringEventId = recurringEventId
	builder.recurringEventIdFlag = true
	return builder
}

// 日程的创建时间（秒级时间戳）
//
// 示例值：1602504000
func (builder *CalendarEventBuilder) CreateTime(createTime string) *CalendarEventBuilder {
	builder.createTime = createTime
	builder.createTimeFlag = true
	return builder
}

// 日程自定义信息；控制日程详情页的ui展示。
//
// 示例值：
func (builder *CalendarEventBuilder) Schemas(schemas []*Schema) *CalendarEventBuilder {
	builder.schemas = schemas
	builder.schemasFlag = true
	return builder
}

func (builder *CalendarEventBuilder) Build() *CalendarEvent {
	req := &CalendarEvent{}
	if builder.eventIdFlag {
		req.EventId = &builder.eventId

	}
	if builder.organizerCalendarIdFlag {
		req.OrganizerCalendarId = &builder.organizerCalendarId

	}
	if builder.summaryFlag {
		req.Summary = &builder.summary

	}
	if builder.descriptionFlag {
		req.Description = &builder.description

	}
	if builder.needNotificationFlag {
		req.NeedNotification = &builder.needNotification

	}
	if builder.startTimeFlag {
		req.StartTime = builder.startTime
	}
	if builder.endTimeFlag {
		req.EndTime = builder.endTime
	}
	if builder.vchatFlag {
		req.Vchat = builder.vchat
	}
	if builder.visibilityFlag {
		req.Visibility = &builder.visibility

	}
	if builder.attendeeAbilityFlag {
		req.AttendeeAbility = &builder.attendeeAbility

	}
	if builder.freeBusyStatusFlag {
		req.FreeBusyStatus = &builder.freeBusyStatus

	}
	if builder.locationFlag {
		req.Location = builder.location
	}
	if builder.colorFlag {
		req.Color = &builder.color

	}
	if builder.remindersFlag {
		req.Reminders = builder.reminders
	}
	if builder.recurrenceFlag {
		req.Recurrence = &builder.recurrence

	}
	if builder.statusFlag {
		req.Status = &builder.status

	}
	if builder.isExceptionFlag {
		req.IsException = &builder.isException

	}
	if builder.recurringEventIdFlag {
		req.RecurringEventId = &builder.recurringEventId

	}
	if builder.createTimeFlag {
		req.CreateTime = &builder.createTime

	}
	if builder.schemasFlag {
		req.Schemas = builder.schemas
	}
	return req
}

type CalendarEventAttendee struct {
	Type                  *string                                  `json:"type,omitempty"`                   // 参与人类型
	AttendeeId            *string                                  `json:"attendee_id,omitempty"`            // 参与人ID。参见[参与人ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/calendar-v4/calendar-event-attendee/introduction#4998889c)
	RsvpStatus            *string                                  `json:"rsvp_status,omitempty"`            // 参与人RSVP状态
	IsOptional            *bool                                    `json:"is_optional,omitempty"`            // 参与人是否为「可选参加」，无法编辑群参与人的此字段
	IsOrganizer           *bool                                    `json:"is_organizer,omitempty"`           // 参与人是否为日程组织者
	IsExternal            *bool                                    `json:"is_external,omitempty"`            // 参与人是否为外部参与人；外部参与人不支持编辑
	DisplayName           *string                                  `json:"display_name,omitempty"`           // 参与人名称
	ChatMembers           []*AttendeeChatMember                    `json:"chat_members,omitempty"`           // 群中的群成员，当type为Chat时有效；群成员不支持编辑
	UserId                *string                                  `json:"user_id,omitempty"`                // 参与人的用户id，依赖于user_id_type返回对应的取值，当is_external为true时，此字段只会返回open_id或者union_id，参见[用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction)
	ChatId                *string                                  `json:"chat_id,omitempty"`                // chat类型参与人的群组chat_id，参见[群ID 说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-id-description)
	RoomId                *string                                  `json:"room_id,omitempty"`                // resource类型参与人的会议室room_id
	ThirdPartyEmail       *string                                  `json:"third_party_email,omitempty"`      // third_party类型参与人的邮箱
	OperateId             *string                                  `json:"operate_id,omitempty"`             // 如果日程是使用应用身份创建的，在添加会议室的时候，用来指定会议室的联系人，在会议室视图展示。参见[用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction)
	ResourceCustomization []*CalendarAttendeeResourceCustomization `json:"resource_customization,omitempty"` // 会议室的个性化配置
}

type CalendarEventAttendeeBuilder struct {
	type_                     string // 参与人类型
	typeFlag                  bool
	attendeeId                string // 参与人ID。参见[参与人ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/calendar-v4/calendar-event-attendee/introduction#4998889c)
	attendeeIdFlag            bool
	rsvpStatus                string // 参与人RSVP状态
	rsvpStatusFlag            bool
	isOptional                bool // 参与人是否为「可选参加」，无法编辑群参与人的此字段
	isOptionalFlag            bool
	isOrganizer               bool // 参与人是否为日程组织者
	isOrganizerFlag           bool
	isExternal                bool // 参与人是否为外部参与人；外部参与人不支持编辑
	isExternalFlag            bool
	displayName               string // 参与人名称
	displayNameFlag           bool
	chatMembers               []*AttendeeChatMember // 群中的群成员，当type为Chat时有效；群成员不支持编辑
	chatMembersFlag           bool
	userId                    string // 参与人的用户id，依赖于user_id_type返回对应的取值，当is_external为true时，此字段只会返回open_id或者union_id，参见[用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction)
	userIdFlag                bool
	chatId                    string // chat类型参与人的群组chat_id，参见[群ID 说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-id-description)
	chatIdFlag                bool
	roomId                    string // resource类型参与人的会议室room_id
	roomIdFlag                bool
	thirdPartyEmail           string // third_party类型参与人的邮箱
	thirdPartyEmailFlag       bool
	operateId                 string // 如果日程是使用应用身份创建的，在添加会议室的时候，用来指定会议室的联系人，在会议室视图展示。参见[用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction)
	operateIdFlag             bool
	resourceCustomization     []*CalendarAttendeeResourceCustomization // 会议室的个性化配置
	resourceCustomizationFlag bool
}

func NewCalendarEventAttendeeBuilder() *CalendarEventAttendeeBuilder {
	builder := &CalendarEventAttendeeBuilder{}
	return builder
}

// 参与人类型
//
// 示例值：user
func (builder *CalendarEventAttendeeBuilder) Type(type_ string) *CalendarEventAttendeeBuilder {
	builder.type_ = type_
	builder.typeFlag = true
	return builder
}

// 参与人ID。参见[参与人ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/calendar-v4/calendar-event-attendee/introduction#4998889c)
//
// 示例值：user_xxxxxx
func (builder *CalendarEventAttendeeBuilder) AttendeeId(attendeeId string) *CalendarEventAttendeeBuilder {
	builder.attendeeId = attendeeId
	builder.attendeeIdFlag = true
	return builder
}

// 参与人RSVP状态
//
// 示例值：needs_action
func (builder *CalendarEventAttendeeBuilder) RsvpStatus(rsvpStatus string) *CalendarEventAttendeeBuilder {
	builder.rsvpStatus = rsvpStatus
	builder.rsvpStatusFlag = true
	return builder
}

// 参与人是否为「可选参加」，无法编辑群参与人的此字段
//
// 示例值：true
func (builder *CalendarEventAttendeeBuilder) IsOptional(isOptional bool) *CalendarEventAttendeeBuilder {
	builder.isOptional = isOptional
	builder.isOptionalFlag = true
	return builder
}

// 参与人是否为日程组织者
//
// 示例值：true
func (builder *CalendarEventAttendeeBuilder) IsOrganizer(isOrganizer bool) *CalendarEventAttendeeBuilder {
	builder.isOrganizer = isOrganizer
	builder.isOrganizerFlag = true
	return builder
}

// 参与人是否为外部参与人；外部参与人不支持编辑
//
// 示例值：false
func (builder *CalendarEventAttendeeBuilder) IsExternal(isExternal bool) *CalendarEventAttendeeBuilder {
	builder.isExternal = isExternal
	builder.isExternalFlag = true
	return builder
}

// 参与人名称
//
// 示例值：Zhang San
func (builder *CalendarEventAttendeeBuilder) DisplayName(displayName string) *CalendarEventAttendeeBuilder {
	builder.displayName = displayName
	builder.displayNameFlag = true
	return builder
}

// 群中的群成员，当type为Chat时有效；群成员不支持编辑
//
// 示例值：
func (builder *CalendarEventAttendeeBuilder) ChatMembers(chatMembers []*AttendeeChatMember) *CalendarEventAttendeeBuilder {
	builder.chatMembers = chatMembers
	builder.chatMembersFlag = true
	return builder
}

// 参与人的用户id，依赖于user_id_type返回对应的取值，当is_external为true时，此字段只会返回open_id或者union_id，参见[用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction)
//
// 示例值：ou_xxxxxxxx
func (builder *CalendarEventAttendeeBuilder) UserId(userId string) *CalendarEventAttendeeBuilder {
	builder.userId = userId
	builder.userIdFlag = true
	return builder
}

// chat类型参与人的群组chat_id，参见[群ID 说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-id-description)
//
// 示例值：oc_xxxxxxxxx
func (builder *CalendarEventAttendeeBuilder) ChatId(chatId string) *CalendarEventAttendeeBuilder {
	builder.chatId = chatId
	builder.chatIdFlag = true
	return builder
}

// resource类型参与人的会议室room_id
//
// 示例值：omm_xxxxxxxx
func (builder *CalendarEventAttendeeBuilder) RoomId(roomId string) *CalendarEventAttendeeBuilder {
	builder.roomId = roomId
	builder.roomIdFlag = true
	return builder
}

// third_party类型参与人的邮箱
//
// 示例值：<EMAIL>
func (builder *CalendarEventAttendeeBuilder) ThirdPartyEmail(thirdPartyEmail string) *CalendarEventAttendeeBuilder {
	builder.thirdPartyEmail = thirdPartyEmail
	builder.thirdPartyEmailFlag = true
	return builder
}

// 如果日程是使用应用身份创建的，在添加会议室的时候，用来指定会议室的联系人，在会议室视图展示。参见[用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction)
//
// 示例值：ou_xxxxxxxx
func (builder *CalendarEventAttendeeBuilder) OperateId(operateId string) *CalendarEventAttendeeBuilder {
	builder.operateId = operateId
	builder.operateIdFlag = true
	return builder
}

// 会议室的个性化配置
//
// 示例值：
func (builder *CalendarEventAttendeeBuilder) ResourceCustomization(resourceCustomization []*CalendarAttendeeResourceCustomization) *CalendarEventAttendeeBuilder {
	builder.resourceCustomization = resourceCustomization
	builder.resourceCustomizationFlag = true
	return builder
}

func (builder *CalendarEventAttendeeBuilder) Build() *CalendarEventAttendee {
	req := &CalendarEventAttendee{}
	if builder.typeFlag {
		req.Type = &builder.type_

	}
	if builder.attendeeIdFlag {
		req.AttendeeId = &builder.attendeeId

	}
	if builder.rsvpStatusFlag {
		req.RsvpStatus = &builder.rsvpStatus

	}
	if builder.isOptionalFlag {
		req.IsOptional = &builder.isOptional

	}
	if builder.isOrganizerFlag {
		req.IsOrganizer = &builder.isOrganizer

	}
	if builder.isExternalFlag {
		req.IsExternal = &builder.isExternal

	}
	if builder.displayNameFlag {
		req.DisplayName = &builder.displayName

	}
	if builder.chatMembersFlag {
		req.ChatMembers = builder.chatMembers
	}
	if builder.userIdFlag {
		req.UserId = &builder.userId

	}
	if builder.chatIdFlag {
		req.ChatId = &builder.chatId

	}
	if builder.roomIdFlag {
		req.RoomId = &builder.roomId

	}
	if builder.thirdPartyEmailFlag {
		req.ThirdPartyEmail = &builder.thirdPartyEmail

	}
	if builder.operateIdFlag {
		req.OperateId = &builder.operateId

	}
	if builder.resourceCustomizationFlag {
		req.ResourceCustomization = builder.resourceCustomization
	}
	return req
}

type CalendarEventAttendeeChatMember struct {
	RsvpStatus  *string `json:"rsvp_status,omitempty"`  // 参与人RSVP状态
	IsOptional  *bool   `json:"is_optional,omitempty"`  // 参与人是否为「可选参加」
	DisplayName *string `json:"display_name,omitempty"` // 参与人名称
	OpenId      *string `json:"open_id,omitempty"`      // 参与人open_id，参见[用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction);;**示例值**："ou_xxxxxxxx"
	IsOrganizer *bool   `json:"is_organizer,omitempty"` // 参与人是否为日程组织者
	IsExternal  *bool   `json:"is_external,omitempty"`  // 参与人是否为外部参与人
}

type CalendarEventAttendeeChatMemberBuilder struct {
	rsvpStatus      string // 参与人RSVP状态
	rsvpStatusFlag  bool
	isOptional      bool // 参与人是否为「可选参加」
	isOptionalFlag  bool
	displayName     string // 参与人名称
	displayNameFlag bool
	openId          string // 参与人open_id，参见[用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction);;**示例值**："ou_xxxxxxxx"
	openIdFlag      bool
	isOrganizer     bool // 参与人是否为日程组织者
	isOrganizerFlag bool
	isExternal      bool // 参与人是否为外部参与人
	isExternalFlag  bool
}

func NewCalendarEventAttendeeChatMemberBuilder() *CalendarEventAttendeeChatMemberBuilder {
	builder := &CalendarEventAttendeeChatMemberBuilder{}
	return builder
}

// 参与人RSVP状态
//
// 示例值：needs_action
func (builder *CalendarEventAttendeeChatMemberBuilder) RsvpStatus(rsvpStatus string) *CalendarEventAttendeeChatMemberBuilder {
	builder.rsvpStatus = rsvpStatus
	builder.rsvpStatusFlag = true
	return builder
}

// 参与人是否为「可选参加」
//
// 示例值：true
func (builder *CalendarEventAttendeeChatMemberBuilder) IsOptional(isOptional bool) *CalendarEventAttendeeChatMemberBuilder {
	builder.isOptional = isOptional
	builder.isOptionalFlag = true
	return builder
}

// 参与人名称
//
// 示例值：Zhang San
func (builder *CalendarEventAttendeeChatMemberBuilder) DisplayName(displayName string) *CalendarEventAttendeeChatMemberBuilder {
	builder.displayName = displayName
	builder.displayNameFlag = true
	return builder
}

// 参与人open_id，参见[用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction);;**示例值**："ou_xxxxxxxx"
//
// 示例值：ou_143669c5a53647f00f6c80a0253aa68b
func (builder *CalendarEventAttendeeChatMemberBuilder) OpenId(openId string) *CalendarEventAttendeeChatMemberBuilder {
	builder.openId = openId
	builder.openIdFlag = true
	return builder
}

// 参与人是否为日程组织者
//
// 示例值：true
func (builder *CalendarEventAttendeeChatMemberBuilder) IsOrganizer(isOrganizer bool) *CalendarEventAttendeeChatMemberBuilder {
	builder.isOrganizer = isOrganizer
	builder.isOrganizerFlag = true
	return builder
}

// 参与人是否为外部参与人
//
// 示例值：false
func (builder *CalendarEventAttendeeChatMemberBuilder) IsExternal(isExternal bool) *CalendarEventAttendeeChatMemberBuilder {
	builder.isExternal = isExternal
	builder.isExternalFlag = true
	return builder
}

func (builder *CalendarEventAttendeeChatMemberBuilder) Build() *CalendarEventAttendeeChatMember {
	req := &CalendarEventAttendeeChatMember{}
	if builder.rsvpStatusFlag {
		req.RsvpStatus = &builder.rsvpStatus

	}
	if builder.isOptionalFlag {
		req.IsOptional = &builder.isOptional

	}
	if builder.displayNameFlag {
		req.DisplayName = &builder.displayName

	}
	if builder.openIdFlag {
		req.OpenId = &builder.openId

	}
	if builder.isOrganizerFlag {
		req.IsOrganizer = &builder.isOrganizer

	}
	if builder.isExternalFlag {
		req.IsExternal = &builder.isExternal

	}
	return req
}

type CalendarEventAttendeeId struct {
	Type            *string `json:"type,omitempty"`              // 参与人类型，仅当新建参与人时可设置类型
	AttendeeId      *string `json:"attendee_id,omitempty"`       // 参与人ID
	UserId          *string `json:"user_id,omitempty"`           // 参与人的用户id，依赖于user_id_type返回对应的取值，当is_external为true时，此字段只会返回open_id或者union_id
	ChatId          *string `json:"chat_id,omitempty"`           // chat类型参与人的群组chat_id
	RoomId          *string `json:"room_id,omitempty"`           // resource类型参与人的会议室room_id
	ThirdPartyEmail *string `json:"third_party_email,omitempty"` // third_party类型参与人的邮箱
}

type CalendarEventAttendeeIdBuilder struct {
	type_               string // 参与人类型，仅当新建参与人时可设置类型
	typeFlag            bool
	attendeeId          string // 参与人ID
	attendeeIdFlag      bool
	userId              string // 参与人的用户id，依赖于user_id_type返回对应的取值，当is_external为true时，此字段只会返回open_id或者union_id
	userIdFlag          bool
	chatId              string // chat类型参与人的群组chat_id
	chatIdFlag          bool
	roomId              string // resource类型参与人的会议室room_id
	roomIdFlag          bool
	thirdPartyEmail     string // third_party类型参与人的邮箱
	thirdPartyEmailFlag bool
}

func NewCalendarEventAttendeeIdBuilder() *CalendarEventAttendeeIdBuilder {
	builder := &CalendarEventAttendeeIdBuilder{}
	return builder
}

// 参与人类型，仅当新建参与人时可设置类型
//
// 示例值：user
func (builder *CalendarEventAttendeeIdBuilder) Type(type_ string) *CalendarEventAttendeeIdBuilder {
	builder.type_ = type_
	builder.typeFlag = true
	return builder
}

// 参与人ID
//
// 示例值：
func (builder *CalendarEventAttendeeIdBuilder) AttendeeId(attendeeId string) *CalendarEventAttendeeIdBuilder {
	builder.attendeeId = attendeeId
	builder.attendeeIdFlag = true
	return builder
}

// 参与人的用户id，依赖于user_id_type返回对应的取值，当is_external为true时，此字段只会返回open_id或者union_id
//
// 示例值：ou_xxxxxxxx
func (builder *CalendarEventAttendeeIdBuilder) UserId(userId string) *CalendarEventAttendeeIdBuilder {
	builder.userId = userId
	builder.userIdFlag = true
	return builder
}

// chat类型参与人的群组chat_id
//
// 示例值：oc_xxxxxxxxx
func (builder *CalendarEventAttendeeIdBuilder) ChatId(chatId string) *CalendarEventAttendeeIdBuilder {
	builder.chatId = chatId
	builder.chatIdFlag = true
	return builder
}

// resource类型参与人的会议室room_id
//
// 示例值：omm_xxxxxxxx
func (builder *CalendarEventAttendeeIdBuilder) RoomId(roomId string) *CalendarEventAttendeeIdBuilder {
	builder.roomId = roomId
	builder.roomIdFlag = true
	return builder
}

// third_party类型参与人的邮箱
//
// 示例值：<EMAIL>
func (builder *CalendarEventAttendeeIdBuilder) ThirdPartyEmail(thirdPartyEmail string) *CalendarEventAttendeeIdBuilder {
	builder.thirdPartyEmail = thirdPartyEmail
	builder.thirdPartyEmailFlag = true
	return builder
}

func (builder *CalendarEventAttendeeIdBuilder) Build() *CalendarEventAttendeeId {
	req := &CalendarEventAttendeeId{}
	if builder.typeFlag {
		req.Type = &builder.type_

	}
	if builder.attendeeIdFlag {
		req.AttendeeId = &builder.attendeeId

	}
	if builder.userIdFlag {
		req.UserId = &builder.userId

	}
	if builder.chatIdFlag {
		req.ChatId = &builder.chatId

	}
	if builder.roomIdFlag {
		req.RoomId = &builder.roomId

	}
	if builder.thirdPartyEmailFlag {
		req.ThirdPartyEmail = &builder.thirdPartyEmail

	}
	return req
}

type CalendarEventAttendeeResp struct {
	Type                  *string                                  `json:"type,omitempty"`                   // 参与人类型，仅当新建参与人时可设置类型。
	AttendeeId            *string                                  `json:"attendee_id,omitempty"`            // 参与人ID
	RsvpStatus            *string                                  `json:"rsvp_status,omitempty"`            // 参与人RSVP状态
	IsOptional            *bool                                    `json:"is_optional,omitempty"`            // 参与人是否为「可选参加」，无法编辑群参与人的此字段
	IsOrganizer           *bool                                    `json:"is_organizer,omitempty"`           // 参与人是否为日程组织者
	IsExternal            *bool                                    `json:"is_external,omitempty"`            // 参与人是否为外部参与人；外部参与人不支持编辑
	DisplayName           *string                                  `json:"display_name,omitempty"`           // 参与人名称
	ChatMembers           []*AttendeeChatMember                    `json:"chat_members,omitempty"`           // 群中的群成员，当type为Chat时有效；群成员不支持编辑
	UserId                *string                                  `json:"user_id,omitempty"`                // 参与人的用户id，依赖于user_id_type返回对应的取值，当is_external为true时，此字段只会返回open_id或者union_id
	ChatId                *string                                  `json:"chat_id,omitempty"`                // chat类型参与人的群组chat_id
	RoomId                *string                                  `json:"room_id,omitempty"`                // resource类型参与人的会议室room_id
	ThirdPartyEmail       *string                                  `json:"third_party_email,omitempty"`      // third_party类型参与人的邮箱
	OperateId             *string                                  `json:"operate_id,omitempty"`             // bot身份操作时，为预定的会议室指定实际预定人
	ResourceCustomization []*CalendarAttendeeResourceCustomization `json:"resource_customization,omitempty"` // 会议室的个性化配置
}

type CalendarEventAttendeeRespBuilder struct {
	type_                     string // 参与人类型，仅当新建参与人时可设置类型。
	typeFlag                  bool
	attendeeId                string // 参与人ID
	attendeeIdFlag            bool
	rsvpStatus                string // 参与人RSVP状态
	rsvpStatusFlag            bool
	isOptional                bool // 参与人是否为「可选参加」，无法编辑群参与人的此字段
	isOptionalFlag            bool
	isOrganizer               bool // 参与人是否为日程组织者
	isOrganizerFlag           bool
	isExternal                bool // 参与人是否为外部参与人；外部参与人不支持编辑
	isExternalFlag            bool
	displayName               string // 参与人名称
	displayNameFlag           bool
	chatMembers               []*AttendeeChatMember // 群中的群成员，当type为Chat时有效；群成员不支持编辑
	chatMembersFlag           bool
	userId                    string // 参与人的用户id，依赖于user_id_type返回对应的取值，当is_external为true时，此字段只会返回open_id或者union_id
	userIdFlag                bool
	chatId                    string // chat类型参与人的群组chat_id
	chatIdFlag                bool
	roomId                    string // resource类型参与人的会议室room_id
	roomIdFlag                bool
	thirdPartyEmail           string // third_party类型参与人的邮箱
	thirdPartyEmailFlag       bool
	operateId                 string // bot身份操作时，为预定的会议室指定实际预定人
	operateIdFlag             bool
	resourceCustomization     []*CalendarAttendeeResourceCustomization // 会议室的个性化配置
	resourceCustomizationFlag bool
}

func NewCalendarEventAttendeeRespBuilder() *CalendarEventAttendeeRespBuilder {
	builder := &CalendarEventAttendeeRespBuilder{}
	return builder
}

// 参与人类型，仅当新建参与人时可设置类型。
//
// 示例值：
func (builder *CalendarEventAttendeeRespBuilder) Type(type_ string) *CalendarEventAttendeeRespBuilder {
	builder.type_ = type_
	builder.typeFlag = true
	return builder
}

// 参与人ID
//
// 示例值：
func (builder *CalendarEventAttendeeRespBuilder) AttendeeId(attendeeId string) *CalendarEventAttendeeRespBuilder {
	builder.attendeeId = attendeeId
	builder.attendeeIdFlag = true
	return builder
}

// 参与人RSVP状态
//
// 示例值：
func (builder *CalendarEventAttendeeRespBuilder) RsvpStatus(rsvpStatus string) *CalendarEventAttendeeRespBuilder {
	builder.rsvpStatus = rsvpStatus
	builder.rsvpStatusFlag = true
	return builder
}

// 参与人是否为「可选参加」，无法编辑群参与人的此字段
//
// 示例值：
func (builder *CalendarEventAttendeeRespBuilder) IsOptional(isOptional bool) *CalendarEventAttendeeRespBuilder {
	builder.isOptional = isOptional
	builder.isOptionalFlag = true
	return builder
}

// 参与人是否为日程组织者
//
// 示例值：
func (builder *CalendarEventAttendeeRespBuilder) IsOrganizer(isOrganizer bool) *CalendarEventAttendeeRespBuilder {
	builder.isOrganizer = isOrganizer
	builder.isOrganizerFlag = true
	return builder
}

// 参与人是否为外部参与人；外部参与人不支持编辑
//
// 示例值：
func (builder *CalendarEventAttendeeRespBuilder) IsExternal(isExternal bool) *CalendarEventAttendeeRespBuilder {
	builder.isExternal = isExternal
	builder.isExternalFlag = true
	return builder
}

// 参与人名称
//
// 示例值：
func (builder *CalendarEventAttendeeRespBuilder) DisplayName(displayName string) *CalendarEventAttendeeRespBuilder {
	builder.displayName = displayName
	builder.displayNameFlag = true
	return builder
}

// 群中的群成员，当type为Chat时有效；群成员不支持编辑
//
// 示例值：
func (builder *CalendarEventAttendeeRespBuilder) ChatMembers(chatMembers []*AttendeeChatMember) *CalendarEventAttendeeRespBuilder {
	builder.chatMembers = chatMembers
	builder.chatMembersFlag = true
	return builder
}

// 参与人的用户id，依赖于user_id_type返回对应的取值，当is_external为true时，此字段只会返回open_id或者union_id
//
// 示例值：
func (builder *CalendarEventAttendeeRespBuilder) UserId(userId string) *CalendarEventAttendeeRespBuilder {
	builder.userId = userId
	builder.userIdFlag = true
	return builder
}

// chat类型参与人的群组chat_id
//
// 示例值：
func (builder *CalendarEventAttendeeRespBuilder) ChatId(chatId string) *CalendarEventAttendeeRespBuilder {
	builder.chatId = chatId
	builder.chatIdFlag = true
	return builder
}

// resource类型参与人的会议室room_id
//
// 示例值：
func (builder *CalendarEventAttendeeRespBuilder) RoomId(roomId string) *CalendarEventAttendeeRespBuilder {
	builder.roomId = roomId
	builder.roomIdFlag = true
	return builder
}

// third_party类型参与人的邮箱
//
// 示例值：
func (builder *CalendarEventAttendeeRespBuilder) ThirdPartyEmail(thirdPartyEmail string) *CalendarEventAttendeeRespBuilder {
	builder.thirdPartyEmail = thirdPartyEmail
	builder.thirdPartyEmailFlag = true
	return builder
}

// bot身份操作时，为预定的会议室指定实际预定人
//
// 示例值：
func (builder *CalendarEventAttendeeRespBuilder) OperateId(operateId string) *CalendarEventAttendeeRespBuilder {
	builder.operateId = operateId
	builder.operateIdFlag = true
	return builder
}

// 会议室的个性化配置
//
// 示例值：
func (builder *CalendarEventAttendeeRespBuilder) ResourceCustomization(resourceCustomization []*CalendarAttendeeResourceCustomization) *CalendarEventAttendeeRespBuilder {
	builder.resourceCustomization = resourceCustomization
	builder.resourceCustomizationFlag = true
	return builder
}

func (builder *CalendarEventAttendeeRespBuilder) Build() *CalendarEventAttendeeResp {
	req := &CalendarEventAttendeeResp{}
	if builder.typeFlag {
		req.Type = &builder.type_

	}
	if builder.attendeeIdFlag {
		req.AttendeeId = &builder.attendeeId

	}
	if builder.rsvpStatusFlag {
		req.RsvpStatus = &builder.rsvpStatus

	}
	if builder.isOptionalFlag {
		req.IsOptional = &builder.isOptional

	}
	if builder.isOrganizerFlag {
		req.IsOrganizer = &builder.isOrganizer

	}
	if builder.isExternalFlag {
		req.IsExternal = &builder.isExternal

	}
	if builder.displayNameFlag {
		req.DisplayName = &builder.displayName

	}
	if builder.chatMembersFlag {
		req.ChatMembers = builder.chatMembers
	}
	if builder.userIdFlag {
		req.UserId = &builder.userId

	}
	if builder.chatIdFlag {
		req.ChatId = &builder.chatId

	}
	if builder.roomIdFlag {
		req.RoomId = &builder.roomId

	}
	if builder.thirdPartyEmailFlag {
		req.ThirdPartyEmail = &builder.thirdPartyEmail

	}
	if builder.operateIdFlag {
		req.OperateId = &builder.operateId

	}
	if builder.resourceCustomizationFlag {
		req.ResourceCustomization = builder.resourceCustomization
	}
	return req
}

type CalendarEventResp struct {
	EventId             *string        `json:"event_id,omitempty"`              // 日程ID
	OrganizerCalendarId *string        `json:"organizer_calendar_id,omitempty"` // 日程组织者日历ID
	Summary             *string        `json:"summary,omitempty"`               // 日程标题
	Description         *string        `json:"description,omitempty"`           // 日程描述
	NeedNotification    *bool          `json:"need_notification,omitempty"`     // 是否发送通知消息
	StartTime           *TimeInfo      `json:"start_time,omitempty"`            // 日程开始时间
	EndTime             *TimeInfo      `json:"end_time,omitempty"`              // 日程结束时间
	Vchat               *Vchat         `json:"vchat,omitempty"`                 // 视频会议信息，仅当日程至少有一位attendee时生效
	Visibility          *string        `json:"visibility,omitempty"`            // 日程公开范围，新建日程默认为Default；仅新建日程时对所有参与人生效，之后修改该属性仅对当前身份生效
	AttendeeAbility     *string        `json:"attendee_ability,omitempty"`      // 参与人权限
	FreeBusyStatus      *string        `json:"free_busy_status,omitempty"`      // 日程占用的忙闲状态，新建日程默认为Busy；仅新建日程时对所有参与人生效，之后修改该属性仅对当前身份生效
	Location            *EventLocation `json:"location,omitempty"`              // 日程地点
	Color               *int           `json:"color,omitempty"`                 // 日程颜色，颜色RGB值的int32表示。仅对当前身份生效；客户端展示时会映射到色板上最接近的一种颜色；值为0或-1时默认跟随日历颜色。
	Reminders           []*Reminder    `json:"reminders,omitempty"`             // 日程提醒列表
	Recurrence          *string        `json:"recurrence,omitempty"`            // 重复日程的重复性规则
	Status              *string        `json:"status,omitempty"`                // 日程状态
	IsException         *bool          `json:"is_exception,omitempty"`          // 日程是否是一个重复日程的例外日程
	RecurringEventId    *string        `json:"recurring_event_id,omitempty"`    // 例外日程的原重复日程的event_id
	CreateTime          *string        `json:"create_time,omitempty"`           // 日程的创建时间戳
	Schemas             []*Schema      `json:"schemas,omitempty"`               // 日程自定义信息
}

type CalendarEventRespBuilder struct {
	eventId                 string // 日程ID
	eventIdFlag             bool
	organizerCalendarId     string // 日程组织者日历ID
	organizerCalendarIdFlag bool
	summary                 string // 日程标题
	summaryFlag             bool
	description             string // 日程描述
	descriptionFlag         bool
	needNotification        bool // 是否发送通知消息
	needNotificationFlag    bool
	startTime               *TimeInfo // 日程开始时间
	startTimeFlag           bool
	endTime                 *TimeInfo // 日程结束时间
	endTimeFlag             bool
	vchat                   *Vchat // 视频会议信息，仅当日程至少有一位attendee时生效
	vchatFlag               bool
	visibility              string // 日程公开范围，新建日程默认为Default；仅新建日程时对所有参与人生效，之后修改该属性仅对当前身份生效
	visibilityFlag          bool
	attendeeAbility         string // 参与人权限
	attendeeAbilityFlag     bool
	freeBusyStatus          string // 日程占用的忙闲状态，新建日程默认为Busy；仅新建日程时对所有参与人生效，之后修改该属性仅对当前身份生效
	freeBusyStatusFlag      bool
	location                *EventLocation // 日程地点
	locationFlag            bool
	color                   int // 日程颜色，颜色RGB值的int32表示。仅对当前身份生效；客户端展示时会映射到色板上最接近的一种颜色；值为0或-1时默认跟随日历颜色。
	colorFlag               bool
	reminders               []*Reminder // 日程提醒列表
	remindersFlag           bool
	recurrence              string // 重复日程的重复性规则
	recurrenceFlag          bool
	status                  string // 日程状态
	statusFlag              bool
	isException             bool // 日程是否是一个重复日程的例外日程
	isExceptionFlag         bool
	recurringEventId        string // 例外日程的原重复日程的event_id
	recurringEventIdFlag    bool
	createTime              string // 日程的创建时间戳
	createTimeFlag          bool
	schemas                 []*Schema // 日程自定义信息
	schemasFlag             bool
}

func NewCalendarEventRespBuilder() *CalendarEventRespBuilder {
	builder := &CalendarEventRespBuilder{}
	return builder
}

// 日程ID
//
// 示例值：
func (builder *CalendarEventRespBuilder) EventId(eventId string) *CalendarEventRespBuilder {
	builder.eventId = eventId
	builder.eventIdFlag = true
	return builder
}

// 日程组织者日历ID
//
// 示例值：
func (builder *CalendarEventRespBuilder) OrganizerCalendarId(organizerCalendarId string) *CalendarEventRespBuilder {
	builder.organizerCalendarId = organizerCalendarId
	builder.organizerCalendarIdFlag = true
	return builder
}

// 日程标题
//
// 示例值：
func (builder *CalendarEventRespBuilder) Summary(summary string) *CalendarEventRespBuilder {
	builder.summary = summary
	builder.summaryFlag = true
	return builder
}

// 日程描述
//
// 示例值：
func (builder *CalendarEventRespBuilder) Description(description string) *CalendarEventRespBuilder {
	builder.description = description
	builder.descriptionFlag = true
	return builder
}

// 是否发送通知消息
//
// 示例值：
func (builder *CalendarEventRespBuilder) NeedNotification(needNotification bool) *CalendarEventRespBuilder {
	builder.needNotification = needNotification
	builder.needNotificationFlag = true
	return builder
}

// 日程开始时间
//
// 示例值：
func (builder *CalendarEventRespBuilder) StartTime(startTime *TimeInfo) *CalendarEventRespBuilder {
	builder.startTime = startTime
	builder.startTimeFlag = true
	return builder
}

// 日程结束时间
//
// 示例值：
func (builder *CalendarEventRespBuilder) EndTime(endTime *TimeInfo) *CalendarEventRespBuilder {
	builder.endTime = endTime
	builder.endTimeFlag = true
	return builder
}

// 视频会议信息，仅当日程至少有一位attendee时生效
//
// 示例值：
func (builder *CalendarEventRespBuilder) Vchat(vchat *Vchat) *CalendarEventRespBuilder {
	builder.vchat = vchat
	builder.vchatFlag = true
	return builder
}

// 日程公开范围，新建日程默认为Default；仅新建日程时对所有参与人生效，之后修改该属性仅对当前身份生效
//
// 示例值：
func (builder *CalendarEventRespBuilder) Visibility(visibility string) *CalendarEventRespBuilder {
	builder.visibility = visibility
	builder.visibilityFlag = true
	return builder
}

// 参与人权限
//
// 示例值：
func (builder *CalendarEventRespBuilder) AttendeeAbility(attendeeAbility string) *CalendarEventRespBuilder {
	builder.attendeeAbility = attendeeAbility
	builder.attendeeAbilityFlag = true
	return builder
}

// 日程占用的忙闲状态，新建日程默认为Busy；仅新建日程时对所有参与人生效，之后修改该属性仅对当前身份生效
//
// 示例值：
func (builder *CalendarEventRespBuilder) FreeBusyStatus(freeBusyStatus string) *CalendarEventRespBuilder {
	builder.freeBusyStatus = freeBusyStatus
	builder.freeBusyStatusFlag = true
	return builder
}

// 日程地点
//
// 示例值：
func (builder *CalendarEventRespBuilder) Location(location *EventLocation) *CalendarEventRespBuilder {
	builder.location = location
	builder.locationFlag = true
	return builder
}

// 日程颜色，颜色RGB值的int32表示。仅对当前身份生效；客户端展示时会映射到色板上最接近的一种颜色；值为0或-1时默认跟随日历颜色。
//
// 示例值：
func (builder *CalendarEventRespBuilder) Color(color int) *CalendarEventRespBuilder {
	builder.color = color
	builder.colorFlag = true
	return builder
}

// 日程提醒列表
//
// 示例值：
func (builder *CalendarEventRespBuilder) Reminders(reminders []*Reminder) *CalendarEventRespBuilder {
	builder.reminders = reminders
	builder.remindersFlag = true
	return builder
}

// 重复日程的重复性规则
//
// 示例值：
func (builder *CalendarEventRespBuilder) Recurrence(recurrence string) *CalendarEventRespBuilder {
	builder.recurrence = recurrence
	builder.recurrenceFlag = true
	return builder
}

// 日程状态
//
// 示例值：
func (builder *CalendarEventRespBuilder) Status(status string) *CalendarEventRespBuilder {
	builder.status = status
	builder.statusFlag = true
	return builder
}

// 日程是否是一个重复日程的例外日程
//
// 示例值：
func (builder *CalendarEventRespBuilder) IsException(isException bool) *CalendarEventRespBuilder {
	builder.isException = isException
	builder.isExceptionFlag = true
	return builder
}

// 例外日程的原重复日程的event_id
//
// 示例值：
func (builder *CalendarEventRespBuilder) RecurringEventId(recurringEventId string) *CalendarEventRespBuilder {
	builder.recurringEventId = recurringEventId
	builder.recurringEventIdFlag = true
	return builder
}

// 日程的创建时间戳
//
// 示例值：
func (builder *CalendarEventRespBuilder) CreateTime(createTime string) *CalendarEventRespBuilder {
	builder.createTime = createTime
	builder.createTimeFlag = true
	return builder
}

// 日程自定义信息
//
// 示例值：
func (builder *CalendarEventRespBuilder) Schemas(schemas []*Schema) *CalendarEventRespBuilder {
	builder.schemas = schemas
	builder.schemasFlag = true
	return builder
}

func (builder *CalendarEventRespBuilder) Build() *CalendarEventResp {
	req := &CalendarEventResp{}
	if builder.eventIdFlag {
		req.EventId = &builder.eventId

	}
	if builder.organizerCalendarIdFlag {
		req.OrganizerCalendarId = &builder.organizerCalendarId

	}
	if builder.summaryFlag {
		req.Summary = &builder.summary

	}
	if builder.descriptionFlag {
		req.Description = &builder.description

	}
	if builder.needNotificationFlag {
		req.NeedNotification = &builder.needNotification

	}
	if builder.startTimeFlag {
		req.StartTime = builder.startTime
	}
	if builder.endTimeFlag {
		req.EndTime = builder.endTime
	}
	if builder.vchatFlag {
		req.Vchat = builder.vchat
	}
	if builder.visibilityFlag {
		req.Visibility = &builder.visibility

	}
	if builder.attendeeAbilityFlag {
		req.AttendeeAbility = &builder.attendeeAbility

	}
	if builder.freeBusyStatusFlag {
		req.FreeBusyStatus = &builder.freeBusyStatus

	}
	if builder.locationFlag {
		req.Location = builder.location
	}
	if builder.colorFlag {
		req.Color = &builder.color

	}
	if builder.remindersFlag {
		req.Reminders = builder.reminders
	}
	if builder.recurrenceFlag {
		req.Recurrence = &builder.recurrence

	}
	if builder.statusFlag {
		req.Status = &builder.status

	}
	if builder.isExceptionFlag {
		req.IsException = &builder.isException

	}
	if builder.recurringEventIdFlag {
		req.RecurringEventId = &builder.recurringEventId

	}
	if builder.createTimeFlag {
		req.CreateTime = &builder.createTime

	}
	if builder.schemasFlag {
		req.Schemas = builder.schemas
	}
	return req
}

type CalendarFreebusy struct {
	StartTime  *string `json:"start_time,omitempty"`  // 忙闲信息开始时间，RFC3339 date_time格式
	EndTime    *string `json:"end_time,omitempty"`    // 忙闲信息结束时间，RFC3339 date_time格式
	CalendarId *string `json:"calendar_id,omitempty"` // 日历id
}

type CalendarFreebusyBuilder struct {
	startTime      string // 忙闲信息开始时间，RFC3339 date_time格式
	startTimeFlag  bool
	endTime        string // 忙闲信息结束时间，RFC3339 date_time格式
	endTimeFlag    bool
	calendarId     string // 日历id
	calendarIdFlag bool
}

func NewCalendarFreebusyBuilder() *CalendarFreebusyBuilder {
	builder := &CalendarFreebusyBuilder{}
	return builder
}

// 忙闲信息开始时间，RFC3339 date_time格式
//
// 示例值：
func (builder *CalendarFreebusyBuilder) StartTime(startTime string) *CalendarFreebusyBuilder {
	builder.startTime = startTime
	builder.startTimeFlag = true
	return builder
}

// 忙闲信息结束时间，RFC3339 date_time格式
//
// 示例值：
func (builder *CalendarFreebusyBuilder) EndTime(endTime string) *CalendarFreebusyBuilder {
	builder.endTime = endTime
	builder.endTimeFlag = true
	return builder
}

// 日历id
//
// 示例值：
func (builder *CalendarFreebusyBuilder) CalendarId(calendarId string) *CalendarFreebusyBuilder {
	builder.calendarId = calendarId
	builder.calendarIdFlag = true
	return builder
}

func (builder *CalendarFreebusyBuilder) Build() *CalendarFreebusy {
	req := &CalendarFreebusy{}
	if builder.startTimeFlag {
		req.StartTime = &builder.startTime

	}
	if builder.endTimeFlag {
		req.EndTime = &builder.endTime

	}
	if builder.calendarIdFlag {
		req.CalendarId = &builder.calendarId

	}
	return req
}

type CalendarFreebusyError struct {
	CalendarId *string `json:"calendar_id,omitempty"` // 日历id
	ErrorMsg   *string `json:"error_msg,omitempty"`   // 错误信息
}

type CalendarFreebusyErrorBuilder struct {
	calendarId     string // 日历id
	calendarIdFlag bool
	errorMsg       string // 错误信息
	errorMsgFlag   bool
}

func NewCalendarFreebusyErrorBuilder() *CalendarFreebusyErrorBuilder {
	builder := &CalendarFreebusyErrorBuilder{}
	return builder
}

// 日历id
//
// 示例值：
func (builder *CalendarFreebusyErrorBuilder) CalendarId(calendarId string) *CalendarFreebusyErrorBuilder {
	builder.calendarId = calendarId
	builder.calendarIdFlag = true
	return builder
}

// 错误信息
//
// 示例值：
func (builder *CalendarFreebusyErrorBuilder) ErrorMsg(errorMsg string) *CalendarFreebusyErrorBuilder {
	builder.errorMsg = errorMsg
	builder.errorMsgFlag = true
	return builder
}

func (builder *CalendarFreebusyErrorBuilder) Build() *CalendarFreebusyError {
	req := &CalendarFreebusyError{}
	if builder.calendarIdFlag {
		req.CalendarId = &builder.calendarId

	}
	if builder.errorMsgFlag {
		req.ErrorMsg = &builder.errorMsg

	}
	return req
}

type CustomizationOption struct {
	OptionKey     *string `json:"option_key,omitempty"`     // 每个选项的唯一ID
	OthersContent *string `json:"others_content,omitempty"` // 当type类型为其它选项时，该参数需要填入
}

type CustomizationOptionBuilder struct {
	optionKey         string // 每个选项的唯一ID
	optionKeyFlag     bool
	othersContent     string // 当type类型为其它选项时，该参数需要填入
	othersContentFlag bool
}

func NewCustomizationOptionBuilder() *CustomizationOptionBuilder {
	builder := &CustomizationOptionBuilder{}
	return builder
}

// 每个选项的唯一ID
//
// 示例值：16281481596185
func (builder *CustomizationOptionBuilder) OptionKey(optionKey string) *CustomizationOptionBuilder {
	builder.optionKey = optionKey
	builder.optionKeyFlag = true
	return builder
}

// 当type类型为其它选项时，该参数需要填入
//
// 示例值：xxx
func (builder *CustomizationOptionBuilder) OthersContent(othersContent string) *CustomizationOptionBuilder {
	builder.othersContent = othersContent
	builder.othersContentFlag = true
	return builder
}

func (builder *CustomizationOptionBuilder) Build() *CustomizationOption {
	req := &CustomizationOption{}
	if builder.optionKeyFlag {
		req.OptionKey = &builder.optionKey

	}
	if builder.othersContentFlag {
		req.OthersContent = &builder.othersContent

	}
	return req
}

type EventLocation struct {
	Name      *string  `json:"name,omitempty"`      // 地点名称
	Address   *string  `json:"address,omitempty"`   // 地点地址
	Latitude  *float64 `json:"latitude,omitempty"`  // 地点坐标纬度信息，对于国内的地点，采用GCJ-02标准，海外地点采用WGS84标准
	Longitude *float64 `json:"longitude,omitempty"` // 地点坐标经度信息，对于国内的地点，采用GCJ-02标准，海外地点采用WGS84标准
}

type EventLocationBuilder struct {
	name          string // 地点名称
	nameFlag      bool
	address       string // 地点地址
	addressFlag   bool
	latitude      float64 // 地点坐标纬度信息，对于国内的地点，采用GCJ-02标准，海外地点采用WGS84标准
	latitudeFlag  bool
	longitude     float64 // 地点坐标经度信息，对于国内的地点，采用GCJ-02标准，海外地点采用WGS84标准
	longitudeFlag bool
}

func NewEventLocationBuilder() *EventLocationBuilder {
	builder := &EventLocationBuilder{}
	return builder
}

// 地点名称
//
// 示例值：地点名称
func (builder *EventLocationBuilder) Name(name string) *EventLocationBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

// 地点地址
//
// 示例值：地点地址
func (builder *EventLocationBuilder) Address(address string) *EventLocationBuilder {
	builder.address = address
	builder.addressFlag = true
	return builder
}

// 地点坐标纬度信息，对于国内的地点，采用GCJ-02标准，海外地点采用WGS84标准
//
// 示例值：1.100000023841858
func (builder *EventLocationBuilder) Latitude(latitude float64) *EventLocationBuilder {
	builder.latitude = latitude
	builder.latitudeFlag = true
	return builder
}

// 地点坐标经度信息，对于国内的地点，采用GCJ-02标准，海外地点采用WGS84标准
//
// 示例值：2.200000047683716
func (builder *EventLocationBuilder) Longitude(longitude float64) *EventLocationBuilder {
	builder.longitude = longitude
	builder.longitudeFlag = true
	return builder
}

func (builder *EventLocationBuilder) Build() *EventLocation {
	req := &EventLocation{}
	if builder.nameFlag {
		req.Name = &builder.name

	}
	if builder.addressFlag {
		req.Address = &builder.address

	}
	if builder.latitudeFlag {
		req.Latitude = &builder.latitude

	}
	if builder.longitudeFlag {
		req.Longitude = &builder.longitude

	}
	return req
}

type EventSearchFilter struct {
	StartTime *TimeInfo `json:"start_time,omitempty"` // 搜索过滤项，日程搜索区间的开始时间，被搜索日程的事件必须与搜索区间有交集
	EndTime   *TimeInfo `json:"end_time,omitempty"`   // 搜索过滤项，日程搜索区间的结束时间，被搜索日程的事件必须与搜索区间有交集
	UserIds   []string  `json:"user_ids,omitempty"`   // 搜索过滤项，参与人的用户ID列表，被搜索日程中必须包含至少一个其中的参与人。参见[用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction)
	RoomIds   []string  `json:"room_ids,omitempty"`   // 搜索过滤项，会议室ID列表，被搜索日程中必须包含至少一个其中的会议室
	ChatIds   []string  `json:"chat_ids,omitempty"`   // 搜索过滤项，群ID列表，被搜索日程的参与人中必须包含至少一个其中的群。参见[群ID 说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-id-description)
}

type EventSearchFilterBuilder struct {
	startTime     *TimeInfo // 搜索过滤项，日程搜索区间的开始时间，被搜索日程的事件必须与搜索区间有交集
	startTimeFlag bool
	endTime       *TimeInfo // 搜索过滤项，日程搜索区间的结束时间，被搜索日程的事件必须与搜索区间有交集
	endTimeFlag   bool
	userIds       []string // 搜索过滤项，参与人的用户ID列表，被搜索日程中必须包含至少一个其中的参与人。参见[用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction)
	userIdsFlag   bool
	roomIds       []string // 搜索过滤项，会议室ID列表，被搜索日程中必须包含至少一个其中的会议室
	roomIdsFlag   bool
	chatIds       []string // 搜索过滤项，群ID列表，被搜索日程的参与人中必须包含至少一个其中的群。参见[群ID 说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-id-description)
	chatIdsFlag   bool
}

func NewEventSearchFilterBuilder() *EventSearchFilterBuilder {
	builder := &EventSearchFilterBuilder{}
	return builder
}

// 搜索过滤项，日程搜索区间的开始时间，被搜索日程的事件必须与搜索区间有交集
//
// 示例值：
func (builder *EventSearchFilterBuilder) StartTime(startTime *TimeInfo) *EventSearchFilterBuilder {
	builder.startTime = startTime
	builder.startTimeFlag = true
	return builder
}

// 搜索过滤项，日程搜索区间的结束时间，被搜索日程的事件必须与搜索区间有交集
//
// 示例值：
func (builder *EventSearchFilterBuilder) EndTime(endTime *TimeInfo) *EventSearchFilterBuilder {
	builder.endTime = endTime
	builder.endTimeFlag = true
	return builder
}

// 搜索过滤项，参与人的用户ID列表，被搜索日程中必须包含至少一个其中的参与人。参见[用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction)
//
// 示例值：xxxxx
func (builder *EventSearchFilterBuilder) UserIds(userIds []string) *EventSearchFilterBuilder {
	builder.userIds = userIds
	builder.userIdsFlag = true
	return builder
}

// 搜索过滤项，会议室ID列表，被搜索日程中必须包含至少一个其中的会议室
//
// 示例值：xxxxx
func (builder *EventSearchFilterBuilder) RoomIds(roomIds []string) *EventSearchFilterBuilder {
	builder.roomIds = roomIds
	builder.roomIdsFlag = true
	return builder
}

// 搜索过滤项，群ID列表，被搜索日程的参与人中必须包含至少一个其中的群。参见[群ID 说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-id-description)
//
// 示例值：xxxxx
func (builder *EventSearchFilterBuilder) ChatIds(chatIds []string) *EventSearchFilterBuilder {
	builder.chatIds = chatIds
	builder.chatIdsFlag = true
	return builder
}

func (builder *EventSearchFilterBuilder) Build() *EventSearchFilter {
	req := &EventSearchFilter{}
	if builder.startTimeFlag {
		req.StartTime = builder.startTime
	}
	if builder.endTimeFlag {
		req.EndTime = builder.endTime
	}
	if builder.userIdsFlag {
		req.UserIds = builder.userIds
	}
	if builder.roomIdsFlag {
		req.RoomIds = builder.roomIds
	}
	if builder.chatIdsFlag {
		req.ChatIds = builder.chatIds
	}
	return req
}

type EventTime struct {
	TimeStamp *string `json:"time_stamp,omitempty"` //
}

type EventTimeBuilder struct {
	timeStamp     string //
	timeStampFlag bool
}

func NewEventTimeBuilder() *EventTimeBuilder {
	builder := &EventTimeBuilder{}
	return builder
}

//
//
// 示例值：
func (builder *EventTimeBuilder) TimeStamp(timeStamp string) *EventTimeBuilder {
	builder.timeStamp = timeStamp
	builder.timeStampFlag = true
	return builder
}

func (builder *EventTimeBuilder) Build() *EventTime {
	req := &EventTime{}
	if builder.timeStampFlag {
		req.TimeStamp = &builder.timeStamp

	}
	return req
}

type ExchangeBinding struct {
	AdminAccount      *string `json:"admin_account,omitempty"`       // admin账户
	ExchangeAccount   *string `json:"exchange_account,omitempty"`    // 用户绑定的exchange账户
	UserId            *string `json:"user_id,omitempty"`             // exchange账户绑定user唯一标识id，参见[用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction)
	Status            *string `json:"status,omitempty"`              // exchange账户同步状态
	ExchangeBindingId *string `json:"exchange_binding_id,omitempty"` // exchange绑定唯一标识id。参见[exchange绑定ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/calendar-v4/exchange_binding/introduction#12533d5e)
}

type ExchangeBindingBuilder struct {
	adminAccount          string // admin账户
	adminAccountFlag      bool
	exchangeAccount       string // 用户绑定的exchange账户
	exchangeAccountFlag   bool
	userId                string // exchange账户绑定user唯一标识id，参见[用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction)
	userIdFlag            bool
	status                string // exchange账户同步状态
	statusFlag            bool
	exchangeBindingId     string // exchange绑定唯一标识id。参见[exchange绑定ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/calendar-v4/exchange_binding/introduction#12533d5e)
	exchangeBindingIdFlag bool
}

func NewExchangeBindingBuilder() *ExchangeBindingBuilder {
	builder := &ExchangeBindingBuilder{}
	return builder
}

// admin账户
//
// 示例值：<EMAIL>
func (builder *ExchangeBindingBuilder) AdminAccount(adminAccount string) *ExchangeBindingBuilder {
	builder.adminAccount = adminAccount
	builder.adminAccountFlag = true
	return builder
}

// 用户绑定的exchange账户
//
// 示例值：<EMAIL>
func (builder *ExchangeBindingBuilder) ExchangeAccount(exchangeAccount string) *ExchangeBindingBuilder {
	builder.exchangeAccount = exchangeAccount
	builder.exchangeAccountFlag = true
	return builder
}

// exchange账户绑定user唯一标识id，参见[用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction)
//
// 示例值：ou_xxxxxxxxxxxxxxxxxx
func (builder *ExchangeBindingBuilder) UserId(userId string) *ExchangeBindingBuilder {
	builder.userId = userId
	builder.userIdFlag = true
	return builder
}

// exchange账户同步状态
//
// 示例值：doing
func (builder *ExchangeBindingBuilder) Status(status string) *ExchangeBindingBuilder {
	builder.status = status
	builder.statusFlag = true
	return builder
}

// exchange绑定唯一标识id。参见[exchange绑定ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/calendar-v4/exchange_binding/introduction#12533d5e)
//
// 示例值：****************************************************************************************
func (builder *ExchangeBindingBuilder) ExchangeBindingId(exchangeBindingId string) *ExchangeBindingBuilder {
	builder.exchangeBindingId = exchangeBindingId
	builder.exchangeBindingIdFlag = true
	return builder
}

func (builder *ExchangeBindingBuilder) Build() *ExchangeBinding {
	req := &ExchangeBinding{}
	if builder.adminAccountFlag {
		req.AdminAccount = &builder.adminAccount

	}
	if builder.exchangeAccountFlag {
		req.ExchangeAccount = &builder.exchangeAccount

	}
	if builder.userIdFlag {
		req.UserId = &builder.userId

	}
	if builder.statusFlag {
		req.Status = &builder.status

	}
	if builder.exchangeBindingIdFlag {
		req.ExchangeBindingId = &builder.exchangeBindingId

	}
	return req
}

type Freebusy struct {
	StartTime *string `json:"start_time,omitempty"` // 忙闲信息开始时间，RFC3339 date_time 格式
	EndTime   *string `json:"end_time,omitempty"`   // 忙闲信息结束时间，RFC3339 date_time 格式
}

type FreebusyBuilder struct {
	startTime     string // 忙闲信息开始时间，RFC3339 date_time 格式
	startTimeFlag bool
	endTime       string // 忙闲信息结束时间，RFC3339 date_time 格式
	endTimeFlag   bool
}

func NewFreebusyBuilder() *FreebusyBuilder {
	builder := &FreebusyBuilder{}
	return builder
}

// 忙闲信息开始时间，RFC3339 date_time 格式
//
// 示例值：2020-10-28T22:30:00+08:00
func (builder *FreebusyBuilder) StartTime(startTime string) *FreebusyBuilder {
	builder.startTime = startTime
	builder.startTimeFlag = true
	return builder
}

// 忙闲信息结束时间，RFC3339 date_time 格式
//
// 示例值：2020-10-28T22:45:00+08:00
func (builder *FreebusyBuilder) EndTime(endTime string) *FreebusyBuilder {
	builder.endTime = endTime
	builder.endTimeFlag = true
	return builder
}

func (builder *FreebusyBuilder) Build() *Freebusy {
	req := &Freebusy{}
	if builder.startTimeFlag {
		req.StartTime = &builder.startTime

	}
	if builder.endTimeFlag {
		req.EndTime = &builder.endTime

	}
	return req
}

type Reminder struct {
	Minutes *int `json:"minutes,omitempty"` // 日程提醒时间的偏移量，正数时表示在日程开始前X分钟提醒，负数时表示在日程开始后X分钟提醒;新建或更新日程时传入该字段，仅对当前身份生效
}

type ReminderBuilder struct {
	minutes     int // 日程提醒时间的偏移量，正数时表示在日程开始前X分钟提醒，负数时表示在日程开始后X分钟提醒;新建或更新日程时传入该字段，仅对当前身份生效
	minutesFlag bool
}

func NewReminderBuilder() *ReminderBuilder {
	builder := &ReminderBuilder{}
	return builder
}

// 日程提醒时间的偏移量，正数时表示在日程开始前X分钟提醒，负数时表示在日程开始后X分钟提醒;新建或更新日程时传入该字段，仅对当前身份生效
//
// 示例值：5
func (builder *ReminderBuilder) Minutes(minutes int) *ReminderBuilder {
	builder.minutes = minutes
	builder.minutesFlag = true
	return builder
}

func (builder *ReminderBuilder) Build() *Reminder {
	req := &Reminder{}
	if builder.minutesFlag {
		req.Minutes = &builder.minutes

	}
	return req
}

type Schema struct {
	UiName   *string `json:"ui_name,omitempty"`   // UI名称。取值范围如下： ;- ForwardIcon: 日程转发按钮 ;- MeetingChatIcon: 会议群聊按钮 ;- MeetingMinutesIcon: 会议纪要按钮 ;- MeetingVideo: 视频会议区域 ;- RSVP: 接受/拒绝/待定区域 ;- Attendee: 参与者区域 ;- OrganizerOrCreator: 组织者/创建者区域
	UiStatus *string `json:"ui_status,omitempty"` // UI项自定义状态。**目前只支持hide**
	AppLink  *string `json:"app_link,omitempty"`  // 按钮点击后跳转的链接; **该字段暂不支持传入。**
}

type SchemaBuilder struct {
	uiName       string // UI名称。取值范围如下： ;- ForwardIcon: 日程转发按钮 ;- MeetingChatIcon: 会议群聊按钮 ;- MeetingMinutesIcon: 会议纪要按钮 ;- MeetingVideo: 视频会议区域 ;- RSVP: 接受/拒绝/待定区域 ;- Attendee: 参与者区域 ;- OrganizerOrCreator: 组织者/创建者区域
	uiNameFlag   bool
	uiStatus     string // UI项自定义状态。**目前只支持hide**
	uiStatusFlag bool
	appLink      string // 按钮点击后跳转的链接; **该字段暂不支持传入。**
	appLinkFlag  bool
}

func NewSchemaBuilder() *SchemaBuilder {
	builder := &SchemaBuilder{}
	return builder
}

// UI名称。取值范围如下： ;- ForwardIcon: 日程转发按钮 ;- MeetingChatIcon: 会议群聊按钮 ;- MeetingMinutesIcon: 会议纪要按钮 ;- MeetingVideo: 视频会议区域 ;- RSVP: 接受/拒绝/待定区域 ;- Attendee: 参与者区域 ;- OrganizerOrCreator: 组织者/创建者区域
//
// 示例值：ForwardIcon
func (builder *SchemaBuilder) UiName(uiName string) *SchemaBuilder {
	builder.uiName = uiName
	builder.uiNameFlag = true
	return builder
}

// UI项自定义状态。**目前只支持hide**
//
// 示例值：hide
func (builder *SchemaBuilder) UiStatus(uiStatus string) *SchemaBuilder {
	builder.uiStatus = uiStatus
	builder.uiStatusFlag = true
	return builder
}

// 按钮点击后跳转的链接; **该字段暂不支持传入。**
//
// 示例值：https://applink.feishu.cn/client/calendar/event/detail?calendarId=xxxxxx&key=xxxxxx&originalTime=xxxxxx&startTime=xxxxxx
func (builder *SchemaBuilder) AppLink(appLink string) *SchemaBuilder {
	builder.appLink = appLink
	builder.appLinkFlag = true
	return builder
}

func (builder *SchemaBuilder) Build() *Schema {
	req := &Schema{}
	if builder.uiNameFlag {
		req.UiName = &builder.uiName

	}
	if builder.uiStatusFlag {
		req.UiStatus = &builder.uiStatus

	}
	if builder.appLinkFlag {
		req.AppLink = &builder.appLink

	}
	return req
}

type Setting struct {
}

type TimeInfo struct {
	Date      *string `json:"date,omitempty"`      // 仅全天日程使用该字段，如2018-09-01。需满足 RFC3339 格式。不能与 timestamp 同时指定
	Timestamp *string `json:"timestamp,omitempty"` // 秒级时间戳，如1602504000(表示2020/10/12 20:0:00 +8时区)
	Timezone  *string `json:"timezone,omitempty"`  // 时区名称，使用IANA Time Zone Database标准，如Asia/Shanghai；全天日程时区固定为UTC，非全天日程时区默认为Asia/Shanghai
}

type TimeInfoBuilder struct {
	date          string // 仅全天日程使用该字段，如2018-09-01。需满足 RFC3339 格式。不能与 timestamp 同时指定
	dateFlag      bool
	timestamp     string // 秒级时间戳，如1602504000(表示2020/10/12 20:0:00 +8时区)
	timestampFlag bool
	timezone      string // 时区名称，使用IANA Time Zone Database标准，如Asia/Shanghai；全天日程时区固定为UTC，非全天日程时区默认为Asia/Shanghai
	timezoneFlag  bool
}

func NewTimeInfoBuilder() *TimeInfoBuilder {
	builder := &TimeInfoBuilder{}
	return builder
}

// 仅全天日程使用该字段，如2018-09-01。需满足 RFC3339 格式。不能与 timestamp 同时指定
//
// 示例值：2018-09-01
func (builder *TimeInfoBuilder) Date(date string) *TimeInfoBuilder {
	builder.date = date
	builder.dateFlag = true
	return builder
}

// 秒级时间戳，如1602504000(表示2020/10/12 20:0:00 +8时区)
//
// 示例值：1602504000
func (builder *TimeInfoBuilder) Timestamp(timestamp string) *TimeInfoBuilder {
	builder.timestamp = timestamp
	builder.timestampFlag = true
	return builder
}

// 时区名称，使用IANA Time Zone Database标准，如Asia/Shanghai；全天日程时区固定为UTC，非全天日程时区默认为Asia/Shanghai
//
// 示例值：Asia/Shanghai
func (builder *TimeInfoBuilder) Timezone(timezone string) *TimeInfoBuilder {
	builder.timezone = timezone
	builder.timezoneFlag = true
	return builder
}

func (builder *TimeInfoBuilder) Build() *TimeInfo {
	req := &TimeInfo{}
	if builder.dateFlag {
		req.Date = &builder.date

	}
	if builder.timestampFlag {
		req.Timestamp = &builder.timestamp

	}
	if builder.timezoneFlag {
		req.Timezone = &builder.timezone

	}
	return req
}

type TimeoffEvent struct {
	TimeoffEventId *string `json:"timeoff_event_id,omitempty"` // 请假日程ID。参见[请假日程ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/calendar-v4/timeoff_event/introduction#b6611a02)
	UserId         *string `json:"user_id,omitempty"`          // 用户id，参见[用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction)
	Timezone       *string `json:"timezone,omitempty"`         // 时区
	StartTime      *string `json:"start_time,omitempty"`       // 休假开始时间：;有时间戳(1609430400)和日期(2021-01-01)两种格式，其它格式无效；;时间戳格式是按小时休假日程，日期格式是全天休假日程；;start_time与end_time格式需保持一致，否则无效。
	EndTime        *string `json:"end_time,omitempty"`         // 休假结束时间：;有时间戳(1609430400)和日期(2021-01-01)两种格式，其它格式无效；;时间戳格式是按小时休假日程，日期格式是全天休假日程；;start_time与end_time格式需保持一致，否则无效。
	Title          *string `json:"title,omitempty"`            // 自定义请假日程标题，没有设置则为默认日程标题
	Description    *string `json:"description,omitempty"`      // 自定义请假日程描述，没有设置则为默认日程描述
}

type TimeoffEventBuilder struct {
	timeoffEventId     string // 请假日程ID。参见[请假日程ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/calendar-v4/timeoff_event/introduction#b6611a02)
	timeoffEventIdFlag bool
	userId             string // 用户id，参见[用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction)
	userIdFlag         bool
	timezone           string // 时区
	timezoneFlag       bool
	startTime          string // 休假开始时间：;有时间戳(1609430400)和日期(2021-01-01)两种格式，其它格式无效；;时间戳格式是按小时休假日程，日期格式是全天休假日程；;start_time与end_time格式需保持一致，否则无效。
	startTimeFlag      bool
	endTime            string // 休假结束时间：;有时间戳(1609430400)和日期(2021-01-01)两种格式，其它格式无效；;时间戳格式是按小时休假日程，日期格式是全天休假日程；;start_time与end_time格式需保持一致，否则无效。
	endTimeFlag        bool
	title              string // 自定义请假日程标题，没有设置则为默认日程标题
	titleFlag          bool
	description        string // 自定义请假日程描述，没有设置则为默认日程描述
	descriptionFlag    bool
}

func NewTimeoffEventBuilder() *TimeoffEventBuilder {
	builder := &TimeoffEventBuilder{}
	return builder
}

// 请假日程ID。参见[请假日程ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/calendar-v4/timeoff_event/introduction#b6611a02)
//
// 示例值：timeoff:XXXXXX-XXXX-0917-1623-aa493d591a39-XXXXXX
func (builder *TimeoffEventBuilder) TimeoffEventId(timeoffEventId string) *TimeoffEventBuilder {
	builder.timeoffEventId = timeoffEventId
	builder.timeoffEventIdFlag = true
	return builder
}

// 用户id，参见[用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction)
//
// 示例值：ou_XXXXXXXXXX
func (builder *TimeoffEventBuilder) UserId(userId string) *TimeoffEventBuilder {
	builder.userId = userId
	builder.userIdFlag = true
	return builder
}

// 时区
//
// 示例值：Asia/Shanghai
func (builder *TimeoffEventBuilder) Timezone(timezone string) *TimeoffEventBuilder {
	builder.timezone = timezone
	builder.timezoneFlag = true
	return builder
}

// 休假开始时间：;有时间戳(1609430400)和日期(2021-01-01)两种格式，其它格式无效；;时间戳格式是按小时休假日程，日期格式是全天休假日程；;start_time与end_time格式需保持一致，否则无效。
//
// 示例值：2021-01-01
func (builder *TimeoffEventBuilder) StartTime(startTime string) *TimeoffEventBuilder {
	builder.startTime = startTime
	builder.startTimeFlag = true
	return builder
}

// 休假结束时间：;有时间戳(1609430400)和日期(2021-01-01)两种格式，其它格式无效；;时间戳格式是按小时休假日程，日期格式是全天休假日程；;start_time与end_time格式需保持一致，否则无效。
//
// 示例值：2021-01-01
func (builder *TimeoffEventBuilder) EndTime(endTime string) *TimeoffEventBuilder {
	builder.endTime = endTime
	builder.endTimeFlag = true
	return builder
}

// 自定义请假日程标题，没有设置则为默认日程标题
//
// 示例值：请假中(全天) / 1-Day Time Off
func (builder *TimeoffEventBuilder) Title(title string) *TimeoffEventBuilder {
	builder.title = title
	builder.titleFlag = true
	return builder
}

// 自定义请假日程描述，没有设置则为默认日程描述
//
// 示例值：若删除此日程，飞书中相应的“请假”标签将自动消失，而请假系统中的休假申请不会被撤销。
func (builder *TimeoffEventBuilder) Description(description string) *TimeoffEventBuilder {
	builder.description = description
	builder.descriptionFlag = true
	return builder
}

func (builder *TimeoffEventBuilder) Build() *TimeoffEvent {
	req := &TimeoffEvent{}
	if builder.timeoffEventIdFlag {
		req.TimeoffEventId = &builder.timeoffEventId

	}
	if builder.userIdFlag {
		req.UserId = &builder.userId

	}
	if builder.timezoneFlag {
		req.Timezone = &builder.timezone

	}
	if builder.startTimeFlag {
		req.StartTime = &builder.startTime

	}
	if builder.endTimeFlag {
		req.EndTime = &builder.endTime

	}
	if builder.titleFlag {
		req.Title = &builder.title

	}
	if builder.descriptionFlag {
		req.Description = &builder.description

	}
	return req
}

type UserCalendar struct {
	Calendar *Calendar `json:"calendar,omitempty"` // 日历实体信息
	UserId   *string   `json:"user_id,omitempty"`  // 日历的创建者user ID，参见[用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction)
}

type UserCalendarBuilder struct {
	calendar     *Calendar // 日历实体信息
	calendarFlag bool
	userId       string // 日历的创建者user ID，参见[用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction)
	userIdFlag   bool
}

func NewUserCalendarBuilder() *UserCalendarBuilder {
	builder := &UserCalendarBuilder{}
	return builder
}

// 日历实体信息
//
// 示例值：
func (builder *UserCalendarBuilder) Calendar(calendar *Calendar) *UserCalendarBuilder {
	builder.calendar = calendar
	builder.calendarFlag = true
	return builder
}

// 日历的创建者user ID，参见[用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction)
//
// 示例值：ou_xxxxxx
func (builder *UserCalendarBuilder) UserId(userId string) *UserCalendarBuilder {
	builder.userId = userId
	builder.userIdFlag = true
	return builder
}

func (builder *UserCalendarBuilder) Build() *UserCalendar {
	req := &UserCalendar{}
	if builder.calendarFlag {
		req.Calendar = builder.calendar
	}
	if builder.userIdFlag {
		req.UserId = &builder.userId

	}
	return req
}

type UserId struct {
	UserId  *string `json:"user_id,omitempty"`  //
	OpenId  *string `json:"open_id,omitempty"`  //
	UnionId *string `json:"union_id,omitempty"` //
}

type UserIdBuilder struct {
	userId      string //
	userIdFlag  bool
	openId      string //
	openIdFlag  bool
	unionId     string //
	unionIdFlag bool
}

func NewUserIdBuilder() *UserIdBuilder {
	builder := &UserIdBuilder{}
	return builder
}

//
//
// 示例值：
func (builder *UserIdBuilder) UserId(userId string) *UserIdBuilder {
	builder.userId = userId
	builder.userIdFlag = true
	return builder
}

//
//
// 示例值：
func (builder *UserIdBuilder) OpenId(openId string) *UserIdBuilder {
	builder.openId = openId
	builder.openIdFlag = true
	return builder
}

//
//
// 示例值：
func (builder *UserIdBuilder) UnionId(unionId string) *UserIdBuilder {
	builder.unionId = unionId
	builder.unionIdFlag = true
	return builder
}

func (builder *UserIdBuilder) Build() *UserId {
	req := &UserId{}
	if builder.userIdFlag {
		req.UserId = &builder.userId

	}
	if builder.openIdFlag {
		req.OpenId = &builder.openId

	}
	if builder.unionIdFlag {
		req.UnionId = &builder.unionId

	}
	return req
}

type Vchat struct {
	VcType      *string `json:"vc_type,omitempty"`     // 视频会议类型
	IconType    *string `json:"icon_type,omitempty"`   // 第三方视频会议icon类型；可以为空，为空展示默认icon。
	Description *string `json:"description,omitempty"` // 第三方视频会议文案，可以为空，为空展示默认文案
	MeetingUrl  *string `json:"meeting_url,omitempty"` // 视频会议URL
}

type VchatBuilder struct {
	vcType          string // 视频会议类型
	vcTypeFlag      bool
	iconType        string // 第三方视频会议icon类型；可以为空，为空展示默认icon。
	iconTypeFlag    bool
	description     string // 第三方视频会议文案，可以为空，为空展示默认文案
	descriptionFlag bool
	meetingUrl      string // 视频会议URL
	meetingUrlFlag  bool
}

func NewVchatBuilder() *VchatBuilder {
	builder := &VchatBuilder{}
	return builder
}

// 视频会议类型
//
// 示例值：third_party
func (builder *VchatBuilder) VcType(vcType string) *VchatBuilder {
	builder.vcType = vcType
	builder.vcTypeFlag = true
	return builder
}

// 第三方视频会议icon类型；可以为空，为空展示默认icon。
//
// 示例值：vc
func (builder *VchatBuilder) IconType(iconType string) *VchatBuilder {
	builder.iconType = iconType
	builder.iconTypeFlag = true
	return builder
}

// 第三方视频会议文案，可以为空，为空展示默认文案
//
// 示例值：发起视频会议
func (builder *VchatBuilder) Description(description string) *VchatBuilder {
	builder.description = description
	builder.descriptionFlag = true
	return builder
}

// 视频会议URL
//
// 示例值：https://example.com
func (builder *VchatBuilder) MeetingUrl(meetingUrl string) *VchatBuilder {
	builder.meetingUrl = meetingUrl
	builder.meetingUrlFlag = true
	return builder
}

func (builder *VchatBuilder) Build() *Vchat {
	req := &Vchat{}
	if builder.vcTypeFlag {
		req.VcType = &builder.vcType

	}
	if builder.iconTypeFlag {
		req.IconType = &builder.iconType

	}
	if builder.descriptionFlag {
		req.Description = &builder.description

	}
	if builder.meetingUrlFlag {
		req.MeetingUrl = &builder.meetingUrl

	}
	return req
}

type CreateCalendarReqBuilder struct {
	apiReq   *larkcore.ApiReq
	calendar *Calendar
}

func NewCreateCalendarReqBuilder() *CreateCalendarReqBuilder {
	builder := &CreateCalendarReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 该接口用于为当前身份（应用 / 用户）创建一个共享日历。;;身份由 Header Authorization 的 Token 类型决定。
func (builder *CreateCalendarReqBuilder) Calendar(calendar *Calendar) *CreateCalendarReqBuilder {
	builder.calendar = calendar
	return builder
}

func (builder *CreateCalendarReqBuilder) Build() *CreateCalendarReq {
	req := &CreateCalendarReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.Body = builder.calendar
	return req
}

type CreateCalendarReq struct {
	apiReq   *larkcore.ApiReq
	Calendar *Calendar `body:""`
}

type CreateCalendarRespData struct {
	Calendar *Calendar `json:"calendar,omitempty"` // 新创建的日历实体
}

type CreateCalendarResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *CreateCalendarRespData `json:"data"` // 业务数据
}

func (resp *CreateCalendarResp) Success() bool {
	return resp.Code == 0
}

type DeleteCalendarReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewDeleteCalendarReqBuilder() *DeleteCalendarReqBuilder {
	builder := &DeleteCalendarReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 日历ID。参见[日历ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/calendar-v4/calendar/introduction)
//
// 示例值：<EMAIL>
func (builder *DeleteCalendarReqBuilder) CalendarId(calendarId string) *DeleteCalendarReqBuilder {
	builder.apiReq.PathParams.Set("calendar_id", fmt.Sprint(calendarId))
	return builder
}

func (builder *DeleteCalendarReqBuilder) Build() *DeleteCalendarReq {
	req := &DeleteCalendarReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	return req
}

type DeleteCalendarReq struct {
	apiReq *larkcore.ApiReq
}

type DeleteCalendarResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *DeleteCalendarResp) Success() bool {
	return resp.Code == 0
}

type GetCalendarReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewGetCalendarReqBuilder() *GetCalendarReqBuilder {
	builder := &GetCalendarReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 日历ID。参见[日历ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/calendar-v4/calendar/introduction)
//
// 示例值：<EMAIL>
func (builder *GetCalendarReqBuilder) CalendarId(calendarId string) *GetCalendarReqBuilder {
	builder.apiReq.PathParams.Set("calendar_id", fmt.Sprint(calendarId))
	return builder
}

func (builder *GetCalendarReqBuilder) Build() *GetCalendarReq {
	req := &GetCalendarReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	return req
}

type GetCalendarReq struct {
	apiReq *larkcore.ApiReq
}

type GetCalendarRespData struct {
	CalendarId   *string `json:"calendar_id,omitempty"`    // 日历OpenId
	Summary      *string `json:"summary,omitempty"`        // 日历标题
	Description  *string `json:"description,omitempty"`    // 日历描述
	Permissions  *string `json:"permissions,omitempty"`    //
	Color        *int    `json:"color,omitempty"`          // 日历颜色，颜色RGB值的int32表示。客户端展示时会映射到色板上最接近的一种颜色。仅对当前身份生效
	Type         *string `json:"type,omitempty"`           // 日历类型
	SummaryAlias *string `json:"summary_alias,omitempty"`  // 日历备注名，修改或添加后仅对当前身份生效
	IsDeleted    *bool   `json:"is_deleted,omitempty"`     // 对于当前身份，日历是否已经被标记为删除
	IsThirdParty *bool   `json:"is_third_party,omitempty"` // 当前日历是否是第三方数据；三方日历及日程只支持读，不支持写入
	Role         *string `json:"role,omitempty"`           // 当前身份对于该日历的访问权限
}

type GetCalendarResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *GetCalendarRespData `json:"data"` // 业务数据
}

func (resp *GetCalendarResp) Success() bool {
	return resp.Code == 0
}

type ListCalendarReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewListCalendarReqBuilder() *ListCalendarReqBuilder {
	builder := &ListCalendarReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 一次请求要求返回最大数量，默认500，取值范围为[50. 1000]
//
// 示例值：50
func (builder *ListCalendarReqBuilder) PageSize(pageSize int) *ListCalendarReqBuilder {
	builder.apiReq.QueryParams.Set("page_size", fmt.Sprint(pageSize))
	return builder
}

// 上次请求Response返回的分页标记，首次请求时为空
//
// 示例值：ListCalendarsPageToken_xxx
func (builder *ListCalendarReqBuilder) PageToken(pageToken string) *ListCalendarReqBuilder {
	builder.apiReq.QueryParams.Set("page_token", fmt.Sprint(pageToken))
	return builder
}

// 上次请求Response返回的增量同步标记，分页请求未结束时为空
//
// 示例值：ListCalendarsSyncToken_xxx
func (builder *ListCalendarReqBuilder) SyncToken(syncToken string) *ListCalendarReqBuilder {
	builder.apiReq.QueryParams.Set("sync_token", fmt.Sprint(syncToken))
	return builder
}

func (builder *ListCalendarReqBuilder) Build() *ListCalendarReq {
	req := &ListCalendarReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type ListCalendarReq struct {
	apiReq *larkcore.ApiReq
}

type ListCalendarRespData struct {
	HasMore      *bool       `json:"has_more,omitempty"`      // 是否还有更多数据
	PageToken    *string     `json:"page_token,omitempty"`    // 下次请求需要带上的分页标记，90 天有效期
	SyncToken    *string     `json:"sync_token,omitempty"`    // 下次请求需要带上的增量同步标记，90 天有效期
	CalendarList []*Calendar `json:"calendar_list,omitempty"` // 分页加载的日历数据列表
}

type ListCalendarResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *ListCalendarRespData `json:"data"` // 业务数据
}

func (resp *ListCalendarResp) Success() bool {
	return resp.Code == 0
}

type PatchCalendarReqBuilder struct {
	apiReq   *larkcore.ApiReq
	calendar *Calendar
}

func NewPatchCalendarReqBuilder() *PatchCalendarReqBuilder {
	builder := &PatchCalendarReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 日历ID。参见[日历ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/calendar-v4/calendar/introduction)
//
// 示例值：<EMAIL>
func (builder *PatchCalendarReqBuilder) CalendarId(calendarId string) *PatchCalendarReqBuilder {
	builder.apiReq.PathParams.Set("calendar_id", fmt.Sprint(calendarId))
	return builder
}

// 该接口用于以当前身份（应用 / 用户）修改日历信息。;;身份由 Header Authorization 的 Token 类型决定。
func (builder *PatchCalendarReqBuilder) Calendar(calendar *Calendar) *PatchCalendarReqBuilder {
	builder.calendar = calendar
	return builder
}

func (builder *PatchCalendarReqBuilder) Build() *PatchCalendarReq {
	req := &PatchCalendarReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.Body = builder.calendar
	return req
}

type PatchCalendarReq struct {
	apiReq   *larkcore.ApiReq
	Calendar *Calendar `body:""`
}

type PatchCalendarRespData struct {
	Calendar *Calendar `json:"calendar,omitempty"` // 更新后的日历实体
}

type PatchCalendarResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *PatchCalendarRespData `json:"data"` // 业务数据
}

func (resp *PatchCalendarResp) Success() bool {
	return resp.Code == 0
}

type PrimaryCalendarReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewPrimaryCalendarReqBuilder() *PrimaryCalendarReqBuilder {
	builder := &PrimaryCalendarReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 此次调用中使用的用户ID的类型
//
// 示例值：
func (builder *PrimaryCalendarReqBuilder) UserIdType(userIdType string) *PrimaryCalendarReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

func (builder *PrimaryCalendarReqBuilder) Build() *PrimaryCalendarReq {
	req := &PrimaryCalendarReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type PrimaryCalendarReq struct {
	apiReq *larkcore.ApiReq
}

type PrimaryCalendarRespData struct {
	Calendars []*UserCalendar `json:"calendars,omitempty"` // 主日历列表
}

type PrimaryCalendarResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *PrimaryCalendarRespData `json:"data"` // 业务数据
}

func (resp *PrimaryCalendarResp) Success() bool {
	return resp.Code == 0
}

type SearchCalendarReqBodyBuilder struct {
	query     string // 搜索关键字
	queryFlag bool
}

func NewSearchCalendarReqBodyBuilder() *SearchCalendarReqBodyBuilder {
	builder := &SearchCalendarReqBodyBuilder{}
	return builder
}

// 搜索关键字
//
//示例值：query words
func (builder *SearchCalendarReqBodyBuilder) Query(query string) *SearchCalendarReqBodyBuilder {
	builder.query = query
	builder.queryFlag = true
	return builder
}

func (builder *SearchCalendarReqBodyBuilder) Build() *SearchCalendarReqBody {
	req := &SearchCalendarReqBody{}
	if builder.queryFlag {
		req.Query = &builder.query
	}
	return req
}

type SearchCalendarPathReqBodyBuilder struct {
	query     string // 搜索关键字
	queryFlag bool
}

func NewSearchCalendarPathReqBodyBuilder() *SearchCalendarPathReqBodyBuilder {
	builder := &SearchCalendarPathReqBodyBuilder{}
	return builder
}

// 搜索关键字
//
// 示例值：query words
func (builder *SearchCalendarPathReqBodyBuilder) Query(query string) *SearchCalendarPathReqBodyBuilder {
	builder.query = query
	builder.queryFlag = true
	return builder
}

func (builder *SearchCalendarPathReqBodyBuilder) Build() (*SearchCalendarReqBody, error) {
	req := &SearchCalendarReqBody{}
	if builder.queryFlag {
		req.Query = &builder.query
	}
	return req, nil
}

type SearchCalendarReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *SearchCalendarReqBody
	limit  int // 最大返回多少记录，当使用迭代器访问时才有效
}

func NewSearchCalendarReqBuilder() *SearchCalendarReqBuilder {
	builder := &SearchCalendarReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 最大返回多少记录，当使用迭代器访问时才有效
func (builder *SearchCalendarReqBuilder) Limit(limit int) *SearchCalendarReqBuilder {
	builder.limit = limit
	return builder
}

//
//
// 示例值：10
func (builder *SearchCalendarReqBuilder) PageToken(pageToken string) *SearchCalendarReqBuilder {
	builder.apiReq.QueryParams.Set("page_token", fmt.Sprint(pageToken))
	return builder
}

//
//
// 示例值：10
func (builder *SearchCalendarReqBuilder) PageSize(pageSize int) *SearchCalendarReqBuilder {
	builder.apiReq.QueryParams.Set("page_size", fmt.Sprint(pageSize))
	return builder
}

// 该接口用于通过关键字查询公共日历或用户主日历。
func (builder *SearchCalendarReqBuilder) Body(body *SearchCalendarReqBody) *SearchCalendarReqBuilder {
	builder.body = body
	return builder
}

func (builder *SearchCalendarReqBuilder) Build() *SearchCalendarReq {
	req := &SearchCalendarReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.Limit = builder.limit
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.body
	return req
}

type SearchCalendarReqBody struct {
	Query *string `json:"query,omitempty"` // 搜索关键字
}

type SearchCalendarReq struct {
	apiReq *larkcore.ApiReq
	Body   *SearchCalendarReqBody `body:""`
	Limit  int                    // 最多返回多少记录，只有在使用迭代器访问时，才有效

}

type SearchCalendarRespData struct {
	Items     []*Calendar `json:"items,omitempty"`      // 搜索命中的日历列表
	PageToken *string     `json:"page_token,omitempty"` // 下次请求需要带上的分页标记
}

type SearchCalendarResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *SearchCalendarRespData `json:"data"` // 业务数据
}

func (resp *SearchCalendarResp) Success() bool {
	return resp.Code == 0
}

type SubscribeCalendarReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewSubscribeCalendarReqBuilder() *SubscribeCalendarReqBuilder {
	builder := &SubscribeCalendarReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 日历ID。参见[日历ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/calendar-v4/calendar/introduction)
//
// 示例值：<EMAIL>
func (builder *SubscribeCalendarReqBuilder) CalendarId(calendarId string) *SubscribeCalendarReqBuilder {
	builder.apiReq.PathParams.Set("calendar_id", fmt.Sprint(calendarId))
	return builder
}

func (builder *SubscribeCalendarReqBuilder) Build() *SubscribeCalendarReq {
	req := &SubscribeCalendarReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	return req
}

type SubscribeCalendarReq struct {
	apiReq *larkcore.ApiReq
}

type SubscribeCalendarRespData struct {
	Calendar *Calendar `json:"calendar,omitempty"` // 订阅的日历实体
}

type SubscribeCalendarResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *SubscribeCalendarRespData `json:"data"` // 业务数据
}

func (resp *SubscribeCalendarResp) Success() bool {
	return resp.Code == 0
}

type SubscriptionCalendarResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *SubscriptionCalendarResp) Success() bool {
	return resp.Code == 0
}

type UnsubscribeCalendarReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewUnsubscribeCalendarReqBuilder() *UnsubscribeCalendarReqBuilder {
	builder := &UnsubscribeCalendarReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 日历ID。参见[日历ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/calendar-v4/calendar/introduction)
//
// 示例值：<EMAIL>
func (builder *UnsubscribeCalendarReqBuilder) CalendarId(calendarId string) *UnsubscribeCalendarReqBuilder {
	builder.apiReq.PathParams.Set("calendar_id", fmt.Sprint(calendarId))
	return builder
}

func (builder *UnsubscribeCalendarReqBuilder) Build() *UnsubscribeCalendarReq {
	req := &UnsubscribeCalendarReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	return req
}

type UnsubscribeCalendarReq struct {
	apiReq *larkcore.ApiReq
}

type UnsubscribeCalendarResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *UnsubscribeCalendarResp) Success() bool {
	return resp.Code == 0
}

type UnsubscriptionCalendarResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *UnsubscriptionCalendarResp) Success() bool {
	return resp.Code == 0
}

type CreateCalendarAclReqBuilder struct {
	apiReq      *larkcore.ApiReq
	calendarAcl *CalendarAcl
}

func NewCreateCalendarAclReqBuilder() *CreateCalendarAclReqBuilder {
	builder := &CreateCalendarAclReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 日历ID。参见[日历ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/calendar-v4/calendar/introduction)
//
// 示例值：<EMAIL>
func (builder *CreateCalendarAclReqBuilder) CalendarId(calendarId string) *CreateCalendarAclReqBuilder {
	builder.apiReq.PathParams.Set("calendar_id", fmt.Sprint(calendarId))
	return builder
}

// 此次调用中使用的用户ID的类型
//
// 示例值：
func (builder *CreateCalendarAclReqBuilder) UserIdType(userIdType string) *CreateCalendarAclReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

// 该接口用于以当前身份（应用 / 用户）给日历添加访问控制权限，即日历成员。;;身份由 Header Authorization 的 Token 类型决定。
func (builder *CreateCalendarAclReqBuilder) CalendarAcl(calendarAcl *CalendarAcl) *CreateCalendarAclReqBuilder {
	builder.calendarAcl = calendarAcl
	return builder
}

func (builder *CreateCalendarAclReqBuilder) Build() *CreateCalendarAclReq {
	req := &CreateCalendarAclReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.calendarAcl
	return req
}

type CreateCalendarAclReq struct {
	apiReq      *larkcore.ApiReq
	CalendarAcl *CalendarAcl `body:""`
}

type CreateCalendarAclRespData struct {
	AclId *string   `json:"acl_id,omitempty"` // acl资源ID
	Role  *string   `json:"role,omitempty"`   // 对日历的访问权限
	Scope *AclScope `json:"scope,omitempty"`  // 权限范围
}

type CreateCalendarAclResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *CreateCalendarAclRespData `json:"data"` // 业务数据
}

func (resp *CreateCalendarAclResp) Success() bool {
	return resp.Code == 0
}

type DeleteCalendarAclReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewDeleteCalendarAclReqBuilder() *DeleteCalendarAclReqBuilder {
	builder := &DeleteCalendarAclReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 日历ID。参见[日历ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/calendar-v4/calendar/introduction)
//
// 示例值：<EMAIL>
func (builder *DeleteCalendarAclReqBuilder) CalendarId(calendarId string) *DeleteCalendarAclReqBuilder {
	builder.apiReq.PathParams.Set("calendar_id", fmt.Sprint(calendarId))
	return builder
}

// acl资源ID。参见[ACL ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/calendar-v4/calendar-acl/introduction)
//
// 示例值：user_xxxxxx
func (builder *DeleteCalendarAclReqBuilder) AclId(aclId string) *DeleteCalendarAclReqBuilder {
	builder.apiReq.PathParams.Set("acl_id", fmt.Sprint(aclId))
	return builder
}

func (builder *DeleteCalendarAclReqBuilder) Build() *DeleteCalendarAclReq {
	req := &DeleteCalendarAclReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	return req
}

type DeleteCalendarAclReq struct {
	apiReq *larkcore.ApiReq
}

type DeleteCalendarAclResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *DeleteCalendarAclResp) Success() bool {
	return resp.Code == 0
}

type ListCalendarAclReqBuilder struct {
	apiReq *larkcore.ApiReq
	limit  int // 最大返回多少记录，当使用迭代器访问时才有效
}

func NewListCalendarAclReqBuilder() *ListCalendarAclReqBuilder {
	builder := &ListCalendarAclReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 最大返回多少记录，当使用迭代器访问时才有效
func (builder *ListCalendarAclReqBuilder) Limit(limit int) *ListCalendarAclReqBuilder {
	builder.limit = limit
	return builder
}

// 日历ID。参见[日历ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/calendar-v4/calendar/introduction)
//
// 示例值：<EMAIL>
func (builder *ListCalendarAclReqBuilder) CalendarId(calendarId string) *ListCalendarAclReqBuilder {
	builder.apiReq.PathParams.Set("calendar_id", fmt.Sprint(calendarId))
	return builder
}

// 此次调用中使用的用户ID的类型
//
// 示例值：
func (builder *ListCalendarAclReqBuilder) UserIdType(userIdType string) *ListCalendarAclReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

//
//
// 示例值：xxx
func (builder *ListCalendarAclReqBuilder) PageToken(pageToken string) *ListCalendarAclReqBuilder {
	builder.apiReq.QueryParams.Set("page_token", fmt.Sprint(pageToken))
	return builder
}

//
//
// 示例值：10，小于10取10
func (builder *ListCalendarAclReqBuilder) PageSize(pageSize int) *ListCalendarAclReqBuilder {
	builder.apiReq.QueryParams.Set("page_size", fmt.Sprint(pageSize))
	return builder
}

func (builder *ListCalendarAclReqBuilder) Build() *ListCalendarAclReq {
	req := &ListCalendarAclReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.Limit = builder.limit
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type ListCalendarAclReq struct {
	apiReq *larkcore.ApiReq
	Limit  int // 最多返回多少记录，只有在使用迭代器访问时，才有效

}

type ListCalendarAclRespData struct {
	Acls      []*CalendarAcl `json:"acls,omitempty"`       // 入参日历对应的acl列表
	HasMore   *bool          `json:"has_more,omitempty"`   // 是否有下一页数据
	PageToken *string        `json:"page_token,omitempty"` // 下次请求需要带上的分页标记，90 天有效期
}

type ListCalendarAclResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *ListCalendarAclRespData `json:"data"` // 业务数据
}

func (resp *ListCalendarAclResp) Success() bool {
	return resp.Code == 0
}

type SubscriptionCalendarAclReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewSubscriptionCalendarAclReqBuilder() *SubscriptionCalendarAclReqBuilder {
	builder := &SubscriptionCalendarAclReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 日历ID。参见[日历ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/calendar-v4/calendar/introduction)
//
// 示例值：<EMAIL>
func (builder *SubscriptionCalendarAclReqBuilder) CalendarId(calendarId string) *SubscriptionCalendarAclReqBuilder {
	builder.apiReq.PathParams.Set("calendar_id", fmt.Sprint(calendarId))
	return builder
}

func (builder *SubscriptionCalendarAclReqBuilder) Build() *SubscriptionCalendarAclReq {
	req := &SubscriptionCalendarAclReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	return req
}

type SubscriptionCalendarAclReq struct {
	apiReq *larkcore.ApiReq
}

type SubscriptionCalendarAclResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *SubscriptionCalendarAclResp) Success() bool {
	return resp.Code == 0
}

type UnsubscriptionCalendarAclReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewUnsubscriptionCalendarAclReqBuilder() *UnsubscriptionCalendarAclReqBuilder {
	builder := &UnsubscriptionCalendarAclReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 日历ID。参见[日历ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/calendar-v4/calendar/introduction)
//
// 示例值：<EMAIL>
func (builder *UnsubscriptionCalendarAclReqBuilder) CalendarId(calendarId string) *UnsubscriptionCalendarAclReqBuilder {
	builder.apiReq.PathParams.Set("calendar_id", fmt.Sprint(calendarId))
	return builder
}

func (builder *UnsubscriptionCalendarAclReqBuilder) Build() *UnsubscriptionCalendarAclReq {
	req := &UnsubscriptionCalendarAclReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	return req
}

type UnsubscriptionCalendarAclReq struct {
	apiReq *larkcore.ApiReq
}

type UnsubscriptionCalendarAclResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *UnsubscriptionCalendarAclResp) Success() bool {
	return resp.Code == 0
}

type CreateCalendarEventReqBuilder struct {
	apiReq        *larkcore.ApiReq
	calendarEvent *CalendarEvent
}

func NewCreateCalendarEventReqBuilder() *CreateCalendarEventReqBuilder {
	builder := &CreateCalendarEventReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 日历ID。参见[日历ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/calendar-v4/calendar/introduction)
//
// 示例值：<EMAIL>
func (builder *CreateCalendarEventReqBuilder) CalendarId(calendarId string) *CreateCalendarEventReqBuilder {
	builder.apiReq.PathParams.Set("calendar_id", fmt.Sprint(calendarId))
	return builder
}

// 该接口用于以当前身份（应用 / 用户）在日历上创建一个日程。;;身份由 Header Authorization 的 Token 类型决定。
func (builder *CreateCalendarEventReqBuilder) CalendarEvent(calendarEvent *CalendarEvent) *CreateCalendarEventReqBuilder {
	builder.calendarEvent = calendarEvent
	return builder
}

func (builder *CreateCalendarEventReqBuilder) Build() *CreateCalendarEventReq {
	req := &CreateCalendarEventReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.Body = builder.calendarEvent
	return req
}

type CreateCalendarEventReq struct {
	apiReq        *larkcore.ApiReq
	CalendarEvent *CalendarEvent `body:""`
}

type CreateCalendarEventRespData struct {
	Event *CalendarEvent `json:"event,omitempty"` // 新创建的日程实体
}

type CreateCalendarEventResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *CreateCalendarEventRespData `json:"data"` // 业务数据
}

func (resp *CreateCalendarEventResp) Success() bool {
	return resp.Code == 0
}

type DeleteCalendarEventReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewDeleteCalendarEventReqBuilder() *DeleteCalendarEventReqBuilder {
	builder := &DeleteCalendarEventReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 日历ID。参见[日历ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/calendar-v4/calendar/introduction)
//
// 示例值：<EMAIL>
func (builder *DeleteCalendarEventReqBuilder) CalendarId(calendarId string) *DeleteCalendarEventReqBuilder {
	builder.apiReq.PathParams.Set("calendar_id", fmt.Sprint(calendarId))
	return builder
}

// 日程ID。参见[日程ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/calendar-v4/calendar-event/introduction)
//
// 示例值：xxxxxxxxx_0
func (builder *DeleteCalendarEventReqBuilder) EventId(eventId string) *DeleteCalendarEventReqBuilder {
	builder.apiReq.PathParams.Set("event_id", fmt.Sprint(eventId))
	return builder
}

// 删除日程是否给日程参与人发送bot通知，默认为true
//
// 示例值：false
func (builder *DeleteCalendarEventReqBuilder) NeedNotification(needNotification bool) *DeleteCalendarEventReqBuilder {
	builder.apiReq.QueryParams.Set("need_notification", fmt.Sprint(needNotification))
	return builder
}

func (builder *DeleteCalendarEventReqBuilder) Build() *DeleteCalendarEventReq {
	req := &DeleteCalendarEventReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type DeleteCalendarEventReq struct {
	apiReq *larkcore.ApiReq
}

type DeleteCalendarEventResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *DeleteCalendarEventResp) Success() bool {
	return resp.Code == 0
}

type GetCalendarEventReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewGetCalendarEventReqBuilder() *GetCalendarEventReqBuilder {
	builder := &GetCalendarEventReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 日历ID。参见[日历ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/calendar-v4/calendar/introduction)
//
// 示例值：<EMAIL>
func (builder *GetCalendarEventReqBuilder) CalendarId(calendarId string) *GetCalendarEventReqBuilder {
	builder.apiReq.PathParams.Set("calendar_id", fmt.Sprint(calendarId))
	return builder
}

// 日程ID。参见[日程ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/calendar-v4/calendar-event/introduction)
//
// 示例值：xxxxxxxxx_0
func (builder *GetCalendarEventReqBuilder) EventId(eventId string) *GetCalendarEventReqBuilder {
	builder.apiReq.PathParams.Set("event_id", fmt.Sprint(eventId))
	return builder
}

func (builder *GetCalendarEventReqBuilder) Build() *GetCalendarEventReq {
	req := &GetCalendarEventReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	return req
}

type GetCalendarEventReq struct {
	apiReq *larkcore.ApiReq
}

type GetCalendarEventRespData struct {
	Event *CalendarEvent `json:"event,omitempty"` // 日程实体
}

type GetCalendarEventResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *GetCalendarEventRespData `json:"data"` // 业务数据
}

func (resp *GetCalendarEventResp) Success() bool {
	return resp.Code == 0
}

type ListCalendarEventReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewListCalendarEventReqBuilder() *ListCalendarEventReqBuilder {
	builder := &ListCalendarEventReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 日历ID。参见[日历ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/calendar-v4/calendar/introduction)
//
// 示例值：<EMAIL>
func (builder *ListCalendarEventReqBuilder) CalendarId(calendarId string) *ListCalendarEventReqBuilder {
	builder.apiReq.PathParams.Set("calendar_id", fmt.Sprint(calendarId))
	return builder
}

// 一次请求要求返回最大数量，默认500，取值范围为[50, 1000]
//
// 示例值：50
func (builder *ListCalendarEventReqBuilder) PageSize(pageSize int) *ListCalendarEventReqBuilder {
	builder.apiReq.QueryParams.Set("page_size", fmt.Sprint(pageSize))
	return builder
}

// 拉取anchor_time之后的日程，为timestamp
//
// 示例值：1609430400
func (builder *ListCalendarEventReqBuilder) AnchorTime(anchorTime string) *ListCalendarEventReqBuilder {
	builder.apiReq.QueryParams.Set("anchor_time", fmt.Sprint(anchorTime))
	return builder
}

// 上次请求Response返回的分页标记，首次请求时为空
//
// 示例值：ListCalendarsPageToken_1632452910_1632539310
func (builder *ListCalendarEventReqBuilder) PageToken(pageToken string) *ListCalendarEventReqBuilder {
	builder.apiReq.QueryParams.Set("page_token", fmt.Sprint(pageToken))
	return builder
}

// 上次请求Response返回的增量同步标记，分页请求未结束时为空
//
// 示例值：ListCalendarsSyncToken_1632452910
func (builder *ListCalendarEventReqBuilder) SyncToken(syncToken string) *ListCalendarEventReqBuilder {
	builder.apiReq.QueryParams.Set("sync_token", fmt.Sprint(syncToken))
	return builder
}

// 日程开始Unix时间戳，单位为秒
//
// 示例值：1631777271
func (builder *ListCalendarEventReqBuilder) StartTime(startTime string) *ListCalendarEventReqBuilder {
	builder.apiReq.QueryParams.Set("start_time", fmt.Sprint(startTime))
	return builder
}

// 日程结束Unix时间戳，单位为秒
//
// 示例值：1631777271
func (builder *ListCalendarEventReqBuilder) EndTime(endTime string) *ListCalendarEventReqBuilder {
	builder.apiReq.QueryParams.Set("end_time", fmt.Sprint(endTime))
	return builder
}

func (builder *ListCalendarEventReqBuilder) Build() *ListCalendarEventReq {
	req := &ListCalendarEventReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type ListCalendarEventReq struct {
	apiReq *larkcore.ApiReq
}

type ListCalendarEventRespData struct {
	HasMore   *bool            `json:"has_more,omitempty"`   // 是否还有更多数据
	PageToken *string          `json:"page_token,omitempty"` // 下次请求需要带上的分页标记
	SyncToken *string          `json:"sync_token,omitempty"` // 下次请求需要带上的增量同步标记
	Items     []*CalendarEvent `json:"items,omitempty"`      // 日程列表
}

type ListCalendarEventResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *ListCalendarEventRespData `json:"data"` // 业务数据
}

func (resp *ListCalendarEventResp) Success() bool {
	return resp.Code == 0
}

type PatchCalendarEventReqBuilder struct {
	apiReq        *larkcore.ApiReq
	calendarEvent *CalendarEvent
}

func NewPatchCalendarEventReqBuilder() *PatchCalendarEventReqBuilder {
	builder := &PatchCalendarEventReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 日历ID。参见[日历ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/calendar-v4/calendar/introduction)
//
// 示例值：<EMAIL>
func (builder *PatchCalendarEventReqBuilder) CalendarId(calendarId string) *PatchCalendarEventReqBuilder {
	builder.apiReq.PathParams.Set("calendar_id", fmt.Sprint(calendarId))
	return builder
}

// 日程ID。参见[日程ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/calendar-v4/calendar-event/introduction)
//
// 示例值：00592a0e-7edf-4678-bc9d-1b77383ef08e_0
func (builder *PatchCalendarEventReqBuilder) EventId(eventId string) *PatchCalendarEventReqBuilder {
	builder.apiReq.PathParams.Set("event_id", fmt.Sprint(eventId))
	return builder
}

// 该接口用于以当前身份（应用 / 用户）更新日历上的一个日程。;;身份由 Header Authorization 的 Token 类型决定。
func (builder *PatchCalendarEventReqBuilder) CalendarEvent(calendarEvent *CalendarEvent) *PatchCalendarEventReqBuilder {
	builder.calendarEvent = calendarEvent
	return builder
}

func (builder *PatchCalendarEventReqBuilder) Build() *PatchCalendarEventReq {
	req := &PatchCalendarEventReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.Body = builder.calendarEvent
	return req
}

type PatchCalendarEventReq struct {
	apiReq        *larkcore.ApiReq
	CalendarEvent *CalendarEvent `body:""`
}

type PatchCalendarEventRespData struct {
	Event *CalendarEvent `json:"event,omitempty"` // 更新后的日程实体
}

type PatchCalendarEventResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *PatchCalendarEventRespData `json:"data"` // 业务数据
}

func (resp *PatchCalendarEventResp) Success() bool {
	return resp.Code == 0
}

type SearchCalendarEventReqBodyBuilder struct {
	query      string // 搜索关键字
	queryFlag  bool
	filter     *EventSearchFilter // 搜索过滤器
	filterFlag bool
}

func NewSearchCalendarEventReqBodyBuilder() *SearchCalendarEventReqBodyBuilder {
	builder := &SearchCalendarEventReqBodyBuilder{}
	return builder
}

// 搜索关键字
//
//示例值：query words
func (builder *SearchCalendarEventReqBodyBuilder) Query(query string) *SearchCalendarEventReqBodyBuilder {
	builder.query = query
	builder.queryFlag = true
	return builder
}

// 搜索过滤器
//
//示例值：
func (builder *SearchCalendarEventReqBodyBuilder) Filter(filter *EventSearchFilter) *SearchCalendarEventReqBodyBuilder {
	builder.filter = filter
	builder.filterFlag = true
	return builder
}

func (builder *SearchCalendarEventReqBodyBuilder) Build() *SearchCalendarEventReqBody {
	req := &SearchCalendarEventReqBody{}
	if builder.queryFlag {
		req.Query = &builder.query
	}
	if builder.filterFlag {
		req.Filter = builder.filter
	}
	return req
}

type SearchCalendarEventPathReqBodyBuilder struct {
	query      string // 搜索关键字
	queryFlag  bool
	filter     *EventSearchFilter // 搜索过滤器
	filterFlag bool
}

func NewSearchCalendarEventPathReqBodyBuilder() *SearchCalendarEventPathReqBodyBuilder {
	builder := &SearchCalendarEventPathReqBodyBuilder{}
	return builder
}

// 搜索关键字
//
// 示例值：query words
func (builder *SearchCalendarEventPathReqBodyBuilder) Query(query string) *SearchCalendarEventPathReqBodyBuilder {
	builder.query = query
	builder.queryFlag = true
	return builder
}

// 搜索过滤器
//
// 示例值：
func (builder *SearchCalendarEventPathReqBodyBuilder) Filter(filter *EventSearchFilter) *SearchCalendarEventPathReqBodyBuilder {
	builder.filter = filter
	builder.filterFlag = true
	return builder
}

func (builder *SearchCalendarEventPathReqBodyBuilder) Build() (*SearchCalendarEventReqBody, error) {
	req := &SearchCalendarEventReqBody{}
	if builder.queryFlag {
		req.Query = &builder.query
	}
	if builder.filterFlag {
		req.Filter = builder.filter
	}
	return req, nil
}

type SearchCalendarEventReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *SearchCalendarEventReqBody
	limit  int // 最大返回多少记录，当使用迭代器访问时才有效
}

func NewSearchCalendarEventReqBuilder() *SearchCalendarEventReqBuilder {
	builder := &SearchCalendarEventReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 最大返回多少记录，当使用迭代器访问时才有效
func (builder *SearchCalendarEventReqBuilder) Limit(limit int) *SearchCalendarEventReqBuilder {
	builder.limit = limit
	return builder
}

// 日历ID。参见[日历ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/calendar-v4/calendar/introduction)
//
// 示例值：<EMAIL>
func (builder *SearchCalendarEventReqBuilder) CalendarId(calendarId string) *SearchCalendarEventReqBuilder {
	builder.apiReq.PathParams.Set("calendar_id", fmt.Sprint(calendarId))
	return builder
}

// 此次调用中使用的用户ID的类型
//
// 示例值：
func (builder *SearchCalendarEventReqBuilder) UserIdType(userIdType string) *SearchCalendarEventReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

//
//
// 示例值：xxxxx
func (builder *SearchCalendarEventReqBuilder) PageToken(pageToken string) *SearchCalendarEventReqBuilder {
	builder.apiReq.QueryParams.Set("page_token", fmt.Sprint(pageToken))
	return builder
}

//
//
// 示例值：10
func (builder *SearchCalendarEventReqBuilder) PageSize(pageSize int) *SearchCalendarEventReqBuilder {
	builder.apiReq.QueryParams.Set("page_size", fmt.Sprint(pageSize))
	return builder
}

// 该接口用于以用户身份搜索某日历下的相关日程。;;身份由 Header Authorization 的 Token 类型决定。
func (builder *SearchCalendarEventReqBuilder) Body(body *SearchCalendarEventReqBody) *SearchCalendarEventReqBuilder {
	builder.body = body
	return builder
}

func (builder *SearchCalendarEventReqBuilder) Build() *SearchCalendarEventReq {
	req := &SearchCalendarEventReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.Limit = builder.limit
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.body
	return req
}

type SearchCalendarEventReqBody struct {
	Query  *string            `json:"query,omitempty"`  // 搜索关键字
	Filter *EventSearchFilter `json:"filter,omitempty"` // 搜索过滤器
}

type SearchCalendarEventReq struct {
	apiReq *larkcore.ApiReq
	Body   *SearchCalendarEventReqBody `body:""`
	Limit  int                         // 最多返回多少记录，只有在使用迭代器访问时，才有效

}

type SearchCalendarEventRespData struct {
	Items     []*CalendarEvent `json:"items,omitempty"`      // 搜索命中的日程列表
	PageToken *string          `json:"page_token,omitempty"` // 下次请求需要带上的分页标记
}

type SearchCalendarEventResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *SearchCalendarEventRespData `json:"data"` // 业务数据
}

func (resp *SearchCalendarEventResp) Success() bool {
	return resp.Code == 0
}

type SubscriptionCalendarEventReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewSubscriptionCalendarEventReqBuilder() *SubscriptionCalendarEventReqBuilder {
	builder := &SubscriptionCalendarEventReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 日历ID。参见[日历ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/calendar-v4/calendar/introduction)
//
// 示例值：<EMAIL>
func (builder *SubscriptionCalendarEventReqBuilder) CalendarId(calendarId string) *SubscriptionCalendarEventReqBuilder {
	builder.apiReq.PathParams.Set("calendar_id", fmt.Sprint(calendarId))
	return builder
}

func (builder *SubscriptionCalendarEventReqBuilder) Build() *SubscriptionCalendarEventReq {
	req := &SubscriptionCalendarEventReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	return req
}

type SubscriptionCalendarEventReq struct {
	apiReq *larkcore.ApiReq
}

type SubscriptionCalendarEventResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *SubscriptionCalendarEventResp) Success() bool {
	return resp.Code == 0
}

type UnsubscriptionCalendarEventReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewUnsubscriptionCalendarEventReqBuilder() *UnsubscriptionCalendarEventReqBuilder {
	builder := &UnsubscriptionCalendarEventReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 日历ID。参见[日历ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/calendar-v4/calendar/introduction)
//
// 示例值：<EMAIL>
func (builder *UnsubscriptionCalendarEventReqBuilder) CalendarId(calendarId string) *UnsubscriptionCalendarEventReqBuilder {
	builder.apiReq.PathParams.Set("calendar_id", fmt.Sprint(calendarId))
	return builder
}

func (builder *UnsubscriptionCalendarEventReqBuilder) Build() *UnsubscriptionCalendarEventReq {
	req := &UnsubscriptionCalendarEventReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	return req
}

type UnsubscriptionCalendarEventReq struct {
	apiReq *larkcore.ApiReq
}

type UnsubscriptionCalendarEventResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *UnsubscriptionCalendarEventResp) Success() bool {
	return resp.Code == 0
}

type BatchDeleteCalendarEventAttendeeReqBodyBuilder struct {
	attendeeIds                []string // 要移除的参与人 ID 列表。参见[参与人ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/calendar-v4/calendar-event-attendee/introduction#4998889c)
	attendeeIdsFlag            bool
	deleteIds                  []*CalendarEventAttendeeId // 需要删除的参与人类型实体ID，作为attendee_ids字段的补充。
	deleteIdsFlag              bool
	needNotification           bool // 删除日程参与人时是否要给参与人发送bot通知，默认为true
	needNotificationFlag       bool
	instanceStartTimeAdmin     string // 使用管理员身份访问时要修改的实例
	instanceStartTimeAdminFlag bool
	isEnableAdmin              bool // 是否启用管理员身份(需先在管理后台设置某人为会议室管理员)
	isEnableAdminFlag          bool
}

func NewBatchDeleteCalendarEventAttendeeReqBodyBuilder() *BatchDeleteCalendarEventAttendeeReqBodyBuilder {
	builder := &BatchDeleteCalendarEventAttendeeReqBodyBuilder{}
	return builder
}

// 要移除的参与人 ID 列表。参见[参与人ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/calendar-v4/calendar-event-attendee/introduction#4998889c)
//
//示例值：["user_xxxxx", "chat_xxxxx", "resource_xxxxx", "third_party_xxxxx"]
func (builder *BatchDeleteCalendarEventAttendeeReqBodyBuilder) AttendeeIds(attendeeIds []string) *BatchDeleteCalendarEventAttendeeReqBodyBuilder {
	builder.attendeeIds = attendeeIds
	builder.attendeeIdsFlag = true
	return builder
}

// 需要删除的参与人类型实体ID，作为attendee_ids字段的补充。
//
//示例值：
func (builder *BatchDeleteCalendarEventAttendeeReqBodyBuilder) DeleteIds(deleteIds []*CalendarEventAttendeeId) *BatchDeleteCalendarEventAttendeeReqBodyBuilder {
	builder.deleteIds = deleteIds
	builder.deleteIdsFlag = true
	return builder
}

// 删除日程参与人时是否要给参与人发送bot通知，默认为true
//
//示例值：false
func (builder *BatchDeleteCalendarEventAttendeeReqBodyBuilder) NeedNotification(needNotification bool) *BatchDeleteCalendarEventAttendeeReqBodyBuilder {
	builder.needNotification = needNotification
	builder.needNotificationFlag = true
	return builder
}

// 使用管理员身份访问时要修改的实例
//
//示例值：1647320400
func (builder *BatchDeleteCalendarEventAttendeeReqBodyBuilder) InstanceStartTimeAdmin(instanceStartTimeAdmin string) *BatchDeleteCalendarEventAttendeeReqBodyBuilder {
	builder.instanceStartTimeAdmin = instanceStartTimeAdmin
	builder.instanceStartTimeAdminFlag = true
	return builder
}

// 是否启用管理员身份(需先在管理后台设置某人为会议室管理员)
//
//示例值：false
func (builder *BatchDeleteCalendarEventAttendeeReqBodyBuilder) IsEnableAdmin(isEnableAdmin bool) *BatchDeleteCalendarEventAttendeeReqBodyBuilder {
	builder.isEnableAdmin = isEnableAdmin
	builder.isEnableAdminFlag = true
	return builder
}

func (builder *BatchDeleteCalendarEventAttendeeReqBodyBuilder) Build() *BatchDeleteCalendarEventAttendeeReqBody {
	req := &BatchDeleteCalendarEventAttendeeReqBody{}
	if builder.attendeeIdsFlag {
		req.AttendeeIds = builder.attendeeIds
	}
	if builder.deleteIdsFlag {
		req.DeleteIds = builder.deleteIds
	}
	if builder.needNotificationFlag {
		req.NeedNotification = &builder.needNotification
	}
	if builder.instanceStartTimeAdminFlag {
		req.InstanceStartTimeAdmin = &builder.instanceStartTimeAdmin
	}
	if builder.isEnableAdminFlag {
		req.IsEnableAdmin = &builder.isEnableAdmin
	}
	return req
}

type BatchDeleteCalendarEventAttendeePathReqBodyBuilder struct {
	attendeeIds                []string // 要移除的参与人 ID 列表。参见[参与人ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/calendar-v4/calendar-event-attendee/introduction#4998889c)
	attendeeIdsFlag            bool
	deleteIds                  []*CalendarEventAttendeeId // 需要删除的参与人类型实体ID，作为attendee_ids字段的补充。
	deleteIdsFlag              bool
	needNotification           bool // 删除日程参与人时是否要给参与人发送bot通知，默认为true
	needNotificationFlag       bool
	instanceStartTimeAdmin     string // 使用管理员身份访问时要修改的实例
	instanceStartTimeAdminFlag bool
	isEnableAdmin              bool // 是否启用管理员身份(需先在管理后台设置某人为会议室管理员)
	isEnableAdminFlag          bool
}

func NewBatchDeleteCalendarEventAttendeePathReqBodyBuilder() *BatchDeleteCalendarEventAttendeePathReqBodyBuilder {
	builder := &BatchDeleteCalendarEventAttendeePathReqBodyBuilder{}
	return builder
}

// 要移除的参与人 ID 列表。参见[参与人ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/calendar-v4/calendar-event-attendee/introduction#4998889c)
//
// 示例值：["user_xxxxx", "chat_xxxxx", "resource_xxxxx", "third_party_xxxxx"]
func (builder *BatchDeleteCalendarEventAttendeePathReqBodyBuilder) AttendeeIds(attendeeIds []string) *BatchDeleteCalendarEventAttendeePathReqBodyBuilder {
	builder.attendeeIds = attendeeIds
	builder.attendeeIdsFlag = true
	return builder
}

// 需要删除的参与人类型实体ID，作为attendee_ids字段的补充。
//
// 示例值：
func (builder *BatchDeleteCalendarEventAttendeePathReqBodyBuilder) DeleteIds(deleteIds []*CalendarEventAttendeeId) *BatchDeleteCalendarEventAttendeePathReqBodyBuilder {
	builder.deleteIds = deleteIds
	builder.deleteIdsFlag = true
	return builder
}

// 删除日程参与人时是否要给参与人发送bot通知，默认为true
//
// 示例值：false
func (builder *BatchDeleteCalendarEventAttendeePathReqBodyBuilder) NeedNotification(needNotification bool) *BatchDeleteCalendarEventAttendeePathReqBodyBuilder {
	builder.needNotification = needNotification
	builder.needNotificationFlag = true
	return builder
}

// 使用管理员身份访问时要修改的实例
//
// 示例值：1647320400
func (builder *BatchDeleteCalendarEventAttendeePathReqBodyBuilder) InstanceStartTimeAdmin(instanceStartTimeAdmin string) *BatchDeleteCalendarEventAttendeePathReqBodyBuilder {
	builder.instanceStartTimeAdmin = instanceStartTimeAdmin
	builder.instanceStartTimeAdminFlag = true
	return builder
}

// 是否启用管理员身份(需先在管理后台设置某人为会议室管理员)
//
// 示例值：false
func (builder *BatchDeleteCalendarEventAttendeePathReqBodyBuilder) IsEnableAdmin(isEnableAdmin bool) *BatchDeleteCalendarEventAttendeePathReqBodyBuilder {
	builder.isEnableAdmin = isEnableAdmin
	builder.isEnableAdminFlag = true
	return builder
}

func (builder *BatchDeleteCalendarEventAttendeePathReqBodyBuilder) Build() (*BatchDeleteCalendarEventAttendeeReqBody, error) {
	req := &BatchDeleteCalendarEventAttendeeReqBody{}
	if builder.attendeeIdsFlag {
		req.AttendeeIds = builder.attendeeIds
	}
	if builder.deleteIdsFlag {
		req.DeleteIds = builder.deleteIds
	}
	if builder.needNotificationFlag {
		req.NeedNotification = &builder.needNotification
	}
	if builder.instanceStartTimeAdminFlag {
		req.InstanceStartTimeAdmin = &builder.instanceStartTimeAdmin
	}
	if builder.isEnableAdminFlag {
		req.IsEnableAdmin = &builder.isEnableAdmin
	}
	return req, nil
}

type BatchDeleteCalendarEventAttendeeReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *BatchDeleteCalendarEventAttendeeReqBody
}

func NewBatchDeleteCalendarEventAttendeeReqBuilder() *BatchDeleteCalendarEventAttendeeReqBuilder {
	builder := &BatchDeleteCalendarEventAttendeeReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 日历ID。参见[日历ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/calendar-v4/calendar/introduction)
//
// 示例值：<EMAIL>
func (builder *BatchDeleteCalendarEventAttendeeReqBuilder) CalendarId(calendarId string) *BatchDeleteCalendarEventAttendeeReqBuilder {
	builder.apiReq.PathParams.Set("calendar_id", fmt.Sprint(calendarId))
	return builder
}

// 日程ID。参见[日程ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/calendar-v4/calendar-event/introduction)
//
// 示例值：xxxxxxxxx_0
func (builder *BatchDeleteCalendarEventAttendeeReqBuilder) EventId(eventId string) *BatchDeleteCalendarEventAttendeeReqBuilder {
	builder.apiReq.PathParams.Set("event_id", fmt.Sprint(eventId))
	return builder
}

// 此次调用中使用的用户ID的类型
//
// 示例值：
func (builder *BatchDeleteCalendarEventAttendeeReqBuilder) UserIdType(userIdType string) *BatchDeleteCalendarEventAttendeeReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

// 批量删除日程的参与人。
func (builder *BatchDeleteCalendarEventAttendeeReqBuilder) Body(body *BatchDeleteCalendarEventAttendeeReqBody) *BatchDeleteCalendarEventAttendeeReqBuilder {
	builder.body = body
	return builder
}

func (builder *BatchDeleteCalendarEventAttendeeReqBuilder) Build() *BatchDeleteCalendarEventAttendeeReq {
	req := &BatchDeleteCalendarEventAttendeeReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.body
	return req
}

type BatchDeleteCalendarEventAttendeeReqBody struct {
	AttendeeIds            []string                   `json:"attendee_ids,omitempty"`              // 要移除的参与人 ID 列表。参见[参与人ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/calendar-v4/calendar-event-attendee/introduction#4998889c)
	DeleteIds              []*CalendarEventAttendeeId `json:"delete_ids,omitempty"`                // 需要删除的参与人类型实体ID，作为attendee_ids字段的补充。
	NeedNotification       *bool                      `json:"need_notification,omitempty"`         // 删除日程参与人时是否要给参与人发送bot通知，默认为true
	InstanceStartTimeAdmin *string                    `json:"instance_start_time_admin,omitempty"` // 使用管理员身份访问时要修改的实例
	IsEnableAdmin          *bool                      `json:"is_enable_admin,omitempty"`           // 是否启用管理员身份(需先在管理后台设置某人为会议室管理员)
}

type BatchDeleteCalendarEventAttendeeReq struct {
	apiReq *larkcore.ApiReq
	Body   *BatchDeleteCalendarEventAttendeeReqBody `body:""`
}

type BatchDeleteCalendarEventAttendeeResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *BatchDeleteCalendarEventAttendeeResp) Success() bool {
	return resp.Code == 0
}

type CreateCalendarEventAttendeeReqBodyBuilder struct {
	attendees                  []*CalendarEventAttendee // 新增参与人列表；;- 单次请求会议室的数量限制为100。
	attendeesFlag              bool
	needNotification           bool // 是否给参与人发送bot通知 默认为true
	needNotificationFlag       bool
	instanceStartTimeAdmin     string // 使用管理员身份访问时要修改的实例(仅用于重复日程修改其中的一个实例，非重复日程无需填此字段)
	instanceStartTimeAdminFlag bool
	isEnableAdmin              bool // 是否启用管理员身份(需先在管理后台设置某人为会议室管理员)
	isEnableAdminFlag          bool
}

func NewCreateCalendarEventAttendeeReqBodyBuilder() *CreateCalendarEventAttendeeReqBodyBuilder {
	builder := &CreateCalendarEventAttendeeReqBodyBuilder{}
	return builder
}

// 新增参与人列表；;- 单次请求会议室的数量限制为100。
//
//示例值：
func (builder *CreateCalendarEventAttendeeReqBodyBuilder) Attendees(attendees []*CalendarEventAttendee) *CreateCalendarEventAttendeeReqBodyBuilder {
	builder.attendees = attendees
	builder.attendeesFlag = true
	return builder
}

// 是否给参与人发送bot通知 默认为true
//
//示例值：false
func (builder *CreateCalendarEventAttendeeReqBodyBuilder) NeedNotification(needNotification bool) *CreateCalendarEventAttendeeReqBodyBuilder {
	builder.needNotification = needNotification
	builder.needNotificationFlag = true
	return builder
}

// 使用管理员身份访问时要修改的实例(仅用于重复日程修改其中的一个实例，非重复日程无需填此字段)
//
//示例值：1647320400
func (builder *CreateCalendarEventAttendeeReqBodyBuilder) InstanceStartTimeAdmin(instanceStartTimeAdmin string) *CreateCalendarEventAttendeeReqBodyBuilder {
	builder.instanceStartTimeAdmin = instanceStartTimeAdmin
	builder.instanceStartTimeAdminFlag = true
	return builder
}

// 是否启用管理员身份(需先在管理后台设置某人为会议室管理员)
//
//示例值：false
func (builder *CreateCalendarEventAttendeeReqBodyBuilder) IsEnableAdmin(isEnableAdmin bool) *CreateCalendarEventAttendeeReqBodyBuilder {
	builder.isEnableAdmin = isEnableAdmin
	builder.isEnableAdminFlag = true
	return builder
}

func (builder *CreateCalendarEventAttendeeReqBodyBuilder) Build() *CreateCalendarEventAttendeeReqBody {
	req := &CreateCalendarEventAttendeeReqBody{}
	if builder.attendeesFlag {
		req.Attendees = builder.attendees
	}
	if builder.needNotificationFlag {
		req.NeedNotification = &builder.needNotification
	}
	if builder.instanceStartTimeAdminFlag {
		req.InstanceStartTimeAdmin = &builder.instanceStartTimeAdmin
	}
	if builder.isEnableAdminFlag {
		req.IsEnableAdmin = &builder.isEnableAdmin
	}
	return req
}

type CreateCalendarEventAttendeePathReqBodyBuilder struct {
	attendees                  []*CalendarEventAttendee // 新增参与人列表；;- 单次请求会议室的数量限制为100。
	attendeesFlag              bool
	needNotification           bool // 是否给参与人发送bot通知 默认为true
	needNotificationFlag       bool
	instanceStartTimeAdmin     string // 使用管理员身份访问时要修改的实例(仅用于重复日程修改其中的一个实例，非重复日程无需填此字段)
	instanceStartTimeAdminFlag bool
	isEnableAdmin              bool // 是否启用管理员身份(需先在管理后台设置某人为会议室管理员)
	isEnableAdminFlag          bool
}

func NewCreateCalendarEventAttendeePathReqBodyBuilder() *CreateCalendarEventAttendeePathReqBodyBuilder {
	builder := &CreateCalendarEventAttendeePathReqBodyBuilder{}
	return builder
}

// 新增参与人列表；;- 单次请求会议室的数量限制为100。
//
// 示例值：
func (builder *CreateCalendarEventAttendeePathReqBodyBuilder) Attendees(attendees []*CalendarEventAttendee) *CreateCalendarEventAttendeePathReqBodyBuilder {
	builder.attendees = attendees
	builder.attendeesFlag = true
	return builder
}

// 是否给参与人发送bot通知 默认为true
//
// 示例值：false
func (builder *CreateCalendarEventAttendeePathReqBodyBuilder) NeedNotification(needNotification bool) *CreateCalendarEventAttendeePathReqBodyBuilder {
	builder.needNotification = needNotification
	builder.needNotificationFlag = true
	return builder
}

// 使用管理员身份访问时要修改的实例(仅用于重复日程修改其中的一个实例，非重复日程无需填此字段)
//
// 示例值：1647320400
func (builder *CreateCalendarEventAttendeePathReqBodyBuilder) InstanceStartTimeAdmin(instanceStartTimeAdmin string) *CreateCalendarEventAttendeePathReqBodyBuilder {
	builder.instanceStartTimeAdmin = instanceStartTimeAdmin
	builder.instanceStartTimeAdminFlag = true
	return builder
}

// 是否启用管理员身份(需先在管理后台设置某人为会议室管理员)
//
// 示例值：false
func (builder *CreateCalendarEventAttendeePathReqBodyBuilder) IsEnableAdmin(isEnableAdmin bool) *CreateCalendarEventAttendeePathReqBodyBuilder {
	builder.isEnableAdmin = isEnableAdmin
	builder.isEnableAdminFlag = true
	return builder
}

func (builder *CreateCalendarEventAttendeePathReqBodyBuilder) Build() (*CreateCalendarEventAttendeeReqBody, error) {
	req := &CreateCalendarEventAttendeeReqBody{}
	if builder.attendeesFlag {
		req.Attendees = builder.attendees
	}
	if builder.needNotificationFlag {
		req.NeedNotification = &builder.needNotification
	}
	if builder.instanceStartTimeAdminFlag {
		req.InstanceStartTimeAdmin = &builder.instanceStartTimeAdmin
	}
	if builder.isEnableAdminFlag {
		req.IsEnableAdmin = &builder.isEnableAdmin
	}
	return req, nil
}

type CreateCalendarEventAttendeeReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *CreateCalendarEventAttendeeReqBody
}

func NewCreateCalendarEventAttendeeReqBuilder() *CreateCalendarEventAttendeeReqBuilder {
	builder := &CreateCalendarEventAttendeeReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 日历ID。参见[日历ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/calendar-v4/calendar/introduction)
//
// 示例值：<EMAIL>
func (builder *CreateCalendarEventAttendeeReqBuilder) CalendarId(calendarId string) *CreateCalendarEventAttendeeReqBuilder {
	builder.apiReq.PathParams.Set("calendar_id", fmt.Sprint(calendarId))
	return builder
}

// 日程ID。参见[日程ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/calendar-v4/calendar-event/introduction)
//
// 示例值：xxxxxxxxx_0
func (builder *CreateCalendarEventAttendeeReqBuilder) EventId(eventId string) *CreateCalendarEventAttendeeReqBuilder {
	builder.apiReq.PathParams.Set("event_id", fmt.Sprint(eventId))
	return builder
}

// 此次调用中使用的用户ID的类型
//
// 示例值：
func (builder *CreateCalendarEventAttendeeReqBuilder) UserIdType(userIdType string) *CreateCalendarEventAttendeeReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

// 批量给日程添加参与人。
func (builder *CreateCalendarEventAttendeeReqBuilder) Body(body *CreateCalendarEventAttendeeReqBody) *CreateCalendarEventAttendeeReqBuilder {
	builder.body = body
	return builder
}

func (builder *CreateCalendarEventAttendeeReqBuilder) Build() *CreateCalendarEventAttendeeReq {
	req := &CreateCalendarEventAttendeeReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.body
	return req
}

type CreateCalendarEventAttendeeReqBody struct {
	Attendees              []*CalendarEventAttendee `json:"attendees,omitempty"`                 // 新增参与人列表；;- 单次请求会议室的数量限制为100。
	NeedNotification       *bool                    `json:"need_notification,omitempty"`         // 是否给参与人发送bot通知 默认为true
	InstanceStartTimeAdmin *string                  `json:"instance_start_time_admin,omitempty"` // 使用管理员身份访问时要修改的实例(仅用于重复日程修改其中的一个实例，非重复日程无需填此字段)
	IsEnableAdmin          *bool                    `json:"is_enable_admin,omitempty"`           // 是否启用管理员身份(需先在管理后台设置某人为会议室管理员)
}

type CreateCalendarEventAttendeeReq struct {
	apiReq *larkcore.ApiReq
	Body   *CreateCalendarEventAttendeeReqBody `body:""`
}

type CreateCalendarEventAttendeeRespData struct {
	Attendees []*CalendarEventAttendee `json:"attendees,omitempty"` // 新增参与人后的日程所有参与人列表
}

type CreateCalendarEventAttendeeResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *CreateCalendarEventAttendeeRespData `json:"data"` // 业务数据
}

func (resp *CreateCalendarEventAttendeeResp) Success() bool {
	return resp.Code == 0
}

type ListCalendarEventAttendeeReqBuilder struct {
	apiReq *larkcore.ApiReq
	limit  int // 最大返回多少记录，当使用迭代器访问时才有效
}

func NewListCalendarEventAttendeeReqBuilder() *ListCalendarEventAttendeeReqBuilder {
	builder := &ListCalendarEventAttendeeReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 最大返回多少记录，当使用迭代器访问时才有效
func (builder *ListCalendarEventAttendeeReqBuilder) Limit(limit int) *ListCalendarEventAttendeeReqBuilder {
	builder.limit = limit
	return builder
}

// 日历ID。参见[日历ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/calendar-v4/calendar/introduction)
//
// 示例值：<EMAIL>
func (builder *ListCalendarEventAttendeeReqBuilder) CalendarId(calendarId string) *ListCalendarEventAttendeeReqBuilder {
	builder.apiReq.PathParams.Set("calendar_id", fmt.Sprint(calendarId))
	return builder
}

// 日程ID。参见[日程ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/calendar-v4/calendar-event/introduction)
//
// 示例值：xxxxxxxxx_0
func (builder *ListCalendarEventAttendeeReqBuilder) EventId(eventId string) *ListCalendarEventAttendeeReqBuilder {
	builder.apiReq.PathParams.Set("event_id", fmt.Sprint(eventId))
	return builder
}

// 此次调用中使用的用户ID的类型
//
// 示例值：
func (builder *ListCalendarEventAttendeeReqBuilder) UserIdType(userIdType string) *ListCalendarEventAttendeeReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

//
//
// 示例值：780TRhwXXXXX
func (builder *ListCalendarEventAttendeeReqBuilder) PageToken(pageToken string) *ListCalendarEventAttendeeReqBuilder {
	builder.apiReq.QueryParams.Set("page_token", fmt.Sprint(pageToken))
	return builder
}

//
//
// 示例值：10
func (builder *ListCalendarEventAttendeeReqBuilder) PageSize(pageSize int) *ListCalendarEventAttendeeReqBuilder {
	builder.apiReq.QueryParams.Set("page_size", fmt.Sprint(pageSize))
	return builder
}

func (builder *ListCalendarEventAttendeeReqBuilder) Build() *ListCalendarEventAttendeeReq {
	req := &ListCalendarEventAttendeeReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.Limit = builder.limit
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type ListCalendarEventAttendeeReq struct {
	apiReq *larkcore.ApiReq
	Limit  int // 最多返回多少记录，只有在使用迭代器访问时，才有效

}

type ListCalendarEventAttendeeRespData struct {
	Items     []*CalendarEventAttendee `json:"items,omitempty"`      // 日程的参与者列表
	HasMore   *bool                    `json:"has_more,omitempty"`   // 是否有下一页数据
	PageToken *string                  `json:"page_token,omitempty"` // 下次请求需要带上的分页标记，90 天有效期
}

type ListCalendarEventAttendeeResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *ListCalendarEventAttendeeRespData `json:"data"` // 业务数据
}

func (resp *ListCalendarEventAttendeeResp) Success() bool {
	return resp.Code == 0
}

type ListCalendarEventAttendeeChatMemberReqBuilder struct {
	apiReq *larkcore.ApiReq
	limit  int // 最大返回多少记录，当使用迭代器访问时才有效
}

func NewListCalendarEventAttendeeChatMemberReqBuilder() *ListCalendarEventAttendeeChatMemberReqBuilder {
	builder := &ListCalendarEventAttendeeChatMemberReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 最大返回多少记录，当使用迭代器访问时才有效
func (builder *ListCalendarEventAttendeeChatMemberReqBuilder) Limit(limit int) *ListCalendarEventAttendeeChatMemberReqBuilder {
	builder.limit = limit
	return builder
}

// 日历ID。参见[日历ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/calendar-v4/calendar/introduction)
//
// 示例值：<EMAIL>
func (builder *ListCalendarEventAttendeeChatMemberReqBuilder) CalendarId(calendarId string) *ListCalendarEventAttendeeChatMemberReqBuilder {
	builder.apiReq.PathParams.Set("calendar_id", fmt.Sprint(calendarId))
	return builder
}

// 日程ID。参见[日程ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/calendar-v4/calendar-event/introduction)
//
// 示例值：xxxxxxxxx_0
func (builder *ListCalendarEventAttendeeChatMemberReqBuilder) EventId(eventId string) *ListCalendarEventAttendeeChatMemberReqBuilder {
	builder.apiReq.PathParams.Set("event_id", fmt.Sprint(eventId))
	return builder
}

// 群参与人 ID。参见[参与人ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/calendar-v4/calendar-event-attendee/introduction#4998889c)
//
// 示例值：chat_xxxxxx
func (builder *ListCalendarEventAttendeeChatMemberReqBuilder) AttendeeId(attendeeId string) *ListCalendarEventAttendeeChatMemberReqBuilder {
	builder.apiReq.PathParams.Set("attendee_id", fmt.Sprint(attendeeId))
	return builder
}

//
//
// 示例值：23jhysaxxxxsysy
func (builder *ListCalendarEventAttendeeChatMemberReqBuilder) PageToken(pageToken string) *ListCalendarEventAttendeeChatMemberReqBuilder {
	builder.apiReq.QueryParams.Set("page_token", fmt.Sprint(pageToken))
	return builder
}

//
//
// 示例值：10
func (builder *ListCalendarEventAttendeeChatMemberReqBuilder) PageSize(pageSize int) *ListCalendarEventAttendeeChatMemberReqBuilder {
	builder.apiReq.QueryParams.Set("page_size", fmt.Sprint(pageSize))
	return builder
}

// 此次调用中使用的用户ID的类型
//
// 示例值：user_id
func (builder *ListCalendarEventAttendeeChatMemberReqBuilder) UserIdType(userIdType string) *ListCalendarEventAttendeeChatMemberReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

func (builder *ListCalendarEventAttendeeChatMemberReqBuilder) Build() *ListCalendarEventAttendeeChatMemberReq {
	req := &ListCalendarEventAttendeeChatMemberReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.Limit = builder.limit
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type ListCalendarEventAttendeeChatMemberReq struct {
	apiReq *larkcore.ApiReq
	Limit  int // 最多返回多少记录，只有在使用迭代器访问时，才有效

}

type ListCalendarEventAttendeeChatMemberRespData struct {
	Items     []*CalendarEventAttendeeChatMember `json:"items,omitempty"`      // 群中的群成员，当type为chat时有效；群成员不支持编辑
	HasMore   *bool                              `json:"has_more,omitempty"`   // 是否有下一页数据
	PageToken *string                            `json:"page_token,omitempty"` // 下次请求需要带上的分页标记
}

type ListCalendarEventAttendeeChatMemberResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *ListCalendarEventAttendeeChatMemberRespData `json:"data"` // 业务数据
}

func (resp *ListCalendarEventAttendeeChatMemberResp) Success() bool {
	return resp.Code == 0
}

type CreateExchangeBindingReqBuilder struct {
	apiReq          *larkcore.ApiReq
	exchangeBinding *ExchangeBinding
}

func NewCreateExchangeBindingReqBuilder() *CreateExchangeBindingReqBuilder {
	builder := &CreateExchangeBindingReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 此次调用中使用的用户ID的类型
//
// 示例值：user_id
func (builder *CreateExchangeBindingReqBuilder) UserIdType(userIdType string) *CreateExchangeBindingReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

// 本接口将Exchange账户绑定到飞书账户，进而支持Exchange日历的导入
func (builder *CreateExchangeBindingReqBuilder) ExchangeBinding(exchangeBinding *ExchangeBinding) *CreateExchangeBindingReqBuilder {
	builder.exchangeBinding = exchangeBinding
	return builder
}

func (builder *CreateExchangeBindingReqBuilder) Build() *CreateExchangeBindingReq {
	req := &CreateExchangeBindingReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.exchangeBinding
	return req
}

type CreateExchangeBindingReq struct {
	apiReq          *larkcore.ApiReq
	ExchangeBinding *ExchangeBinding `body:""`
}

type CreateExchangeBindingRespData struct {
	AdminAccount      *string `json:"admin_account,omitempty"`       // admin账户
	ExchangeAccount   *string `json:"exchange_account,omitempty"`    // 用户绑定的Exchange账户
	UserId            *string `json:"user_id,omitempty"`             // Exchange账户绑定user唯一标识id
	Status            *string `json:"status,omitempty"`              // Exchange账户同步状态
	ExchangeBindingId *string `json:"exchange_binding_id,omitempty"` // exchange绑定唯一标识id
}

type CreateExchangeBindingResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *CreateExchangeBindingRespData `json:"data"` // 业务数据
}

func (resp *CreateExchangeBindingResp) Success() bool {
	return resp.Code == 0
}

type DeleteExchangeBindingReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewDeleteExchangeBindingReqBuilder() *DeleteExchangeBindingReqBuilder {
	builder := &DeleteExchangeBindingReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// exchange绑定唯一标识id。参见[exchange绑定ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/calendar-v4/exchange_binding/introduction#12533d5e)
//
// 示例值：****************************************************************************************
func (builder *DeleteExchangeBindingReqBuilder) ExchangeBindingId(exchangeBindingId string) *DeleteExchangeBindingReqBuilder {
	builder.apiReq.PathParams.Set("exchange_binding_id", fmt.Sprint(exchangeBindingId))
	return builder
}

func (builder *DeleteExchangeBindingReqBuilder) Build() *DeleteExchangeBindingReq {
	req := &DeleteExchangeBindingReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	return req
}

type DeleteExchangeBindingReq struct {
	apiReq *larkcore.ApiReq
}

type DeleteExchangeBindingResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *DeleteExchangeBindingResp) Success() bool {
	return resp.Code == 0
}

type GetExchangeBindingReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewGetExchangeBindingReqBuilder() *GetExchangeBindingReqBuilder {
	builder := &GetExchangeBindingReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// exchange绑定唯一标识id。参见[exchange绑定ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/calendar-v4/exchange_binding/introduction#12533d5e)
//
// 示例值：****************************************************************************************
func (builder *GetExchangeBindingReqBuilder) ExchangeBindingId(exchangeBindingId string) *GetExchangeBindingReqBuilder {
	builder.apiReq.PathParams.Set("exchange_binding_id", fmt.Sprint(exchangeBindingId))
	return builder
}

// 此次调用中使用的用户ID的类型
//
// 示例值：user_id
func (builder *GetExchangeBindingReqBuilder) UserIdType(userIdType string) *GetExchangeBindingReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

func (builder *GetExchangeBindingReqBuilder) Build() *GetExchangeBindingReq {
	req := &GetExchangeBindingReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type GetExchangeBindingReq struct {
	apiReq *larkcore.ApiReq
}

type GetExchangeBindingRespData struct {
	AdminAccount      *string `json:"admin_account,omitempty"`       // admin账户
	ExchangeAccount   *string `json:"exchange_account,omitempty"`    // 用户绑定的Exchange账户
	UserId            *string `json:"user_id,omitempty"`             // Exchange账户绑定user唯一标识id
	Status            *string `json:"status,omitempty"`              // Exchange账户同步状态
	ExchangeBindingId *string `json:"exchange_binding_id,omitempty"` // Exchange绑定关系唯一标识ID
}

type GetExchangeBindingResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *GetExchangeBindingRespData `json:"data"` // 业务数据
}

func (resp *GetExchangeBindingResp) Success() bool {
	return resp.Code == 0
}

type ListFreebusyReqBodyBuilder struct {
	timeMin     string // 查询时段开始时间，需要url编码
	timeMinFlag bool
	timeMax     string // 查询时段结束时间，需要url编码
	timeMaxFlag bool
	userId      string // 用户user_id，输入时与 room_id 二选一。参见[用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction)
	userIdFlag  bool
	roomId      string // 会议室room_id，输入时与 user_id 二选一
	roomIdFlag  bool
}

func NewListFreebusyReqBodyBuilder() *ListFreebusyReqBodyBuilder {
	builder := &ListFreebusyReqBodyBuilder{}
	return builder
}

// 查询时段开始时间，需要url编码
//
//示例值：2020-10-28T12:00:00+08:00
func (builder *ListFreebusyReqBodyBuilder) TimeMin(timeMin string) *ListFreebusyReqBodyBuilder {
	builder.timeMin = timeMin
	builder.timeMinFlag = true
	return builder
}

// 查询时段结束时间，需要url编码
//
//示例值：2020-12-28T12:00:00+08:00
func (builder *ListFreebusyReqBodyBuilder) TimeMax(timeMax string) *ListFreebusyReqBodyBuilder {
	builder.timeMax = timeMax
	builder.timeMaxFlag = true
	return builder
}

// 用户user_id，输入时与 room_id 二选一。参见[用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction)
//
//示例值：ou_xxxxxxxxxx
func (builder *ListFreebusyReqBodyBuilder) UserId(userId string) *ListFreebusyReqBodyBuilder {
	builder.userId = userId
	builder.userIdFlag = true
	return builder
}

// 会议室room_id，输入时与 user_id 二选一
//
//示例值：omm_xxxxxxxxxx
func (builder *ListFreebusyReqBodyBuilder) RoomId(roomId string) *ListFreebusyReqBodyBuilder {
	builder.roomId = roomId
	builder.roomIdFlag = true
	return builder
}

func (builder *ListFreebusyReqBodyBuilder) Build() *ListFreebusyReqBody {
	req := &ListFreebusyReqBody{}
	if builder.timeMinFlag {
		req.TimeMin = &builder.timeMin
	}
	if builder.timeMaxFlag {
		req.TimeMax = &builder.timeMax
	}
	if builder.userIdFlag {
		req.UserId = &builder.userId
	}
	if builder.roomIdFlag {
		req.RoomId = &builder.roomId
	}
	return req
}

type ListFreebusyPathReqBodyBuilder struct {
	timeMin     string // 查询时段开始时间，需要url编码
	timeMinFlag bool
	timeMax     string // 查询时段结束时间，需要url编码
	timeMaxFlag bool
	userId      string // 用户user_id，输入时与 room_id 二选一。参见[用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction)
	userIdFlag  bool
	roomId      string // 会议室room_id，输入时与 user_id 二选一
	roomIdFlag  bool
}

func NewListFreebusyPathReqBodyBuilder() *ListFreebusyPathReqBodyBuilder {
	builder := &ListFreebusyPathReqBodyBuilder{}
	return builder
}

// 查询时段开始时间，需要url编码
//
// 示例值：2020-10-28T12:00:00+08:00
func (builder *ListFreebusyPathReqBodyBuilder) TimeMin(timeMin string) *ListFreebusyPathReqBodyBuilder {
	builder.timeMin = timeMin
	builder.timeMinFlag = true
	return builder
}

// 查询时段结束时间，需要url编码
//
// 示例值：2020-12-28T12:00:00+08:00
func (builder *ListFreebusyPathReqBodyBuilder) TimeMax(timeMax string) *ListFreebusyPathReqBodyBuilder {
	builder.timeMax = timeMax
	builder.timeMaxFlag = true
	return builder
}

// 用户user_id，输入时与 room_id 二选一。参见[用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction)
//
// 示例值：ou_xxxxxxxxxx
func (builder *ListFreebusyPathReqBodyBuilder) UserId(userId string) *ListFreebusyPathReqBodyBuilder {
	builder.userId = userId
	builder.userIdFlag = true
	return builder
}

// 会议室room_id，输入时与 user_id 二选一
//
// 示例值：omm_xxxxxxxxxx
func (builder *ListFreebusyPathReqBodyBuilder) RoomId(roomId string) *ListFreebusyPathReqBodyBuilder {
	builder.roomId = roomId
	builder.roomIdFlag = true
	return builder
}

func (builder *ListFreebusyPathReqBodyBuilder) Build() (*ListFreebusyReqBody, error) {
	req := &ListFreebusyReqBody{}
	if builder.timeMinFlag {
		req.TimeMin = &builder.timeMin
	}
	if builder.timeMaxFlag {
		req.TimeMax = &builder.timeMax
	}
	if builder.userIdFlag {
		req.UserId = &builder.userId
	}
	if builder.roomIdFlag {
		req.RoomId = &builder.roomId
	}
	return req, nil
}

type ListFreebusyReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *ListFreebusyReqBody
}

func NewListFreebusyReqBuilder() *ListFreebusyReqBuilder {
	builder := &ListFreebusyReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 此次调用中使用的用户ID的类型
//
// 示例值：
func (builder *ListFreebusyReqBuilder) UserIdType(userIdType string) *ListFreebusyReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

// 查询用户主日历或会议室的忙闲信息。
func (builder *ListFreebusyReqBuilder) Body(body *ListFreebusyReqBody) *ListFreebusyReqBuilder {
	builder.body = body
	return builder
}

func (builder *ListFreebusyReqBuilder) Build() *ListFreebusyReq {
	req := &ListFreebusyReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.body
	return req
}

type ListFreebusyReqBody struct {
	TimeMin *string `json:"time_min,omitempty"` // 查询时段开始时间，需要url编码
	TimeMax *string `json:"time_max,omitempty"` // 查询时段结束时间，需要url编码
	UserId  *string `json:"user_id,omitempty"`  // 用户user_id，输入时与 room_id 二选一。参见[用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction)
	RoomId  *string `json:"room_id,omitempty"`  // 会议室room_id，输入时与 user_id 二选一
}

type ListFreebusyReq struct {
	apiReq *larkcore.ApiReq
	Body   *ListFreebusyReqBody `body:""`
}

type ListFreebusyRespData struct {
	FreebusyList []*Freebusy `json:"freebusy_list,omitempty"` // 日历上请求时间区间内的忙碌时间段信息。
}

type ListFreebusyResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *ListFreebusyRespData `json:"data"` // 业务数据
}

func (resp *ListFreebusyResp) Success() bool {
	return resp.Code == 0
}

type GenerateCaldavConfSettingReqBodyBuilder struct {
	deviceName     string // 需要同步日历的设备名，在日历中展示用来管理密码
	deviceNameFlag bool
}

func NewGenerateCaldavConfSettingReqBodyBuilder() *GenerateCaldavConfSettingReqBodyBuilder {
	builder := &GenerateCaldavConfSettingReqBodyBuilder{}
	return builder
}

// 需要同步日历的设备名，在日历中展示用来管理密码
//
//示例值：iPhone
func (builder *GenerateCaldavConfSettingReqBodyBuilder) DeviceName(deviceName string) *GenerateCaldavConfSettingReqBodyBuilder {
	builder.deviceName = deviceName
	builder.deviceNameFlag = true
	return builder
}

func (builder *GenerateCaldavConfSettingReqBodyBuilder) Build() *GenerateCaldavConfSettingReqBody {
	req := &GenerateCaldavConfSettingReqBody{}
	if builder.deviceNameFlag {
		req.DeviceName = &builder.deviceName
	}
	return req
}

type GenerateCaldavConfSettingPathReqBodyBuilder struct {
	deviceName     string // 需要同步日历的设备名，在日历中展示用来管理密码
	deviceNameFlag bool
}

func NewGenerateCaldavConfSettingPathReqBodyBuilder() *GenerateCaldavConfSettingPathReqBodyBuilder {
	builder := &GenerateCaldavConfSettingPathReqBodyBuilder{}
	return builder
}

// 需要同步日历的设备名，在日历中展示用来管理密码
//
// 示例值：iPhone
func (builder *GenerateCaldavConfSettingPathReqBodyBuilder) DeviceName(deviceName string) *GenerateCaldavConfSettingPathReqBodyBuilder {
	builder.deviceName = deviceName
	builder.deviceNameFlag = true
	return builder
}

func (builder *GenerateCaldavConfSettingPathReqBodyBuilder) Build() (*GenerateCaldavConfSettingReqBody, error) {
	req := &GenerateCaldavConfSettingReqBody{}
	if builder.deviceNameFlag {
		req.DeviceName = &builder.deviceName
	}
	return req, nil
}

type GenerateCaldavConfSettingReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *GenerateCaldavConfSettingReqBody
}

func NewGenerateCaldavConfSettingReqBuilder() *GenerateCaldavConfSettingReqBuilder {
	builder := &GenerateCaldavConfSettingReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 用于为当前用户生成一个CalDAV账号密码，用于将飞书日历信息同步到本地设备日历。
func (builder *GenerateCaldavConfSettingReqBuilder) Body(body *GenerateCaldavConfSettingReqBody) *GenerateCaldavConfSettingReqBuilder {
	builder.body = body
	return builder
}

func (builder *GenerateCaldavConfSettingReqBuilder) Build() *GenerateCaldavConfSettingReq {
	req := &GenerateCaldavConfSettingReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.Body = builder.body
	return req
}

type GenerateCaldavConfSettingReqBody struct {
	DeviceName *string `json:"device_name,omitempty"` // 需要同步日历的设备名，在日历中展示用来管理密码
}

type GenerateCaldavConfSettingReq struct {
	apiReq *larkcore.ApiReq
	Body   *GenerateCaldavConfSettingReqBody `body:""`
}

type GenerateCaldavConfSettingRespData struct {
	Password      *string `json:"password,omitempty"`       // caldav密码
	UserName      *string `json:"user_name,omitempty"`      // caldav用户名
	ServerAddress *string `json:"server_address,omitempty"` // 服务器地址
	DeviceName    *string `json:"device_name,omitempty"`    // 设备名
}

type GenerateCaldavConfSettingResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *GenerateCaldavConfSettingRespData `json:"data"` // 业务数据
}

func (resp *GenerateCaldavConfSettingResp) Success() bool {
	return resp.Code == 0
}

type CreateTimeoffEventReqBuilder struct {
	apiReq       *larkcore.ApiReq
	timeoffEvent *TimeoffEvent
}

func NewCreateTimeoffEventReqBuilder() *CreateTimeoffEventReqBuilder {
	builder := &CreateTimeoffEventReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 此次调用中使用的用户ID的类型
//
// 示例值：
func (builder *CreateTimeoffEventReqBuilder) UserIdType(userIdType string) *CreateTimeoffEventReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

// 为指定用户创建一个请假日程，可以是一个普通请假日程，也可以是一个全天日程。;创建请假日程后，会在相应时间内，在用户个人签名页展示请假信息。
func (builder *CreateTimeoffEventReqBuilder) TimeoffEvent(timeoffEvent *TimeoffEvent) *CreateTimeoffEventReqBuilder {
	builder.timeoffEvent = timeoffEvent
	return builder
}

func (builder *CreateTimeoffEventReqBuilder) Build() *CreateTimeoffEventReq {
	req := &CreateTimeoffEventReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.timeoffEvent
	return req
}

type CreateTimeoffEventReq struct {
	apiReq       *larkcore.ApiReq
	TimeoffEvent *TimeoffEvent `body:""`
}

type CreateTimeoffEventRespData struct {
	TimeoffEventId *string `json:"timeoff_event_id,omitempty"` // 休假申请的唯一标识id
	UserId         *string `json:"user_id,omitempty"`          // 用户的user id
	Timezone       *string `json:"timezone,omitempty"`         // 休假人的时区
	StartTime      *string `json:"start_time,omitempty"`       // 休假开始时间（时间戳）/日期（2021-01-01），为日期时将生成全天日程，且与end_time对应，不符合将返回错误
	EndTime        *string `json:"end_time,omitempty"`         // 休假结束时间（时间戳）/日期（2021-01-01），为日期时将生成全天日程，与start_time对应，不符合将返回错误
	Title          *string `json:"title,omitempty"`            // 休假日程标题，可自定义例如："请假中(全天) / 1-Day Time Off"，"请假中(半天) / 0.5-Day Time Off"，"长期休假中 / Leave of Absence"，"请假中"
	Description    *string `json:"description,omitempty"`      // 休假日程描述，可自定义,例如：;"若拒绝或删除此日程，飞书中相应的“请假”标签将自动消失，而请假系统中的休假申请不会被撤销。;;If the event is rejected or deleted, corresponding "On Leave" tag in Feishu will disappear, while the leave request in the time off system will not be revoked."
}

type CreateTimeoffEventResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *CreateTimeoffEventRespData `json:"data"` // 业务数据
}

func (resp *CreateTimeoffEventResp) Success() bool {
	return resp.Code == 0
}

type DeleteTimeoffEventReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewDeleteTimeoffEventReqBuilder() *DeleteTimeoffEventReqBuilder {
	builder := &DeleteTimeoffEventReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 休假申请的唯一标识id。参见[请假日程ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/calendar-v4/timeoff_event/introduction#b6611a02)
//
// 示例值：timeoff:XXXXXX-XXXX-0917-1623-aa493d591a39
func (builder *DeleteTimeoffEventReqBuilder) TimeoffEventId(timeoffEventId string) *DeleteTimeoffEventReqBuilder {
	builder.apiReq.PathParams.Set("timeoff_event_id", fmt.Sprint(timeoffEventId))
	return builder
}

func (builder *DeleteTimeoffEventReqBuilder) Build() *DeleteTimeoffEventReq {
	req := &DeleteTimeoffEventReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	return req
}

type DeleteTimeoffEventReq struct {
	apiReq *larkcore.ApiReq
}

type DeleteTimeoffEventResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *DeleteTimeoffEventResp) Success() bool {
	return resp.Code == 0
}

type P2CalendarChangedV4Data struct {
	UserIdList []*UserId `json:"user_id_list,omitempty"` // 需要推送事件的用户列表
}

type P2CalendarChangedV4 struct {
	*larkevent.EventV2Base                          // 事件基础数据
	*larkevent.EventReq                             // 请求原生数据
	Event                  *P2CalendarChangedV4Data `json:"event"` // 事件内容
}

func (m *P2CalendarChangedV4) RawReq(req *larkevent.EventReq) {
	m.EventReq = req
}

type P2CalendarAclCreatedV4Data struct {
	AclId      *string        `json:"acl_id,omitempty"`       // acl资源ID
	Role       *string        `json:"role,omitempty"`         // 对日历的访问权限
	Scope      *AclScopeEvent `json:"scope,omitempty"`        // 权限范围
	UserIdList []*UserId      `json:"user_id_list,omitempty"` // 需要推送事件的用户列表
}

type P2CalendarAclCreatedV4 struct {
	*larkevent.EventV2Base                             // 事件基础数据
	*larkevent.EventReq                                // 请求原生数据
	Event                  *P2CalendarAclCreatedV4Data `json:"event"` // 事件内容
}

func (m *P2CalendarAclCreatedV4) RawReq(req *larkevent.EventReq) {
	m.EventReq = req
}

type P2CalendarAclDeletedV4Data struct {
	AclId      *string        `json:"acl_id,omitempty"`       // acl资源ID
	Role       *string        `json:"role,omitempty"`         // 对日历的访问权限
	Scope      *AclScopeEvent `json:"scope,omitempty"`        // 权限范围
	UserIdList []*UserId      `json:"user_id_list,omitempty"` // 需要推送事件的用户列表
}

type P2CalendarAclDeletedV4 struct {
	*larkevent.EventV2Base                             // 事件基础数据
	*larkevent.EventReq                                // 请求原生数据
	Event                  *P2CalendarAclDeletedV4Data `json:"event"` // 事件内容
}

func (m *P2CalendarAclDeletedV4) RawReq(req *larkevent.EventReq) {
	m.EventReq = req
}

type P2CalendarEventChangedV4Data struct {
	CalendarId *string   `json:"calendar_id,omitempty"`  // 日历id
	UserIdList []*UserId `json:"user_id_list,omitempty"` // 需要推送事件的用户列表
}

type P2CalendarEventChangedV4 struct {
	*larkevent.EventV2Base                               // 事件基础数据
	*larkevent.EventReq                                  // 请求原生数据
	Event                  *P2CalendarEventChangedV4Data `json:"event"` // 事件内容
}

func (m *P2CalendarEventChangedV4) RawReq(req *larkevent.EventReq) {
	m.EventReq = req
}

type SearchCalendarIterator struct {
	nextPageToken *string
	items         []*Calendar
	index         int
	limit         int
	ctx           context.Context
	req           *SearchCalendarReq
	listFunc      func(ctx context.Context, req *SearchCalendarReq, options ...larkcore.RequestOptionFunc) (*SearchCalendarResp, error)
	options       []larkcore.RequestOptionFunc
	curlNum       int
}

func (iterator *SearchCalendarIterator) Next() (bool, *Calendar, error) {
	// 达到最大量，则返回
	if iterator.limit > 0 && iterator.curlNum >= iterator.limit {
		return false, nil, nil
	}

	// 为0则拉取数据
	if iterator.index == 0 || iterator.index >= len(iterator.items) {
		if iterator.index != 0 && iterator.nextPageToken == nil {
			return false, nil, nil
		}
		if iterator.nextPageToken != nil {
			iterator.req.apiReq.QueryParams.Set("page_token", *iterator.nextPageToken)
		}
		resp, err := iterator.listFunc(iterator.ctx, iterator.req, iterator.options...)
		if err != nil {
			return false, nil, err
		}

		if resp.Code != 0 {
			return false, nil, errors.New(fmt.Sprintf("Code:%d,Msg:%s", resp.Code, resp.Msg))
		}

		if len(resp.Data.Items) == 0 {
			return false, nil, nil
		}

		iterator.nextPageToken = resp.Data.PageToken
		iterator.items = resp.Data.Items
		iterator.index = 0
	}

	block := iterator.items[iterator.index]
	iterator.index++
	iterator.curlNum++
	return true, block, nil
}

func (iterator *SearchCalendarIterator) NextPageToken() *string {
	return iterator.nextPageToken
}

type ListCalendarAclIterator struct {
	nextPageToken *string
	items         []*CalendarAcl
	index         int
	limit         int
	ctx           context.Context
	req           *ListCalendarAclReq
	listFunc      func(ctx context.Context, req *ListCalendarAclReq, options ...larkcore.RequestOptionFunc) (*ListCalendarAclResp, error)
	options       []larkcore.RequestOptionFunc
	curlNum       int
}

func (iterator *ListCalendarAclIterator) Next() (bool, *CalendarAcl, error) {
	// 达到最大量，则返回
	if iterator.limit > 0 && iterator.curlNum >= iterator.limit {
		return false, nil, nil
	}

	// 为0则拉取数据
	if iterator.index == 0 || iterator.index >= len(iterator.items) {
		if iterator.index != 0 && iterator.nextPageToken == nil {
			return false, nil, nil
		}
		if iterator.nextPageToken != nil {
			iterator.req.apiReq.QueryParams.Set("page_token", *iterator.nextPageToken)
		}
		resp, err := iterator.listFunc(iterator.ctx, iterator.req, iterator.options...)
		if err != nil {
			return false, nil, err
		}

		if resp.Code != 0 {
			return false, nil, errors.New(fmt.Sprintf("Code:%d,Msg:%s", resp.Code, resp.Msg))
		}

		if len(resp.Data.Acls) == 0 {
			return false, nil, nil
		}

		iterator.nextPageToken = resp.Data.PageToken
		iterator.items = resp.Data.Acls
		iterator.index = 0
	}

	block := iterator.items[iterator.index]
	iterator.index++
	iterator.curlNum++
	return true, block, nil
}

func (iterator *ListCalendarAclIterator) NextPageToken() *string {
	return iterator.nextPageToken
}

type SearchCalendarEventIterator struct {
	nextPageToken *string
	items         []*CalendarEvent
	index         int
	limit         int
	ctx           context.Context
	req           *SearchCalendarEventReq
	listFunc      func(ctx context.Context, req *SearchCalendarEventReq, options ...larkcore.RequestOptionFunc) (*SearchCalendarEventResp, error)
	options       []larkcore.RequestOptionFunc
	curlNum       int
}

func (iterator *SearchCalendarEventIterator) Next() (bool, *CalendarEvent, error) {
	// 达到最大量，则返回
	if iterator.limit > 0 && iterator.curlNum >= iterator.limit {
		return false, nil, nil
	}

	// 为0则拉取数据
	if iterator.index == 0 || iterator.index >= len(iterator.items) {
		if iterator.index != 0 && iterator.nextPageToken == nil {
			return false, nil, nil
		}
		if iterator.nextPageToken != nil {
			iterator.req.apiReq.QueryParams.Set("page_token", *iterator.nextPageToken)
		}
		resp, err := iterator.listFunc(iterator.ctx, iterator.req, iterator.options...)
		if err != nil {
			return false, nil, err
		}

		if resp.Code != 0 {
			return false, nil, errors.New(fmt.Sprintf("Code:%d,Msg:%s", resp.Code, resp.Msg))
		}

		if len(resp.Data.Items) == 0 {
			return false, nil, nil
		}

		iterator.nextPageToken = resp.Data.PageToken
		iterator.items = resp.Data.Items
		iterator.index = 0
	}

	block := iterator.items[iterator.index]
	iterator.index++
	iterator.curlNum++
	return true, block, nil
}

func (iterator *SearchCalendarEventIterator) NextPageToken() *string {
	return iterator.nextPageToken
}

type ListCalendarEventAttendeeIterator struct {
	nextPageToken *string
	items         []*CalendarEventAttendee
	index         int
	limit         int
	ctx           context.Context
	req           *ListCalendarEventAttendeeReq
	listFunc      func(ctx context.Context, req *ListCalendarEventAttendeeReq, options ...larkcore.RequestOptionFunc) (*ListCalendarEventAttendeeResp, error)
	options       []larkcore.RequestOptionFunc
	curlNum       int
}

func (iterator *ListCalendarEventAttendeeIterator) Next() (bool, *CalendarEventAttendee, error) {
	// 达到最大量，则返回
	if iterator.limit > 0 && iterator.curlNum >= iterator.limit {
		return false, nil, nil
	}

	// 为0则拉取数据
	if iterator.index == 0 || iterator.index >= len(iterator.items) {
		if iterator.index != 0 && iterator.nextPageToken == nil {
			return false, nil, nil
		}
		if iterator.nextPageToken != nil {
			iterator.req.apiReq.QueryParams.Set("page_token", *iterator.nextPageToken)
		}
		resp, err := iterator.listFunc(iterator.ctx, iterator.req, iterator.options...)
		if err != nil {
			return false, nil, err
		}

		if resp.Code != 0 {
			return false, nil, errors.New(fmt.Sprintf("Code:%d,Msg:%s", resp.Code, resp.Msg))
		}

		if len(resp.Data.Items) == 0 {
			return false, nil, nil
		}

		iterator.nextPageToken = resp.Data.PageToken
		iterator.items = resp.Data.Items
		iterator.index = 0
	}

	block := iterator.items[iterator.index]
	iterator.index++
	iterator.curlNum++
	return true, block, nil
}

func (iterator *ListCalendarEventAttendeeIterator) NextPageToken() *string {
	return iterator.nextPageToken
}

type ListCalendarEventAttendeeChatMemberIterator struct {
	nextPageToken *string
	items         []*CalendarEventAttendeeChatMember
	index         int
	limit         int
	ctx           context.Context
	req           *ListCalendarEventAttendeeChatMemberReq
	listFunc      func(ctx context.Context, req *ListCalendarEventAttendeeChatMemberReq, options ...larkcore.RequestOptionFunc) (*ListCalendarEventAttendeeChatMemberResp, error)
	options       []larkcore.RequestOptionFunc
	curlNum       int
}

func (iterator *ListCalendarEventAttendeeChatMemberIterator) Next() (bool, *CalendarEventAttendeeChatMember, error) {
	// 达到最大量，则返回
	if iterator.limit > 0 && iterator.curlNum >= iterator.limit {
		return false, nil, nil
	}

	// 为0则拉取数据
	if iterator.index == 0 || iterator.index >= len(iterator.items) {
		if iterator.index != 0 && iterator.nextPageToken == nil {
			return false, nil, nil
		}
		if iterator.nextPageToken != nil {
			iterator.req.apiReq.QueryParams.Set("page_token", *iterator.nextPageToken)
		}
		resp, err := iterator.listFunc(iterator.ctx, iterator.req, iterator.options...)
		if err != nil {
			return false, nil, err
		}

		if resp.Code != 0 {
			return false, nil, errors.New(fmt.Sprintf("Code:%d,Msg:%s", resp.Code, resp.Msg))
		}

		if len(resp.Data.Items) == 0 {
			return false, nil, nil
		}

		iterator.nextPageToken = resp.Data.PageToken
		iterator.items = resp.Data.Items
		iterator.index = 0
	}

	block := iterator.items[iterator.index]
	iterator.index++
	iterator.curlNum++
	return true, block, nil
}

func (iterator *ListCalendarEventAttendeeChatMemberIterator) NextPageToken() *string {
	return iterator.nextPageToken
}
