// Package okr code generated by oapi sdk gen
/*
 * MIT License
 *
 * Copyright (c) 2022 Lark Technologies Pte. Ltd.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice, shall be included in all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */

package larkokr

import (
	"io"

	"bytes"

	"fmt"

	"github.com/larksuite/oapi-sdk-go/v3/core"
)

const (
	TargetTypeObjective = 2 // okr的O
	TargetTypeKeyResult = 3 // okr的KR

)

const (
	UserIdTypeUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypeUnionId = "union_id" // 以union_id来识别用户
	UserIdTypeOpenId  = "open_id"  // 以open_id来识别用户
)

const (
	UserIdTypeGetMetricSourceTableItemUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypeGetMetricSourceTableItemUnionId = "union_id" // 以union_id来识别用户
	UserIdTypeGetMetricSourceTableItemOpenId  = "open_id"  // 以open_id来识别用户
)

const (
	UserIdTypeListMetricSourceTableItemUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypeListMetricSourceTableItemUnionId = "union_id" // 以union_id来识别用户
	UserIdTypeListMetricSourceTableItemOpenId  = "open_id"  // 以open_id来识别用户
)

const (
	UserIdTypePatchMetricSourceTableItemUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypePatchMetricSourceTableItemUnionId = "union_id" // 以union_id来识别用户
	UserIdTypePatchMetricSourceTableItemOpenId  = "open_id"  // 以open_id来识别用户
)

const (
	UserIdTypeBatchGetOkrUserId        = "user_id"         // 以user_id来识别用户
	UserIdTypeBatchGetOkrUnionId       = "union_id"        // 以union_id来识别用户
	UserIdTypeBatchGetOkrOpenId        = "open_id"         // 以open_id来识别用户
	UserIdTypeBatchGetOkrPeopleAdminId = "people_admin_id" // 以people_admin_id来识别用户
)

const (
	TargetTypeCreateProgressRecordObjective = 2 // okr的O
	TargetTypeCreateProgressRecordKeyResult = 3 // okr的KR

)

const (
	UserIdTypeCreateProgressRecordUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypeCreateProgressRecordUnionId = "union_id" // 以union_id来识别用户
	UserIdTypeCreateProgressRecordOpenId  = "open_id"  // 以open_id来识别用户
)

const (
	UserIdTypeGetProgressRecordUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypeGetProgressRecordUnionId = "union_id" // 以union_id来识别用户
	UserIdTypeGetProgressRecordOpenId  = "open_id"  // 以open_id来识别用户
)

const (
	UserIdTypeUpdateProgressRecordUserId  = "user_id"  // 以user_id来识别用户
	UserIdTypeUpdateProgressRecordUnionId = "union_id" // 以union_id来识别用户
	UserIdTypeUpdateProgressRecordOpenId  = "open_id"  // 以open_id来识别用户
)

const (
	UserIdTypeQueryReviewUserId        = "user_id"         // 以user_id来识别用户
	UserIdTypeQueryReviewUnionId       = "union_id"        // 以union_id来识别用户
	UserIdTypeQueryReviewOpenId        = "open_id"         // 以open_id来识别用户
	UserIdTypeQueryReviewPeopleAdminId = "people_admin_id" // 以people_admin_id来识别用户
)

const (
	UserIdTypeListUserOkrUserId        = "user_id"         // 以user_id来识别用户
	UserIdTypeListUserOkrUnionId       = "union_id"        // 以union_id来识别用户
	UserIdTypeListUserOkrOpenId        = "open_id"         // 以open_id来识别用户
	UserIdTypeListUserOkrPeopleAdminId = "people_admin_id" // 以people_admin_id来识别用户
)

type AlignObjective struct {
	Id     *string `json:"id,omitempty"`      // Objective ID
	OkrId  *string `json:"okr_id,omitempty"`  // OKR ID
	UserId *string `json:"user_id,omitempty"` // 用户 UUID
}

type AlignObjectiveBuilder struct {
	id         string // Objective ID
	idFlag     bool
	okrId      string // OKR ID
	okrIdFlag  bool
	userId     string // 用户 UUID
	userIdFlag bool
}

func NewAlignObjectiveBuilder() *AlignObjectiveBuilder {
	builder := &AlignObjectiveBuilder{}
	return builder
}

// Objective ID
//
// 示例值：
func (builder *AlignObjectiveBuilder) Id(id string) *AlignObjectiveBuilder {
	builder.id = id
	builder.idFlag = true
	return builder
}

// OKR ID
//
// 示例值：
func (builder *AlignObjectiveBuilder) OkrId(okrId string) *AlignObjectiveBuilder {
	builder.okrId = okrId
	builder.okrIdFlag = true
	return builder
}

// 用户 UUID
//
// 示例值：
func (builder *AlignObjectiveBuilder) UserId(userId string) *AlignObjectiveBuilder {
	builder.userId = userId
	builder.userIdFlag = true
	return builder
}

func (builder *AlignObjectiveBuilder) Build() *AlignObjective {
	req := &AlignObjective{}
	if builder.idFlag {
		req.Id = &builder.id

	}
	if builder.okrIdFlag {
		req.OkrId = &builder.okrId

	}
	if builder.userIdFlag {
		req.UserId = &builder.userId

	}
	return req
}

type ContentBlock struct {
	Blocks []*ContentBlockElement `json:"blocks,omitempty"` // 文档结构是按行排列的，每行内容是一个 Block
}

type ContentBlockBuilder struct {
	blocks     []*ContentBlockElement // 文档结构是按行排列的，每行内容是一个 Block
	blocksFlag bool
}

func NewContentBlockBuilder() *ContentBlockBuilder {
	builder := &ContentBlockBuilder{}
	return builder
}

// 文档结构是按行排列的，每行内容是一个 Block
//
// 示例值：
func (builder *ContentBlockBuilder) Blocks(blocks []*ContentBlockElement) *ContentBlockBuilder {
	builder.blocks = blocks
	builder.blocksFlag = true
	return builder
}

func (builder *ContentBlockBuilder) Build() *ContentBlock {
	req := &ContentBlock{}
	if builder.blocksFlag {
		req.Blocks = builder.blocks
	}
	return req
}

type ContentBlockElement struct {
	Type      *string           `json:"type,omitempty"`      // 文档元素类型
	Paragraph *ContentParagraph `json:"paragraph,omitempty"` // 文本段落
	Gallery   *ContentGallery   `json:"gallery,omitempty"`   // 图片
}

type ContentBlockElementBuilder struct {
	type_         string // 文档元素类型
	typeFlag      bool
	paragraph     *ContentParagraph // 文本段落
	paragraphFlag bool
	gallery       *ContentGallery // 图片
	galleryFlag   bool
}

func NewContentBlockElementBuilder() *ContentBlockElementBuilder {
	builder := &ContentBlockElementBuilder{}
	return builder
}

// 文档元素类型
//
// 示例值：paragraph
func (builder *ContentBlockElementBuilder) Type(type_ string) *ContentBlockElementBuilder {
	builder.type_ = type_
	builder.typeFlag = true
	return builder
}

// 文本段落
//
// 示例值：
func (builder *ContentBlockElementBuilder) Paragraph(paragraph *ContentParagraph) *ContentBlockElementBuilder {
	builder.paragraph = paragraph
	builder.paragraphFlag = true
	return builder
}

// 图片
//
// 示例值：
func (builder *ContentBlockElementBuilder) Gallery(gallery *ContentGallery) *ContentBlockElementBuilder {
	builder.gallery = gallery
	builder.galleryFlag = true
	return builder
}

func (builder *ContentBlockElementBuilder) Build() *ContentBlockElement {
	req := &ContentBlockElement{}
	if builder.typeFlag {
		req.Type = &builder.type_

	}
	if builder.paragraphFlag {
		req.Paragraph = builder.paragraph
	}
	if builder.galleryFlag {
		req.Gallery = builder.gallery
	}
	return req
}

type ContentColor struct {
	Red   *int     `json:"red,omitempty"`   // 红 取值范围[0,255]
	Green *int     `json:"green,omitempty"` // 绿 取值范围[0,255]
	Blue  *int     `json:"blue,omitempty"`  // 蓝 取值范围[0,255]
	Alpha *float64 `json:"alpha,omitempty"` // 透明度 取值范围[0,1]
}

type ContentColorBuilder struct {
	red       int // 红 取值范围[0,255]
	redFlag   bool
	green     int // 绿 取值范围[0,255]
	greenFlag bool
	blue      int // 蓝 取值范围[0,255]
	blueFlag  bool
	alpha     float64 // 透明度 取值范围[0,1]
	alphaFlag bool
}

func NewContentColorBuilder() *ContentColorBuilder {
	builder := &ContentColorBuilder{}
	return builder
}

// 红 取值范围[0,255]
//
// 示例值：216
func (builder *ContentColorBuilder) Red(red int) *ContentColorBuilder {
	builder.red = red
	builder.redFlag = true
	return builder
}

// 绿 取值范围[0,255]
//
// 示例值：191
func (builder *ContentColorBuilder) Green(green int) *ContentColorBuilder {
	builder.green = green
	builder.greenFlag = true
	return builder
}

// 蓝 取值范围[0,255]
//
// 示例值：188
func (builder *ContentColorBuilder) Blue(blue int) *ContentColorBuilder {
	builder.blue = blue
	builder.blueFlag = true
	return builder
}

// 透明度 取值范围[0,1]
//
// 示例值：0.1
func (builder *ContentColorBuilder) Alpha(alpha float64) *ContentColorBuilder {
	builder.alpha = alpha
	builder.alphaFlag = true
	return builder
}

func (builder *ContentColorBuilder) Build() *ContentColor {
	req := &ContentColor{}
	if builder.redFlag {
		req.Red = &builder.red

	}
	if builder.greenFlag {
		req.Green = &builder.green

	}
	if builder.blueFlag {
		req.Blue = &builder.blue

	}
	if builder.alphaFlag {
		req.Alpha = &builder.alpha

	}
	return req
}

type ContentDocsLink struct {
	Url   *string `json:"url,omitempty"`   // 飞书云文档链接地址
	Title *string `json:"title,omitempty"` // 飞书云文档标题
}

type ContentDocsLinkBuilder struct {
	url       string // 飞书云文档链接地址
	urlFlag   bool
	title     string // 飞书云文档标题
	titleFlag bool
}

func NewContentDocsLinkBuilder() *ContentDocsLinkBuilder {
	builder := &ContentDocsLinkBuilder{}
	return builder
}

// 飞书云文档链接地址
//
// 示例值：https://xxx.feishu.cn/docx/xxxxxxxx
func (builder *ContentDocsLinkBuilder) Url(url string) *ContentDocsLinkBuilder {
	builder.url = url
	builder.urlFlag = true
	return builder
}

// 飞书云文档标题
//
// 示例值：项目说明文档
func (builder *ContentDocsLinkBuilder) Title(title string) *ContentDocsLinkBuilder {
	builder.title = title
	builder.titleFlag = true
	return builder
}

func (builder *ContentDocsLinkBuilder) Build() *ContentDocsLink {
	req := &ContentDocsLink{}
	if builder.urlFlag {
		req.Url = &builder.url

	}
	if builder.titleFlag {
		req.Title = &builder.title

	}
	return req
}

type ContentGallery struct {
	ImageList []*ContentImageItem `json:"imageList,omitempty"` // 图片元素
}

type ContentGalleryBuilder struct {
	imageList     []*ContentImageItem // 图片元素
	imageListFlag bool
}

func NewContentGalleryBuilder() *ContentGalleryBuilder {
	builder := &ContentGalleryBuilder{}
	return builder
}

// 图片元素
//
// 示例值：
func (builder *ContentGalleryBuilder) ImageList(imageList []*ContentImageItem) *ContentGalleryBuilder {
	builder.imageList = imageList
	builder.imageListFlag = true
	return builder
}

func (builder *ContentGalleryBuilder) Build() *ContentGallery {
	req := &ContentGallery{}
	if builder.imageListFlag {
		req.ImageList = builder.imageList
	}
	return req
}

type ContentImageItem struct {
	FileToken *string  `json:"fileToken,omitempty"` // 图片 token，通过上传图片接口获取
	Src       *string  `json:"src,omitempty"`       // 图片链接
	Width     *float64 `json:"width,omitempty"`     // 图片宽，单位px
	Height    *float64 `json:"height,omitempty"`    // 图片高，单位px
}

type ContentImageItemBuilder struct {
	fileToken     string // 图片 token，通过上传图片接口获取
	fileTokenFlag bool
	src           string // 图片链接
	srcFlag       bool
	width         float64 // 图片宽，单位px
	widthFlag     bool
	height        float64 // 图片高，单位px
	heightFlag    bool
}

func NewContentImageItemBuilder() *ContentImageItemBuilder {
	builder := &ContentImageItemBuilder{}
	return builder
}

// 图片 token，通过上传图片接口获取
//
// 示例值：boxcnOj88GDkmWGm2zsTyCBqoLb
func (builder *ContentImageItemBuilder) FileToken(fileToken string) *ContentImageItemBuilder {
	builder.fileToken = fileToken
	builder.fileTokenFlag = true
	return builder
}

// 图片链接
//
// 示例值：https://bytedance.feishu.cn/drive/home/
func (builder *ContentImageItemBuilder) Src(src string) *ContentImageItemBuilder {
	builder.src = src
	builder.srcFlag = true
	return builder
}

// 图片宽，单位px
//
// 示例值：458
func (builder *ContentImageItemBuilder) Width(width float64) *ContentImageItemBuilder {
	builder.width = width
	builder.widthFlag = true
	return builder
}

// 图片高，单位px
//
// 示例值：372
func (builder *ContentImageItemBuilder) Height(height float64) *ContentImageItemBuilder {
	builder.height = height
	builder.heightFlag = true
	return builder
}

func (builder *ContentImageItemBuilder) Build() *ContentImageItem {
	req := &ContentImageItem{}
	if builder.fileTokenFlag {
		req.FileToken = &builder.fileToken

	}
	if builder.srcFlag {
		req.Src = &builder.src

	}
	if builder.widthFlag {
		req.Width = &builder.width

	}
	if builder.heightFlag {
		req.Height = &builder.height

	}
	return req
}

type ContentLink struct {
	Url *string `json:"url,omitempty"` // 链接地址
}

type ContentLinkBuilder struct {
	url     string // 链接地址
	urlFlag bool
}

func NewContentLinkBuilder() *ContentLinkBuilder {
	builder := &ContentLinkBuilder{}
	return builder
}

// 链接地址
//
// 示例值：https://www.xxxxx.com/
func (builder *ContentLinkBuilder) Url(url string) *ContentLinkBuilder {
	builder.url = url
	builder.urlFlag = true
	return builder
}

func (builder *ContentLinkBuilder) Build() *ContentLink {
	req := &ContentLink{}
	if builder.urlFlag {
		req.Url = &builder.url

	}
	return req
}

type ContentList struct {
	Type        *string `json:"type,omitempty"`        // 列表类型
	IndentLevel *int    `json:"indentLevel,omitempty"` // 列表的缩进级别，支持指定一行的缩进 除代码块以外的列表都支持设置缩进，支持 1-16 级缩进，取值范围：[1,16]
	Number      *int    `json:"number,omitempty"`      // 用于指定列表的行号，仅对有序列表和代码块生效 如果为有序列表设置了缩进，行号可能会显示为字母或者罗马数字
}

type ContentListBuilder struct {
	type_           string // 列表类型
	typeFlag        bool
	indentLevel     int // 列表的缩进级别，支持指定一行的缩进 除代码块以外的列表都支持设置缩进，支持 1-16 级缩进，取值范围：[1,16]
	indentLevelFlag bool
	number          int // 用于指定列表的行号，仅对有序列表和代码块生效 如果为有序列表设置了缩进，行号可能会显示为字母或者罗马数字
	numberFlag      bool
}

func NewContentListBuilder() *ContentListBuilder {
	builder := &ContentListBuilder{}
	return builder
}

// 列表类型
//
// 示例值：number
func (builder *ContentListBuilder) Type(type_ string) *ContentListBuilder {
	builder.type_ = type_
	builder.typeFlag = true
	return builder
}

// 列表的缩进级别，支持指定一行的缩进 除代码块以外的列表都支持设置缩进，支持 1-16 级缩进，取值范围：[1,16]
//
// 示例值：1
func (builder *ContentListBuilder) IndentLevel(indentLevel int) *ContentListBuilder {
	builder.indentLevel = indentLevel
	builder.indentLevelFlag = true
	return builder
}

// 用于指定列表的行号，仅对有序列表和代码块生效 如果为有序列表设置了缩进，行号可能会显示为字母或者罗马数字
//
// 示例值：1
func (builder *ContentListBuilder) Number(number int) *ContentListBuilder {
	builder.number = number
	builder.numberFlag = true
	return builder
}

func (builder *ContentListBuilder) Build() *ContentList {
	req := &ContentList{}
	if builder.typeFlag {
		req.Type = &builder.type_

	}
	if builder.indentLevelFlag {
		req.IndentLevel = &builder.indentLevel

	}
	if builder.numberFlag {
		req.Number = &builder.number

	}
	return req
}

type ContentParagraph struct {
	Style    *ContentParagraphStyle     `json:"style,omitempty"`    // 段落样式
	Elements []*ContentParagraphElement `json:"elements,omitempty"` // 段落元素组成一个段落
}

type ContentParagraphBuilder struct {
	style        *ContentParagraphStyle // 段落样式
	styleFlag    bool
	elements     []*ContentParagraphElement // 段落元素组成一个段落
	elementsFlag bool
}

func NewContentParagraphBuilder() *ContentParagraphBuilder {
	builder := &ContentParagraphBuilder{}
	return builder
}

// 段落样式
//
// 示例值：
func (builder *ContentParagraphBuilder) Style(style *ContentParagraphStyle) *ContentParagraphBuilder {
	builder.style = style
	builder.styleFlag = true
	return builder
}

// 段落元素组成一个段落
//
// 示例值：
func (builder *ContentParagraphBuilder) Elements(elements []*ContentParagraphElement) *ContentParagraphBuilder {
	builder.elements = elements
	builder.elementsFlag = true
	return builder
}

func (builder *ContentParagraphBuilder) Build() *ContentParagraph {
	req := &ContentParagraph{}
	if builder.styleFlag {
		req.Style = builder.style
	}
	if builder.elementsFlag {
		req.Elements = builder.elements
	}
	return req
}

type ContentParagraphElement struct {
	Type     *string          `json:"type,omitempty"`     // 元素类型
	TextRun  *ContentTextRun  `json:"textRun,omitempty"`  // 文本
	DocsLink *ContentDocsLink `json:"docsLink,omitempty"` // 飞书云文档
	Person   *ContentPerson   `json:"person,omitempty"`   // 艾特用户
}

type ContentParagraphElementBuilder struct {
	type_        string // 元素类型
	typeFlag     bool
	textRun      *ContentTextRun // 文本
	textRunFlag  bool
	docsLink     *ContentDocsLink // 飞书云文档
	docsLinkFlag bool
	person       *ContentPerson // 艾特用户
	personFlag   bool
}

func NewContentParagraphElementBuilder() *ContentParagraphElementBuilder {
	builder := &ContentParagraphElementBuilder{}
	return builder
}

// 元素类型
//
// 示例值：textRun
func (builder *ContentParagraphElementBuilder) Type(type_ string) *ContentParagraphElementBuilder {
	builder.type_ = type_
	builder.typeFlag = true
	return builder
}

// 文本
//
// 示例值：
func (builder *ContentParagraphElementBuilder) TextRun(textRun *ContentTextRun) *ContentParagraphElementBuilder {
	builder.textRun = textRun
	builder.textRunFlag = true
	return builder
}

// 飞书云文档
//
// 示例值：
func (builder *ContentParagraphElementBuilder) DocsLink(docsLink *ContentDocsLink) *ContentParagraphElementBuilder {
	builder.docsLink = docsLink
	builder.docsLinkFlag = true
	return builder
}

// 艾特用户
//
// 示例值：
func (builder *ContentParagraphElementBuilder) Person(person *ContentPerson) *ContentParagraphElementBuilder {
	builder.person = person
	builder.personFlag = true
	return builder
}

func (builder *ContentParagraphElementBuilder) Build() *ContentParagraphElement {
	req := &ContentParagraphElement{}
	if builder.typeFlag {
		req.Type = &builder.type_

	}
	if builder.textRunFlag {
		req.TextRun = builder.textRun
	}
	if builder.docsLinkFlag {
		req.DocsLink = builder.docsLink
	}
	if builder.personFlag {
		req.Person = builder.person
	}
	return req
}

type ContentParagraphStyle struct {
	List *ContentList `json:"list,omitempty"` // 有序列表/无序列表/任务列表
}

type ContentParagraphStyleBuilder struct {
	list     *ContentList // 有序列表/无序列表/任务列表
	listFlag bool
}

func NewContentParagraphStyleBuilder() *ContentParagraphStyleBuilder {
	builder := &ContentParagraphStyleBuilder{}
	return builder
}

// 有序列表/无序列表/任务列表
//
// 示例值：
func (builder *ContentParagraphStyleBuilder) List(list *ContentList) *ContentParagraphStyleBuilder {
	builder.list = list
	builder.listFlag = true
	return builder
}

func (builder *ContentParagraphStyleBuilder) Build() *ContentParagraphStyle {
	req := &ContentParagraphStyle{}
	if builder.listFlag {
		req.List = builder.list
	}
	return req
}

type ContentPerson struct {
	OpenId *string `json:"openId,omitempty"` // 员工的OpenID
}

type ContentPersonBuilder struct {
	openId     string // 员工的OpenID
	openIdFlag bool
}

func NewContentPersonBuilder() *ContentPersonBuilder {
	builder := &ContentPersonBuilder{}
	return builder
}

// 员工的OpenID
//
// 示例值：ou_3bbe8a09c20e89cce9bff989ed840674
func (builder *ContentPersonBuilder) OpenId(openId string) *ContentPersonBuilder {
	builder.openId = openId
	builder.openIdFlag = true
	return builder
}

func (builder *ContentPersonBuilder) Build() *ContentPerson {
	req := &ContentPerson{}
	if builder.openIdFlag {
		req.OpenId = &builder.openId

	}
	return req
}

type ContentTextRun struct {
	Text  *string           `json:"text,omitempty"`  // 具体的文本内容
	Style *ContentTextStyle `json:"style,omitempty"` // 文本内容的样式，支持 BIUS、颜色等
}

type ContentTextRunBuilder struct {
	text      string // 具体的文本内容
	textFlag  bool
	style     *ContentTextStyle // 文本内容的样式，支持 BIUS、颜色等
	styleFlag bool
}

func NewContentTextRunBuilder() *ContentTextRunBuilder {
	builder := &ContentTextRunBuilder{}
	return builder
}

// 具体的文本内容
//
// 示例值：周报内容
func (builder *ContentTextRunBuilder) Text(text string) *ContentTextRunBuilder {
	builder.text = text
	builder.textFlag = true
	return builder
}

// 文本内容的样式，支持 BIUS、颜色等
//
// 示例值：
func (builder *ContentTextRunBuilder) Style(style *ContentTextStyle) *ContentTextRunBuilder {
	builder.style = style
	builder.styleFlag = true
	return builder
}

func (builder *ContentTextRunBuilder) Build() *ContentTextRun {
	req := &ContentTextRun{}
	if builder.textFlag {
		req.Text = &builder.text

	}
	if builder.styleFlag {
		req.Style = builder.style
	}
	return req
}

type ContentTextStyle struct {
	Bold          *bool         `json:"bold,omitempty"`          // 是否加粗
	StrikeThrough *bool         `json:"strikeThrough,omitempty"` // 是否删除
	BackColor     *ContentColor `json:"backColor,omitempty"`     // 背景颜色
	TextColor     *ContentColor `json:"textColor,omitempty"`     // 字体颜色
	Link          *ContentLink  `json:"link,omitempty"`          // 链接地址
}

type ContentTextStyleBuilder struct {
	bold              bool // 是否加粗
	boldFlag          bool
	strikeThrough     bool // 是否删除
	strikeThroughFlag bool
	backColor         *ContentColor // 背景颜色
	backColorFlag     bool
	textColor         *ContentColor // 字体颜色
	textColorFlag     bool
	link              *ContentLink // 链接地址
	linkFlag          bool
}

func NewContentTextStyleBuilder() *ContentTextStyleBuilder {
	builder := &ContentTextStyleBuilder{}
	return builder
}

// 是否加粗
//
// 示例值：true
func (builder *ContentTextStyleBuilder) Bold(bold bool) *ContentTextStyleBuilder {
	builder.bold = bold
	builder.boldFlag = true
	return builder
}

// 是否删除
//
// 示例值：true
func (builder *ContentTextStyleBuilder) StrikeThrough(strikeThrough bool) *ContentTextStyleBuilder {
	builder.strikeThrough = strikeThrough
	builder.strikeThroughFlag = true
	return builder
}

// 背景颜色
//
// 示例值：
func (builder *ContentTextStyleBuilder) BackColor(backColor *ContentColor) *ContentTextStyleBuilder {
	builder.backColor = backColor
	builder.backColorFlag = true
	return builder
}

// 字体颜色
//
// 示例值：
func (builder *ContentTextStyleBuilder) TextColor(textColor *ContentColor) *ContentTextStyleBuilder {
	builder.textColor = textColor
	builder.textColorFlag = true
	return builder
}

// 链接地址
//
// 示例值：
func (builder *ContentTextStyleBuilder) Link(link *ContentLink) *ContentTextStyleBuilder {
	builder.link = link
	builder.linkFlag = true
	return builder
}

func (builder *ContentTextStyleBuilder) Build() *ContentTextStyle {
	req := &ContentTextStyle{}
	if builder.boldFlag {
		req.Bold = &builder.bold

	}
	if builder.strikeThroughFlag {
		req.StrikeThrough = &builder.strikeThrough

	}
	if builder.backColorFlag {
		req.BackColor = builder.backColor
	}
	if builder.textColorFlag {
		req.TextColor = builder.textColor
	}
	if builder.linkFlag {
		req.Link = builder.link
	}
	return req
}

type CurrentOkrSimple struct {
	OkrId    *string `json:"okr_id,omitempty"`    // OKR ID
	PeriodId *string `json:"period_id,omitempty"` // 周期 ID
}

type CurrentOkrSimpleBuilder struct {
	okrId        string // OKR ID
	okrIdFlag    bool
	periodId     string // 周期 ID
	periodIdFlag bool
}

func NewCurrentOkrSimpleBuilder() *CurrentOkrSimpleBuilder {
	builder := &CurrentOkrSimpleBuilder{}
	return builder
}

// OKR ID
//
// 示例值：
func (builder *CurrentOkrSimpleBuilder) OkrId(okrId string) *CurrentOkrSimpleBuilder {
	builder.okrId = okrId
	builder.okrIdFlag = true
	return builder
}

// 周期 ID
//
// 示例值：
func (builder *CurrentOkrSimpleBuilder) PeriodId(periodId string) *CurrentOkrSimpleBuilder {
	builder.periodId = periodId
	builder.periodIdFlag = true
	return builder
}

func (builder *CurrentOkrSimpleBuilder) Build() *CurrentOkrSimple {
	req := &CurrentOkrSimple{}
	if builder.okrIdFlag {
		req.OkrId = &builder.okrId

	}
	if builder.periodIdFlag {
		req.PeriodId = &builder.periodId

	}
	return req
}

type Dummy struct {
	Id *string `json:"id,omitempty"` // id
}

type DummyBuilder struct {
	id     string // id
	idFlag bool
}

func NewDummyBuilder() *DummyBuilder {
	builder := &DummyBuilder{}
	return builder
}

// id
//
// 示例值：
func (builder *DummyBuilder) Id(id string) *DummyBuilder {
	builder.id = id
	builder.idFlag = true
	return builder
}

func (builder *DummyBuilder) Build() *Dummy {
	req := &Dummy{}
	if builder.idFlag {
		req.Id = &builder.id

	}
	return req
}

type FailedMetricItem struct {
	MetricItemId *string `json:"metric_item_id,omitempty"` // 指标项ID
	Reason       *string `json:"reason,omitempty"`         // 失败原因
}

type FailedMetricItemBuilder struct {
	metricItemId     string // 指标项ID
	metricItemIdFlag bool
	reason           string // 失败原因
	reasonFlag       bool
}

func NewFailedMetricItemBuilder() *FailedMetricItemBuilder {
	builder := &FailedMetricItemBuilder{}
	return builder
}

// 指标项ID
//
// 示例值：7139040982003302420
func (builder *FailedMetricItemBuilder) MetricItemId(metricItemId string) *FailedMetricItemBuilder {
	builder.metricItemId = metricItemId
	builder.metricItemIdFlag = true
	return builder
}

// 失败原因
//
// 示例值：start value equals target value
func (builder *FailedMetricItemBuilder) Reason(reason string) *FailedMetricItemBuilder {
	builder.reason = reason
	builder.reasonFlag = true
	return builder
}

func (builder *FailedMetricItemBuilder) Build() *FailedMetricItem {
	req := &FailedMetricItem{}
	if builder.metricItemIdFlag {
		req.MetricItemId = &builder.metricItemId

	}
	if builder.reasonFlag {
		req.Reason = &builder.reason

	}
	return req
}

type ImageInfo struct {
	FileToken *string `json:"file_token,omitempty"` // 图片token
	Url       *string `json:"url,omitempty"`        // 图片下载链接
}

type ImageInfoBuilder struct {
	fileToken     string // 图片token
	fileTokenFlag bool
	url           string // 图片下载链接
	urlFlag       bool
}

func NewImageInfoBuilder() *ImageInfoBuilder {
	builder := &ImageInfoBuilder{}
	return builder
}

// 图片token
//
// 示例值：boxbcLxEnhUE3REJSAwAbVFZwPf
func (builder *ImageInfoBuilder) FileToken(fileToken string) *ImageInfoBuilder {
	builder.fileToken = fileToken
	builder.fileTokenFlag = true
	return builder
}

// 图片下载链接
//
// 示例值：https://bytedance.feishu.cn/drive/home/
func (builder *ImageInfoBuilder) Url(url string) *ImageInfoBuilder {
	builder.url = url
	builder.urlFlag = true
	return builder
}

func (builder *ImageInfoBuilder) Build() *ImageInfo {
	req := &ImageInfo{}
	if builder.fileTokenFlag {
		req.FileToken = &builder.fileToken

	}
	if builder.urlFlag {
		req.Url = &builder.url

	}
	return req
}

type Kr struct {
	Id         *string    `json:"id,omitempty"`          // KeyResult ID
	Pos        *string    `json:"pos,omitempty"`         // KeyResult 在所属 Objective 中的排序
	Score      *string    `json:"score,omitempty"`       // KeyResult 评分，返回值为百分制分数，需要除以 100 以获得 OKR 页面上显示的 1 分制分数
	Weight     *string    `json:"weight,omitempty"`      // KeyResult 的权重
	Content    *KrContent `json:"content,omitempty"`     // KeyResult 对应的 Content 详细内容
	CreateTime *int       `json:"create_time,omitempty"` // KeyResult 的创建时间 毫秒
	ModifyTime *int       `json:"modify_time,omitempty"` // KeyResult 的最后修改时间 毫秒
}

type KrBuilder struct {
	id             string // KeyResult ID
	idFlag         bool
	pos            string // KeyResult 在所属 Objective 中的排序
	posFlag        bool
	score          string // KeyResult 评分，返回值为百分制分数，需要除以 100 以获得 OKR 页面上显示的 1 分制分数
	scoreFlag      bool
	weight         string // KeyResult 的权重
	weightFlag     bool
	content        *KrContent // KeyResult 对应的 Content 详细内容
	contentFlag    bool
	createTime     int // KeyResult 的创建时间 毫秒
	createTimeFlag bool
	modifyTime     int // KeyResult 的最后修改时间 毫秒
	modifyTimeFlag bool
}

func NewKrBuilder() *KrBuilder {
	builder := &KrBuilder{}
	return builder
}

// KeyResult ID
//
// 示例值：
func (builder *KrBuilder) Id(id string) *KrBuilder {
	builder.id = id
	builder.idFlag = true
	return builder
}

// KeyResult 在所属 Objective 中的排序
//
// 示例值：
func (builder *KrBuilder) Pos(pos string) *KrBuilder {
	builder.pos = pos
	builder.posFlag = true
	return builder
}

// KeyResult 评分，返回值为百分制分数，需要除以 100 以获得 OKR 页面上显示的 1 分制分数
//
// 示例值：
func (builder *KrBuilder) Score(score string) *KrBuilder {
	builder.score = score
	builder.scoreFlag = true
	return builder
}

// KeyResult 的权重
//
// 示例值：
func (builder *KrBuilder) Weight(weight string) *KrBuilder {
	builder.weight = weight
	builder.weightFlag = true
	return builder
}

// KeyResult 对应的 Content 详细内容
//
// 示例值：
func (builder *KrBuilder) Content(content *KrContent) *KrBuilder {
	builder.content = content
	builder.contentFlag = true
	return builder
}

// KeyResult 的创建时间 毫秒
//
// 示例值：
func (builder *KrBuilder) CreateTime(createTime int) *KrBuilder {
	builder.createTime = createTime
	builder.createTimeFlag = true
	return builder
}

// KeyResult 的最后修改时间 毫秒
//
// 示例值：
func (builder *KrBuilder) ModifyTime(modifyTime int) *KrBuilder {
	builder.modifyTime = modifyTime
	builder.modifyTimeFlag = true
	return builder
}

func (builder *KrBuilder) Build() *Kr {
	req := &Kr{}
	if builder.idFlag {
		req.Id = &builder.id

	}
	if builder.posFlag {
		req.Pos = &builder.pos

	}
	if builder.scoreFlag {
		req.Score = &builder.score

	}
	if builder.weightFlag {
		req.Weight = &builder.weight

	}
	if builder.contentFlag {
		req.Content = builder.content
	}
	if builder.createTimeFlag {
		req.CreateTime = &builder.createTime

	}
	if builder.modifyTimeFlag {
		req.ModifyTime = &builder.modifyTime

	}
	return req
}

type KrContent struct {
	Zh *string `json:"zh,omitempty"` // 中文内容
	En *string `json:"en,omitempty"` // 英文内容
}

type KrContentBuilder struct {
	zh     string // 中文内容
	zhFlag bool
	en     string // 英文内容
	enFlag bool
}

func NewKrContentBuilder() *KrContentBuilder {
	builder := &KrContentBuilder{}
	return builder
}

// 中文内容
//
// 示例值：
func (builder *KrContentBuilder) Zh(zh string) *KrContentBuilder {
	builder.zh = zh
	builder.zhFlag = true
	return builder
}

// 英文内容
//
// 示例值：
func (builder *KrContentBuilder) En(en string) *KrContentBuilder {
	builder.en = en
	builder.enFlag = true
	return builder
}

func (builder *KrContentBuilder) Build() *KrContent {
	req := &KrContent{}
	if builder.zhFlag {
		req.Zh = &builder.zh

	}
	if builder.enFlag {
		req.En = &builder.en

	}
	return req
}

type MetricItem struct {
	MetricItemId       *string     `json:"metric_item_id,omitempty"`       // 指标项id
	UserId             *string     `json:"user_id,omitempty"`              // 指标承接人员id
	PeriodId           *string     `json:"period_id,omitempty"`            // 指标的okr周期
	MetricUnit         *MetricUnit `json:"metric_unit,omitempty"`          // 指标单位
	MetricInitialValue *float64    `json:"metric_initial_value,omitempty"` // 指标起始值
	MetricTargetValue  *float64    `json:"metric_target_value,omitempty"`  // 指标目标值
	MetricCurrentValue *float64    `json:"metric_current_value,omitempty"` // 指标进度值
	SupportedUserId    *string     `json:"supported_user_id,omitempty"`    // 指标支撑的上级人员id
	KrId               *string     `json:"kr_id,omitempty"`                // 指标关联的kr
	UpdatedAt          *string     `json:"updated_at,omitempty"`           // 更新时间
	UpdatedBy          *string     `json:"updated_by,omitempty"`           // 更新人
}

type MetricItemBuilder struct {
	metricItemId           string // 指标项id
	metricItemIdFlag       bool
	userId                 string // 指标承接人员id
	userIdFlag             bool
	periodId               string // 指标的okr周期
	periodIdFlag           bool
	metricUnit             *MetricUnit // 指标单位
	metricUnitFlag         bool
	metricInitialValue     float64 // 指标起始值
	metricInitialValueFlag bool
	metricTargetValue      float64 // 指标目标值
	metricTargetValueFlag  bool
	metricCurrentValue     float64 // 指标进度值
	metricCurrentValueFlag bool
	supportedUserId        string // 指标支撑的上级人员id
	supportedUserIdFlag    bool
	krId                   string // 指标关联的kr
	krIdFlag               bool
	updatedAt              string // 更新时间
	updatedAtFlag          bool
	updatedBy              string // 更新人
	updatedByFlag          bool
}

func NewMetricItemBuilder() *MetricItemBuilder {
	builder := &MetricItemBuilder{}
	return builder
}

// 指标项id
//
// 示例值：635782378412311
func (builder *MetricItemBuilder) MetricItemId(metricItemId string) *MetricItemBuilder {
	builder.metricItemId = metricItemId
	builder.metricItemIdFlag = true
	return builder
}

// 指标承接人员id
//
// 示例值：635782378412311
func (builder *MetricItemBuilder) UserId(userId string) *MetricItemBuilder {
	builder.userId = userId
	builder.userIdFlag = true
	return builder
}

// 指标的okr周期
//
// 示例值：635782378412311
func (builder *MetricItemBuilder) PeriodId(periodId string) *MetricItemBuilder {
	builder.periodId = periodId
	builder.periodIdFlag = true
	return builder
}

// 指标单位
//
// 示例值：
func (builder *MetricItemBuilder) MetricUnit(metricUnit *MetricUnit) *MetricItemBuilder {
	builder.metricUnit = metricUnit
	builder.metricUnitFlag = true
	return builder
}

// 指标起始值
//
// 示例值：10.01
func (builder *MetricItemBuilder) MetricInitialValue(metricInitialValue float64) *MetricItemBuilder {
	builder.metricInitialValue = metricInitialValue
	builder.metricInitialValueFlag = true
	return builder
}

// 指标目标值
//
// 示例值：10.01
func (builder *MetricItemBuilder) MetricTargetValue(metricTargetValue float64) *MetricItemBuilder {
	builder.metricTargetValue = metricTargetValue
	builder.metricTargetValueFlag = true
	return builder
}

// 指标进度值
//
// 示例值：10.01
func (builder *MetricItemBuilder) MetricCurrentValue(metricCurrentValue float64) *MetricItemBuilder {
	builder.metricCurrentValue = metricCurrentValue
	builder.metricCurrentValueFlag = true
	return builder
}

// 指标支撑的上级人员id
//
// 示例值：ou_8e7d79ca2327bf4f0b3c37899d6abbd5
func (builder *MetricItemBuilder) SupportedUserId(supportedUserId string) *MetricItemBuilder {
	builder.supportedUserId = supportedUserId
	builder.supportedUserIdFlag = true
	return builder
}

// 指标关联的kr
//
// 示例值：7139040982003302420
func (builder *MetricItemBuilder) KrId(krId string) *MetricItemBuilder {
	builder.krId = krId
	builder.krIdFlag = true
	return builder
}

// 更新时间
//
// 示例值：1663145941129
func (builder *MetricItemBuilder) UpdatedAt(updatedAt string) *MetricItemBuilder {
	builder.updatedAt = updatedAt
	builder.updatedAtFlag = true
	return builder
}

// 更新人
//
// 示例值：ou_8e7d79ca2327bf4f0b3c37899d6abbd5
func (builder *MetricItemBuilder) UpdatedBy(updatedBy string) *MetricItemBuilder {
	builder.updatedBy = updatedBy
	builder.updatedByFlag = true
	return builder
}

func (builder *MetricItemBuilder) Build() *MetricItem {
	req := &MetricItem{}
	if builder.metricItemIdFlag {
		req.MetricItemId = &builder.metricItemId

	}
	if builder.userIdFlag {
		req.UserId = &builder.userId

	}
	if builder.periodIdFlag {
		req.PeriodId = &builder.periodId

	}
	if builder.metricUnitFlag {
		req.MetricUnit = builder.metricUnit
	}
	if builder.metricInitialValueFlag {
		req.MetricInitialValue = &builder.metricInitialValue

	}
	if builder.metricTargetValueFlag {
		req.MetricTargetValue = &builder.metricTargetValue

	}
	if builder.metricCurrentValueFlag {
		req.MetricCurrentValue = &builder.metricCurrentValue

	}
	if builder.supportedUserIdFlag {
		req.SupportedUserId = &builder.supportedUserId

	}
	if builder.krIdFlag {
		req.KrId = &builder.krId

	}
	if builder.updatedAtFlag {
		req.UpdatedAt = &builder.updatedAt

	}
	if builder.updatedByFlag {
		req.UpdatedBy = &builder.updatedBy

	}
	return req
}

type MetricItemRequest struct {
	MetricItemId       *string  `json:"metric_item_id,omitempty"`       // 指标表id
	MetricInitialValue *float64 `json:"metric_initial_value,omitempty"` // 指标起始值
	MetricTargetValue  *float64 `json:"metric_target_value,omitempty"`  // 指标目标值
	MetricCurrentValue *float64 `json:"metric_current_value,omitempty"` // 指标进度值
	SupportedUserId    *string  `json:"supported_user_id,omitempty"`    // 指标支撑的上级人员 id
}

type MetricItemRequestBuilder struct {
	metricItemId           string // 指标表id
	metricItemIdFlag       bool
	metricInitialValue     float64 // 指标起始值
	metricInitialValueFlag bool
	metricTargetValue      float64 // 指标目标值
	metricTargetValueFlag  bool
	metricCurrentValue     float64 // 指标进度值
	metricCurrentValueFlag bool
	supportedUserId        string // 指标支撑的上级人员 id
	supportedUserIdFlag    bool
}

func NewMetricItemRequestBuilder() *MetricItemRequestBuilder {
	builder := &MetricItemRequestBuilder{}
	return builder
}

// 指标表id
//
// 示例值：635782378412311
func (builder *MetricItemRequestBuilder) MetricItemId(metricItemId string) *MetricItemRequestBuilder {
	builder.metricItemId = metricItemId
	builder.metricItemIdFlag = true
	return builder
}

// 指标起始值
//
// 示例值：1.0
func (builder *MetricItemRequestBuilder) MetricInitialValue(metricInitialValue float64) *MetricItemRequestBuilder {
	builder.metricInitialValue = metricInitialValue
	builder.metricInitialValueFlag = true
	return builder
}

// 指标目标值
//
// 示例值：3.0
func (builder *MetricItemRequestBuilder) MetricTargetValue(metricTargetValue float64) *MetricItemRequestBuilder {
	builder.metricTargetValue = metricTargetValue
	builder.metricTargetValueFlag = true
	return builder
}

// 指标进度值
//
// 示例值：2.0
func (builder *MetricItemRequestBuilder) MetricCurrentValue(metricCurrentValue float64) *MetricItemRequestBuilder {
	builder.metricCurrentValue = metricCurrentValue
	builder.metricCurrentValueFlag = true
	return builder
}

// 指标支撑的上级人员 id
//
// 示例值：7041857032248410131
func (builder *MetricItemRequestBuilder) SupportedUserId(supportedUserId string) *MetricItemRequestBuilder {
	builder.supportedUserId = supportedUserId
	builder.supportedUserIdFlag = true
	return builder
}

func (builder *MetricItemRequestBuilder) Build() *MetricItemRequest {
	req := &MetricItemRequest{}
	if builder.metricItemIdFlag {
		req.MetricItemId = &builder.metricItemId

	}
	if builder.metricInitialValueFlag {
		req.MetricInitialValue = &builder.metricInitialValue

	}
	if builder.metricTargetValueFlag {
		req.MetricTargetValue = &builder.metricTargetValue

	}
	if builder.metricCurrentValueFlag {
		req.MetricCurrentValue = &builder.metricCurrentValue

	}
	if builder.supportedUserIdFlag {
		req.SupportedUserId = &builder.supportedUserId

	}
	return req
}

type MetricSource struct {
	MetricSourceId   *string     `json:"metric_source_id,omitempty"`   // 指标库 id
	MetricSourceName *string     `json:"metric_source_name,omitempty"` // 指标库名称
	MetricName       *string     `json:"metric_name,omitempty"`        // 指标名称
	MetricUnit       *MetricUnit `json:"metric_unit,omitempty"`        // 指标单位
}

type MetricSourceBuilder struct {
	metricSourceId       string // 指标库 id
	metricSourceIdFlag   bool
	metricSourceName     string // 指标库名称
	metricSourceNameFlag bool
	metricName           string // 指标名称
	metricNameFlag       bool
	metricUnit           *MetricUnit // 指标单位
	metricUnitFlag       bool
}

func NewMetricSourceBuilder() *MetricSourceBuilder {
	builder := &MetricSourceBuilder{}
	return builder
}

// 指标库 id
//
// 示例值：7139040982003302420
func (builder *MetricSourceBuilder) MetricSourceId(metricSourceId string) *MetricSourceBuilder {
	builder.metricSourceId = metricSourceId
	builder.metricSourceIdFlag = true
	return builder
}

// 指标库名称
//
// 示例值：指标库A
func (builder *MetricSourceBuilder) MetricSourceName(metricSourceName string) *MetricSourceBuilder {
	builder.metricSourceName = metricSourceName
	builder.metricSourceNameFlag = true
	return builder
}

// 指标名称
//
// 示例值：指标A
func (builder *MetricSourceBuilder) MetricName(metricName string) *MetricSourceBuilder {
	builder.metricName = metricName
	builder.metricNameFlag = true
	return builder
}

// 指标单位
//
// 示例值：
func (builder *MetricSourceBuilder) MetricUnit(metricUnit *MetricUnit) *MetricSourceBuilder {
	builder.metricUnit = metricUnit
	builder.metricUnitFlag = true
	return builder
}

func (builder *MetricSourceBuilder) Build() *MetricSource {
	req := &MetricSource{}
	if builder.metricSourceIdFlag {
		req.MetricSourceId = &builder.metricSourceId

	}
	if builder.metricSourceNameFlag {
		req.MetricSourceName = &builder.metricSourceName

	}
	if builder.metricNameFlag {
		req.MetricName = &builder.metricName

	}
	if builder.metricUnitFlag {
		req.MetricUnit = builder.metricUnit
	}
	return req
}

type MetricTable struct {
	MetricTableId   *string `json:"metric_table_id,omitempty"`   // 指标表 id
	MetricTableName *string `json:"metric_table_name,omitempty"` // 指标表名称
	PeriodId        *string `json:"period_id,omitempty"`         // okr周期
}

type MetricTableBuilder struct {
	metricTableId       string // 指标表 id
	metricTableIdFlag   bool
	metricTableName     string // 指标表名称
	metricTableNameFlag bool
	periodId            string // okr周期
	periodIdFlag        bool
}

func NewMetricTableBuilder() *MetricTableBuilder {
	builder := &MetricTableBuilder{}
	return builder
}

// 指标表 id
//
// 示例值：635782378412311
func (builder *MetricTableBuilder) MetricTableId(metricTableId string) *MetricTableBuilder {
	builder.metricTableId = metricTableId
	builder.metricTableIdFlag = true
	return builder
}

// 指标表名称
//
// 示例值：指标表a
func (builder *MetricTableBuilder) MetricTableName(metricTableName string) *MetricTableBuilder {
	builder.metricTableName = metricTableName
	builder.metricTableNameFlag = true
	return builder
}

// okr周期
//
// 示例值：635782378221221
func (builder *MetricTableBuilder) PeriodId(periodId string) *MetricTableBuilder {
	builder.periodId = periodId
	builder.periodIdFlag = true
	return builder
}

func (builder *MetricTableBuilder) Build() *MetricTable {
	req := &MetricTable{}
	if builder.metricTableIdFlag {
		req.MetricTableId = &builder.metricTableId

	}
	if builder.metricTableNameFlag {
		req.MetricTableName = &builder.metricTableName

	}
	if builder.periodIdFlag {
		req.PeriodId = &builder.periodId

	}
	return req
}

type MetricUnit struct {
	ZhCn *string `json:"zh_cn,omitempty"` // 指标单位中文
	EnUs *string `json:"en_us,omitempty"` // 指标单位英文
	JaJp *string `json:"ja_jp,omitempty"` // 指标单位日文
}

type MetricUnitBuilder struct {
	zhCn     string // 指标单位中文
	zhCnFlag bool
	enUs     string // 指标单位英文
	enUsFlag bool
	jaJp     string // 指标单位日文
	jaJpFlag bool
}

func NewMetricUnitBuilder() *MetricUnitBuilder {
	builder := &MetricUnitBuilder{}
	return builder
}

// 指标单位中文
//
// 示例值：小明
func (builder *MetricUnitBuilder) ZhCn(zhCn string) *MetricUnitBuilder {
	builder.zhCn = zhCn
	builder.zhCnFlag = true
	return builder
}

// 指标单位英文
//
// 示例值：jack
func (builder *MetricUnitBuilder) EnUs(enUs string) *MetricUnitBuilder {
	builder.enUs = enUs
	builder.enUsFlag = true
	return builder
}

// 指标单位日文
//
// 示例值：シャオ・ミン
func (builder *MetricUnitBuilder) JaJp(jaJp string) *MetricUnitBuilder {
	builder.jaJp = jaJp
	builder.jaJpFlag = true
	return builder
}

func (builder *MetricUnitBuilder) Build() *MetricUnit {
	req := &MetricUnit{}
	if builder.zhCnFlag {
		req.ZhCn = &builder.zhCn

	}
	if builder.enUsFlag {
		req.EnUs = &builder.enUs

	}
	if builder.jaJpFlag {
		req.JaJp = &builder.jaJp

	}
	return req
}

type Objective struct {
	Id                    *string            `json:"id,omitempty"`                      // 目标的ID
	OkrId                 *string            `json:"okr_id,omitempty"`                  // 所属的OKR ID
	UserId                *string            `json:"user_id,omitempty"`                 // 拥有者的用户 UUID
	Pos                   *string            `json:"pos,omitempty"`                     // Objective 在所属 OKR 中的排序
	Score                 *string            `json:"score,omitempty"`                   // Objective 评分，返回值为百分制分数，需要除以 100 以获得 OKR 页面上显示的 1 分制分数
	Name                  *ObjectiveName     `json:"name,omitempty"`                    // Objective 的详细内容
	Progress              *ObjectiveProgress `json:"progress,omitempty"`                // Objective 对应的 Progress 详细内容
	CreateTime            *int               `json:"create_time,omitempty"`             // Objective 的创建时间
	ModifyTime            *int               `json:"modify_time,omitempty"`             // Objective 的最后修改时间
	KrList                []*Kr              `json:"kr_list,omitempty"`                 // Objective 下的 KeyResult 列表
	AligningObjectiveList []*AlignObjective  `json:"aligning_objective_list,omitempty"` // Objective 对齐别人的 Objective 列表
	AlignedObjectiveList  []*AlignObjective  `json:"aligned_objective_list,omitempty"`  // 被别人对齐的 Objective 列表
}

type ObjectiveBuilder struct {
	id                        string // 目标的ID
	idFlag                    bool
	okrId                     string // 所属的OKR ID
	okrIdFlag                 bool
	userId                    string // 拥有者的用户 UUID
	userIdFlag                bool
	pos                       string // Objective 在所属 OKR 中的排序
	posFlag                   bool
	score                     string // Objective 评分，返回值为百分制分数，需要除以 100 以获得 OKR 页面上显示的 1 分制分数
	scoreFlag                 bool
	name                      *ObjectiveName // Objective 的详细内容
	nameFlag                  bool
	progress                  *ObjectiveProgress // Objective 对应的 Progress 详细内容
	progressFlag              bool
	createTime                int // Objective 的创建时间
	createTimeFlag            bool
	modifyTime                int // Objective 的最后修改时间
	modifyTimeFlag            bool
	krList                    []*Kr // Objective 下的 KeyResult 列表
	krListFlag                bool
	aligningObjectiveList     []*AlignObjective // Objective 对齐别人的 Objective 列表
	aligningObjectiveListFlag bool
	alignedObjectiveList      []*AlignObjective // 被别人对齐的 Objective 列表
	alignedObjectiveListFlag  bool
}

func NewObjectiveBuilder() *ObjectiveBuilder {
	builder := &ObjectiveBuilder{}
	return builder
}

// 目标的ID
//
// 示例值：
func (builder *ObjectiveBuilder) Id(id string) *ObjectiveBuilder {
	builder.id = id
	builder.idFlag = true
	return builder
}

// 所属的OKR ID
//
// 示例值：
func (builder *ObjectiveBuilder) OkrId(okrId string) *ObjectiveBuilder {
	builder.okrId = okrId
	builder.okrIdFlag = true
	return builder
}

// 拥有者的用户 UUID
//
// 示例值：
func (builder *ObjectiveBuilder) UserId(userId string) *ObjectiveBuilder {
	builder.userId = userId
	builder.userIdFlag = true
	return builder
}

// Objective 在所属 OKR 中的排序
//
// 示例值：
func (builder *ObjectiveBuilder) Pos(pos string) *ObjectiveBuilder {
	builder.pos = pos
	builder.posFlag = true
	return builder
}

// Objective 评分，返回值为百分制分数，需要除以 100 以获得 OKR 页面上显示的 1 分制分数
//
// 示例值：
func (builder *ObjectiveBuilder) Score(score string) *ObjectiveBuilder {
	builder.score = score
	builder.scoreFlag = true
	return builder
}

// Objective 的详细内容
//
// 示例值：
func (builder *ObjectiveBuilder) Name(name *ObjectiveName) *ObjectiveBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

// Objective 对应的 Progress 详细内容
//
// 示例值：
func (builder *ObjectiveBuilder) Progress(progress *ObjectiveProgress) *ObjectiveBuilder {
	builder.progress = progress
	builder.progressFlag = true
	return builder
}

// Objective 的创建时间
//
// 示例值：
func (builder *ObjectiveBuilder) CreateTime(createTime int) *ObjectiveBuilder {
	builder.createTime = createTime
	builder.createTimeFlag = true
	return builder
}

// Objective 的最后修改时间
//
// 示例值：
func (builder *ObjectiveBuilder) ModifyTime(modifyTime int) *ObjectiveBuilder {
	builder.modifyTime = modifyTime
	builder.modifyTimeFlag = true
	return builder
}

// Objective 下的 KeyResult 列表
//
// 示例值：
func (builder *ObjectiveBuilder) KrList(krList []*Kr) *ObjectiveBuilder {
	builder.krList = krList
	builder.krListFlag = true
	return builder
}

// Objective 对齐别人的 Objective 列表
//
// 示例值：
func (builder *ObjectiveBuilder) AligningObjectiveList(aligningObjectiveList []*AlignObjective) *ObjectiveBuilder {
	builder.aligningObjectiveList = aligningObjectiveList
	builder.aligningObjectiveListFlag = true
	return builder
}

// 被别人对齐的 Objective 列表
//
// 示例值：
func (builder *ObjectiveBuilder) AlignedObjectiveList(alignedObjectiveList []*AlignObjective) *ObjectiveBuilder {
	builder.alignedObjectiveList = alignedObjectiveList
	builder.alignedObjectiveListFlag = true
	return builder
}

func (builder *ObjectiveBuilder) Build() *Objective {
	req := &Objective{}
	if builder.idFlag {
		req.Id = &builder.id

	}
	if builder.okrIdFlag {
		req.OkrId = &builder.okrId

	}
	if builder.userIdFlag {
		req.UserId = &builder.userId

	}
	if builder.posFlag {
		req.Pos = &builder.pos

	}
	if builder.scoreFlag {
		req.Score = &builder.score

	}
	if builder.nameFlag {
		req.Name = builder.name
	}
	if builder.progressFlag {
		req.Progress = builder.progress
	}
	if builder.createTimeFlag {
		req.CreateTime = &builder.createTime

	}
	if builder.modifyTimeFlag {
		req.ModifyTime = &builder.modifyTime

	}
	if builder.krListFlag {
		req.KrList = builder.krList
	}
	if builder.aligningObjectiveListFlag {
		req.AligningObjectiveList = builder.aligningObjectiveList
	}
	if builder.alignedObjectiveListFlag {
		req.AlignedObjectiveList = builder.alignedObjectiveList
	}
	return req
}

type ObjectiveName struct {
	Zh *string `json:"zh,omitempty"` // 中文内容
	En *string `json:"en,omitempty"` // 英文内容
}

type ObjectiveNameBuilder struct {
	zh     string // 中文内容
	zhFlag bool
	en     string // 英文内容
	enFlag bool
}

func NewObjectiveNameBuilder() *ObjectiveNameBuilder {
	builder := &ObjectiveNameBuilder{}
	return builder
}

// 中文内容
//
// 示例值：
func (builder *ObjectiveNameBuilder) Zh(zh string) *ObjectiveNameBuilder {
	builder.zh = zh
	builder.zhFlag = true
	return builder
}

// 英文内容
//
// 示例值：
func (builder *ObjectiveNameBuilder) En(en string) *ObjectiveNameBuilder {
	builder.en = en
	builder.enFlag = true
	return builder
}

func (builder *ObjectiveNameBuilder) Build() *ObjectiveName {
	req := &ObjectiveName{}
	if builder.zhFlag {
		req.Zh = &builder.zh

	}
	if builder.enFlag {
		req.En = &builder.en

	}
	return req
}

type ObjectiveProgress struct {
	Zh *string `json:"zh,omitempty"` // 中文内容
	En *string `json:"en,omitempty"` // 英文内容
}

type ObjectiveProgressBuilder struct {
	zh     string // 中文内容
	zhFlag bool
	en     string // 英文内容
	enFlag bool
}

func NewObjectiveProgressBuilder() *ObjectiveProgressBuilder {
	builder := &ObjectiveProgressBuilder{}
	return builder
}

// 中文内容
//
// 示例值：
func (builder *ObjectiveProgressBuilder) Zh(zh string) *ObjectiveProgressBuilder {
	builder.zh = zh
	builder.zhFlag = true
	return builder
}

// 英文内容
//
// 示例值：
func (builder *ObjectiveProgressBuilder) En(en string) *ObjectiveProgressBuilder {
	builder.en = en
	builder.enFlag = true
	return builder
}

func (builder *ObjectiveProgressBuilder) Build() *ObjectiveProgress {
	req := &ObjectiveProgress{}
	if builder.zhFlag {
		req.Zh = &builder.zh

	}
	if builder.enFlag {
		req.En = &builder.en

	}
	return req
}

type Okr struct {
	OkrId         *string               `json:"okr_id,omitempty"`         // OKR ID
	PeriodId      *string               `json:"period_id,omitempty"`      // 周期 ID
	PeriodType    *string               `json:"period_type,omitempty"`    // 周期类型：1、月周期；2、年周期
	Name          *OkrName              `json:"name,omitempty"`           // 周期展示名称
	CreateTime    *int                  `json:"create_time,omitempty"`    // 创建时间 毫秒
	ModifyTime    *int                  `json:"modify_time,omitempty"`    // 修改时间 毫秒
	ObjectiveList []*OkrDetailObjective `json:"objective_list,omitempty"` // objective 列表
	OkrScore      *int                  `json:"okr_score,omitempty"`      // 打分
}

type OkrBuilder struct {
	okrId             string // OKR ID
	okrIdFlag         bool
	periodId          string // 周期 ID
	periodIdFlag      bool
	periodType        string // 周期类型：1、月周期；2、年周期
	periodTypeFlag    bool
	name              *OkrName // 周期展示名称
	nameFlag          bool
	createTime        int // 创建时间 毫秒
	createTimeFlag    bool
	modifyTime        int // 修改时间 毫秒
	modifyTimeFlag    bool
	objectiveList     []*OkrDetailObjective // objective 列表
	objectiveListFlag bool
	okrScore          int // 打分
	okrScoreFlag      bool
}

func NewOkrBuilder() *OkrBuilder {
	builder := &OkrBuilder{}
	return builder
}

// OKR ID
//
// 示例值：6976173067307927084
func (builder *OkrBuilder) OkrId(okrId string) *OkrBuilder {
	builder.okrId = okrId
	builder.okrIdFlag = true
	return builder
}

// 周期 ID
//
// 示例值：6974586812973581868
func (builder *OkrBuilder) PeriodId(periodId string) *OkrBuilder {
	builder.periodId = periodId
	builder.periodIdFlag = true
	return builder
}

// 周期类型：1、月周期；2、年周期
//
// 示例值：1
func (builder *OkrBuilder) PeriodType(periodType string) *OkrBuilder {
	builder.periodType = periodType
	builder.periodTypeFlag = true
	return builder
}

// 周期展示名称
//
// 示例值：
func (builder *OkrBuilder) Name(name *OkrName) *OkrBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

// 创建时间 毫秒
//
// 示例值：1624267575928
func (builder *OkrBuilder) CreateTime(createTime int) *OkrBuilder {
	builder.createTime = createTime
	builder.createTimeFlag = true
	return builder
}

// 修改时间 毫秒
//
// 示例值：1624329170463
func (builder *OkrBuilder) ModifyTime(modifyTime int) *OkrBuilder {
	builder.modifyTime = modifyTime
	builder.modifyTimeFlag = true
	return builder
}

// objective 列表
//
// 示例值：
func (builder *OkrBuilder) ObjectiveList(objectiveList []*OkrDetailObjective) *OkrBuilder {
	builder.objectiveList = objectiveList
	builder.objectiveListFlag = true
	return builder
}

// 打分
//
// 示例值：0.5
func (builder *OkrBuilder) OkrScore(okrScore int) *OkrBuilder {
	builder.okrScore = okrScore
	builder.okrScoreFlag = true
	return builder
}

func (builder *OkrBuilder) Build() *Okr {
	req := &Okr{}
	if builder.okrIdFlag {
		req.OkrId = &builder.okrId

	}
	if builder.periodIdFlag {
		req.PeriodId = &builder.periodId

	}
	if builder.periodTypeFlag {
		req.PeriodType = &builder.periodType

	}
	if builder.nameFlag {
		req.Name = builder.name
	}
	if builder.createTimeFlag {
		req.CreateTime = &builder.createTime

	}
	if builder.modifyTimeFlag {
		req.ModifyTime = &builder.modifyTime

	}
	if builder.objectiveListFlag {
		req.ObjectiveList = builder.objectiveList
	}
	if builder.okrScoreFlag {
		req.OkrScore = &builder.okrScore

	}
	return req
}

type OkrComment struct {
	Id                *string                            `json:"id,omitempty"`                  // 全局评论ID
	Content           *string                            `json:"content,omitempty"`             // 全局评论内容
	CommentTime       *int                               `json:"comment_time,omitempty"`        // 全局评论时间 毫秒
	Commentator       *OkrObjectiveAlignedObjectiveOwner `json:"commentator,omitempty"`         // 评论人员
	LastModifier      *OkrObjectiveAlignedObjectiveOwner `json:"last_modifier,omitempty"`       // 修改评论的用户
	ContentModifyTime *int                               `json:"content_modify_time,omitempty"` // 评论的被修改时间  0 表示未被修改过， ms级别时间戳
	SolvedTime        *int                               `json:"solved_time,omitempty"`         // 评论被解决的时间   0 表示未解决过， ms级别时间戳
}

type OkrCommentBuilder struct {
	id                    string // 全局评论ID
	idFlag                bool
	content               string // 全局评论内容
	contentFlag           bool
	commentTime           int // 全局评论时间 毫秒
	commentTimeFlag       bool
	commentator           *OkrObjectiveAlignedObjectiveOwner // 评论人员
	commentatorFlag       bool
	lastModifier          *OkrObjectiveAlignedObjectiveOwner // 修改评论的用户
	lastModifierFlag      bool
	contentModifyTime     int // 评论的被修改时间  0 表示未被修改过， ms级别时间戳
	contentModifyTimeFlag bool
	solvedTime            int // 评论被解决的时间   0 表示未解决过， ms级别时间戳
	solvedTimeFlag        bool
}

func NewOkrCommentBuilder() *OkrCommentBuilder {
	builder := &OkrCommentBuilder{}
	return builder
}

// 全局评论ID
//
// 示例值：6976173067307927084
func (builder *OkrCommentBuilder) Id(id string) *OkrCommentBuilder {
	builder.id = id
	builder.idFlag = true
	return builder
}

// 全局评论内容
//
// 示例值：well done
func (builder *OkrCommentBuilder) Content(content string) *OkrCommentBuilder {
	builder.content = content
	builder.contentFlag = true
	return builder
}

// 全局评论时间 毫秒
//
// 示例值：1624267575928
func (builder *OkrCommentBuilder) CommentTime(commentTime int) *OkrCommentBuilder {
	builder.commentTime = commentTime
	builder.commentTimeFlag = true
	return builder
}

// 评论人员
//
// 示例值：
func (builder *OkrCommentBuilder) Commentator(commentator *OkrObjectiveAlignedObjectiveOwner) *OkrCommentBuilder {
	builder.commentator = commentator
	builder.commentatorFlag = true
	return builder
}

// 修改评论的用户
//
// 示例值：
func (builder *OkrCommentBuilder) LastModifier(lastModifier *OkrObjectiveAlignedObjectiveOwner) *OkrCommentBuilder {
	builder.lastModifier = lastModifier
	builder.lastModifierFlag = true
	return builder
}

// 评论的被修改时间  0 表示未被修改过， ms级别时间戳
//
// 示例值：1624267575928
func (builder *OkrCommentBuilder) ContentModifyTime(contentModifyTime int) *OkrCommentBuilder {
	builder.contentModifyTime = contentModifyTime
	builder.contentModifyTimeFlag = true
	return builder
}

// 评论被解决的时间   0 表示未解决过， ms级别时间戳
//
// 示例值：1624267575928
func (builder *OkrCommentBuilder) SolvedTime(solvedTime int) *OkrCommentBuilder {
	builder.solvedTime = solvedTime
	builder.solvedTimeFlag = true
	return builder
}

func (builder *OkrCommentBuilder) Build() *OkrComment {
	req := &OkrComment{}
	if builder.idFlag {
		req.Id = &builder.id

	}
	if builder.contentFlag {
		req.Content = &builder.content

	}
	if builder.commentTimeFlag {
		req.CommentTime = &builder.commentTime

	}
	if builder.commentatorFlag {
		req.Commentator = builder.commentator
	}
	if builder.lastModifierFlag {
		req.LastModifier = builder.lastModifier
	}
	if builder.contentModifyTimeFlag {
		req.ContentModifyTime = &builder.contentModifyTime

	}
	if builder.solvedTimeFlag {
		req.SolvedTime = &builder.solvedTime

	}
	return req
}

type OkrObjectiveAlignment struct {
	Id     *string `json:"id,omitempty"`      // ID
	ToId   *string `json:"to_id,omitempty"`   // 目标id
	ToType *int    `json:"to_type,omitempty"` // 目标类型
}

type OkrObjectiveAlignmentBuilder struct {
	id         string // ID
	idFlag     bool
	toId       string // 目标id
	toIdFlag   bool
	toType     int // 目标类型
	toTypeFlag bool
}

func NewOkrObjectiveAlignmentBuilder() *OkrObjectiveAlignmentBuilder {
	builder := &OkrObjectiveAlignmentBuilder{}
	return builder
}

// ID
//
// 示例值：
func (builder *OkrObjectiveAlignmentBuilder) Id(id string) *OkrObjectiveAlignmentBuilder {
	builder.id = id
	builder.idFlag = true
	return builder
}

// 目标id
//
// 示例值：
func (builder *OkrObjectiveAlignmentBuilder) ToId(toId string) *OkrObjectiveAlignmentBuilder {
	builder.toId = toId
	builder.toIdFlag = true
	return builder
}

// 目标类型
//
// 示例值：
func (builder *OkrObjectiveAlignmentBuilder) ToType(toType int) *OkrObjectiveAlignmentBuilder {
	builder.toType = toType
	builder.toTypeFlag = true
	return builder
}

func (builder *OkrObjectiveAlignmentBuilder) Build() *OkrObjectiveAlignment {
	req := &OkrObjectiveAlignment{}
	if builder.idFlag {
		req.Id = &builder.id

	}
	if builder.toIdFlag {
		req.ToId = &builder.toId

	}
	if builder.toTypeFlag {
		req.ToType = &builder.toType

	}
	return req
}

type OkrBatch struct {
	Id            *string         `json:"id,omitempty"`             // id
	Permission    *int            `json:"permission,omitempty"`     // OKR的访问权限
	PeriodId      *string         `json:"period_id,omitempty"`      // period_id
	Name          *string         `json:"name,omitempty"`           // 名称
	ObjectiveList []*OkrObjective `json:"objective_list,omitempty"` // Objective列表
	ConfirmStatus *int            `json:"confirm_status,omitempty"` // OKR确认状态
}

type OkrBatchBuilder struct {
	id                string // id
	idFlag            bool
	permission        int // OKR的访问权限
	permissionFlag    bool
	periodId          string // period_id
	periodIdFlag      bool
	name              string // 名称
	nameFlag          bool
	objectiveList     []*OkrObjective // Objective列表
	objectiveListFlag bool
	confirmStatus     int // OKR确认状态
	confirmStatusFlag bool
}

func NewOkrBatchBuilder() *OkrBatchBuilder {
	builder := &OkrBatchBuilder{}
	return builder
}

// id
//
// 示例值：11123123123123
func (builder *OkrBatchBuilder) Id(id string) *OkrBatchBuilder {
	builder.id = id
	builder.idFlag = true
	return builder
}

// OKR的访问权限
//
// 示例值：0
func (builder *OkrBatchBuilder) Permission(permission int) *OkrBatchBuilder {
	builder.permission = permission
	builder.permissionFlag = true
	return builder
}

// period_id
//
// 示例值：11123123123123
func (builder *OkrBatchBuilder) PeriodId(periodId string) *OkrBatchBuilder {
	builder.periodId = periodId
	builder.periodIdFlag = true
	return builder
}

// 名称
//
// 示例值：My OKR
func (builder *OkrBatchBuilder) Name(name string) *OkrBatchBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

// Objective列表
//
// 示例值：
func (builder *OkrBatchBuilder) ObjectiveList(objectiveList []*OkrObjective) *OkrBatchBuilder {
	builder.objectiveList = objectiveList
	builder.objectiveListFlag = true
	return builder
}

// OKR确认状态
//
// 示例值：0
func (builder *OkrBatchBuilder) ConfirmStatus(confirmStatus int) *OkrBatchBuilder {
	builder.confirmStatus = confirmStatus
	builder.confirmStatusFlag = true
	return builder
}

func (builder *OkrBatchBuilder) Build() *OkrBatch {
	req := &OkrBatch{}
	if builder.idFlag {
		req.Id = &builder.id

	}
	if builder.permissionFlag {
		req.Permission = &builder.permission

	}
	if builder.periodIdFlag {
		req.PeriodId = &builder.periodId

	}
	if builder.nameFlag {
		req.Name = &builder.name

	}
	if builder.objectiveListFlag {
		req.ObjectiveList = builder.objectiveList
	}
	if builder.confirmStatusFlag {
		req.ConfirmStatus = &builder.confirmStatus

	}
	return req
}

type OkrDetail struct {
	OkrId         *string               `json:"okr_id,omitempty"`         // OKR ID
	PeriodId      *string               `json:"period_id,omitempty"`      // 周期 ID
	PeriodType    *string               `json:"period_type,omitempty"`    // 周期类型：1、月周期；2、年周期
	Name          *OkrName              `json:"name,omitempty"`           // 周期展示名称
	CreateTime    *int                  `json:"create_time,omitempty"`    // 创建时间
	ModifyTime    *int                  `json:"modify_time,omitempty"`    // 修改时间
	ObjectiveList []*OkrDetailObjective `json:"objective_list,omitempty"` // objective 列表
	OkrScore      *int                  `json:"okr_score,omitempty"`      // 打分
}

type OkrDetailBuilder struct {
	okrId             string // OKR ID
	okrIdFlag         bool
	periodId          string // 周期 ID
	periodIdFlag      bool
	periodType        string // 周期类型：1、月周期；2、年周期
	periodTypeFlag    bool
	name              *OkrName // 周期展示名称
	nameFlag          bool
	createTime        int // 创建时间
	createTimeFlag    bool
	modifyTime        int // 修改时间
	modifyTimeFlag    bool
	objectiveList     []*OkrDetailObjective // objective 列表
	objectiveListFlag bool
	okrScore          int // 打分
	okrScoreFlag      bool
}

func NewOkrDetailBuilder() *OkrDetailBuilder {
	builder := &OkrDetailBuilder{}
	return builder
}

// OKR ID
//
// 示例值：6976173067307927084
func (builder *OkrDetailBuilder) OkrId(okrId string) *OkrDetailBuilder {
	builder.okrId = okrId
	builder.okrIdFlag = true
	return builder
}

// 周期 ID
//
// 示例值：6974586812973581868
func (builder *OkrDetailBuilder) PeriodId(periodId string) *OkrDetailBuilder {
	builder.periodId = periodId
	builder.periodIdFlag = true
	return builder
}

// 周期类型：1、月周期；2、年周期
//
// 示例值：1
func (builder *OkrDetailBuilder) PeriodType(periodType string) *OkrDetailBuilder {
	builder.periodType = periodType
	builder.periodTypeFlag = true
	return builder
}

// 周期展示名称
//
// 示例值：
func (builder *OkrDetailBuilder) Name(name *OkrName) *OkrDetailBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

// 创建时间
//
// 示例值：1624267575928
func (builder *OkrDetailBuilder) CreateTime(createTime int) *OkrDetailBuilder {
	builder.createTime = createTime
	builder.createTimeFlag = true
	return builder
}

// 修改时间
//
// 示例值：1624329170463
func (builder *OkrDetailBuilder) ModifyTime(modifyTime int) *OkrDetailBuilder {
	builder.modifyTime = modifyTime
	builder.modifyTimeFlag = true
	return builder
}

// objective 列表
//
// 示例值：
func (builder *OkrDetailBuilder) ObjectiveList(objectiveList []*OkrDetailObjective) *OkrDetailBuilder {
	builder.objectiveList = objectiveList
	builder.objectiveListFlag = true
	return builder
}

// 打分
//
// 示例值：0.5
func (builder *OkrDetailBuilder) OkrScore(okrScore int) *OkrDetailBuilder {
	builder.okrScore = okrScore
	builder.okrScoreFlag = true
	return builder
}

func (builder *OkrDetailBuilder) Build() *OkrDetail {
	req := &OkrDetail{}
	if builder.okrIdFlag {
		req.OkrId = &builder.okrId

	}
	if builder.periodIdFlag {
		req.PeriodId = &builder.periodId

	}
	if builder.periodTypeFlag {
		req.PeriodType = &builder.periodType

	}
	if builder.nameFlag {
		req.Name = builder.name
	}
	if builder.createTimeFlag {
		req.CreateTime = &builder.createTime

	}
	if builder.modifyTimeFlag {
		req.ModifyTime = &builder.modifyTime

	}
	if builder.objectiveListFlag {
		req.ObjectiveList = builder.objectiveList
	}
	if builder.okrScoreFlag {
		req.OkrScore = &builder.okrScore

	}
	return req
}

type OkrDetailObjective struct {
	Id                    *string                    `json:"id,omitempty"`                      // Objective ID
	OkrId                 *string                    `json:"okr_id,omitempty"`                  // 所属okr id
	UserId                *string                    `json:"user_id,omitempty"`                 // 用户id
	Pos                   *int                       `json:"pos,omitempty"`                     // pos
	Score                 *int                       `json:"score,omitempty"`                   // 得分
	Name                  *OkrName                   `json:"name,omitempty"`                    // Objective 名称
	Progress              *OkrName                   `json:"progress,omitempty"`                // Objective 进度描述
	CreateTime            *int                       `json:"create_time,omitempty"`             // 创建时间 毫秒
	ModifyTime            *int                       `json:"modify_time,omitempty"`             // 修改时间 毫秒
	KrList                []*OkrDetailObjectiveKr    `json:"kr_list,omitempty"`                 // Kr list
	AligningObjectiveList []*OkrDetailObjectiveAlign `json:"aligning_objective_list,omitempty"` // aligning_objective_list list
	AlignedObjectiveList  []*OkrDetailObjectiveAlign `json:"aligned_objective_list,omitempty"`  // aligned_objective_list list
	Weight                *float64                   `json:"weight,omitempty"`                  // 权重
}

type OkrDetailObjectiveBuilder struct {
	id                        string // Objective ID
	idFlag                    bool
	okrId                     string // 所属okr id
	okrIdFlag                 bool
	userId                    string // 用户id
	userIdFlag                bool
	pos                       int // pos
	posFlag                   bool
	score                     int // 得分
	scoreFlag                 bool
	name                      *OkrName // Objective 名称
	nameFlag                  bool
	progress                  *OkrName // Objective 进度描述
	progressFlag              bool
	createTime                int // 创建时间 毫秒
	createTimeFlag            bool
	modifyTime                int // 修改时间 毫秒
	modifyTimeFlag            bool
	krList                    []*OkrDetailObjectiveKr // Kr list
	krListFlag                bool
	aligningObjectiveList     []*OkrDetailObjectiveAlign // aligning_objective_list list
	aligningObjectiveListFlag bool
	alignedObjectiveList      []*OkrDetailObjectiveAlign // aligned_objective_list list
	alignedObjectiveListFlag  bool
	weight                    float64 // 权重
	weightFlag                bool
}

func NewOkrDetailObjectiveBuilder() *OkrDetailObjectiveBuilder {
	builder := &OkrDetailObjectiveBuilder{}
	return builder
}

// Objective ID
//
// 示例值：6976243668438730284
func (builder *OkrDetailObjectiveBuilder) Id(id string) *OkrDetailObjectiveBuilder {
	builder.id = id
	builder.idFlag = true
	return builder
}

// 所属okr id
//
// 示例值：6976173067307927084
func (builder *OkrDetailObjectiveBuilder) OkrId(okrId string) *OkrDetailObjectiveBuilder {
	builder.okrId = okrId
	builder.okrIdFlag = true
	return builder
}

// 用户id
//
// 示例值：ou_186301103180d3469a0bfd6e80977ec9
func (builder *OkrDetailObjectiveBuilder) UserId(userId string) *OkrDetailObjectiveBuilder {
	builder.userId = userId
	builder.userIdFlag = true
	return builder
}

// pos
//
// 示例值：0
func (builder *OkrDetailObjectiveBuilder) Pos(pos int) *OkrDetailObjectiveBuilder {
	builder.pos = pos
	builder.posFlag = true
	return builder
}

// 得分
//
// 示例值：0
func (builder *OkrDetailObjectiveBuilder) Score(score int) *OkrDetailObjectiveBuilder {
	builder.score = score
	builder.scoreFlag = true
	return builder
}

// Objective 名称
//
// 示例值：
func (builder *OkrDetailObjectiveBuilder) Name(name *OkrName) *OkrDetailObjectiveBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

// Objective 进度描述
//
// 示例值：
func (builder *OkrDetailObjectiveBuilder) Progress(progress *OkrName) *OkrDetailObjectiveBuilder {
	builder.progress = progress
	builder.progressFlag = true
	return builder
}

// 创建时间 毫秒
//
// 示例值：1624327915435
func (builder *OkrDetailObjectiveBuilder) CreateTime(createTime int) *OkrDetailObjectiveBuilder {
	builder.createTime = createTime
	builder.createTimeFlag = true
	return builder
}

// 修改时间 毫秒
//
// 示例值：1624329170551
func (builder *OkrDetailObjectiveBuilder) ModifyTime(modifyTime int) *OkrDetailObjectiveBuilder {
	builder.modifyTime = modifyTime
	builder.modifyTimeFlag = true
	return builder
}

// Kr list
//
// 示例值：
func (builder *OkrDetailObjectiveBuilder) KrList(krList []*OkrDetailObjectiveKr) *OkrDetailObjectiveBuilder {
	builder.krList = krList
	builder.krListFlag = true
	return builder
}

// aligning_objective_list list
//
// 示例值：
func (builder *OkrDetailObjectiveBuilder) AligningObjectiveList(aligningObjectiveList []*OkrDetailObjectiveAlign) *OkrDetailObjectiveBuilder {
	builder.aligningObjectiveList = aligningObjectiveList
	builder.aligningObjectiveListFlag = true
	return builder
}

// aligned_objective_list list
//
// 示例值：
func (builder *OkrDetailObjectiveBuilder) AlignedObjectiveList(alignedObjectiveList []*OkrDetailObjectiveAlign) *OkrDetailObjectiveBuilder {
	builder.alignedObjectiveList = alignedObjectiveList
	builder.alignedObjectiveListFlag = true
	return builder
}

// 权重
//
// 示例值：100
func (builder *OkrDetailObjectiveBuilder) Weight(weight float64) *OkrDetailObjectiveBuilder {
	builder.weight = weight
	builder.weightFlag = true
	return builder
}

func (builder *OkrDetailObjectiveBuilder) Build() *OkrDetailObjective {
	req := &OkrDetailObjective{}
	if builder.idFlag {
		req.Id = &builder.id

	}
	if builder.okrIdFlag {
		req.OkrId = &builder.okrId

	}
	if builder.userIdFlag {
		req.UserId = &builder.userId

	}
	if builder.posFlag {
		req.Pos = &builder.pos

	}
	if builder.scoreFlag {
		req.Score = &builder.score

	}
	if builder.nameFlag {
		req.Name = builder.name
	}
	if builder.progressFlag {
		req.Progress = builder.progress
	}
	if builder.createTimeFlag {
		req.CreateTime = &builder.createTime

	}
	if builder.modifyTimeFlag {
		req.ModifyTime = &builder.modifyTime

	}
	if builder.krListFlag {
		req.KrList = builder.krList
	}
	if builder.aligningObjectiveListFlag {
		req.AligningObjectiveList = builder.aligningObjectiveList
	}
	if builder.alignedObjectiveListFlag {
		req.AlignedObjectiveList = builder.alignedObjectiveList
	}
	if builder.weightFlag {
		req.Weight = &builder.weight

	}
	return req
}

type OkrDetailObjectiveAlign struct {
	Id     *string `json:"id,omitempty"`      // Objective的ID
	OkrId  *string `json:"okr_id,omitempty"`  // OKR的ID
	UserId *string `json:"user_id,omitempty"` // 该Objective的Owner
}

type OkrDetailObjectiveAlignBuilder struct {
	id         string // Objective的ID
	idFlag     bool
	okrId      string // OKR的ID
	okrIdFlag  bool
	userId     string // 该Objective的Owner
	userIdFlag bool
}

func NewOkrDetailObjectiveAlignBuilder() *OkrDetailObjectiveAlignBuilder {
	builder := &OkrDetailObjectiveAlignBuilder{}
	return builder
}

// Objective的ID
//
// 示例值：6975871409026975276
func (builder *OkrDetailObjectiveAlignBuilder) Id(id string) *OkrDetailObjectiveAlignBuilder {
	builder.id = id
	builder.idFlag = true
	return builder
}

// OKR的ID
//
// 示例值：6975085709464143404
func (builder *OkrDetailObjectiveAlignBuilder) OkrId(okrId string) *OkrDetailObjectiveAlignBuilder {
	builder.okrId = okrId
	builder.okrIdFlag = true
	return builder
}

// 该Objective的Owner
//
// 示例值：6975085709464143404
func (builder *OkrDetailObjectiveAlignBuilder) UserId(userId string) *OkrDetailObjectiveAlignBuilder {
	builder.userId = userId
	builder.userIdFlag = true
	return builder
}

func (builder *OkrDetailObjectiveAlignBuilder) Build() *OkrDetailObjectiveAlign {
	req := &OkrDetailObjectiveAlign{}
	if builder.idFlag {
		req.Id = &builder.id

	}
	if builder.okrIdFlag {
		req.OkrId = &builder.okrId

	}
	if builder.userIdFlag {
		req.UserId = &builder.userId

	}
	return req
}

type OkrDetailObjectiveKr struct {
	Id         *string  `json:"id,omitempty"`          // Key Result ID
	Pos        *int     `json:"pos,omitempty"`         // pos
	Score      *int     `json:"score,omitempty"`       // KeyResult打分（0 - 100）
	Weight     *float64 `json:"weight,omitempty"`      // KeyResult的权重（0 - 100）（废弃）
	Content    *OkrName `json:"content,omitempty"`     // KeyResult 内容
	CreateTime *int     `json:"create_time,omitempty"` // 创建时间 毫秒
	ModifyTime *int     `json:"modify_time,omitempty"` // 修改时间 毫秒
}

type OkrDetailObjectiveKrBuilder struct {
	id             string // Key Result ID
	idFlag         bool
	pos            int // pos
	posFlag        bool
	score          int // KeyResult打分（0 - 100）
	scoreFlag      bool
	weight         float64 // KeyResult的权重（0 - 100）（废弃）
	weightFlag     bool
	content        *OkrName // KeyResult 内容
	contentFlag    bool
	createTime     int // 创建时间 毫秒
	createTimeFlag bool
	modifyTime     int // 修改时间 毫秒
	modifyTimeFlag bool
}

func NewOkrDetailObjectiveKrBuilder() *OkrDetailObjectiveKrBuilder {
	builder := &OkrDetailObjectiveKrBuilder{}
	return builder
}

// Key Result ID
//
// 示例值：6976243668438992428
func (builder *OkrDetailObjectiveKrBuilder) Id(id string) *OkrDetailObjectiveKrBuilder {
	builder.id = id
	builder.idFlag = true
	return builder
}

// pos
//
// 示例值：100
func (builder *OkrDetailObjectiveKrBuilder) Pos(pos int) *OkrDetailObjectiveKrBuilder {
	builder.pos = pos
	builder.posFlag = true
	return builder
}

// KeyResult打分（0 - 100）
//
// 示例值：100
func (builder *OkrDetailObjectiveKrBuilder) Score(score int) *OkrDetailObjectiveKrBuilder {
	builder.score = score
	builder.scoreFlag = true
	return builder
}

// KeyResult的权重（0 - 100）（废弃）
//
// 示例值：100
func (builder *OkrDetailObjectiveKrBuilder) Weight(weight float64) *OkrDetailObjectiveKrBuilder {
	builder.weight = weight
	builder.weightFlag = true
	return builder
}

// KeyResult 内容
//
// 示例值：
func (builder *OkrDetailObjectiveKrBuilder) Content(content *OkrName) *OkrDetailObjectiveKrBuilder {
	builder.content = content
	builder.contentFlag = true
	return builder
}

// 创建时间 毫秒
//
// 示例值：1624327915448
func (builder *OkrDetailObjectiveKrBuilder) CreateTime(createTime int) *OkrDetailObjectiveKrBuilder {
	builder.createTime = createTime
	builder.createTimeFlag = true
	return builder
}

// 修改时间 毫秒
//
// 示例值：1624327915448
func (builder *OkrDetailObjectiveKrBuilder) ModifyTime(modifyTime int) *OkrDetailObjectiveKrBuilder {
	builder.modifyTime = modifyTime
	builder.modifyTimeFlag = true
	return builder
}

func (builder *OkrDetailObjectiveKrBuilder) Build() *OkrDetailObjectiveKr {
	req := &OkrDetailObjectiveKr{}
	if builder.idFlag {
		req.Id = &builder.id

	}
	if builder.posFlag {
		req.Pos = &builder.pos

	}
	if builder.scoreFlag {
		req.Score = &builder.score

	}
	if builder.weightFlag {
		req.Weight = &builder.weight

	}
	if builder.contentFlag {
		req.Content = builder.content
	}
	if builder.createTimeFlag {
		req.CreateTime = &builder.createTime

	}
	if builder.modifyTimeFlag {
		req.ModifyTime = &builder.modifyTime

	}
	return req
}

type OkrListInfo struct {
	CurrentOkr *Okr   `json:"current_okr,omitempty"` // 中文内容
	OkrList    []*Okr `json:"okr_list,omitempty"`    // 英文内容
}

type OkrListInfoBuilder struct {
	currentOkr     *Okr // 中文内容
	currentOkrFlag bool
	okrList        []*Okr // 英文内容
	okrListFlag    bool
}

func NewOkrListInfoBuilder() *OkrListInfoBuilder {
	builder := &OkrListInfoBuilder{}
	return builder
}

// 中文内容
//
// 示例值：
func (builder *OkrListInfoBuilder) CurrentOkr(currentOkr *Okr) *OkrListInfoBuilder {
	builder.currentOkr = currentOkr
	builder.currentOkrFlag = true
	return builder
}

// 英文内容
//
// 示例值：
func (builder *OkrListInfoBuilder) OkrList(okrList []*Okr) *OkrListInfoBuilder {
	builder.okrList = okrList
	builder.okrListFlag = true
	return builder
}

func (builder *OkrListInfoBuilder) Build() *OkrListInfo {
	req := &OkrListInfo{}
	if builder.currentOkrFlag {
		req.CurrentOkr = builder.currentOkr
	}
	if builder.okrListFlag {
		req.OkrList = builder.okrList
	}
	return req
}

type OkrName struct {
	Zh *string `json:"zh,omitempty"` // 中文内容
	En *string `json:"en,omitempty"` // 英文内容
}

type OkrNameBuilder struct {
	zh     string // 中文内容
	zhFlag bool
	en     string // 英文内容
	enFlag bool
}

func NewOkrNameBuilder() *OkrNameBuilder {
	builder := &OkrNameBuilder{}
	return builder
}

// 中文内容
//
// 示例值：
func (builder *OkrNameBuilder) Zh(zh string) *OkrNameBuilder {
	builder.zh = zh
	builder.zhFlag = true
	return builder
}

// 英文内容
//
// 示例值：
func (builder *OkrNameBuilder) En(en string) *OkrNameBuilder {
	builder.en = en
	builder.enFlag = true
	return builder
}

func (builder *OkrNameBuilder) Build() *OkrName {
	req := &OkrName{}
	if builder.zhFlag {
		req.Zh = &builder.zh

	}
	if builder.enFlag {
		req.En = &builder.en

	}
	return req
}

type OkrObjective struct {
	Id                                 *string                              `json:"id,omitempty"`                                      // Objective ID
	Permission                         *int                                 `json:"permission,omitempty"`                              // 权限
	Content                            *string                              `json:"content,omitempty"`                                 // Objective 内容
	ProgressReport                     *string                              `json:"progress_report,omitempty"`                         // Objective 备注内容
	Score                              *int                                 `json:"score,omitempty"`                                   // Objective 分数（0 - 100）
	Weight                             *float64                             `json:"weight,omitempty"`                                  // Objective的权重（0 - 100）
	ProgressRate                       *OkrObjectiveProgressRate            `json:"progress_rate,omitempty"`                           // Objective进度
	KrList                             []*OkrObjectiveKr                    `json:"kr_list,omitempty"`                                 // Objective KeyResult 列表
	AlignedObjectiveList               []*OkrObjectiveAlignedObjective      `json:"aligned_objective_list,omitempty"`                  // 对齐到该Objective的Objective列表
	AligningObjectiveList              []*OkrObjectiveAlignedObjective      `json:"aligning_objective_list,omitempty"`                 // 该Objective对齐到的Objective列表
	ProgressRecordList                 []*ProgressRecordSimplify            `json:"progress_record_list,omitempty"`                    // 该Objective的进度列表
	ProgressRatePercentLastUpdatedTime *string                              `json:"progress_rate_percent_last_updated_time,omitempty"` // 最后一次进度百分比更新时间 毫秒
	ProgressRateStatusLastUpdatedTime  *string                              `json:"progress_rate_status_last_updated_time,omitempty"`  // 最后一次状态更新时间 毫秒
	ProgressRecordLastUpdatedTime      *string                              `json:"progress_record_last_updated_time,omitempty"`       // 最后一次在侧边栏新增或者编辑进展的时间 毫秒
	ProgressReportLastUpdatedTime      *string                              `json:"progress_report_last_updated_time,omitempty"`       // 最后一次编辑备注的时间 毫秒
	ScoreLastUpdatedTime               *string                              `json:"score_last_updated_time,omitempty"`                 // 最后一次打分更新时间 毫秒
	Deadline                           *string                              `json:"deadline,omitempty"`                                // 截止时间 毫秒
	MentionedUserList                  []*OkrObjectiveAlignedObjectiveOwner `json:"mentioned_user_list,omitempty"`                     // 该Objective提到的人员列表
}

type OkrObjectiveBuilder struct {
	id                                     string // Objective ID
	idFlag                                 bool
	permission                             int // 权限
	permissionFlag                         bool
	content                                string // Objective 内容
	contentFlag                            bool
	progressReport                         string // Objective 备注内容
	progressReportFlag                     bool
	score                                  int // Objective 分数（0 - 100）
	scoreFlag                              bool
	weight                                 float64 // Objective的权重（0 - 100）
	weightFlag                             bool
	progressRate                           *OkrObjectiveProgressRate // Objective进度
	progressRateFlag                       bool
	krList                                 []*OkrObjectiveKr // Objective KeyResult 列表
	krListFlag                             bool
	alignedObjectiveList                   []*OkrObjectiveAlignedObjective // 对齐到该Objective的Objective列表
	alignedObjectiveListFlag               bool
	aligningObjectiveList                  []*OkrObjectiveAlignedObjective // 该Objective对齐到的Objective列表
	aligningObjectiveListFlag              bool
	progressRecordList                     []*ProgressRecordSimplify // 该Objective的进度列表
	progressRecordListFlag                 bool
	progressRatePercentLastUpdatedTime     string // 最后一次进度百分比更新时间 毫秒
	progressRatePercentLastUpdatedTimeFlag bool
	progressRateStatusLastUpdatedTime      string // 最后一次状态更新时间 毫秒
	progressRateStatusLastUpdatedTimeFlag  bool
	progressRecordLastUpdatedTime          string // 最后一次在侧边栏新增或者编辑进展的时间 毫秒
	progressRecordLastUpdatedTimeFlag      bool
	progressReportLastUpdatedTime          string // 最后一次编辑备注的时间 毫秒
	progressReportLastUpdatedTimeFlag      bool
	scoreLastUpdatedTime                   string // 最后一次打分更新时间 毫秒
	scoreLastUpdatedTimeFlag               bool
	deadline                               string // 截止时间 毫秒
	deadlineFlag                           bool
	mentionedUserList                      []*OkrObjectiveAlignedObjectiveOwner // 该Objective提到的人员列表
	mentionedUserListFlag                  bool
}

func NewOkrObjectiveBuilder() *OkrObjectiveBuilder {
	builder := &OkrObjectiveBuilder{}
	return builder
}

// Objective ID
//
// 示例值：1231231231231
func (builder *OkrObjectiveBuilder) Id(id string) *OkrObjectiveBuilder {
	builder.id = id
	builder.idFlag = true
	return builder
}

// 权限
//
// 示例值：0
func (builder *OkrObjectiveBuilder) Permission(permission int) *OkrObjectiveBuilder {
	builder.permission = permission
	builder.permissionFlag = true
	return builder
}

// Objective 内容
//
// 示例值：Objective 内容
func (builder *OkrObjectiveBuilder) Content(content string) *OkrObjectiveBuilder {
	builder.content = content
	builder.contentFlag = true
	return builder
}

// Objective 备注内容
//
// 示例值：Objective 进度记录内容
func (builder *OkrObjectiveBuilder) ProgressReport(progressReport string) *OkrObjectiveBuilder {
	builder.progressReport = progressReport
	builder.progressReportFlag = true
	return builder
}

// Objective 分数（0 - 100）
//
// 示例值：100
func (builder *OkrObjectiveBuilder) Score(score int) *OkrObjectiveBuilder {
	builder.score = score
	builder.scoreFlag = true
	return builder
}

// Objective的权重（0 - 100）
//
// 示例值：33.33
func (builder *OkrObjectiveBuilder) Weight(weight float64) *OkrObjectiveBuilder {
	builder.weight = weight
	builder.weightFlag = true
	return builder
}

// Objective进度
//
// 示例值：
func (builder *OkrObjectiveBuilder) ProgressRate(progressRate *OkrObjectiveProgressRate) *OkrObjectiveBuilder {
	builder.progressRate = progressRate
	builder.progressRateFlag = true
	return builder
}

// Objective KeyResult 列表
//
// 示例值：
func (builder *OkrObjectiveBuilder) KrList(krList []*OkrObjectiveKr) *OkrObjectiveBuilder {
	builder.krList = krList
	builder.krListFlag = true
	return builder
}

// 对齐到该Objective的Objective列表
//
// 示例值：
func (builder *OkrObjectiveBuilder) AlignedObjectiveList(alignedObjectiveList []*OkrObjectiveAlignedObjective) *OkrObjectiveBuilder {
	builder.alignedObjectiveList = alignedObjectiveList
	builder.alignedObjectiveListFlag = true
	return builder
}

// 该Objective对齐到的Objective列表
//
// 示例值：
func (builder *OkrObjectiveBuilder) AligningObjectiveList(aligningObjectiveList []*OkrObjectiveAlignedObjective) *OkrObjectiveBuilder {
	builder.aligningObjectiveList = aligningObjectiveList
	builder.aligningObjectiveListFlag = true
	return builder
}

// 该Objective的进度列表
//
// 示例值：
func (builder *OkrObjectiveBuilder) ProgressRecordList(progressRecordList []*ProgressRecordSimplify) *OkrObjectiveBuilder {
	builder.progressRecordList = progressRecordList
	builder.progressRecordListFlag = true
	return builder
}

// 最后一次进度百分比更新时间 毫秒
//
// 示例值：1618500278663
func (builder *OkrObjectiveBuilder) ProgressRatePercentLastUpdatedTime(progressRatePercentLastUpdatedTime string) *OkrObjectiveBuilder {
	builder.progressRatePercentLastUpdatedTime = progressRatePercentLastUpdatedTime
	builder.progressRatePercentLastUpdatedTimeFlag = true
	return builder
}

// 最后一次状态更新时间 毫秒
//
// 示例值：1618500278663
func (builder *OkrObjectiveBuilder) ProgressRateStatusLastUpdatedTime(progressRateStatusLastUpdatedTime string) *OkrObjectiveBuilder {
	builder.progressRateStatusLastUpdatedTime = progressRateStatusLastUpdatedTime
	builder.progressRateStatusLastUpdatedTimeFlag = true
	return builder
}

// 最后一次在侧边栏新增或者编辑进展的时间 毫秒
//
// 示例值：1618500278663
func (builder *OkrObjectiveBuilder) ProgressRecordLastUpdatedTime(progressRecordLastUpdatedTime string) *OkrObjectiveBuilder {
	builder.progressRecordLastUpdatedTime = progressRecordLastUpdatedTime
	builder.progressRecordLastUpdatedTimeFlag = true
	return builder
}

// 最后一次编辑备注的时间 毫秒
//
// 示例值：1618500278663
func (builder *OkrObjectiveBuilder) ProgressReportLastUpdatedTime(progressReportLastUpdatedTime string) *OkrObjectiveBuilder {
	builder.progressReportLastUpdatedTime = progressReportLastUpdatedTime
	builder.progressReportLastUpdatedTimeFlag = true
	return builder
}

// 最后一次打分更新时间 毫秒
//
// 示例值：1618500278663
func (builder *OkrObjectiveBuilder) ScoreLastUpdatedTime(scoreLastUpdatedTime string) *OkrObjectiveBuilder {
	builder.scoreLastUpdatedTime = scoreLastUpdatedTime
	builder.scoreLastUpdatedTimeFlag = true
	return builder
}

// 截止时间 毫秒
//
// 示例值：1618500278663
func (builder *OkrObjectiveBuilder) Deadline(deadline string) *OkrObjectiveBuilder {
	builder.deadline = deadline
	builder.deadlineFlag = true
	return builder
}

// 该Objective提到的人员列表
//
// 示例值：
func (builder *OkrObjectiveBuilder) MentionedUserList(mentionedUserList []*OkrObjectiveAlignedObjectiveOwner) *OkrObjectiveBuilder {
	builder.mentionedUserList = mentionedUserList
	builder.mentionedUserListFlag = true
	return builder
}

func (builder *OkrObjectiveBuilder) Build() *OkrObjective {
	req := &OkrObjective{}
	if builder.idFlag {
		req.Id = &builder.id

	}
	if builder.permissionFlag {
		req.Permission = &builder.permission

	}
	if builder.contentFlag {
		req.Content = &builder.content

	}
	if builder.progressReportFlag {
		req.ProgressReport = &builder.progressReport

	}
	if builder.scoreFlag {
		req.Score = &builder.score

	}
	if builder.weightFlag {
		req.Weight = &builder.weight

	}
	if builder.progressRateFlag {
		req.ProgressRate = builder.progressRate
	}
	if builder.krListFlag {
		req.KrList = builder.krList
	}
	if builder.alignedObjectiveListFlag {
		req.AlignedObjectiveList = builder.alignedObjectiveList
	}
	if builder.aligningObjectiveListFlag {
		req.AligningObjectiveList = builder.aligningObjectiveList
	}
	if builder.progressRecordListFlag {
		req.ProgressRecordList = builder.progressRecordList
	}
	if builder.progressRatePercentLastUpdatedTimeFlag {
		req.ProgressRatePercentLastUpdatedTime = &builder.progressRatePercentLastUpdatedTime

	}
	if builder.progressRateStatusLastUpdatedTimeFlag {
		req.ProgressRateStatusLastUpdatedTime = &builder.progressRateStatusLastUpdatedTime

	}
	if builder.progressRecordLastUpdatedTimeFlag {
		req.ProgressRecordLastUpdatedTime = &builder.progressRecordLastUpdatedTime

	}
	if builder.progressReportLastUpdatedTimeFlag {
		req.ProgressReportLastUpdatedTime = &builder.progressReportLastUpdatedTime

	}
	if builder.scoreLastUpdatedTimeFlag {
		req.ScoreLastUpdatedTime = &builder.scoreLastUpdatedTime

	}
	if builder.deadlineFlag {
		req.Deadline = &builder.deadline

	}
	if builder.mentionedUserListFlag {
		req.MentionedUserList = builder.mentionedUserList
	}
	return req
}

type OkrObjectiveAlignedObjective struct {
	Id    *string                            `json:"id,omitempty"`     // Objective的ID
	OkrId *string                            `json:"okr_id,omitempty"` // OKR的ID
	Owner *OkrObjectiveAlignedObjectiveOwner `json:"owner,omitempty"`  // 该Objective的Owner
}

type OkrObjectiveAlignedObjectiveBuilder struct {
	id        string // Objective的ID
	idFlag    bool
	okrId     string // OKR的ID
	okrIdFlag bool
	owner     *OkrObjectiveAlignedObjectiveOwner // 该Objective的Owner
	ownerFlag bool
}

func NewOkrObjectiveAlignedObjectiveBuilder() *OkrObjectiveAlignedObjectiveBuilder {
	builder := &OkrObjectiveAlignedObjectiveBuilder{}
	return builder
}

// Objective的ID
//
// 示例值：1231231231213
func (builder *OkrObjectiveAlignedObjectiveBuilder) Id(id string) *OkrObjectiveAlignedObjectiveBuilder {
	builder.id = id
	builder.idFlag = true
	return builder
}

// OKR的ID
//
// 示例值：12323231231213
func (builder *OkrObjectiveAlignedObjectiveBuilder) OkrId(okrId string) *OkrObjectiveAlignedObjectiveBuilder {
	builder.okrId = okrId
	builder.okrIdFlag = true
	return builder
}

// 该Objective的Owner
//
// 示例值：
func (builder *OkrObjectiveAlignedObjectiveBuilder) Owner(owner *OkrObjectiveAlignedObjectiveOwner) *OkrObjectiveAlignedObjectiveBuilder {
	builder.owner = owner
	builder.ownerFlag = true
	return builder
}

func (builder *OkrObjectiveAlignedObjectiveBuilder) Build() *OkrObjectiveAlignedObjective {
	req := &OkrObjectiveAlignedObjective{}
	if builder.idFlag {
		req.Id = &builder.id

	}
	if builder.okrIdFlag {
		req.OkrId = &builder.okrId

	}
	if builder.ownerFlag {
		req.Owner = builder.owner
	}
	return req
}

type OkrObjectiveAlignedObjectiveOwner struct {
	OpenId *string `json:"open_id,omitempty"` // 用户的 open_id
	UserId *string `json:"user_id,omitempty"` // 用户的 user_id
}

type OkrObjectiveAlignedObjectiveOwnerBuilder struct {
	openId     string // 用户的 open_id
	openIdFlag bool
	userId     string // 用户的 user_id
	userIdFlag bool
}

func NewOkrObjectiveAlignedObjectiveOwnerBuilder() *OkrObjectiveAlignedObjectiveOwnerBuilder {
	builder := &OkrObjectiveAlignedObjectiveOwnerBuilder{}
	return builder
}

// 用户的 open_id
//
// 示例值：od-asd2dasdasd
func (builder *OkrObjectiveAlignedObjectiveOwnerBuilder) OpenId(openId string) *OkrObjectiveAlignedObjectiveOwnerBuilder {
	builder.openId = openId
	builder.openIdFlag = true
	return builder
}

// 用户的 user_id
//
// 示例值：ou-ux987dsf6x
func (builder *OkrObjectiveAlignedObjectiveOwnerBuilder) UserId(userId string) *OkrObjectiveAlignedObjectiveOwnerBuilder {
	builder.userId = userId
	builder.userIdFlag = true
	return builder
}

func (builder *OkrObjectiveAlignedObjectiveOwnerBuilder) Build() *OkrObjectiveAlignedObjectiveOwner {
	req := &OkrObjectiveAlignedObjectiveOwner{}
	if builder.openIdFlag {
		req.OpenId = &builder.openId

	}
	if builder.userIdFlag {
		req.UserId = &builder.userId

	}
	return req
}

type OkrObjectiveKr struct {
	Id                                 *string                              `json:"id,omitempty"`                                      // Key Result ID
	Content                            *string                              `json:"content,omitempty"`                                 // KeyResult 内容
	Score                              *int                                 `json:"score,omitempty"`                                   // KeyResult打分（0 - 100）
	Weight                             *int                                 `json:"weight,omitempty"`                                  // KeyResult权重（0 - 100）（废弃）
	KrWeight                           *float64                             `json:"kr_weight,omitempty"`                               // KeyResult的权重（0 - 100）
	ProgressRate                       *OkrObjectiveProgressRate            `json:"progress_rate,omitempty"`                           // KR进度
	ProgressRecordList                 []*ProgressRecordSimplify            `json:"progress_record_list,omitempty"`                    // 该KR的进度列表
	ProgressRatePercentLastUpdatedTime *string                              `json:"progress_rate_percent_last_updated_time,omitempty"` // 最后一次进度百分比更新时间 毫秒
	ProgressRateStatusLastUpdatedTime  *string                              `json:"progress_rate_status_last_updated_time,omitempty"`  // 最后一次状态更新时间 毫秒
	ProgressRecordLastUpdatedTime      *string                              `json:"progress_record_last_updated_time,omitempty"`       // 最后一次在侧边栏新增或者编辑进展的时间 毫秒
	ProgressReportLastUpdatedTime      *string                              `json:"progress_report_last_updated_time,omitempty"`       // 最后一次编辑备注的时间 毫秒
	ScoreLastUpdatedTime               *string                              `json:"score_last_updated_time,omitempty"`                 // 最后一次打分更新时间 毫秒
	Deadline                           *string                              `json:"deadline,omitempty"`                                // 截止时间 毫秒
	MentionedUserList                  []*OkrObjectiveAlignedObjectiveOwner `json:"mentioned_user_list,omitempty"`                     // 该Objective提到的人员列表
}

type OkrObjectiveKrBuilder struct {
	id                                     string // Key Result ID
	idFlag                                 bool
	content                                string // KeyResult 内容
	contentFlag                            bool
	score                                  int // KeyResult打分（0 - 100）
	scoreFlag                              bool
	weight                                 int // KeyResult权重（0 - 100）（废弃）
	weightFlag                             bool
	krWeight                               float64 // KeyResult的权重（0 - 100）
	krWeightFlag                           bool
	progressRate                           *OkrObjectiveProgressRate // KR进度
	progressRateFlag                       bool
	progressRecordList                     []*ProgressRecordSimplify // 该KR的进度列表
	progressRecordListFlag                 bool
	progressRatePercentLastUpdatedTime     string // 最后一次进度百分比更新时间 毫秒
	progressRatePercentLastUpdatedTimeFlag bool
	progressRateStatusLastUpdatedTime      string // 最后一次状态更新时间 毫秒
	progressRateStatusLastUpdatedTimeFlag  bool
	progressRecordLastUpdatedTime          string // 最后一次在侧边栏新增或者编辑进展的时间 毫秒
	progressRecordLastUpdatedTimeFlag      bool
	progressReportLastUpdatedTime          string // 最后一次编辑备注的时间 毫秒
	progressReportLastUpdatedTimeFlag      bool
	scoreLastUpdatedTime                   string // 最后一次打分更新时间 毫秒
	scoreLastUpdatedTimeFlag               bool
	deadline                               string // 截止时间 毫秒
	deadlineFlag                           bool
	mentionedUserList                      []*OkrObjectiveAlignedObjectiveOwner // 该Objective提到的人员列表
	mentionedUserListFlag                  bool
}

func NewOkrObjectiveKrBuilder() *OkrObjectiveKrBuilder {
	builder := &OkrObjectiveKrBuilder{}
	return builder
}

// Key Result ID
//
// 示例值：1231231223
func (builder *OkrObjectiveKrBuilder) Id(id string) *OkrObjectiveKrBuilder {
	builder.id = id
	builder.idFlag = true
	return builder
}

// KeyResult 内容
//
// 示例值：KeyResult 内容
func (builder *OkrObjectiveKrBuilder) Content(content string) *OkrObjectiveKrBuilder {
	builder.content = content
	builder.contentFlag = true
	return builder
}

// KeyResult打分（0 - 100）
//
// 示例值：100
func (builder *OkrObjectiveKrBuilder) Score(score int) *OkrObjectiveKrBuilder {
	builder.score = score
	builder.scoreFlag = true
	return builder
}

// KeyResult权重（0 - 100）（废弃）
//
// 示例值：100
func (builder *OkrObjectiveKrBuilder) Weight(weight int) *OkrObjectiveKrBuilder {
	builder.weight = weight
	builder.weightFlag = true
	return builder
}

// KeyResult的权重（0 - 100）
//
// 示例值：33.33
func (builder *OkrObjectiveKrBuilder) KrWeight(krWeight float64) *OkrObjectiveKrBuilder {
	builder.krWeight = krWeight
	builder.krWeightFlag = true
	return builder
}

// KR进度
//
// 示例值：
func (builder *OkrObjectiveKrBuilder) ProgressRate(progressRate *OkrObjectiveProgressRate) *OkrObjectiveKrBuilder {
	builder.progressRate = progressRate
	builder.progressRateFlag = true
	return builder
}

// 该KR的进度列表
//
// 示例值：
func (builder *OkrObjectiveKrBuilder) ProgressRecordList(progressRecordList []*ProgressRecordSimplify) *OkrObjectiveKrBuilder {
	builder.progressRecordList = progressRecordList
	builder.progressRecordListFlag = true
	return builder
}

// 最后一次进度百分比更新时间 毫秒
//
// 示例值：1618500278663
func (builder *OkrObjectiveKrBuilder) ProgressRatePercentLastUpdatedTime(progressRatePercentLastUpdatedTime string) *OkrObjectiveKrBuilder {
	builder.progressRatePercentLastUpdatedTime = progressRatePercentLastUpdatedTime
	builder.progressRatePercentLastUpdatedTimeFlag = true
	return builder
}

// 最后一次状态更新时间 毫秒
//
// 示例值：1618500278663
func (builder *OkrObjectiveKrBuilder) ProgressRateStatusLastUpdatedTime(progressRateStatusLastUpdatedTime string) *OkrObjectiveKrBuilder {
	builder.progressRateStatusLastUpdatedTime = progressRateStatusLastUpdatedTime
	builder.progressRateStatusLastUpdatedTimeFlag = true
	return builder
}

// 最后一次在侧边栏新增或者编辑进展的时间 毫秒
//
// 示例值：1618500278663
func (builder *OkrObjectiveKrBuilder) ProgressRecordLastUpdatedTime(progressRecordLastUpdatedTime string) *OkrObjectiveKrBuilder {
	builder.progressRecordLastUpdatedTime = progressRecordLastUpdatedTime
	builder.progressRecordLastUpdatedTimeFlag = true
	return builder
}

// 最后一次编辑备注的时间 毫秒
//
// 示例值：1618500278663
func (builder *OkrObjectiveKrBuilder) ProgressReportLastUpdatedTime(progressReportLastUpdatedTime string) *OkrObjectiveKrBuilder {
	builder.progressReportLastUpdatedTime = progressReportLastUpdatedTime
	builder.progressReportLastUpdatedTimeFlag = true
	return builder
}

// 最后一次打分更新时间 毫秒
//
// 示例值：1618500278663
func (builder *OkrObjectiveKrBuilder) ScoreLastUpdatedTime(scoreLastUpdatedTime string) *OkrObjectiveKrBuilder {
	builder.scoreLastUpdatedTime = scoreLastUpdatedTime
	builder.scoreLastUpdatedTimeFlag = true
	return builder
}

// 截止时间 毫秒
//
// 示例值：1618500278663
func (builder *OkrObjectiveKrBuilder) Deadline(deadline string) *OkrObjectiveKrBuilder {
	builder.deadline = deadline
	builder.deadlineFlag = true
	return builder
}

// 该Objective提到的人员列表
//
// 示例值：
func (builder *OkrObjectiveKrBuilder) MentionedUserList(mentionedUserList []*OkrObjectiveAlignedObjectiveOwner) *OkrObjectiveKrBuilder {
	builder.mentionedUserList = mentionedUserList
	builder.mentionedUserListFlag = true
	return builder
}

func (builder *OkrObjectiveKrBuilder) Build() *OkrObjectiveKr {
	req := &OkrObjectiveKr{}
	if builder.idFlag {
		req.Id = &builder.id

	}
	if builder.contentFlag {
		req.Content = &builder.content

	}
	if builder.scoreFlag {
		req.Score = &builder.score

	}
	if builder.weightFlag {
		req.Weight = &builder.weight

	}
	if builder.krWeightFlag {
		req.KrWeight = &builder.krWeight

	}
	if builder.progressRateFlag {
		req.ProgressRate = builder.progressRate
	}
	if builder.progressRecordListFlag {
		req.ProgressRecordList = builder.progressRecordList
	}
	if builder.progressRatePercentLastUpdatedTimeFlag {
		req.ProgressRatePercentLastUpdatedTime = &builder.progressRatePercentLastUpdatedTime

	}
	if builder.progressRateStatusLastUpdatedTimeFlag {
		req.ProgressRateStatusLastUpdatedTime = &builder.progressRateStatusLastUpdatedTime

	}
	if builder.progressRecordLastUpdatedTimeFlag {
		req.ProgressRecordLastUpdatedTime = &builder.progressRecordLastUpdatedTime

	}
	if builder.progressReportLastUpdatedTimeFlag {
		req.ProgressReportLastUpdatedTime = &builder.progressReportLastUpdatedTime

	}
	if builder.scoreLastUpdatedTimeFlag {
		req.ScoreLastUpdatedTime = &builder.scoreLastUpdatedTime

	}
	if builder.deadlineFlag {
		req.Deadline = &builder.deadline

	}
	if builder.mentionedUserListFlag {
		req.MentionedUserList = builder.mentionedUserList
	}
	return req
}

type OkrObjectiveProgressRate struct {
	Percent *int    `json:"percent,omitempty"` // Objective 进度百分比 >= 0
	Status  *string `json:"status,omitempty"`  // Objective 进度状态
}

type OkrObjectiveProgressRateBuilder struct {
	percent     int // Objective 进度百分比 >= 0
	percentFlag bool
	status      string // Objective 进度状态
	statusFlag  bool
}

func NewOkrObjectiveProgressRateBuilder() *OkrObjectiveProgressRateBuilder {
	builder := &OkrObjectiveProgressRateBuilder{}
	return builder
}

// Objective 进度百分比 >= 0
//
// 示例值：50
func (builder *OkrObjectiveProgressRateBuilder) Percent(percent int) *OkrObjectiveProgressRateBuilder {
	builder.percent = percent
	builder.percentFlag = true
	return builder
}

// Objective 进度状态
//
// 示例值：1
func (builder *OkrObjectiveProgressRateBuilder) Status(status string) *OkrObjectiveProgressRateBuilder {
	builder.status = status
	builder.statusFlag = true
	return builder
}

func (builder *OkrObjectiveProgressRateBuilder) Build() *OkrObjectiveProgressRate {
	req := &OkrObjectiveProgressRate{}
	if builder.percentFlag {
		req.Percent = &builder.percent

	}
	if builder.statusFlag {
		req.Status = &builder.status

	}
	return req
}

type OkrReview struct {
	UserId           *OkrObjectiveAlignedObjectiveOwner `json:"user_id,omitempty"`            // 复盘的用户
	ReviewPeriodList []*OkrReviewPeriod                 `json:"review_period_list,omitempty"` // 用户对应的OKR复盘列表
}

type OkrReviewBuilder struct {
	userId               *OkrObjectiveAlignedObjectiveOwner // 复盘的用户
	userIdFlag           bool
	reviewPeriodList     []*OkrReviewPeriod // 用户对应的OKR复盘列表
	reviewPeriodListFlag bool
}

func NewOkrReviewBuilder() *OkrReviewBuilder {
	builder := &OkrReviewBuilder{}
	return builder
}

// 复盘的用户
//
// 示例值：
func (builder *OkrReviewBuilder) UserId(userId *OkrObjectiveAlignedObjectiveOwner) *OkrReviewBuilder {
	builder.userId = userId
	builder.userIdFlag = true
	return builder
}

// 用户对应的OKR复盘列表
//
// 示例值：
func (builder *OkrReviewBuilder) ReviewPeriodList(reviewPeriodList []*OkrReviewPeriod) *OkrReviewBuilder {
	builder.reviewPeriodList = reviewPeriodList
	builder.reviewPeriodListFlag = true
	return builder
}

func (builder *OkrReviewBuilder) Build() *OkrReview {
	req := &OkrReview{}
	if builder.userIdFlag {
		req.UserId = builder.userId
	}
	if builder.reviewPeriodListFlag {
		req.ReviewPeriodList = builder.reviewPeriodList
	}
	return req
}

type OkrReviewPeriod struct {
	PeriodId           *string               `json:"period_id,omitempty"`            // 周期ID
	CycleReviewList    []*OkrReviewPeriodUrl `json:"cycle_review_list,omitempty"`    // 复盘文档
	ProgressReportList []*OkrReviewPeriodUrl `json:"progress_report_list,omitempty"` // 进展报告
}

type OkrReviewPeriodBuilder struct {
	periodId               string // 周期ID
	periodIdFlag           bool
	cycleReviewList        []*OkrReviewPeriodUrl // 复盘文档
	cycleReviewListFlag    bool
	progressReportList     []*OkrReviewPeriodUrl // 进展报告
	progressReportListFlag bool
}

func NewOkrReviewPeriodBuilder() *OkrReviewPeriodBuilder {
	builder := &OkrReviewPeriodBuilder{}
	return builder
}

// 周期ID
//
// 示例值：6951461264858777132
func (builder *OkrReviewPeriodBuilder) PeriodId(periodId string) *OkrReviewPeriodBuilder {
	builder.periodId = periodId
	builder.periodIdFlag = true
	return builder
}

// 复盘文档
//
// 示例值：
func (builder *OkrReviewPeriodBuilder) CycleReviewList(cycleReviewList []*OkrReviewPeriodUrl) *OkrReviewPeriodBuilder {
	builder.cycleReviewList = cycleReviewList
	builder.cycleReviewListFlag = true
	return builder
}

// 进展报告
//
// 示例值：
func (builder *OkrReviewPeriodBuilder) ProgressReportList(progressReportList []*OkrReviewPeriodUrl) *OkrReviewPeriodBuilder {
	builder.progressReportList = progressReportList
	builder.progressReportListFlag = true
	return builder
}

func (builder *OkrReviewPeriodBuilder) Build() *OkrReviewPeriod {
	req := &OkrReviewPeriod{}
	if builder.periodIdFlag {
		req.PeriodId = &builder.periodId

	}
	if builder.cycleReviewListFlag {
		req.CycleReviewList = builder.cycleReviewList
	}
	if builder.progressReportListFlag {
		req.ProgressReportList = builder.progressReportList
	}
	return req
}

type OkrReviewPeriodUrl struct {
	Url        *string `json:"url,omitempty"`         // 文档链接
	CreateTime *string `json:"create_time,omitempty"` // 创建时间 毫秒
}

type OkrReviewPeriodUrlBuilder struct {
	url            string // 文档链接
	urlFlag        bool
	createTime     string // 创建时间 毫秒
	createTimeFlag bool
}

func NewOkrReviewPeriodUrlBuilder() *OkrReviewPeriodUrlBuilder {
	builder := &OkrReviewPeriodUrlBuilder{}
	return builder
}

// 文档链接
//
// 示例值：https://bytedance.feishu.cn/drive/home/
func (builder *OkrReviewPeriodUrlBuilder) Url(url string) *OkrReviewPeriodUrlBuilder {
	builder.url = url
	builder.urlFlag = true
	return builder
}

// 创建时间 毫秒
//
// 示例值：1618500278663
func (builder *OkrReviewPeriodUrlBuilder) CreateTime(createTime string) *OkrReviewPeriodUrlBuilder {
	builder.createTime = createTime
	builder.createTimeFlag = true
	return builder
}

func (builder *OkrReviewPeriodUrlBuilder) Build() *OkrReviewPeriodUrl {
	req := &OkrReviewPeriodUrl{}
	if builder.urlFlag {
		req.Url = &builder.url

	}
	if builder.createTimeFlag {
		req.CreateTime = &builder.createTime

	}
	return req
}

type OkrSimple struct {
	Name       *OkrName `json:"name,omitempty"`        // 周期展示名称
	OkrId      *string  `json:"okr_id,omitempty"`      // OKR ID
	PeriodId   *string  `json:"period_id,omitempty"`   // 周期 ID
	PeriodType *string  `json:"period_type,omitempty"` // 周期类型：1、月周期；2、年周期
}

type OkrSimpleBuilder struct {
	name           *OkrName // 周期展示名称
	nameFlag       bool
	okrId          string // OKR ID
	okrIdFlag      bool
	periodId       string // 周期 ID
	periodIdFlag   bool
	periodType     string // 周期类型：1、月周期；2、年周期
	periodTypeFlag bool
}

func NewOkrSimpleBuilder() *OkrSimpleBuilder {
	builder := &OkrSimpleBuilder{}
	return builder
}

// 周期展示名称
//
// 示例值：
func (builder *OkrSimpleBuilder) Name(name *OkrName) *OkrSimpleBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

// OKR ID
//
// 示例值：
func (builder *OkrSimpleBuilder) OkrId(okrId string) *OkrSimpleBuilder {
	builder.okrId = okrId
	builder.okrIdFlag = true
	return builder
}

// 周期 ID
//
// 示例值：
func (builder *OkrSimpleBuilder) PeriodId(periodId string) *OkrSimpleBuilder {
	builder.periodId = periodId
	builder.periodIdFlag = true
	return builder
}

// 周期类型：1、月周期；2、年周期
//
// 示例值：
func (builder *OkrSimpleBuilder) PeriodType(periodType string) *OkrSimpleBuilder {
	builder.periodType = periodType
	builder.periodTypeFlag = true
	return builder
}

func (builder *OkrSimpleBuilder) Build() *OkrSimple {
	req := &OkrSimple{}
	if builder.nameFlag {
		req.Name = builder.name
	}
	if builder.okrIdFlag {
		req.OkrId = &builder.okrId

	}
	if builder.periodIdFlag {
		req.PeriodId = &builder.periodId

	}
	if builder.periodTypeFlag {
		req.PeriodType = &builder.periodType

	}
	return req
}

type Period struct {
	Id     *string `json:"id,omitempty"`      // id
	ZhName *string `json:"zh_name,omitempty"` // 中文名称
	EnName *string `json:"en_name,omitempty"` // 英文名称
	Status *int    `json:"status,omitempty"`  // 启用状态
}

type PeriodBuilder struct {
	id         string // id
	idFlag     bool
	zhName     string // 中文名称
	zhNameFlag bool
	enName     string // 英文名称
	enNameFlag bool
	status     int // 启用状态
	statusFlag bool
}

func NewPeriodBuilder() *PeriodBuilder {
	builder := &PeriodBuilder{}
	return builder
}

// id
//
// 示例值：635782378412311
func (builder *PeriodBuilder) Id(id string) *PeriodBuilder {
	builder.id = id
	builder.idFlag = true
	return builder
}

// 中文名称
//
// 示例值：中文周期
func (builder *PeriodBuilder) ZhName(zhName string) *PeriodBuilder {
	builder.zhName = zhName
	builder.zhNameFlag = true
	return builder
}

// 英文名称
//
// 示例值：english period
func (builder *PeriodBuilder) EnName(enName string) *PeriodBuilder {
	builder.enName = enName
	builder.enNameFlag = true
	return builder
}

// 启用状态
//
// 示例值：0
func (builder *PeriodBuilder) Status(status int) *PeriodBuilder {
	builder.status = status
	builder.statusFlag = true
	return builder
}

func (builder *PeriodBuilder) Build() *Period {
	req := &Period{}
	if builder.idFlag {
		req.Id = &builder.id

	}
	if builder.zhNameFlag {
		req.ZhName = &builder.zhName

	}
	if builder.enNameFlag {
		req.EnName = &builder.enName

	}
	if builder.statusFlag {
		req.Status = &builder.status

	}
	return req
}

type ProgressItem struct {
	Content     *string  `json:"content,omitempty"`      // 进度内容
	Timestamp   *string  `json:"timestamp,omitempty"`    // 更新时间
	MentionList []string `json:"mention_list,omitempty"` // 负责人
}

type ProgressItemBuilder struct {
	content         string // 进度内容
	contentFlag     bool
	timestamp       string // 更新时间
	timestampFlag   bool
	mentionList     []string // 负责人
	mentionListFlag bool
}

func NewProgressItemBuilder() *ProgressItemBuilder {
	builder := &ProgressItemBuilder{}
	return builder
}

// 进度内容
//
// 示例值：
func (builder *ProgressItemBuilder) Content(content string) *ProgressItemBuilder {
	builder.content = content
	builder.contentFlag = true
	return builder
}

// 更新时间
//
// 示例值：
func (builder *ProgressItemBuilder) Timestamp(timestamp string) *ProgressItemBuilder {
	builder.timestamp = timestamp
	builder.timestampFlag = true
	return builder
}

// 负责人
//
// 示例值：
func (builder *ProgressItemBuilder) MentionList(mentionList []string) *ProgressItemBuilder {
	builder.mentionList = mentionList
	builder.mentionListFlag = true
	return builder
}

func (builder *ProgressItemBuilder) Build() *ProgressItem {
	req := &ProgressItem{}
	if builder.contentFlag {
		req.Content = &builder.content

	}
	if builder.timestampFlag {
		req.Timestamp = &builder.timestamp

	}
	if builder.mentionListFlag {
		req.MentionList = builder.mentionList
	}
	return req
}

type ProgressRate struct {
	Percent *int `json:"percent,omitempty"` // 进度百分比
	Status  *int `json:"status,omitempty"`  // 状态
}

type ProgressRateBuilder struct {
	percent     int // 进度百分比
	percentFlag bool
	status      int // 状态
	statusFlag  bool
}

func NewProgressRateBuilder() *ProgressRateBuilder {
	builder := &ProgressRateBuilder{}
	return builder
}

// 进度百分比
//
// 示例值：
func (builder *ProgressRateBuilder) Percent(percent int) *ProgressRateBuilder {
	builder.percent = percent
	builder.percentFlag = true
	return builder
}

// 状态
//
// 示例值：
func (builder *ProgressRateBuilder) Status(status int) *ProgressRateBuilder {
	builder.status = status
	builder.statusFlag = true
	return builder
}

func (builder *ProgressRateBuilder) Build() *ProgressRate {
	req := &ProgressRate{}
	if builder.percentFlag {
		req.Percent = &builder.percent

	}
	if builder.statusFlag {
		req.Status = &builder.status

	}
	return req
}

type ProgressRecord struct {
	ProgressId *string       `json:"progress_id,omitempty"` // OKR 进展ID
	ModifyTime *string       `json:"modify_time,omitempty"` // 进展更新时间 毫秒
	Content    *ContentBlock `json:"content,omitempty"`     // 进展 对应的 Content 详细内容
}

type ProgressRecordBuilder struct {
	progressId     string // OKR 进展ID
	progressIdFlag bool
	modifyTime     string // 进展更新时间 毫秒
	modifyTimeFlag bool
	content        *ContentBlock // 进展 对应的 Content 详细内容
	contentFlag    bool
}

func NewProgressRecordBuilder() *ProgressRecordBuilder {
	builder := &ProgressRecordBuilder{}
	return builder
}

// OKR 进展ID
//
// 示例值：7041469619902693396
func (builder *ProgressRecordBuilder) ProgressId(progressId string) *ProgressRecordBuilder {
	builder.progressId = progressId
	builder.progressIdFlag = true
	return builder
}

// 进展更新时间 毫秒
//
// 示例值：1618500278663
func (builder *ProgressRecordBuilder) ModifyTime(modifyTime string) *ProgressRecordBuilder {
	builder.modifyTime = modifyTime
	builder.modifyTimeFlag = true
	return builder
}

// 进展 对应的 Content 详细内容
//
// 示例值：
func (builder *ProgressRecordBuilder) Content(content *ContentBlock) *ProgressRecordBuilder {
	builder.content = content
	builder.contentFlag = true
	return builder
}

func (builder *ProgressRecordBuilder) Build() *ProgressRecord {
	req := &ProgressRecord{}
	if builder.progressIdFlag {
		req.ProgressId = &builder.progressId

	}
	if builder.modifyTimeFlag {
		req.ModifyTime = &builder.modifyTime

	}
	if builder.contentFlag {
		req.Content = builder.content
	}
	return req
}

type ProgressRecordSimplify struct {
	Id *string `json:"id,omitempty"` // OKR 进展记录ID
}

type ProgressRecordSimplifyBuilder struct {
	id     string // OKR 进展记录ID
	idFlag bool
}

func NewProgressRecordSimplifyBuilder() *ProgressRecordSimplifyBuilder {
	builder := &ProgressRecordSimplifyBuilder{}
	return builder
}

// OKR 进展记录ID
//
// 示例值：7041469619902693396
func (builder *ProgressRecordSimplifyBuilder) Id(id string) *ProgressRecordSimplifyBuilder {
	builder.id = id
	builder.idFlag = true
	return builder
}

func (builder *ProgressRecordSimplifyBuilder) Build() *ProgressRecordSimplify {
	req := &ProgressRecordSimplify{}
	if builder.idFlag {
		req.Id = &builder.id

	}
	return req
}

type UpsertObjective struct {
	Content      *string              `json:"content,omitempty"`       // OKR内容
	MentionList  []string             `json:"mention_list,omitempty"`  // 负责人列表
	KrList       []*UpsertObjectiveKr `json:"kr_list,omitempty"`       // KR列表
	ProgressRate *ProgressRate        `json:"progress_rate,omitempty"` // Objective进度状态
	ProgressList []*ProgressItem      `json:"progress_list,omitempty"` // Objective进度列表
	Weight       *float64             `json:"weight,omitempty"`        // OKR权重
}

type UpsertObjectiveBuilder struct {
	content          string // OKR内容
	contentFlag      bool
	mentionList      []string // 负责人列表
	mentionListFlag  bool
	krList           []*UpsertObjectiveKr // KR列表
	krListFlag       bool
	progressRate     *ProgressRate // Objective进度状态
	progressRateFlag bool
	progressList     []*ProgressItem // Objective进度列表
	progressListFlag bool
	weight           float64 // OKR权重
	weightFlag       bool
}

func NewUpsertObjectiveBuilder() *UpsertObjectiveBuilder {
	builder := &UpsertObjectiveBuilder{}
	return builder
}

// OKR内容
//
// 示例值：okr
func (builder *UpsertObjectiveBuilder) Content(content string) *UpsertObjectiveBuilder {
	builder.content = content
	builder.contentFlag = true
	return builder
}

// 负责人列表
//
// 示例值：
func (builder *UpsertObjectiveBuilder) MentionList(mentionList []string) *UpsertObjectiveBuilder {
	builder.mentionList = mentionList
	builder.mentionListFlag = true
	return builder
}

// KR列表
//
// 示例值：
func (builder *UpsertObjectiveBuilder) KrList(krList []*UpsertObjectiveKr) *UpsertObjectiveBuilder {
	builder.krList = krList
	builder.krListFlag = true
	return builder
}

// Objective进度状态
//
// 示例值：
func (builder *UpsertObjectiveBuilder) ProgressRate(progressRate *ProgressRate) *UpsertObjectiveBuilder {
	builder.progressRate = progressRate
	builder.progressRateFlag = true
	return builder
}

// Objective进度列表
//
// 示例值：
func (builder *UpsertObjectiveBuilder) ProgressList(progressList []*ProgressItem) *UpsertObjectiveBuilder {
	builder.progressList = progressList
	builder.progressListFlag = true
	return builder
}

// OKR权重
//
// 示例值：100
func (builder *UpsertObjectiveBuilder) Weight(weight float64) *UpsertObjectiveBuilder {
	builder.weight = weight
	builder.weightFlag = true
	return builder
}

func (builder *UpsertObjectiveBuilder) Build() *UpsertObjective {
	req := &UpsertObjective{}
	if builder.contentFlag {
		req.Content = &builder.content

	}
	if builder.mentionListFlag {
		req.MentionList = builder.mentionList
	}
	if builder.krListFlag {
		req.KrList = builder.krList
	}
	if builder.progressRateFlag {
		req.ProgressRate = builder.progressRate
	}
	if builder.progressListFlag {
		req.ProgressList = builder.progressList
	}
	if builder.weightFlag {
		req.Weight = &builder.weight

	}
	return req
}

type UpsertObjectiveKr struct {
	Content      *string         `json:"content,omitempty"`       // OKR内容
	MentionList  []string        `json:"mention_list,omitempty"`  // 负责人列表
	Score        *int            `json:"score,omitempty"`         // 得分
	ProgressRate *ProgressRate   `json:"progress_rate,omitempty"` // Objective进度状态
	ProgressList []*ProgressItem `json:"progress_list,omitempty"` // Objective进度列表
	Weight       *float64        `json:"weight,omitempty"`        // OKR权重
}

type UpsertObjectiveKrBuilder struct {
	content          string // OKR内容
	contentFlag      bool
	mentionList      []string // 负责人列表
	mentionListFlag  bool
	score            int // 得分
	scoreFlag        bool
	progressRate     *ProgressRate // Objective进度状态
	progressRateFlag bool
	progressList     []*ProgressItem // Objective进度列表
	progressListFlag bool
	weight           float64 // OKR权重
	weightFlag       bool
}

func NewUpsertObjectiveKrBuilder() *UpsertObjectiveKrBuilder {
	builder := &UpsertObjectiveKrBuilder{}
	return builder
}

// OKR内容
//
// 示例值：okr
func (builder *UpsertObjectiveKrBuilder) Content(content string) *UpsertObjectiveKrBuilder {
	builder.content = content
	builder.contentFlag = true
	return builder
}

// 负责人列表
//
// 示例值：
func (builder *UpsertObjectiveKrBuilder) MentionList(mentionList []string) *UpsertObjectiveKrBuilder {
	builder.mentionList = mentionList
	builder.mentionListFlag = true
	return builder
}

// 得分
//
// 示例值：100
func (builder *UpsertObjectiveKrBuilder) Score(score int) *UpsertObjectiveKrBuilder {
	builder.score = score
	builder.scoreFlag = true
	return builder
}

// Objective进度状态
//
// 示例值：
func (builder *UpsertObjectiveKrBuilder) ProgressRate(progressRate *ProgressRate) *UpsertObjectiveKrBuilder {
	builder.progressRate = progressRate
	builder.progressRateFlag = true
	return builder
}

// Objective进度列表
//
// 示例值：
func (builder *UpsertObjectiveKrBuilder) ProgressList(progressList []*ProgressItem) *UpsertObjectiveKrBuilder {
	builder.progressList = progressList
	builder.progressListFlag = true
	return builder
}

// OKR权重
//
// 示例值：100
func (builder *UpsertObjectiveKrBuilder) Weight(weight float64) *UpsertObjectiveKrBuilder {
	builder.weight = weight
	builder.weightFlag = true
	return builder
}

func (builder *UpsertObjectiveKrBuilder) Build() *UpsertObjectiveKr {
	req := &UpsertObjectiveKr{}
	if builder.contentFlag {
		req.Content = &builder.content

	}
	if builder.mentionListFlag {
		req.MentionList = builder.mentionList
	}
	if builder.scoreFlag {
		req.Score = &builder.score

	}
	if builder.progressRateFlag {
		req.ProgressRate = builder.progressRate
	}
	if builder.progressListFlag {
		req.ProgressList = builder.progressList
	}
	if builder.weightFlag {
		req.Weight = &builder.weight

	}
	return req
}

type User struct {
	Id *string `json:"id,omitempty"` // id
}

type UserBuilder struct {
	id     string // id
	idFlag bool
}

func NewUserBuilder() *UserBuilder {
	builder := &UserBuilder{}
	return builder
}

// id
//
// 示例值：
func (builder *UserBuilder) Id(id string) *UserBuilder {
	builder.id = id
	builder.idFlag = true
	return builder
}

func (builder *UserBuilder) Build() *User {
	req := &User{}
	if builder.idFlag {
		req.Id = &builder.id

	}
	return req
}

type UserOkr struct {
	Id            *string             `json:"id,omitempty"`             // OKR ID
	Name          *string             `json:"name,omitempty"`           // OKR周期名称(lang指定中英文)
	Permission    *int                `json:"permission,omitempty"`     // 权限，0无权限，1有权限
	ObjectiveList []*UserOkrObjective `json:"objective_list,omitempty"` // OKR的Objective 列表
}

type UserOkrBuilder struct {
	id                string // OKR ID
	idFlag            bool
	name              string // OKR周期名称(lang指定中英文)
	nameFlag          bool
	permission        int // 权限，0无权限，1有权限
	permissionFlag    bool
	objectiveList     []*UserOkrObjective // OKR的Objective 列表
	objectiveListFlag bool
}

func NewUserOkrBuilder() *UserOkrBuilder {
	builder := &UserOkrBuilder{}
	return builder
}

// OKR ID
//
// 示例值：
func (builder *UserOkrBuilder) Id(id string) *UserOkrBuilder {
	builder.id = id
	builder.idFlag = true
	return builder
}

// OKR周期名称(lang指定中英文)
//
// 示例值：
func (builder *UserOkrBuilder) Name(name string) *UserOkrBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

// 权限，0无权限，1有权限
//
// 示例值：
func (builder *UserOkrBuilder) Permission(permission int) *UserOkrBuilder {
	builder.permission = permission
	builder.permissionFlag = true
	return builder
}

// OKR的Objective 列表
//
// 示例值：
func (builder *UserOkrBuilder) ObjectiveList(objectiveList []*UserOkrObjective) *UserOkrBuilder {
	builder.objectiveList = objectiveList
	builder.objectiveListFlag = true
	return builder
}

func (builder *UserOkrBuilder) Build() *UserOkr {
	req := &UserOkr{}
	if builder.idFlag {
		req.Id = &builder.id

	}
	if builder.nameFlag {
		req.Name = &builder.name

	}
	if builder.permissionFlag {
		req.Permission = &builder.permission

	}
	if builder.objectiveListFlag {
		req.ObjectiveList = builder.objectiveList
	}
	return req
}

type UserOkrObjective struct {
	Id                    *string                             `json:"id,omitempty"`                      // Objective ID
	Permission            *int                                `json:"permission,omitempty"`              // 权限，0无权限，1有权限
	Content               *string                             `json:"content,omitempty"`                 // Objective 内容
	ProgressReport        *string                             `json:"progress_report,omitempty"`         // Objective 进度记录内容
	Score                 *string                             `json:"score,omitempty"`                   // Objective 分数（0 - 100）
	ProgressRate          *UserOkrObjectiveProgressRate       `json:"progress_rate,omitempty"`           // Objective进度
	KrList                []*UserOkrObjectiveKr               `json:"kr_list,omitempty"`                 // Objective KeyResult 列表
	AlignedObjectiveList  []*UserOkrObjectiveAlignedObjective `json:"aligned_objective_list,omitempty"`  // 对齐到该Objective的Objective列表
	AligningObjectiveList []*UserOkrObjectiveAlignedObjective `json:"aligning_objective_list,omitempty"` // 该Objective对齐到的Objective列表
}

type UserOkrObjectiveBuilder struct {
	id                        string // Objective ID
	idFlag                    bool
	permission                int // 权限，0无权限，1有权限
	permissionFlag            bool
	content                   string // Objective 内容
	contentFlag               bool
	progressReport            string // Objective 进度记录内容
	progressReportFlag        bool
	score                     string // Objective 分数（0 - 100）
	scoreFlag                 bool
	progressRate              *UserOkrObjectiveProgressRate // Objective进度
	progressRateFlag          bool
	krList                    []*UserOkrObjectiveKr // Objective KeyResult 列表
	krListFlag                bool
	alignedObjectiveList      []*UserOkrObjectiveAlignedObjective // 对齐到该Objective的Objective列表
	alignedObjectiveListFlag  bool
	aligningObjectiveList     []*UserOkrObjectiveAlignedObjective // 该Objective对齐到的Objective列表
	aligningObjectiveListFlag bool
}

func NewUserOkrObjectiveBuilder() *UserOkrObjectiveBuilder {
	builder := &UserOkrObjectiveBuilder{}
	return builder
}

// Objective ID
//
// 示例值：
func (builder *UserOkrObjectiveBuilder) Id(id string) *UserOkrObjectiveBuilder {
	builder.id = id
	builder.idFlag = true
	return builder
}

// 权限，0无权限，1有权限
//
// 示例值：
func (builder *UserOkrObjectiveBuilder) Permission(permission int) *UserOkrObjectiveBuilder {
	builder.permission = permission
	builder.permissionFlag = true
	return builder
}

// Objective 内容
//
// 示例值：
func (builder *UserOkrObjectiveBuilder) Content(content string) *UserOkrObjectiveBuilder {
	builder.content = content
	builder.contentFlag = true
	return builder
}

// Objective 进度记录内容
//
// 示例值：
func (builder *UserOkrObjectiveBuilder) ProgressReport(progressReport string) *UserOkrObjectiveBuilder {
	builder.progressReport = progressReport
	builder.progressReportFlag = true
	return builder
}

// Objective 分数（0 - 100）
//
// 示例值：
func (builder *UserOkrObjectiveBuilder) Score(score string) *UserOkrObjectiveBuilder {
	builder.score = score
	builder.scoreFlag = true
	return builder
}

// Objective进度
//
// 示例值：
func (builder *UserOkrObjectiveBuilder) ProgressRate(progressRate *UserOkrObjectiveProgressRate) *UserOkrObjectiveBuilder {
	builder.progressRate = progressRate
	builder.progressRateFlag = true
	return builder
}

// Objective KeyResult 列表
//
// 示例值：
func (builder *UserOkrObjectiveBuilder) KrList(krList []*UserOkrObjectiveKr) *UserOkrObjectiveBuilder {
	builder.krList = krList
	builder.krListFlag = true
	return builder
}

// 对齐到该Objective的Objective列表
//
// 示例值：
func (builder *UserOkrObjectiveBuilder) AlignedObjectiveList(alignedObjectiveList []*UserOkrObjectiveAlignedObjective) *UserOkrObjectiveBuilder {
	builder.alignedObjectiveList = alignedObjectiveList
	builder.alignedObjectiveListFlag = true
	return builder
}

// 该Objective对齐到的Objective列表
//
// 示例值：
func (builder *UserOkrObjectiveBuilder) AligningObjectiveList(aligningObjectiveList []*UserOkrObjectiveAlignedObjective) *UserOkrObjectiveBuilder {
	builder.aligningObjectiveList = aligningObjectiveList
	builder.aligningObjectiveListFlag = true
	return builder
}

func (builder *UserOkrObjectiveBuilder) Build() *UserOkrObjective {
	req := &UserOkrObjective{}
	if builder.idFlag {
		req.Id = &builder.id

	}
	if builder.permissionFlag {
		req.Permission = &builder.permission

	}
	if builder.contentFlag {
		req.Content = &builder.content

	}
	if builder.progressReportFlag {
		req.ProgressReport = &builder.progressReport

	}
	if builder.scoreFlag {
		req.Score = &builder.score

	}
	if builder.progressRateFlag {
		req.ProgressRate = builder.progressRate
	}
	if builder.krListFlag {
		req.KrList = builder.krList
	}
	if builder.alignedObjectiveListFlag {
		req.AlignedObjectiveList = builder.alignedObjectiveList
	}
	if builder.aligningObjectiveListFlag {
		req.AligningObjectiveList = builder.aligningObjectiveList
	}
	return req
}

type UserOkrObjectiveAlignedObjective struct {
	Id    *string                                `json:"id,omitempty"`     // Objective的ID
	OkrId *string                                `json:"okr_id,omitempty"` // OKR的ID
	Owner *UserOkrObjectiveAlignedObjectiveOwner `json:"owner,omitempty"`  // 该Objective的Owner
}

type UserOkrObjectiveAlignedObjectiveBuilder struct {
	id        string // Objective的ID
	idFlag    bool
	okrId     string // OKR的ID
	okrIdFlag bool
	owner     *UserOkrObjectiveAlignedObjectiveOwner // 该Objective的Owner
	ownerFlag bool
}

func NewUserOkrObjectiveAlignedObjectiveBuilder() *UserOkrObjectiveAlignedObjectiveBuilder {
	builder := &UserOkrObjectiveAlignedObjectiveBuilder{}
	return builder
}

// Objective的ID
//
// 示例值：
func (builder *UserOkrObjectiveAlignedObjectiveBuilder) Id(id string) *UserOkrObjectiveAlignedObjectiveBuilder {
	builder.id = id
	builder.idFlag = true
	return builder
}

// OKR的ID
//
// 示例值：
func (builder *UserOkrObjectiveAlignedObjectiveBuilder) OkrId(okrId string) *UserOkrObjectiveAlignedObjectiveBuilder {
	builder.okrId = okrId
	builder.okrIdFlag = true
	return builder
}

// 该Objective的Owner
//
// 示例值：
func (builder *UserOkrObjectiveAlignedObjectiveBuilder) Owner(owner *UserOkrObjectiveAlignedObjectiveOwner) *UserOkrObjectiveAlignedObjectiveBuilder {
	builder.owner = owner
	builder.ownerFlag = true
	return builder
}

func (builder *UserOkrObjectiveAlignedObjectiveBuilder) Build() *UserOkrObjectiveAlignedObjective {
	req := &UserOkrObjectiveAlignedObjective{}
	if builder.idFlag {
		req.Id = &builder.id

	}
	if builder.okrIdFlag {
		req.OkrId = &builder.okrId

	}
	if builder.ownerFlag {
		req.Owner = builder.owner
	}
	return req
}

type UserOkrObjectiveAlignedObjectiveOwner struct {
	OpenId     *string `json:"open_id,omitempty"`     // 用户的 open_id
	EmployeeId *string `json:"employee_id,omitempty"` // 用户的 employee_id
	EmployeeNo *string `json:"employee_no,omitempty"` // 工号
	UnionId    *string `json:"union_id,omitempty"`    // 用户的 union_id
	Name       *string `json:"name,omitempty"`        // 用户名
}

type UserOkrObjectiveAlignedObjectiveOwnerBuilder struct {
	openId         string // 用户的 open_id
	openIdFlag     bool
	employeeId     string // 用户的 employee_id
	employeeIdFlag bool
	employeeNo     string // 工号
	employeeNoFlag bool
	unionId        string // 用户的 union_id
	unionIdFlag    bool
	name           string // 用户名
	nameFlag       bool
}

func NewUserOkrObjectiveAlignedObjectiveOwnerBuilder() *UserOkrObjectiveAlignedObjectiveOwnerBuilder {
	builder := &UserOkrObjectiveAlignedObjectiveOwnerBuilder{}
	return builder
}

// 用户的 open_id
//
// 示例值：
func (builder *UserOkrObjectiveAlignedObjectiveOwnerBuilder) OpenId(openId string) *UserOkrObjectiveAlignedObjectiveOwnerBuilder {
	builder.openId = openId
	builder.openIdFlag = true
	return builder
}

// 用户的 employee_id
//
// 示例值：
func (builder *UserOkrObjectiveAlignedObjectiveOwnerBuilder) EmployeeId(employeeId string) *UserOkrObjectiveAlignedObjectiveOwnerBuilder {
	builder.employeeId = employeeId
	builder.employeeIdFlag = true
	return builder
}

// 工号
//
// 示例值：
func (builder *UserOkrObjectiveAlignedObjectiveOwnerBuilder) EmployeeNo(employeeNo string) *UserOkrObjectiveAlignedObjectiveOwnerBuilder {
	builder.employeeNo = employeeNo
	builder.employeeNoFlag = true
	return builder
}

// 用户的 union_id
//
// 示例值：
func (builder *UserOkrObjectiveAlignedObjectiveOwnerBuilder) UnionId(unionId string) *UserOkrObjectiveAlignedObjectiveOwnerBuilder {
	builder.unionId = unionId
	builder.unionIdFlag = true
	return builder
}

// 用户名
//
// 示例值：
func (builder *UserOkrObjectiveAlignedObjectiveOwnerBuilder) Name(name string) *UserOkrObjectiveAlignedObjectiveOwnerBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

func (builder *UserOkrObjectiveAlignedObjectiveOwnerBuilder) Build() *UserOkrObjectiveAlignedObjectiveOwner {
	req := &UserOkrObjectiveAlignedObjectiveOwner{}
	if builder.openIdFlag {
		req.OpenId = &builder.openId

	}
	if builder.employeeIdFlag {
		req.EmployeeId = &builder.employeeId

	}
	if builder.employeeNoFlag {
		req.EmployeeNo = &builder.employeeNo

	}
	if builder.unionIdFlag {
		req.UnionId = &builder.unionId

	}
	if builder.nameFlag {
		req.Name = &builder.name

	}
	return req
}

type UserOkrObjectiveKr struct {
	Id           *string                       `json:"id,omitempty"`            // Key Result ID
	Content      *string                       `json:"content,omitempty"`       // KeyResult 内容
	Score        *int                          `json:"score,omitempty"`         // KeyResult打分（0 - 100）
	Weight       *int                          `json:"weight,omitempty"`        // KeyResult权重（0 - 100）
	ProgressRate *UserOkrObjectiveProgressRate `json:"progress_rate,omitempty"` // Objective进度
}

type UserOkrObjectiveKrBuilder struct {
	id               string // Key Result ID
	idFlag           bool
	content          string // KeyResult 内容
	contentFlag      bool
	score            int // KeyResult打分（0 - 100）
	scoreFlag        bool
	weight           int // KeyResult权重（0 - 100）
	weightFlag       bool
	progressRate     *UserOkrObjectiveProgressRate // Objective进度
	progressRateFlag bool
}

func NewUserOkrObjectiveKrBuilder() *UserOkrObjectiveKrBuilder {
	builder := &UserOkrObjectiveKrBuilder{}
	return builder
}

// Key Result ID
//
// 示例值：
func (builder *UserOkrObjectiveKrBuilder) Id(id string) *UserOkrObjectiveKrBuilder {
	builder.id = id
	builder.idFlag = true
	return builder
}

// KeyResult 内容
//
// 示例值：
func (builder *UserOkrObjectiveKrBuilder) Content(content string) *UserOkrObjectiveKrBuilder {
	builder.content = content
	builder.contentFlag = true
	return builder
}

// KeyResult打分（0 - 100）
//
// 示例值：
func (builder *UserOkrObjectiveKrBuilder) Score(score int) *UserOkrObjectiveKrBuilder {
	builder.score = score
	builder.scoreFlag = true
	return builder
}

// KeyResult权重（0 - 100）
//
// 示例值：
func (builder *UserOkrObjectiveKrBuilder) Weight(weight int) *UserOkrObjectiveKrBuilder {
	builder.weight = weight
	builder.weightFlag = true
	return builder
}

// Objective进度
//
// 示例值：
func (builder *UserOkrObjectiveKrBuilder) ProgressRate(progressRate *UserOkrObjectiveProgressRate) *UserOkrObjectiveKrBuilder {
	builder.progressRate = progressRate
	builder.progressRateFlag = true
	return builder
}

func (builder *UserOkrObjectiveKrBuilder) Build() *UserOkrObjectiveKr {
	req := &UserOkrObjectiveKr{}
	if builder.idFlag {
		req.Id = &builder.id

	}
	if builder.contentFlag {
		req.Content = &builder.content

	}
	if builder.scoreFlag {
		req.Score = &builder.score

	}
	if builder.weightFlag {
		req.Weight = &builder.weight

	}
	if builder.progressRateFlag {
		req.ProgressRate = builder.progressRate
	}
	return req
}

type UserOkrObjectiveProgressRate struct {
	Percent *int    `json:"percent,omitempty"` // Objective 进度百分比 >= 0
	Status  *string `json:"status,omitempty"`  // Objective 进度状态,undefined 未更新,normal 正常,risky 有风险,overdue 已延期
}

type UserOkrObjectiveProgressRateBuilder struct {
	percent     int // Objective 进度百分比 >= 0
	percentFlag bool
	status      string // Objective 进度状态,undefined 未更新,normal 正常,risky 有风险,overdue 已延期
	statusFlag  bool
}

func NewUserOkrObjectiveProgressRateBuilder() *UserOkrObjectiveProgressRateBuilder {
	builder := &UserOkrObjectiveProgressRateBuilder{}
	return builder
}

// Objective 进度百分比 >= 0
//
// 示例值：
func (builder *UserOkrObjectiveProgressRateBuilder) Percent(percent int) *UserOkrObjectiveProgressRateBuilder {
	builder.percent = percent
	builder.percentFlag = true
	return builder
}

// Objective 进度状态,undefined 未更新,normal 正常,risky 有风险,overdue 已延期
//
// 示例值：
func (builder *UserOkrObjectiveProgressRateBuilder) Status(status string) *UserOkrObjectiveProgressRateBuilder {
	builder.status = status
	builder.statusFlag = true
	return builder
}

func (builder *UserOkrObjectiveProgressRateBuilder) Build() *UserOkrObjectiveProgressRate {
	req := &UserOkrObjectiveProgressRate{}
	if builder.percentFlag {
		req.Percent = &builder.percent

	}
	if builder.statusFlag {
		req.Status = &builder.status

	}
	return req
}

type UploadImageReqBodyBuilder struct {
	data           io.Reader // 图片
	dataFlag       bool
	targetId       string // 图片的目标ID
	targetIdFlag   bool
	targetType     int // 图片使用的目标类型
	targetTypeFlag bool
}

func NewUploadImageReqBodyBuilder() *UploadImageReqBodyBuilder {
	builder := &UploadImageReqBodyBuilder{}
	return builder
}

// 图片
//
//示例值：file binary
func (builder *UploadImageReqBodyBuilder) Data(data io.Reader) *UploadImageReqBodyBuilder {
	builder.data = data
	builder.dataFlag = true
	return builder
}

// 图片的目标ID
//
//示例值：6974586812998174252
func (builder *UploadImageReqBodyBuilder) TargetId(targetId string) *UploadImageReqBodyBuilder {
	builder.targetId = targetId
	builder.targetIdFlag = true
	return builder
}

// 图片使用的目标类型
//
//示例值：1
func (builder *UploadImageReqBodyBuilder) TargetType(targetType int) *UploadImageReqBodyBuilder {
	builder.targetType = targetType
	builder.targetTypeFlag = true
	return builder
}

func (builder *UploadImageReqBodyBuilder) Build() *UploadImageReqBody {
	req := &UploadImageReqBody{}
	if builder.dataFlag {
		req.Data = builder.data
	}
	if builder.targetIdFlag {
		req.TargetId = &builder.targetId
	}
	if builder.targetTypeFlag {
		req.TargetType = &builder.targetType
	}
	return req
}

type UploadImagePathReqBodyBuilder struct {
	dataPath       string // 图片
	dataPathFlag   bool
	targetId       string // 图片的目标ID
	targetIdFlag   bool
	targetType     int // 图片使用的目标类型
	targetTypeFlag bool
}

func NewUploadImagePathReqBodyBuilder() *UploadImagePathReqBodyBuilder {
	builder := &UploadImagePathReqBodyBuilder{}
	return builder
}

// 图片
//
// 示例值：file binary
func (builder *UploadImagePathReqBodyBuilder) DataPath(dataPath string) *UploadImagePathReqBodyBuilder {
	builder.dataPath = dataPath
	builder.dataPathFlag = true
	return builder
}

// 图片的目标ID
//
// 示例值：6974586812998174252
func (builder *UploadImagePathReqBodyBuilder) TargetId(targetId string) *UploadImagePathReqBodyBuilder {
	builder.targetId = targetId
	builder.targetIdFlag = true
	return builder
}

// 图片使用的目标类型
//
// 示例值：1
func (builder *UploadImagePathReqBodyBuilder) TargetType(targetType int) *UploadImagePathReqBodyBuilder {
	builder.targetType = targetType
	builder.targetTypeFlag = true
	return builder
}

func (builder *UploadImagePathReqBodyBuilder) Build() (*UploadImageReqBody, error) {
	req := &UploadImageReqBody{}
	if builder.dataPathFlag {
		data, err := larkcore.File2Bytes(builder.dataPath)
		if err != nil {
			return nil, err
		}
		req.Data = bytes.NewBuffer(data)
	}
	if builder.targetIdFlag {
		req.TargetId = &builder.targetId
	}
	if builder.targetTypeFlag {
		req.TargetType = &builder.targetType
	}
	return req, nil
}

type UploadImageReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *UploadImageReqBody
}

func NewUploadImageReqBuilder() *UploadImageReqBuilder {
	builder := &UploadImageReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 上传图片
func (builder *UploadImageReqBuilder) Body(body *UploadImageReqBody) *UploadImageReqBuilder {
	builder.body = body
	return builder
}

func (builder *UploadImageReqBuilder) Build() *UploadImageReq {
	req := &UploadImageReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.Body = builder.body
	return req
}

type UploadImageReqBody struct {
	Data       io.Reader `json:"data,omitempty"`        // 图片
	TargetId   *string   `json:"target_id,omitempty"`   // 图片的目标ID
	TargetType *int      `json:"target_type,omitempty"` // 图片使用的目标类型
}

type UploadImageReq struct {
	apiReq *larkcore.ApiReq
	Body   *UploadImageReqBody `body:""`
}

type UploadImageRespData struct {
	FileToken *string `json:"file_token,omitempty"` // 图片token
	Url       *string `json:"url,omitempty"`        // 图片下载链接
}

type UploadImageResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *UploadImageRespData `json:"data"` // 业务数据
}

func (resp *UploadImageResp) Success() bool {
	return resp.Code == 0
}

type ListMetricSourceReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewListMetricSourceReqBuilder() *ListMetricSourceReqBuilder {
	builder := &ListMetricSourceReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 页码标识，获取第一页传空，每次查询会返回下一页的page_token
//
// 示例值：6969864184272078374
func (builder *ListMetricSourceReqBuilder) PageToken(pageToken string) *ListMetricSourceReqBuilder {
	builder.apiReq.QueryParams.Set("page_token", fmt.Sprint(pageToken))
	return builder
}

// 每页获取记录数
//
// 示例值：10
func (builder *ListMetricSourceReqBuilder) PageSize(pageSize string) *ListMetricSourceReqBuilder {
	builder.apiReq.QueryParams.Set("page_size", fmt.Sprint(pageSize))
	return builder
}

func (builder *ListMetricSourceReqBuilder) Build() *ListMetricSourceReq {
	req := &ListMetricSourceReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type ListMetricSourceReq struct {
	apiReq *larkcore.ApiReq
}

type ListMetricSourceRespData struct {
	Total     *int            `json:"total,omitempty"`      // 符合条件的记录总数
	HasMore   *bool           `json:"has_more,omitempty"`   // 是否有下一页
	PageToken *string         `json:"page_token,omitempty"` // 下一页页码
	Items     []*MetricSource `json:"items,omitempty"`      // 指标库列表
}

type ListMetricSourceResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *ListMetricSourceRespData `json:"data"` // 业务数据
}

func (resp *ListMetricSourceResp) Success() bool {
	return resp.Code == 0
}

type ListMetricSourceTableReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewListMetricSourceTableReqBuilder() *ListMetricSourceTableReqBuilder {
	builder := &ListMetricSourceTableReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// okr指标库id
//
// 示例值：7041857032248410131
func (builder *ListMetricSourceTableReqBuilder) MetricSourceId(metricSourceId string) *ListMetricSourceTableReqBuilder {
	builder.apiReq.PathParams.Set("metric_source_id", fmt.Sprint(metricSourceId))
	return builder
}

// 页码标识，获取第一页传空，每次查询会返回下一页的page_token
//
// 示例值：6969864184272078374
func (builder *ListMetricSourceTableReqBuilder) PageToken(pageToken string) *ListMetricSourceTableReqBuilder {
	builder.apiReq.QueryParams.Set("page_token", fmt.Sprint(pageToken))
	return builder
}

// 每页获取记录数
//
// 示例值：10
func (builder *ListMetricSourceTableReqBuilder) PageSize(pageSize string) *ListMetricSourceTableReqBuilder {
	builder.apiReq.QueryParams.Set("page_size", fmt.Sprint(pageSize))
	return builder
}

func (builder *ListMetricSourceTableReqBuilder) Build() *ListMetricSourceTableReq {
	req := &ListMetricSourceTableReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type ListMetricSourceTableReq struct {
	apiReq *larkcore.ApiReq
}

type ListMetricSourceTableRespData struct {
	Total     *int           `json:"total,omitempty"`      // 符合条件的记录总数
	HasMore   *bool          `json:"has_more,omitempty"`   // 是否有下一页
	PageToken *string        `json:"page_token,omitempty"` // 下一页页码
	Items     []*MetricTable `json:"items,omitempty"`      // 指标表列表
}

type ListMetricSourceTableResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *ListMetricSourceTableRespData `json:"data"` // 业务数据
}

func (resp *ListMetricSourceTableResp) Success() bool {
	return resp.Code == 0
}

type BatchUpdateMetricSourceTableItemReqBodyBuilder struct {
	items     []*MetricItemRequest // 指标列表
	itemsFlag bool
}

func NewBatchUpdateMetricSourceTableItemReqBodyBuilder() *BatchUpdateMetricSourceTableItemReqBodyBuilder {
	builder := &BatchUpdateMetricSourceTableItemReqBodyBuilder{}
	return builder
}

// 指标列表
//
//示例值：
func (builder *BatchUpdateMetricSourceTableItemReqBodyBuilder) Items(items []*MetricItemRequest) *BatchUpdateMetricSourceTableItemReqBodyBuilder {
	builder.items = items
	builder.itemsFlag = true
	return builder
}

func (builder *BatchUpdateMetricSourceTableItemReqBodyBuilder) Build() *BatchUpdateMetricSourceTableItemReqBody {
	req := &BatchUpdateMetricSourceTableItemReqBody{}
	if builder.itemsFlag {
		req.Items = builder.items
	}
	return req
}

type BatchUpdateMetricSourceTableItemPathReqBodyBuilder struct {
	items     []*MetricItemRequest // 指标列表
	itemsFlag bool
}

func NewBatchUpdateMetricSourceTableItemPathReqBodyBuilder() *BatchUpdateMetricSourceTableItemPathReqBodyBuilder {
	builder := &BatchUpdateMetricSourceTableItemPathReqBodyBuilder{}
	return builder
}

// 指标列表
//
// 示例值：
func (builder *BatchUpdateMetricSourceTableItemPathReqBodyBuilder) Items(items []*MetricItemRequest) *BatchUpdateMetricSourceTableItemPathReqBodyBuilder {
	builder.items = items
	builder.itemsFlag = true
	return builder
}

func (builder *BatchUpdateMetricSourceTableItemPathReqBodyBuilder) Build() (*BatchUpdateMetricSourceTableItemReqBody, error) {
	req := &BatchUpdateMetricSourceTableItemReqBody{}
	if builder.itemsFlag {
		req.Items = builder.items
	}
	return req, nil
}

type BatchUpdateMetricSourceTableItemReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *BatchUpdateMetricSourceTableItemReqBody
}

func NewBatchUpdateMetricSourceTableItemReqBuilder() *BatchUpdateMetricSourceTableItemReqBuilder {
	builder := &BatchUpdateMetricSourceTableItemReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// okr指标库id
//
// 示例值：7041857032248410131
func (builder *BatchUpdateMetricSourceTableItemReqBuilder) MetricSourceId(metricSourceId string) *BatchUpdateMetricSourceTableItemReqBuilder {
	builder.apiReq.PathParams.Set("metric_source_id", fmt.Sprint(metricSourceId))
	return builder
}

// okr指标表id
//
// 示例值：7041857032248410131
func (builder *BatchUpdateMetricSourceTableItemReqBuilder) MetricTableId(metricTableId string) *BatchUpdateMetricSourceTableItemReqBuilder {
	builder.apiReq.PathParams.Set("metric_table_id", fmt.Sprint(metricTableId))
	return builder
}

// 此次调用中使用的用户ID的类型
//
// 示例值：
func (builder *BatchUpdateMetricSourceTableItemReqBuilder) UserIdType(userIdType string) *BatchUpdateMetricSourceTableItemReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

// - 该接口用于批量更新多项指标，单次调用最多更新 100 条记录。接口仅限 OKR 企业版使用。;;  更新成功后 OKR 系统会给以下人员发送消息通知：;;	- 首次更新目标值的人员 ;;	- 已经将指标添加为 KR、且本次目标值/起始值/支撑的上级有变更的人员，不包含仅更新了进度值的人员
func (builder *BatchUpdateMetricSourceTableItemReqBuilder) Body(body *BatchUpdateMetricSourceTableItemReqBody) *BatchUpdateMetricSourceTableItemReqBuilder {
	builder.body = body
	return builder
}

func (builder *BatchUpdateMetricSourceTableItemReqBuilder) Build() *BatchUpdateMetricSourceTableItemReq {
	req := &BatchUpdateMetricSourceTableItemReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.body
	return req
}

type BatchUpdateMetricSourceTableItemReqBody struct {
	Items []*MetricItemRequest `json:"items,omitempty"` // 指标列表
}

type BatchUpdateMetricSourceTableItemReq struct {
	apiReq *larkcore.ApiReq
	Body   *BatchUpdateMetricSourceTableItemReqBody `body:""`
}

type BatchUpdateMetricSourceTableItemRespData struct {
	Items       []*MetricItem       `json:"items,omitempty"`        // 指标项列表
	FailedItems []*FailedMetricItem `json:"failed_items,omitempty"` // 更新失败列表
}

type BatchUpdateMetricSourceTableItemResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *BatchUpdateMetricSourceTableItemRespData `json:"data"` // 业务数据
}

func (resp *BatchUpdateMetricSourceTableItemResp) Success() bool {
	return resp.Code == 0
}

type GetMetricSourceTableItemReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewGetMetricSourceTableItemReqBuilder() *GetMetricSourceTableItemReqBuilder {
	builder := &GetMetricSourceTableItemReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// okr指标库id
//
// 示例值：7041857032248410131
func (builder *GetMetricSourceTableItemReqBuilder) MetricSourceId(metricSourceId string) *GetMetricSourceTableItemReqBuilder {
	builder.apiReq.PathParams.Set("metric_source_id", fmt.Sprint(metricSourceId))
	return builder
}

// okr指标表id
//
// 示例值：7041857032248410131
func (builder *GetMetricSourceTableItemReqBuilder) MetricTableId(metricTableId string) *GetMetricSourceTableItemReqBuilder {
	builder.apiReq.PathParams.Set("metric_table_id", fmt.Sprint(metricTableId))
	return builder
}

// okr指标项id
//
// 示例值：7041857032248410131
func (builder *GetMetricSourceTableItemReqBuilder) MetricItemId(metricItemId string) *GetMetricSourceTableItemReqBuilder {
	builder.apiReq.PathParams.Set("metric_item_id", fmt.Sprint(metricItemId))
	return builder
}

// 此次调用中使用的用户ID的类型
//
// 示例值：
func (builder *GetMetricSourceTableItemReqBuilder) UserIdType(userIdType string) *GetMetricSourceTableItemReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

func (builder *GetMetricSourceTableItemReqBuilder) Build() *GetMetricSourceTableItemReq {
	req := &GetMetricSourceTableItemReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type GetMetricSourceTableItemReq struct {
	apiReq *larkcore.ApiReq
}

type GetMetricSourceTableItemRespData struct {
	MetricItemId       *string     `json:"metric_item_id,omitempty"`       // 指标表id
	UserId             *string     `json:"user_id,omitempty"`              // 指标承接人员id
	PeriodId           *string     `json:"period_id,omitempty"`            // 指标的okr周期
	MetricUnit         *MetricUnit `json:"metric_unit,omitempty"`          // 指标单位
	MetricInitialValue *float64    `json:"metric_initial_value,omitempty"` // 指标起始值
	MetricTargetValue  *float64    `json:"metric_target_value,omitempty"`  // 指标目标值
	MetricCurrentValue *float64    `json:"metric_current_value,omitempty"` // 指标进度值
	SupportedUserId    *string     `json:"supported_user_id,omitempty"`    // 指标支撑的上级人员id
	KrId               *string     `json:"kr_id,omitempty"`                // 指标关联的kr
	UpdatedAt          *string     `json:"updated_at,omitempty"`           // 更新时间
	UpdatedBy          *string     `json:"updated_by,omitempty"`           // 更新人
}

type GetMetricSourceTableItemResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *GetMetricSourceTableItemRespData `json:"data"` // 业务数据
}

func (resp *GetMetricSourceTableItemResp) Success() bool {
	return resp.Code == 0
}

type ListMetricSourceTableItemReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewListMetricSourceTableItemReqBuilder() *ListMetricSourceTableItemReqBuilder {
	builder := &ListMetricSourceTableItemReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// okr指标库id
//
// 示例值：7041857032248410131
func (builder *ListMetricSourceTableItemReqBuilder) MetricSourceId(metricSourceId string) *ListMetricSourceTableItemReqBuilder {
	builder.apiReq.PathParams.Set("metric_source_id", fmt.Sprint(metricSourceId))
	return builder
}

// okr指标表id
//
// 示例值：7041857032248410131
func (builder *ListMetricSourceTableItemReqBuilder) MetricTableId(metricTableId string) *ListMetricSourceTableItemReqBuilder {
	builder.apiReq.PathParams.Set("metric_table_id", fmt.Sprint(metricTableId))
	return builder
}

// 此次调用中使用的用户ID的类型
//
// 示例值：
func (builder *ListMetricSourceTableItemReqBuilder) UserIdType(userIdType string) *ListMetricSourceTableItemReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

// 页码标识，获取第一页传空，每次查询会返回下一页的page_token
//
// 示例值：6969864184272078374
func (builder *ListMetricSourceTableItemReqBuilder) PageToken(pageToken string) *ListMetricSourceTableItemReqBuilder {
	builder.apiReq.QueryParams.Set("page_token", fmt.Sprint(pageToken))
	return builder
}

// 每页获取记录数
//
// 示例值：10
func (builder *ListMetricSourceTableItemReqBuilder) PageSize(pageSize string) *ListMetricSourceTableItemReqBuilder {
	builder.apiReq.QueryParams.Set("page_size", fmt.Sprint(pageSize))
	return builder
}

func (builder *ListMetricSourceTableItemReqBuilder) Build() *ListMetricSourceTableItemReq {
	req := &ListMetricSourceTableItemReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type ListMetricSourceTableItemReq struct {
	apiReq *larkcore.ApiReq
}

type ListMetricSourceTableItemRespData struct {
	Total     *int          `json:"total,omitempty"`      // 符合条件的记录总数
	HasMore   *bool         `json:"has_more,omitempty"`   // 是否有下一页
	PageToken *string       `json:"page_token,omitempty"` // 下一页页码
	Items     []*MetricItem `json:"items,omitempty"`      // 指标项列表
}

type ListMetricSourceTableItemResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *ListMetricSourceTableItemRespData `json:"data"` // 业务数据
}

func (resp *ListMetricSourceTableItemResp) Success() bool {
	return resp.Code == 0
}

type PatchMetricSourceTableItemReqBodyBuilder struct {
	metricInitialValue     float64 // 指标起始值
	metricInitialValueFlag bool
	metricTargetValue      float64 // 指标目标值
	metricTargetValueFlag  bool
	metricCurrentValue     float64 // 指标进度值
	metricCurrentValueFlag bool
	supportedUserId        string // 指标支撑的上级人员 id
	supportedUserIdFlag    bool
}

func NewPatchMetricSourceTableItemReqBodyBuilder() *PatchMetricSourceTableItemReqBodyBuilder {
	builder := &PatchMetricSourceTableItemReqBodyBuilder{}
	return builder
}

// 指标起始值
//
//示例值：1.0
func (builder *PatchMetricSourceTableItemReqBodyBuilder) MetricInitialValue(metricInitialValue float64) *PatchMetricSourceTableItemReqBodyBuilder {
	builder.metricInitialValue = metricInitialValue
	builder.metricInitialValueFlag = true
	return builder
}

// 指标目标值
//
//示例值：3.0
func (builder *PatchMetricSourceTableItemReqBodyBuilder) MetricTargetValue(metricTargetValue float64) *PatchMetricSourceTableItemReqBodyBuilder {
	builder.metricTargetValue = metricTargetValue
	builder.metricTargetValueFlag = true
	return builder
}

// 指标进度值
//
//示例值：2.0
func (builder *PatchMetricSourceTableItemReqBodyBuilder) MetricCurrentValue(metricCurrentValue float64) *PatchMetricSourceTableItemReqBodyBuilder {
	builder.metricCurrentValue = metricCurrentValue
	builder.metricCurrentValueFlag = true
	return builder
}

// 指标支撑的上级人员 id
//
//示例值：7041857032248410131
func (builder *PatchMetricSourceTableItemReqBodyBuilder) SupportedUserId(supportedUserId string) *PatchMetricSourceTableItemReqBodyBuilder {
	builder.supportedUserId = supportedUserId
	builder.supportedUserIdFlag = true
	return builder
}

func (builder *PatchMetricSourceTableItemReqBodyBuilder) Build() *PatchMetricSourceTableItemReqBody {
	req := &PatchMetricSourceTableItemReqBody{}
	if builder.metricInitialValueFlag {
		req.MetricInitialValue = &builder.metricInitialValue
	}
	if builder.metricTargetValueFlag {
		req.MetricTargetValue = &builder.metricTargetValue
	}
	if builder.metricCurrentValueFlag {
		req.MetricCurrentValue = &builder.metricCurrentValue
	}
	if builder.supportedUserIdFlag {
		req.SupportedUserId = &builder.supportedUserId
	}
	return req
}

type PatchMetricSourceTableItemPathReqBodyBuilder struct {
	metricInitialValue     float64 // 指标起始值
	metricInitialValueFlag bool
	metricTargetValue      float64 // 指标目标值
	metricTargetValueFlag  bool
	metricCurrentValue     float64 // 指标进度值
	metricCurrentValueFlag bool
	supportedUserId        string // 指标支撑的上级人员 id
	supportedUserIdFlag    bool
}

func NewPatchMetricSourceTableItemPathReqBodyBuilder() *PatchMetricSourceTableItemPathReqBodyBuilder {
	builder := &PatchMetricSourceTableItemPathReqBodyBuilder{}
	return builder
}

// 指标起始值
//
// 示例值：1.0
func (builder *PatchMetricSourceTableItemPathReqBodyBuilder) MetricInitialValue(metricInitialValue float64) *PatchMetricSourceTableItemPathReqBodyBuilder {
	builder.metricInitialValue = metricInitialValue
	builder.metricInitialValueFlag = true
	return builder
}

// 指标目标值
//
// 示例值：3.0
func (builder *PatchMetricSourceTableItemPathReqBodyBuilder) MetricTargetValue(metricTargetValue float64) *PatchMetricSourceTableItemPathReqBodyBuilder {
	builder.metricTargetValue = metricTargetValue
	builder.metricTargetValueFlag = true
	return builder
}

// 指标进度值
//
// 示例值：2.0
func (builder *PatchMetricSourceTableItemPathReqBodyBuilder) MetricCurrentValue(metricCurrentValue float64) *PatchMetricSourceTableItemPathReqBodyBuilder {
	builder.metricCurrentValue = metricCurrentValue
	builder.metricCurrentValueFlag = true
	return builder
}

// 指标支撑的上级人员 id
//
// 示例值：7041857032248410131
func (builder *PatchMetricSourceTableItemPathReqBodyBuilder) SupportedUserId(supportedUserId string) *PatchMetricSourceTableItemPathReqBodyBuilder {
	builder.supportedUserId = supportedUserId
	builder.supportedUserIdFlag = true
	return builder
}

func (builder *PatchMetricSourceTableItemPathReqBodyBuilder) Build() (*PatchMetricSourceTableItemReqBody, error) {
	req := &PatchMetricSourceTableItemReqBody{}
	if builder.metricInitialValueFlag {
		req.MetricInitialValue = &builder.metricInitialValue
	}
	if builder.metricTargetValueFlag {
		req.MetricTargetValue = &builder.metricTargetValue
	}
	if builder.metricCurrentValueFlag {
		req.MetricCurrentValue = &builder.metricCurrentValue
	}
	if builder.supportedUserIdFlag {
		req.SupportedUserId = &builder.supportedUserId
	}
	return req, nil
}

type PatchMetricSourceTableItemReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *PatchMetricSourceTableItemReqBody
}

func NewPatchMetricSourceTableItemReqBuilder() *PatchMetricSourceTableItemReqBuilder {
	builder := &PatchMetricSourceTableItemReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// okr指标库id
//
// 示例值：7041857032248410131
func (builder *PatchMetricSourceTableItemReqBuilder) MetricSourceId(metricSourceId string) *PatchMetricSourceTableItemReqBuilder {
	builder.apiReq.PathParams.Set("metric_source_id", fmt.Sprint(metricSourceId))
	return builder
}

// okr指标表id
//
// 示例值：7041857032248410131
func (builder *PatchMetricSourceTableItemReqBuilder) MetricTableId(metricTableId string) *PatchMetricSourceTableItemReqBuilder {
	builder.apiReq.PathParams.Set("metric_table_id", fmt.Sprint(metricTableId))
	return builder
}

// okr指标项id
//
// 示例值：7041857032248410131
func (builder *PatchMetricSourceTableItemReqBuilder) MetricItemId(metricItemId string) *PatchMetricSourceTableItemReqBuilder {
	builder.apiReq.PathParams.Set("metric_item_id", fmt.Sprint(metricItemId))
	return builder
}

// 此次调用中使用的用户ID的类型
//
// 示例值：
func (builder *PatchMetricSourceTableItemReqBuilder) UserIdType(userIdType string) *PatchMetricSourceTableItemReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

// - 该接口用于更新某项指标，接口仅限 OKR 企业版使用。;;	更新成功后 OKR 系统会给以下人员发送消息通知：;;	- 首次更新目标值的人员 ;;	- 已经将指标添加为 KR、且本次目标值/起始值/支撑的上级有变更的人员，不包含仅更新了进度值的人员
func (builder *PatchMetricSourceTableItemReqBuilder) Body(body *PatchMetricSourceTableItemReqBody) *PatchMetricSourceTableItemReqBuilder {
	builder.body = body
	return builder
}

func (builder *PatchMetricSourceTableItemReqBuilder) Build() *PatchMetricSourceTableItemReq {
	req := &PatchMetricSourceTableItemReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.body
	return req
}

type PatchMetricSourceTableItemReqBody struct {
	MetricInitialValue *float64 `json:"metric_initial_value,omitempty"` // 指标起始值
	MetricTargetValue  *float64 `json:"metric_target_value,omitempty"`  // 指标目标值
	MetricCurrentValue *float64 `json:"metric_current_value,omitempty"` // 指标进度值
	SupportedUserId    *string  `json:"supported_user_id,omitempty"`    // 指标支撑的上级人员 id
}

type PatchMetricSourceTableItemReq struct {
	apiReq *larkcore.ApiReq
	Body   *PatchMetricSourceTableItemReqBody `body:""`
}

type PatchMetricSourceTableItemRespData struct {
	MetricItemId       *string     `json:"metric_item_id,omitempty"`       // 指标表id
	UserId             *string     `json:"user_id,omitempty"`              // 指标承接人员id
	PeriodId           *string     `json:"period_id,omitempty"`            // 指标的okr周期
	MetricUnit         *MetricUnit `json:"metric_unit,omitempty"`          // 指标单位
	MetricInitialValue *float64    `json:"metric_initial_value,omitempty"` // 指标起始值
	MetricTargetValue  *float64    `json:"metric_target_value,omitempty"`  // 指标目标值
	MetricCurrentValue *float64    `json:"metric_current_value,omitempty"` // 指标进度值
	SupportedUserId    *string     `json:"supported_user_id,omitempty"`    // 指标支撑的上级人员id
	KrId               *string     `json:"kr_id,omitempty"`                // 指标关联的kr
	UpdatedAt          *string     `json:"updated_at,omitempty"`           // 更新时间
	UpdatedBy          *string     `json:"updated_by,omitempty"`           // 更新人
}

type PatchMetricSourceTableItemResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *PatchMetricSourceTableItemRespData `json:"data"` // 业务数据
}

func (resp *PatchMetricSourceTableItemResp) Success() bool {
	return resp.Code == 0
}

type BatchGetOkrReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewBatchGetOkrReqBuilder() *BatchGetOkrReqBuilder {
	builder := &BatchGetOkrReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 此次调用中使用的用户ID的类型
//
// 示例值：
func (builder *BatchGetOkrReqBuilder) UserIdType(userIdType string) *BatchGetOkrReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

// OKR ID 列表，最多10个
//
// 示例值：7043693679567028244
func (builder *BatchGetOkrReqBuilder) OkrIds(okrIds []string) *BatchGetOkrReqBuilder {
	for _, v := range okrIds {
		builder.apiReq.QueryParams.Add("okr_ids", fmt.Sprint(v))
	}
	return builder
}

// 请求OKR的语言版本（比如@的人名），lang=en_us/zh_cn，请求 Query中
//
// 示例值：zh_cn
func (builder *BatchGetOkrReqBuilder) Lang(lang string) *BatchGetOkrReqBuilder {
	builder.apiReq.QueryParams.Set("lang", fmt.Sprint(lang))
	return builder
}

func (builder *BatchGetOkrReqBuilder) Build() *BatchGetOkrReq {
	req := &BatchGetOkrReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type BatchGetOkrReq struct {
	apiReq *larkcore.ApiReq
}

type BatchGetOkrRespData struct {
	OkrList []*OkrBatch `json:"okr_list,omitempty"` // OKR 列表
}

type BatchGetOkrResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *BatchGetOkrRespData `json:"data"` // 业务数据
}

func (resp *BatchGetOkrResp) Success() bool {
	return resp.Code == 0
}

type ListPeriodReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewListPeriodReqBuilder() *ListPeriodReqBuilder {
	builder := &ListPeriodReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 分页标志page_token
//
// 示例值：xaasdasdax
func (builder *ListPeriodReqBuilder) PageToken(pageToken string) *ListPeriodReqBuilder {
	builder.apiReq.QueryParams.Set("page_token", fmt.Sprint(pageToken))
	return builder
}

// 分页大小，默认10
//
// 示例值：10
func (builder *ListPeriodReqBuilder) PageSize(pageSize int) *ListPeriodReqBuilder {
	builder.apiReq.QueryParams.Set("page_size", fmt.Sprint(pageSize))
	return builder
}

func (builder *ListPeriodReqBuilder) Build() *ListPeriodReq {
	req := &ListPeriodReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type ListPeriodReq struct {
	apiReq *larkcore.ApiReq
}

type ListPeriodRespData struct {
	PageToken *string   `json:"page_token,omitempty"` // 分页标志
	HasMore   *bool     `json:"has_more,omitempty"`   // 是否有更多
	Items     []*Period `json:"items,omitempty"`      // 数据项
}

type ListPeriodResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *ListPeriodRespData `json:"data"` // 业务数据
}

func (resp *ListPeriodResp) Success() bool {
	return resp.Code == 0
}

type CreateProgressRecordReqBodyBuilder struct {
	sourceTitle     string // 进展来源
	sourceTitleFlag bool
	sourceUrl       string // 进展来源链接
	sourceUrlFlag   bool
	targetId        string // 目标id，与target_type对应
	targetIdFlag    bool
	targetType      int // 目标类型
	targetTypeFlag  bool
	content         *ContentBlock // 进展详情 富文本格式
	contentFlag     bool
}

func NewCreateProgressRecordReqBodyBuilder() *CreateProgressRecordReqBodyBuilder {
	builder := &CreateProgressRecordReqBodyBuilder{}
	return builder
}

// 进展来源
//
//示例值：周报系统
func (builder *CreateProgressRecordReqBodyBuilder) SourceTitle(sourceTitle string) *CreateProgressRecordReqBodyBuilder {
	builder.sourceTitle = sourceTitle
	builder.sourceTitleFlag = true
	return builder
}

// 进展来源链接
//
//示例值：https://www.zhoubao.com
func (builder *CreateProgressRecordReqBodyBuilder) SourceUrl(sourceUrl string) *CreateProgressRecordReqBodyBuilder {
	builder.sourceUrl = sourceUrl
	builder.sourceUrlFlag = true
	return builder
}

// 目标id，与target_type对应
//
//示例值：7041430377642082323
func (builder *CreateProgressRecordReqBodyBuilder) TargetId(targetId string) *CreateProgressRecordReqBodyBuilder {
	builder.targetId = targetId
	builder.targetIdFlag = true
	return builder
}

// 目标类型
//
//示例值：1
func (builder *CreateProgressRecordReqBodyBuilder) TargetType(targetType int) *CreateProgressRecordReqBodyBuilder {
	builder.targetType = targetType
	builder.targetTypeFlag = true
	return builder
}

// 进展详情 富文本格式
//
//示例值：
func (builder *CreateProgressRecordReqBodyBuilder) Content(content *ContentBlock) *CreateProgressRecordReqBodyBuilder {
	builder.content = content
	builder.contentFlag = true
	return builder
}

func (builder *CreateProgressRecordReqBodyBuilder) Build() *CreateProgressRecordReqBody {
	req := &CreateProgressRecordReqBody{}
	if builder.sourceTitleFlag {
		req.SourceTitle = &builder.sourceTitle
	}
	if builder.sourceUrlFlag {
		req.SourceUrl = &builder.sourceUrl
	}
	if builder.targetIdFlag {
		req.TargetId = &builder.targetId
	}
	if builder.targetTypeFlag {
		req.TargetType = &builder.targetType
	}
	if builder.contentFlag {
		req.Content = builder.content
	}
	return req
}

type CreateProgressRecordPathReqBodyBuilder struct {
	sourceTitle     string // 进展来源
	sourceTitleFlag bool
	sourceUrl       string // 进展来源链接
	sourceUrlFlag   bool
	targetId        string // 目标id，与target_type对应
	targetIdFlag    bool
	targetType      int // 目标类型
	targetTypeFlag  bool
	content         *ContentBlock // 进展详情 富文本格式
	contentFlag     bool
}

func NewCreateProgressRecordPathReqBodyBuilder() *CreateProgressRecordPathReqBodyBuilder {
	builder := &CreateProgressRecordPathReqBodyBuilder{}
	return builder
}

// 进展来源
//
// 示例值：周报系统
func (builder *CreateProgressRecordPathReqBodyBuilder) SourceTitle(sourceTitle string) *CreateProgressRecordPathReqBodyBuilder {
	builder.sourceTitle = sourceTitle
	builder.sourceTitleFlag = true
	return builder
}

// 进展来源链接
//
// 示例值：https://www.zhoubao.com
func (builder *CreateProgressRecordPathReqBodyBuilder) SourceUrl(sourceUrl string) *CreateProgressRecordPathReqBodyBuilder {
	builder.sourceUrl = sourceUrl
	builder.sourceUrlFlag = true
	return builder
}

// 目标id，与target_type对应
//
// 示例值：7041430377642082323
func (builder *CreateProgressRecordPathReqBodyBuilder) TargetId(targetId string) *CreateProgressRecordPathReqBodyBuilder {
	builder.targetId = targetId
	builder.targetIdFlag = true
	return builder
}

// 目标类型
//
// 示例值：1
func (builder *CreateProgressRecordPathReqBodyBuilder) TargetType(targetType int) *CreateProgressRecordPathReqBodyBuilder {
	builder.targetType = targetType
	builder.targetTypeFlag = true
	return builder
}

// 进展详情 富文本格式
//
// 示例值：
func (builder *CreateProgressRecordPathReqBodyBuilder) Content(content *ContentBlock) *CreateProgressRecordPathReqBodyBuilder {
	builder.content = content
	builder.contentFlag = true
	return builder
}

func (builder *CreateProgressRecordPathReqBodyBuilder) Build() (*CreateProgressRecordReqBody, error) {
	req := &CreateProgressRecordReqBody{}
	if builder.sourceTitleFlag {
		req.SourceTitle = &builder.sourceTitle
	}
	if builder.sourceUrlFlag {
		req.SourceUrl = &builder.sourceUrl
	}
	if builder.targetIdFlag {
		req.TargetId = &builder.targetId
	}
	if builder.targetTypeFlag {
		req.TargetType = &builder.targetType
	}
	if builder.contentFlag {
		req.Content = builder.content
	}
	return req, nil
}

type CreateProgressRecordReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *CreateProgressRecordReqBody
}

func NewCreateProgressRecordReqBuilder() *CreateProgressRecordReqBuilder {
	builder := &CreateProgressRecordReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 此次调用中使用的用户ID的类型
//
// 示例值：
func (builder *CreateProgressRecordReqBuilder) UserIdType(userIdType string) *CreateProgressRecordReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

// 创建OKR进展记录
func (builder *CreateProgressRecordReqBuilder) Body(body *CreateProgressRecordReqBody) *CreateProgressRecordReqBuilder {
	builder.body = body
	return builder
}

func (builder *CreateProgressRecordReqBuilder) Build() *CreateProgressRecordReq {
	req := &CreateProgressRecordReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.body
	return req
}

type CreateProgressRecordReqBody struct {
	SourceTitle *string       `json:"source_title,omitempty"` // 进展来源
	SourceUrl   *string       `json:"source_url,omitempty"`   // 进展来源链接
	TargetId    *string       `json:"target_id,omitempty"`    // 目标id，与target_type对应
	TargetType  *int          `json:"target_type,omitempty"`  // 目标类型
	Content     *ContentBlock `json:"content,omitempty"`      // 进展详情 富文本格式
}

type CreateProgressRecordReq struct {
	apiReq *larkcore.ApiReq
	Body   *CreateProgressRecordReqBody `body:""`
}

type CreateProgressRecordRespData struct {
	ProgressId *string       `json:"progress_id,omitempty"` // OKR 进展ID
	ModifyTime *string       `json:"modify_time,omitempty"` // 进展更新时间 毫秒
	Content    *ContentBlock `json:"content,omitempty"`     // 进展 对应的 Content 详细内容
}

type CreateProgressRecordResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *CreateProgressRecordRespData `json:"data"` // 业务数据
}

func (resp *CreateProgressRecordResp) Success() bool {
	return resp.Code == 0
}

type DeleteProgressRecordReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewDeleteProgressRecordReqBuilder() *DeleteProgressRecordReqBuilder {
	builder := &DeleteProgressRecordReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 待删除的 OKR进展记录 ID
//
// 示例值：7041857032248410131
func (builder *DeleteProgressRecordReqBuilder) ProgressId(progressId string) *DeleteProgressRecordReqBuilder {
	builder.apiReq.PathParams.Set("progress_id", fmt.Sprint(progressId))
	return builder
}

func (builder *DeleteProgressRecordReqBuilder) Build() *DeleteProgressRecordReq {
	req := &DeleteProgressRecordReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	return req
}

type DeleteProgressRecordReq struct {
	apiReq *larkcore.ApiReq
}

type DeleteProgressRecordResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *DeleteProgressRecordResp) Success() bool {
	return resp.Code == 0
}

type GetProgressRecordReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewGetProgressRecordReqBuilder() *GetProgressRecordReqBuilder {
	builder := &GetProgressRecordReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 待查询的 OKR进展记录 ID
//
// 示例值：7041857032248410131
func (builder *GetProgressRecordReqBuilder) ProgressId(progressId string) *GetProgressRecordReqBuilder {
	builder.apiReq.PathParams.Set("progress_id", fmt.Sprint(progressId))
	return builder
}

// 此次调用中使用的用户ID的类型
//
// 示例值：
func (builder *GetProgressRecordReqBuilder) UserIdType(userIdType string) *GetProgressRecordReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

func (builder *GetProgressRecordReqBuilder) Build() *GetProgressRecordReq {
	req := &GetProgressRecordReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type GetProgressRecordReq struct {
	apiReq *larkcore.ApiReq
}

type GetProgressRecordRespData struct {
	ProgressId *string       `json:"progress_id,omitempty"` // OKR 进展ID
	ModifyTime *string       `json:"modify_time,omitempty"` // 进展更新时间 毫秒
	Content    *ContentBlock `json:"content,omitempty"`     // 进展 对应的 Content 详细内容
}

type GetProgressRecordResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *GetProgressRecordRespData `json:"data"` // 业务数据
}

func (resp *GetProgressRecordResp) Success() bool {
	return resp.Code == 0
}

type UpdateProgressRecordReqBodyBuilder struct {
	content     *ContentBlock // 进展详情 富文本格式
	contentFlag bool
}

func NewUpdateProgressRecordReqBodyBuilder() *UpdateProgressRecordReqBodyBuilder {
	builder := &UpdateProgressRecordReqBodyBuilder{}
	return builder
}

// 进展详情 富文本格式
//
//示例值：
func (builder *UpdateProgressRecordReqBodyBuilder) Content(content *ContentBlock) *UpdateProgressRecordReqBodyBuilder {
	builder.content = content
	builder.contentFlag = true
	return builder
}

func (builder *UpdateProgressRecordReqBodyBuilder) Build() *UpdateProgressRecordReqBody {
	req := &UpdateProgressRecordReqBody{}
	if builder.contentFlag {
		req.Content = builder.content
	}
	return req
}

type UpdateProgressRecordPathReqBodyBuilder struct {
	content     *ContentBlock // 进展详情 富文本格式
	contentFlag bool
}

func NewUpdateProgressRecordPathReqBodyBuilder() *UpdateProgressRecordPathReqBodyBuilder {
	builder := &UpdateProgressRecordPathReqBodyBuilder{}
	return builder
}

// 进展详情 富文本格式
//
// 示例值：
func (builder *UpdateProgressRecordPathReqBodyBuilder) Content(content *ContentBlock) *UpdateProgressRecordPathReqBodyBuilder {
	builder.content = content
	builder.contentFlag = true
	return builder
}

func (builder *UpdateProgressRecordPathReqBodyBuilder) Build() (*UpdateProgressRecordReqBody, error) {
	req := &UpdateProgressRecordReqBody{}
	if builder.contentFlag {
		req.Content = builder.content
	}
	return req, nil
}

type UpdateProgressRecordReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *UpdateProgressRecordReqBody
}

func NewUpdateProgressRecordReqBuilder() *UpdateProgressRecordReqBuilder {
	builder := &UpdateProgressRecordReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 待更新的 OKR进展记录 ID
//
// 示例值：7041857032248410131
func (builder *UpdateProgressRecordReqBuilder) ProgressId(progressId string) *UpdateProgressRecordReqBuilder {
	builder.apiReq.PathParams.Set("progress_id", fmt.Sprint(progressId))
	return builder
}

// 此次调用中使用的用户ID的类型
//
// 示例值：
func (builder *UpdateProgressRecordReqBuilder) UserIdType(userIdType string) *UpdateProgressRecordReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

// 根据OKR进展记录ID更新进展详情
func (builder *UpdateProgressRecordReqBuilder) Body(body *UpdateProgressRecordReqBody) *UpdateProgressRecordReqBuilder {
	builder.body = body
	return builder
}

func (builder *UpdateProgressRecordReqBuilder) Build() *UpdateProgressRecordReq {
	req := &UpdateProgressRecordReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.body
	return req
}

type UpdateProgressRecordReqBody struct {
	Content *ContentBlock `json:"content,omitempty"` // 进展详情 富文本格式
}

type UpdateProgressRecordReq struct {
	apiReq *larkcore.ApiReq
	Body   *UpdateProgressRecordReqBody `body:""`
}

type UpdateProgressRecordRespData struct {
	ProgressId *string       `json:"progress_id,omitempty"` // OKR 进展ID
	ModifyTime *string       `json:"modify_time,omitempty"` // 进展更新时间 毫秒
	Content    *ContentBlock `json:"content,omitempty"`     // 进展 对应的 Content 详细内容
}

type UpdateProgressRecordResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *UpdateProgressRecordRespData `json:"data"` // 业务数据
}

func (resp *UpdateProgressRecordResp) Success() bool {
	return resp.Code == 0
}

type QueryReviewReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewQueryReviewReqBuilder() *QueryReviewReqBuilder {
	builder := &QueryReviewReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 此次调用中使用的用户ID的类型
//
// 示例值：
func (builder *QueryReviewReqBuilder) UserIdType(userIdType string) *QueryReviewReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

// 目标用户id列表，最多5个
//
// 示例值：ou_e6139117c300506837def50545420c6a
func (builder *QueryReviewReqBuilder) UserIds(userIds []string) *QueryReviewReqBuilder {
	for _, v := range userIds {
		builder.apiReq.QueryParams.Add("user_ids", fmt.Sprint(v))
	}
	return builder
}

// period_id列表，最多5个
//
// 示例值：7067724095781142548
func (builder *QueryReviewReqBuilder) PeriodIds(periodIds []string) *QueryReviewReqBuilder {
	for _, v := range periodIds {
		builder.apiReq.QueryParams.Add("period_ids", fmt.Sprint(v))
	}
	return builder
}

func (builder *QueryReviewReqBuilder) Build() *QueryReviewReq {
	req := &QueryReviewReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type QueryReviewReq struct {
	apiReq *larkcore.ApiReq
}

type QueryReviewRespData struct {
	ReviewList []*OkrReview `json:"review_list,omitempty"` // OKR复盘 列表
}

type QueryReviewResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *QueryReviewRespData `json:"data"` // 业务数据
}

func (resp *QueryReviewResp) Success() bool {
	return resp.Code == 0
}

type ListUserOkrReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewListUserOkrReqBuilder() *ListUserOkrReqBuilder {
	builder := &ListUserOkrReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 目标用户id
//
// 示例值：ou-asdasdasdasdasd
func (builder *ListUserOkrReqBuilder) UserId(userId string) *ListUserOkrReqBuilder {
	builder.apiReq.PathParams.Set("user_id", fmt.Sprint(userId))
	return builder
}

// 此次调用中使用的用户ID的类型
//
// 示例值：
func (builder *ListUserOkrReqBuilder) UserIdType(userIdType string) *ListUserOkrReqBuilder {
	builder.apiReq.QueryParams.Set("user_id_type", fmt.Sprint(userIdType))
	return builder
}

// 请求列表的偏移，offset>=0
//
// 示例值：0
func (builder *ListUserOkrReqBuilder) Offset(offset string) *ListUserOkrReqBuilder {
	builder.apiReq.QueryParams.Set("offset", fmt.Sprint(offset))
	return builder
}

// 列表长度，0-10
//
// 示例值：5
func (builder *ListUserOkrReqBuilder) Limit(limit string) *ListUserOkrReqBuilder {
	builder.apiReq.QueryParams.Set("limit", fmt.Sprint(limit))
	return builder
}

// 请求OKR的语言版本（比如@的人名），lang=en_us/zh_cn
//
// 示例值：zh_cn
func (builder *ListUserOkrReqBuilder) Lang(lang string) *ListUserOkrReqBuilder {
	builder.apiReq.QueryParams.Set("lang", fmt.Sprint(lang))
	return builder
}

// period_id列表，最多10个
//
// 示例值：["6951461264858777132"]
func (builder *ListUserOkrReqBuilder) PeriodIds(periodIds []string) *ListUserOkrReqBuilder {
	for _, v := range periodIds {
		builder.apiReq.QueryParams.Add("period_ids", fmt.Sprint(v))
	}
	return builder
}

func (builder *ListUserOkrReqBuilder) Build() *ListUserOkrReq {
	req := &ListUserOkrReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type ListUserOkrReq struct {
	apiReq *larkcore.ApiReq
}

type ListUserOkrRespData struct {
	Total   *int        `json:"total,omitempty"`    // OKR周期总数
	OkrList []*OkrBatch `json:"okr_list,omitempty"` // OKR 列表
}

type ListUserOkrResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *ListUserOkrRespData `json:"data"` // 业务数据
}

func (resp *ListUserOkrResp) Success() bool {
	return resp.Code == 0
}
