// Package search code generated by oapi sdk gen
/*
 * MIT License
 *
 * Copyright (c) 2022 Lark Technologies Pte. Ltd.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice, shall be included in all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */

package larksearch

import (
	"fmt"

	"context"
	"errors"

	"github.com/larksuite/oapi-sdk-go/v3/core"
)

const (
	StateOnline  = 0 // 已上线
	StateOffline = 1 // 未上线

)

const (
	ViewFULL  = 0 // 全量数据
	ViewBASIC = 1 // 摘要数据

)

const (
	StatePatchDataSourceOnline  = 0 // 已上线
	StatePatchDataSourceOffline = 1 // 未上线

)

type Acl struct {
	Access *string `json:"access,omitempty"` // 权限类型，优先级：Deny > Allow。
	Value  *string `json:"value,omitempty"`  // 设置的权限值，例如 userID ，依赖 type 描述。;;**注**：在 type 为 user 且 access 为 allow 时，可填 "everyone" 来表示该数据项对全员可见；
	Type   *string `json:"type,omitempty"`   // 权限值类型
}

type AclBuilder struct {
	access     string // 权限类型，优先级：Deny > Allow。
	accessFlag bool
	value      string // 设置的权限值，例如 userID ，依赖 type 描述。;;**注**：在 type 为 user 且 access 为 allow 时，可填 "everyone" 来表示该数据项对全员可见；
	valueFlag  bool
	type_      string // 权限值类型
	typeFlag   bool
}

func NewAclBuilder() *AclBuilder {
	builder := &AclBuilder{}
	return builder
}

// 权限类型，优先级：Deny > Allow。
//
// 示例值：allow
func (builder *AclBuilder) Access(access string) *AclBuilder {
	builder.access = access
	builder.accessFlag = true
	return builder
}

// 设置的权限值，例如 userID ，依赖 type 描述。;;**注**：在 type 为 user 且 access 为 allow 时，可填 "everyone" 来表示该数据项对全员可见；
//
// 示例值：d35e3c23
func (builder *AclBuilder) Value(value string) *AclBuilder {
	builder.value = value
	builder.valueFlag = true
	return builder
}

// 权限值类型
//
// 示例值：user
func (builder *AclBuilder) Type(type_ string) *AclBuilder {
	builder.type_ = type_
	builder.typeFlag = true
	return builder
}

func (builder *AclBuilder) Build() *Acl {
	req := &Acl{}
	if builder.accessFlag {
		req.Access = &builder.access

	}
	if builder.valueFlag {
		req.Value = &builder.value

	}
	if builder.typeFlag {
		req.Type = &builder.type_

	}
	return req
}

type BatchItemResult struct {
	ItemId    *string `json:"item_id,omitempty"`    // 数据项ID，对应一条索引数据的ID
	IsSuccess *bool   `json:"is_success,omitempty"` // 判断单条数据是否成功
	Err       *string `json:"err,omitempty"`        // 如果单条数据失败，表示单条数据的错误信息；如果单条数据成功被索引，则err是空字符串
}

type BatchItemResultBuilder struct {
	itemId        string // 数据项ID，对应一条索引数据的ID
	itemIdFlag    bool
	isSuccess     bool // 判断单条数据是否成功
	isSuccessFlag bool
	err           string // 如果单条数据失败，表示单条数据的错误信息；如果单条数据成功被索引，则err是空字符串
	errFlag       bool
}

func NewBatchItemResultBuilder() *BatchItemResultBuilder {
	builder := &BatchItemResultBuilder{}
	return builder
}

// 数据项ID，对应一条索引数据的ID
//
// 示例值：
func (builder *BatchItemResultBuilder) ItemId(itemId string) *BatchItemResultBuilder {
	builder.itemId = itemId
	builder.itemIdFlag = true
	return builder
}

// 判断单条数据是否成功
//
// 示例值：
func (builder *BatchItemResultBuilder) IsSuccess(isSuccess bool) *BatchItemResultBuilder {
	builder.isSuccess = isSuccess
	builder.isSuccessFlag = true
	return builder
}

// 如果单条数据失败，表示单条数据的错误信息；如果单条数据成功被索引，则err是空字符串
//
// 示例值：
func (builder *BatchItemResultBuilder) Err(err string) *BatchItemResultBuilder {
	builder.err = err
	builder.errFlag = true
	return builder
}

func (builder *BatchItemResultBuilder) Build() *BatchItemResult {
	req := &BatchItemResult{}
	if builder.itemIdFlag {
		req.ItemId = &builder.itemId

	}
	if builder.isSuccessFlag {
		req.IsSuccess = &builder.isSuccess

	}
	if builder.errFlag {
		req.Err = &builder.err

	}
	return req
}

type ConnectDataSource struct {
	ServiceUrl         *string `json:"service_url,omitempty"`         // 要托管的服务API地址，例如https://open.feishu.cn/xxxx/xxxx
	ProjectName        *string `json:"project_name,omitempty"`        // 项目地址，只能包含小写字母，如bytedance_test
	DisplayName        *string `json:"display_name,omitempty"`        // datasource名称，会展示在飞书搜索分类按钮（searchTab）中，如：公司wiki
	Description        *string `json:"description,omitempty"`         // 描述datasource
	IconUrl            *string `json:"icon_url,omitempty"`            // 图标
	ProjectDescription *string `json:"project_description,omitempty"` // 托管api的描述
	ContactEmail       *string `json:"contact_email,omitempty"`       // 联系人邮箱，开发人员的邮箱，用于托管API的SLA（Service Level Agreement）问题沟通
	TenantName         *string `json:"tenant_name,omitempty"`         // 创建api的组织名称，对企业开发者来说，建议使用企业名称
}

type ConnectDataSourceBuilder struct {
	serviceUrl             string // 要托管的服务API地址，例如https://open.feishu.cn/xxxx/xxxx
	serviceUrlFlag         bool
	projectName            string // 项目地址，只能包含小写字母，如bytedance_test
	projectNameFlag        bool
	displayName            string // datasource名称，会展示在飞书搜索分类按钮（searchTab）中，如：公司wiki
	displayNameFlag        bool
	description            string // 描述datasource
	descriptionFlag        bool
	iconUrl                string // 图标
	iconUrlFlag            bool
	projectDescription     string // 托管api的描述
	projectDescriptionFlag bool
	contactEmail           string // 联系人邮箱，开发人员的邮箱，用于托管API的SLA（Service Level Agreement）问题沟通
	contactEmailFlag       bool
	tenantName             string // 创建api的组织名称，对企业开发者来说，建议使用企业名称
	tenantNameFlag         bool
}

func NewConnectDataSourceBuilder() *ConnectDataSourceBuilder {
	builder := &ConnectDataSourceBuilder{}
	return builder
}

// 要托管的服务API地址，例如https://open.feishu.cn/xxxx/xxxx
//
// 示例值：
func (builder *ConnectDataSourceBuilder) ServiceUrl(serviceUrl string) *ConnectDataSourceBuilder {
	builder.serviceUrl = serviceUrl
	builder.serviceUrlFlag = true
	return builder
}

// 项目地址，只能包含小写字母，如bytedance_test
//
// 示例值：
func (builder *ConnectDataSourceBuilder) ProjectName(projectName string) *ConnectDataSourceBuilder {
	builder.projectName = projectName
	builder.projectNameFlag = true
	return builder
}

// datasource名称，会展示在飞书搜索分类按钮（searchTab）中，如：公司wiki
//
// 示例值：
func (builder *ConnectDataSourceBuilder) DisplayName(displayName string) *ConnectDataSourceBuilder {
	builder.displayName = displayName
	builder.displayNameFlag = true
	return builder
}

// 描述datasource
//
// 示例值：
func (builder *ConnectDataSourceBuilder) Description(description string) *ConnectDataSourceBuilder {
	builder.description = description
	builder.descriptionFlag = true
	return builder
}

// 图标
//
// 示例值：
func (builder *ConnectDataSourceBuilder) IconUrl(iconUrl string) *ConnectDataSourceBuilder {
	builder.iconUrl = iconUrl
	builder.iconUrlFlag = true
	return builder
}

// 托管api的描述
//
// 示例值：
func (builder *ConnectDataSourceBuilder) ProjectDescription(projectDescription string) *ConnectDataSourceBuilder {
	builder.projectDescription = projectDescription
	builder.projectDescriptionFlag = true
	return builder
}

// 联系人邮箱，开发人员的邮箱，用于托管API的SLA（Service Level Agreement）问题沟通
//
// 示例值：
func (builder *ConnectDataSourceBuilder) ContactEmail(contactEmail string) *ConnectDataSourceBuilder {
	builder.contactEmail = contactEmail
	builder.contactEmailFlag = true
	return builder
}

// 创建api的组织名称，对企业开发者来说，建议使用企业名称
//
// 示例值：
func (builder *ConnectDataSourceBuilder) TenantName(tenantName string) *ConnectDataSourceBuilder {
	builder.tenantName = tenantName
	builder.tenantNameFlag = true
	return builder
}

func (builder *ConnectDataSourceBuilder) Build() *ConnectDataSource {
	req := &ConnectDataSource{}
	if builder.serviceUrlFlag {
		req.ServiceUrl = &builder.serviceUrl

	}
	if builder.projectNameFlag {
		req.ProjectName = &builder.projectName

	}
	if builder.displayNameFlag {
		req.DisplayName = &builder.displayName

	}
	if builder.descriptionFlag {
		req.Description = &builder.description

	}
	if builder.iconUrlFlag {
		req.IconUrl = &builder.iconUrl

	}
	if builder.projectDescriptionFlag {
		req.ProjectDescription = &builder.projectDescription

	}
	if builder.contactEmailFlag {
		req.ContactEmail = &builder.contactEmail

	}
	if builder.tenantNameFlag {
		req.TenantName = &builder.tenantName

	}
	return req
}

type DataSource struct {
	Id               *string   `json:"id,omitempty"`                // 数据源的唯一标识
	Name             *string   `json:"name,omitempty"`              // data_source的展示名称
	State            *int      `json:"state,omitempty"`             // 数据源状态，0-已上线，1-未上线
	Description      *string   `json:"description,omitempty"`       // 对于数据源的描述
	CreateTime       *string   `json:"create_time,omitempty"`       // 创建时间，使用Unix时间戳，单位为“秒”
	UpdateTime       *string   `json:"update_time,omitempty"`       // 更新时间，使用Unix时间戳，单位为“秒”
	IsExceedQuota    *bool     `json:"is_exceed_quota,omitempty"`   // 是否超限
	IconUrl          *string   `json:"icon_url,omitempty"`          // 数据源在 search tab 上的展示图标路径
	Template         *string   `json:"template,omitempty"`          // 数据源采用的展示模版名称
	SearchableFields []string  `json:"searchable_fields,omitempty"` // 描述哪些字段可以被搜索
	I18nName         *I18nMeta `json:"i18n_name,omitempty"`         // 数据源的国际化展示名称
	I18nDescription  *I18nMeta `json:"i18n_description,omitempty"`  // 数据源的国际化描述
	SchemaId         *string   `json:"schema_id,omitempty"`         // 数据源关联的 schema 标识
}

type DataSourceBuilder struct {
	id                   string // 数据源的唯一标识
	idFlag               bool
	name                 string // data_source的展示名称
	nameFlag             bool
	state                int // 数据源状态，0-已上线，1-未上线
	stateFlag            bool
	description          string // 对于数据源的描述
	descriptionFlag      bool
	createTime           string // 创建时间，使用Unix时间戳，单位为“秒”
	createTimeFlag       bool
	updateTime           string // 更新时间，使用Unix时间戳，单位为“秒”
	updateTimeFlag       bool
	isExceedQuota        bool // 是否超限
	isExceedQuotaFlag    bool
	iconUrl              string // 数据源在 search tab 上的展示图标路径
	iconUrlFlag          bool
	template             string // 数据源采用的展示模版名称
	templateFlag         bool
	searchableFields     []string // 描述哪些字段可以被搜索
	searchableFieldsFlag bool
	i18nName             *I18nMeta // 数据源的国际化展示名称
	i18nNameFlag         bool
	i18nDescription      *I18nMeta // 数据源的国际化描述
	i18nDescriptionFlag  bool
	schemaId             string // 数据源关联的 schema 标识
	schemaIdFlag         bool
}

func NewDataSourceBuilder() *DataSourceBuilder {
	builder := &DataSourceBuilder{}
	return builder
}

// 数据源的唯一标识
//
// 示例值：5577006791947779410
func (builder *DataSourceBuilder) Id(id string) *DataSourceBuilder {
	builder.id = id
	builder.idFlag = true
	return builder
}

// data_source的展示名称
//
// 示例值：客服工单
func (builder *DataSourceBuilder) Name(name string) *DataSourceBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

// 数据源状态，0-已上线，1-未上线
//
// 示例值：0
func (builder *DataSourceBuilder) State(state int) *DataSourceBuilder {
	builder.state = state
	builder.stateFlag = true
	return builder
}

// 对于数据源的描述
//
// 示例值：搜索客服工单数据
func (builder *DataSourceBuilder) Description(description string) *DataSourceBuilder {
	builder.description = description
	builder.descriptionFlag = true
	return builder
}

// 创建时间，使用Unix时间戳，单位为“秒”
//
// 示例值：
func (builder *DataSourceBuilder) CreateTime(createTime string) *DataSourceBuilder {
	builder.createTime = createTime
	builder.createTimeFlag = true
	return builder
}

// 更新时间，使用Unix时间戳，单位为“秒”
//
// 示例值：
func (builder *DataSourceBuilder) UpdateTime(updateTime string) *DataSourceBuilder {
	builder.updateTime = updateTime
	builder.updateTimeFlag = true
	return builder
}

// 是否超限
//
// 示例值：
func (builder *DataSourceBuilder) IsExceedQuota(isExceedQuota bool) *DataSourceBuilder {
	builder.isExceedQuota = isExceedQuota
	builder.isExceedQuotaFlag = true
	return builder
}

// 数据源在 search tab 上的展示图标路径
//
// 示例值：https://www.xxx.com/open.jpg
func (builder *DataSourceBuilder) IconUrl(iconUrl string) *DataSourceBuilder {
	builder.iconUrl = iconUrl
	builder.iconUrlFlag = true
	return builder
}

// 数据源采用的展示模版名称
//
// 示例值：search_common_card
func (builder *DataSourceBuilder) Template(template string) *DataSourceBuilder {
	builder.template = template
	builder.templateFlag = true
	return builder
}

// 描述哪些字段可以被搜索
//
// 示例值：["field1", "field2"]（不推荐使用，如果有定制搜索需求，请用 schema 接口）
func (builder *DataSourceBuilder) SearchableFields(searchableFields []string) *DataSourceBuilder {
	builder.searchableFields = searchableFields
	builder.searchableFieldsFlag = true
	return builder
}

// 数据源的国际化展示名称
//
// 示例值：
func (builder *DataSourceBuilder) I18nName(i18nName *I18nMeta) *DataSourceBuilder {
	builder.i18nName = i18nName
	builder.i18nNameFlag = true
	return builder
}

// 数据源的国际化描述
//
// 示例值：
func (builder *DataSourceBuilder) I18nDescription(i18nDescription *I18nMeta) *DataSourceBuilder {
	builder.i18nDescription = i18nDescription
	builder.i18nDescriptionFlag = true
	return builder
}

// 数据源关联的 schema 标识
//
// 示例值：custom_schema
func (builder *DataSourceBuilder) SchemaId(schemaId string) *DataSourceBuilder {
	builder.schemaId = schemaId
	builder.schemaIdFlag = true
	return builder
}

func (builder *DataSourceBuilder) Build() *DataSource {
	req := &DataSource{}
	if builder.idFlag {
		req.Id = &builder.id

	}
	if builder.nameFlag {
		req.Name = &builder.name

	}
	if builder.stateFlag {
		req.State = &builder.state

	}
	if builder.descriptionFlag {
		req.Description = &builder.description

	}
	if builder.createTimeFlag {
		req.CreateTime = &builder.createTime

	}
	if builder.updateTimeFlag {
		req.UpdateTime = &builder.updateTime

	}
	if builder.isExceedQuotaFlag {
		req.IsExceedQuota = &builder.isExceedQuota

	}
	if builder.iconUrlFlag {
		req.IconUrl = &builder.iconUrl

	}
	if builder.templateFlag {
		req.Template = &builder.template

	}
	if builder.searchableFieldsFlag {
		req.SearchableFields = builder.searchableFields
	}
	if builder.i18nNameFlag {
		req.I18nName = builder.i18nName
	}
	if builder.i18nDescriptionFlag {
		req.I18nDescription = builder.i18nDescription
	}
	if builder.schemaIdFlag {
		req.SchemaId = &builder.schemaId

	}
	return req
}

type I18nMeta struct {
	ZhCn *string `json:"zh_cn,omitempty"` // 国际化字段：中文
	EnUs *string `json:"en_us,omitempty"` // 国际化字段：英文
	JaJp *string `json:"ja_jp,omitempty"` // 国际化字段：日文
}

type I18nMetaBuilder struct {
	zhCn     string // 国际化字段：中文
	zhCnFlag bool
	enUs     string // 国际化字段：英文
	enUsFlag bool
	jaJp     string // 国际化字段：日文
	jaJpFlag bool
}

func NewI18nMetaBuilder() *I18nMetaBuilder {
	builder := &I18nMetaBuilder{}
	return builder
}

// 国际化字段：中文
//
// 示例值：任务
func (builder *I18nMetaBuilder) ZhCn(zhCn string) *I18nMetaBuilder {
	builder.zhCn = zhCn
	builder.zhCnFlag = true
	return builder
}

// 国际化字段：英文
//
// 示例值：TODO
func (builder *I18nMetaBuilder) EnUs(enUs string) *I18nMetaBuilder {
	builder.enUs = enUs
	builder.enUsFlag = true
	return builder
}

// 国际化字段：日文
//
// 示例值：タスク
func (builder *I18nMetaBuilder) JaJp(jaJp string) *I18nMetaBuilder {
	builder.jaJp = jaJp
	builder.jaJpFlag = true
	return builder
}

func (builder *I18nMetaBuilder) Build() *I18nMeta {
	req := &I18nMeta{}
	if builder.zhCnFlag {
		req.ZhCn = &builder.zhCn

	}
	if builder.enUsFlag {
		req.EnUs = &builder.enUs

	}
	if builder.jaJpFlag {
		req.JaJp = &builder.jaJp

	}
	return req
}

type Item struct {
	Id             *string       `json:"id,omitempty"`              // item 在 datasource 中的唯一标识
	Acl            []*Acl        `json:"acl,omitempty"`             // item 的访问权限控制。 acl 字段为空数组，则默认数据不可见。如果数据是全员可见，需要设置 access="allow"; type="user"; value="everyone"
	Metadata       *ItemMetadata `json:"metadata,omitempty"`        // item 的元信息
	StructuredData *string       `json:"structured_data,omitempty"` // 结构化数据（以 json 字符串传递），这些字段是搜索结果的展示字段(特殊字段无须在此另外指定);具体格式可参参考 [通用模块接入指南](/uAjLw4CM/ukTMukTMukTM/search-v2/common-template-intergration-handbook) **请求创建数据项**部分
	Content        *ItemContent  `json:"content,omitempty"`         // 非结构化数据，如文档文本，飞书搜索会用来做召回
}

type ItemBuilder struct {
	id                 string // item 在 datasource 中的唯一标识
	idFlag             bool
	acl                []*Acl // item 的访问权限控制。 acl 字段为空数组，则默认数据不可见。如果数据是全员可见，需要设置 access="allow"; type="user"; value="everyone"
	aclFlag            bool
	metadata           *ItemMetadata // item 的元信息
	metadataFlag       bool
	structuredData     string // 结构化数据（以 json 字符串传递），这些字段是搜索结果的展示字段(特殊字段无须在此另外指定);具体格式可参参考 [通用模块接入指南](/uAjLw4CM/ukTMukTMukTM/search-v2/common-template-intergration-handbook) **请求创建数据项**部分
	structuredDataFlag bool
	content            *ItemContent // 非结构化数据，如文档文本，飞书搜索会用来做召回
	contentFlag        bool
}

func NewItemBuilder() *ItemBuilder {
	builder := &ItemBuilder{}
	return builder
}

// item 在 datasource 中的唯一标识
//
// 示例值：01010111
func (builder *ItemBuilder) Id(id string) *ItemBuilder {
	builder.id = id
	builder.idFlag = true
	return builder
}

// item 的访问权限控制。 acl 字段为空数组，则默认数据不可见。如果数据是全员可见，需要设置 access="allow"; type="user"; value="everyone"
//
// 示例值：
func (builder *ItemBuilder) Acl(acl []*Acl) *ItemBuilder {
	builder.acl = acl
	builder.aclFlag = true
	return builder
}

// item 的元信息
//
// 示例值：
func (builder *ItemBuilder) Metadata(metadata *ItemMetadata) *ItemBuilder {
	builder.metadata = metadata
	builder.metadataFlag = true
	return builder
}

// 结构化数据（以 json 字符串传递），这些字段是搜索结果的展示字段(特殊字段无须在此另外指定);具体格式可参参考 [通用模块接入指南](/uAjLw4CM/ukTMukTMukTM/search-v2/common-template-intergration-handbook) **请求创建数据项**部分
//
// 示例值：{\"key\":\"value\"}
func (builder *ItemBuilder) StructuredData(structuredData string) *ItemBuilder {
	builder.structuredData = structuredData
	builder.structuredDataFlag = true
	return builder
}

// 非结构化数据，如文档文本，飞书搜索会用来做召回
//
// 示例值：
func (builder *ItemBuilder) Content(content *ItemContent) *ItemBuilder {
	builder.content = content
	builder.contentFlag = true
	return builder
}

func (builder *ItemBuilder) Build() *Item {
	req := &Item{}
	if builder.idFlag {
		req.Id = &builder.id

	}
	if builder.aclFlag {
		req.Acl = builder.acl
	}
	if builder.metadataFlag {
		req.Metadata = builder.metadata
	}
	if builder.structuredDataFlag {
		req.StructuredData = &builder.structuredData

	}
	if builder.contentFlag {
		req.Content = builder.content
	}
	return req
}

type ItemContent struct {
	Format      *string `json:"format,omitempty"`       // 内容的格式
	ContentData *string `json:"content_data,omitempty"` // 全文数据
}

type ItemContentBuilder struct {
	format          string // 内容的格式
	formatFlag      bool
	contentData     string // 全文数据
	contentDataFlag bool
}

func NewItemContentBuilder() *ItemContentBuilder {
	builder := &ItemContentBuilder{}
	return builder
}

// 内容的格式
//
// 示例值：html
func (builder *ItemContentBuilder) Format(format string) *ItemContentBuilder {
	builder.format = format
	builder.formatFlag = true
	return builder
}

// 全文数据
//
// 示例值：这是一个很长的文本
func (builder *ItemContentBuilder) ContentData(contentData string) *ItemContentBuilder {
	builder.contentData = contentData
	builder.contentDataFlag = true
	return builder
}

func (builder *ItemContentBuilder) Build() *ItemContent {
	req := &ItemContent{}
	if builder.formatFlag {
		req.Format = &builder.format

	}
	if builder.contentDataFlag {
		req.ContentData = &builder.contentData

	}
	return req
}

type ItemMetadata struct {
	Title           *string `json:"title,omitempty"`             // 该条数据记录对应的标题
	SourceUrl       *string `json:"source_url,omitempty"`        // 该条数据记录对应的跳转url
	CreateTime      *int    `json:"create_time,omitempty"`       // 数据项的创建时间。Unix 时间，单位为秒
	UpdateTime      *int    `json:"update_time,omitempty"`       // 数据项的更新时间。Unix 时间，单位为秒
	SourceUrlMobile *string `json:"source_url_mobile,omitempty"` // 移动端搜索命中的跳转地址。如果您PC端和移动端有不同的跳转地址，可以在这里写入移动端专用的url，我们会在搜索时为您选择合适的地址
}

type ItemMetadataBuilder struct {
	title               string // 该条数据记录对应的标题
	titleFlag           bool
	sourceUrl           string // 该条数据记录对应的跳转url
	sourceUrlFlag       bool
	createTime          int // 数据项的创建时间。Unix 时间，单位为秒
	createTimeFlag      bool
	updateTime          int // 数据项的更新时间。Unix 时间，单位为秒
	updateTimeFlag      bool
	sourceUrlMobile     string // 移动端搜索命中的跳转地址。如果您PC端和移动端有不同的跳转地址，可以在这里写入移动端专用的url，我们会在搜索时为您选择合适的地址
	sourceUrlMobileFlag bool
}

func NewItemMetadataBuilder() *ItemMetadataBuilder {
	builder := &ItemMetadataBuilder{}
	return builder
}

// 该条数据记录对应的标题
//
// 示例值：工单：无法创建文章
func (builder *ItemMetadataBuilder) Title(title string) *ItemMetadataBuilder {
	builder.title = title
	builder.titleFlag = true
	return builder
}

// 该条数据记录对应的跳转url
//
// 示例值：http://www.abc.com.cn
func (builder *ItemMetadataBuilder) SourceUrl(sourceUrl string) *ItemMetadataBuilder {
	builder.sourceUrl = sourceUrl
	builder.sourceUrlFlag = true
	return builder
}

// 数据项的创建时间。Unix 时间，单位为秒
//
// 示例值：1618831236
func (builder *ItemMetadataBuilder) CreateTime(createTime int) *ItemMetadataBuilder {
	builder.createTime = createTime
	builder.createTimeFlag = true
	return builder
}

// 数据项的更新时间。Unix 时间，单位为秒
//
// 示例值：1618831236
func (builder *ItemMetadataBuilder) UpdateTime(updateTime int) *ItemMetadataBuilder {
	builder.updateTime = updateTime
	builder.updateTimeFlag = true
	return builder
}

// 移动端搜索命中的跳转地址。如果您PC端和移动端有不同的跳转地址，可以在这里写入移动端专用的url，我们会在搜索时为您选择合适的地址
//
// 示例值：https://www.feishu.cn
func (builder *ItemMetadataBuilder) SourceUrlMobile(sourceUrlMobile string) *ItemMetadataBuilder {
	builder.sourceUrlMobile = sourceUrlMobile
	builder.sourceUrlMobileFlag = true
	return builder
}

func (builder *ItemMetadataBuilder) Build() *ItemMetadata {
	req := &ItemMetadata{}
	if builder.titleFlag {
		req.Title = &builder.title

	}
	if builder.sourceUrlFlag {
		req.SourceUrl = &builder.sourceUrl

	}
	if builder.createTimeFlag {
		req.CreateTime = &builder.createTime

	}
	if builder.updateTimeFlag {
		req.UpdateTime = &builder.updateTime

	}
	if builder.sourceUrlMobileFlag {
		req.SourceUrlMobile = &builder.sourceUrlMobile

	}
	return req
}

type ItemRecord struct {
	ItemId       *string `json:"item_id,omitempty"`        // 冗余当前item的ID
	DataSourceId *string `json:"data_source_id,omitempty"` // 数据源id
	Version      *string `json:"version,omitempty"`        // 当前数据的最新版本号，其值等于上一次item/create接口传入的时间戳
	CreatedAt    *string `json:"created_at,omitempty"`     // 第一次投递时间
	UpdatedAt    *string `json:"updated_at,omitempty"`     // 上一次更新落库时间
}

type ItemRecordBuilder struct {
	itemId           string // 冗余当前item的ID
	itemIdFlag       bool
	dataSourceId     string // 数据源id
	dataSourceIdFlag bool
	version          string // 当前数据的最新版本号，其值等于上一次item/create接口传入的时间戳
	versionFlag      bool
	createdAt        string // 第一次投递时间
	createdAtFlag    bool
	updatedAt        string // 上一次更新落库时间
	updatedAtFlag    bool
}

func NewItemRecordBuilder() *ItemRecordBuilder {
	builder := &ItemRecordBuilder{}
	return builder
}

// 冗余当前item的ID
//
// 示例值：
func (builder *ItemRecordBuilder) ItemId(itemId string) *ItemRecordBuilder {
	builder.itemId = itemId
	builder.itemIdFlag = true
	return builder
}

// 数据源id
//
// 示例值：
func (builder *ItemRecordBuilder) DataSourceId(dataSourceId string) *ItemRecordBuilder {
	builder.dataSourceId = dataSourceId
	builder.dataSourceIdFlag = true
	return builder
}

// 当前数据的最新版本号，其值等于上一次item/create接口传入的时间戳
//
// 示例值：
func (builder *ItemRecordBuilder) Version(version string) *ItemRecordBuilder {
	builder.version = version
	builder.versionFlag = true
	return builder
}

// 第一次投递时间
//
// 示例值：
func (builder *ItemRecordBuilder) CreatedAt(createdAt string) *ItemRecordBuilder {
	builder.createdAt = createdAt
	builder.createdAtFlag = true
	return builder
}

// 上一次更新落库时间
//
// 示例值：
func (builder *ItemRecordBuilder) UpdatedAt(updatedAt string) *ItemRecordBuilder {
	builder.updatedAt = updatedAt
	builder.updatedAtFlag = true
	return builder
}

func (builder *ItemRecordBuilder) Build() *ItemRecord {
	req := &ItemRecord{}
	if builder.itemIdFlag {
		req.ItemId = &builder.itemId

	}
	if builder.dataSourceIdFlag {
		req.DataSourceId = &builder.dataSourceId

	}
	if builder.versionFlag {
		req.Version = &builder.version

	}
	if builder.createdAtFlag {
		req.CreatedAt = &builder.createdAt

	}
	if builder.updatedAtFlag {
		req.UpdatedAt = &builder.updatedAt

	}
	return req
}

type Schema struct {
	Properties []*SchemaProperty `json:"properties,omitempty"` // 数据范式的属性定义
	Display    *SchemaDisplay    `json:"display,omitempty"`    // 数据展示相关配置
	SchemaId   *string           `json:"schema_id,omitempty"`  // 用户自定义数据范式的唯一标识
}

type SchemaBuilder struct {
	properties     []*SchemaProperty // 数据范式的属性定义
	propertiesFlag bool
	display        *SchemaDisplay // 数据展示相关配置
	displayFlag    bool
	schemaId       string // 用户自定义数据范式的唯一标识
	schemaIdFlag   bool
}

func NewSchemaBuilder() *SchemaBuilder {
	builder := &SchemaBuilder{}
	return builder
}

// 数据范式的属性定义
//
// 示例值：
func (builder *SchemaBuilder) Properties(properties []*SchemaProperty) *SchemaBuilder {
	builder.properties = properties
	builder.propertiesFlag = true
	return builder
}

// 数据展示相关配置
//
// 示例值：
func (builder *SchemaBuilder) Display(display *SchemaDisplay) *SchemaBuilder {
	builder.display = display
	builder.displayFlag = true
	return builder
}

// 用户自定义数据范式的唯一标识
//
// 示例值：jira_schema
func (builder *SchemaBuilder) SchemaId(schemaId string) *SchemaBuilder {
	builder.schemaId = schemaId
	builder.schemaIdFlag = true
	return builder
}

func (builder *SchemaBuilder) Build() *Schema {
	req := &Schema{}
	if builder.propertiesFlag {
		req.Properties = builder.properties
	}
	if builder.displayFlag {
		req.Display = builder.display
	}
	if builder.schemaIdFlag {
		req.SchemaId = &builder.schemaId

	}
	return req
}

type SchemaDisplay struct {
	CardKey       *string                      `json:"card_key,omitempty"`       // 搜索数据的展示卡片;;;卡片详细信息请参考 [通用模块接入指南](/uAjLw4CM/ukTMukTMukTM/search-v2/common-template-intergration-handbook)  "请求创建数据范式"部分
	FieldsMapping []*SchemaDisplayFieldMapping `json:"fields_mapping,omitempty"` // 数据字段名称和展示字段名称的映射关系。如果没有设置，则只会展示 与展示字段名称同名的 数据字段
}

type SchemaDisplayBuilder struct {
	cardKey           string // 搜索数据的展示卡片;;;卡片详细信息请参考 [通用模块接入指南](/uAjLw4CM/ukTMukTMukTM/search-v2/common-template-intergration-handbook)  "请求创建数据范式"部分
	cardKeyFlag       bool
	fieldsMapping     []*SchemaDisplayFieldMapping // 数据字段名称和展示字段名称的映射关系。如果没有设置，则只会展示 与展示字段名称同名的 数据字段
	fieldsMappingFlag bool
}

func NewSchemaDisplayBuilder() *SchemaDisplayBuilder {
	builder := &SchemaDisplayBuilder{}
	return builder
}

// 搜索数据的展示卡片;;;卡片详细信息请参考 [通用模块接入指南](/uAjLw4CM/ukTMukTMukTM/search-v2/common-template-intergration-handbook)  "请求创建数据范式"部分
//
// 示例值：search_common_card
func (builder *SchemaDisplayBuilder) CardKey(cardKey string) *SchemaDisplayBuilder {
	builder.cardKey = cardKey
	builder.cardKeyFlag = true
	return builder
}

// 数据字段名称和展示字段名称的映射关系。如果没有设置，则只会展示 与展示字段名称同名的 数据字段
//
// 示例值：
func (builder *SchemaDisplayBuilder) FieldsMapping(fieldsMapping []*SchemaDisplayFieldMapping) *SchemaDisplayBuilder {
	builder.fieldsMapping = fieldsMapping
	builder.fieldsMappingFlag = true
	return builder
}

func (builder *SchemaDisplayBuilder) Build() *SchemaDisplay {
	req := &SchemaDisplay{}
	if builder.cardKeyFlag {
		req.CardKey = &builder.cardKey

	}
	if builder.fieldsMappingFlag {
		req.FieldsMapping = builder.fieldsMapping
	}
	return req
}

type SchemaDisplayFieldMapping struct {
	DisplayField *string `json:"display_field,omitempty"` // 展示字段名称，与 card_key 有关，每个模版能展示的字段不同。该字段不能重复
	DataField    *string `json:"data_field,omitempty"`    // 数据字段的名称。需要确保该字段对应在 schema 属性定义中的 is_returnable 为 true，否则无法展示。需要使用 ${xxx} 的规则来描述
}

type SchemaDisplayFieldMappingBuilder struct {
	displayField     string // 展示字段名称，与 card_key 有关，每个模版能展示的字段不同。该字段不能重复
	displayFieldFlag bool
	dataField        string // 数据字段的名称。需要确保该字段对应在 schema 属性定义中的 is_returnable 为 true，否则无法展示。需要使用 ${xxx} 的规则来描述
	dataFieldFlag    bool
}

func NewSchemaDisplayFieldMappingBuilder() *SchemaDisplayFieldMappingBuilder {
	builder := &SchemaDisplayFieldMappingBuilder{}
	return builder
}

// 展示字段名称，与 card_key 有关，每个模版能展示的字段不同。该字段不能重复
//
// 示例值：summary
func (builder *SchemaDisplayFieldMappingBuilder) DisplayField(displayField string) *SchemaDisplayFieldMappingBuilder {
	builder.displayField = displayField
	builder.displayFieldFlag = true
	return builder
}

// 数据字段的名称。需要确保该字段对应在 schema 属性定义中的 is_returnable 为 true，否则无法展示。需要使用 ${xxx} 的规则来描述
//
// 示例值：${description}
func (builder *SchemaDisplayFieldMappingBuilder) DataField(dataField string) *SchemaDisplayFieldMappingBuilder {
	builder.dataField = dataField
	builder.dataFieldFlag = true
	return builder
}

func (builder *SchemaDisplayFieldMappingBuilder) Build() *SchemaDisplayFieldMapping {
	req := &SchemaDisplayFieldMapping{}
	if builder.displayFieldFlag {
		req.DisplayField = &builder.displayField

	}
	if builder.dataFieldFlag {
		req.DataField = &builder.dataField

	}
	return req
}

type SchemaDisplayOption struct {
	DisplayLabel *string `json:"display_label,omitempty"` // 对外展示的标签名
	DisplayType  *string `json:"display_type,omitempty"`  // 对外展示类型
}

type SchemaDisplayOptionBuilder struct {
	displayLabel     string // 对外展示的标签名
	displayLabelFlag bool
	displayType      string // 对外展示类型
	displayTypeFlag  bool
}

func NewSchemaDisplayOptionBuilder() *SchemaDisplayOptionBuilder {
	builder := &SchemaDisplayOptionBuilder{}
	return builder
}

// 对外展示的标签名
//
// 示例值：
func (builder *SchemaDisplayOptionBuilder) DisplayLabel(displayLabel string) *SchemaDisplayOptionBuilder {
	builder.displayLabel = displayLabel
	builder.displayLabelFlag = true
	return builder
}

// 对外展示类型
//
// 示例值：
func (builder *SchemaDisplayOptionBuilder) DisplayType(displayType string) *SchemaDisplayOptionBuilder {
	builder.displayType = displayType
	builder.displayTypeFlag = true
	return builder
}

func (builder *SchemaDisplayOptionBuilder) Build() *SchemaDisplayOption {
	req := &SchemaDisplayOption{}
	if builder.displayLabelFlag {
		req.DisplayLabel = &builder.displayLabel

	}
	if builder.displayTypeFlag {
		req.DisplayType = &builder.displayType

	}
	return req
}

type SchemaProperty struct {
	Name            *string                `json:"name,omitempty"`             // 属性名
	Type            *string                `json:"type,omitempty"`             // 属性类型
	IsSearchable    *bool                  `json:"is_searchable,omitempty"`    // 该属性是否可用作搜索，默认为 false
	IsSortable      *bool                  `json:"is_sortable,omitempty"`      // 该属性是否可用作搜索结果排序，默认为 false。如果为 true，需要再配置 sortOptions
	IsReturnable    *bool                  `json:"is_returnable,omitempty"`    // 该属性是否可用作返回字段，为 false 时，该字段不会被召回和展示。默认为 false
	SortOptions     *SchemaSortOptions     `json:"sort_options,omitempty"`     // 属性排序的可选配置，当 is_sortable 为 true 时，该字段为必填字段
	TypeDefinitions *SchemaTypeDefinitions `json:"type_definitions,omitempty"` // 相关类型数据的定义和约束
	SearchOptions   *SchemaSearchOptions   `json:"search_options,omitempty"`   // 属性搜索的可选配置，当 is_searchable 为 true 时，该字段为必填参数
}

type SchemaPropertyBuilder struct {
	name                string // 属性名
	nameFlag            bool
	type_               string // 属性类型
	typeFlag            bool
	isSearchable        bool // 该属性是否可用作搜索，默认为 false
	isSearchableFlag    bool
	isSortable          bool // 该属性是否可用作搜索结果排序，默认为 false。如果为 true，需要再配置 sortOptions
	isSortableFlag      bool
	isReturnable        bool // 该属性是否可用作返回字段，为 false 时，该字段不会被召回和展示。默认为 false
	isReturnableFlag    bool
	sortOptions         *SchemaSortOptions // 属性排序的可选配置，当 is_sortable 为 true 时，该字段为必填字段
	sortOptionsFlag     bool
	typeDefinitions     *SchemaTypeDefinitions // 相关类型数据的定义和约束
	typeDefinitionsFlag bool
	searchOptions       *SchemaSearchOptions // 属性搜索的可选配置，当 is_searchable 为 true 时，该字段为必填参数
	searchOptionsFlag   bool
}

func NewSchemaPropertyBuilder() *SchemaPropertyBuilder {
	builder := &SchemaPropertyBuilder{}
	return builder
}

// 属性名
//
// 示例值：summary
func (builder *SchemaPropertyBuilder) Name(name string) *SchemaPropertyBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

// 属性类型
//
// 示例值：text
func (builder *SchemaPropertyBuilder) Type(type_ string) *SchemaPropertyBuilder {
	builder.type_ = type_
	builder.typeFlag = true
	return builder
}

// 该属性是否可用作搜索，默认为 false
//
// 示例值：true
func (builder *SchemaPropertyBuilder) IsSearchable(isSearchable bool) *SchemaPropertyBuilder {
	builder.isSearchable = isSearchable
	builder.isSearchableFlag = true
	return builder
}

// 该属性是否可用作搜索结果排序，默认为 false。如果为 true，需要再配置 sortOptions
//
// 示例值：false
func (builder *SchemaPropertyBuilder) IsSortable(isSortable bool) *SchemaPropertyBuilder {
	builder.isSortable = isSortable
	builder.isSortableFlag = true
	return builder
}

// 该属性是否可用作返回字段，为 false 时，该字段不会被召回和展示。默认为 false
//
// 示例值：true
func (builder *SchemaPropertyBuilder) IsReturnable(isReturnable bool) *SchemaPropertyBuilder {
	builder.isReturnable = isReturnable
	builder.isReturnableFlag = true
	return builder
}

// 属性排序的可选配置，当 is_sortable 为 true 时，该字段为必填字段
//
// 示例值：
func (builder *SchemaPropertyBuilder) SortOptions(sortOptions *SchemaSortOptions) *SchemaPropertyBuilder {
	builder.sortOptions = sortOptions
	builder.sortOptionsFlag = true
	return builder
}

// 相关类型数据的定义和约束
//
// 示例值：
func (builder *SchemaPropertyBuilder) TypeDefinitions(typeDefinitions *SchemaTypeDefinitions) *SchemaPropertyBuilder {
	builder.typeDefinitions = typeDefinitions
	builder.typeDefinitionsFlag = true
	return builder
}

// 属性搜索的可选配置，当 is_searchable 为 true 时，该字段为必填参数
//
// 示例值：
func (builder *SchemaPropertyBuilder) SearchOptions(searchOptions *SchemaSearchOptions) *SchemaPropertyBuilder {
	builder.searchOptions = searchOptions
	builder.searchOptionsFlag = true
	return builder
}

func (builder *SchemaPropertyBuilder) Build() *SchemaProperty {
	req := &SchemaProperty{}
	if builder.nameFlag {
		req.Name = &builder.name

	}
	if builder.typeFlag {
		req.Type = &builder.type_

	}
	if builder.isSearchableFlag {
		req.IsSearchable = &builder.isSearchable

	}
	if builder.isSortableFlag {
		req.IsSortable = &builder.isSortable

	}
	if builder.isReturnableFlag {
		req.IsReturnable = &builder.isReturnable

	}
	if builder.sortOptionsFlag {
		req.SortOptions = builder.sortOptions
	}
	if builder.typeDefinitionsFlag {
		req.TypeDefinitions = builder.typeDefinitions
	}
	if builder.searchOptionsFlag {
		req.SearchOptions = builder.searchOptions
	}
	return req
}

type SchemaPropertyDefinition struct {
	Name                 *string              `json:"name,omitempty"`                   // 属性名称
	IsReturnable         *bool                `json:"is_returnable,omitempty"`          // 搜索中是否可作为搜索结果返回
	IsRepeatable         *bool                `json:"is_repeatable,omitempty"`          // 是否允许重复
	IsSortable           *bool                `json:"is_sortable,omitempty"`            // 是否可用作排序
	IsFacetable          *bool                `json:"is_facetable,omitempty"`           // 是否可用来生成 facet，仅支持 Boolean，Enum，String 类型属性。
	IsWildcardSearchable *bool                `json:"is_wildcard_searchable,omitempty"` // 是否可以对该属性使用通配符搜索，只支持 String 类型属性。
	Type                 *string              `json:"type,omitempty"`                   // 属性数据类型
	DisplayOptions       *SchemaDisplayOption `json:"display_options,omitempty"`        // 属性对外展示可选项
}

type SchemaPropertyDefinitionBuilder struct {
	name                     string // 属性名称
	nameFlag                 bool
	isReturnable             bool // 搜索中是否可作为搜索结果返回
	isReturnableFlag         bool
	isRepeatable             bool // 是否允许重复
	isRepeatableFlag         bool
	isSortable               bool // 是否可用作排序
	isSortableFlag           bool
	isFacetable              bool // 是否可用来生成 facet，仅支持 Boolean，Enum，String 类型属性。
	isFacetableFlag          bool
	isWildcardSearchable     bool // 是否可以对该属性使用通配符搜索，只支持 String 类型属性。
	isWildcardSearchableFlag bool
	type_                    string // 属性数据类型
	typeFlag                 bool
	displayOptions           *SchemaDisplayOption // 属性对外展示可选项
	displayOptionsFlag       bool
}

func NewSchemaPropertyDefinitionBuilder() *SchemaPropertyDefinitionBuilder {
	builder := &SchemaPropertyDefinitionBuilder{}
	return builder
}

// 属性名称
//
// 示例值：
func (builder *SchemaPropertyDefinitionBuilder) Name(name string) *SchemaPropertyDefinitionBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

// 搜索中是否可作为搜索结果返回
//
// 示例值：false
func (builder *SchemaPropertyDefinitionBuilder) IsReturnable(isReturnable bool) *SchemaPropertyDefinitionBuilder {
	builder.isReturnable = isReturnable
	builder.isReturnableFlag = true
	return builder
}

// 是否允许重复
//
// 示例值：false
func (builder *SchemaPropertyDefinitionBuilder) IsRepeatable(isRepeatable bool) *SchemaPropertyDefinitionBuilder {
	builder.isRepeatable = isRepeatable
	builder.isRepeatableFlag = true
	return builder
}

// 是否可用作排序
//
// 示例值：false
func (builder *SchemaPropertyDefinitionBuilder) IsSortable(isSortable bool) *SchemaPropertyDefinitionBuilder {
	builder.isSortable = isSortable
	builder.isSortableFlag = true
	return builder
}

// 是否可用来生成 facet，仅支持 Boolean，Enum，String 类型属性。
//
// 示例值：false
func (builder *SchemaPropertyDefinitionBuilder) IsFacetable(isFacetable bool) *SchemaPropertyDefinitionBuilder {
	builder.isFacetable = isFacetable
	builder.isFacetableFlag = true
	return builder
}

// 是否可以对该属性使用通配符搜索，只支持 String 类型属性。
//
// 示例值：
func (builder *SchemaPropertyDefinitionBuilder) IsWildcardSearchable(isWildcardSearchable bool) *SchemaPropertyDefinitionBuilder {
	builder.isWildcardSearchable = isWildcardSearchable
	builder.isWildcardSearchableFlag = true
	return builder
}

// 属性数据类型
//
// 示例值：INTEGER
func (builder *SchemaPropertyDefinitionBuilder) Type(type_ string) *SchemaPropertyDefinitionBuilder {
	builder.type_ = type_
	builder.typeFlag = true
	return builder
}

// 属性对外展示可选项
//
// 示例值：
func (builder *SchemaPropertyDefinitionBuilder) DisplayOptions(displayOptions *SchemaDisplayOption) *SchemaPropertyDefinitionBuilder {
	builder.displayOptions = displayOptions
	builder.displayOptionsFlag = true
	return builder
}

func (builder *SchemaPropertyDefinitionBuilder) Build() *SchemaPropertyDefinition {
	req := &SchemaPropertyDefinition{}
	if builder.nameFlag {
		req.Name = &builder.name

	}
	if builder.isReturnableFlag {
		req.IsReturnable = &builder.isReturnable

	}
	if builder.isRepeatableFlag {
		req.IsRepeatable = &builder.isRepeatable

	}
	if builder.isSortableFlag {
		req.IsSortable = &builder.isSortable

	}
	if builder.isFacetableFlag {
		req.IsFacetable = &builder.isFacetable

	}
	if builder.isWildcardSearchableFlag {
		req.IsWildcardSearchable = &builder.isWildcardSearchable

	}
	if builder.typeFlag {
		req.Type = &builder.type_

	}
	if builder.displayOptionsFlag {
		req.DisplayOptions = builder.displayOptions
	}
	return req
}

type SchemaSearchOptions struct {
	EnableSemanticMatch     *bool `json:"enable_semantic_match,omitempty"`      // 是否支持语义切词召回。默认不支持（推荐使用在长文本的场景）
	EnableExactMatch        *bool `json:"enable_exact_match,omitempty"`         // 是否支持精确匹配。默认不支持（推荐使用在短文本、需要精确查找的场景）
	EnablePrefixMatch       *bool `json:"enable_prefix_match,omitempty"`        // 是否支持前缀匹配（短文本的默认的分词/召回策略。前缀长度为 1-12）
	EnableNumberSuffixMatch *bool `json:"enable_number_suffix_match,omitempty"` // 是否支持数据后缀匹配。默认不支持（推荐使用在短文本、有数字后缀查找的场景。后缀长度为3-12）
	EnableCamelMatch        *bool `json:"enable_camel_match,omitempty"`         // 是否支持驼峰英文匹配。默认不支持（推荐使用在短文本，且包含驼峰形式英文的查找场景）
}

type SchemaSearchOptionsBuilder struct {
	enableSemanticMatch         bool // 是否支持语义切词召回。默认不支持（推荐使用在长文本的场景）
	enableSemanticMatchFlag     bool
	enableExactMatch            bool // 是否支持精确匹配。默认不支持（推荐使用在短文本、需要精确查找的场景）
	enableExactMatchFlag        bool
	enablePrefixMatch           bool // 是否支持前缀匹配（短文本的默认的分词/召回策略。前缀长度为 1-12）
	enablePrefixMatchFlag       bool
	enableNumberSuffixMatch     bool // 是否支持数据后缀匹配。默认不支持（推荐使用在短文本、有数字后缀查找的场景。后缀长度为3-12）
	enableNumberSuffixMatchFlag bool
	enableCamelMatch            bool // 是否支持驼峰英文匹配。默认不支持（推荐使用在短文本，且包含驼峰形式英文的查找场景）
	enableCamelMatchFlag        bool
}

func NewSchemaSearchOptionsBuilder() *SchemaSearchOptionsBuilder {
	builder := &SchemaSearchOptionsBuilder{}
	return builder
}

// 是否支持语义切词召回。默认不支持（推荐使用在长文本的场景）
//
// 示例值：true
func (builder *SchemaSearchOptionsBuilder) EnableSemanticMatch(enableSemanticMatch bool) *SchemaSearchOptionsBuilder {
	builder.enableSemanticMatch = enableSemanticMatch
	builder.enableSemanticMatchFlag = true
	return builder
}

// 是否支持精确匹配。默认不支持（推荐使用在短文本、需要精确查找的场景）
//
// 示例值：false
func (builder *SchemaSearchOptionsBuilder) EnableExactMatch(enableExactMatch bool) *SchemaSearchOptionsBuilder {
	builder.enableExactMatch = enableExactMatch
	builder.enableExactMatchFlag = true
	return builder
}

// 是否支持前缀匹配（短文本的默认的分词/召回策略。前缀长度为 1-12）
//
// 示例值：false
func (builder *SchemaSearchOptionsBuilder) EnablePrefixMatch(enablePrefixMatch bool) *SchemaSearchOptionsBuilder {
	builder.enablePrefixMatch = enablePrefixMatch
	builder.enablePrefixMatchFlag = true
	return builder
}

// 是否支持数据后缀匹配。默认不支持（推荐使用在短文本、有数字后缀查找的场景。后缀长度为3-12）
//
// 示例值：false
func (builder *SchemaSearchOptionsBuilder) EnableNumberSuffixMatch(enableNumberSuffixMatch bool) *SchemaSearchOptionsBuilder {
	builder.enableNumberSuffixMatch = enableNumberSuffixMatch
	builder.enableNumberSuffixMatchFlag = true
	return builder
}

// 是否支持驼峰英文匹配。默认不支持（推荐使用在短文本，且包含驼峰形式英文的查找场景）
//
// 示例值：false
func (builder *SchemaSearchOptionsBuilder) EnableCamelMatch(enableCamelMatch bool) *SchemaSearchOptionsBuilder {
	builder.enableCamelMatch = enableCamelMatch
	builder.enableCamelMatchFlag = true
	return builder
}

func (builder *SchemaSearchOptionsBuilder) Build() *SchemaSearchOptions {
	req := &SchemaSearchOptions{}
	if builder.enableSemanticMatchFlag {
		req.EnableSemanticMatch = &builder.enableSemanticMatch

	}
	if builder.enableExactMatchFlag {
		req.EnableExactMatch = &builder.enableExactMatch

	}
	if builder.enablePrefixMatchFlag {
		req.EnablePrefixMatch = &builder.enablePrefixMatch

	}
	if builder.enableNumberSuffixMatchFlag {
		req.EnableNumberSuffixMatch = &builder.enableNumberSuffixMatch

	}
	if builder.enableCamelMatchFlag {
		req.EnableCamelMatch = &builder.enableCamelMatch

	}
	return req
}

type SchemaSortOptions struct {
	Priority *int    `json:"priority,omitempty"` // 排序的优先级，可选范围为 0~4，0为最高优先级。如果优先级相同，则随机进行排序。默认为0
	Order    *string `json:"order,omitempty"`    // 排序的顺序。默认为 desc
}

type SchemaSortOptionsBuilder struct {
	priority     int // 排序的优先级，可选范围为 0~4，0为最高优先级。如果优先级相同，则随机进行排序。默认为0
	priorityFlag bool
	order        string // 排序的顺序。默认为 desc
	orderFlag    bool
}

func NewSchemaSortOptionsBuilder() *SchemaSortOptionsBuilder {
	builder := &SchemaSortOptionsBuilder{}
	return builder
}

// 排序的优先级，可选范围为 0~4，0为最高优先级。如果优先级相同，则随机进行排序。默认为0
//
// 示例值：0
func (builder *SchemaSortOptionsBuilder) Priority(priority int) *SchemaSortOptionsBuilder {
	builder.priority = priority
	builder.priorityFlag = true
	return builder
}

// 排序的顺序。默认为 desc
//
// 示例值：asc
func (builder *SchemaSortOptionsBuilder) Order(order string) *SchemaSortOptionsBuilder {
	builder.order = order
	builder.orderFlag = true
	return builder
}

func (builder *SchemaSortOptionsBuilder) Build() *SchemaSortOptions {
	req := &SchemaSortOptions{}
	if builder.priorityFlag {
		req.Priority = &builder.priority

	}
	if builder.orderFlag {
		req.Order = &builder.order

	}
	return req
}

type SchemaTagOptions struct {
	Name  *string `json:"name,omitempty"`  // tag 对应的枚举值名称
	Color *string `json:"color,omitempty"` // 标签对应的颜色
	Text  *string `json:"text,omitempty"`  // 标签中展示的文本
}

type SchemaTagOptionsBuilder struct {
	name      string // tag 对应的枚举值名称
	nameFlag  bool
	color     string // 标签对应的颜色
	colorFlag bool
	text      string // 标签中展示的文本
	textFlag  bool
}

func NewSchemaTagOptionsBuilder() *SchemaTagOptionsBuilder {
	builder := &SchemaTagOptionsBuilder{}
	return builder
}

// tag 对应的枚举值名称
//
// 示例值：status
func (builder *SchemaTagOptionsBuilder) Name(name string) *SchemaTagOptionsBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

// 标签对应的颜色
//
// 示例值：blue
func (builder *SchemaTagOptionsBuilder) Color(color string) *SchemaTagOptionsBuilder {
	builder.color = color
	builder.colorFlag = true
	return builder
}

// 标签中展示的文本
//
// 示例值：PASS
func (builder *SchemaTagOptionsBuilder) Text(text string) *SchemaTagOptionsBuilder {
	builder.text = text
	builder.textFlag = true
	return builder
}

func (builder *SchemaTagOptionsBuilder) Build() *SchemaTagOptions {
	req := &SchemaTagOptions{}
	if builder.nameFlag {
		req.Name = &builder.name

	}
	if builder.colorFlag {
		req.Color = &builder.color

	}
	if builder.textFlag {
		req.Text = &builder.text

	}
	return req
}

type SchemaTypeDefinitions struct {
	Tag []*SchemaTagOptions `json:"tag,omitempty"` // 标签类型的定义
}

type SchemaTypeDefinitionsBuilder struct {
	tag     []*SchemaTagOptions // 标签类型的定义
	tagFlag bool
}

func NewSchemaTypeDefinitionsBuilder() *SchemaTypeDefinitionsBuilder {
	builder := &SchemaTypeDefinitionsBuilder{}
	return builder
}

// 标签类型的定义
//
// 示例值：
func (builder *SchemaTypeDefinitionsBuilder) Tag(tag []*SchemaTagOptions) *SchemaTypeDefinitionsBuilder {
	builder.tag = tag
	builder.tagFlag = true
	return builder
}

func (builder *SchemaTypeDefinitionsBuilder) Build() *SchemaTypeDefinitions {
	req := &SchemaTypeDefinitions{}
	if builder.tagFlag {
		req.Tag = builder.tag
	}
	return req
}

type CreateDataSourceReqBuilder struct {
	apiReq     *larkcore.ApiReq
	dataSource *DataSource
}

func NewCreateDataSourceReqBuilder() *CreateDataSourceReqBuilder {
	builder := &CreateDataSourceReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 创建一个数据源
func (builder *CreateDataSourceReqBuilder) DataSource(dataSource *DataSource) *CreateDataSourceReqBuilder {
	builder.dataSource = dataSource
	return builder
}

func (builder *CreateDataSourceReqBuilder) Build() *CreateDataSourceReq {
	req := &CreateDataSourceReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.Body = builder.dataSource
	return req
}

type CreateDataSourceReq struct {
	apiReq     *larkcore.ApiReq
	DataSource *DataSource `body:""`
}

type CreateDataSourceRespData struct {
	DataSource *DataSource `json:"data_source,omitempty"` // 数据源实例
}

type CreateDataSourceResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *CreateDataSourceRespData `json:"data"` // 业务数据
}

func (resp *CreateDataSourceResp) Success() bool {
	return resp.Code == 0
}

type DeleteDataSourceReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewDeleteDataSourceReqBuilder() *DeleteDataSourceReqBuilder {
	builder := &DeleteDataSourceReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 数据源的唯一标识
//
// 示例值：6953903108179099667
func (builder *DeleteDataSourceReqBuilder) DataSourceId(dataSourceId string) *DeleteDataSourceReqBuilder {
	builder.apiReq.PathParams.Set("data_source_id", fmt.Sprint(dataSourceId))
	return builder
}

func (builder *DeleteDataSourceReqBuilder) Build() *DeleteDataSourceReq {
	req := &DeleteDataSourceReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	return req
}

type DeleteDataSourceReq struct {
	apiReq *larkcore.ApiReq
}

type DeleteDataSourceResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *DeleteDataSourceResp) Success() bool {
	return resp.Code == 0
}

type GetDataSourceReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewGetDataSourceReqBuilder() *GetDataSourceReqBuilder {
	builder := &GetDataSourceReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 数据源的唯一标识
//
// 示例值：service_ticket
func (builder *GetDataSourceReqBuilder) DataSourceId(dataSourceId string) *GetDataSourceReqBuilder {
	builder.apiReq.PathParams.Set("data_source_id", fmt.Sprint(dataSourceId))
	return builder
}

func (builder *GetDataSourceReqBuilder) Build() *GetDataSourceReq {
	req := &GetDataSourceReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	return req
}

type GetDataSourceReq struct {
	apiReq *larkcore.ApiReq
}

type GetDataSourceRespData struct {
	DataSource *DataSource `json:"data_source,omitempty"` // 数据源实例
}

type GetDataSourceResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *GetDataSourceRespData `json:"data"` // 业务数据
}

func (resp *GetDataSourceResp) Success() bool {
	return resp.Code == 0
}

type ListDataSourceReqBuilder struct {
	apiReq *larkcore.ApiReq
	limit  int // 最大返回多少记录，当使用迭代器访问时才有效
}

func NewListDataSourceReqBuilder() *ListDataSourceReqBuilder {
	builder := &ListDataSourceReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 最大返回多少记录，当使用迭代器访问时才有效
func (builder *ListDataSourceReqBuilder) Limit(limit int) *ListDataSourceReqBuilder {
	builder.limit = limit
	return builder
}

// 回包数据格式，0-全量数据；1-摘要数据。;;**注**：摘要数据仅包含"id"，"name"，"state"。
//
// 示例值：0
func (builder *ListDataSourceReqBuilder) View(view int) *ListDataSourceReqBuilder {
	builder.apiReq.QueryParams.Set("view", fmt.Sprint(view))
	return builder
}

//
//
// 示例值：PxZFma9OIRhdBlT/dOYNiu2Ro8F2WAhcby7OhOijfljZ
func (builder *ListDataSourceReqBuilder) PageToken(pageToken string) *ListDataSourceReqBuilder {
	builder.apiReq.QueryParams.Set("page_token", fmt.Sprint(pageToken))
	return builder
}

//
//
// 示例值：10
func (builder *ListDataSourceReqBuilder) PageSize(pageSize int) *ListDataSourceReqBuilder {
	builder.apiReq.QueryParams.Set("page_size", fmt.Sprint(pageSize))
	return builder
}

func (builder *ListDataSourceReqBuilder) Build() *ListDataSourceReq {
	req := &ListDataSourceReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.Limit = builder.limit
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	return req
}

type ListDataSourceReq struct {
	apiReq *larkcore.ApiReq
	Limit  int // 最多返回多少记录，只有在使用迭代器访问时，才有效

}

type ListDataSourceRespData struct {
	HasMore   *bool         `json:"has_more,omitempty"`   // 是否有更多数据
	PageToken *string       `json:"page_token,omitempty"` // 取数据的凭证
	Items     []*DataSource `json:"items,omitempty"`      // 数据源中的数据记录
}

type ListDataSourceResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *ListDataSourceRespData `json:"data"` // 业务数据
}

func (resp *ListDataSourceResp) Success() bool {
	return resp.Code == 0
}

type PatchDataSourceReqBodyBuilder struct {
	name                string // 数据源的展示名称
	nameFlag            bool
	state               int // 数据源状态，0-已上线，1-未上线
	stateFlag           bool
	description         string // 对于数据源的描述
	descriptionFlag     bool
	iconUrl             string // 数据源在 search tab 上的展示图标路径
	iconUrlFlag         bool
	i18nName            *I18nMeta // 数据源名称多语言配置，json格式，key为语言locale，value为对应文案，例如{"zh_cn":"测试数据源", "en_us":"Test DataSource"}
	i18nNameFlag        bool
	i18nDescription     *I18nMeta // 数据源描述多语言配置，json格式，key为语言locale，value为对应文案，例如{"zh_cn":"搜索测试数据源相关数据", "en_us":"Search data from Test DataSource"}
	i18nDescriptionFlag bool
}

func NewPatchDataSourceReqBodyBuilder() *PatchDataSourceReqBodyBuilder {
	builder := &PatchDataSourceReqBodyBuilder{}
	return builder
}

// 数据源的展示名称
//
//示例值：客服工单
func (builder *PatchDataSourceReqBodyBuilder) Name(name string) *PatchDataSourceReqBodyBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

// 数据源状态，0-已上线，1-未上线
//
//示例值：0
func (builder *PatchDataSourceReqBodyBuilder) State(state int) *PatchDataSourceReqBodyBuilder {
	builder.state = state
	builder.stateFlag = true
	return builder
}

// 对于数据源的描述
//
//示例值：搜索客服工单
func (builder *PatchDataSourceReqBodyBuilder) Description(description string) *PatchDataSourceReqBodyBuilder {
	builder.description = description
	builder.descriptionFlag = true
	return builder
}

// 数据源在 search tab 上的展示图标路径
//
//示例值：https://www.xxx.com/open.jpg
func (builder *PatchDataSourceReqBodyBuilder) IconUrl(iconUrl string) *PatchDataSourceReqBodyBuilder {
	builder.iconUrl = iconUrl
	builder.iconUrlFlag = true
	return builder
}

// 数据源名称多语言配置，json格式，key为语言locale，value为对应文案，例如{"zh_cn":"测试数据源", "en_us":"Test DataSource"}
//
//示例值：
func (builder *PatchDataSourceReqBodyBuilder) I18nName(i18nName *I18nMeta) *PatchDataSourceReqBodyBuilder {
	builder.i18nName = i18nName
	builder.i18nNameFlag = true
	return builder
}

// 数据源描述多语言配置，json格式，key为语言locale，value为对应文案，例如{"zh_cn":"搜索测试数据源相关数据", "en_us":"Search data from Test DataSource"}
//
//示例值：
func (builder *PatchDataSourceReqBodyBuilder) I18nDescription(i18nDescription *I18nMeta) *PatchDataSourceReqBodyBuilder {
	builder.i18nDescription = i18nDescription
	builder.i18nDescriptionFlag = true
	return builder
}

func (builder *PatchDataSourceReqBodyBuilder) Build() *PatchDataSourceReqBody {
	req := &PatchDataSourceReqBody{}
	if builder.nameFlag {
		req.Name = &builder.name
	}
	if builder.stateFlag {
		req.State = &builder.state
	}
	if builder.descriptionFlag {
		req.Description = &builder.description
	}
	if builder.iconUrlFlag {
		req.IconUrl = &builder.iconUrl
	}
	if builder.i18nNameFlag {
		req.I18nName = builder.i18nName
	}
	if builder.i18nDescriptionFlag {
		req.I18nDescription = builder.i18nDescription
	}
	return req
}

type PatchDataSourcePathReqBodyBuilder struct {
	name                string // 数据源的展示名称
	nameFlag            bool
	state               int // 数据源状态，0-已上线，1-未上线
	stateFlag           bool
	description         string // 对于数据源的描述
	descriptionFlag     bool
	iconUrl             string // 数据源在 search tab 上的展示图标路径
	iconUrlFlag         bool
	i18nName            *I18nMeta // 数据源名称多语言配置，json格式，key为语言locale，value为对应文案，例如{"zh_cn":"测试数据源", "en_us":"Test DataSource"}
	i18nNameFlag        bool
	i18nDescription     *I18nMeta // 数据源描述多语言配置，json格式，key为语言locale，value为对应文案，例如{"zh_cn":"搜索测试数据源相关数据", "en_us":"Search data from Test DataSource"}
	i18nDescriptionFlag bool
}

func NewPatchDataSourcePathReqBodyBuilder() *PatchDataSourcePathReqBodyBuilder {
	builder := &PatchDataSourcePathReqBodyBuilder{}
	return builder
}

// 数据源的展示名称
//
// 示例值：客服工单
func (builder *PatchDataSourcePathReqBodyBuilder) Name(name string) *PatchDataSourcePathReqBodyBuilder {
	builder.name = name
	builder.nameFlag = true
	return builder
}

// 数据源状态，0-已上线，1-未上线
//
// 示例值：0
func (builder *PatchDataSourcePathReqBodyBuilder) State(state int) *PatchDataSourcePathReqBodyBuilder {
	builder.state = state
	builder.stateFlag = true
	return builder
}

// 对于数据源的描述
//
// 示例值：搜索客服工单
func (builder *PatchDataSourcePathReqBodyBuilder) Description(description string) *PatchDataSourcePathReqBodyBuilder {
	builder.description = description
	builder.descriptionFlag = true
	return builder
}

// 数据源在 search tab 上的展示图标路径
//
// 示例值：https://www.xxx.com/open.jpg
func (builder *PatchDataSourcePathReqBodyBuilder) IconUrl(iconUrl string) *PatchDataSourcePathReqBodyBuilder {
	builder.iconUrl = iconUrl
	builder.iconUrlFlag = true
	return builder
}

// 数据源名称多语言配置，json格式，key为语言locale，value为对应文案，例如{"zh_cn":"测试数据源", "en_us":"Test DataSource"}
//
// 示例值：
func (builder *PatchDataSourcePathReqBodyBuilder) I18nName(i18nName *I18nMeta) *PatchDataSourcePathReqBodyBuilder {
	builder.i18nName = i18nName
	builder.i18nNameFlag = true
	return builder
}

// 数据源描述多语言配置，json格式，key为语言locale，value为对应文案，例如{"zh_cn":"搜索测试数据源相关数据", "en_us":"Search data from Test DataSource"}
//
// 示例值：
func (builder *PatchDataSourcePathReqBodyBuilder) I18nDescription(i18nDescription *I18nMeta) *PatchDataSourcePathReqBodyBuilder {
	builder.i18nDescription = i18nDescription
	builder.i18nDescriptionFlag = true
	return builder
}

func (builder *PatchDataSourcePathReqBodyBuilder) Build() (*PatchDataSourceReqBody, error) {
	req := &PatchDataSourceReqBody{}
	if builder.nameFlag {
		req.Name = &builder.name
	}
	if builder.stateFlag {
		req.State = &builder.state
	}
	if builder.descriptionFlag {
		req.Description = &builder.description
	}
	if builder.iconUrlFlag {
		req.IconUrl = &builder.iconUrl
	}
	if builder.i18nNameFlag {
		req.I18nName = builder.i18nName
	}
	if builder.i18nDescriptionFlag {
		req.I18nDescription = builder.i18nDescription
	}
	return req, nil
}

type PatchDataSourceReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *PatchDataSourceReqBody
}

func NewPatchDataSourceReqBuilder() *PatchDataSourceReqBuilder {
	builder := &PatchDataSourceReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 数据源的唯一标识
//
// 示例值：service_ticket
func (builder *PatchDataSourceReqBuilder) DataSourceId(dataSourceId string) *PatchDataSourceReqBuilder {
	builder.apiReq.PathParams.Set("data_source_id", fmt.Sprint(dataSourceId))
	return builder
}

// 更新一个已经存在的数据源
func (builder *PatchDataSourceReqBuilder) Body(body *PatchDataSourceReqBody) *PatchDataSourceReqBuilder {
	builder.body = body
	return builder
}

func (builder *PatchDataSourceReqBuilder) Build() *PatchDataSourceReq {
	req := &PatchDataSourceReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.Body = builder.body
	return req
}

type PatchDataSourceReqBody struct {
	Name            *string   `json:"name,omitempty"`             // 数据源的展示名称
	State           *int      `json:"state,omitempty"`            // 数据源状态，0-已上线，1-未上线
	Description     *string   `json:"description,omitempty"`      // 对于数据源的描述
	IconUrl         *string   `json:"icon_url,omitempty"`         // 数据源在 search tab 上的展示图标路径
	I18nName        *I18nMeta `json:"i18n_name,omitempty"`        // 数据源名称多语言配置，json格式，key为语言locale，value为对应文案，例如{"zh_cn":"测试数据源", "en_us":"Test DataSource"}
	I18nDescription *I18nMeta `json:"i18n_description,omitempty"` // 数据源描述多语言配置，json格式，key为语言locale，value为对应文案，例如{"zh_cn":"搜索测试数据源相关数据", "en_us":"Search data from Test DataSource"}
}

type PatchDataSourceReq struct {
	apiReq *larkcore.ApiReq
	Body   *PatchDataSourceReqBody `body:""`
}

type PatchDataSourceRespData struct {
	DataSource *DataSource `json:"data_source,omitempty"` // 数据源
}

type PatchDataSourceResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *PatchDataSourceRespData `json:"data"` // 业务数据
}

func (resp *PatchDataSourceResp) Success() bool {
	return resp.Code == 0
}

type CreateDataSourceItemReqBuilder struct {
	apiReq *larkcore.ApiReq
	item   *Item
}

func NewCreateDataSourceItemReqBuilder() *CreateDataSourceItemReqBuilder {
	builder := &CreateDataSourceItemReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 数据源的ID
//
// 示例值：service_ticket
func (builder *CreateDataSourceItemReqBuilder) DataSourceId(dataSourceId string) *CreateDataSourceItemReqBuilder {
	builder.apiReq.PathParams.Set("data_source_id", fmt.Sprint(dataSourceId))
	return builder
}

// 索引一条数据记录
func (builder *CreateDataSourceItemReqBuilder) Item(item *Item) *CreateDataSourceItemReqBuilder {
	builder.item = item
	return builder
}

func (builder *CreateDataSourceItemReqBuilder) Build() *CreateDataSourceItemReq {
	req := &CreateDataSourceItemReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.Body = builder.item
	return req
}

type CreateDataSourceItemReq struct {
	apiReq *larkcore.ApiReq
	Item   *Item `body:""`
}

type CreateDataSourceItemResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *CreateDataSourceItemResp) Success() bool {
	return resp.Code == 0
}

type DeleteDataSourceItemReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewDeleteDataSourceItemReqBuilder() *DeleteDataSourceItemReqBuilder {
	builder := &DeleteDataSourceItemReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 数据源的ID
//
// 示例值：service_ticket
func (builder *DeleteDataSourceItemReqBuilder) DataSourceId(dataSourceId string) *DeleteDataSourceItemReqBuilder {
	builder.apiReq.PathParams.Set("data_source_id", fmt.Sprint(dataSourceId))
	return builder
}

// 数据记录的ID
//
// 示例值：01010111
func (builder *DeleteDataSourceItemReqBuilder) ItemId(itemId string) *DeleteDataSourceItemReqBuilder {
	builder.apiReq.PathParams.Set("item_id", fmt.Sprint(itemId))
	return builder
}

func (builder *DeleteDataSourceItemReqBuilder) Build() *DeleteDataSourceItemReq {
	req := &DeleteDataSourceItemReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	return req
}

type DeleteDataSourceItemReq struct {
	apiReq *larkcore.ApiReq
}

type DeleteDataSourceItemResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *DeleteDataSourceItemResp) Success() bool {
	return resp.Code == 0
}

type GetDataSourceItemReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewGetDataSourceItemReqBuilder() *GetDataSourceItemReqBuilder {
	builder := &GetDataSourceItemReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 数据源的id
//
// 示例值：service_ticket
func (builder *GetDataSourceItemReqBuilder) DataSourceId(dataSourceId string) *GetDataSourceItemReqBuilder {
	builder.apiReq.PathParams.Set("data_source_id", fmt.Sprint(dataSourceId))
	return builder
}

// 数据记录的唯一标识
//
// 示例值：01010111
func (builder *GetDataSourceItemReqBuilder) ItemId(itemId string) *GetDataSourceItemReqBuilder {
	builder.apiReq.PathParams.Set("item_id", fmt.Sprint(itemId))
	return builder
}

func (builder *GetDataSourceItemReqBuilder) Build() *GetDataSourceItemReq {
	req := &GetDataSourceItemReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	return req
}

type GetDataSourceItemReq struct {
	apiReq *larkcore.ApiReq
}

type GetDataSourceItemRespData struct {
	Item *Item `json:"item,omitempty"` // 数据项实例
}

type GetDataSourceItemResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *GetDataSourceItemRespData `json:"data"` // 业务数据
}

func (resp *GetDataSourceItemResp) Success() bool {
	return resp.Code == 0
}

type CreateSchemaReqBuilder struct {
	apiReq *larkcore.ApiReq
	schema *Schema
}

func NewCreateSchemaReqBuilder() *CreateSchemaReqBuilder {
	builder := &CreateSchemaReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 是否只用来校验合法性
//
// 示例值：true
func (builder *CreateSchemaReqBuilder) ValidateOnly(validateOnly bool) *CreateSchemaReqBuilder {
	builder.apiReq.QueryParams.Set("validate_only", fmt.Sprint(validateOnly))
	return builder
}

// 创建一个数据源
func (builder *CreateSchemaReqBuilder) Schema(schema *Schema) *CreateSchemaReqBuilder {
	builder.schema = schema
	return builder
}

func (builder *CreateSchemaReqBuilder) Build() *CreateSchemaReq {
	req := &CreateSchemaReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.QueryParams = builder.apiReq.QueryParams
	req.apiReq.Body = builder.schema
	return req
}

type CreateSchemaReq struct {
	apiReq *larkcore.ApiReq
	Schema *Schema `body:""`
}

type CreateSchemaRespData struct {
	Schema *Schema `json:"schema,omitempty"` // 数据范式实例
}

type CreateSchemaResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *CreateSchemaRespData `json:"data"` // 业务数据
}

func (resp *CreateSchemaResp) Success() bool {
	return resp.Code == 0
}

type DeleteSchemaReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewDeleteSchemaReqBuilder() *DeleteSchemaReqBuilder {
	builder := &DeleteSchemaReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 用户自定义数据范式的唯一标识
//
// 示例值：custom_schema_id
func (builder *DeleteSchemaReqBuilder) SchemaId(schemaId string) *DeleteSchemaReqBuilder {
	builder.apiReq.PathParams.Set("schema_id", fmt.Sprint(schemaId))
	return builder
}

func (builder *DeleteSchemaReqBuilder) Build() *DeleteSchemaReq {
	req := &DeleteSchemaReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	return req
}

type DeleteSchemaReq struct {
	apiReq *larkcore.ApiReq
}

type DeleteSchemaResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
}

func (resp *DeleteSchemaResp) Success() bool {
	return resp.Code == 0
}

type GetSchemaReqBuilder struct {
	apiReq *larkcore.ApiReq
}

func NewGetSchemaReqBuilder() *GetSchemaReqBuilder {
	builder := &GetSchemaReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 用户自定义数据范式的唯一标识
//
// 示例值：custom_schema_id
func (builder *GetSchemaReqBuilder) SchemaId(schemaId string) *GetSchemaReqBuilder {
	builder.apiReq.PathParams.Set("schema_id", fmt.Sprint(schemaId))
	return builder
}

func (builder *GetSchemaReqBuilder) Build() *GetSchemaReq {
	req := &GetSchemaReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	return req
}

type GetSchemaReq struct {
	apiReq *larkcore.ApiReq
}

type GetSchemaRespData struct {
	Schema *Schema `json:"schema,omitempty"` // 数据范式
}

type GetSchemaResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *GetSchemaRespData `json:"data"` // 业务数据
}

func (resp *GetSchemaResp) Success() bool {
	return resp.Code == 0
}

type PatchSchemaReqBodyBuilder struct {
	display     *SchemaDisplay // 数据展示相关配置
	displayFlag bool
}

func NewPatchSchemaReqBodyBuilder() *PatchSchemaReqBodyBuilder {
	builder := &PatchSchemaReqBodyBuilder{}
	return builder
}

// 数据展示相关配置
//
//示例值：
func (builder *PatchSchemaReqBodyBuilder) Display(display *SchemaDisplay) *PatchSchemaReqBodyBuilder {
	builder.display = display
	builder.displayFlag = true
	return builder
}

func (builder *PatchSchemaReqBodyBuilder) Build() *PatchSchemaReqBody {
	req := &PatchSchemaReqBody{}
	if builder.displayFlag {
		req.Display = builder.display
	}
	return req
}

type PatchSchemaPathReqBodyBuilder struct {
	display     *SchemaDisplay // 数据展示相关配置
	displayFlag bool
}

func NewPatchSchemaPathReqBodyBuilder() *PatchSchemaPathReqBodyBuilder {
	builder := &PatchSchemaPathReqBodyBuilder{}
	return builder
}

// 数据展示相关配置
//
// 示例值：
func (builder *PatchSchemaPathReqBodyBuilder) Display(display *SchemaDisplay) *PatchSchemaPathReqBodyBuilder {
	builder.display = display
	builder.displayFlag = true
	return builder
}

func (builder *PatchSchemaPathReqBodyBuilder) Build() (*PatchSchemaReqBody, error) {
	req := &PatchSchemaReqBody{}
	if builder.displayFlag {
		req.Display = builder.display
	}
	return req, nil
}

type PatchSchemaReqBuilder struct {
	apiReq *larkcore.ApiReq
	body   *PatchSchemaReqBody
}

func NewPatchSchemaReqBuilder() *PatchSchemaReqBuilder {
	builder := &PatchSchemaReqBuilder{}
	builder.apiReq = &larkcore.ApiReq{
		PathParams:  larkcore.PathParams{},
		QueryParams: larkcore.QueryParams{},
	}
	return builder
}

// 用户自定义数据范式的唯一标识
//
// 示例值：custom_schema_id
func (builder *PatchSchemaReqBuilder) SchemaId(schemaId string) *PatchSchemaReqBuilder {
	builder.apiReq.PathParams.Set("schema_id", fmt.Sprint(schemaId))
	return builder
}

// 修改数据范式
func (builder *PatchSchemaReqBuilder) Body(body *PatchSchemaReqBody) *PatchSchemaReqBuilder {
	builder.body = body
	return builder
}

func (builder *PatchSchemaReqBuilder) Build() *PatchSchemaReq {
	req := &PatchSchemaReq{}
	req.apiReq = &larkcore.ApiReq{}
	req.apiReq.PathParams = builder.apiReq.PathParams
	req.apiReq.Body = builder.body
	return req
}

type PatchSchemaReqBody struct {
	Display *SchemaDisplay `json:"display,omitempty"` // 数据展示相关配置
}

type PatchSchemaReq struct {
	apiReq *larkcore.ApiReq
	Body   *PatchSchemaReqBody `body:""`
}

type PatchSchemaRespData struct {
	Schema *Schema `json:"schema,omitempty"` // 数据范式实例
}

type PatchSchemaResp struct {
	*larkcore.ApiResp `json:"-"`
	larkcore.CodeError
	Data *PatchSchemaRespData `json:"data"` // 业务数据
}

func (resp *PatchSchemaResp) Success() bool {
	return resp.Code == 0
}

type ListDataSourceIterator struct {
	nextPageToken *string
	items         []*DataSource
	index         int
	limit         int
	ctx           context.Context
	req           *ListDataSourceReq
	listFunc      func(ctx context.Context, req *ListDataSourceReq, options ...larkcore.RequestOptionFunc) (*ListDataSourceResp, error)
	options       []larkcore.RequestOptionFunc
	curlNum       int
}

func (iterator *ListDataSourceIterator) Next() (bool, *DataSource, error) {
	// 达到最大量，则返回
	if iterator.limit > 0 && iterator.curlNum >= iterator.limit {
		return false, nil, nil
	}

	// 为0则拉取数据
	if iterator.index == 0 || iterator.index >= len(iterator.items) {
		if iterator.index != 0 && iterator.nextPageToken == nil {
			return false, nil, nil
		}
		if iterator.nextPageToken != nil {
			iterator.req.apiReq.QueryParams.Set("page_token", *iterator.nextPageToken)
		}
		resp, err := iterator.listFunc(iterator.ctx, iterator.req, iterator.options...)
		if err != nil {
			return false, nil, err
		}

		if resp.Code != 0 {
			return false, nil, errors.New(fmt.Sprintf("Code:%d,Msg:%s", resp.Code, resp.Msg))
		}

		if len(resp.Data.Items) == 0 {
			return false, nil, nil
		}

		iterator.nextPageToken = resp.Data.PageToken
		iterator.items = resp.Data.Items
		iterator.index = 0
	}

	block := iterator.items[iterator.index]
	iterator.index++
	iterator.curlNum++
	return true, block, nil
}

func (iterator *ListDataSourceIterator) NextPageToken() *string {
	return iterator.nextPageToken
}
